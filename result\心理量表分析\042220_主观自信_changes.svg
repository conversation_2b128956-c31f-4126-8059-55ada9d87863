<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="720pt" height="432pt" viewBox="0 0 720 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:34.429729</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 720 432 
L 720 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 39.29375 393.48625 
L 709.2 393.48625 
L 709.2 25.14 
L 39.29375 25.14 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 148.689215 393.48625 
L 148.689215 25.14 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mb772a99561" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb772a99561" x="148.689215" y="393.48625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 第一次刺激 -->
      <g transform="translate(128.689215 406.92375) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-7b2c" d="M 4475 -350 
Q 4450 -125 4250 200 
Q 4900 175 5087 262 
Q 5275 350 5275 1000 
L 3475 1000 
L 3475 175 
Q 3475 -50 3500 -675 
L 2975 -675 
Q 3000 -25 3000 175 
L 3000 775 
Q 2625 400 2037 50 
Q 1450 -300 700 -600 
Q 600 -350 325 -150 
Q 900 -25 1600 312 
Q 2300 650 2625 1000 
L 875 1000 
Q 975 1750 1000 2450 
L 3000 2450 
L 3000 3100 
L 1975 3100 
Q 1525 3100 975 3075 
L 975 3525 
Q 1525 3500 1825 3500 
L 5400 3500 
Q 5375 3050 5375 2725 
Q 5375 2400 5400 2050 
L 3475 2050 
L 3475 1400 
L 5800 1400 
Q 5725 525 5662 225 
Q 5600 -75 5275 -187 
Q 4950 -300 4475 -350 
z
M 2050 5025 
Q 1775 4800 1650 4625 
L 2500 4625 
Q 3025 4625 3325 4650 
L 3325 4225 
L 2325 4225 
Q 2475 4000 2625 3725 
Q 2400 3650 2200 3550 
Q 2050 3900 1825 4225 
L 1425 4225 
Q 1150 3825 725 3350 
Q 500 3500 250 3600 
Q 700 3975 975 4375 
Q 1250 4775 1450 5275 
Q 1825 5075 2050 5025 
z
M 3600 3550 
Q 3350 3650 3125 3750 
Q 3375 3975 3600 4387 
Q 3825 4800 3950 5200 
Q 4225 5075 4525 4975 
Q 4400 4875 4275 4650 
L 5250 4650 
Q 5425 4650 6000 4675 
L 6000 4225 
Q 5625 4250 5000 4250 
Q 5175 4000 5350 3725 
Q 5075 3650 4900 3550 
Q 4725 3900 4475 4250 
L 4075 4250 
Q 3850 3850 3600 3550 
z
M 3000 1400 
L 3000 2050 
L 1475 2050 
Q 1425 1750 1400 1400 
L 3000 1400 
z
M 4900 2450 
L 4900 3100 
L 3475 3100 
L 3475 2450 
L 4900 2450 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-4e00" d="M 4875 2675 
Q 5525 2675 6050 2700 
L 6050 2050 
Q 5525 2075 4900 2075 
L 1600 2075 
Q 950 2075 350 2050 
L 350 2700 
Q 950 2675 1600 2675 
L 4875 2675 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-6b21" d="M 4150 3025 
Q 4000 2550 4050 2187 
Q 4100 1825 4275 1450 
Q 4450 1075 4800 750 
Q 5150 425 5475 212 
Q 5800 0 6150 -150 
Q 5875 -350 5750 -625 
Q 5250 -325 4887 -25 
Q 4525 275 4275 612 
Q 4025 950 3825 1425 
Q 3525 625 2950 125 
Q 2375 -375 1850 -650 
Q 1675 -400 1475 -225 
Q 2025 0 2487 362 
Q 2950 725 3162 1100 
Q 3375 1475 3450 1825 
Q 3525 2175 3537 2400 
Q 3550 2625 3525 3125 
Q 3825 3050 4150 3025 
z
M 3700 4975 
Q 3625 4900 3475 4575 
Q 3325 4250 3250 4075 
L 5775 4075 
Q 5650 3725 5550 3362 
Q 5450 3000 5275 2500 
Q 5000 2575 4775 2600 
Q 5025 3350 5100 3650 
L 3100 3650 
Q 2975 3400 2812 3100 
Q 2650 2800 2425 2450 
Q 2275 2600 1975 2700 
Q 2300 3050 2625 3762 
Q 2950 4475 3125 5200 
Q 3450 5025 3700 4975 
z
M 2025 2000 
Q 1825 1675 1475 1087 
Q 1125 500 725 -200 
Q 500 -25 250 175 
Q 725 725 1037 1262 
Q 1350 1800 1575 2325 
Q 1750 2175 2025 2000 
z
M 900 4750 
Q 1725 3725 1950 3375 
Q 1750 3250 1500 3050 
Q 1250 3525 500 4425 
Q 700 4575 900 4750 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-523a" d="M 2800 2075 
Q 3075 2100 3187 2112 
Q 3300 2125 3300 2350 
L 3300 2875 
L 2450 2875 
L 2450 100 
Q 2450 -250 2475 -575 
L 1950 -575 
Q 1975 -275 1975 50 
L 1975 1300 
Q 1675 875 1312 512 
Q 950 150 575 -150 
Q 400 100 200 225 
Q 750 575 1200 1062 
Q 1650 1550 1975 2125 
L 1975 2875 
L 1125 2875 
L 1125 1675 
L 625 1675 
Q 650 2225 650 2525 
Q 650 2825 625 3275 
L 1975 3275 
L 1975 3925 
L 1350 3925 
Q 850 3925 400 3900 
L 400 4350 
Q 850 4325 1350 4325 
L 1975 4325 
Q 1975 4775 1950 5175 
L 2475 5175 
Q 2450 4750 2450 4325 
L 3100 4325 
Q 3525 4325 3925 4350 
L 3925 3900 
Q 3525 3925 3125 3925 
L 2450 3925 
L 2450 3275 
L 3775 3275 
Q 3750 2925 3750 2650 
L 3750 2150 
Q 3750 1825 3550 1737 
Q 3350 1650 3025 1600 
Q 2925 1875 2800 2075 
z
M 5900 5050 
Q 5875 4725 5875 4325 
L 5875 50 
Q 5875 -300 5550 -412 
Q 5225 -525 4775 -575 
Q 4700 -250 4525 -25 
Q 4975 -50 5187 12 
Q 5400 75 5400 300 
L 5400 4325 
Q 5400 4700 5375 5050 
L 5900 5050 
z
M 4800 4275 
Q 4775 3800 4775 3550 
L 4775 1775 
Q 4775 1375 4800 925 
L 4300 925 
Q 4325 1350 4325 1775 
L 4325 3550 
Q 4325 3875 4300 4275 
L 4800 4275 
z
M 3950 550 
Q 3750 400 3550 175 
Q 3275 500 2987 775 
Q 2700 1050 2500 1200 
Q 2675 1350 2850 1525 
Q 3150 1275 3425 1025 
Q 3700 775 3950 550 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-6fc0" d="M 1825 2625 
Q 1850 3050 1850 3400 
L 1850 3850 
Q 1850 4200 1825 4650 
L 2375 4650 
Q 2425 4875 2475 5275 
Q 2750 5225 3000 5175 
Q 2875 4925 2825 4650 
L 3725 4650 
Q 3700 4225 3700 3850 
L 3700 2650 
Q 3850 2900 3975 3175 
Q 4125 3500 4237 3850 
Q 4350 4200 4437 4575 
Q 4525 4950 4550 5200 
Q 4825 5100 5125 5075 
Q 5025 4875 4962 4650 
Q 4900 4425 4750 4050 
L 5475 4050 
Q 5750 4050 6100 4075 
L 6100 3625 
Q 5850 3650 5750 3650 
Q 5700 3175 5675 2987 
Q 5650 2800 5600 2450 
Q 5550 2100 5437 1675 
Q 5325 1250 5175 875 
Q 5425 450 5687 175 
Q 5950 -100 6200 -225 
Q 5950 -400 5825 -650 
Q 5475 -325 5287 -100 
Q 5100 125 4900 425 
Q 4675 150 4425 -125 
Q 4175 -400 3850 -650 
Q 3675 -425 3450 -225 
Q 3800 -75 4075 187 
Q 4350 450 4625 900 
Q 4425 1375 4325 1725 
Q 4225 2050 4150 2550 
Q 4075 2400 4000 2250 
Q 3875 2400 3625 2550 
L 3675 2625 
L 1825 2625 
z
M 2950 2600 
Q 3025 2300 3075 2125 
L 3250 2125 
Q 3475 2125 3900 2150 
L 3900 1725 
Q 3475 1750 3075 1750 
L 2750 1750 
Q 2725 1600 2675 1375 
L 3750 1375 
Q 3725 225 3550 12 
Q 3375 -200 2825 -300 
Q 2775 -75 2650 200 
Q 3150 175 3200 387 
Q 3250 600 3275 1000 
L 2600 1000 
Q 2500 575 2287 225 
Q 2075 -125 1725 -500 
Q 1525 -275 1250 -175 
Q 1700 175 1962 637 
Q 2225 1100 2300 1750 
L 2125 1750 
Q 2000 1750 1550 1725 
L 1550 2150 
Q 2000 2125 2375 2125 
L 2600 2125 
Q 2575 2275 2475 2525 
Q 2700 2550 2950 2600 
z
M 4625 3650 
Q 4525 3400 4450 3200 
Q 4525 2500 4675 2000 
Q 4825 1500 4925 1350 
Q 5075 1900 5137 2262 
Q 5200 2625 5237 2925 
Q 5275 3225 5300 3650 
L 4625 3650 
z
M 1525 1775 
Q 1300 1250 1200 975 
Q 1100 700 975 362 
Q 850 25 750 -275 
Q 475 -75 200 50 
Q 350 250 475 487 
Q 600 725 712 975 
Q 825 1225 1125 2050 
Q 1300 1900 1525 1775 
z
M 650 3600 
Q 850 3450 1062 3262 
Q 1275 3075 1525 2850 
Q 1325 2650 1200 2425 
Q 1050 2625 850 2812 
Q 650 3000 350 3225 
Q 475 3375 650 3600 
z
M 950 5000 
Q 1100 4875 1312 4662 
Q 1525 4450 1725 4275 
Q 1475 4075 1325 3900 
Q 1175 4150 1000 4325 
Q 825 4500 600 4675 
Q 775 4850 950 5000 
z
M 3300 3000 
L 3300 3450 
L 2275 3450 
L 2275 3000 
L 3300 3000 
z
M 3300 3825 
L 3300 4275 
L 2275 4275 
L 2275 3825 
L 3300 3825 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-7b2c"/>
       <use xlink:href="#SimHei-4e00" x="100"/>
       <use xlink:href="#SimHei-6b21" x="200"/>
       <use xlink:href="#SimHei-523a" x="300"/>
       <use xlink:href="#SimHei-6fc0" x="400"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 374.246875 393.48625 
L 374.246875 25.14 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mb772a99561" x="374.246875" y="393.48625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 第二次刺激 -->
      <g transform="translate(354.246875 406.92375) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-4e8c" d="M 5050 625 
Q 5650 625 6025 675 
L 6025 50 
Q 5625 75 5050 75 
L 1425 75 
Q 775 75 350 50 
L 350 650 
Q 800 625 1400 625 
L 5050 625 
z
M 4350 4150 
Q 4900 4175 5350 4200 
L 5350 3575 
Q 4900 3600 4375 3600 
L 2050 3600 
Q 1450 3600 1000 3575 
L 1000 4175 
Q 1450 4150 2025 4150 
L 4350 4150 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-7b2c"/>
       <use xlink:href="#SimHei-4e8c" x="100"/>
       <use xlink:href="#SimHei-6b21" x="200"/>
       <use xlink:href="#SimHei-523a" x="300"/>
       <use xlink:href="#SimHei-6fc0" x="400"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 599.804535 393.48625 
L 599.804535 25.14 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mb772a99561" x="599.804535" y="393.48625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 第三次刺激 -->
      <g transform="translate(579.804535 406.92375) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-4e09" d="M 6000 -200 
Q 5550 -175 5200 -175 
L 1300 -175 
Q 900 -175 375 -200 
L 375 350 
Q 850 325 1300 325 
L 5200 325 
Q 5575 325 6000 375 
L 6000 -200 
z
M 900 4600 
Q 1400 4575 1825 4575 
L 4600 4575 
Q 5075 4575 5475 4600 
L 5475 4075 
Q 5100 4100 4600 4100 
L 1800 4100 
Q 1375 4100 900 4075 
L 900 4600 
z
M 2075 2100 
Q 1700 2100 1275 2075 
L 1275 2625 
Q 1675 2600 2075 2600 
L 4225 2600 
Q 4600 2600 5050 2625 
L 5050 2075 
Q 4550 2100 4225 2100 
L 2075 2100 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-7b2c"/>
       <use xlink:href="#SimHei-4e09" x="100"/>
       <use xlink:href="#SimHei-6b21" x="200"/>
       <use xlink:href="#SimHei-523a" x="300"/>
       <use xlink:href="#SimHei-6fc0" x="400"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- 刺激次数 -->
     <g transform="translate(354.246875 419.931562) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-6570" d="M 4525 4925 
Q 4475 4800 4400 4587 
Q 4325 4375 4175 3825 
L 5325 3825 
Q 5575 3825 5925 3850 
L 5925 3400 
Q 5650 3425 5500 3425 
Q 5500 3025 5362 2162 
Q 5225 1300 4875 675 
Q 5125 375 5437 137 
Q 5750 -100 6025 -200 
Q 5725 -425 5625 -625 
Q 5325 -450 5075 -225 
Q 4825 0 4575 325 
Q 4300 25 3987 -187 
Q 3675 -400 3200 -650 
Q 3075 -425 2850 -300 
Q 3225 -150 3650 125 
Q 4075 400 4300 675 
Q 4125 1025 3962 1425 
Q 3800 1825 3675 2475 
Q 3600 2275 3425 1950 
Q 3250 2050 3000 2150 
Q 3400 2850 3650 3675 
Q 3900 4500 3975 5100 
Q 4275 4975 4525 4925 
z
M 2100 2250 
Q 2000 2150 1875 1925 
L 3250 1925 
Q 3100 1300 2725 675 
Q 3125 525 3350 400 
Q 3225 225 3125 25 
Q 2900 175 2450 325 
Q 1950 -225 725 -650 
Q 600 -375 400 -275 
Q 1575 0 2000 475 
Q 1300 650 850 775 
Q 1000 1000 1250 1525 
Q 1000 1525 425 1500 
L 425 1950 
Q 900 1925 1400 1925 
Q 1500 2150 1550 2425 
Q 1825 2325 2100 2250 
z
M 1775 3950 
Q 1775 4550 1750 5100 
L 2250 5100 
Q 2225 4575 2225 3950 
Q 3075 3950 3425 3975 
L 3425 3525 
Q 3075 3550 2225 3550 
Q 2225 2825 2250 2425 
L 1750 2425 
Q 1775 2775 1775 3300 
Q 1650 3100 1300 2787 
Q 950 2475 650 2300 
Q 525 2525 275 2625 
Q 500 2700 875 2975 
Q 1250 3250 1450 3550 
Q 950 3550 500 3525 
L 500 3975 
Q 925 3950 1775 3950 
z
M 3975 3175 
Q 4200 1700 4600 1125 
Q 4850 1700 4937 2287 
Q 5025 2875 5050 3425 
L 4075 3425 
L 3975 3175 
z
M 1450 1000 
Q 1750 925 2275 800 
Q 2475 1050 2650 1525 
L 1725 1525 
Q 1600 1275 1450 1000 
z
M 3325 4825 
Q 3200 4625 3125 4450 
Q 3050 4275 2925 4025 
Q 2725 4125 2525 4175 
Q 2725 4500 2900 5000 
Q 3150 4875 3325 4825 
z
M 2575 3375 
Q 2900 3050 3200 2725 
Q 3050 2600 2875 2425 
Q 2525 2850 2300 3100 
Q 2450 3225 2575 3375 
z
M 925 5000 
Q 1275 4650 1500 4300 
L 1125 4075 
Q 950 4425 600 4725 
Q 825 4875 925 5000 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-523a"/>
      <use xlink:href="#SimHei-6fc0" x="100"/>
      <use xlink:href="#SimHei-6b21" x="200"/>
      <use xlink:href="#SimHei-6570" x="300"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_7">
      <path d="M 39.29375 393.48625 
L 709.2 393.48625 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <defs>
       <path id="m2497f89aa7" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="393.48625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(28.29375 396.23625) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <path d="M 39.29375 328.056369 
L 709.2 328.056369 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="328.056369" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 10 -->
      <g transform="translate(24.29375 330.806369) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_11">
      <path d="M 39.29375 262.626487 
L 709.2 262.626487 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="262.626487" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 20 -->
      <g transform="translate(24.29375 265.376487) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-32"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_13">
      <path d="M 39.29375 197.196606 
L 709.2 197.196606 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="197.196606" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 30 -->
      <g transform="translate(24.29375 199.946606) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-33"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_15">
      <path d="M 39.29375 131.766725 
L 709.2 131.766725 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="131.766725" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 40 -->
      <g transform="translate(24.29375 134.516725) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-34"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_17">
      <path d="M 39.29375 66.336844 
L 709.2 66.336844 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m2497f89aa7" x="39.29375" y="66.336844" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 50 -->
      <g transform="translate(24.29375 69.086844) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-35"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="text_11">
     <!-- 主观自信分数 -->
     <g transform="translate(19.04375 239.313125) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-4e3b" d="M 4750 3850 
Q 5150 3850 5750 3875 
L 5750 3400 
Q 5150 3425 4750 3425 
L 3425 3425 
L 3425 2175 
L 4425 2175 
Q 4825 2175 5375 2200 
L 5375 1725 
Q 4825 1750 4425 1750 
L 3425 1750 
L 3425 150 
L 5000 150 
Q 5450 150 6050 175 
L 6050 -300 
Q 5450 -275 5000 -275 
L 350 -275 
L 350 200 
Q 950 150 1425 150 
L 2900 150 
L 2900 1750 
L 1925 1750 
Q 1525 1750 975 1725 
L 975 2200 
Q 1575 2175 1925 2175 
L 2900 2175 
L 2900 3425 
L 1650 3425 
Q 1125 3425 625 3400 
L 625 3875 
Q 1175 3850 1675 3850 
L 4750 3850 
z
M 3175 3900 
Q 2875 4350 2400 4875 
Q 2650 5050 2800 5200 
Q 3325 4600 3625 4175 
Q 3475 4100 3175 3900 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-89c2" d="M 2600 4750 
Q 2500 3900 2387 3325 
Q 2275 2750 2075 2150 
Q 2425 1625 2800 1000 
Q 2575 825 2350 625 
Q 2100 1200 1800 1675 
Q 1600 1275 1300 912 
Q 1000 550 650 175 
Q 500 375 200 625 
Q 675 950 975 1300 
Q 1275 1650 1500 2100 
Q 1075 2725 625 3200 
Q 850 3375 1025 3525 
Q 1375 3150 1725 2600 
Q 1975 3400 2075 4350 
L 1300 4350 
Q 850 4350 475 4325 
L 475 4775 
Q 825 4750 1275 4750 
L 2600 4750 
z
M 4775 225 
Q 4775 0 4887 -25 
Q 5000 -50 5225 -50 
Q 5475 -50 5537 37 
Q 5600 125 5650 675 
Q 5875 525 6175 450 
Q 6025 -300 5850 -400 
Q 5675 -500 5400 -500 
L 4825 -500 
Q 4525 -500 4425 -325 
Q 4325 -150 4325 50 
L 4325 1600 
Q 4200 1125 3900 687 
Q 3600 250 3187 -87 
Q 2775 -425 2325 -650 
Q 2225 -400 1925 -150 
Q 2625 25 3087 475 
Q 3550 925 3737 1425 
Q 3925 1925 3962 2562 
Q 4000 3200 3975 3875 
Q 4325 3825 4550 3800 
Q 4450 3400 4425 2750 
Q 4400 2100 4325 1700 
L 4825 1700 
Q 4800 1550 4775 1025 
L 4775 225 
z
M 5700 5050 
Q 5675 4650 5675 3350 
Q 5675 2050 5700 1575 
L 5200 1575 
L 5200 4650 
L 3450 4650 
L 3450 1550 
L 2950 1550 
Q 2975 2150 2975 3300 
Q 2975 4475 2950 5050 
L 5700 5050 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-81ea" d="M 3200 5075 
Q 3050 4850 2900 4225 
L 5400 4225 
Q 5375 3650 5375 3175 
L 5375 375 
Q 5375 -75 5400 -525 
L 4900 -525 
L 4900 75 
L 1550 75 
L 1550 -525 
L 1050 -525 
Q 1075 -50 1075 350 
L 1075 3200 
Q 1075 3650 1050 4225 
L 2425 4225 
Q 2525 4900 2550 5225 
Q 2850 5125 3200 5075 
z
M 4900 475 
L 4900 1375 
L 1550 1375 
L 1550 475 
L 4900 475 
z
M 4900 2975 
L 4900 3800 
L 1550 3800 
L 1550 2975 
L 4900 2975 
z
M 4900 1775 
L 4900 2575 
L 1550 2575 
L 1550 1775 
L 4900 1775 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-4fe1" d="M 5650 1575 
Q 5625 1275 5625 575 
Q 5625 -100 5650 -450 
L 5175 -450 
L 5175 -25 
L 3025 -25 
L 3025 -550 
L 2525 -550 
Q 2550 -175 2550 500 
Q 2550 1200 2525 1575 
L 5650 1575 
z
M 2125 4975 
Q 1975 4825 1650 3975 
L 1650 550 
Q 1650 -200 1675 -625 
L 1125 -625 
Q 1200 -175 1200 575 
L 1200 3100 
Q 900 2500 625 2100 
Q 475 2350 250 2500 
Q 525 2800 925 3562 
Q 1325 4325 1525 5175 
Q 1800 5075 2125 4975 
z
M 5175 375 
L 5175 1225 
L 3025 1225 
L 3025 375 
L 5175 375 
z
M 6075 3900 
Q 5575 3925 4100 3925 
Q 2625 3925 2100 3900 
L 2100 4325 
L 6075 4325 
L 6075 3900 
z
M 5700 3000 
Q 5275 3025 4150 3025 
Q 3025 3025 2475 3000 
L 2475 3425 
Q 2950 3400 4100 3400 
Q 5275 3400 5700 3425 
L 5700 3000 
z
M 5700 2125 
Q 5350 2150 4175 2150 
Q 3025 2150 2475 2125 
L 2475 2550 
Q 3000 2525 4175 2525 
Q 5375 2525 5700 2550 
L 5700 2125 
z
M 4025 5200 
Q 4100 5050 4175 4875 
Q 4250 4700 4325 4500 
Q 4100 4425 3875 4375 
Q 3700 4900 3575 5025 
Q 3825 5125 4025 5200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5206" d="M 1000 -650 
Q 825 -375 575 -225 
Q 975 -75 1300 137 
Q 1625 350 1875 662 
Q 2125 975 2250 1350 
Q 2375 1725 2425 2275 
Q 1850 2275 1500 2250 
L 1500 2775 
Q 1900 2750 2525 2750 
L 5025 2750 
Q 4975 2375 4950 2100 
L 4750 250 
Q 4700 -200 4350 -350 
Q 4000 -500 3575 -525 
Q 3550 -275 3325 50 
Q 3850 25 4037 87 
Q 4225 150 4275 525 
L 4450 2275 
L 2950 2275 
Q 2875 1625 2737 1187 
Q 2600 750 2350 425 
Q 2100 100 1750 -175 
Q 1400 -450 1000 -650 
z
M 3950 5175 
Q 4125 4650 4662 3975 
Q 5200 3300 6175 2800 
Q 5975 2625 5775 2275 
Q 5325 2600 4987 2887 
Q 4650 3175 4425 3425 
Q 4200 3675 3925 4137 
Q 3650 4600 3475 5000 
Q 3750 5075 3950 5175 
z
M 2700 4825 
Q 2475 4425 2325 4125 
Q 2175 3825 2025 3587 
Q 1875 3350 1575 2950 
Q 1275 2550 850 2125 
Q 625 2350 400 2475 
Q 900 2925 1187 3300 
Q 1475 3675 1737 4112 
Q 2000 4550 2200 5075 
Q 2400 4925 2700 4825 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-4e3b"/>
      <use xlink:href="#SimHei-89c2" x="100"/>
      <use xlink:href="#SimHei-81ea" x="200"/>
      <use xlink:href="#SimHei-4fe1" x="300"/>
      <use xlink:href="#SimHei-5206" x="400"/>
      <use xlink:href="#SimHei-6570" x="500"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 69.744034 393.48625 
L 148.689215 393.48625 
L 148.689215 75.958885 
L 69.744034 75.958885 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_4">
    <path d="M 295.301694 393.48625 
L 374.246875 393.48625 
L 374.246875 94.818086 
L 295.301694 94.818086 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_5">
    <path d="M 520.859354 393.48625 
L 599.804535 393.48625 
L 599.804535 105.594772 
L 520.859354 105.594772 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_6">
    <path d="M 148.689215 393.48625 
L 227.634396 393.48625 
L 227.634396 92.508796 
L 148.689215 92.508796 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_7">
    <path d="M 374.246875 393.48625 
L 453.192056 393.48625 
L 453.192056 101.23278 
L 374.246875 101.23278 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_8">
    <path d="M 599.804535 393.48625 
L 678.749716 393.48625 
L 678.749716 71.571234 
L 599.804535 71.571234 
z
" clip-path="url(#p3ddbfd59a4)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="LineCollection_1">
    <path d="M 109.216625 90.165007 
L 109.216625 61.752763 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 334.774285 111.261364 
L 334.774285 78.374808 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 560.331944 125.287779 
L 560.331944 85.901765 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_19">
    <defs>
     <path id="m283bb9dc50" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p3ddbfd59a4)">
     <use xlink:href="#m283bb9dc50" x="109.216625" y="90.165007" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="334.774285" y="111.261364" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="560.331944" y="125.287779" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_20">
    <g clip-path="url(#p3ddbfd59a4)">
     <use xlink:href="#m283bb9dc50" x="109.216625" y="61.752763" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="334.774285" y="78.374808" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="560.331944" y="85.901765" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 188.161806 110.387643 
L 188.161806 74.629949 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 413.719465 122.155408 
L 413.719465 80.310153 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 639.277125 100.462171 
L 639.277125 42.680298 
" clip-path="url(#p3ddbfd59a4)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_21">
    <g clip-path="url(#p3ddbfd59a4)">
     <use xlink:href="#m283bb9dc50" x="188.161806" y="110.387643" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="413.719465" y="122.155408" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="639.277125" y="100.462171" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_22">
    <g clip-path="url(#p3ddbfd59a4)">
     <use xlink:href="#m283bb9dc50" x="188.161806" y="74.629949" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="413.719465" y="80.310153" style="stroke: #000000"/>
     <use xlink:href="#m283bb9dc50" x="639.277125" y="42.680298" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_9">
    <path d="M 39.29375 393.48625 
L 39.29375 25.14 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 709.2 393.48625 
L 709.2 25.14 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 39.29375 393.48625 
L 709.2 393.48625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 39.29375 25.14 
L 709.2 25.14 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_12">
    <!-- 高低焦虑组主观自信变化 -->
    <g transform="translate(319.246875 19.14) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-9ad8" d="M 5825 -25 
Q 5850 -400 5550 -500 
Q 5250 -600 4975 -625 
Q 4875 -325 4725 -100 
Q 4975 -100 5175 -87 
Q 5375 -75 5350 250 
L 5350 1825 
L 1100 1825 
L 1100 -575 
L 600 -575 
Q 625 -225 625 850 
Q 625 1950 600 2200 
L 5850 2200 
Q 5825 1850 5825 1425 
L 5825 -25 
z
M 4925 3800 
Q 4900 3475 4900 3175 
Q 4900 2900 4925 2600 
L 1400 2600 
Q 1425 2825 1425 3175 
Q 1425 3525 1400 3800 
L 4925 3800 
z
M 4500 1400 
Q 4475 1125 4475 800 
Q 4475 500 4500 200 
L 1950 200 
Q 2000 500 2000 800 
Q 2000 1125 1950 1400 
L 4500 1400 
z
M 425 4600 
Q 750 4575 1125 4575 
L 2925 4575 
Q 2850 4875 2750 5100 
Q 3050 5125 3350 5200 
Q 3400 4950 3525 4575 
L 5250 4575 
Q 5600 4575 5975 4600 
L 5975 4150 
Q 5600 4175 5050 4175 
L 1150 4175 
Q 750 4175 425 4150 
L 425 4600 
z
M 4450 2975 
L 4450 3425 
L 1900 3425 
L 1900 2975 
L 4450 2975 
z
M 4025 550 
L 4025 1050 
L 2400 1050 
L 2400 550 
L 4025 550 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4f4e" d="M 2300 4550 
Q 2750 4525 3750 4637 
Q 4750 4750 5275 5000 
Q 5425 4725 5600 4475 
Q 5125 4400 4350 4300 
Q 4350 3825 4400 2850 
L 5000 2850 
Q 5425 2850 5850 2875 
L 5850 2425 
Q 5425 2450 5025 2450 
L 4450 2450 
Q 4575 1325 4862 762 
Q 5150 200 5325 75 
Q 5500 -50 5562 125 
Q 5625 300 5675 775 
Q 5925 575 6150 525 
Q 6000 -100 5850 -337 
Q 5700 -575 5350 -500 
Q 5000 -425 4587 262 
Q 4175 950 4000 2450 
L 2775 2450 
L 2775 500 
Q 3275 950 3575 1275 
Q 3675 975 3775 800 
Q 2775 -50 2500 -350 
Q 2325 -175 2175 25 
Q 2300 150 2325 500 
L 2325 3725 
Q 2325 4075 2300 4550 
z
M 2050 5075 
Q 1900 4900 1600 3950 
L 1600 425 
Q 1600 -75 1625 -625 
L 1100 -625 
Q 1125 -75 1125 450 
L 1125 3025 
Q 875 2500 625 2125 
Q 400 2350 225 2425 
Q 600 2900 962 3687 
Q 1325 4475 1500 5275 
Q 1575 5200 2050 5075 
z
M 3950 2850 
Q 3900 3775 3900 4225 
Q 3125 4175 2775 4150 
L 2775 2850 
L 3950 2850 
z
M 3850 -625 
Q 3450 -75 3250 100 
Q 3450 275 3575 425 
Q 3925 50 4225 -300 
Q 4025 -450 3850 -625 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7126" d="M 2375 4900 
Q 2125 4625 1975 4300 
L 5075 4300 
Q 5400 4300 5900 4325 
L 5900 3850 
Q 5400 3875 5075 3875 
L 3875 3875 
L 3875 3300 
L 4700 3300 
Q 5200 3300 5600 3325 
L 5600 2875 
Q 5200 2900 4700 2900 
L 3875 2900 
L 3875 2325 
L 4750 2325 
Q 5150 2325 5600 2350 
L 5600 1900 
Q 5150 1925 4750 1925 
L 3875 1925 
L 3875 1400 
L 5025 1400 
Q 5500 1400 5950 1425 
L 5950 950 
Q 5500 1000 5050 1000 
L 1750 1000 
L 1750 675 
L 1200 675 
Q 1225 1250 1225 1700 
L 1225 3075 
Q 925 2625 600 2275 
Q 400 2525 200 2675 
Q 800 3225 1225 3950 
Q 1650 4675 1800 5200 
Q 2100 5025 2375 4900 
z
M 3350 2325 
L 3350 2900 
L 1750 2900 
L 1750 2325 
L 3350 2325 
z
M 3350 3300 
L 3350 3875 
L 1750 3875 
L 1750 3300 
L 3350 3300 
z
M 3350 1400 
L 3350 1925 
L 1750 1925 
L 1750 1400 
L 3350 1400 
z
M 350 -500 
Q 775 -25 975 525 
Q 1225 450 1500 375 
Q 1250 -25 925 -700 
Q 700 -575 350 -500 
z
M 3000 -525 
Q 2675 -575 2450 -650 
Q 2375 -175 2225 375 
Q 2450 425 2775 525 
L 3000 -525 
z
M 5675 -575 
Q 5500 -225 5125 425 
Q 5350 525 5550 700 
Q 5850 150 6175 -250 
Q 5850 -400 5675 -575 
z
M 4175 600 
Q 4425 -100 4525 -425 
Q 4225 -500 4000 -575 
Q 3900 -150 3650 450 
Q 3950 525 4175 600 
z
M 3600 5275 
Q 3800 5025 4175 4625 
Q 3925 4525 3700 4350 
Q 3500 4650 3200 5025 
Q 3400 5125 3600 5275 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8651" d="M 3225 5275 
Q 3200 5000 3200 4825 
L 4550 4825 
Q 4900 4825 5325 4850 
L 5325 4425 
Q 4900 4450 4550 4450 
L 3200 4450 
L 3200 3950 
L 5975 3950 
Q 5825 3550 5700 3000 
Q 5450 3075 5175 3100 
Q 5250 3350 5325 3575 
L 3300 3575 
L 3300 3050 
Q 4175 3150 4725 3225 
L 4825 2850 
Q 4350 2800 3300 2650 
L 3300 2400 
Q 3325 2150 3650 2150 
L 4500 2150 
Q 4750 2150 4862 2175 
Q 4975 2200 5075 2675 
Q 5225 2525 5575 2400 
Q 5400 1975 5237 1850 
Q 5075 1725 4675 1750 
L 3475 1750 
Q 2850 1750 2850 2225 
L 2850 2600 
Q 1850 2450 1550 2400 
L 1525 2850 
Q 1850 2850 2850 2975 
L 2850 3575 
L 1350 3575 
Q 1350 1775 1300 1287 
Q 1250 800 1112 362 
Q 975 -75 725 -600 
Q 575 -475 250 -325 
Q 475 50 650 450 
Q 825 850 862 1287 
Q 900 1725 900 2625 
Q 900 3525 875 3950 
L 2750 3950 
L 2750 4475 
Q 2750 4825 2725 5275 
L 3225 5275 
z
M 3050 1125 
Q 3025 750 3025 500 
L 3025 150 
Q 3025 -150 3350 -150 
L 4050 -150 
Q 4350 -150 4475 -87 
Q 4600 -25 4650 400 
Q 4875 175 5150 125 
Q 4975 -300 4862 -425 
Q 4750 -550 4300 -550 
L 3175 -550 
Q 2575 -550 2575 -100 
L 2575 300 
Q 2575 750 2550 1125 
L 3050 1125 
z
M 2350 1000 
Q 1900 50 1700 -325 
Q 1500 -225 1250 -75 
Q 1475 225 1875 1225 
Q 2150 1075 2350 1000 
z
M 5225 1350 
Q 5500 1050 6125 200 
Q 5900 50 5725 -75 
Q 5425 350 4875 1050 
Q 4975 1125 5225 1350 
z
M 3700 1575 
Q 4200 925 4350 675 
Q 4075 525 3925 400 
Q 3800 650 3325 1325 
Q 3500 1425 3700 1575 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7ec4" d="M 6125 -425 
Q 5750 -400 5225 -400 
L 3275 -400 
Q 2775 -400 2400 -425 
L 2400 50 
Q 2725 25 2950 25 
L 2950 3775 
Q 2950 4375 2900 4625 
L 5525 4625 
Q 5500 4400 5500 3775 
L 5500 25 
Q 5825 25 6125 50 
L 6125 -425 
z
M 1725 4825 
Q 1500 4550 1287 4137 
Q 1075 3725 725 3050 
Q 950 3050 1550 3075 
Q 1800 3550 1900 3975 
Q 2075 3825 2450 3675 
Q 2225 3350 1937 2875 
Q 1650 2400 1150 1625 
Q 1950 1725 2425 1775 
L 2425 1350 
Q 2100 1325 1575 1250 
Q 1050 1175 575 1050 
Q 550 1300 400 1575 
Q 675 1675 912 1975 
Q 1150 2275 1375 2725 
Q 1225 2725 950 2687 
Q 675 2650 425 2525 
Q 325 2825 225 3050 
Q 450 3300 712 3862 
Q 975 4425 1150 5075 
Q 1425 4925 1725 4825 
z
M 5025 25 
L 5025 1275 
L 3425 1275 
L 3425 25 
L 5025 25 
z
M 5025 3125 
L 5025 4225 
L 3425 4225 
L 3425 3125 
L 5025 3125 
z
M 5025 1700 
L 5025 2725 
L 3425 2725 
L 3425 1700 
L 5025 1700 
z
M 2375 175 
Q 1875 100 1375 -12 
Q 875 -125 450 -300 
Q 375 -75 250 250 
Q 925 325 1162 375 
Q 1400 425 2375 600 
L 2375 175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-53d8" d="M 5200 1975 
Q 4950 1600 4537 1137 
Q 4125 675 3675 375 
Q 3925 225 4600 87 
Q 5275 -50 6000 -75 
Q 5700 -400 5650 -625 
Q 4850 -525 4212 -312 
Q 3575 -100 3225 125 
Q 2800 -125 2150 -325 
Q 1500 -525 700 -675 
Q 600 -425 400 -175 
Q 950 -150 1687 12 
Q 2425 175 2825 375 
Q 2275 775 1675 1575 
Q 1350 1575 1000 1550 
L 1000 2000 
Q 1450 1975 2075 1975 
L 5200 1975 
z
M 3225 5275 
Q 3400 4900 3550 4475 
L 4675 4475 
Q 5400 4475 5975 4500 
L 5975 4025 
Q 5375 4050 4725 4050 
L 4125 4050 
L 4125 2950 
Q 4125 2550 4150 2150 
L 3600 2150 
Q 3625 2550 3625 2950 
L 3625 4050 
L 2750 4050 
L 2750 2950 
Q 2750 2675 2775 2150 
L 2225 2150 
Q 2250 2675 2250 2950 
L 2250 4050 
L 1700 4050 
Q 975 4050 425 4025 
L 425 4500 
Q 950 4475 1700 4475 
L 2975 4475 
Q 2925 4675 2700 5125 
Q 2950 5150 3225 5275 
z
M 2200 1575 
Q 2650 975 3250 600 
Q 3850 1000 4275 1575 
L 2200 1575 
z
M 4850 3750 
Q 5375 3275 5950 2675 
Q 5750 2525 5500 2300 
Q 5075 2800 4450 3400 
Q 4675 3575 4850 3750 
z
M 1825 3500 
Q 1700 3375 1425 3025 
Q 1150 2675 825 2300 
Q 575 2475 375 2600 
Q 975 3150 1300 3825 
Q 1525 3650 1825 3500 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5316" d="M 3925 5025 
Q 3900 4900 3900 4400 
L 3900 2625 
Q 4375 3025 4775 3450 
Q 5175 3875 5400 4300 
Q 5600 4100 5900 3925 
Q 5700 3700 5225 3212 
Q 4750 2725 4487 2512 
Q 4225 2300 3900 2025 
L 3900 450 
Q 3900 50 4225 50 
L 5200 50 
Q 5400 75 5462 262 
Q 5525 450 5575 850 
Q 5800 650 6150 600 
Q 6000 -100 5825 -250 
Q 5650 -400 5350 -400 
L 3950 -400 
Q 3425 -400 3400 200 
L 3400 1675 
Q 3225 1525 2962 1350 
Q 2700 1175 2375 975 
Q 2225 1225 2025 1400 
Q 2350 1550 2737 1787 
Q 3125 2025 3400 2250 
L 3400 4500 
Q 3400 4800 3375 5025 
L 3925 5025 
z
M 2400 4850 
Q 2175 4550 2075 4337 
Q 1975 4125 1825 3775 
L 1825 675 
Q 1825 -75 1850 -550 
L 1325 -550 
Q 1350 -125 1350 650 
L 1350 3050 
Q 1125 2725 962 2525 
Q 800 2325 675 2175 
Q 500 2375 275 2525 
Q 625 2850 875 3212 
Q 1125 3575 1312 3912 
Q 1500 4250 1625 4575 
Q 1750 4900 1825 5100 
Q 1975 4975 2400 4850 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-9ad8"/>
     <use xlink:href="#SimHei-4f4e" x="100"/>
     <use xlink:href="#SimHei-7126" x="200"/>
     <use xlink:href="#SimHei-8651" x="300"/>
     <use xlink:href="#SimHei-7ec4" x="400"/>
     <use xlink:href="#SimHei-4e3b" x="500"/>
     <use xlink:href="#SimHei-89c2" x="600"/>
     <use xlink:href="#SimHei-81ea" x="700"/>
     <use xlink:href="#SimHei-4fe1" x="800"/>
     <use xlink:href="#SimHei-53d8" x="900"/>
     <use xlink:href="#SimHei-5316" x="1000"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_13">
     <path d="M 646 54.4775 
L 703.6 54.4775 
Q 705.2 54.4775 705.2 52.8775 
L 705.2 30.74 
Q 705.2 29.14 703.6 29.14 
L 646 29.14 
Q 644.4 29.14 644.4 30.74 
L 644.4 52.8775 
Q 644.4 54.4775 646 54.4775 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_14">
     <path d="M 647.6 38.80875 
L 663.6 38.80875 
L 663.6 33.20875 
L 647.6 33.20875 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_13">
     <!-- 高焦虑组 -->
     <g transform="translate(670 38.80875) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-9ad8"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
    <g id="patch_15">
     <path d="M 647.6 50.2775 
L 663.6 50.2775 
L 663.6 44.6775 
L 647.6 44.6775 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_14">
     <!-- 低焦虑组 -->
     <g transform="translate(670 50.2775) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-4f4e"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p3ddbfd59a4">
   <rect x="39.29375" y="25.14" width="669.90625" height="368.34625"/>
  </clipPath>
 </defs>
</svg>

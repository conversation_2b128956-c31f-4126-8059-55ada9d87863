import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader

from data_provider.data_loader import Dataset_Custom, Dataset_Synthetic

def data_provider(config, flag='train'):
    """
    数据加载工厂函数
    
    Args:
        config: 配置对象
        flag: 'train', 'val', 'test'
    
    Returns:
        data_set: 数据集对象
        data_loader: 数据加载器
    """
    if config.dataset == 'synthetic':
        # 合成数据集
        data_set = Dataset_Synthetic(
            root_path=config.data_path,
            flag=flag,
            size=[config.seq_len, config.label_len, config.pred_len],
            features=config.features
        )
    else:
        # 自定义数据集
        data_set = Dataset_Custom(
            root_path=config.data_path,
            data_path=config.dataset + '.csv',
            flag=flag,
            size=[config.seq_len, config.label_len, config.pred_len],
            features=config.features,
            target=config.target,
            freq=config.freq
        )
    
    # 数据加载器
    if flag == 'train':
        shuffle_flag = True
    else:
        shuffle_flag = False
    
    data_loader = DataLoader(
        data_set,
        batch_size=config.batch_size,
        shuffle=shuffle_flag,
        num_workers=config.num_workers,
        drop_last=True
    )
    
    return data_set, data_loader

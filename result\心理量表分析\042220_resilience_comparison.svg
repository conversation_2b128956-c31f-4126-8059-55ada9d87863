<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="720pt" height="432pt" viewBox="0 0 720 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:34.694873</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 720 432 
L 720 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 43.1675 393.53875 
L 709.2 393.53875 
L 709.2 25.16625 
L 43.1675 25.16625 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 130.717227 393.53875 
L 130.717227 25.16625 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m9849ce4537" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m9849ce4537" x="130.717227" y="393.53875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 总体韧性 -->
      <g transform="translate(114.717227 406.78875) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-603b" d="M 4550 4850 
Q 4375 4650 4025 3900 
L 5100 3900 
Q 5075 3425 5075 2950 
Q 5075 2500 5100 2025 
L 1175 2025 
Q 1200 2550 1200 3000 
Q 1200 3475 1175 3900 
L 2175 3900 
Q 1900 4350 1525 4750 
Q 1700 4850 1875 5050 
Q 2350 4550 2575 4200 
Q 2400 4075 2250 3900 
L 3550 3900 
Q 3875 4700 3950 5100 
Q 4300 4925 4550 4850 
z
M 4600 2450 
L 4600 3525 
L 1650 3525 
L 1650 2450 
L 4600 2450 
z
M 2275 1450 
Q 2250 1050 2250 675 
Q 2250 300 2262 112 
Q 2275 -75 2675 -75 
L 4000 -75 
Q 4325 -75 4400 550 
Q 4650 400 4925 325 
Q 4775 -200 4562 -337 
Q 4350 -475 4000 -450 
L 2325 -450 
Q 1850 -450 1825 -175 
Q 1800 100 1800 525 
Q 1800 975 1775 1450 
L 2275 1450 
z
M 1350 1325 
Q 1050 300 825 -250 
Q 625 -150 375 -50 
Q 675 525 900 1500 
Q 1100 1425 1350 1325 
z
M 4950 1575 
Q 5700 825 6000 450 
Q 5825 275 5625 100 
Q 5375 450 4600 1275 
Q 4750 1375 4950 1575 
z
M 3350 575 
Q 2975 1175 2675 1500 
Q 2825 1650 3000 1800 
Q 3225 1575 3750 875 
Q 3525 775 3350 575 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-4f53" d="M 3650 3850 
Q 3650 4750 3625 5125 
L 4125 5125 
Q 4100 4775 4100 3850 
L 5200 3850 
Q 5700 3850 5925 3875 
L 5925 3425 
Q 5700 3450 5200 3450 
L 4525 3450 
Q 4925 1850 6200 1200 
Q 5925 1050 5775 775 
Q 4425 1800 4100 3425 
L 4100 1025 
Q 4825 1025 5050 1050 
L 5050 600 
Q 4825 625 4100 625 
Q 4100 75 4125 -575 
L 3625 -575 
Q 3650 100 3650 625 
Q 3200 625 2700 600 
L 2700 1050 
Q 3200 1025 3650 1025 
L 3650 3450 
Q 3375 1800 2000 650 
Q 1850 875 1550 1025 
Q 2850 1875 3200 3450 
L 2675 3450 
Q 2275 3450 1975 3425 
L 1975 3875 
Q 2275 3850 2675 3850 
L 3650 3850 
z
M 2000 4900 
Q 1825 4675 1525 3775 
L 1525 575 
Q 1525 -200 1550 -550 
L 1050 -550 
Q 1075 -225 1075 550 
L 1075 2900 
Q 950 2625 575 2050 
Q 450 2275 225 2425 
Q 525 2725 925 3612 
Q 1325 4500 1425 5125 
Q 1700 5000 2000 4900 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-97e7" d="M 1875 750 
Q 2325 750 2387 800 
Q 2450 850 2475 1700 
L 1675 1700 
L 1675 150 
Q 1675 -275 1700 -650 
L 1200 -650 
Q 1225 -225 1225 150 
L 1225 1700 
Q 600 1700 200 1675 
L 200 2100 
Q 650 2075 1225 2075 
L 1225 2800 
Q 775 2800 425 2775 
L 425 3200 
Q 800 3175 1225 3175 
L 1225 3775 
Q 700 3775 300 3750 
L 300 4175 
Q 725 4150 1225 4150 
Q 1225 4700 1200 5100 
L 1700 5100 
Q 1675 4700 1675 4150 
Q 2425 4150 2975 4175 
L 2975 3750 
Q 2425 3775 1675 3775 
L 1675 3175 
Q 2325 3175 2775 3200 
L 2775 2775 
Q 2300 2800 1675 2800 
L 1675 2050 
L 2975 2050 
Q 2950 1675 2925 1225 
Q 2900 775 2875 612 
Q 2850 450 2612 375 
Q 2375 300 2100 225 
Q 2075 475 1875 750 
z
M 4575 225 
Q 5075 200 5225 250 
Q 5375 300 5375 550 
L 5475 4200 
L 4775 4200 
Q 4775 3275 4625 2300 
Q 4475 1325 4087 662 
Q 3700 0 3150 -575 
Q 3025 -425 2625 -300 
Q 3275 125 3787 1162 
Q 4300 2200 4300 4200 
Q 3775 4200 3250 4175 
L 3250 4575 
Q 3750 4550 4250 4550 
L 5925 4550 
Q 5900 4225 5900 3700 
L 5825 425 
Q 5825 -100 5550 -187 
Q 5275 -275 4800 -350 
Q 4775 -75 4575 225 
z
M 3150 1775 
Q 3300 2775 3300 3375 
Q 3550 3325 3850 3325 
Q 3800 3125 3750 2687 
Q 3700 2250 3575 1725 
Q 3350 1775 3150 1775 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-6027" d="M 3450 4525 
Q 3325 4075 3275 3750 
L 4050 3750 
L 4050 4275 
Q 4050 4700 4025 5100 
L 4550 5100 
Q 4525 4700 4525 4275 
L 4525 3750 
L 5025 3750 
Q 5350 3750 5800 3775 
L 5800 3300 
Q 5350 3325 5025 3325 
L 4525 3325 
L 4525 2000 
L 4975 2000 
Q 5350 2000 5725 2025 
L 5725 1550 
Q 5350 1575 4975 1575 
L 4525 1575 
L 4525 25 
L 5200 25 
Q 5625 25 6025 50 
L 6025 -400 
Q 5625 -375 5200 -375 
L 3275 -375 
Q 2825 -375 2400 -400 
L 2400 50 
Q 2825 25 3250 25 
L 4050 25 
L 4050 1575 
L 3625 1575 
Q 3300 1575 2875 1550 
L 2875 2025 
Q 3200 2000 3625 2000 
L 4050 2000 
L 4050 3325 
L 3175 3325 
Q 3100 2950 2775 2225 
Q 2500 2375 2325 2400 
Q 2675 3125 2775 3650 
Q 2875 4175 2950 4650 
Q 3200 4575 3450 4525 
z
M 1700 5150 
Q 1675 4650 1675 4175 
L 1675 425 
Q 1675 -100 1700 -625 
L 1175 -625 
Q 1200 -100 1200 425 
L 1200 4175 
Q 1200 4700 1175 5150 
L 1700 5150 
z
M 975 3725 
Q 825 2675 700 2150 
Q 450 2200 250 2250 
Q 425 2900 525 3800 
Q 725 3775 975 3725 
z
M 2125 4025 
Q 2325 3625 2525 3000 
Q 2300 2925 2125 2825 
Q 1950 3375 1750 3850 
Q 1950 3950 2125 4025 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-603b"/>
       <use xlink:href="#SimHei-4f53" x="100"/>
       <use xlink:href="#SimHei-97e7" x="200"/>
       <use xlink:href="#SimHei-6027" x="300"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 294.361576 393.53875 
L 294.361576 25.16625 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m9849ce4537" x="294.361576" y="393.53875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 坚韧性 -->
      <g transform="translate(282.361576 406.78875) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-575a" d="M 5675 4725 
Q 5475 4200 5162 3700 
Q 4850 3200 4575 2925 
Q 5250 2400 6000 2200 
Q 5775 2025 5600 1750 
Q 4825 2125 4250 2625 
Q 3900 2350 3500 2150 
Q 3100 1950 2750 1825 
Q 2625 2075 2450 2250 
Q 2850 2325 3212 2487 
Q 3575 2650 3925 2900 
Q 3425 3375 3025 4350 
Q 2800 4325 2575 4325 
L 2575 4750 
Q 3050 4725 3700 4725 
L 5675 4725 
z
M 2950 1200 
Q 2950 1500 2925 1850 
L 3425 1850 
Q 3400 1500 3400 1200 
L 4550 1200 
Q 4925 1200 5325 1225 
L 5325 775 
Q 4925 800 4575 800 
L 3400 800 
L 3400 -50 
L 5000 -50 
Q 5475 -50 6075 -25 
L 6075 -500 
Q 5450 -475 5025 -475 
L 1375 -475 
Q 800 -475 350 -500 
L 350 -25 
Q 825 -50 1425 -50 
L 2950 -50 
L 2950 800 
L 2000 800 
Q 1300 800 1100 775 
L 1100 1225 
Q 1300 1200 2000 1200 
L 2950 1200 
z
M 2300 5075 
Q 2275 4650 2275 4225 
L 2275 2775 
Q 2275 2250 2300 1800 
L 1800 1800 
Q 1825 2250 1825 2775 
L 1825 4225 
Q 1825 4675 1800 5075 
L 2300 5075 
z
M 1200 4825 
Q 1175 4475 1175 4050 
L 1175 2825 
Q 1175 2400 1200 2000 
L 675 2000 
Q 700 2425 700 2825 
L 700 4050 
Q 700 4450 675 4825 
L 1200 4825 
z
M 3475 4350 
Q 3775 3625 4250 3200 
Q 4675 3575 5000 4350 
L 3700 4350 
L 3475 4350 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-575a"/>
       <use xlink:href="#SimHei-97e7" x="100"/>
       <use xlink:href="#SimHei-6027" x="200"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 458.005924 393.53875 
L 458.005924 25.16625 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m9849ce4537" x="458.005924" y="393.53875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 乐观性 -->
      <g transform="translate(446.005924 406.78875) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-4e50" d="M 3100 2700 
L 3100 3200 
Q 3100 3400 3075 4050 
L 3650 4050 
Q 3625 3525 3625 3200 
L 3625 2700 
L 4925 2700 
Q 5425 2700 5875 2725 
L 5875 2225 
Q 5200 2250 4875 2250 
L 3625 2250 
L 3625 25 
Q 3625 -400 3300 -500 
Q 2975 -600 2375 -650 
Q 2300 -300 2175 -75 
Q 2650 -100 2875 -62 
Q 3100 -25 3100 175 
L 3100 2250 
L 1850 2250 
Q 1125 2225 925 2100 
Q 875 2275 675 2525 
Q 875 2675 962 2912 
Q 1050 3150 1175 4625 
Q 2375 4625 3487 4787 
Q 4600 4950 5100 5100 
Q 5275 4700 5425 4550 
Q 4600 4450 3562 4350 
Q 2525 4250 1675 4200 
Q 1550 3150 1475 2700 
L 3100 2700 
z
M 2425 1500 
Q 2225 1325 1725 787 
Q 1225 250 900 -75 
Q 775 75 500 300 
Q 875 600 1237 987 
Q 1600 1375 1950 1900 
Q 2225 1625 2425 1500 
z
M 4525 1750 
Q 4900 1425 5250 1087 
Q 5600 750 5975 375 
Q 5625 125 5500 -25 
Q 5150 425 4175 1375 
Q 4325 1500 4525 1750 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-89c2" d="M 2600 4750 
Q 2500 3900 2387 3325 
Q 2275 2750 2075 2150 
Q 2425 1625 2800 1000 
Q 2575 825 2350 625 
Q 2100 1200 1800 1675 
Q 1600 1275 1300 912 
Q 1000 550 650 175 
Q 500 375 200 625 
Q 675 950 975 1300 
Q 1275 1650 1500 2100 
Q 1075 2725 625 3200 
Q 850 3375 1025 3525 
Q 1375 3150 1725 2600 
Q 1975 3400 2075 4350 
L 1300 4350 
Q 850 4350 475 4325 
L 475 4775 
Q 825 4750 1275 4750 
L 2600 4750 
z
M 4775 225 
Q 4775 0 4887 -25 
Q 5000 -50 5225 -50 
Q 5475 -50 5537 37 
Q 5600 125 5650 675 
Q 5875 525 6175 450 
Q 6025 -300 5850 -400 
Q 5675 -500 5400 -500 
L 4825 -500 
Q 4525 -500 4425 -325 
Q 4325 -150 4325 50 
L 4325 1600 
Q 4200 1125 3900 687 
Q 3600 250 3187 -87 
Q 2775 -425 2325 -650 
Q 2225 -400 1925 -150 
Q 2625 25 3087 475 
Q 3550 925 3737 1425 
Q 3925 1925 3962 2562 
Q 4000 3200 3975 3875 
Q 4325 3825 4550 3800 
Q 4450 3400 4425 2750 
Q 4400 2100 4325 1700 
L 4825 1700 
Q 4800 1550 4775 1025 
L 4775 225 
z
M 5700 5050 
Q 5675 4650 5675 3350 
Q 5675 2050 5700 1575 
L 5200 1575 
L 5200 4650 
L 3450 4650 
L 3450 1550 
L 2950 1550 
Q 2975 2150 2975 3300 
Q 2975 4475 2950 5050 
L 5700 5050 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-4e50"/>
       <use xlink:href="#SimHei-89c2" x="100"/>
       <use xlink:href="#SimHei-6027" x="200"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 621.650273 393.53875 
L 621.650273 25.16625 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m9849ce4537" x="621.650273" y="393.53875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 力量性 -->
      <g transform="translate(609.650273 406.8825) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-529b" d="M 3275 3275 
Q 3200 2400 3050 1875 
Q 2900 1350 2637 912 
Q 2375 475 1950 87 
Q 1525 -300 925 -675 
Q 800 -475 425 -250 
Q 1025 50 1425 375 
Q 1825 700 2112 1112 
Q 2400 1525 2537 2050 
Q 2675 2575 2725 3275 
L 2000 3275 
Q 1275 3275 675 3250 
L 675 3750 
Q 1275 3725 2000 3725 
L 2750 3725 
L 2750 4475 
Q 2750 4825 2725 5200 
Q 3000 5150 3350 5125 
Q 3300 4900 3287 4475 
Q 3275 4050 3275 3725 
L 5825 3725 
Q 5725 2375 5700 1862 
Q 5675 1350 5600 612 
Q 5525 -125 5187 -300 
Q 4850 -475 4025 -550 
Q 4000 -250 3775 100 
Q 4500 75 4750 175 
Q 5000 275 5037 550 
Q 5075 825 5112 1262 
Q 5150 1700 5225 3275 
L 3275 3275 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-91cf" d="M 5250 2500 
Q 5225 2200 5225 1700 
Q 5225 1225 5250 950 
L 3425 950 
L 3425 625 
L 4575 625 
Q 5000 625 5375 650 
L 5375 275 
Q 5025 300 4575 300 
L 3425 300 
L 3425 -75 
L 5125 -75 
Q 5525 -75 5925 -50 
L 5925 -425 
Q 5525 -400 5125 -400 
L 1275 -400 
Q 900 -400 475 -425 
L 475 -50 
Q 925 -50 1275 -75 
L 2975 -75 
L 2975 300 
L 1725 300 
Q 1350 300 975 250 
L 975 650 
Q 1375 625 1725 625 
L 2975 625 
L 2975 950 
L 1150 950 
Q 1175 1375 1175 1775 
Q 1175 2175 1150 2500 
L 5250 2500 
z
M 5200 4950 
Q 5175 4575 5175 4250 
Q 5175 3925 5200 3400 
L 1200 3400 
Q 1225 3925 1225 4250 
Q 1225 4575 1200 4950 
L 5200 4950 
z
M 4900 3100 
Q 5500 3125 6000 3150 
L 6000 2750 
Q 5500 2775 4925 2775 
L 1650 2775 
Q 975 2775 325 2750 
L 325 3150 
Q 975 3125 1650 3100 
L 4900 3100 
z
M 4725 3700 
L 4725 4025 
L 1675 4025 
L 1675 3700 
L 4725 3700 
z
M 4725 4350 
L 4725 4650 
L 1675 4650 
L 1675 4350 
L 4725 4350 
z
M 4750 1875 
L 4750 2175 
L 3425 2175 
L 3425 1875 
L 4750 1875 
z
M 2975 1875 
L 2975 2175 
L 1650 2175 
L 1650 1875 
L 2975 1875 
z
M 2975 1275 
L 2975 1550 
L 1650 1550 
L 1650 1275 
L 2975 1275 
z
M 4750 1275 
L 4750 1550 
L 3425 1550 
L 3425 1275 
L 4750 1275 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-529b"/>
       <use xlink:href="#SimHei-91cf" x="100"/>
       <use xlink:href="#SimHei-6027" x="200"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- 心理韧性维度 -->
     <g transform="translate(346.18375 419.890312) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-5fc3" d="M 2700 3425 
Q 2675 3125 2675 2525 
L 2675 375 
Q 2675 75 2975 75 
L 4150 75 
Q 4425 50 4512 212 
Q 4600 375 4650 950 
Q 4900 750 5225 675 
Q 5050 -125 4875 -287 
Q 4700 -450 4450 -425 
L 2625 -425 
Q 2175 -425 2125 125 
L 2125 2500 
Q 2125 3000 2100 3425 
L 2700 3425 
z
M 1475 3075 
Q 1400 2925 1275 2287 
Q 1150 1650 925 750 
Q 625 850 325 900 
Q 675 1900 850 3200 
Q 1075 3150 1475 3075 
z
M 5075 3300 
Q 5650 2500 6175 1700 
Q 5900 1575 5650 1350 
Q 5125 2350 4625 3000 
Q 4850 3125 5075 3300 
z
M 3050 5250 
Q 3325 4775 3925 3925 
Q 3625 3775 3375 3600 
Q 2975 4425 2600 4950 
Q 2825 5075 3050 5250 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-7406" d="M 5825 4850 
Q 5800 4525 5800 3425 
Q 5800 2350 5825 1875 
L 4450 1875 
L 4450 1125 
L 5200 1125 
Q 5650 1125 5900 1150 
L 5900 700 
Q 5625 725 5200 725 
L 4450 725 
L 4450 -125 
L 5350 -125 
Q 5825 -125 6150 -100 
L 6150 -550 
Q 5825 -525 5375 -525 
L 2950 -525 
Q 2550 -525 2250 -550 
L 2250 -100 
Q 2575 -125 2950 -125 
L 3975 -125 
L 3975 725 
L 3150 725 
Q 2825 725 2575 700 
L 2575 1150 
Q 2825 1125 3150 1125 
L 3975 1125 
L 3975 1875 
L 2625 1875 
Q 2650 2250 2650 3400 
Q 2650 4550 2625 4850 
L 5825 4850 
z
M 1750 4525 
Q 2025 4525 2350 4550 
L 2350 4075 
Q 2000 4100 1750 4100 
L 1525 4100 
L 1525 2750 
Q 1875 2750 2225 2775 
L 2225 2300 
Q 1875 2325 1525 2325 
L 1525 725 
Q 2000 900 2325 1050 
Q 2375 800 2425 625 
Q 1950 475 1362 237 
Q 775 0 450 -200 
Q 350 50 225 325 
Q 550 375 1075 600 
L 1075 2325 
Q 625 2325 300 2300 
L 300 2775 
Q 650 2750 1075 2750 
L 1075 4100 
L 775 4100 
Q 550 4100 225 4075 
L 225 4550 
Q 550 4525 775 4525 
L 1750 4525 
z
M 5325 2275 
L 5325 3175 
L 4450 3175 
L 4450 2275 
L 5325 2275 
z
M 5325 3550 
L 5325 4450 
L 4450 4450 
L 4450 3550 
L 5325 3550 
z
M 3975 2275 
L 3975 3175 
L 3125 3175 
L 3125 2275 
L 3975 2275 
z
M 3975 3550 
L 3975 4450 
L 3125 4450 
L 3125 3550 
L 3975 3550 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-7ef4" d="M 3700 5075 
Q 3575 4900 3475 4637 
Q 3375 4375 3300 4150 
L 4900 4150 
Q 5400 4150 5800 4175 
L 5800 3725 
Q 5375 3750 4900 3750 
L 4650 3750 
L 4650 2900 
Q 5250 2900 5625 2925 
L 5625 2475 
Q 5250 2500 4650 2500 
L 4650 1600 
Q 5275 1600 5650 1625 
L 5650 1200 
Q 5275 1225 4650 1225 
L 4650 325 
L 5050 325 
Q 5550 325 6050 350 
L 6050 -75 
Q 5550 -50 5050 -50 
L 3250 -50 
L 3250 -625 
L 2775 -625 
Q 2800 -150 2800 650 
L 2800 3075 
Q 2650 2700 2475 2375 
Q 2225 2550 2050 2650 
Q 2275 2975 2450 3312 
Q 2625 3650 2837 4250 
Q 3050 4850 3100 5250 
Q 3375 5150 3700 5075 
z
M 1750 5000 
Q 1525 4775 1300 4225 
Q 1075 3675 825 3150 
Q 1175 3200 1525 3225 
Q 1775 3775 1825 4100 
L 2375 3900 
Q 2225 3725 1950 3200 
Q 1675 2675 1125 1575 
L 2250 1725 
Q 2150 1425 2125 1250 
Q 1800 1250 1387 1187 
Q 975 1125 575 1000 
Q 475 1300 425 1525 
Q 650 1625 875 1987 
Q 1100 2350 1325 2800 
Q 1050 2775 812 2737 
Q 575 2700 400 2625 
Q 325 2825 200 3125 
Q 425 3275 687 3875 
Q 950 4475 1150 5175 
Q 1425 5075 1750 5000 
z
M 2475 650 
Q 2450 400 2450 200 
Q 2000 125 1450 12 
Q 900 -100 450 -275 
Q 350 0 250 300 
Q 750 350 1187 412 
Q 1625 475 2475 650 
z
M 4200 1600 
L 4200 2500 
L 3250 2500 
L 3250 1600 
L 4200 1600 
z
M 4200 325 
L 4200 1225 
L 3250 1225 
L 3250 325 
L 4200 325 
z
M 4200 2900 
L 4200 3750 
L 3250 3750 
L 3250 2900 
L 4200 2900 
z
M 4500 5225 
Q 4600 5075 4900 4475 
Q 4700 4400 4475 4275 
Q 4350 4575 4100 4975 
L 4500 5225 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5ea6" d="M 1750 1675 
Q 2450 1650 2875 1650 
L 5200 1650 
Q 4975 1250 4650 850 
Q 4325 450 4025 200 
Q 4350 50 4950 -50 
Q 5550 -150 6100 -150 
Q 5950 -300 5825 -650 
Q 5100 -550 4550 -412 
Q 4000 -275 3625 -100 
Q 3100 -325 2587 -462 
Q 2075 -600 1525 -675 
Q 1450 -475 1225 -250 
Q 1675 -250 2250 -125 
Q 2825 0 3250 200 
Q 2825 550 2400 1275 
L 1750 1250 
L 1750 1675 
z
M 2975 5075 
Q 3250 5150 3475 5250 
Q 3625 4975 3775 4525 
L 4700 4525 
Q 4975 4525 5700 4550 
L 5700 4100 
Q 5000 4125 4650 4125 
L 1375 4125 
Q 1375 3000 1350 2312 
Q 1325 1625 1212 950 
Q 1100 275 725 -525 
Q 500 -375 250 -300 
Q 650 400 762 1075 
Q 875 1750 887 2800 
Q 900 3850 875 4525 
L 3200 4525 
Q 3100 4850 2975 5075 
z
M 2475 3300 
Q 2475 3700 2450 3925 
L 2975 3925 
Q 2950 3725 2950 3300 
L 4300 3300 
Q 4300 3700 4275 3950 
L 4800 3950 
Q 4775 3650 4775 3300 
Q 5325 3300 5725 3325 
L 5725 2900 
Q 5350 2925 4775 2925 
Q 4775 2250 4800 1950 
L 2450 1950 
Q 2475 2350 2475 2925 
Q 2175 2925 1600 2900 
L 1600 3325 
Q 2175 3300 2475 3300 
z
M 4300 2350 
L 4300 2925 
L 2950 2925 
L 2950 2350 
L 4300 2350 
z
M 2875 1275 
Q 3200 775 3650 425 
Q 4025 700 4425 1275 
L 2900 1275 
L 2875 1275 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-5fc3"/>
      <use xlink:href="#SimHei-7406" x="100"/>
      <use xlink:href="#SimHei-97e7" x="200"/>
      <use xlink:href="#SimHei-6027" x="300"/>
      <use xlink:href="#SimHei-7ef4" x="400"/>
      <use xlink:href="#SimHei-5ea6" x="500"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_9">
      <path d="M 43.1675 393.53875 
L 709.2 393.53875 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <defs>
       <path id="m3cc6afd1cb" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="393.53875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.0 -->
      <g transform="translate(24.1675 396.28875) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-2e" d="M 1075 125 
L 500 125 
L 500 675 
L 1075 675 
L 1075 125 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <path d="M 43.1675 349.277337 
L 709.2 349.277337 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="349.277337" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 0.5 -->
      <g transform="translate(24.1675 352.027337) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-35" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_13">
      <path d="M 43.1675 305.015925 
L 709.2 305.015925 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="305.015925" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 1.0 -->
      <g transform="translate(24.1675 307.765925) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_15">
      <path d="M 43.1675 260.754512 
L 709.2 260.754512 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="260.754512" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 1.5 -->
      <g transform="translate(24.1675 263.504512) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-35" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_17">
      <path d="M 43.1675 216.493099 
L 709.2 216.493099 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="216.493099" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 2.0 -->
      <g transform="translate(24.1675 219.243099) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-32"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_19">
      <path d="M 43.1675 172.231686 
L 709.2 172.231686 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="172.231686" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 2.5 -->
      <g transform="translate(24.1675 174.981686) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-32"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-35" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_21">
      <path d="M 43.1675 127.970274 
L 709.2 127.970274 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="127.970274" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 3.0 -->
      <g transform="translate(24.1675 130.720274) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-33"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_23">
      <path d="M 43.1675 83.708861 
L 709.2 83.708861 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="83.708861" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 3.5 -->
      <g transform="translate(24.1675 86.458861) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-33"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-35" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_25">
      <path d="M 43.1675 39.447448 
L 709.2 39.447448 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m3cc6afd1cb" x="43.1675" y="39.447448" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 4.0 -->
      <g transform="translate(24.1675 42.197448) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-34"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="text_15">
     <!-- 分数 -->
     <g transform="translate(18.9175 219.3525) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-5206" d="M 1000 -650 
Q 825 -375 575 -225 
Q 975 -75 1300 137 
Q 1625 350 1875 662 
Q 2125 975 2250 1350 
Q 2375 1725 2425 2275 
Q 1850 2275 1500 2250 
L 1500 2775 
Q 1900 2750 2525 2750 
L 5025 2750 
Q 4975 2375 4950 2100 
L 4750 250 
Q 4700 -200 4350 -350 
Q 4000 -500 3575 -525 
Q 3550 -275 3325 50 
Q 3850 25 4037 87 
Q 4225 150 4275 525 
L 4450 2275 
L 2950 2275 
Q 2875 1625 2737 1187 
Q 2600 750 2350 425 
Q 2100 100 1750 -175 
Q 1400 -450 1000 -650 
z
M 3950 5175 
Q 4125 4650 4662 3975 
Q 5200 3300 6175 2800 
Q 5975 2625 5775 2275 
Q 5325 2600 4987 2887 
Q 4650 3175 4425 3425 
Q 4200 3675 3925 4137 
Q 3650 4600 3475 5000 
Q 3750 5075 3950 5175 
z
M 2700 4825 
Q 2475 4425 2325 4125 
Q 2175 3825 2025 3587 
Q 1875 3350 1575 2950 
Q 1275 2550 850 2125 
Q 625 2350 400 2475 
Q 900 2925 1187 3300 
Q 1475 3675 1737 4112 
Q 2000 4550 2200 5075 
Q 2400 4925 2700 4825 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6570" d="M 4525 4925 
Q 4475 4800 4400 4587 
Q 4325 4375 4175 3825 
L 5325 3825 
Q 5575 3825 5925 3850 
L 5925 3400 
Q 5650 3425 5500 3425 
Q 5500 3025 5362 2162 
Q 5225 1300 4875 675 
Q 5125 375 5437 137 
Q 5750 -100 6025 -200 
Q 5725 -425 5625 -625 
Q 5325 -450 5075 -225 
Q 4825 0 4575 325 
Q 4300 25 3987 -187 
Q 3675 -400 3200 -650 
Q 3075 -425 2850 -300 
Q 3225 -150 3650 125 
Q 4075 400 4300 675 
Q 4125 1025 3962 1425 
Q 3800 1825 3675 2475 
Q 3600 2275 3425 1950 
Q 3250 2050 3000 2150 
Q 3400 2850 3650 3675 
Q 3900 4500 3975 5100 
Q 4275 4975 4525 4925 
z
M 2100 2250 
Q 2000 2150 1875 1925 
L 3250 1925 
Q 3100 1300 2725 675 
Q 3125 525 3350 400 
Q 3225 225 3125 25 
Q 2900 175 2450 325 
Q 1950 -225 725 -650 
Q 600 -375 400 -275 
Q 1575 0 2000 475 
Q 1300 650 850 775 
Q 1000 1000 1250 1525 
Q 1000 1525 425 1500 
L 425 1950 
Q 900 1925 1400 1925 
Q 1500 2150 1550 2425 
Q 1825 2325 2100 2250 
z
M 1775 3950 
Q 1775 4550 1750 5100 
L 2250 5100 
Q 2225 4575 2225 3950 
Q 3075 3950 3425 3975 
L 3425 3525 
Q 3075 3550 2225 3550 
Q 2225 2825 2250 2425 
L 1750 2425 
Q 1775 2775 1775 3300 
Q 1650 3100 1300 2787 
Q 950 2475 650 2300 
Q 525 2525 275 2625 
Q 500 2700 875 2975 
Q 1250 3250 1450 3550 
Q 950 3550 500 3525 
L 500 3975 
Q 925 3950 1775 3950 
z
M 3975 3175 
Q 4200 1700 4600 1125 
Q 4850 1700 4937 2287 
Q 5025 2875 5050 3425 
L 4075 3425 
L 3975 3175 
z
M 1450 1000 
Q 1750 925 2275 800 
Q 2475 1050 2650 1525 
L 1725 1525 
Q 1600 1275 1450 1000 
z
M 3325 4825 
Q 3200 4625 3125 4450 
Q 3050 4275 2925 4025 
Q 2725 4125 2525 4175 
Q 2725 4500 2900 5000 
Q 3150 4875 3325 4825 
z
M 2575 3375 
Q 2900 3050 3200 2725 
Q 3050 2600 2875 2425 
Q 2525 2850 2300 3100 
Q 2450 3225 2575 3375 
z
M 925 5000 
Q 1275 4650 1500 4300 
L 1125 4075 
Q 950 4425 600 4725 
Q 825 4875 925 5000 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-5206"/>
      <use xlink:href="#SimHei-6570" x="100"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 73.441705 393.53875 
L 130.717227 393.53875 
L 130.717227 131.094609 
L 73.441705 131.094609 
z
" clip-path="url(#peeac81ccad)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_4">
    <path d="M 237.086053 393.53875 
L 294.361576 393.53875 
L 294.361576 144.373033 
L 237.086053 144.373033 
z
" clip-path="url(#peeac81ccad)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_5">
    <path d="M 400.730402 393.53875 
L 458.005924 393.53875 
L 458.005924 134.479305 
L 400.730402 134.479305 
z
" clip-path="url(#peeac81ccad)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_6">
    <path d="M 564.374751 393.53875 
L 621.650273 393.53875 
L 621.650273 107.610024 
L 564.374751 107.610024 
z
" clip-path="url(#peeac81ccad)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_7">
    <path d="M 130.717227 393.53875 
L 187.992749 393.53875 
L 187.992749 68.718996 
L 130.717227 68.718996 
z
" clip-path="url(#peeac81ccad)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_8">
    <path d="M 294.361576 393.53875 
L 351.637098 393.53875 
L 351.637098 70.725513 
L 294.361576 70.725513 
z
" clip-path="url(#peeac81ccad)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_9">
    <path d="M 458.005924 393.53875 
L 515.281447 393.53875 
L 515.281447 91.085763 
L 458.005924 91.085763 
z
" clip-path="url(#peeac81ccad)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_10">
    <path d="M 621.650273 393.53875 
L 678.925795 393.53875 
L 678.925795 51.014431 
L 621.650273 51.014431 
z
" clip-path="url(#peeac81ccad)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="LineCollection_1">
    <path d="M 102.079466 156.187599 
L 102.079466 106.001619 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 265.723814 168.301195 
L 265.723814 120.44487 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 429.368163 161.23372 
L 429.368163 107.72489 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 593.012512 135.15155 
L 593.012512 80.068498 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_27">
    <defs>
     <path id="m30cb2d0d76" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#peeac81ccad)">
     <use xlink:href="#m30cb2d0d76" x="102.079466" y="156.187599" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="265.723814" y="168.301195" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="429.368163" y="161.23372" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="593.012512" y="135.15155" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_28">
    <g clip-path="url(#peeac81ccad)">
     <use xlink:href="#m30cb2d0d76" x="102.079466" y="106.001619" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="265.723814" y="120.44487" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="429.368163" y="107.72489" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="593.012512" y="80.068498" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 159.354988 77.799124 
L 159.354988 59.638868 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 322.999337 82.04143 
L 322.999337 59.409597 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 486.643686 99.069973 
L 486.643686 83.101554 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 650.288034 59.321064 
L 650.288034 42.707798 
" clip-path="url(#peeac81ccad)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_29">
    <g clip-path="url(#peeac81ccad)">
     <use xlink:href="#m30cb2d0d76" x="159.354988" y="77.799124" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="322.999337" y="82.04143" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="486.643686" y="99.069973" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="650.288034" y="59.321064" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_30">
    <g clip-path="url(#peeac81ccad)">
     <use xlink:href="#m30cb2d0d76" x="159.354988" y="59.638868" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="322.999337" y="59.409597" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="486.643686" y="83.101554" style="stroke: #000000"/>
     <use xlink:href="#m30cb2d0d76" x="650.288034" y="42.707798" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_11">
    <path d="M 43.1675 393.53875 
L 43.1675 25.16625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 709.2 393.53875 
L 709.2 25.16625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 43.1675 393.53875 
L 709.2 393.53875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 43.1675 25.16625 
L 709.2 25.16625 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_16">
    <!-- p=0.034 -->
    <g transform="translate(116.717227 40.934303) scale(0.08 -0.08)">
     <defs>
      <path id="SimHei-70" d="M 2825 1525 
Q 2825 800 2475 425 
Q 2125 50 1625 50 
Q 1350 50 1150 162 
Q 950 275 800 500 
L 800 -800 
L 300 -800 
L 300 2925 
L 800 2925 
L 800 2550 
Q 950 2775 1150 2875 
Q 1350 2975 1625 2975 
Q 2125 2975 2475 2612 
Q 2825 2250 2825 1525 
z
M 2275 1525 
Q 2275 2000 2087 2262 
Q 1900 2525 1525 2525 
Q 1225 2525 1012 2262 
Q 800 2000 800 1525 
Q 800 1050 1012 775 
Q 1225 500 1525 500 
Q 1900 500 2087 775 
Q 2275 1050 2275 1525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-3d" d="M 2975 2900 
L 125 2900 
L 125 3300 
L 2975 3300 
L 2975 2900 
z
M 2975 1375 
L 125 1375 
L 125 1775 
L 2975 1775 
L 2975 1375 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-70"/>
     <use xlink:href="#SimHei-3d" x="50"/>
     <use xlink:href="#SimHei-30" x="100"/>
     <use xlink:href="#SimHei-2e" x="150"/>
     <use xlink:href="#SimHei-30" x="200"/>
     <use xlink:href="#SimHei-33" x="250"/>
     <use xlink:href="#SimHei-34" x="300"/>
    </g>
   </g>
   <g id="text_17">
    <!-- p=0.012 -->
    <g transform="translate(280.361576 40.705032) scale(0.08 -0.08)">
     <use xlink:href="#SimHei-70"/>
     <use xlink:href="#SimHei-3d" x="50"/>
     <use xlink:href="#SimHei-30" x="100"/>
     <use xlink:href="#SimHei-2e" x="150"/>
     <use xlink:href="#SimHei-30" x="200"/>
     <use xlink:href="#SimHei-31" x="250"/>
     <use xlink:href="#SimHei-32" x="300"/>
    </g>
   </g>
   <g id="text_18">
    <!-- 高低焦虑组心理韧性比较 -->
    <g transform="translate(321.18375 19.16625) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-9ad8" d="M 5825 -25 
Q 5850 -400 5550 -500 
Q 5250 -600 4975 -625 
Q 4875 -325 4725 -100 
Q 4975 -100 5175 -87 
Q 5375 -75 5350 250 
L 5350 1825 
L 1100 1825 
L 1100 -575 
L 600 -575 
Q 625 -225 625 850 
Q 625 1950 600 2200 
L 5850 2200 
Q 5825 1850 5825 1425 
L 5825 -25 
z
M 4925 3800 
Q 4900 3475 4900 3175 
Q 4900 2900 4925 2600 
L 1400 2600 
Q 1425 2825 1425 3175 
Q 1425 3525 1400 3800 
L 4925 3800 
z
M 4500 1400 
Q 4475 1125 4475 800 
Q 4475 500 4500 200 
L 1950 200 
Q 2000 500 2000 800 
Q 2000 1125 1950 1400 
L 4500 1400 
z
M 425 4600 
Q 750 4575 1125 4575 
L 2925 4575 
Q 2850 4875 2750 5100 
Q 3050 5125 3350 5200 
Q 3400 4950 3525 4575 
L 5250 4575 
Q 5600 4575 5975 4600 
L 5975 4150 
Q 5600 4175 5050 4175 
L 1150 4175 
Q 750 4175 425 4150 
L 425 4600 
z
M 4450 2975 
L 4450 3425 
L 1900 3425 
L 1900 2975 
L 4450 2975 
z
M 4025 550 
L 4025 1050 
L 2400 1050 
L 2400 550 
L 4025 550 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4f4e" d="M 2300 4550 
Q 2750 4525 3750 4637 
Q 4750 4750 5275 5000 
Q 5425 4725 5600 4475 
Q 5125 4400 4350 4300 
Q 4350 3825 4400 2850 
L 5000 2850 
Q 5425 2850 5850 2875 
L 5850 2425 
Q 5425 2450 5025 2450 
L 4450 2450 
Q 4575 1325 4862 762 
Q 5150 200 5325 75 
Q 5500 -50 5562 125 
Q 5625 300 5675 775 
Q 5925 575 6150 525 
Q 6000 -100 5850 -337 
Q 5700 -575 5350 -500 
Q 5000 -425 4587 262 
Q 4175 950 4000 2450 
L 2775 2450 
L 2775 500 
Q 3275 950 3575 1275 
Q 3675 975 3775 800 
Q 2775 -50 2500 -350 
Q 2325 -175 2175 25 
Q 2300 150 2325 500 
L 2325 3725 
Q 2325 4075 2300 4550 
z
M 2050 5075 
Q 1900 4900 1600 3950 
L 1600 425 
Q 1600 -75 1625 -625 
L 1100 -625 
Q 1125 -75 1125 450 
L 1125 3025 
Q 875 2500 625 2125 
Q 400 2350 225 2425 
Q 600 2900 962 3687 
Q 1325 4475 1500 5275 
Q 1575 5200 2050 5075 
z
M 3950 2850 
Q 3900 3775 3900 4225 
Q 3125 4175 2775 4150 
L 2775 2850 
L 3950 2850 
z
M 3850 -625 
Q 3450 -75 3250 100 
Q 3450 275 3575 425 
Q 3925 50 4225 -300 
Q 4025 -450 3850 -625 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7126" d="M 2375 4900 
Q 2125 4625 1975 4300 
L 5075 4300 
Q 5400 4300 5900 4325 
L 5900 3850 
Q 5400 3875 5075 3875 
L 3875 3875 
L 3875 3300 
L 4700 3300 
Q 5200 3300 5600 3325 
L 5600 2875 
Q 5200 2900 4700 2900 
L 3875 2900 
L 3875 2325 
L 4750 2325 
Q 5150 2325 5600 2350 
L 5600 1900 
Q 5150 1925 4750 1925 
L 3875 1925 
L 3875 1400 
L 5025 1400 
Q 5500 1400 5950 1425 
L 5950 950 
Q 5500 1000 5050 1000 
L 1750 1000 
L 1750 675 
L 1200 675 
Q 1225 1250 1225 1700 
L 1225 3075 
Q 925 2625 600 2275 
Q 400 2525 200 2675 
Q 800 3225 1225 3950 
Q 1650 4675 1800 5200 
Q 2100 5025 2375 4900 
z
M 3350 2325 
L 3350 2900 
L 1750 2900 
L 1750 2325 
L 3350 2325 
z
M 3350 3300 
L 3350 3875 
L 1750 3875 
L 1750 3300 
L 3350 3300 
z
M 3350 1400 
L 3350 1925 
L 1750 1925 
L 1750 1400 
L 3350 1400 
z
M 350 -500 
Q 775 -25 975 525 
Q 1225 450 1500 375 
Q 1250 -25 925 -700 
Q 700 -575 350 -500 
z
M 3000 -525 
Q 2675 -575 2450 -650 
Q 2375 -175 2225 375 
Q 2450 425 2775 525 
L 3000 -525 
z
M 5675 -575 
Q 5500 -225 5125 425 
Q 5350 525 5550 700 
Q 5850 150 6175 -250 
Q 5850 -400 5675 -575 
z
M 4175 600 
Q 4425 -100 4525 -425 
Q 4225 -500 4000 -575 
Q 3900 -150 3650 450 
Q 3950 525 4175 600 
z
M 3600 5275 
Q 3800 5025 4175 4625 
Q 3925 4525 3700 4350 
Q 3500 4650 3200 5025 
Q 3400 5125 3600 5275 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8651" d="M 3225 5275 
Q 3200 5000 3200 4825 
L 4550 4825 
Q 4900 4825 5325 4850 
L 5325 4425 
Q 4900 4450 4550 4450 
L 3200 4450 
L 3200 3950 
L 5975 3950 
Q 5825 3550 5700 3000 
Q 5450 3075 5175 3100 
Q 5250 3350 5325 3575 
L 3300 3575 
L 3300 3050 
Q 4175 3150 4725 3225 
L 4825 2850 
Q 4350 2800 3300 2650 
L 3300 2400 
Q 3325 2150 3650 2150 
L 4500 2150 
Q 4750 2150 4862 2175 
Q 4975 2200 5075 2675 
Q 5225 2525 5575 2400 
Q 5400 1975 5237 1850 
Q 5075 1725 4675 1750 
L 3475 1750 
Q 2850 1750 2850 2225 
L 2850 2600 
Q 1850 2450 1550 2400 
L 1525 2850 
Q 1850 2850 2850 2975 
L 2850 3575 
L 1350 3575 
Q 1350 1775 1300 1287 
Q 1250 800 1112 362 
Q 975 -75 725 -600 
Q 575 -475 250 -325 
Q 475 50 650 450 
Q 825 850 862 1287 
Q 900 1725 900 2625 
Q 900 3525 875 3950 
L 2750 3950 
L 2750 4475 
Q 2750 4825 2725 5275 
L 3225 5275 
z
M 3050 1125 
Q 3025 750 3025 500 
L 3025 150 
Q 3025 -150 3350 -150 
L 4050 -150 
Q 4350 -150 4475 -87 
Q 4600 -25 4650 400 
Q 4875 175 5150 125 
Q 4975 -300 4862 -425 
Q 4750 -550 4300 -550 
L 3175 -550 
Q 2575 -550 2575 -100 
L 2575 300 
Q 2575 750 2550 1125 
L 3050 1125 
z
M 2350 1000 
Q 1900 50 1700 -325 
Q 1500 -225 1250 -75 
Q 1475 225 1875 1225 
Q 2150 1075 2350 1000 
z
M 5225 1350 
Q 5500 1050 6125 200 
Q 5900 50 5725 -75 
Q 5425 350 4875 1050 
Q 4975 1125 5225 1350 
z
M 3700 1575 
Q 4200 925 4350 675 
Q 4075 525 3925 400 
Q 3800 650 3325 1325 
Q 3500 1425 3700 1575 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7ec4" d="M 6125 -425 
Q 5750 -400 5225 -400 
L 3275 -400 
Q 2775 -400 2400 -425 
L 2400 50 
Q 2725 25 2950 25 
L 2950 3775 
Q 2950 4375 2900 4625 
L 5525 4625 
Q 5500 4400 5500 3775 
L 5500 25 
Q 5825 25 6125 50 
L 6125 -425 
z
M 1725 4825 
Q 1500 4550 1287 4137 
Q 1075 3725 725 3050 
Q 950 3050 1550 3075 
Q 1800 3550 1900 3975 
Q 2075 3825 2450 3675 
Q 2225 3350 1937 2875 
Q 1650 2400 1150 1625 
Q 1950 1725 2425 1775 
L 2425 1350 
Q 2100 1325 1575 1250 
Q 1050 1175 575 1050 
Q 550 1300 400 1575 
Q 675 1675 912 1975 
Q 1150 2275 1375 2725 
Q 1225 2725 950 2687 
Q 675 2650 425 2525 
Q 325 2825 225 3050 
Q 450 3300 712 3862 
Q 975 4425 1150 5075 
Q 1425 4925 1725 4825 
z
M 5025 25 
L 5025 1275 
L 3425 1275 
L 3425 25 
L 5025 25 
z
M 5025 3125 
L 5025 4225 
L 3425 4225 
L 3425 3125 
L 5025 3125 
z
M 5025 1700 
L 5025 2725 
L 3425 2725 
L 3425 1700 
L 5025 1700 
z
M 2375 175 
Q 1875 100 1375 -12 
Q 875 -125 450 -300 
Q 375 -75 250 250 
Q 925 325 1162 375 
Q 1400 425 2375 600 
L 2375 175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6bd4" d="M 3775 5100 
Q 3750 4575 3750 4100 
L 3750 3050 
Q 4925 3600 5475 4175 
Q 5675 3900 5900 3675 
Q 5600 3500 5037 3187 
Q 4475 2875 3750 2550 
L 3750 450 
Q 3750 100 4050 100 
L 5000 100 
Q 5175 100 5262 250 
Q 5350 400 5450 900 
Q 5650 775 6025 675 
Q 5850 0 5650 -175 
Q 5450 -350 5225 -350 
L 3900 -350 
Q 3250 -350 3250 275 
L 3250 4000 
Q 3250 4550 3225 5100 
L 3775 5100 
z
M 1275 4975 
Q 1250 4575 1250 4000 
L 1250 3150 
L 1850 3150 
Q 2325 3150 2875 3175 
L 2875 2650 
Q 2325 2675 1850 2675 
L 1250 2675 
L 1250 475 
Q 1650 650 1962 812 
Q 2275 975 2850 1275 
Q 2900 950 2950 750 
Q 2375 475 1762 175 
Q 1150 -125 925 -325 
Q 750 -100 575 200 
Q 700 300 750 600 
L 750 3950 
Q 750 4550 725 4975 
L 1275 4975 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8f83" d="M 2950 1425 
Q 2925 1225 2925 1000 
Q 2725 975 2025 850 
Q 2025 225 2050 -625 
L 1525 -625 
Q 1550 225 1550 750 
Q 875 600 425 450 
Q 350 750 200 1000 
Q 625 1050 1550 1175 
L 1550 2100 
Q 1250 2100 937 2087 
Q 625 2075 425 2000 
L 275 2450 
Q 500 2675 662 3075 
Q 825 3475 950 3950 
Q 775 3950 350 3925 
L 350 4400 
Q 725 4350 1050 4350 
Q 1225 4800 1275 5250 
Q 1550 5150 1850 5075 
Q 1725 4775 1575 4350 
Q 2200 4350 2650 4375 
L 2650 3925 
Q 2200 3950 1450 3950 
Q 1300 3475 900 2500 
L 1550 2500 
Q 1550 3100 1525 3400 
L 2050 3400 
Q 2025 3050 2025 2500 
L 2675 2500 
L 2675 2100 
L 2025 2100 
L 2025 1250 
Q 2700 1375 2950 1425 
z
M 5450 2325 
Q 5300 1925 5100 1437 
Q 4900 950 4675 575 
Q 4900 375 5300 225 
Q 5700 75 6200 -25 
Q 6000 -275 5950 -575 
Q 5300 -375 4925 -162 
Q 4550 50 4325 250 
Q 4025 -25 3687 -212 
Q 3350 -400 2825 -625 
Q 2725 -375 2450 -125 
Q 3025 0 3425 212 
Q 3825 425 4025 625 
Q 3800 1000 3650 1425 
L 3350 2275 
Q 3550 2375 3850 2475 
Q 4000 1875 4100 1600 
Q 4200 1325 4350 1025 
Q 4525 1275 4650 1662 
Q 4775 2050 4850 2525 
Q 5125 2425 5450 2325 
z
M 5175 4400 
Q 5725 4425 6025 4425 
L 6025 3975 
Q 5725 4000 5200 4000 
L 3800 4000 
Q 3250 4000 2925 3975 
L 2925 4425 
Q 3350 4400 3800 4400 
L 5175 4400 
z
M 4150 3425 
Q 3875 3100 3175 2275 
Q 3025 2475 2775 2625 
Q 3425 3300 3675 3750 
Q 3875 3600 4150 3425 
z
M 5200 3750 
Q 5450 3500 6125 2750 
Q 5825 2550 5650 2400 
Q 5425 2775 4775 3425 
Q 4975 3600 5200 3750 
z
M 4275 5300 
Q 4625 4925 4775 4675 
Q 4525 4550 4300 4425 
Q 4225 4625 3850 5050 
Q 4075 5175 4275 5300 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-9ad8"/>
     <use xlink:href="#SimHei-4f4e" x="100"/>
     <use xlink:href="#SimHei-7126" x="200"/>
     <use xlink:href="#SimHei-8651" x="300"/>
     <use xlink:href="#SimHei-7ec4" x="400"/>
     <use xlink:href="#SimHei-5fc3" x="500"/>
     <use xlink:href="#SimHei-7406" x="600"/>
     <use xlink:href="#SimHei-97e7" x="700"/>
     <use xlink:href="#SimHei-6027" x="800"/>
     <use xlink:href="#SimHei-6bd4" x="900"/>
     <use xlink:href="#SimHei-8f83" x="1000"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_15">
     <path d="M 48.7675 54.50375 
L 106.3675 54.50375 
Q 107.9675 54.50375 107.9675 52.90375 
L 107.9675 30.76625 
Q 107.9675 29.16625 106.3675 29.16625 
L 48.7675 29.16625 
Q 47.1675 29.16625 47.1675 30.76625 
L 47.1675 52.90375 
Q 47.1675 54.50375 48.7675 54.50375 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_16">
     <path d="M 50.3675 38.835 
L 66.3675 38.835 
L 66.3675 33.235 
L 50.3675 33.235 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_19">
     <!-- 高焦虑组 -->
     <g transform="translate(72.7675 38.835) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-9ad8"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
    <g id="patch_17">
     <path d="M 50.3675 50.30375 
L 66.3675 50.30375 
L 66.3675 44.70375 
L 50.3675 44.70375 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_20">
     <!-- 低焦虑组 -->
     <g transform="translate(72.7675 50.30375) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-4f4e"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="peeac81ccad">
   <rect x="43.1675" y="25.16625" width="666.0325" height="368.3725"/>
  </clipPath>
 </defs>
</svg>

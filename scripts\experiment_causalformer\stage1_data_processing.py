#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 阶段一：基础数据处理与模型框架搭建

功能：
- 加载EEG和ECG数据
- 进行基本的数据预处理（滤波、R波检测、数据分段和标准化）
- 搭建基础CausalFormer模型框架
- 实现简单的单变量预测功能

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
import mne
import h5py
from datetime import datetime
import sys
import json

# 添加CausalFormer源码路径
causalformer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "CausalFormer")
sys.path.append(causalformer_path)

# 导入CausalFormer相关模块
try:
    from CausalFormer.model.model import Embedding, CausalSelfAttention, MultiHeadAttention, PositionwiseFeedForward, EncoderLayer, Encoder, PredictModel
except ImportError:
    print("警告: 无法导入CausalFormer模块，将使用简化模型")

# 定义常量
DATA_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer"
RATINGS_FILE = r"C:\Users\<USER>\Desktop\stress0422.xlsx"

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

def load_subject_data(data_dir=DATA_DIR):
    """
    加载HEP数据

    参数:
    data_dir (str): 数据目录

    返回:
    dict: 包含各阶段HEP数据的字典
    """
    print(f"加载HEP数据...")

    # 查找所有阶段数据文件
    stage_files = [f for f in os.listdir(data_dir) if f.endswith('.h5') and 'raw_epochs' in f]

    if not stage_files:
        print(f"警告: 未找到HEP数据文件")
        return None

    # 存储各阶段数据
    stages_data = {}

    for file_name in stage_files:
        # 提取阶段信息
        if "rest1" in file_name:
            stage = "rest1"
        elif "rest2" in file_name:
            stage = "rest2"
        elif "rest3" in file_name:
            stage = "rest3"
        elif "test1" in file_name:
            stage = "test1"
        elif "test2" in file_name:
            stage = "test2"
        elif "test3" in file_name:
            stage = "test3"
        elif "prac" in file_name:
            stage = "prac"
        else:
            stage = "unknown"

        # 加载数据
        file_path = os.path.join(data_dir, file_name)
        try:
            with h5py.File(file_path, 'r') as f:
                # 获取数据
                data = f['data'][:]  # (n_epochs, n_channels, n_times)
                times = f['times'][:]
                ch_names = [ch.decode('utf-8') for ch in f['ch_names'][:]]
                subject_ids = [subj.decode('utf-8') for subj in f['subject_ids'][:]]

                # 分离EEG和ECG通道
                eeg_indices = [i for i, ch in enumerate(ch_names) if not ch.startswith('ECG')]
                ecg_indices = [i for i, ch in enumerate(ch_names) if ch.startswith('ECG')]

                if not eeg_indices or not ecg_indices:
                    print(f"警告: 文件 {file_name} 缺少EEG或ECG通道")
                    continue

                print(f"  - 找到 {len(eeg_indices)} 个EEG通道, {len(ecg_indices)} 个ECG通道")

                # 提取EEG和ECG数据
                eeg_data = data[:, eeg_indices, :]  # (n_epochs, n_eeg_channels, n_times)
                ecg_data = data[:, ecg_indices, :]  # (n_epochs, n_ecg_channels, n_times)

                # 计算R波位置（假设在时间序列中间）
                r_peaks = [int(data.shape[2] / 2) for _ in range(data.shape[0])]

                # 存储数据
                stages_data[stage] = {
                    'eeg': eeg_data,
                    'ecg': ecg_data,
                    'eeg_channels': [ch_names[i] for i in eeg_indices],
                    'ecg_channels': [ch_names[i] for i in ecg_indices],
                    'times': times,
                    'r_peaks': r_peaks,
                    'subject_ids': subject_ids,
                    'sfreq': 500  # 假设采样率为500Hz
                }

                print(f"  - 成功加载 {stage} 阶段数据: {len(subject_ids)} 个被试, {len(eeg_indices)} EEG通道, {len(ecg_indices)} ECG通道, {data.shape[2]} 个时间点")

        except Exception as e:
            print(f"错误: 加载文件 {file_name} 时出错: {str(e)}")

    return stages_data

def preprocess_data(stages_data, key_channels=None):
    """
    预处理HEP数据

    参数:
    stages_data (dict): 各阶段的原始数据
    key_channels (list): 关键EEG通道列表，如果为None则使用所有通道

    返回:
    dict: 预处理后的数据
    """
    print("预处理数据...")

    if not stages_data:
        print("错误: 没有数据可供预处理")
        return None

    # 获取采样率
    sfreq = next(iter(stages_data.values()))['sfreq']
    print(f"数据采样率: {sfreq} Hz")

    # 预处理后的数据
    processed_data = {}

    for stage, data in stages_data.items():
        print(f"处理 {stage} 阶段数据...")

        # 提取EEG和ECG数据
        eeg_data = data['eeg']  # (n_epochs, n_eeg_channels, n_times)
        ecg_data = data['ecg']  # (n_epochs, n_ecg_channels, n_times)
        eeg_channels = data['eeg_channels']
        r_peaks = data['r_peaks']

        # 选择关键通道
        if key_channels:
            # 找到关键通道的索引
            key_indices = [i for i, ch in enumerate(eeg_channels) if ch in key_channels]
            if not key_indices:
                print(f"警告: 未找到指定的关键通道，使用所有通道")
                key_eeg_data = eeg_data
                key_eeg_channels = eeg_channels
            else:
                key_eeg_data = eeg_data[:, key_indices, :]  # (n_epochs, n_key_channels, n_times)
                key_eeg_channels = [eeg_channels[i] for i in key_indices]
                print(f"  - 选择了 {len(key_eeg_channels)} 个关键通道: {key_eeg_channels}")
        else:
            key_eeg_data = eeg_data
            key_eeg_channels = eeg_channels

        # 数据标准化
        # 对每个epoch分别标准化
        eeg_normalized = np.zeros_like(key_eeg_data)
        ecg_normalized = np.zeros_like(ecg_data)

        for i in range(key_eeg_data.shape[0]):  # 遍历每个epoch
            # 标准化EEG
            for j in range(key_eeg_data.shape[1]):  # 遍历每个通道
                channel_data = key_eeg_data[i, j, :]
                channel_mean = np.mean(channel_data)
                channel_std = np.std(channel_data)
                if channel_std > 0:
                    eeg_normalized[i, j, :] = (channel_data - channel_mean) / channel_std
                else:
                    eeg_normalized[i, j, :] = channel_data

            # 标准化ECG
            for j in range(ecg_data.shape[1]):  # 遍历每个通道
                channel_data = ecg_data[i, j, :]
                channel_mean = np.mean(channel_data)
                channel_std = np.std(channel_data)
                if channel_std > 0:
                    ecg_normalized[i, j, :] = (channel_data - channel_mean) / channel_std
                else:
                    ecg_normalized[i, j, :] = channel_data

        # 存储处理后的数据
        processed_data[stage] = {
            'eeg': eeg_normalized,
            'ecg': ecg_normalized,
            'r_peaks': r_peaks,
            'eeg_channels': key_eeg_channels,
            'sfreq': sfreq
        }

        print(f"  - 处理了 {eeg_normalized.shape[0]} 个epoch, 每个epoch包含 {eeg_normalized.shape[2]} 个时间点")

    return processed_data

def create_segments(processed_data):
    """
    准备数据片段

    参数:
    processed_data (dict): 预处理后的数据

    返回:
    dict: 包含数据片段的字典
    """
    print("准备数据片段...")

    # 存储各阶段的数据片段
    segments_data = {}

    for stage, data in processed_data.items():
        print(f"处理 {stage} 阶段数据...")

        # 提取数据
        eeg = data['eeg']  # (n_epochs, n_channels, n_times)
        ecg = data['ecg']  # (n_epochs, n_channels, n_times)
        r_peaks = data['r_peaks']  # 每个epoch中R波的位置
        sfreq = data['sfreq']

        # 创建数据片段
        segments = []

        for i in range(eeg.shape[0]):  # 遍历每个epoch
            # 提取当前epoch的数据
            eeg_epoch = eeg[i]  # (n_channels, n_times)
            ecg_epoch = ecg[i]  # (n_channels, n_times)
            r_peak = r_peaks[i]  # R波在当前epoch中的位置

            segments.append({
                'ecg': ecg_epoch,
                'eeg': eeg_epoch,
                'r_peak_idx': r_peak  # R波在片段中的位置
            })

        segments_data[stage] = {
            'segments': segments,
            'eeg_channels': data['eeg_channels'],
            'sfreq': sfreq
        }

        print(f"  - 准备了 {len(segments)} 个数据片段")

    return segments_data

def prepare_dataset(segments_data, batch_size=32, train_ratio=0.8, sequence_length=None):
    """
    准备训练和验证数据集

    参数:
    segments_data (dict): 包含数据片段的字典
    batch_size (int): 批处理大小
    train_ratio (float): 训练集比例
    sequence_length (int): 序列长度，如果为None则使用片段的实际长度

    返回:
    dict: 包含数据加载器的字典
    """
    print("准备数据集...")

    class HEPDataset(Dataset):
        def __init__(self, segments, seq_length=None):
            self.segments = segments
            self.seq_length = seq_length

        def __len__(self):
            return len(self.segments)

        def __getitem__(self, idx):
            segment = self.segments[idx]

            # 提取数据
            ecg = segment['ecg']
            eeg = segment['eeg']

            # 确保ECG是2D数组 [channels, time]
            if ecg.ndim == 1:
                ecg = ecg.reshape(1, -1)

            # 如果指定了序列长度，则裁剪或填充
            if self.seq_length is not None:
                if ecg.shape[1] > self.seq_length:
                    # 裁剪
                    start_idx = 0  # 从头开始，保留R波
                    ecg = ecg[:, start_idx:start_idx + self.seq_length]
                    eeg = eeg[:, start_idx:start_idx + self.seq_length]
                elif ecg.shape[1] < self.seq_length:
                    # 填充
                    pad_length = self.seq_length - ecg.shape[1]
                    ecg = np.pad(ecg, ((0, 0), (0, pad_length)), 'constant')
                    eeg = np.pad(eeg, ((0, 0), (0, pad_length)), 'constant')

            # 转换为PyTorch张量
            ecg_tensor = torch.FloatTensor(ecg)
            eeg_tensor = torch.FloatTensor(eeg)

            # 创建输入和目标
            # 输入: 序列前T-1个时间点
            # 目标: 序列后T-1个时间点
            ecg_input = ecg_tensor[:, :-1]
            ecg_target = ecg_tensor[:, 1:]

            return {
                'ecg_input': ecg_input,
                'ecg_target': ecg_target,
                'eeg': eeg_tensor
            }

    # 合并所有阶段的数据片段
    all_segments = []
    for stage, data in segments_data.items():
        all_segments.extend(data['segments'])

    print(f"总共有 {len(all_segments)} 个数据片段")

    # 创建数据集
    dataset = HEPDataset(all_segments, seq_length=sequence_length)

    # 划分训练集和验证集
    train_size = int(train_ratio * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)

    print(f"训练集大小: {train_size}个样本, 验证集大小: {val_size}个样本")

    return {
        'train': train_loader,
        'val': val_loader
    }

def main():
    """主函数"""
    # 1. 加载数据
    stages_data = load_subject_data()

    if not stages_data:
        print("错误: 加载数据失败")
        return

    # 2. 数据预处理
    key_channels = ['Fz', 'Cz', 'Pz']  # 关键中线导联
    processed_data = preprocess_data(stages_data, key_channels)

    if not processed_data:
        print("错误: 数据预处理失败")
        return

    # 3. 创建数据片段
    segments_data = create_segments(processed_data)

    # 4. 准备数据集
    dataloaders = prepare_dataset(segments_data, batch_size=32, sequence_length=500)

    # 5. 打印数据集信息
    for batch in dataloaders['train']:
        print(f"批次大小: {batch['ecg_input'].shape[0]}")
        print(f"ECG输入形状: {batch['ecg_input'].shape}")
        print(f"ECG目标形状: {batch['ecg_target'].shape}")
        print(f"EEG形状: {batch['eeg'].shape}")
        break

    print("阶段一: 基础数据处理与模型框架搭建完成")

if __name__ == "__main__":
    main()

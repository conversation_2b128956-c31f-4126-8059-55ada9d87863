<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="686.554062pt" height="326.098125pt" viewBox="0 0 686.554062 326.098125" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-21T15:59:12.932990</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 326.098125 
L 686.554062 326.098125 
L 686.554062 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 47.059375 301.666875 
L 311.51 301.666875 
L 311.51 70.64625 
L 47.059375 70.64625 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1"/>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m2e20856502" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m2e20856502" x="59.079858" y="301.666875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m2e20856502" x="119.182273" y="301.666875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m2e20856502" x="179.284687" y="301.666875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m2e20856502" x="239.387102" y="301.666875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m2e20856502" x="299.489517" y="301.666875" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <defs>
       <path id="m1d3678f79b" d="M 0 0 
L 0 2 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m1d3678f79b" x="89.131065" y="301.666875" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m1d3678f79b" x="149.23348" y="301.666875" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m1d3678f79b" x="209.335895" y="301.666875" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m1d3678f79b" x="269.43831" y="301.666875" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_1">
     <!-- 时间 (秒) -->
     <g transform="translate(159.034687 317.177812) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-65f6" d="M 5094 -13 
L 5114 -224 
Q 5114 -358 5040 -432 
Q 4966 -506 4892 -534 
Q 4819 -563 4774 -563 
Q 4730 -563 4576 -489 
Q 4422 -416 4243 -304 
Q 4064 -192 3904 -70 
Q 3744 51 3641 150 
Q 3539 250 3539 294 
Q 3539 339 3600 339 
Q 3661 339 3821 269 
Q 4237 70 4685 -38 
L 4666 3091 
L 3142 3002 
Q 3104 2995 3072 2995 
L 3002 2995 
Q 2822 2995 2771 3046 
Q 2650 3168 2605 3341 
Q 2598 3360 2598 3389 
Q 2598 3418 2617 3418 
Q 2637 3418 2717 3389 
Q 2797 3360 2950 3360 
L 2995 3360 
L 4666 3456 
L 4659 4589 
Q 4659 4774 4579 4883 
Q 4499 4992 4499 5030 
Q 4499 5069 4579 5069 
Q 4659 5069 4828 5008 
Q 4998 4947 5033 4899 
Q 5069 4851 5069 4762 
L 5075 3482 
L 5562 3507 
Q 5766 3526 5827 3552 
Q 5888 3578 5933 3578 
Q 5978 3578 6064 3523 
Q 6150 3469 6217 3392 
Q 6285 3315 6285 3264 
Q 6285 3213 6237 3197 
Q 6189 3181 6125 3174 
L 5075 3117 
L 5094 -13 
z
M 4032 1389 
Q 3885 1254 3811 1254 
Q 3738 1254 3674 1350 
Q 3398 1824 3059 2246 
Q 3021 2298 3021 2346 
Q 3021 2394 3075 2438 
Q 3130 2483 3190 2508 
Q 3251 2534 3280 2534 
Q 3309 2534 3341 2496 
Q 3373 2458 3446 2368 
Q 3520 2278 3629 2150 
Q 3738 2022 3843 1891 
Q 3949 1760 4019 1654 
Q 4090 1549 4090 1497 
Q 4090 1446 4032 1389 
z
M 2003 2400 
L 1094 2355 
L 1120 1088 
L 1978 1114 
L 2003 2400 
z
M 2042 3974 
L 1062 3891 
L 1088 2694 
L 2016 2739 
L 2042 3974 
z
M 691 3885 
Q 691 4058 646 4160 
Q 602 4262 602 4313 
Q 602 4365 678 4365 
Q 755 4365 1050 4230 
L 2144 4320 
L 2214 4320 
Q 2278 4320 2377 4272 
Q 2477 4224 2477 4109 
Q 2477 4070 2464 4032 
Q 2451 3994 2445 3962 
L 2342 1126 
Q 2522 928 2522 867 
Q 2522 806 2464 796 
Q 2406 787 2336 781 
L 1126 742 
L 1133 378 
L 1133 358 
Q 1133 224 1024 224 
Q 883 224 800 304 
Q 717 384 717 429 
L 717 493 
Q 717 512 736 589 
Q 755 666 755 896 
L 691 3885 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-95f4" d="M 2643 1139 
L 3795 1190 
L 3840 1990 
L 2611 1920 
L 2643 1139 
z
M 2598 2253 
L 3859 2323 
L 3904 3053 
L 2566 2976 
L 2598 2253 
z
M 2547 435 
Q 2522 435 2496 444 
Q 2470 454 2394 480 
Q 2266 525 2266 653 
L 2266 1062 
Q 2176 2995 2166 3100 
Q 2157 3206 2125 3280 
Q 2093 3354 2093 3395 
Q 2093 3437 2163 3437 
Q 2234 3437 2573 3341 
L 3987 3437 
Q 4147 3437 4249 3360 
Q 4352 3283 4352 3213 
L 4179 1197 
Q 4352 992 4352 931 
Q 4352 870 4285 857 
Q 4218 845 2656 774 
L 2662 563 
Q 2662 435 2547 435 
z
M 4154 282 
Q 4154 333 4205 333 
Q 4256 333 4371 275 
Q 4787 58 5280 -96 
L 5331 4378 
L 3066 4230 
Q 2976 4211 2877 4211 
Q 2778 4211 2739 4256 
Q 2701 4301 2621 4419 
Q 2541 4538 2541 4598 
Q 2541 4659 2573 4659 
Q 2579 4659 2588 4652 
Q 2598 4646 2716 4630 
Q 2835 4614 2938 4614 
Q 5446 4774 5545 4774 
Q 5645 4774 5712 4694 
Q 5779 4614 5779 4563 
L 5747 4397 
L 5696 -109 
L 5702 -275 
Q 5702 -461 5590 -557 
Q 5478 -653 5408 -653 
Q 5338 -653 5120 -531 
Q 4902 -410 4528 -106 
Q 4154 198 4154 282 
z
M 1805 3987 
Q 1619 4288 1165 4800 
Q 1114 4864 1114 4931 
Q 1114 4998 1194 5056 
Q 1274 5114 1312 5114 
Q 1408 5114 1914 4550 
Q 2176 4262 2176 4154 
Q 2176 4128 2086 4022 
Q 1997 3917 1926 3917 
Q 1856 3917 1805 3987 
z
M 1165 -486 
Q 1120 -486 1030 -435 
Q 800 -320 800 -166 
Q 800 -128 825 16 
Q 851 160 851 301 
L 851 3430 
Q 851 3584 736 3738 
Q 704 3782 704 3795 
Q 704 3834 803 3834 
Q 902 3834 1049 3782 
Q 1197 3731 1232 3689 
Q 1267 3648 1267 3565 
L 1274 -326 
Q 1274 -486 1165 -486 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-28" d="M 2016 -896 
Q 2016 -1018 1914 -1018 
Q 1862 -1018 1715 -883 
Q 1350 -544 1037 102 
Q 640 902 640 1846 
Q 640 2790 998 3644 
Q 1357 4499 1728 4902 
Q 1882 5062 1946 5062 
Q 2067 5062 2067 4954 
Q 2067 4902 2035 4870 
Q 1619 4384 1369 3478 
Q 1120 2573 1120 1865 
Q 1120 1158 1350 384 
Q 1581 -390 1965 -774 
Q 2016 -826 2016 -896 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-79d2" d="M 4627 2323 
L 4621 1734 
Q 4621 1574 4506 1587 
Q 4346 1587 4237 1709 
Q 4192 1760 4192 1811 
Q 4192 1862 4205 1942 
Q 4218 2022 4224 2150 
L 4256 4787 
Q 4256 4954 4182 5043 
Q 4109 5133 4109 5171 
Q 4109 5210 4182 5210 
Q 4256 5210 4413 5158 
Q 4570 5107 4611 5065 
Q 4653 5024 4653 4928 
L 4627 2323 
z
M 5722 2195 
Q 5222 1133 4441 438 
Q 3661 -256 2528 -589 
Q 2445 -614 2361 -614 
Q 2278 -614 2278 -570 
Q 2278 -493 2470 -410 
Q 3398 6 4108 652 
Q 4819 1299 5171 2131 
Q 5254 2330 5254 2554 
Q 5254 2637 5306 2637 
Q 5370 2637 5472 2563 
Q 5574 2490 5657 2397 
Q 5741 2304 5741 2265 
Q 5741 2227 5722 2195 
z
M 1434 -326 
Q 1478 32 1478 237 
L 1478 397 
L 1491 1805 
Q 1491 1901 1500 2019 
Q 1510 2138 1517 2227 
L 1523 2310 
Q 1286 1715 794 1101 
Q 582 838 416 681 
Q 250 525 192 525 
Q 147 525 147 576 
Q 147 627 218 736 
Q 928 1734 1427 2938 
L 877 2893 
Q 800 2886 742 2880 
Q 685 2874 627 2874 
Q 570 2874 470 2922 
Q 371 2970 294 3194 
Q 288 3206 288 3235 
Q 288 3264 333 3264 
L 634 3232 
L 691 3232 
L 1498 3296 
L 1504 4051 
Q 781 3802 518 3802 
Q 429 3802 429 3856 
Q 429 3910 550 3955 
Q 557 3962 717 4026 
Q 2074 4582 2253 4934 
Q 2278 4998 2326 4998 
Q 2374 4998 2505 4844 
Q 2637 4691 2637 4620 
Q 2637 4550 2598 4531 
Q 2240 4339 1882 4198 
L 1882 3328 
L 2157 3347 
Q 2304 3360 2381 3385 
Q 2458 3411 2502 3411 
Q 2547 3411 2630 3360 
Q 2842 3232 2842 3123 
Q 2842 3053 2733 3040 
L 1882 2970 
L 1882 2522 
Q 1958 2566 1987 2566 
Q 2016 2566 2112 2483 
Q 2310 2330 2576 2026 
Q 2842 1722 2842 1626 
Q 2842 1587 2765 1491 
Q 2688 1395 2605 1395 
Q 2522 1395 2432 1536 
Q 2208 1907 1882 2246 
L 1882 -416 
Q 1882 -582 1766 -582 
Q 1619 -582 1526 -499 
Q 1434 -416 1434 -326 
z
M 5773 2624 
Q 5370 3238 4902 3770 
Q 4858 3821 4858 3872 
Q 4858 3923 4938 3996 
Q 5018 4070 5082 4070 
Q 5146 4070 5190 4013 
Q 5677 3507 5920 3187 
Q 6163 2867 6163 2809 
Q 6163 2752 6105 2694 
Q 6048 2637 5977 2598 
Q 5907 2560 5862 2560 
Q 5818 2560 5773 2624 
z
M 3123 2854 
Q 3462 3482 3462 3898 
Q 3462 3987 3516 3987 
Q 3571 3987 3673 3920 
Q 3776 3853 3862 3769 
Q 3949 3686 3949 3641 
Q 3949 3597 3923 3546 
Q 3616 2938 3357 2621 
Q 3098 2304 2934 2192 
Q 2771 2080 2736 2080 
Q 2701 2080 2701 2131 
Q 2701 2182 2835 2374 
Q 2970 2566 3123 2854 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-29" d="M 518 -883 
Q 371 -1018 320 -1018 
Q 218 -1018 218 -896 
Q 218 -826 269 -774 
Q 653 -390 883 384 
Q 1114 1158 1114 1865 
Q 1114 2573 864 3478 
Q 614 4384 198 4870 
Q 166 4902 166 4954 
Q 166 5062 288 5062 
Q 358 5062 512 4902 
Q 877 4499 1203 3738 
Q 1594 2790 1594 1849 
Q 1594 909 1238 179 
Q 883 -550 518 -883 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-65f6"/>
      <use xlink:href="#LXGWWenKai-Regular-95f4" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-79d2" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="369.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_10">
      <defs>
       <path id="mbf334138d6" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="290.901074" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −1.5 -->
      <g transform="translate(21.059375 294.43623) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-2212" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="254.42557" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −1.0 -->
      <g transform="translate(21.059375 257.960726) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_12">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="217.950067" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −0.5 -->
      <g transform="translate(21.059375 221.485223) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_13">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="181.474563" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.0 -->
      <g transform="translate(24.559375 185.00972) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_14">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="144.99906" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.5 -->
      <g transform="translate(24.559375 148.534216) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_15">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="108.523557" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 1.0 -->
      <g transform="translate(24.559375 112.058713) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_16">
      <g>
       <use xlink:href="#mbf334138d6" x="47.059375" y="72.048053" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 1.5 -->
      <g transform="translate(24.559375 75.58321) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_17">
      <defs>
       <path id="mfd2ad3e83d" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="272.663322" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_18">
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="236.187819" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_19">
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="199.712315" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_20">
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="163.236812" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_21">
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="126.761308" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_22">
      <g>
       <use xlink:href="#mfd2ad3e83d" x="47.059375" y="90.285805" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_9">
     <!-- 振幅 (μV) -->
     <g transform="translate(15.159375 207.316719) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-632f" d="M 326 3590 
Q 429 3565 506 3565 
L 627 3565 
Q 672 3565 723 3571 
L 1402 3616 
L 1408 4621 
Q 1408 4794 1293 4973 
Q 1274 5005 1274 5024 
Q 1274 5069 1341 5069 
Q 1408 5069 1517 5043 
Q 1805 4966 1805 4800 
L 1798 3642 
L 1958 3654 
Q 2048 3661 2137 3699 
Q 2227 3738 2259 3738 
Q 2291 3738 2374 3686 
Q 2598 3546 2598 3450 
Q 2598 3366 2438 3354 
L 1792 3302 
L 1786 2227 
Q 2042 2349 2275 2477 
Q 2509 2605 2569 2605 
Q 2630 2605 2630 2560 
Q 2630 2445 1786 1901 
L 1773 122 
L 1786 -141 
Q 1786 -301 1680 -400 
Q 1574 -499 1466 -499 
Q 1261 -499 902 -108 
Q 544 282 544 384 
Q 544 416 585 416 
Q 627 416 838 272 
Q 1050 128 1382 6 
L 1389 1658 
Q 666 1235 531 1235 
Q 493 1235 454 1254 
Q 282 1350 173 1517 
Q 160 1542 160 1555 
Q 160 1594 243 1603 
Q 326 1613 659 1734 
Q 992 1856 1395 2042 
L 1402 3270 
L 851 3226 
Q 710 3213 611 3213 
Q 512 3213 474 3245 
Q 384 3334 345 3424 
Q 307 3514 300 3523 
Q 294 3533 294 3561 
Q 294 3590 326 3590 
z
M 3174 4282 
Q 3174 3142 3123 2547 
L 5280 2656 
Q 5427 2669 5504 2697 
Q 5581 2726 5622 2726 
Q 5664 2726 5801 2627 
Q 5939 2528 5939 2438 
Q 5939 2368 5786 2355 
L 4307 2278 
Q 4557 1722 4723 1440 
Q 5203 1920 5216 2150 
Q 5229 2246 5289 2246 
Q 5350 2246 5420 2176 
Q 5491 2106 5539 2029 
Q 5587 1952 5587 1913 
Q 5587 1875 5466 1741 
Q 5216 1446 4902 1178 
Q 5446 474 6240 13 
Q 6317 -32 6317 -70 
Q 6317 -90 6259 -154 
Q 6106 -339 5990 -339 
Q 5965 -339 5926 -314 
Q 5466 26 5107 384 
Q 4410 1088 3968 2259 
L 3782 2253 
L 3763 166 
Q 4147 288 4406 406 
Q 4666 525 4730 525 
Q 4794 525 4794 486 
Q 4794 378 4285 90 
Q 3776 -198 3481 -323 
Q 3187 -448 3123 -448 
Q 3059 -448 2966 -361 
Q 2874 -275 2813 -185 
Q 2752 -96 2752 -67 
Q 2752 -38 2816 -38 
L 2906 -38 
Q 3149 -38 3373 51 
L 3392 2234 
L 3091 2214 
Q 2976 1203 2541 307 
Q 2362 -58 2211 -262 
Q 2061 -467 2006 -467 
Q 1952 -467 1952 -403 
Q 1952 -339 1997 -250 
Q 2592 1024 2714 2342 
Q 2771 2970 2771 3456 
Q 2771 3942 2768 4227 
Q 2765 4512 2717 4598 
Q 2669 4685 2669 4733 
Q 2669 4781 2771 4781 
Q 2874 4781 3194 4621 
L 5069 4742 
Q 5216 4755 5283 4780 
Q 5350 4806 5401 4806 
Q 5453 4806 5530 4749 
Q 5728 4621 5728 4518 
Q 5728 4448 5574 4435 
L 3174 4282 
z
M 3546 3616 
L 4742 3686 
Q 4890 3699 4957 3724 
Q 5024 3750 5075 3750 
Q 5126 3750 5210 3699 
Q 5402 3565 5402 3462 
Q 5402 3386 5248 3373 
L 3821 3283 
Q 3770 3277 3725 3277 
L 3642 3277 
Q 3514 3277 3491 3302 
Q 3469 3328 3385 3417 
Q 3302 3507 3302 3571 
Q 3302 3635 3334 3635 
L 3546 3616 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e45" d="M 2336 1325 
Q 2170 1325 1747 1824 
L 1728 -429 
Q 1728 -595 1619 -595 
Q 1562 -595 1440 -521 
Q 1318 -448 1318 -326 
L 1318 -294 
Q 1318 -275 1337 -156 
Q 1357 -38 1370 550 
L 1395 3322 
L 986 3290 
L 973 1414 
Q 973 1254 867 1254 
Q 762 1254 659 1350 
Q 557 1446 557 1491 
Q 557 1536 579 1638 
Q 602 1741 602 1862 
L 614 3264 
Q 614 3482 589 3558 
L 550 3661 
Q 544 3693 544 3712 
Q 544 3757 611 3757 
Q 678 3757 979 3629 
L 1402 3661 
L 1408 4640 
Q 1408 4858 1306 5005 
Q 1280 5037 1280 5065 
Q 1280 5094 1350 5094 
Q 1421 5094 1571 5056 
Q 1722 5018 1750 4957 
Q 1779 4896 1779 4826 
L 1766 3686 
L 2336 3725 
L 2400 3725 
Q 2547 3725 2618 3597 
Q 2637 3558 2637 3526 
L 2618 3398 
L 2624 1805 
L 2630 1632 
Q 2630 1472 2524 1398 
Q 2419 1325 2336 1325 
z
M 2893 4576 
L 5293 4717 
Q 5402 4723 5472 4745 
Q 5542 4768 5590 4768 
Q 5638 4768 5708 4710 
Q 5779 4653 5830 4582 
Q 5882 4512 5882 4474 
Q 5882 4397 5722 4384 
L 3117 4224 
L 3040 4224 
Q 2880 4224 2797 4285 
Q 2714 4346 2653 4445 
Q 2592 4544 2592 4569 
Q 2592 4595 2630 4595 
L 2688 4595 
L 2893 4576 
z
M 3194 3475 
Q 3174 3642 3113 3728 
Q 3053 3814 3053 3849 
Q 3053 3885 3158 3885 
Q 3264 3885 3552 3795 
L 5133 3891 
L 5190 3891 
Q 5293 3891 5366 3824 
Q 5440 3757 5440 3709 
Q 5440 3661 5427 3632 
Q 5414 3603 5402 3565 
L 5286 2938 
Q 5421 2778 5421 2723 
Q 5421 2669 5366 2656 
Q 5312 2643 5229 2637 
L 3667 2560 
L 3667 2502 
Q 3667 2355 3565 2355 
Q 3520 2355 3430 2394 
Q 3270 2458 3270 2566 
L 3270 2592 
Q 3283 2669 3283 2720 
L 3277 2810 
L 3194 3475 
z
M 4992 3552 
L 3578 3469 
L 3635 2880 
L 4915 2950 
L 4992 3552 
z
M 2234 3379 
L 1766 3347 
L 1754 1984 
Q 1958 1875 2240 1760 
L 2234 3379 
z
M 5581 2170 
L 5638 2176 
Q 5728 2176 5805 2093 
Q 5882 2010 5882 1958 
Q 5882 1907 5869 1878 
Q 5856 1850 5850 1818 
L 5670 173 
Q 5869 -32 5869 -86 
Q 5869 -141 5821 -150 
Q 5773 -160 5690 -166 
L 3347 -224 
L 3354 -371 
L 3354 -397 
Q 3354 -531 3264 -531 
Q 3130 -531 3043 -448 
Q 2957 -365 2957 -269 
L 2957 -237 
Q 2970 -96 2970 32 
L 2970 96 
L 2886 1677 
Q 2874 1875 2819 1987 
Q 2765 2099 2765 2128 
Q 2765 2157 2841 2157 
Q 2918 2157 3232 2054 
L 5581 2170 
z
M 5446 1830 
L 4506 1786 
L 4499 1165 
L 5395 1197 
L 5446 1830 
z
M 4147 1773 
L 3245 1728 
L 3277 1114 
L 4141 1152 
L 4147 1773 
z
M 5370 883 
L 4499 851 
L 4493 147 
L 5306 166 
L 5370 883 
z
M 4141 838 
L 3290 806 
L 3328 115 
L 4134 134 
L 4141 838 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-3bc" d="M 1658 -102 
Q 1190 -102 998 307 
L 979 -499 
Q 973 -826 979 -1171 
Q 979 -1216 886 -1216 
Q 794 -1216 656 -1155 
Q 518 -1094 512 -1018 
L 512 -1005 
Q 538 -691 538 -448 
L 627 2349 
Q 646 2790 634 3027 
Q 640 3098 736 3098 
Q 832 3098 957 3030 
Q 1082 2963 1094 2874 
L 1094 2854 
Q 1094 2771 1081 2604 
Q 1069 2438 1062 2298 
L 1030 1235 
Q 1030 1146 1046 1002 
Q 1062 858 1123 694 
Q 1184 531 1305 419 
Q 1427 307 1683 307 
Q 1939 307 2179 508 
Q 2419 710 2438 1101 
L 2496 2323 
Q 2509 2637 2496 2995 
Q 2496 3046 2589 3046 
Q 2682 3046 2816 2982 
Q 2950 2918 2963 2835 
L 2963 2822 
Q 2957 2733 2947 2582 
Q 2938 2432 2931 2272 
L 2874 890 
Q 2867 666 2880 576 
Q 2918 250 3132 250 
Q 3347 250 3468 304 
Q 3590 358 3657 358 
Q 3725 358 3731 300 
Q 3738 243 3648 154 
Q 3430 -83 3110 -83 
Q 2906 -83 2736 6 
Q 2566 96 2528 333 
Q 2381 109 2144 3 
Q 1907 -102 1658 -102 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-56" d="M 70 4403 
Q 70 4506 307 4506 
Q 410 4506 506 4474 
Q 602 4442 614 4378 
Q 653 4230 944 3494 
Q 1235 2758 1946 634 
Q 2765 2816 3059 3539 
Q 3354 4262 3411 4480 
Q 3430 4550 3536 4550 
Q 3642 4550 3770 4473 
Q 3898 4397 3898 4307 
Q 3898 4275 3821 4147 
Q 3744 4019 3667 3840 
Q 2387 813 2240 224 
Q 2214 109 2202 38 
Q 2182 -109 2035 -109 
Q 1933 -109 1817 -9 
Q 1702 90 1606 374 
Q 1510 659 1344 1126 
Q 429 3667 77 4378 
Q 70 4390 70 4403 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-632f"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="326.199936"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="388.199921"/>
     </g>
    </g>
    <g id="text_10">
     <!-- 1e−6 -->
     <g transform="translate(47.059375 67.64625) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-65" d="M 992 1613 
Q 1709 1613 2317 1779 
Q 2547 1850 2614 1933 
Q 2682 2016 2682 2182 
Q 2682 2739 2035 2739 
Q 1594 2739 1274 2371 
Q 954 2003 864 1613 
L 992 1613 
z
M 384 1626 
L 442 1632 
Q 538 2138 992 2624 
Q 1446 3110 2042 3110 
Q 2438 3110 2675 2970 
Q 3104 2720 3104 2176 
Q 3104 1958 3069 1830 
Q 3034 1702 2906 1622 
Q 2778 1542 2490 1453 
Q 1850 1254 896 1254 
L 813 1254 
L 813 1190 
Q 813 774 1027 518 
Q 1242 262 1606 262 
Q 2413 262 2912 800 
Q 2995 896 3059 896 
Q 3123 896 3123 829 
Q 3123 762 3030 614 
Q 2938 467 2746 294 
Q 2278 -115 1562 -115 
Q 1235 -115 969 48 
Q 704 211 550 518 
Q 397 826 397 1248 
L 397 1338 
Q 262 1440 262 1542 
Q 262 1626 384 1626 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="line2d_23">
    <path d="M 59.079858 126.75018 
L 59.680882 124.078544 
L 60.281906 124.985083 
L 61.483955 131.919401 
L 62.084979 133.636633 
L 62.686003 132.669002 
L 63.287027 129.107039 
L 64.489075 116.960151 
L 65.090099 111.207169 
L 65.691124 108.904942 
L 66.292148 112.670585 
L 66.893172 123.639063 
L 69.297268 190.222274 
L 69.898293 199.49147 
L 70.499317 205.951951 
L 71.100341 210.303999 
L 71.701365 211.572571 
L 72.302389 207.855046 
L 72.903413 198.440835 
L 74.105462 174.106143 
L 74.706486 169.169039 
L 75.30751 172.926502 
L 76.509558 194.371001 
L 77.110582 201.323678 
L 77.711607 201.803159 
L 78.312631 197.544186 
L 78.913655 192.338454 
L 79.514679 189.007693 
L 80.115703 187.438593 
L 80.716727 185.008293 
L 81.317751 178.93703 
L 83.120824 148.589535 
L 83.721848 146.809395 
L 84.322872 152.795163 
L 86.125945 186.97153 
L 86.726969 190.942379 
L 87.327993 188.549041 
L 87.929017 181.470168 
L 88.530041 172.883684 
L 89.131065 166.326712 
L 89.732089 164.306853 
L 90.333114 167.084491 
L 90.934138 172.296095 
L 91.535162 175.934771 
L 92.136186 174.490928 
L 92.73721 167.162846 
L 93.939259 148.087648 
L 94.540283 145.69197 
L 95.141307 150.575679 
L 96.343355 168.246601 
L 96.944379 171.652433 
L 97.545403 168.958129 
L 100.550524 134.718859 
L 101.752572 118.2577 
L 102.353597 114.801835 
L 102.954621 117.990415 
L 104.156669 136.330562 
L 104.757693 142.182654 
L 105.358717 142.290797 
L 105.959741 139.290857 
L 106.560766 138.738642 
L 107.16179 145.310803 
L 107.762814 159.479212 
L 108.964862 191.824999 
L 109.565886 199.729389 
L 110.166911 201.139976 
L 110.767935 200.25198 
L 111.368959 201.877937 
L 111.969983 208.159285 
L 113.172031 226.03745 
L 113.773055 230.883806 
L 114.37408 231.729671 
L 114.975104 230.674661 
L 115.576128 230.126836 
L 116.778176 231.512577 
L 117.3792 230.476081 
L 118.581249 224.38968 
L 119.182273 224.208228 
L 119.783297 228.668213 
L 120.985345 245.666825 
L 121.586369 251.720981 
L 122.187393 253.605045 
L 122.788418 252.443265 
L 123.389442 250.616415 
L 124.59149 248.639859 
L 125.192514 245.364697 
L 125.793538 237.855159 
L 126.995587 214.932996 
L 127.596611 206.962572 
L 128.197635 205.232984 
L 128.798659 208.917015 
L 129.399683 214.384866 
L 130.000707 217.576675 
L 130.601732 216.529043 
L 132.404804 204.310468 
L 133.005828 202.173839 
L 133.606852 198.790779 
L 134.207876 192.171604 
L 135.409925 174.968039 
L 136.010949 172.708591 
L 136.611973 178.278743 
L 137.814021 198.173029 
L 138.415045 198.912599 
L 139.01607 188.32571 
L 140.218118 151.065837 
L 140.819142 140.380469 
L 141.420166 141.318123 
L 142.02119 151.300445 
L 142.622214 163.553504 
L 143.223239 171.528238 
L 143.824263 172.824472 
L 144.425287 170.18525 
L 145.026311 169.139804 
L 145.627335 174.03538 
L 146.228359 185.089929 
L 146.829384 198.242992 
L 147.430408 207.683097 
L 148.031432 209.29598 
L 148.632456 202.906867 
L 149.834504 182.272466 
L 150.435528 177.382248 
L 151.036553 178.508339 
L 152.238601 188.089373 
L 152.839625 188.613565 
L 153.440649 183.239948 
L 155.243722 152.454627 
L 155.844746 148.313403 
L 156.44577 148.348331 
L 157.046794 148.881723 
L 157.647818 145.193457 
L 158.248842 134.39292 
L 159.450891 100.218163 
L 160.051915 89.117636 
L 160.652939 89.265929 
L 161.253963 100.611487 
L 162.456011 133.370614 
L 163.057036 141.09794 
L 163.65806 140.015823 
L 164.259084 134.164868 
L 164.860108 129.807113 
L 165.461132 131.319008 
L 166.66318 146.5171 
L 167.264205 149.732112 
L 167.865229 144.953141 
L 169.067277 123.036488 
L 169.668301 118.48332 
L 170.269325 123.488306 
L 171.471374 147.479005 
L 172.072398 151.756998 
L 172.673422 144.49287 
L 173.87547 110.266718 
L 174.476494 99.926753 
L 175.077518 102.570595 
L 175.678543 117.361045 
L 176.880591 155.263437 
L 177.481615 163.921093 
L 178.082639 163.226059 
L 178.683663 157.615987 
L 179.284687 153.253471 
L 179.885712 154.221059 
L 180.486736 160.537053 
L 181.688784 176.290356 
L 183.491857 191.631244 
L 184.092881 201.618356 
L 185.294929 224.744412 
L 185.895953 231.601308 
L 186.496977 234.149628 
L 187.098001 235.003982 
L 187.699026 237.641513 
L 188.30005 243.830005 
L 189.502098 260.404379 
L 190.103122 265.374865 
L 190.704146 267.398829 
L 191.30517 268.803797 
L 191.906195 272.018369 
L 193.108243 281.634823 
L 193.709267 281.480319 
L 194.310291 274.686669 
L 196.113364 240.882561 
L 196.714388 236.581349 
L 197.315412 235.032226 
L 197.916436 232.56417 
L 198.51746 227.130557 
L 199.118484 220.119077 
L 199.719509 215.342057 
L 200.320533 215.899088 
L 201.522581 227.177083 
L 202.123605 227.898637 
L 202.724629 220.42927 
L 204.527702 180.877463 
L 205.128726 176.661988 
L 205.72975 176.359488 
L 206.330774 175.303078 
L 206.931798 170.410123 
L 207.532822 162.672919 
L 208.133847 156.544476 
L 208.734871 156.681651 
L 209.335895 164.411359 
L 210.537943 186.750858 
L 211.138967 190.195654 
L 211.739991 185.551666 
L 212.94204 166.06548 
L 213.543064 159.940703 
L 214.144088 157.921211 
L 214.745112 157.52274 
L 215.346136 155.473369 
L 215.947161 150.029929 
L 217.149209 133.763688 
L 217.750233 127.974319 
L 218.351257 125.683777 
L 218.952281 126.205355 
L 219.553305 127.7655 
L 220.15433 128.569606 
L 220.755354 127.540029 
L 221.356378 124.46382 
L 221.957402 119.751406 
L 223.15945 108.591844 
L 223.760474 104.034793 
L 224.361499 101.224867 
L 224.962523 100.319411 
L 226.164571 100.993075 
L 226.765595 100.078024 
L 227.366619 97.563233 
L 227.967643 94.445921 
L 228.568668 92.834864 
L 229.169692 94.978608 
L 229.770716 102.003543 
L 231.573788 134.609077 
L 232.174812 138.174481 
L 232.775837 134.852417 
L 234.578909 110.008315 
L 235.179933 109.631794 
L 235.780957 115.989253 
L 237.58403 146.613667 
L 238.185054 149.473077 
L 238.786078 147.034427 
L 239.988126 135.452739 
L 240.589151 131.619866 
L 241.190175 130.853541 
L 241.791199 132.521874 
L 242.392223 134.956865 
L 242.993247 136.388057 
L 243.594271 135.796832 
L 244.195295 133.320915 
L 244.79632 130.06653 
L 245.397344 127.456448 
L 245.998368 126.452992 
L 246.599392 127.057124 
L 247.200416 128.357493 
L 247.80144 129.125394 
L 248.402464 128.648147 
L 249.003489 127.327409 
L 249.604513 126.653731 
L 250.205537 128.48874 
L 250.806561 133.986113 
L 252.609634 160.807529 
L 253.210658 164.77416 
L 253.811682 163.920526 
L 255.01373 155.520731 
L 255.614754 153.996211 
L 256.215778 156.509756 
L 257.417827 167.010729 
L 258.018851 168.676894 
L 258.619875 165.271596 
L 260.422947 143.834391 
L 261.023972 142.781478 
L 261.624996 145.724375 
L 262.22602 149.92836 
L 262.827044 152.19083 
L 263.428068 150.626472 
L 264.029092 145.565149 
L 264.630116 139.16373 
L 265.231141 134.145114 
L 265.832165 132.515368 
L 266.433189 134.9321 
L 267.034213 140.828343 
L 268.837286 165.327486 
L 269.43831 170.67301 
L 270.039334 172.887444 
L 270.640358 172.248735 
L 271.842406 168.401675 
L 272.44343 168.526658 
L 273.044455 170.824335 
L 273.645479 174.651965 
L 274.246503 179.320904 
L 274.847527 185.003722 
L 275.448551 192.718715 
L 277.251624 225.595204 
L 277.852648 229.870384 
L 278.453672 225.433942 
L 279.054696 213.483156 
L 279.65572 199.09496 
L 280.256744 188.75958 
L 280.857768 186.818572 
L 281.458793 193.103139 
L 282.660841 212.015919 
L 283.261865 215.670763 
L 283.862889 214.436661 
L 285.064937 207.640565 
L 285.665962 204.915637 
L 286.266986 201.078378 
L 286.86801 194.36227 
L 288.070058 175.329631 
L 288.671082 168.670539 
L 289.272107 166.484224 
L 289.873131 167.575748 
L 290.474155 169.16541 
L 291.075179 169.331279 
L 291.676203 168.775521 
L 292.277227 170.386652 
L 292.878251 176.804161 
L 294.0803 199.830273 
L 294.681324 207.650864 
L 295.282348 207.9917 
L 295.883372 201.58755 
L 296.484396 192.68523 
L 297.08542 186.082874 
L 297.686445 183.843844 
L 298.287469 184.036987 
L 298.888493 182.380934 
L 299.489517 175.564279 
L 299.489517 175.564279 
" clip-path="url(#pf1b58a0183)" style="fill: none; stroke: #e63946; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_24">
    <path d="M 194.310291 301.666875 
L 194.310291 70.64625 
" clip-path="url(#pf1b58a0183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #808080; stroke-width: 0.8"/>
   </g>
   <g id="line2d_25">
    <path d="M 254.412706 301.666875 
L 254.412706 70.64625 
" clip-path="url(#pf1b58a0183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #808080; stroke-width: 0.8"/>
   </g>
   <g id="line2d_26">
    <path d="M 47.059375 181.474563 
L 311.51 181.474563 
" clip-path="url(#pf1b58a0183)" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_27">
    <path d="M 119.182273 301.666875 
L 119.182273 70.64625 
" clip-path="url(#pf1b58a0183)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 47.059375 301.666875 
L 47.059375 70.64625 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 311.51 301.666875 
L 311.51 70.64625 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 47.059375 301.666875 
L 311.51 301.666875 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 47.059375 70.64625 
L 311.51 70.64625 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_11">
    <!-- 练习阶段 -->
    <g transform="translate(159.284687 64.64625) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-7ec3" d="M 1805 1888 
Q 2074 1933 2240 1961 
Q 2406 1990 2496 1984 
Q 2586 1978 2586 1926 
Q 2573 1837 2201 1680 
Q 1830 1523 1404 1414 
Q 979 1306 912 1312 
Q 845 1318 800 1357 
Q 685 1504 653 1609 
Q 621 1715 621 1728 
Q 621 1786 685 1779 
L 781 1773 
Q 928 1760 1062 1766 
Q 1267 2131 1446 2490 
L 1510 2605 
Q 1050 2938 602 3149 
Q 499 3200 486 3251 
Q 474 3302 515 3401 
Q 557 3501 614 3513 
Q 672 3526 781 3482 
Q 800 3475 813 3469 
Q 1286 4198 1389 4480 
Q 1498 4774 1498 4915 
L 1498 4979 
Q 1498 5050 1542 5050 
Q 1574 5050 1658 4998 
Q 1888 4851 1888 4717 
Q 1850 4557 1651 4186 
Q 1478 3859 1120 3296 
L 1274 3206 
Q 1523 3059 1690 2944 
Q 2048 3654 2080 3840 
Q 2086 3898 2089 3994 
Q 2093 4090 2125 4090 
Q 2157 4090 2240 4045 
Q 2470 3917 2477 3776 
Q 2483 3731 2464 3686 
Q 1990 2707 1485 1830 
Q 1638 1862 1805 1888 
z
M 1485 627 
Q 2003 845 2304 989 
Q 2605 1133 2662 1139 
Q 2720 1146 2720 1126 
Q 2733 986 1840 442 
Q 947 -102 794 -115 
Q 749 -122 669 -45 
Q 589 32 531 128 
Q 474 224 467 275 
Q 467 301 525 307 
L 589 314 
Q 685 320 864 378 
L 1485 627 
z
M 2829 1882 
L 3213 2803 
L 2976 2790 
Q 2701 2752 2656 2803 
Q 2611 2854 2524 2966 
Q 2438 3078 2438 3148 
Q 2438 3219 2458 3219 
Q 2650 3174 2810 3174 
L 2848 3181 
L 3379 3219 
Q 3520 3603 3610 3866 
L 3168 3834 
L 3059 3834 
Q 2854 3834 2803 3885 
Q 2650 4019 2650 4192 
Q 2650 4224 2669 4224 
L 3002 4192 
L 3053 4192 
L 3731 4224 
Q 3891 4768 3910 4873 
Q 3930 4979 3926 5062 
Q 3923 5146 3926 5190 
Q 3930 5235 3994 5235 
Q 4058 5235 4160 5178 
Q 4371 5075 4352 4941 
L 4352 4928 
Q 4326 4877 4147 4250 
L 5114 4307 
Q 5171 4314 5244 4317 
Q 5318 4320 5372 4339 
Q 5427 4358 5500 4358 
Q 5574 4358 5692 4265 
Q 5811 4173 5811 4083 
Q 5811 3994 5651 3974 
L 4032 3891 
Q 3942 3622 3808 3258 
L 4301 3302 
L 4365 3302 
Q 4493 3302 4560 3222 
Q 4627 3142 4627 3085 
L 4595 2918 
L 4589 2176 
L 5037 2208 
Q 5101 2214 5174 2217 
Q 5248 2221 5296 2240 
Q 5344 2259 5421 2259 
Q 5498 2259 5613 2163 
Q 5728 2067 5728 1977 
Q 5728 1888 5568 1875 
L 4595 1811 
L 4576 1811 
L 4589 -45 
L 4602 -250 
Q 4602 -429 4422 -499 
Q 4358 -538 4307 -538 
Q 4256 -538 4080 -445 
Q 3904 -352 3731 -230 
Q 3277 77 3277 218 
Q 3277 256 3325 256 
Q 3373 256 3577 157 
Q 3782 58 4198 -38 
L 4179 1786 
L 3411 1722 
L 3270 1702 
Q 3213 1696 3168 1680 
Q 3123 1664 3052 1661 
Q 2982 1658 2896 1712 
Q 2810 1766 2829 1882 
z
M 2502 218 
Q 3194 934 3194 1274 
Q 3194 1402 3251 1402 
Q 3309 1402 3398 1338 
Q 3635 1190 3635 1075 
Q 3635 1043 3510 860 
Q 3386 678 3130 438 
Q 2874 198 2630 51 
Q 2387 -96 2348 -96 
Q 2310 -96 2310 -41 
Q 2310 13 2502 218 
z
M 4973 1062 
Q 4890 1146 4890 1213 
Q 4890 1280 4973 1344 
Q 5056 1408 5094 1408 
Q 5133 1408 5254 1305 
Q 5376 1203 5533 1046 
Q 5690 890 5830 736 
Q 5971 582 6057 483 
Q 6144 384 6144 301 
Q 6144 218 6045 131 
Q 5946 45 5898 45 
Q 5850 45 5818 73 
Q 5786 102 5542 432 
Q 5299 762 4973 1062 
z
M 4179 2144 
L 4186 2893 
L 3648 2848 
Q 3603 2701 3488 2438 
L 3341 2086 
L 4179 2144 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-4e60" d="M 4173 2182 
Q 4192 2010 2950 1264 
Q 1709 518 1523 499 
Q 1446 486 1299 589 
Q 883 896 1114 928 
Q 1485 998 1734 1100 
Q 1984 1203 2678 1516 
Q 3373 1830 3763 2054 
Q 4154 2278 4173 2182 
z
M 1811 3264 
Q 1670 3347 1670 3424 
Q 1670 3501 1750 3584 
Q 1830 3667 1891 3667 
Q 1952 3667 2262 3529 
Q 2573 3392 3405 2899 
Q 3558 2803 3558 2720 
Q 3558 2637 3475 2515 
Q 3392 2394 3312 2394 
Q 3232 2394 3030 2544 
Q 2829 2694 1811 3264 
z
M 5382 4294 
Q 5363 2342 5082 653 
Q 4947 -192 4873 -336 
Q 4800 -480 4713 -566 
Q 4627 -653 4528 -653 
Q 4429 -653 4352 -627 
Q 4275 -602 4067 -464 
Q 3859 -326 3066 358 
Q 2957 448 2957 525 
Q 2957 582 3027 582 
Q 3098 582 3293 460 
Q 3488 339 3901 112 
Q 4314 -115 4410 -115 
Q 4448 -90 4448 -70 
Q 4902 1549 4947 4320 
L 1478 4154 
Q 1376 4141 1286 4141 
Q 1126 4141 1011 4314 
Q 928 4422 928 4483 
Q 928 4544 963 4544 
Q 998 4544 1056 4528 
Q 1114 4512 1235 4512 
L 5018 4704 
L 5120 4710 
Q 5229 4710 5328 4630 
Q 5427 4550 5427 4480 
Q 5427 4410 5404 4368 
Q 5382 4326 5382 4294 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-9636" d="M 4090 4160 
Q 3392 3091 2726 2496 
Q 2483 2272 2348 2201 
Q 2214 2131 2185 2131 
Q 2157 2131 2157 2182 
Q 2157 2234 2266 2355 
Q 3328 3590 3789 4499 
Q 3994 4890 3994 5043 
L 3994 5082 
Q 3994 5190 4048 5190 
Q 4102 5190 4192 5139 
Q 4448 4992 4435 4845 
Q 4435 4826 4374 4675 
Q 4314 4525 4282 4486 
Q 4710 3898 5184 3427 
Q 5658 2957 6170 2522 
Q 6240 2451 6237 2412 
Q 6234 2374 6170 2317 
Q 5990 2176 5907 2176 
Q 5882 2182 5709 2323 
Q 5338 2630 4698 3373 
Q 4410 3706 4090 4160 
z
M 4582 2522 
Q 4582 2675 4515 2761 
Q 4448 2848 4448 2867 
Q 4448 2918 4547 2918 
Q 4646 2918 4777 2867 
Q 4909 2816 4944 2774 
Q 4979 2733 4979 2650 
L 4986 -474 
Q 4986 -640 4870 -640 
Q 4736 -640 4595 -474 
Q 4531 -403 4531 -358 
L 4531 -294 
Q 4531 -275 4556 -160 
Q 4582 -45 4582 122 
L 4582 2522 
z
M 3635 2234 
Q 3635 1446 3507 966 
Q 3341 262 2790 -179 
Q 2573 -358 2406 -441 
Q 2240 -525 2208 -525 
Q 2176 -525 2176 -483 
Q 2176 -442 2291 -320 
Q 2906 275 3091 928 
Q 3232 1408 3232 2182 
L 3232 2387 
Q 3232 2541 3181 2624 
Q 3130 2707 3130 2752 
Q 3130 2797 3213 2797 
Q 3469 2746 3552 2688 
Q 3635 2630 3635 2560 
L 3635 2234 
z
M 1146 4653 
L 2266 4736 
Q 2349 4736 2435 4672 
Q 2522 4608 2522 4550 
Q 2522 4493 2490 4457 
Q 2458 4422 2378 4265 
Q 2298 4109 2077 3725 
Q 1856 3341 1779 3225 
Q 1702 3110 1702 3084 
Q 1702 3059 1785 2931 
Q 1869 2803 1939 2688 
Q 2208 2157 2208 1600 
Q 2208 1293 2099 1069 
Q 2042 947 1920 947 
Q 1798 947 1664 1065 
Q 1530 1184 1398 1344 
Q 1267 1504 1184 1651 
Q 1101 1798 1101 1840 
Q 1101 1882 1155 1882 
Q 1210 1882 1325 1766 
Q 1542 1587 1651 1523 
Q 1760 1459 1773 1459 
Q 1818 1459 1818 1740 
Q 1818 2022 1731 2278 
Q 1645 2534 1533 2704 
Q 1421 2874 1414 2880 
Q 1338 2963 1338 3056 
Q 1338 3149 1411 3286 
Q 1485 3424 1693 3779 
Q 1901 4134 2003 4371 
L 1114 4314 
L 1056 -422 
Q 1056 -595 947 -595 
Q 819 -595 726 -505 
Q 634 -416 634 -371 
L 634 -307 
Q 634 -288 656 -189 
Q 678 -90 678 218 
L 730 4166 
Q 730 4467 678 4592 
Q 627 4717 627 4758 
Q 627 4800 704 4800 
Q 781 4800 1146 4653 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6bb5" d="M 4890 2714 
Q 4525 2771 4525 3136 
L 4525 3149 
L 4544 4320 
L 3680 4256 
L 3680 4128 
Q 3680 3738 3622 3446 
Q 3565 3155 3401 2908 
Q 3238 2662 3078 2537 
Q 2918 2413 2867 2413 
Q 2829 2413 2829 2461 
Q 2829 2509 2880 2579 
Q 3155 2995 3219 3353 
Q 3283 3712 3283 3865 
Q 3283 4019 3276 4240 
Q 3270 4461 3232 4553 
Q 3194 4646 3194 4688 
Q 3194 4730 3264 4730 
Q 3334 4730 3654 4595 
L 4602 4678 
Q 4634 4685 4659 4685 
Q 4685 4685 4710 4685 
Q 4838 4685 4902 4592 
Q 4966 4499 4966 4467 
L 4947 4365 
L 4902 3213 
L 4902 3194 
Q 4902 3130 4953 3107 
Q 5005 3085 5133 3085 
Q 5261 3085 5286 3091 
Q 5312 3098 5376 3107 
Q 5440 3117 5501 3139 
Q 5562 3162 5603 3162 
Q 5645 3162 5734 3104 
Q 5958 2950 5958 2842 
Q 5958 2765 5805 2752 
Q 5318 2656 4890 2714 
z
M 1056 704 
L 915 653 
Q 678 557 608 557 
Q 538 557 461 627 
Q 384 698 329 781 
Q 275 864 275 899 
Q 275 934 384 947 
Q 493 960 1056 1114 
L 1024 3898 
Q 1024 4019 969 4121 
Q 915 4224 915 4243 
Q 915 4301 1001 4301 
Q 1088 4301 1206 4256 
Q 1325 4211 1331 4205 
Q 1843 4422 2182 4691 
Q 2342 4819 2377 4908 
Q 2413 4998 2461 4998 
Q 2509 4998 2579 4931 
Q 2650 4864 2701 4777 
Q 2752 4691 2752 4633 
Q 2752 4576 2682 4525 
Q 2048 4115 1421 3910 
L 1427 3392 
L 2112 3443 
Q 2266 3456 2336 3488 
Q 2406 3520 2451 3520 
Q 2496 3520 2573 3465 
Q 2650 3411 2707 3340 
Q 2765 3270 2765 3226 
Q 2765 3142 2624 3130 
L 1427 3053 
L 1434 2483 
L 2112 2522 
Q 2246 2534 2320 2563 
Q 2394 2592 2442 2592 
Q 2490 2592 2566 2547 
Q 2765 2419 2765 2304 
Q 2765 2221 2624 2208 
L 1440 2131 
L 1446 1229 
Q 2074 1427 2771 1683 
Q 2867 1715 2940 1715 
Q 3014 1715 3014 1657 
Q 3014 1600 2880 1510 
Q 2426 1216 1446 851 
L 1459 -378 
Q 1459 -531 1338 -531 
Q 1235 -531 1123 -464 
Q 1011 -397 1011 -282 
L 1011 -250 
Q 1011 -230 1036 -96 
Q 1062 38 1062 224 
L 1056 704 
z
M 3232 2323 
L 5069 2413 
L 5139 2413 
Q 5267 2413 5340 2345 
Q 5414 2278 5414 2201 
Q 5414 2125 5139 1632 
Q 4864 1139 4422 710 
Q 5158 102 6067 -224 
Q 6195 -269 6195 -313 
Q 6195 -358 6124 -422 
Q 6054 -486 5971 -534 
Q 5888 -582 5859 -582 
Q 5830 -582 5673 -518 
Q 5517 -454 5274 -320 
Q 4666 0 4154 454 
Q 3443 -128 2701 -435 
Q 2400 -563 2301 -563 
Q 2202 -563 2202 -518 
Q 2202 -442 2387 -346 
Q 3206 96 3878 710 
Q 3462 1146 3308 1363 
Q 3155 1581 3155 1629 
Q 3155 1677 3228 1747 
Q 3302 1818 3363 1818 
Q 3424 1818 3494 1728 
Q 3776 1325 4147 960 
Q 4659 1498 4877 2054 
L 3475 1978 
L 3309 1971 
Q 3181 1971 3123 2016 
Q 2944 2182 2944 2323 
Q 2944 2355 2973 2355 
Q 3002 2355 3059 2339 
Q 3117 2323 3232 2323 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
     <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
    </g>
   </g>
  </g>
  <g id="text_12">
   <!-- 通道 F8 在不同实验阶段的心跳诱发电位 -->
   <g transform="translate(464.769687 16.21125) scale(0.12 -0.12)">
    <defs>
     <path id="LXGWWenKai-Regular-901a" d="M 2368 755 
Q 2400 979 2400 1210 
L 2419 3347 
Q 2419 3526 2374 3641 
Q 2330 3757 2330 3792 
Q 2330 3827 2394 3827 
Q 2458 3827 2765 3693 
L 3661 3744 
Q 3514 3866 3168 4090 
Q 3098 4134 3098 4173 
Q 3174 4371 3254 4371 
Q 3334 4371 3789 4013 
Q 4154 4243 4589 4576 
L 2816 4448 
Q 2771 4442 2733 4442 
L 2662 4442 
Q 2554 4442 2464 4515 
Q 2374 4589 2342 4685 
L 2310 4774 
Q 2310 4806 2362 4806 
Q 2374 4806 2387 4806 
L 2605 4781 
L 4845 4922 
L 4909 4922 
Q 5050 4922 5123 4838 
Q 5197 4755 5197 4694 
Q 5197 4634 5149 4598 
Q 5101 4563 4845 4358 
Q 4589 4154 4032 3821 
L 4096 3763 
L 5139 3821 
Q 5165 3827 5219 3827 
Q 5274 3827 5360 3766 
Q 5446 3706 5446 3622 
L 5414 3475 
L 5427 858 
L 5434 672 
Q 5434 550 5344 435 
Q 5254 320 5145 320 
Q 5037 320 4662 624 
Q 4288 928 4288 1018 
Q 4288 1056 4339 1056 
Q 4390 1056 4576 969 
Q 4762 883 5062 794 
L 5056 1754 
L 4045 1709 
L 4045 768 
Q 4045 627 3955 627 
Q 3942 627 3866 653 
Q 3661 736 3661 890 
Q 3693 1114 3693 1370 
L 3693 1690 
L 2758 1645 
L 2758 685 
Q 2758 621 2742 576 
Q 2726 531 2662 531 
L 2586 550 
Q 2368 608 2368 755 
z
M 1453 3718 
Q 1203 4070 768 4493 
Q 704 4550 704 4611 
Q 704 4672 787 4736 
Q 870 4800 902 4800 
Q 934 4800 1049 4710 
Q 1165 4621 1305 4483 
Q 1446 4346 1580 4202 
Q 1715 4058 1804 3942 
Q 1894 3827 1894 3772 
Q 1894 3718 1801 3632 
Q 1709 3546 1641 3546 
Q 1574 3546 1453 3718 
z
M 5050 3494 
L 4045 3437 
L 4045 2906 
L 5050 2957 
L 5050 3494 
z
M 3706 3418 
L 2758 3366 
L 2758 2848 
L 3699 2893 
L 3706 3418 
z
M 390 3072 
Q 544 3046 595 3046 
L 672 3046 
Q 698 3046 723 3053 
L 1677 3149 
L 1754 3155 
Q 1830 3155 1913 3081 
Q 1997 3008 1997 2950 
Q 1997 2893 1958 2854 
L 1882 2778 
Q 1824 2701 1725 2573 
Q 1626 2445 1456 2221 
Q 1286 1997 1286 1965 
Q 1286 1933 1318 1914 
Q 1830 1606 1920 1421 
Q 1965 1344 1965 1280 
Q 1965 1043 1408 582 
Q 1619 570 2147 464 
Q 2675 358 3900 169 
Q 5126 -19 5958 -19 
L 5990 -19 
Q 6131 -19 6131 -70 
Q 6131 -90 6093 -179 
Q 5984 -422 5811 -422 
L 5773 -422 
Q 4973 -397 4070 -250 
Q 2528 13 1990 128 
Q 1453 243 1165 243 
Q 877 243 544 128 
Q 480 109 441 109 
Q 403 109 339 147 
Q 275 186 275 371 
Q 275 474 454 512 
Q 634 550 909 576 
Q 1274 896 1536 1235 
Q 1549 1261 1549 1280 
Q 1549 1331 1318 1485 
Q 1114 1638 1075 1658 
Q 909 1766 909 1884 
Q 909 2003 1053 2217 
Q 1197 2432 1434 2771 
L 838 2694 
Q 749 2682 717 2682 
Q 685 2682 617 2694 
Q 550 2707 473 2771 
Q 397 2835 358 2989 
Q 352 3002 352 3027 
Q 352 3072 390 3072 
z
M 5056 2656 
L 4045 2598 
L 4045 2003 
L 5056 2042 
L 5056 2656 
z
M 3699 2586 
L 2758 2534 
L 2758 1946 
L 3699 1984 
L 3699 2586 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-9053" d="M 3942 3776 
Q 4026 3693 4026 3645 
Q 4026 3597 3997 3545 
Q 3968 3494 3629 3187 
L 4864 3258 
L 4928 3258 
Q 5107 3258 5197 3098 
Q 5229 3040 5229 3014 
Q 5229 2989 5216 2957 
Q 5203 2925 5197 2893 
L 5069 1011 
Q 5248 813 5248 752 
Q 5248 691 5197 681 
Q 5146 672 5062 666 
L 3200 608 
L 3206 525 
L 3206 499 
Q 3206 371 3110 371 
Q 2976 371 2893 441 
Q 2810 512 2810 589 
L 2810 634 
Q 2822 774 2822 941 
L 2822 1030 
L 2752 2790 
Q 2733 3021 2697 3101 
Q 2662 3181 2662 3216 
Q 2662 3251 2739 3251 
Q 2816 3251 3110 3155 
L 3219 3162 
Q 3590 3578 3648 3757 
L 2554 3693 
Q 2426 3680 2323 3680 
Q 2221 3680 2150 3763 
Q 2029 3917 2029 3968 
Q 2029 4019 2080 4019 
L 2112 4019 
Q 2221 4000 2323 4000 
L 3923 4096 
Q 4224 4429 4467 4845 
Q 4512 4915 4538 5082 
Q 4550 5152 4611 5152 
Q 4672 5152 4813 5018 
Q 4954 4896 4954 4825 
Q 4954 4755 4800 4569 
Q 4646 4384 4352 4122 
L 5133 4173 
Q 5325 4186 5401 4211 
Q 5478 4237 5526 4237 
Q 5574 4237 5651 4192 
Q 5728 4147 5785 4083 
Q 5843 4019 5843 3968 
Q 5843 3898 5690 3885 
L 3942 3776 
z
M 3264 4211 
Q 3104 4429 2790 4730 
Q 2746 4774 2746 4809 
Q 2746 4845 2797 4931 
Q 2848 5018 2909 5018 
Q 2970 5018 3082 4928 
Q 3194 4838 3325 4713 
Q 3456 4589 3549 4480 
Q 3642 4371 3642 4326 
Q 3642 4282 3558 4192 
Q 3475 4102 3411 4102 
Q 3347 4102 3264 4211 
z
M 1434 3712 
Q 1107 4160 749 4506 
Q 678 4576 678 4617 
Q 678 4659 729 4736 
Q 781 4813 841 4813 
Q 902 4813 1014 4720 
Q 1126 4627 1273 4489 
Q 1421 4352 1555 4205 
Q 1690 4058 1779 3946 
Q 1869 3834 1869 3776 
Q 1869 3718 1779 3635 
Q 1690 3552 1619 3552 
Q 1549 3552 1434 3712 
z
M 403 3072 
Q 557 3046 614 3046 
L 691 3046 
Q 717 3046 742 3053 
L 1811 3162 
Q 1862 3162 1958 3094 
Q 2054 3027 2054 2960 
Q 2054 2893 2010 2854 
L 1939 2778 
Q 1338 2016 1338 1958 
Q 1338 1933 1370 1914 
Q 1741 1677 1885 1536 
Q 2029 1395 2029 1267 
Q 2029 1139 1875 966 
Q 1722 794 1446 550 
Q 1645 538 2105 438 
Q 2566 339 3827 144 
Q 5088 -51 5971 -51 
L 6003 -51 
Q 6150 -51 6150 -109 
Q 6150 -115 6118 -205 
Q 6010 -454 5837 -454 
L 5786 -454 
Q 4410 -416 1805 134 
Q 1466 211 1155 211 
Q 845 211 518 96 
Q 454 77 416 77 
Q 378 77 314 115 
Q 250 154 250 269 
Q 250 384 288 422 
Q 371 506 934 550 
Q 1280 870 1594 1229 
Q 1606 1254 1606 1261 
Q 1606 1338 1126 1651 
Q 954 1766 954 1888 
Q 954 2010 1114 2246 
Q 1274 2483 1485 2784 
L 858 2707 
Q 819 2701 794 2701 
L 730 2701 
Q 570 2701 467 2822 
Q 365 2944 365 3008 
Q 365 3072 403 3072 
z
M 4800 2931 
L 3117 2842 
L 3130 2451 
L 4781 2528 
L 4800 2931 
z
M 4768 2221 
L 3142 2144 
L 3162 1734 
L 4749 1798 
L 4768 2221 
z
M 4736 1498 
L 3174 1427 
L 3187 954 
L 4717 998 
L 4736 1498 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-46" d="M 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4230 
Q 3565 4128 3482 4128 
L 3085 4128 
Q 2912 4128 2822 4122 
L 1126 4006 
L 1126 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1126 2202 
L 1126 685 
L 1146 6 
Q 1146 -58 1027 -58 
Q 909 -58 787 -10 
Q 666 38 666 122 
L 678 691 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-38" d="M 1811 2304 
Q 1338 1971 1078 1708 
Q 819 1446 819 1126 
Q 819 915 922 723 
Q 1152 294 1798 294 
Q 2317 294 2621 515 
Q 2925 736 2925 1104 
Q 2925 1472 2797 1670 
Q 2669 1869 2422 2009 
Q 2176 2150 1811 2304 
z
M 1773 2739 
Q 2355 3110 2989 3616 
Q 2976 3610 2912 3610 
Q 2848 3610 2790 3699 
Q 2496 4179 1939 4179 
Q 1530 4179 1280 3971 
Q 1030 3763 1030 3504 
Q 1030 3245 1212 3081 
Q 1395 2918 1773 2739 
z
M 3130 3731 
Q 3238 3840 3315 3840 
Q 3418 3840 3418 3738 
Q 3418 3552 2528 2848 
Q 2317 2682 2157 2566 
Q 2752 2342 3069 2006 
Q 3386 1670 3386 1123 
Q 3386 576 2963 233 
Q 2541 -109 1798 -109 
Q 1363 -109 1040 51 
Q 717 211 541 489 
Q 365 768 365 1107 
Q 365 1805 1421 2496 
Q 1011 2714 796 2944 
Q 582 3174 582 3472 
Q 582 3770 748 4013 
Q 915 4256 1219 4406 
Q 1523 4557 1878 4557 
Q 2234 4557 2464 4464 
Q 2694 4371 2848 4236 
Q 3002 4102 3078 3987 
Q 3155 3872 3155 3827 
Q 3155 3782 3130 3731 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5728" d="M 2874 5030 
Q 2874 5107 2867 5171 
Q 2861 5235 2912 5235 
Q 2938 5235 3040 5197 
Q 3334 5088 3334 4954 
Q 3334 4934 3232 4649 
Q 3130 4365 2944 3987 
L 4979 4115 
Q 5197 4134 5261 4153 
Q 5325 4173 5363 4173 
Q 5402 4173 5491 4122 
Q 5734 3994 5734 3872 
Q 5734 3789 5613 3776 
L 2752 3603 
Q 2394 2925 1914 2298 
L 1894 -416 
Q 1894 -582 1786 -582 
Q 1709 -582 1587 -515 
Q 1466 -448 1466 -333 
Q 1504 -26 1504 218 
L 1523 1837 
Q 1261 1542 947 1286 
Q 378 819 218 819 
Q 179 819 179 864 
Q 179 909 403 1101 
Q 627 1293 937 1635 
Q 1248 1978 1530 2362 
L 1536 2605 
Q 1536 2701 1485 2800 
Q 1434 2899 1434 2937 
Q 1434 2976 1485 2976 
Q 1536 2976 1587 2970 
L 1696 2944 
Q 1830 2931 1888 2874 
Q 2150 3290 2304 3578 
L 1376 3520 
Q 1261 3507 1158 3507 
Q 1056 3507 957 3555 
Q 858 3603 768 3827 
Q 762 3840 762 3866 
Q 762 3910 813 3910 
Q 973 3878 1146 3878 
L 1229 3878 
L 2496 3955 
Q 2874 4806 2874 5030 
z
M 5376 384 
Q 5446 390 5491 393 
Q 5536 397 5722 429 
Q 5830 429 6003 243 
Q 6074 173 6074 122 
Q 6074 32 5920 19 
L 2464 -115 
L 2355 -115 
Q 2157 -115 2099 -38 
Q 1946 141 1946 282 
Q 1946 314 1968 314 
Q 1990 314 2073 282 
Q 2157 250 2304 250 
L 2336 250 
L 3597 307 
L 3610 1728 
L 2835 1683 
L 2714 1677 
Q 2541 1677 2486 1737 
Q 2432 1798 2374 1904 
Q 2317 2010 2317 2054 
Q 2317 2099 2339 2099 
Q 2362 2099 2445 2067 
Q 2528 2035 2675 2035 
L 2707 2035 
L 3610 2099 
L 3616 3104 
Q 3616 3290 3539 3418 
Q 3526 3437 3526 3465 
Q 3526 3494 3596 3494 
Q 3667 3494 3808 3440 
Q 3949 3386 3984 3344 
Q 4019 3302 4019 3213 
L 4013 2131 
L 4730 2182 
Q 4800 2189 4877 2192 
Q 4954 2195 5002 2211 
Q 5050 2227 5088 2227 
Q 5126 2227 5206 2176 
Q 5286 2125 5353 2051 
Q 5421 1978 5421 1926 
Q 5421 1843 5274 1830 
L 4006 1754 
L 3994 320 
L 5376 384 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-4e0d" d="M 672 4454 
Q 864 4416 992 4416 
L 1050 4416 
L 5082 4634 
Q 5242 4646 5309 4668 
Q 5376 4691 5414 4691 
Q 5453 4691 5542 4634 
Q 5760 4499 5760 4378 
Q 5760 4301 5600 4282 
L 3942 4192 
Q 3661 3667 3302 3162 
L 3309 -422 
Q 3309 -589 3174 -589 
Q 3130 -589 3043 -553 
Q 2957 -518 2886 -444 
Q 2816 -371 2816 -300 
Q 2816 -230 2841 -115 
Q 2867 0 2867 173 
L 2867 2611 
Q 2170 1818 1312 1248 
Q 947 1005 694 877 
Q 442 749 374 749 
Q 307 749 307 794 
Q 307 858 429 941 
Q 1952 2016 2854 3283 
Q 2829 3373 2774 3440 
Q 2720 3507 2720 3526 
Q 2720 3571 2813 3571 
Q 2906 3571 3027 3533 
Q 3168 3744 3405 4166 
L 1184 4045 
Q 1146 4038 1114 4038 
L 1011 4038 
Q 941 4038 825 4089 
Q 710 4141 640 4378 
Q 634 4390 634 4410 
Q 634 4454 672 4454 
z
M 5491 1056 
Q 4550 1843 3731 2336 
Q 3635 2394 3635 2454 
Q 3635 2515 3696 2611 
Q 3757 2707 3821 2707 
Q 3885 2707 4160 2541 
Q 4896 2099 5754 1446 
Q 5856 1370 5856 1312 
Q 5856 1254 5830 1180 
Q 5805 1107 5753 1049 
Q 5702 992 5632 992 
Q 5562 992 5491 1056 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-540c" d="M 5421 -32 
L 5440 -262 
Q 5440 -390 5350 -483 
Q 5261 -576 5177 -576 
Q 5094 -576 4941 -499 
Q 4627 -358 4230 -73 
Q 3834 211 3834 320 
Q 3834 378 3894 378 
Q 3955 378 4147 294 
Q 4704 32 5030 -45 
L 5005 4378 
L 1427 4192 
L 1414 -403 
Q 1414 -576 1280 -576 
Q 1146 -576 1056 -486 
Q 966 -397 966 -288 
L 966 -243 
Q 966 -224 988 -118 
Q 1011 -13 1011 173 
L 1030 4173 
Q 1030 4435 976 4524 
Q 922 4614 922 4662 
Q 922 4710 1018 4710 
Q 1114 4710 1446 4557 
L 5088 4755 
L 5165 4755 
Q 5267 4755 5350 4681 
Q 5434 4608 5434 4518 
L 5402 4390 
L 5421 -32 
z
M 2432 3187 
L 2240 3181 
Q 2118 3181 2080 3213 
Q 1869 3386 1869 3533 
Q 1869 3571 1901 3571 
Q 1933 3571 2003 3555 
Q 2074 3539 2214 3539 
L 2298 3539 
L 3872 3616 
Q 4019 3629 4105 3657 
Q 4192 3686 4230 3686 
Q 4269 3686 4352 3629 
Q 4557 3482 4557 3386 
Q 4557 3309 4429 3296 
L 2432 3187 
z
M 2189 2170 
Q 2170 2406 2118 2496 
Q 2067 2586 2067 2621 
Q 2067 2656 2160 2656 
Q 2253 2656 2573 2541 
L 3955 2624 
L 4019 2624 
Q 4141 2624 4230 2547 
Q 4320 2470 4320 2412 
Q 4320 2355 4301 2326 
Q 4282 2298 4275 2266 
L 4166 1408 
Q 4339 1210 4339 1146 
Q 4339 1082 4284 1069 
Q 4230 1056 4147 1050 
L 2675 992 
L 2682 909 
L 2682 890 
Q 2682 736 2560 736 
L 2483 755 
Q 2406 768 2329 825 
Q 2253 883 2253 992 
L 2253 1024 
Q 2266 1139 2266 1248 
L 2259 1382 
L 2189 2170 
z
M 3866 2266 
L 2586 2195 
L 2650 1338 
L 3789 1382 
L 3866 2266 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5b9e" d="M 1344 3661 
Q 1190 3213 979 2816 
Q 947 2739 876 2739 
Q 806 2739 694 2812 
Q 582 2886 582 2947 
Q 582 3008 681 3168 
Q 781 3328 928 3680 
Q 1075 4032 1178 4371 
Q 1210 4480 1293 4480 
Q 1350 4480 1449 4445 
Q 1549 4410 1549 4346 
Q 1549 4282 1466 4026 
L 2970 4115 
L 2970 4755 
Q 2970 4877 2902 4973 
Q 2835 5069 2835 5094 
Q 2835 5152 2918 5152 
Q 3002 5152 3117 5126 
Q 3430 5043 3430 4915 
L 3424 4147 
L 5510 4269 
L 5555 4269 
Q 5683 4269 5788 4198 
Q 5894 4128 5894 4057 
Q 5894 3987 5849 3945 
Q 5805 3904 5728 3782 
Q 5651 3661 5372 3299 
Q 5094 2938 4973 2938 
Q 4922 2938 4922 3005 
Q 4922 3072 5056 3302 
Q 5190 3533 5312 3885 
L 1344 3661 
z
M 1248 2195 
Q 1165 2246 1165 2300 
Q 1165 2355 1213 2435 
Q 1261 2515 1318 2515 
Q 1376 2515 1709 2364 
Q 2042 2214 2400 1984 
Q 2534 1907 2534 1833 
Q 2534 1760 2460 1670 
Q 2387 1581 2339 1581 
Q 2291 1581 1996 1779 
Q 1702 1978 1248 2195 
z
M 1747 2982 
Q 1664 3034 1664 3088 
Q 1664 3142 1712 3222 
Q 1760 3302 1817 3302 
Q 1875 3302 2188 3161 
Q 2502 3021 2899 2771 
Q 3027 2694 3027 2617 
Q 3027 2541 2953 2454 
Q 2880 2368 2835 2368 
Q 2790 2368 2480 2576 
Q 2170 2784 1747 2982 
z
M 5165 -538 
Q 4813 -294 4438 -54 
Q 4064 186 3584 390 
Q 3520 429 3520 467 
Q 3520 506 3546 557 
Q 3616 710 3718 710 
Q 3770 710 4058 582 
Q 4672 307 5466 -224 
Q 5549 -282 5549 -355 
Q 5549 -429 5462 -518 
Q 5376 -608 5305 -608 
Q 5235 -608 5165 -538 
z
M 2848 883 
L 934 800 
Q 858 800 739 848 
Q 621 896 538 1139 
Q 525 1178 525 1197 
Q 525 1216 550 1216 
Q 576 1216 646 1190 
Q 717 1165 890 1165 
L 954 1165 
L 3066 1261 
Q 3251 1683 3354 2445 
Q 3373 2618 3373 2886 
L 3373 2989 
Q 3373 3187 3264 3328 
Q 3245 3354 3245 3392 
Q 3245 3462 3318 3462 
Q 3392 3462 3507 3443 
Q 3622 3424 3724 3379 
Q 3827 3334 3827 3200 
Q 3814 2675 3724 2128 
Q 3635 1581 3520 1280 
L 5139 1357 
Q 5286 1370 5356 1398 
Q 5427 1427 5456 1427 
Q 5485 1427 5568 1376 
Q 5805 1229 5805 1101 
Q 5805 1024 5658 1005 
L 3334 902 
Q 2963 282 2150 -141 
Q 1510 -474 838 -621 
Q 608 -672 595 -672 
Q 538 -672 538 -608 
Q 538 -518 691 -461 
Q 1530 -179 2170 256 
Q 2592 550 2848 883 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-9a8c" d="M 5549 1760 
Q 5222 851 4576 179 
L 5536 218 
Q 5626 218 5696 243 
Q 5766 269 5817 269 
Q 5869 269 5997 150 
Q 6125 32 6125 -48 
Q 6125 -128 5971 -141 
L 3194 -243 
Q 3085 -256 3005 -256 
Q 2925 -256 2854 -173 
Q 2784 -90 2749 -3 
Q 2714 83 2714 121 
Q 2714 160 2765 160 
Q 2816 160 2880 141 
Q 2944 122 3040 122 
L 3085 122 
L 4282 166 
Q 4832 941 5107 1734 
Q 5146 1824 5146 1894 
Q 5146 1965 5136 2025 
Q 5126 2086 5190 2106 
Q 5338 2138 5504 1952 
Q 5581 1862 5568 1827 
Q 5555 1792 5549 1760 
z
M 3629 480 
L 3578 480 
Q 3501 499 3462 608 
Q 3328 1094 3078 1606 
Q 3066 1658 3066 1696 
Q 3066 1734 3162 1798 
Q 3258 1862 3309 1846 
Q 3360 1830 3433 1712 
Q 3507 1594 3587 1421 
Q 3667 1248 3724 1110 
Q 3782 973 3827 838 
Q 3872 704 3869 662 
Q 3866 621 3770 550 
Q 3674 480 3629 480 
z
M 4582 1082 
Q 4563 1037 4454 979 
Q 4250 870 4192 1062 
Q 4058 1709 3885 2086 
Q 3866 2144 3872 2169 
Q 3878 2195 3955 2259 
Q 4032 2323 4115 2291 
Q 4230 2246 4416 1715 
Q 4602 1184 4582 1082 
z
M 3776 2630 
L 3642 2624 
Q 3558 2624 3488 2669 
Q 3418 2714 3354 2931 
Q 3354 3002 3443 2989 
L 3648 2963 
L 3699 2963 
L 4653 3027 
Q 4768 3046 4797 3062 
Q 4826 3078 4880 3078 
Q 4934 3078 5011 3027 
Q 5178 2918 5178 2803 
Q 5178 2720 5005 2707 
L 3776 2630 
z
M 934 4320 
Q 685 4320 602 4646 
L 602 4678 
Q 602 4717 650 4717 
Q 698 4717 762 4701 
Q 826 4685 954 4685 
L 998 4685 
L 2336 4794 
Q 2432 4794 2521 4726 
Q 2611 4659 2608 4614 
Q 2605 4570 2592 4538 
Q 2579 4506 2573 4474 
L 2208 2214 
L 2490 2227 
L 2541 2227 
Q 2650 2227 2730 2156 
Q 2810 2086 2810 2028 
Q 2810 1971 2797 1932 
Q 2784 1894 2778 1862 
Q 2682 435 2483 -224 
Q 2445 -346 2342 -419 
Q 2240 -493 2163 -493 
Q 2010 -493 1670 -166 
Q 1318 147 1318 237 
Q 1318 282 1388 282 
Q 1459 282 1664 163 
Q 1869 45 1993 3 
Q 2118 -38 2124 -38 
Q 2131 -38 2131 0 
Q 2323 685 2387 1907 
L 1082 1805 
Q 1024 1805 985 1785 
Q 947 1766 880 1772 
Q 813 1779 723 1856 
Q 634 1933 646 2016 
Q 646 2029 652 2048 
Q 659 2067 691 2172 
Q 723 2278 771 2585 
Q 819 2893 864 3165 
Q 909 3437 928 3578 
L 934 3642 
Q 947 3725 947 3802 
L 915 4000 
Q 922 4051 998 4045 
L 1005 4045 
L 1005 4038 
L 1024 4038 
Q 1370 3878 1357 3706 
L 1357 3674 
Q 1299 3392 1228 2941 
Q 1158 2490 1088 2163 
L 1101 2157 
L 1786 2189 
L 2144 4416 
L 1050 4326 
Q 1018 4320 986 4320 
L 934 4320 
z
M 4422 4576 
Q 4979 3898 5549 3411 
Q 5850 3155 6195 2893 
Q 6266 2835 6266 2803 
Q 6266 2771 6163 2646 
Q 6061 2522 5977 2522 
Q 5894 2522 5366 3030 
Q 4838 3539 4237 4262 
Q 3949 3770 3533 3270 
L 3462 3194 
Q 3027 2752 2650 2483 
Q 2534 2406 2480 2406 
Q 2426 2406 2426 2460 
Q 2426 2515 2528 2618 
Q 3181 3277 3622 4019 
Q 4064 4762 4064 4986 
L 4064 5024 
L 4058 5069 
Q 4058 5146 4115 5146 
Q 4147 5146 4249 5094 
Q 4352 5043 4441 4969 
Q 4531 4896 4531 4845 
Q 4531 4794 4422 4576 
z
M 403 960 
Q 730 960 1978 1568 
Q 2054 1613 2108 1613 
Q 2163 1613 2163 1562 
Q 2163 1478 1820 1216 
Q 1478 954 1072 720 
Q 666 486 589 486 
Q 512 486 461 525 
Q 294 685 243 877 
Q 237 890 237 928 
Q 237 966 269 966 
L 275 966 
Q 307 960 339 960 
L 403 960 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-7684" d="M 3846 4890 
L 3834 4992 
Q 3834 5069 3898 5069 
Q 3917 5069 4019 5024 
Q 4307 4896 4307 4762 
Q 4307 4589 3898 3738 
L 5504 3834 
L 5530 3834 
Q 5613 3834 5696 3773 
Q 5779 3712 5779 3616 
L 5760 3469 
Q 5709 1747 5530 301 
Q 5427 -544 5082 -544 
Q 4934 -544 4841 -483 
Q 4749 -422 4605 -313 
Q 4461 -205 4301 -70 
Q 4141 64 4025 179 
Q 3910 294 3910 345 
Q 3910 397 3984 397 
Q 4058 397 4282 262 
Q 4506 128 4947 -45 
L 4960 -45 
Q 4998 -45 5018 6 
Q 5299 1107 5338 3469 
L 3731 3366 
Q 3450 2880 3232 2595 
Q 3014 2310 2944 2310 
Q 2912 2310 2912 2371 
Q 2912 2432 3104 2787 
Q 3296 3142 3571 3875 
Q 3846 4608 3846 4845 
L 3846 4890 
z
M 1715 4749 
Q 1715 4768 1702 4845 
Q 1702 4922 1779 4922 
Q 1920 4922 2035 4832 
Q 2150 4742 2150 4665 
Q 2150 4589 1920 4160 
Q 1715 3802 1581 3603 
L 2483 3654 
L 2560 3654 
Q 2746 3654 2803 3507 
Q 2822 3462 2822 3456 
L 2790 3328 
L 2688 480 
Q 2842 282 2842 214 
Q 2842 147 2790 134 
Q 2739 122 2656 115 
L 1229 64 
L 1235 -147 
L 1235 -166 
Q 1235 -307 1126 -307 
Q 979 -307 892 -233 
Q 806 -160 806 -77 
L 851 243 
L 794 3232 
Q 794 3418 717 3539 
Q 672 3629 672 3664 
Q 672 3699 768 3699 
Q 864 3699 1165 3584 
L 1222 3584 
Q 1715 4512 1715 4749 
z
M 2400 3302 
L 1171 3238 
L 1190 2061 
L 2368 2118 
L 2400 3302 
z
M 4755 1446 
Q 4755 1318 4589 1229 
Q 4525 1190 4483 1190 
Q 4442 1190 4358 1299 
Q 4160 1619 3913 1913 
Q 3667 2208 3564 2307 
Q 3462 2406 3462 2464 
Q 3462 2522 3532 2589 
Q 3603 2656 3657 2656 
Q 3712 2656 3869 2505 
Q 4026 2355 4390 1939 
Q 4755 1523 4755 1446 
z
M 2355 1773 
L 1197 1722 
L 1222 422 
L 2317 461 
L 2355 1773 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-8df3" d="M 4755 2931 
Q 5370 3597 5370 3859 
Q 5370 3955 5424 3955 
Q 5478 3955 5555 3891 
Q 5760 3712 5760 3571 
Q 5760 3533 5728 3488 
Q 5190 2899 4755 2637 
L 4755 2298 
Q 5734 1722 5811 1562 
Q 5830 1517 5830 1469 
Q 5830 1421 5756 1328 
Q 5683 1235 5632 1235 
Q 5581 1235 5357 1436 
Q 5133 1638 4749 1907 
L 4736 454 
Q 4736 320 4762 237 
Q 4826 70 5280 70 
Q 5734 70 5830 179 
Q 5965 333 6054 1069 
Q 6080 1325 6163 1325 
Q 6246 1325 6252 1101 
Q 6259 877 6259 643 
Q 6259 410 6240 198 
Q 6221 -13 6134 -128 
Q 6048 -243 5849 -284 
Q 5651 -326 5296 -326 
Q 4941 -326 4733 -268 
Q 4525 -211 4435 -54 
Q 4346 102 4346 384 
L 4378 4672 
Q 4378 4838 4330 4905 
Q 4282 4973 4282 5011 
Q 4282 5050 4349 5050 
Q 4416 5050 4518 5030 
Q 4774 4979 4774 4870 
L 4755 2931 
z
M 3974 4710 
Q 3987 4301 3987 3802 
Q 3987 1971 3763 1274 
Q 3488 416 2854 -90 
Q 2598 -294 2406 -387 
Q 2214 -480 2169 -480 
Q 2125 -480 2125 -429 
Q 2125 -378 2403 -144 
Q 2682 90 2931 416 
Q 3373 1005 3514 1933 
Q 2816 1325 2675 1325 
Q 2630 1325 2540 1373 
Q 2451 1421 2380 1485 
Q 2310 1549 2310 1587 
Q 2310 1626 2368 1645 
Q 2426 1664 2560 1715 
Q 2893 1843 3565 2349 
Q 3616 2778 3616 3690 
Q 3616 4602 3520 4749 
Q 3482 4806 3482 4832 
Q 3482 4883 3552 4883 
Q 3622 4883 3712 4870 
Q 3974 4832 3974 4710 
z
M 768 2950 
L 787 3110 
L 787 3302 
Q 787 3341 781 3379 
L 710 4237 
Q 698 4410 602 4550 
Q 589 4570 589 4582 
Q 589 4621 672 4621 
Q 755 4621 1082 4525 
L 2227 4614 
L 2272 4614 
Q 2381 4614 2470 4547 
Q 2560 4480 2560 4429 
Q 2560 4378 2541 4352 
Q 2522 4326 2515 4275 
L 2394 3328 
Q 2554 3181 2554 3113 
Q 2554 3046 2512 3030 
Q 2470 3014 2406 3008 
L 1850 2976 
L 1843 2189 
L 1920 2195 
Q 2080 2208 2179 2233 
Q 2278 2259 2336 2259 
Q 2394 2259 2467 2208 
Q 2541 2157 2589 2089 
Q 2637 2022 2637 1978 
Q 2637 1907 2528 1894 
L 1837 1856 
L 1824 608 
Q 2106 698 2371 790 
Q 2637 883 2704 883 
Q 2771 883 2771 845 
Q 2771 768 2310 531 
Q 1850 294 1283 64 
Q 717 -166 608 -166 
Q 499 -166 390 -67 
Q 282 32 237 112 
Q 192 192 192 211 
Q 192 256 320 256 
Q 448 256 755 307 
L 704 2061 
Q 704 2195 656 2284 
Q 608 2374 608 2412 
Q 608 2451 707 2451 
Q 806 2451 986 2394 
Q 1088 2355 1088 2272 
L 1114 397 
Q 1267 435 1446 493 
L 1466 2950 
L 1178 2931 
L 1178 2854 
Q 1178 2720 1082 2720 
Q 1037 2720 902 2781 
Q 768 2842 768 2950 
z
M 2131 4282 
L 1082 4211 
L 1152 3245 
L 2048 3309 
L 2131 4282 
z
M 3174 2656 
Q 2944 3155 2669 3469 
Q 2611 3539 2611 3596 
Q 2611 3654 2688 3702 
Q 2765 3750 2819 3750 
Q 2874 3750 2966 3654 
Q 3059 3558 3161 3417 
Q 3264 3277 3353 3129 
Q 3443 2982 3500 2876 
Q 3558 2771 3558 2729 
Q 3558 2688 3465 2617 
Q 3373 2547 3299 2547 
Q 3226 2547 3174 2656 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-8bf1" d="M 1472 2586 
L 806 2515 
Q 768 2509 742 2509 
L 678 2509 
Q 518 2509 416 2630 
Q 314 2752 314 2816 
Q 314 2880 352 2880 
Q 506 2854 563 2854 
L 640 2854 
Q 666 2854 691 2861 
L 1677 2976 
Q 1728 2976 1824 2909 
Q 1920 2842 1920 2774 
Q 1920 2707 1875 2669 
L 1837 2630 
L 1760 550 
Q 1869 627 2192 883 
Q 2515 1139 2585 1142 
Q 2656 1146 2659 1107 
Q 2662 1069 2556 947 
Q 2451 826 2124 445 
Q 1798 64 1651 -29 
Q 1504 -122 1437 -131 
Q 1370 -141 1267 -70 
Q 1165 0 1085 86 
Q 1005 173 998 211 
Q 998 243 1113 265 
Q 1229 288 1395 358 
L 1472 2586 
z
M 4288 2336 
Q 4288 2195 4211 2195 
Q 4173 2195 4096 2246 
Q 4019 2298 3974 2342 
Q 3930 2387 3930 2435 
Q 3930 2483 3952 2579 
Q 3974 2675 3974 2912 
L 3974 3027 
Q 3974 3098 3984 3187 
Q 3994 3277 3949 3277 
Q 3814 3002 3350 2659 
Q 2886 2317 2614 2185 
Q 2342 2054 2285 2054 
Q 2253 2054 2253 2108 
Q 2253 2163 2362 2246 
Q 3098 2746 3654 3373 
L 2963 3334 
L 2886 3334 
Q 2746 3334 2650 3382 
Q 2554 3430 2496 3622 
Q 2490 3642 2490 3661 
L 2490 3699 
Q 2496 3699 2573 3680 
Q 2650 3661 2829 3661 
L 2944 3661 
L 3968 3712 
L 3968 4262 
Q 2982 4064 2844 4064 
Q 2707 4064 2707 4122 
Q 2707 4198 2918 4250 
Q 3974 4518 4883 4922 
Q 4986 4960 5043 5046 
Q 5101 5133 5129 5133 
Q 5158 5133 5222 5046 
Q 5286 4960 5331 4857 
Q 5376 4755 5376 4707 
Q 5376 4659 5318 4646 
Q 4685 4429 4275 4333 
L 4282 3731 
L 5229 3782 
Q 5440 3802 5526 3827 
Q 5613 3853 5645 3853 
Q 5677 3853 5741 3821 
Q 5933 3725 5933 3613 
Q 5933 3501 5824 3494 
L 4518 3424 
Q 5267 2867 6208 2445 
Q 6266 2419 6266 2384 
Q 6266 2349 6227 2298 
Q 6074 2131 6019 2131 
Q 5965 2131 5741 2253 
Q 5126 2586 4486 3072 
Q 4422 3142 4301 3283 
Q 4250 3283 4266 3187 
Q 4282 3091 4282 3008 
Q 4282 2925 4285 2758 
Q 4288 2592 4294 2464 
Q 4301 2336 4288 2336 
z
M 2784 2112 
Q 2963 2080 3194 2080 
L 3290 2080 
L 4704 2170 
Q 4736 2176 4749 2176 
L 4794 2176 
Q 4832 2176 4931 2109 
Q 5030 2042 5030 1955 
Q 5030 1869 4992 1827 
Q 4954 1786 4934 1754 
L 4659 1197 
L 5376 1242 
L 5427 1242 
Q 5498 1242 5603 1174 
Q 5709 1107 5709 1033 
Q 5709 960 5673 905 
Q 5638 851 5626 794 
Q 5459 32 5299 -285 
Q 5139 -602 4902 -602 
Q 4749 -602 4374 -323 
Q 4000 -45 4000 70 
Q 4000 109 4041 109 
Q 4083 109 4345 0 
Q 4608 -109 4883 -166 
L 4909 -166 
Q 4954 -166 4986 -128 
Q 5203 371 5293 877 
L 4768 851 
Q 4672 845 4611 825 
Q 4550 806 4502 806 
Q 4454 806 4400 851 
Q 4346 896 4307 957 
Q 4269 1018 4269 1050 
Q 4269 1082 4294 1133 
L 4608 1824 
L 3853 1779 
Q 3680 979 3296 448 
Q 2912 -83 2272 -448 
Q 2182 -499 2138 -499 
Q 2118 -499 2118 -438 
Q 2118 -378 2246 -269 
Q 2726 122 3027 595 
Q 3328 1069 3482 1747 
L 3290 1741 
Q 3245 1734 3206 1734 
L 3104 1734 
Q 2982 1747 2944 1773 
Q 2835 1869 2771 2035 
Q 2758 2054 2758 2083 
Q 2758 2112 2771 2112 
L 2784 2112 
z
M 1811 3654 
Q 1485 4102 1126 4448 
Q 1056 4518 1056 4560 
Q 1056 4602 1107 4678 
Q 1158 4755 1219 4755 
Q 1280 4755 1392 4662 
Q 1504 4570 1651 4432 
Q 1798 4294 1932 4147 
Q 2067 4000 2156 3888 
Q 2246 3776 2246 3718 
Q 2246 3661 2156 3577 
Q 2067 3494 1996 3494 
Q 1926 3494 1811 3654 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-53d1" d="M 3482 563 
Q 2829 96 2048 -205 
Q 1267 -506 992 -506 
Q 890 -506 890 -448 
Q 890 -365 1178 -262 
Q 1466 -160 1837 13 
Q 2656 390 3174 794 
Q 2720 1190 2170 1843 
Q 1594 941 883 346 
Q 595 109 524 109 
Q 454 109 454 163 
Q 454 218 672 432 
Q 890 646 1184 1024 
Q 1798 1824 2093 2432 
Q 2246 2758 2394 3162 
L 1421 3104 
Q 1350 3098 1305 3072 
Q 1261 3046 1193 3046 
Q 1126 3046 1020 3107 
Q 915 3168 915 3232 
Q 915 3296 928 3347 
Q 1549 4378 1587 4538 
L 1606 4755 
L 1581 4954 
Q 1587 4998 1644 4998 
Q 1702 4998 1878 4896 
Q 2054 4794 2054 4672 
Q 2054 4550 1421 3462 
L 2522 3533 
Q 2816 4570 2816 4758 
Q 2816 4947 2790 5030 
Q 2765 5114 2765 5133 
Q 2765 5197 2841 5197 
Q 2918 5197 3027 5165 
Q 3315 5062 3315 4909 
Q 3181 4218 2963 3558 
L 5050 3686 
Q 5184 3693 5254 3718 
Q 5325 3744 5379 3744 
Q 5434 3744 5520 3680 
Q 5606 3616 5664 3542 
Q 5722 3469 5722 3437 
Q 5715 3366 5562 3354 
L 2842 3194 
Q 2739 2925 2496 2432 
L 4589 2541 
Q 4736 2541 4848 2489 
Q 4960 2438 4960 2355 
L 4960 2298 
Q 4960 2246 4902 2192 
Q 4845 2138 4723 1942 
Q 4602 1747 4333 1420 
Q 4064 1094 3776 819 
Q 4819 160 6010 -141 
Q 6099 -173 6099 -259 
Q 6099 -346 5888 -486 
Q 5811 -538 5734 -538 
Q 5658 -538 4966 -240 
Q 4275 58 3482 563 
z
M 4634 3949 
Q 4358 4352 3981 4691 
Q 3923 4742 3923 4777 
Q 3923 4813 3923 4848 
Q 3923 4883 4012 4944 
Q 4102 5005 4134 5005 
Q 4166 5005 4275 4928 
Q 4384 4851 4512 4729 
Q 4640 4608 4761 4480 
Q 4883 4352 4963 4246 
Q 5043 4141 5043 4102 
Q 5043 4064 4998 4006 
Q 4890 3846 4800 3846 
Q 4710 3846 4634 3949 
z
M 3462 1043 
Q 4013 1562 4371 2176 
L 2451 2080 
Q 2925 1466 3462 1043 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-7535" d="M 992 3482 
Q 979 3699 924 3801 
Q 870 3904 870 3949 
Q 870 3994 966 3994 
Q 1062 3994 1395 3878 
L 2803 3949 
L 2810 4870 
Q 2810 5030 2733 5152 
Q 2720 5178 2720 5197 
Q 2720 5235 2768 5235 
Q 2816 5235 2925 5216 
Q 3226 5171 3226 5011 
L 3219 3968 
L 4774 4045 
L 4832 4045 
Q 4992 4045 5081 3952 
Q 5171 3859 5171 3792 
Q 5171 3725 5152 3686 
Q 5133 3648 5126 3610 
L 4915 1600 
Q 5094 1446 5094 1344 
Q 5094 1267 4922 1254 
L 3206 1184 
L 3200 448 
Q 3200 256 3280 176 
Q 3360 96 3574 77 
Q 3789 58 4413 58 
Q 5037 58 5331 96 
Q 5478 115 5549 211 
Q 5690 378 5805 1242 
Q 5843 1536 5914 1536 
Q 6010 1536 6010 1037 
Q 6010 538 5971 266 
Q 5933 -6 5814 -140 
Q 5696 -275 5443 -320 
Q 5190 -365 4464 -365 
Q 3738 -365 3478 -349 
Q 3219 -333 3085 -269 
Q 2784 -128 2784 326 
L 2790 1171 
L 1530 1120 
L 1542 954 
L 1542 941 
Q 1542 838 1411 838 
Q 1280 838 1193 915 
Q 1107 992 1107 1075 
L 1107 1126 
Q 1114 1190 1114 1242 
L 1114 1440 
Q 1114 1491 1107 1549 
L 992 3482 
z
M 4531 1587 
L 4602 2502 
L 3213 2438 
L 3206 1536 
L 4531 1587 
z
M 4634 2854 
L 4704 3686 
L 3219 3622 
L 3213 2784 
L 4634 2854 
z
M 2790 1517 
L 2797 2419 
L 1459 2355 
L 1510 1472 
L 2790 1517 
z
M 2797 2765 
L 2803 3603 
L 1395 3533 
L 1440 2694 
L 2797 2765 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-4f4d" d="M 1907 4864 
L 1907 4954 
Q 1907 4979 1888 5056 
Q 1888 5107 1955 5107 
Q 2022 5107 2128 5056 
Q 2234 5005 2314 4934 
Q 2394 4864 2394 4806 
Q 2394 4749 2173 4275 
Q 1952 3802 1670 3360 
L 1670 -493 
Q 1670 -621 1536 -621 
Q 1402 -621 1309 -521 
Q 1216 -422 1216 -364 
Q 1216 -307 1235 -220 
Q 1254 -134 1254 77 
L 1280 2810 
Q 1056 2522 806 2266 
Q 352 1779 237 1779 
Q 186 1779 186 1843 
Q 186 1907 269 1997 
Q 992 2893 1472 3814 
Q 1683 4218 1795 4506 
Q 1907 4794 1907 4864 
z
M 2643 3181 
L 2451 3174 
Q 2330 3174 2291 3206 
Q 2080 3392 2080 3546 
Q 2080 3584 2112 3584 
Q 2144 3584 2214 3568 
Q 2285 3552 2426 3552 
L 2509 3552 
L 3699 3616 
L 3680 4627 
Q 3680 4768 3565 4941 
Q 3546 4973 3546 4992 
Q 3546 5043 3648 5043 
Q 3750 5043 3894 4995 
Q 4038 4947 4076 4905 
Q 4115 4864 4115 4800 
L 4109 3635 
L 5133 3686 
Q 5280 3699 5366 3728 
Q 5453 3757 5475 3757 
Q 5498 3757 5587 3706 
Q 5837 3565 5837 3443 
Q 5837 3366 5690 3347 
L 2643 3181 
z
M 2336 -173 
L 2240 -173 
Q 2086 -173 2029 -128 
Q 1901 -19 1859 93 
Q 1818 205 1818 230 
Q 1818 256 1856 256 
Q 1894 256 1990 230 
Q 2086 205 2240 205 
L 3955 256 
Q 4346 1331 4531 2470 
Q 4570 2682 4570 2742 
Q 4570 2803 4531 2883 
Q 4493 2963 4493 2989 
Q 4493 3046 4582 3046 
L 4659 3034 
Q 4845 3014 4957 2947 
Q 5069 2880 5069 2822 
Q 5069 2765 4989 2416 
Q 4909 2067 4723 1436 
Q 4538 806 4314 262 
L 5517 301 
Q 5658 301 5750 329 
Q 5843 358 5875 358 
Q 5907 358 5990 307 
Q 6227 179 6227 45 
Q 6227 -58 6061 -70 
L 2336 -173 
z
M 3674 960 
Q 3712 781 3712 713 
Q 3712 646 3638 601 
Q 3565 557 3494 550 
L 3418 538 
Q 3302 538 3270 710 
Q 3072 1798 2778 2611 
Q 2752 2669 2752 2736 
Q 2752 2803 2857 2851 
Q 2963 2899 3008 2899 
Q 3091 2899 3149 2752 
Q 3526 1728 3674 960 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#LXGWWenKai-Regular-901a"/>
    <use xlink:href="#LXGWWenKai-Regular-9053" x="99.999985"/>
    <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
    <use xlink:href="#LXGWWenKai-Regular-46" x="234.999954"/>
    <use xlink:href="#LXGWWenKai-Regular-38" x="293.199951"/>
    <use xlink:href="#LXGWWenKai-Regular-20" x="353.199936"/>
    <use xlink:href="#LXGWWenKai-Regular-5728" x="388.199921"/>
    <use xlink:href="#LXGWWenKai-Regular-4e0d" x="488.199905"/>
    <use xlink:href="#LXGWWenKai-Regular-540c" x="588.19989"/>
    <use xlink:href="#LXGWWenKai-Regular-5b9e" x="688.199875"/>
    <use xlink:href="#LXGWWenKai-Regular-9a8c" x="788.19986"/>
    <use xlink:href="#LXGWWenKai-Regular-9636" x="888.199844"/>
    <use xlink:href="#LXGWWenKai-Regular-6bb5" x="988.199829"/>
    <use xlink:href="#LXGWWenKai-Regular-7684" x="1088.199814"/>
    <use xlink:href="#LXGWWenKai-Regular-5fc3" x="1188.199799"/>
    <use xlink:href="#LXGWWenKai-Regular-8df3" x="1288.199783"/>
    <use xlink:href="#LXGWWenKai-Regular-8bf1" x="1388.199768"/>
    <use xlink:href="#LXGWWenKai-Regular-53d1" x="1488.199753"/>
    <use xlink:href="#LXGWWenKai-Regular-7535" x="1588.199738"/>
    <use xlink:href="#LXGWWenKai-Regular-4f4d" x="1688.199722"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pf1b58a0183">
   <rect x="47.059375" y="70.64625" width="264.450625" height="231.020625"/>
  </clipPath>
 </defs>
</svg>

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心非线性交互作用分析结果汇总脚本

该脚本用于汇总和分析CCM分析的结果，生成摘要统计和可视化。

主要功能：
1. 加载CCM分析结果
2. 计算不同频段、通道、实验阶段的汇总统计
3. 生成汇总报告
4. 创建高级可视化

作者：AI助手
日期：2024年
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging

# 设置路径
BASE_DIR = "D:/ecgeeg/30-数据分析/5-NeuroKit2"
RESULTS_DIR = os.path.join(BASE_DIR, "result")
OUTPUT_DIR = os.path.join(RESULTS_DIR, "nonlinear_interaction", "summary")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("CCM_Summary")

# 定义结果路径
RESULT_PATHS = {
    'ccm': os.path.join(RESULTS_DIR, 'nonlinear_interaction'),
}

def load_results():
    """加载分析结果"""
    logger.info("加载CCM分析结果...")
    
    # 加载CCM结果
    ccm_file = os.path.join(RESULT_PATHS['ccm'], 'ccm_results_all_subjects.csv')
    if not os.path.exists(ccm_file):
        logger.error(f"CCM结果文件不存在: {ccm_file}")
        return None
    
    ccm_results = pd.read_csv(ccm_file)
    logger.info(f"已加载 {len(ccm_results)} 条CCM结果")
    
    return ccm_results

def compute_summary_statistics(results):
    """计算汇总统计"""
    logger.info("计算汇总统计...")
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 1. 按频段分组
    band_summary = results.groupby('frequency_band')[
        ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']
    ].agg(['mean', 'std', 'count']).reset_index()
    
    # 2. 按实验阶段分组
    stage_summary = results.groupby('stage')[
        ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']
    ].agg(['mean', 'std', 'count']).reset_index()
    
    # 3. 按通道分组
    channel_summary = results.groupby('channel')[
        ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']
    ].agg(['mean', 'std', 'count']).reset_index()
    
    # 4. 按被试分组
    subject_summary = results.groupby('subject_id')[
        ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']
    ].agg(['mean', 'std', 'count']).reset_index()
    
    # 5. 按频段和阶段分组
    band_stage_summary = results.groupby(['frequency_band', 'stage'])[
        ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']
    ].agg(['mean', 'std', 'count']).reset_index()
    
    # 保存汇总结果
    band_summary.to_csv(os.path.join(OUTPUT_DIR, 'band_summary.csv'), index=False)
    stage_summary.to_csv(os.path.join(OUTPUT_DIR, 'stage_summary.csv'), index=False)
    channel_summary.to_csv(os.path.join(OUTPUT_DIR, 'channel_summary.csv'), index=False)
    subject_summary.to_csv(os.path.join(OUTPUT_DIR, 'subject_summary.csv'), index=False)
    band_stage_summary.to_csv(os.path.join(OUTPUT_DIR, 'band_stage_summary.csv'), index=False)
    
    logger.info("汇总统计已保存")
    
    return {
        'band': band_summary,
        'stage': stage_summary,
        'channel': channel_summary,
        'subject': subject_summary,
        'band_stage': band_stage_summary
    }

def create_summary_visualizations(results, summaries):
    """创建汇总可视化"""
    logger.info("创建汇总可视化...")
    
    # 创建图表目录
    figures_dir = os.path.join(OUTPUT_DIR, 'figures')
    os.makedirs(figures_dir, exist_ok=True)
    
    # 设置可视化样式
    sns.set(style="whitegrid")
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 1. 频段比较图
    plt.figure(figsize=(12, 8))
    
    # 提取数据
    band_data = summaries['band']
    bands = band_data['frequency_band']
    brain_to_heart = band_data[('brain_to_heart_ccm', 'mean')]
    heart_to_brain = band_data[('heart_to_brain_ccm', 'mean')]
    brain_to_heart_std = band_data[('brain_to_heart_ccm', 'std')]
    heart_to_brain_std = band_data[('heart_to_brain_ccm', 'std')]
    
    # 创建柱状图
    x = np.arange(len(bands))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(12, 8))
    rects1 = ax.bar(x - width/2, brain_to_heart, width, yerr=brain_to_heart_std,
                    label='脑→心', color='#3498db', alpha=0.8, capsize=5)
    rects2 = ax.bar(x + width/2, heart_to_brain, width, yerr=heart_to_brain_std,
                    label='心→脑', color='#e74c3c', alpha=0.8, capsize=5)
    
    # 添加标签和标题
    ax.set_xlabel('频段', fontsize=14)
    ax.set_ylabel('CCM强度', fontsize=14)
    ax.set_title('不同频段的脑-心交互作用强度', fontsize=16)
    ax.set_xticks(x)
    ax.set_xticklabels(bands)
    ax.legend(fontsize=12)
    
    # 添加数值标签
    def add_labels(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.3f}',
                        xy=(rect.get_x() + rect.get_width() / 2, height),
                        xytext=(0, 3),  # 3点垂直偏移
                        textcoords="offset points",
                        ha='center', va='bottom', fontsize=10)
    
    add_labels(rects1)
    add_labels(rects2)
    
    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'frequency_band_comparison.png'), dpi=300)
    plt.savefig(os.path.join(figures_dir, 'frequency_band_comparison.pdf'))
    plt.close()
    
    # 2. 实验阶段比较图
    plt.figure(figsize=(14, 8))
    
    # 提取数据
    stage_data = summaries['stage']
    stages = stage_data['stage']
    brain_to_heart = stage_data[('brain_to_heart_ccm', 'mean')]
    heart_to_brain = stage_data[('heart_to_brain_ccm', 'mean')]
    brain_to_heart_std = stage_data[('brain_to_heart_ccm', 'std')]
    heart_to_brain_std = stage_data[('heart_to_brain_ccm', 'std')]
    
    # 创建柱状图
    x = np.arange(len(stages))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(14, 8))
    rects1 = ax.bar(x - width/2, brain_to_heart, width, yerr=brain_to_heart_std,
                    label='脑→心', color='#3498db', alpha=0.8, capsize=5)
    rects2 = ax.bar(x + width/2, heart_to_brain, width, yerr=heart_to_brain_std,
                    label='心→脑', color='#e74c3c', alpha=0.8, capsize=5)
    
    # 添加标签和标题
    ax.set_xlabel('实验阶段', fontsize=14)
    ax.set_ylabel('CCM强度', fontsize=14)
    ax.set_title('不同实验阶段的脑-心交互作用强度', fontsize=16)
    ax.set_xticks(x)
    ax.set_xticklabels(stages)
    ax.legend(fontsize=12)
    
    # 添加数值标签
    add_labels(rects1)
    add_labels(rects2)
    
    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'stage_comparison.png'), dpi=300)
    plt.savefig(os.path.join(figures_dir, 'stage_comparison.pdf'))
    plt.close()
    
    # 3. 频段和阶段热图
    plt.figure(figsize=(16, 10))
    
    # 重塑数据
    band_stage_data = results.pivot_table(
        index='frequency_band', 
        columns='stage', 
        values='ccm_difference',
        aggfunc='mean'
    )
    
    # 创建热图
    sns.heatmap(band_stage_data, annot=True, cmap='coolwarm', center=0, fmt='.3f',
                linewidths=.5, cbar_kws={'label': 'CCM差异 (脑→心 - 心→脑)'})
    
    plt.title('不同频段和实验阶段的脑-心交互作用方向性', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'band_stage_heatmap.png'), dpi=300)
    plt.savefig(os.path.join(figures_dir, 'band_stage_heatmap.pdf'))
    plt.close()
    
    # 4. 被试散点图
    plt.figure(figsize=(12, 10))
    
    # 提取数据
    subject_data = summaries['subject']
    
    # 创建散点图
    plt.scatter(subject_data[('brain_to_heart_ccm', 'mean')], 
                subject_data[('heart_to_brain_ccm', 'mean')],
                alpha=0.7, s=80, c='#2ecc71')
    
    # 添加对角线
    max_val = max(subject_data[('brain_to_heart_ccm', 'mean')].max(), 
                  subject_data[('heart_to_brain_ccm', 'mean')].max())
    min_val = min(subject_data[('brain_to_heart_ccm', 'mean')].min(), 
                  subject_data[('heart_to_brain_ccm', 'mean')].min())
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5)
    
    # 添加标签
    for i, row in subject_data.iterrows():
        plt.annotate(row['subject_id'], 
                    (row[('brain_to_heart_ccm', 'mean')], row[('heart_to_brain_ccm', 'mean')]),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    plt.xlabel('脑→心 CCM强度', fontsize=14)
    plt.ylabel('心→脑 CCM强度', fontsize=14)
    plt.title('被试脑-心交互作用方向性对比', fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(os.path.join(figures_dir, 'subject_scatter.png'), dpi=300)
    plt.savefig(os.path.join(figures_dir, 'subject_scatter.pdf'))
    plt.close()
    
    logger.info("汇总可视化已保存")

def generate_summary_report(results, summaries):
    """生成汇总报告"""
    logger.info("生成汇总报告...")
    
    # 创建报告文件
    report_file = os.path.join(OUTPUT_DIR, 'summary_report.md')
    
    with open(report_file, 'w', encoding='utf-8') as f:
        # 标题
        f.write("# 脑-心非线性交互作用分析汇总报告\n\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 数据概览
        f.write("## 数据概览\n\n")
        f.write(f"- 总结果数: {len(results)}\n")
        f.write(f"- 被试数量: {results['subject_id'].nunique()}\n")
        f.write(f"- 实验阶段: {', '.join(results['stage'].unique())}\n")
        f.write(f"- 频段: {', '.join(results['frequency_band'].unique())}\n")
        f.write(f"- 通道数量: {results['channel'].nunique()}\n\n")
        
        # 频段分析
        f.write("## 频段分析\n\n")
        f.write("不同频段的脑-心交互作用强度:\n\n")
        
        band_table = summaries['band'].copy()
        band_table.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col for col in band_table.columns]
        f.write(band_table.to_markdown(index=False) + "\n\n")
        
        f.write("![频段比较图](figures/frequency_band_comparison.png)\n\n")
        
        # 实验阶段分析
        f.write("## 实验阶段分析\n\n")
        f.write("不同实验阶段的脑-心交互作用强度:\n\n")
        
        stage_table = summaries['stage'].copy()
        stage_table.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col for col in stage_table.columns]
        f.write(stage_table.to_markdown(index=False) + "\n\n")
        
        f.write("![实验阶段比较图](figures/stage_comparison.png)\n\n")
        
        # 频段和阶段交互分析
        f.write("## 频段和阶段交互分析\n\n")
        f.write("不同频段和实验阶段的脑-心交互作用方向性:\n\n")
        f.write("![频段和阶段热图](figures/band_stage_heatmap.png)\n\n")
        
        # 被试分析
        f.write("## 被试分析\n\n")
        f.write("被试脑-心交互作用方向性对比:\n\n")
        f.write("![被试散点图](figures/subject_scatter.png)\n\n")
        
        # 结论
        f.write("## 结论\n\n")
        
        # 计算总体方向性
        overall_diff = results['ccm_difference'].mean()
        if overall_diff > 0:
            direction = "脑→心"
        else:
            direction = "心→脑"
        
        f.write(f"1. 总体方向性: {direction} (平均差异: {overall_diff:.4f})\n")
        
        # 计算各频段的主导方向
        band_directions = []
        for i, row in summaries['band'].iterrows():
            band = row['frequency_band']
            diff = row[('ccm_difference', 'mean')]
            direction = "脑→心" if diff > 0 else "心→脑"
            band_directions.append(f"{band}: {direction} ({diff:.4f})")
        
        f.write("2. 频段方向性:\n")
        for direction in band_directions:
            f.write(f"   - {direction}\n")
        
        # 计算各阶段的主导方向
        stage_directions = []
        for i, row in summaries['stage'].iterrows():
            stage = row['stage']
            diff = row[('ccm_difference', 'mean')]
            direction = "脑→心" if diff > 0 else "心→脑"
            stage_directions.append(f"{stage}: {direction} ({diff:.4f})")
        
        f.write("\n3. 阶段方向性:\n")
        for direction in stage_directions:
            f.write(f"   - {direction}\n")
    
    logger.info(f"汇总报告已保存至 {report_file}")

def main():
    """主函数"""
    # 加载结果
    results = load_results()
    if results is None:
        return
    
    # 计算汇总统计
    summaries = compute_summary_statistics(results)
    
    # 创建汇总可视化
    create_summary_visualizations(results, summaries)
    
    # 生成汇总报告
    generate_summary_report(results, summaries)
    
    logger.info("汇总分析完成")

if __name__ == "__main__":
    main()

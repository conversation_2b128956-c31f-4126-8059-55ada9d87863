{"name": "EEG-ECG Causality Learning", "n_gpu": 1, "arch": {"type": "PredictModel", "args": {"d_model": 512, "n_head": 8, "n_layers": 2, "ffn_hidden": 2048, "drop_prob": 0.05, "tau": 10}}, "data_loader": {"type": "FIFDataLoader", "args": {"data_dir": "data", "batch_size": 64, "time_step": 128, "output_window": 32, "feature_dim": 1, "output_dim": 1, "selected_eeg_channels": ["Fz", "Cz", "Pz", "CPz", "POz", "F3", "C3", "P3", "O1", "F7", "T7", "P7", "Fp1", "F4", "C4", "P4", "O2", "F8", "T8", "P8", "Fp2"], "ecg_channel": ["ECG10-ECG20"], "shuffle": true, "validation_split": 0.2, "num_workers": 2}}, "optimizer": {"type": "<PERSON>", "args": {"lr": 0.001, "weight_decay": 0, "amsgrad": true}}, "loss": "masked_mse_torch", "metrics": ["masked_mse_torch"], "lr_scheduler": {"type": "StepLR", "args": {"step_size": 30, "gamma": 0.1}}, "trainer": {"epochs": 100, "save_dir": "saved/", "save_period": 1, "verbosity": 2, "monitor": "min val_loss", "early_stop": 10, "tensorboard": true}}
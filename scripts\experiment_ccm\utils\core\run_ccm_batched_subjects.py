#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分批处理被试的CCM分析
"""

import os
import sys
import time
import logging
import argparse
import subprocess
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('CCM_BATCHED_SUBJECTS')

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))

# 数据路径
RAW_DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
RESULTS_DIR = os.path.join(project_root, 'result', 'ccm_brain_heart')
PROGRESS_FILE = os.path.join(current_dir, 'ccm_processing_progress.json')

# 确保结果目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)

def get_subject_list():
    """获取所有被试ID列表（只获取基础ID，如 '01', '02', '05'）"""
    subject_files = []
    for file in os.listdir(RAW_DATA_DIR):
        if file.endswith('_prac.fif') and '_01_' in file:
            # 只提取基础被试ID，如 "05"
            base_subject_id = file.split('_')[0]
            if base_subject_id not in subject_files:
                subject_files.append(base_subject_id)
    return sorted(subject_files)

def split_subjects_into_batches(subject_list, batch_size):
    """将被试列表分成多个批次"""
    return [subject_list[i:i + batch_size] for i in range(0, len(subject_list), batch_size)]

def load_progress():
    """加载处理进度"""
    if os.path.exists(PROGRESS_FILE):
        try:
            with open(PROGRESS_FILE, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载进度文件出错: {e}")
    return {'processed_subjects': [], 'current_batch': 0, 'last_update': None}

def save_progress(progress):
    """保存处理进度"""
    progress['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    try:
        with open(PROGRESS_FILE, 'w') as f:
            json.dump(progress, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"保存进度文件出错: {e}")

def run_command(command):
    """运行命令并返回结果"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               encoding='utf-8')
        logger.info(f"命令执行成功: {command}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {command}")
        logger.error(f"错误信息: {e.stderr}")
        return False, e.stderr

def process_subject(subject_id, max_workers=10, use_complete_ccm=True, use_gpu=False):
    """处理单个被试的所有阶段"""
    logger.info(f"\n==================================================")
    logger.info(f"开始处理被试: {subject_id}")
    logger.info(f"==================================================")

    # 构建命令
    command = (f"python {os.path.dirname(os.path.abspath(__file__))}/run_ccm_all_stages.py "
              f"--subject {subject_id} "
              f"--max_workers {max_workers} "
              f"--start_batch 0 --end_batch 2")

    if use_complete_ccm:
        command += " --use_complete_ccm"

    if use_gpu:
        command += " --use_gpu"

    # 执行命令
    success, _ = run_command(command)

    if success:
        logger.info(f"被试 {subject_id} 处理完成")
        return True
    else:
        logger.error(f"被试 {subject_id} 处理失败")
        return False

def merge_all_subjects_results():
    """合并所有被试的结果"""
    logger.info(f"\n==================================================")
    logger.info(f"开始合并所有被试的结果")
    logger.info(f"==================================================")

    # 构建命令
    command = (f"python {os.path.dirname(os.path.abspath(__file__))}/run_ccm_all_subjects.py "
              f"--merge_only")

    # 执行命令
    success, _ = run_command(command)

    if success:
        logger.info(f"所有被试的结果已合并")
        return True
    else:
        logger.error(f"合并所有被试的结果失败")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分批处理被试的CCM分析')
    parser.add_argument('--batch_size', type=int, default=5,
                        help='每批处理的被试数量')
    parser.add_argument('--max_workers', type=int, default=10,
                        help='最大工作进程数')
    parser.add_argument('--use_complete_ccm', action='store_true', default=True,
                        help='使用完整版CCM模块')
    parser.add_argument('--use_gpu', action='store_true',
                        help='使用GPU加速')
    parser.add_argument('--resume', action='store_true',
                        help='从上次中断的地方继续处理')
    parser.add_argument('--force_reprocess', action='store_true',
                        help='强制重新处理所有被试，即使它们已经在进度文件中标记为已处理')
    parser.add_argument('--merge_only', action='store_true',
                        help='仅合并结果，不处理数据')
    parser.add_argument('--start_batch', type=int, default=0,
                        help='起始批次索引')
    parser.add_argument('--end_batch', type=int, default=-1,
                        help='结束批次索引，-1表示处理所有批次')
    args = parser.parse_args()

    start_time = time.time()

    # 如果只需要合并结果，则直接合并
    if args.merge_only:
        merge_all_subjects_results()
        return

    # 获取被试列表
    subject_list = get_subject_list()
    logger.info(f"共找到 {len(subject_list)} 个被试")

    # 将被试分成多个批次
    subject_batches = split_subjects_into_batches(subject_list, args.batch_size)
    logger.info(f"将被试分成 {len(subject_batches)} 个批次，每批 {args.batch_size} 个被试")

    # 加载处理进度
    progress = load_progress()
    processed_subjects = set() if args.force_reprocess else set(progress['processed_subjects'])
    current_batch = progress['current_batch'] if args.resume else args.start_batch

    # 设置结束批次
    end_batch = args.end_batch if args.end_batch >= 0 else len(subject_batches) - 1
    end_batch = min(end_batch, len(subject_batches) - 1)

    logger.info(f"将处理第 {current_batch} 到第 {end_batch} 批被试")
    logger.info(f"已处理 {len(processed_subjects)} 个被试")

    # 处理每个批次
    for batch_idx in range(current_batch, end_batch + 1):
        batch = subject_batches[batch_idx]
        logger.info(f"\n==================================================")
        logger.info(f"开始处理第 {batch_idx + 1}/{len(subject_batches)} 批被试: {batch}")
        logger.info(f"==================================================")

        # 处理批次中的每个被试
        for subject_id in batch:
            # 如果被试已经处理过，则跳过
            if subject_id in processed_subjects:
                logger.info(f"被试 {subject_id} 已处理过，跳过")
                continue

            # 处理被试
            if process_subject(subject_id, args.max_workers, args.use_complete_ccm, args.use_gpu):
                processed_subjects.add(subject_id)
                progress['processed_subjects'] = list(processed_subjects)
                progress['current_batch'] = batch_idx
                save_progress(progress)

            # 暂停一段时间，避免系统资源过度占用
            if subject_id != batch[-1]:
                logger.info(f"暂停60秒后继续处理下一个被试")
                time.sleep(60)

        # 批次之间暂停一段时间
        if batch_idx < end_batch:
            logger.info(f"第 {batch_idx + 1} 批处理完成，暂停300秒后继续下一批")
            time.sleep(300)

    # 合并所有被试的结果
    if len(processed_subjects) > 0:
        merge_all_subjects_results()

    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    logger.info(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")

if __name__ == "__main__":
    main()

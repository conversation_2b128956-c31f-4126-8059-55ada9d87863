# CCM分析结果可视化脚本
# 此脚本用于读取和可视化收敛交叉映射(CCM)分析的结果

# 加载必要的包
library(tidyverse)  # 数据处理和可视化
library(ggplot2)    # 绘图
library(patchwork)  # 组合多个图
library(viridis)    # 色盲友好配色
library(readr)      # 读取CSV文件
library(arrow)      # 读取Parquet文件
library(ggsci)      # 科学期刊配色方案
library(scales)     # 坐标轴格式化
library(RColorBrewer) # 配色方案
library(ggrepel)    # 避免文本重叠
library(corrplot)   # 相关性可视化

# 设置主题 - 使用IEEE论文风格
theme_ieee <- function(base_size = 12, base_family = "Arial") {
  theme_minimal(base_size = base_size, base_family = base_family) +
    theme(
      # 文本元素
      plot.title = element_text(size = base_size * 1.2, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = base_size, color = "gray30", hjust = 0.5),
      axis.title = element_text(size = base_size, face = "bold"),
      axis.text = element_text(size = base_size * 0.9),
      legend.title = element_text(size = base_size, face = "bold"),
      legend.text = element_text(size = base_size * 0.9),
      
      # 网格线和背景
      panel.grid.major = element_line(color = "gray90", size = 0.3),
      panel.grid.minor = element_blank(),
      panel.border = element_rect(fill = NA, color = "gray70", size = 0.5),
      
      # 图例位置
      legend.position = "bottom",
      legend.box = "horizontal",
      
      # 其他
      plot.margin = margin(10, 10, 10, 10)
    )
}

# 设置主题
theme_set(theme_ieee())

# 读取CCM分析结果
# 函数可以处理CSV或Parquet格式
read_ccm_results <- function(file_path) {
  # 检查文件扩展名
  ext <- tools::file_ext(file_path)
  
  if (ext == "csv") {
    # 读取CSV文件
    data <- read_csv(file_path)
  } else if (ext == "parquet") {
    # 读取Parquet文件
    data <- read_parquet(file_path)
  } else {
    stop("不支持的文件格式。请提供CSV或Parquet文件。")
  }
  
  # 检查是否包含必要的列
  required_columns <- c("subject_id", "stage", "eeg_channel", "ecg_channel", 
                        "band", "eeg_to_heart", "heart_to_eeg", "directionality")
  
  # 检查列名是否匹配（考虑到可能的变体）
  if (!all(required_columns %in% names(data)) && 
      !all(c("subject_id", "stage", "channel", "frequency_band", "eeg_to_hr", "hr_to_eeg", "directionality") %in% names(data))) {
    
    # 尝试重命名列以匹配预期格式
    if ("channel" %in% names(data) && !("eeg_channel" %in% names(data))) {
      data <- data %>% rename(eeg_channel = channel)
    }
    
    if ("frequency_band" %in% names(data) && !("band" %in% names(data))) {
      data <- data %>% rename(band = frequency_band)
    }
    
    if ("eeg_to_hr" %in% names(data) && !("eeg_to_heart" %in% names(data))) {
      data <- data %>% rename(eeg_to_heart = eeg_to_hr)
    }
    
    if ("hr_to_eeg" %in% names(data) && !("heart_to_eeg" %in% names(data))) {
      data <- data %>% rename(heart_to_eeg = hr_to_eeg)
    }
    
    # 再次检查
    required_columns <- c("subject_id", "stage", "eeg_channel", "ecg_channel", 
                          "band", "eeg_to_heart", "heart_to_eeg", "directionality")
    if (!all(required_columns %in% names(data))) {
      missing <- required_columns[!required_columns %in% names(data)]
      stop(paste("数据缺少必要的列:", paste(missing, collapse = ", ")))
    }
  }
  
  # 处理可能的字符串格式的数值列
  if (is.character(data$eeg_to_heart)) {
    # 尝试解析字符串格式的列表
    data <- data %>%
      mutate(
        eeg_to_heart_parsed = map(eeg_to_heart, ~{
          tryCatch({
            eval(parse(text = .x))
          }, error = function(e) {
            NA_real_
          })
        }),
        heart_to_eeg_parsed = map(heart_to_eeg, ~{
          tryCatch({
            eval(parse(text = .x))
          }, error = function(e) {
            NA_real_
          })
        })
      )
    
    # 如果成功解析，计算平均值
    if (!all(is.na(unlist(data$eeg_to_heart_parsed)))) {
      data <- data %>%
        mutate(
          eeg_to_heart_mean = map_dbl(eeg_to_heart_parsed, ~mean(unlist(.x), na.rm = TRUE)),
          heart_to_eeg_mean = map_dbl(heart_to_eeg_parsed, ~mean(unlist(.x), na.rm = TRUE))
        )
      
      # 使用计算的平均值
      data$eeg_to_heart <- data$eeg_to_heart_mean
      data$heart_to_eeg <- data$heart_to_eeg_mean
    }
    
    # 清理临时列
    data <- data %>%
      select(-matches("_parsed$|_mean$"))
  }
  
  # 确保数值列是数值类型
  data <- data %>%
    mutate(across(c(eeg_to_heart, heart_to_eeg, directionality), as.numeric))
  
  # 标准化频段名称
  if ("band" %in% names(data)) {
    data <- data %>%
      mutate(band = tolower(band))
  }
  
  return(data)
}

# 计算汇总统计
summarize_ccm_results <- function(data) {
  # 按EEG通道、ECG通道和频段分组计算平均值
  summary_by_channel_band <- data %>%
    group_by(eeg_channel, ecg_channel, band) %>%
    summarize(
      mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
      mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
      mean_directionality = mean(directionality, na.rm = TRUE),
      sd_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE),
      sd_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE),
      sd_directionality = sd(directionality, na.rm = TRUE),
      n_subjects = n_distinct(subject_id),
      .groups = "drop"
    )
  
  # 按频段分组计算平均值
  summary_by_band <- data %>%
    group_by(band) %>%
    summarize(
      mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
      mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
      mean_directionality = mean(directionality, na.rm = TRUE),
      sd_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE),
      sd_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE),
      sd_directionality = sd(directionality, na.rm = TRUE),
      .groups = "drop"
    )
  
  # 按EEG通道分组计算平均值
  summary_by_eeg <- data %>%
    group_by(eeg_channel) %>%
    summarize(
      mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
      mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
      mean_directionality = mean(directionality, na.rm = TRUE),
      sd_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE),
      sd_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE),
      sd_directionality = sd(directionality, na.rm = TRUE),
      .groups = "drop"
    )
  
  # 按ECG通道分组计算平均值
  summary_by_ecg <- data %>%
    group_by(ecg_channel) %>%
    summarize(
      mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
      mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
      mean_directionality = mean(directionality, na.rm = TRUE),
      sd_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE),
      sd_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE),
      sd_directionality = sd(directionality, na.rm = TRUE),
      .groups = "drop"
    )
  
  # 返回所有汇总
  return(list(
    by_channel_band = summary_by_channel_band,
    by_band = summary_by_band,
    by_eeg = summary_by_eeg,
    by_ecg = summary_by_ecg
  ))
}

# 绘制频段比较图
plot_frequency_bands <- function(summary_data) {
  # 设置频段顺序
  band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  summary_data$by_band$band <- factor(summary_data$by_band$band, levels = band_levels)
  
  # 准备绘图数据
  plot_data <- summary_data$by_band %>%
    pivot_longer(
      cols = c(mean_eeg_to_heart, mean_heart_to_eeg),
      names_to = "direction",
      values_to = "causality"
    ) %>%
    mutate(
      direction = case_when(
        direction == "mean_eeg_to_heart" ~ "EEG → Heart",
        direction == "mean_heart_to_eeg" ~ "Heart → EEG",
        TRUE ~ direction
      )
    )
  
  # 绘制条形图
  p <- ggplot(plot_data, aes(x = band, y = causality, fill = direction)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    geom_errorbar(
      aes(
        ymin = causality - ifelse(direction == "EEG → Heart", 
                                 summary_data$by_band$sd_eeg_to_heart, 
                                 summary_data$by_band$sd_heart_to_eeg) / sqrt(30),
        ymax = causality + ifelse(direction == "EEG → Heart", 
                                 summary_data$by_band$sd_eeg_to_heart, 
                                 summary_data$by_band$sd_heart_to_eeg) / sqrt(30)
      ),
      position = position_dodge(width = 0.8),
      width = 0.25
    ) +
    scale_fill_nejm() +  # New England Journal of Medicine配色
    labs(
      title = "Causality Strength by Frequency Band",
      subtitle = "Comparing EEG → Heart and Heart → EEG directions",
      x = "Frequency Band",
      y = "Causality Strength",
      fill = "Direction"
    ) +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "bottom"
    )
  
  return(p)
}

# 绘制方向性热图
plot_directionality_heatmap <- function(summary_data, top_n_channels = 10) {
  # 选择前N个EEG通道（基于平均方向性的绝对值）
  top_eeg <- summary_data$by_eeg %>%
    arrange(desc(abs(mean_directionality))) %>%
    head(top_n_channels) %>%
    pull(eeg_channel)
  
  # 选择前N个ECG通道
  top_ecg <- summary_data$by_ecg %>%
    arrange(desc(abs(mean_directionality))) %>%
    head(top_n_channels) %>%
    pull(ecg_channel)
  
  # 筛选数据
  heatmap_data <- summary_data$by_channel_band %>%
    filter(eeg_channel %in% top_eeg, ecg_channel %in% top_ecg)
  
  # 为每个频段创建一个热图
  band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  
  # 创建一个函数来生成每个频段的热图
  create_band_heatmap <- function(band_name) {
    band_data <- heatmap_data %>%
      filter(band == band_name) %>%
      select(eeg_channel, ecg_channel, mean_directionality)
    
    # 创建宽格式数据用于热图
    band_matrix <- band_data %>%
      pivot_wider(
        names_from = ecg_channel,
        values_from = mean_directionality,
        id_cols = eeg_channel
      )
    
    # 提取矩阵数据
    eeg_channels <- band_matrix$eeg_channel
    band_matrix <- band_matrix %>% select(-eeg_channel) %>% as.matrix()
    rownames(band_matrix) <- eeg_channels
    
    # 绘制热图
    corrplot(
      band_matrix,
      method = "color",
      type = "full",
      title = paste(band_name, "band"),
      mar = c(0, 0, 2, 0),
      col = colorRampPalette(c("#2166AC", "white", "#B2182B"))(100),
      tl.col = "black",
      tl.srt = 45,
      cl.lim = c(-max(abs(band_matrix), na.rm = TRUE), max(abs(band_matrix), na.rm = TRUE)),
      addCoef.col = "black",
      number.cex = 0.7,
      is.corr = FALSE
    )
  }
  
  # 设置绘图布局
  par(mfrow = c(2, 3))
  
  # 为每个频段创建热图
  for (band in band_levels) {
    if (band %in% unique(heatmap_data$band)) {
      create_band_heatmap(band)
    }
  }
  
  # 重置绘图布局
  par(mfrow = c(1, 1))
}

# 绘制EEG通道拓扑图
plot_eeg_topography <- function(summary_data, band_to_plot = "alpha") {
  # 需要安装和加载topoplot包
  if (!requireNamespace("eegkit", quietly = TRUE)) {
    message("请安装eegkit包: install.packages('eegkit')")
    return(NULL)
  }
  library(eegkit)
  
  # 提取指定频段的数据
  topo_data <- summary_data$by_eeg %>%
    filter(band == band_to_plot) %>%
    select(eeg_channel, mean_directionality)
  
  # 创建通道位置数据框
  # 注意：这需要根据您的EEG系统调整
  # 这里使用10-20系统的近似位置
  channel_positions <- data.frame(
    channel = c("Fp1", "Fp2", "F7", "F3", "Fz", "F4", "F8", 
                "T7", "C3", "Cz", "C4", "T8", 
                "P7", "P3", "Pz", "P4", "P8", 
                "O1", "Oz", "O2"),
    x = c(-0.2, 0.2, -0.5, -0.3, 0, 0.3, 0.5,
          -0.7, -0.4, 0, 0.4, 0.7,
          -0.5, -0.3, 0, 0.3, 0.5,
          -0.2, 0, 0.2),
    y = c(0.7, 0.7, 0.5, 0.5, 0.5, 0.5, 0.5,
          0, 0, 0, 0, 0,
          -0.5, -0.5, -0.5, -0.5, -0.5,
          -0.7, -0.7, -0.7)
  )
  
  # 合并数据
  topo_data <- topo_data %>%
    inner_join(channel_positions, by = c("eeg_channel" = "channel"))
  
  # 创建插值网格
  grid_size <- 100
  x_grid <- seq(-1, 1, length.out = grid_size)
  y_grid <- seq(-1, 1, length.out = grid_size)
  grid <- expand.grid(x = x_grid, y = y_grid)
  
  # 使用eegkit的topoplot函数
  topo <- eegkit::topoplot(
    values = topo_data$mean_directionality,
    channels = topo_data$eeg_channel,
    x = topo_data$x,
    y = topo_data$y,
    grid = grid,
    plot = FALSE
  )
  
  # 绘制拓扑图
  filled.contour(
    x = x_grid,
    y = y_grid,
    z = matrix(topo$z, nrow = grid_size),
    color.palette = colorRampPalette(c("#2166AC", "white", "#B2182B")),
    main = paste("EEG Topography -", band_to_plot, "band"),
    key.title = title("Directionality"),
    asp = 1
  )
  
  # 添加通道位置
  points(topo_data$x, topo_data$y, pch = 19, col = "black")
  text(topo_data$x, topo_data$y, labels = topo_data$eeg_channel, pos = 3, cex = 0.8)
  
  # 添加头部轮廓
  theta <- seq(0, 2 * pi, length.out = 100)
  lines(cos(theta), sin(theta), lwd = 2)
  
  # 添加鼻子和耳朵标记
  lines(c(0, 0), c(1, 1.1), lwd = 2)  # 鼻子
  lines(c(-1, -1.1), c(0, 0), lwd = 2)  # 左耳
  lines(c(1, 1.1), c(0, 0), lwd = 2)  # 右耳
}

# 主函数：读取数据并创建所有可视化
visualize_ccm_analysis <- function(file_path, output_dir = NULL) {
  # 设置输出目录
  if (is.null(output_dir)) {
    output_dir <- getwd()
  }
  
  # 创建输出目录（如果不存在）
  if (!dir.exists(output_dir)) {
    dir.create(output_dir, recursive = TRUE)
  }
  
  # 读取数据
  cat("读取CCM分析结果...\n")
  ccm_data <- read_ccm_results(file_path)
  
  # 计算汇总统计
  cat("计算汇总统计...\n")
  summary_stats <- summarize_ccm_results(ccm_data)
  
  # 创建频段比较图
  cat("创建频段比较图...\n")
  band_plot <- plot_frequency_bands(summary_stats)
  
  # 保存频段比较图
  band_plot_file <- file.path(output_dir, "ccm_frequency_band_comparison.png")
  ggsave(band_plot_file, band_plot, width = 10, height = 7, dpi = 300)
  cat("频段比较图已保存到:", band_plot_file, "\n")
  
  # 创建方向性热图
  cat("创建方向性热图...\n")
  png(file.path(output_dir, "ccm_directionality_heatmap.png"), 
      width = 12, height = 10, units = "in", res = 300)
  plot_directionality_heatmap(summary_stats)
  dev.off()
  cat("方向性热图已保存到:", file.path(output_dir, "ccm_directionality_heatmap.png"), "\n")
  
  # 创建EEG拓扑图（对每个频段）
  cat("创建EEG拓扑图...\n")
  band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  for (band in band_levels) {
    if (band %in% unique(ccm_data$band)) {
      png(file.path(output_dir, paste0("ccm_eeg_topography_", band, ".png")), 
          width = 8, height = 8, units = "in", res = 300)
      plot_eeg_topography(summary_stats, band)
      dev.off()
      cat("EEG拓扑图 (", band, ") 已保存到:", 
          file.path(output_dir, paste0("ccm_eeg_topography_", band, ".png")), "\n")
    }
  }
  
  # 保存汇总统计
  cat("保存汇总统计...\n")
  write_csv(summary_stats$by_band, file.path(output_dir, "ccm_summary_by_band.csv"))
  write_csv(summary_stats$by_eeg, file.path(output_dir, "ccm_summary_by_eeg.csv"))
  write_csv(summary_stats$by_ecg, file.path(output_dir, "ccm_summary_by_ecg.csv"))
  write_csv(summary_stats$by_channel_band, file.path(output_dir, "ccm_summary_by_channel_band.csv"))
  
  cat("\n可视化完成！所有结果已保存到:", output_dir, "\n")
  
  # 返回汇总统计和图表
  return(list(
    data = ccm_data,
    summary = summary_stats,
    plots = list(
      band_plot = band_plot
    )
  ))
}

# 使用示例
# 替换为您的实际文件路径
# result_file <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction/ccm_results_all_subjects.csv"
# output_folder <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_visualization"
# results <- visualize_ccm_analysis(result_file, output_folder)

# 打印使用说明
cat("CCM分析结果可视化脚本\n")
cat("=====================\n\n")
cat("使用方法:\n")
cat("1. 修改脚本底部的文件路径\n")
cat("2. 运行脚本\n\n")
cat("示例:\n")
cat('result_file <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction/ccm_results_all_subjects.csv"\n')
cat('output_folder <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_visualization"\n')
cat('results <- visualize_ccm_analysis(result_file, output_folder)\n')

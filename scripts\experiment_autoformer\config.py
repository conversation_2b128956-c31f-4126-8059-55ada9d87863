import os
import torch

class Config:
    """
    Autoformer模型配置类
    """
    def __init__(self):
        # 基础路径
        self.root_path = os.path.dirname(os.path.realpath(__file__))
        self.data_path = os.path.join(self.root_path, 'data')
        self.result_path = os.path.join(self.root_path, 'result')
        self.checkpoint_path = os.path.join(self.result_path, 'checkpoints')
        
        # 确保目录存在
        os.makedirs(self.data_path, exist_ok=True)
        os.makedirs(self.result_path, exist_ok=True)
        os.makedirs(self.checkpoint_path, exist_ok=True)
        
        # 数据参数
        self.seq_len = 96  # 输入序列长度
        self.label_len = 48  # 标签序列长度
        self.pred_len = 24  # 预测序列长度
        self.dataset = 'synthetic'  # 数据集名称
        self.features = 'M'  # 预测特征: 'M'(多变量),'S'(单变量)
        self.target = 'OT'  # 目标列（用于单变量预测）
        self.freq = 'h'  # 时间频率: 'h'(小时),'t'(分钟),'s'(秒)
        
        # 模型参数
        self.enc_in = 7  # 编码器输入维度
        self.dec_in = 7  # 解码器输入维度
        self.c_out = 7  # 输出维度
        self.d_model = 512  # 模型维度
        self.n_heads = 8  # 注意力头数
        self.e_layers = 2  # 编码器层数
        self.d_layers = 1  # 解码器层数
        self.d_ff = 2048  # 前馈网络维度
        self.moving_avg = 25  # 移动平均窗口大小
        self.factor = 1  # 注意力因子
        self.dropout = 0.05  # Dropout率
        self.embed = 'timeF'  # 时间特征嵌入: 'timeF', 'fixed', 'learned'
        self.activation = 'gelu'  # 激活函数
        self.output_attention = False  # 是否输出注意力
        
        # 训练参数
        self.batch_size = 32
        self.learning_rate = 0.0001
        self.train_epochs = 10
        self.patience = 3
        self.num_workers = 10  # 数据加载线程数
        
        # 硬件参数
        self.use_gpu = torch.cuda.is_available()
        self.gpu = 0
        self.use_multi_gpu = False
        self.devices = '0'
        
    def update(self, **kwargs):
        """
        更新配置参数
        """
        for k, v in kwargs.items():
            if hasattr(self, k):
                setattr(self, k, v)
            else:
                raise ValueError(f"Config has no attribute '{k}'")
        
        # 根据数据集自动调整输入输出维度
        if self.features == 'S':
            self.enc_in = 1
            self.dec_in = 1
            self.c_out = 1
            
    def __str__(self):
        """
        打印配置信息
        """
        config_str = "Autoformer配置:\n"
        for k, v in self.__dict__.items():
            if not k.startswith('__'):
                config_str += f"  {k}: {v}\n"
        return config_str

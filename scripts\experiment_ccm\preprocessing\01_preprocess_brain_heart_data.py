#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心非线性交互作用分析脚本

功能：
- 加载EEG和ECG数据
- 使用MVMD对EEG信号进行分解，提取不同频段
- 使用Hilbert变换提取EEG信号包络
- 提取ECG的R峰和HRV特征
- 使用CCM分析EEG频段与HRV之间的非线性交互作用
- 比较不同刺激条件下的脑-心交互差异
- 生成分析图表和统计结果

参考：
施秋霞.面向抑郁障碍的脑—心非线性交互作用及相关算法研究[D].兰州大学,2023.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
import neurokit2 as nk
from scipy.signal import hilbert
from scipy import stats
import seaborn as sns
from sklearn.preprocessing import StandardScaler
import pywt

# 设置matplotlib参数
plt.rcParams.update({
    'font.family': 'LXGW WenKai',  # 使用LXGW WenKai字体显示中文
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linestyle': '--',
    'axes.axisbelow': True
})

# 定义路径
DATA_DIR = r"C:\Users\<USER>\Desktop\ecgeeg\19-eegecg手动预处理6-ICA3"
PSYCH_DATA_PATH = r'C:\Users\<USER>\Desktop\stress0422.xlsx'
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'scripts', 'results', 'nonlinear_interaction')

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义阶段名称
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

# 定义EEG频段
FREQ_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8),
    'alpha': (8, 13),
    'beta': (13, 30)
}

# 定义关键通道
KEY_CHANNELS = ['Fz', 'Cz', 'Pz']

def load_psychological_data(file_path=PSYCH_DATA_PATH):
    """
    加载心理量表数据
    
    参数:
    file_path (str): 数据文件路径
    
    返回:
    pandas.DataFrame: 心理量表数据
    """
    print("加载心理量表数据...")
    
    # 加载Excel文件
    df = pd.read_excel(file_path)
    print(f"成功加载心理量表数据，共{len(df)}名被试")
    
    # 确保'编号'列存在
    if '编号' not in df.columns:
        # 尝试查找其他可能的ID列
        potential_id_cols = [col for col in df.columns if '编号' in col or 'ID' in col.upper()]
        if potential_id_cols:
            id_col = potential_id_cols[0]
            df['编号'] = df[id_col]
            print(f"使用'{id_col}'列作为被试ID")
    
    # 转换编号为字符串，并确保两位数字
    df['编号'] = df['编号'].apply(lambda x: str(int(x)).zfill(2) if pd.notnull(x) else None)
    
    return df

def group_subjects_by_anxiety(psych_data, threshold_percentile=50):
    """
    根据特质焦虑水平将被试分为高焦虑组和低焦虑组
    
    参数:
    psych_data (pandas.DataFrame): 心理量表数据
    threshold_percentile (int): 分组阈值百分位数，默认为50（中位数）
    
    返回:
    tuple: (高焦虑组被试ID列表, 低焦虑组被试ID列表)
    """
    print(f"根据特质焦虑水平将被试分组，阈值百分位数: {threshold_percentile}%")
    
    # 确认包含焦虑数据的列
    anxiety_cols = [col for col in psych_data.columns if '特质焦虑' in col]
    
    if not anxiety_cols:
        print("警告: 未找到特质焦虑相关列")
        return [], []
    
    # 如果有多个特质焦虑列，优先使用'特质焦虑1'（实验前的测量）
    anxiety_col = '特质焦虑1' if '特质焦虑1' in anxiety_cols else anxiety_cols[0]
    print(f"使用'{anxiety_col}'列进行分组")
    
    # 计算阈值
    anxiety_values = psych_data[anxiety_col].dropna().values
    threshold = np.percentile(anxiety_values, threshold_percentile)
    
    # 分组
    high_anxiety_subjects = psych_data[psych_data[anxiety_col] >= threshold]['编号'].tolist()
    low_anxiety_subjects = psych_data[psych_data[anxiety_col] < threshold]['编号'].tolist()
    
    print(f"高焦虑组: {len(high_anxiety_subjects)}名被试，特质焦虑分数 >= {threshold:.2f}")
    print(f"低焦虑组: {len(low_anxiety_subjects)}名被试，特质焦虑分数 < {threshold:.2f}")
    
    return high_anxiety_subjects, low_anxiety_subjects

def find_raw_data_files(subject_id, stage):
    """
    查找特定被试和阶段的原始数据文件
    
    参数:
    subject_id (str): 被试ID
    stage (str): 阶段名称
    
    返回:
    str: 找到的文件路径，如果未找到则返回None
    """
    # 构建搜索模式
    search_patterns = []
    
    # 根据阶段构建搜索模式
    if stage == 'prac':
        search_patterns.append(f"{subject_id}*prac*.fif")
    elif stage == 'rest1':
        search_patterns.append(f"{subject_id}*01*rest*.fif")
    elif stage == 'test1':
        search_patterns.append(f"{subject_id}*01*test*.fif")
    elif stage == 'rest2':
        search_patterns.append(f"{subject_id}*02*rest*.fif")
    elif stage == 'test2':
        search_patterns.append(f"{subject_id}*02*test*.fif")
    elif stage == 'rest3':
        search_patterns.append(f"{subject_id}*03*rest*.fif")
    elif stage == 'test3':
        search_patterns.append(f"{subject_id}*03*test*.fif")
    
    # 查找匹配的文件
    import glob
    for pattern in search_patterns:
        files = glob.glob(os.path.join(DATA_DIR, pattern))
        if files:
            return files[0]  # 返回第一个匹配的文件
    
    return None

# 这里将添加更多函数，包括MVMD分解、Hilbert变换、CCM分析等
# 由于代码较长，我们将分步实现

def main():
    """主函数"""
    print("开始脑-心非线性交互作用分析...")
    
    # 加载心理量表数据
    psych_data = load_psychological_data()
    
    # 根据焦虑水平分组
    high_anxiety, low_anxiety = group_subjects_by_anxiety(psych_data)
    
    # 这里将添加更多分析代码
    # 由于代码较长，我们将分步实现
    
    print("分析完成！")

if __name__ == "__main__":
    main()

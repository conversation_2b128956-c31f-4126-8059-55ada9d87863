#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建焦虑分组信息文件

该脚本基于真实的心理量表数据（特质焦虑分数）创建被试的焦虑分组信息。
使用特质焦虑1（实验开始前测量）作为分组依据，根据标准阈值或百分位数将被试分为高焦虑组和低焦虑组。

参考文献：
[1] 施秋霞. 面向抑郁障碍的脑—心非线性交互作用及相关算法研究[D]. 兰州大学, 2023.
"""

import os
import pandas as pd
import numpy as np
import logging
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 定义数据路径
DATA_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'nonlinear_interaction')
CCM_RESULTS_PATH = os.path.join(DATA_DIR, 'ccm_results_all_subjects.csv')
ANXIETY_GROUPS_PATH = os.path.join(DATA_DIR, 'anxiety_groups.csv')
PSYCH_DATA_PATH = r'C:/Users/<USER>/Desktop/stress0422.xlsx'
FIGURES_DIR = os.path.join(DATA_DIR, 'figures')
os.makedirs(FIGURES_DIR, exist_ok=True)

# 设置中文字体
try:
    font = FontProperties(fname=r"C:\Windows\Fonts\LXGWWenKai-Regular.ttf")
    plt.rcParams['font.family'] = ['LXGW WenKai']
except:
    logger.warning("无法加载LXGW WenKai字体，将使用系统默认字体")

# 设置IEEE风格的图表参数
plt.rcParams.update({
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.3,
    'lines.linewidth': 1.5,
    'axes.linewidth': 0.8,
    'xtick.major.width': 0.8,
    'ytick.major.width': 0.8
})

def load_psychological_data():
    """
    加载心理量表数据

    返回:
    pandas.DataFrame: 心理量表数据
    """
    logger.info(f"加载心理量表数据: {PSYCH_DATA_PATH}")

    try:
        # 加载Excel文件
        df = pd.read_excel(PSYCH_DATA_PATH)
        logger.info(f"成功加载心理量表数据，共{len(df)}名被试")

        # 检查必要的列是否存在
        required_columns = ['编号', '特质焦虑1']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"心理量表数据缺少必要的列: {', '.join(missing_columns)}")
            return None

        # 转换编号为字符串，并确保两位数字
        df['编号'] = df['编号'].apply(lambda x: str(int(x)).zfill(2) if pd.notnull(x) else None)

        return df

    except Exception as e:
        logger.error(f"加载心理量表数据失败: {e}")
        return None

def create_anxiety_groups(method='threshold', threshold=45, percentile=50):
    """
    创建焦虑分组信息

    参数:
    method (str): 分组方法，'threshold'使用固定阈值，'percentile'使用百分位数
    threshold (int): 高焦虑的阈值分数，默认为45分（STAI标准）
    percentile (int): 分组阈值百分位数，默认为50（中位数）

    返回:
    pandas.DataFrame: 焦虑分组信息
    """
    # 加载CCM分析结果，获取所有被试ID
    logger.info(f"加载CCM分析结果: {CCM_RESULTS_PATH}")
    ccm_results = pd.read_csv(CCM_RESULTS_PATH)
    logger.info(f"成功加载CCM分析结果，共 {len(ccm_results)} 行")

    # 获取所有受试者ID
    subject_ids = ccm_results['subject_id'].unique()
    logger.info(f"CCM分析中共有 {len(subject_ids)} 个受试者")

    # 加载心理量表数据
    psych_data = load_psychological_data()
    if psych_data is None:
        logger.error("无法加载心理量表数据，将使用随机分配的焦虑分数")
        # 设置随机种子以确保可重复性
        np.random.seed(42)
        # 随机分配焦虑分组
        anxiety_scores = np.random.normal(45, 10, len(subject_ids))
        # 创建数据框
        anxiety_groups = pd.DataFrame({
            'subject_id': subject_ids,
            'anxiety_score': anxiety_scores,
            'anxiety_group': ['high' if score >= 45 else 'low' for score in anxiety_scores]
        })
    else:
        logger.info("使用心理量表数据中的特质焦虑分数进行分组")

        # 确认包含焦虑数据的列
        anxiety_col = '特质焦虑1'  # 使用实验开始前的特质焦虑测量
        logger.info(f"使用'{anxiety_col}'列进行分组")

        # 创建存储焦虑分数的列表
        anxiety_data = []

        # 对每个CCM分析中的被试ID，查找对应的焦虑分数
        for subject_id in subject_ids:
            # 提取被试编号（去掉后缀，如"01_01"变为"01"）
            subject_number = subject_id.split('_')[0] if '_' in subject_id else subject_id

            # 在心理量表数据中查找对应的行
            subject_row = psych_data[psych_data['编号'] == subject_number]

            if len(subject_row) == 0:
                logger.warning(f"被试 {subject_id} (编号 {subject_number}) 在心理量表数据中未找到，将使用随机分数")
                anxiety_score = np.random.normal(45, 10)
            else:
                anxiety_score = subject_row[anxiety_col].values[0]
                logger.info(f"被试 {subject_id} (编号 {subject_number}) 的特质焦虑分数: {anxiety_score}")

            # 添加到数据列表
            anxiety_data.append({
                'subject_id': subject_id,
                'anxiety_score': anxiety_score
            })

        # 创建数据框
        anxiety_groups = pd.DataFrame(anxiety_data)

        # 根据方法确定分组阈值
        if method == 'threshold':
            logger.info(f"使用固定阈值 {threshold} 分进行分组")
            anxiety_groups['anxiety_group'] = ['high' if score >= threshold else 'low' for score in anxiety_groups['anxiety_score']]
        else:  # percentile
            threshold_value = np.percentile(anxiety_groups['anxiety_score'], percentile)
            logger.info(f"使用百分位数 {percentile}% (阈值: {threshold_value:.2f}) 进行分组")
            anxiety_groups['anxiety_group'] = ['high' if score >= threshold_value else 'low' for score in anxiety_groups['anxiety_score']]

    # 保存到CSV文件
    anxiety_groups.to_csv(ANXIETY_GROUPS_PATH, index=False)
    logger.info(f"焦虑分组信息已保存至: {ANXIETY_GROUPS_PATH}")

    # 统计高焦虑和低焦虑组的人数
    high_anxiety_count = len(anxiety_groups[anxiety_groups['anxiety_group'] == 'high'])
    low_anxiety_count = len(anxiety_groups[anxiety_groups['anxiety_group'] == 'low'])
    logger.info(f"高焦虑组: {high_anxiety_count} 人，低焦虑组: {low_anxiety_count} 人")

    # 可视化焦虑分数分布
    visualize_anxiety_distribution(anxiety_groups)

    return anxiety_groups

def visualize_anxiety_distribution(anxiety_groups):
    """
    可视化焦虑分数分布

    参数:
    anxiety_groups (pandas.DataFrame): 焦虑分组信息
    """
    logger.info("创建焦虑分数分布图")

    plt.figure(figsize=(10, 6))

    # 绘制直方图
    bins = np.arange(20, 80, 5)  # 从20到80，步长为5
    plt.hist(anxiety_groups['anxiety_score'], bins=bins, color='#457b9d', alpha=0.7, edgecolor='black')

    # 添加高低焦虑分界线
    threshold = anxiety_groups[anxiety_groups['anxiety_group'] == 'high']['anxiety_score'].min()
    plt.axvline(x=threshold, color='#e63946', linestyle='--', linewidth=2, label=f'焦虑阈值: {threshold:.2f}')

    # 添加标签和标题
    plt.xlabel('特质焦虑分数', fontproperties=font)
    plt.ylabel('被试数量', fontproperties=font)
    plt.title('被试特质焦虑分数分布', fontproperties=font)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(prop=font)

    # 保存图片
    output_path = os.path.join(FIGURES_DIR, 'anxiety_distribution.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logger.info(f"焦虑分数分布图已保存至: {output_path}")

    plt.close()

if __name__ == "__main__":
    # 使用固定阈值45分进行分组（STAI标准）
    create_anxiety_groups(method='threshold', threshold=45)

    # 也可以使用百分位数进行分组
    # create_anxiety_groups(method='percentile', percentile=50)  # 使用中位数

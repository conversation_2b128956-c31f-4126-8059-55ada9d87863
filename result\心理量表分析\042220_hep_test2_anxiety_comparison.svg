<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="720pt" height="432pt" viewBox="0 0 720 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:37.093177</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 720 432 
L 720 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 47.22875 394.495 
L 709.2 394.495 
L 709.2 25.05375 
L 47.22875 25.05375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 77.318352 394.495 
L 77.318352 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mf1e6d0d93b" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf1e6d0d93b" x="77.318352" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- -0.2 -->
      <g transform="translate(69.318352 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-2d" d="M 2975 2125 
L 125 2125 
L 125 2525 
L 2975 2525 
L 2975 2125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-2e" d="M 1075 125 
L 500 125 
L 500 675 
L 1075 675 
L 1075 125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 177.617027 394.495 
L 177.617027 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="177.617027" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g transform="translate(171.617027 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 277.915701 394.495 
L 277.915701 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="277.915701" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.2 -->
      <g transform="translate(271.915701 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 378.214375 394.495 
L 378.214375 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="378.214375" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.4 -->
      <g transform="translate(372.214375 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 478.513049 394.495 
L 478.513049 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="478.513049" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.6 -->
      <g transform="translate(472.513049 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-36" d="M 250 1612 
Q 275 1975 387 2225 
Q 500 2475 725 2850 
L 1750 4450 
L 2325 4450 
L 1275 2800 
Q 1950 2975 2350 2750 
Q 2750 2525 2887 2237 
Q 3025 1950 3037 1612 
Q 3050 1275 2937 950 
Q 2825 625 2537 362 
Q 2250 100 1737 75 
Q 1225 50 862 262 
Q 500 475 362 862 
Q 225 1250 250 1612 
z
M 1025 787 
Q 1250 550 1625 525 
Q 2000 500 2250 775 
Q 2500 1050 2500 1575 
Q 2500 2100 2187 2300 
Q 1875 2500 1487 2450 
Q 1100 2400 925 2075 
Q 750 1750 775 1387 
Q 800 1025 1025 787 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-36" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 578.811723 394.495 
L 578.811723 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="578.811723" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.8 -->
      <g transform="translate(572.811723 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-38" d="M 175 1375 
Q 175 1675 325 1962 
Q 475 2250 825 2425 
Q 525 2600 425 2812 
Q 325 3025 312 3300 
Q 300 3575 387 3775 
Q 475 3975 650 4150 
Q 825 4325 1037 4387 
Q 1250 4450 1500 4450 
Q 1750 4450 1950 4400 
Q 2150 4350 2375 4187 
Q 2600 4025 2700 3725 
Q 2800 3425 2687 3025 
Q 2575 2625 2100 2400 
Q 2525 2275 2700 2012 
Q 2875 1750 2875 1375 
Q 2875 1000 2762 775 
Q 2650 550 2512 400 
Q 2375 250 2137 162 
Q 1900 75 1537 75 
Q 1175 75 912 162 
Q 650 250 475 425 
Q 300 600 237 837 
Q 175 1075 175 1375 
z
M 687 1400 
Q 675 1100 787 875 
Q 900 650 1200 587 
Q 1500 525 1825 600 
Q 2150 675 2275 950 
Q 2400 1225 2362 1500 
Q 2325 1775 2050 1962 
Q 1775 2150 1450 2125 
Q 1125 2100 912 1900 
Q 700 1700 687 1400 
z
M 775 3350 
Q 775 3100 950 2875 
Q 1125 2650 1500 2650 
Q 1875 2650 2062 2875 
Q 2250 3100 2237 3412 
Q 2225 3725 2012 3875 
Q 1800 4025 1437 4000 
Q 1075 3975 925 3787 
Q 775 3600 775 3350 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-38" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 679.110398 394.495 
L 679.110398 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#mf1e6d0d93b" x="679.110398" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 1.0 -->
      <g transform="translate(673.110398 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <defs>
       <path id="madd31ea249" d="M 0 0 
L 0 2 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#madd31ea249" x="52.243684" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_16">
      <g>
       <use xlink:href="#madd31ea249" x="102.393021" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_17">
      <g>
       <use xlink:href="#madd31ea249" x="127.467689" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_18">
      <g>
       <use xlink:href="#madd31ea249" x="152.542358" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_19">
      <g>
       <use xlink:href="#madd31ea249" x="202.691695" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_20">
      <g>
       <use xlink:href="#madd31ea249" x="227.766364" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_21">
      <g>
       <use xlink:href="#madd31ea249" x="252.841032" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_22">
      <g>
       <use xlink:href="#madd31ea249" x="302.990369" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_23">
      <g>
       <use xlink:href="#madd31ea249" x="328.065038" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_24">
      <g>
       <use xlink:href="#madd31ea249" x="353.139706" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_25">
      <g>
       <use xlink:href="#madd31ea249" x="403.289044" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_26">
      <g>
       <use xlink:href="#madd31ea249" x="428.363712" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_27">
      <g>
       <use xlink:href="#madd31ea249" x="453.438381" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_28">
      <g>
       <use xlink:href="#madd31ea249" x="503.587718" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_29">
      <g>
       <use xlink:href="#madd31ea249" x="528.662386" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_30">
      <g>
       <use xlink:href="#madd31ea249" x="553.737055" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_31">
      <g>
       <use xlink:href="#madd31ea249" x="603.886392" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_32">
      <g>
       <use xlink:href="#madd31ea249" x="628.961061" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_33">
      <g>
       <use xlink:href="#madd31ea249" x="654.035729" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_34">
      <g>
       <use xlink:href="#madd31ea249" x="704.185066" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- 时间 (s) -->
     <g transform="translate(358.214375 420.002813) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-65f6" d="M 2525 4575 
Q 2500 3675 2500 2400 
Q 2500 1125 2525 400 
L 2000 400 
L 2000 875 
L 1000 875 
L 1000 50 
L 475 50 
Q 500 1200 500 2350 
Q 500 3525 475 4575 
L 2525 4575 
z
M 4875 3825 
Q 4875 4575 4850 5225 
L 5400 5225 
Q 5375 4575 5375 3825 
Q 5750 3825 6175 3850 
L 6175 3375 
Q 5750 3400 5375 3400 
L 5375 50 
Q 5375 -325 5075 -425 
Q 4775 -525 4300 -575 
Q 4300 -275 4025 0 
Q 4500 -50 4687 -25 
Q 4875 0 4875 300 
L 4875 3400 
Q 3625 3400 2775 3375 
L 2775 3850 
Q 3575 3825 4875 3825 
z
M 2000 1300 
L 2000 2550 
L 1000 2550 
L 1000 1300 
L 2000 1300 
z
M 2000 2975 
L 2000 4150 
L 1000 4150 
L 1000 2975 
L 2000 2975 
z
M 3550 2725 
Q 3875 2200 4175 1600 
Q 3925 1525 3650 1375 
Q 3450 1975 3075 2500 
Q 3300 2600 3550 2725 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-95f4" d="M 4450 3550 
Q 4425 3075 4425 2725 
L 4425 1400 
Q 4425 1075 4450 625 
L 1925 625 
Q 1950 1075 1950 1425 
L 1950 2725 
Q 1950 3075 1925 3550 
L 4450 3550 
z
M 5800 4875 
Q 5775 4400 5775 3925 
L 5775 250 
Q 5800 -325 5462 -450 
Q 5125 -575 4725 -625 
Q 4700 -325 4525 -25 
Q 5000 -50 5137 25 
Q 5275 100 5275 350 
L 5275 4450 
L 3775 4450 
Q 3175 4450 2650 4425 
L 2650 4900 
Q 3175 4875 3775 4875 
L 5800 4875 
z
M 550 -525 
Q 600 -25 600 525 
L 600 3150 
Q 600 3675 550 4025 
L 1125 4025 
Q 1100 3650 1100 3300 
L 1100 500 
Q 1100 0 1125 -525 
L 550 -525 
z
M 3950 1025 
L 3950 1950 
L 2425 1950 
L 2425 1025 
L 3950 1025 
z
M 3950 2350 
L 3950 3150 
L 2425 3150 
L 2425 2350 
L 3950 2350 
z
M 1525 5300 
Q 1750 5000 2225 4350 
Q 1950 4225 1700 4075 
Q 1450 4575 1075 5025 
Q 1350 5225 1525 5300 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-20" transform="scale(0.015625)"/>
       <path id="SimHei-28" d="M 2975 -200 
L 2700 -475 
Q 2075 125 1762 775 
Q 1450 1425 1450 2250 
Q 1450 3075 1762 3725 
Q 2075 4375 2700 5000 
L 2975 4725 
Q 2400 4175 2112 3587 
Q 1825 3000 1825 2250 
Q 1825 1500 2112 912 
Q 2400 325 2975 -200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-73" d="M 2750 900 
Q 2750 500 2437 287 
Q 2125 75 1650 75 
Q 1050 75 725 312 
Q 400 550 400 1000 
L 900 1000 
Q 900 700 1112 600 
Q 1325 500 1625 500 
Q 1925 500 2075 612 
Q 2225 725 2225 900 
Q 2225 1025 2100 1150 
Q 1975 1275 1475 1350 
Q 900 1425 687 1637 
Q 475 1850 475 2200 
Q 475 2500 762 2737 
Q 1050 2975 1600 2975 
Q 2100 2975 2387 2750 
Q 2675 2525 2675 2150 
L 2175 2150 
Q 2175 2375 2012 2462 
Q 1850 2550 1600 2550 
Q 1275 2550 1137 2437 
Q 1000 2325 1000 2175 
Q 1000 2000 1125 1900 
Q 1250 1800 1650 1750 
Q 2300 1650 2525 1437 
Q 2750 1225 2750 900 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-29" d="M 1675 2250 
Q 1675 1425 1362 775 
Q 1050 125 425 -475 
L 150 -200 
Q 725 325 1012 912 
Q 1300 1500 1300 2250 
Q 1300 3000 1012 3587 
Q 725 4175 150 4725 
L 425 5000 
Q 1050 4375 1362 3725 
Q 1675 3075 1675 2250 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-65f6"/>
      <use xlink:href="#SimHei-95f4" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-73" x="300"/>
      <use xlink:href="#SimHei-29" x="350"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_35">
      <path d="M 47.22875 374.759992 
L 709.2 374.759992 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <defs>
       <path id="m4b33ac0e0d" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m4b33ac0e0d" x="47.22875" y="374.759992" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- -0.4 -->
      <g transform="translate(24.22875 377.509992) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-34" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_37">
      <path d="M 47.22875 299.093686 
L 709.2 299.093686 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m4b33ac0e0d" x="47.22875" y="299.093686" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- -0.2 -->
      <g transform="translate(24.22875 301.843686) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_39">
      <path d="M 47.22875 223.42738 
L 709.2 223.42738 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m4b33ac0e0d" x="47.22875" y="223.42738" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.0 -->
      <g transform="translate(28.22875 226.17738) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_41">
      <path d="M 47.22875 147.761074 
L 709.2 147.761074 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m4b33ac0e0d" x="47.22875" y="147.761074" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.2 -->
      <g transform="translate(28.22875 150.511074) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_43">
      <path d="M 47.22875 72.094769 
L 709.2 72.094769 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m4b33ac0e0d" x="47.22875" y="72.094769" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.4 -->
      <g transform="translate(28.22875 74.844769) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_45">
      <defs>
       <path id="m22e0256e3e" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="393.676569" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_46">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="355.843416" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_47">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="336.926839" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_48">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="318.010263" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_49">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="280.17711" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_50">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="261.260533" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_51">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="242.343957" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_52">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="204.510804" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_53">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="185.594227" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_54">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="166.677651" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_55">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="128.844498" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_56">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="109.927921" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_57">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="91.011345" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_58">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="53.178192" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_59">
      <g>
       <use xlink:href="#m22e0256e3e" x="47.22875" y="34.261616" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_14">
     <!-- 振幅 (μV) -->
     <g transform="translate(18.97875 234.774375) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-632f" d="M 3275 -200 
Q 3400 -100 3437 37 
Q 3475 175 3475 500 
L 3475 2300 
L 3025 2300 
Q 3000 1800 2950 1375 
Q 2900 950 2800 600 
Q 2700 250 2575 -37 
Q 2450 -325 2325 -625 
Q 2175 -475 1825 -350 
Q 2150 -25 2300 412 
Q 2450 850 2500 1350 
Q 2550 1850 2562 2437 
Q 2575 3025 2575 3625 
Q 2575 4250 2525 4825 
L 5150 4825 
Q 5475 4825 5825 4850 
L 5825 4375 
Q 5400 4400 5225 4400 
L 3025 4400 
L 3025 2700 
L 5375 2700 
Q 5600 2700 5975 2725 
L 5975 2275 
Q 5850 2300 5475 2300 
L 4675 2300 
Q 4775 1600 4925 1275 
Q 5125 1450 5262 1587 
Q 5400 1725 5550 1975 
Q 5650 1775 5900 1575 
Q 5750 1500 5575 1337 
Q 5400 1175 5125 875 
Q 5225 650 5500 362 
Q 5775 75 6175 -75 
Q 5875 -250 5775 -525 
Q 5300 -175 5075 100 
Q 4850 375 4687 687 
Q 4525 1000 4412 1387 
Q 4300 1775 4250 2300 
L 3950 2300 
L 3950 150 
Q 4250 300 4575 475 
Q 4625 325 4750 50 
Q 4575 -25 4237 -212 
Q 3900 -400 3600 -650 
Q 3500 -450 3275 -200 
z
M 250 2000 
Q 550 2075 725 2125 
L 1150 2275 
L 1150 3350 
Q 675 3350 325 3325 
L 325 3800 
Q 650 3775 1150 3775 
L 1150 4500 
Q 1150 4850 1125 5150 
L 1675 5150 
Q 1650 4875 1650 4500 
L 1650 3775 
Q 1875 3775 2300 3800 
L 2300 3325 
Q 1900 3350 1650 3350 
L 1650 2425 
Q 2000 2550 2250 2675 
Q 2250 2475 2275 2250 
Q 2100 2200 1650 2000 
L 1650 100 
Q 1600 -250 1450 -362 
Q 1300 -475 725 -525 
Q 700 -325 600 25 
Q 850 0 1000 0 
Q 1150 0 1150 300 
L 1150 1800 
Q 975 1725 850 1662 
Q 725 1600 475 1450 
Q 425 1675 250 2000 
z
M 4950 3775 
Q 5275 3775 5600 3800 
L 5600 3325 
Q 5275 3350 5000 3350 
L 4000 3350 
Q 3725 3350 3375 3325 
L 3375 3800 
Q 3700 3775 4000 3775 
L 4950 3775 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5e45" d="M 5950 2250 
Q 5925 1875 5925 900 
Q 5925 -50 5950 -425 
L 5450 -425 
L 5450 -175 
L 3350 -175 
L 3350 -475 
L 2875 -475 
Q 2900 200 2900 1025 
Q 2900 1875 2875 2250 
L 5950 2250 
z
M 2575 1125 
Q 2575 800 2437 700 
Q 2300 600 1950 525 
Q 1900 800 1750 1000 
Q 1975 1000 2075 1037 
Q 2175 1075 2175 1300 
L 2175 3725 
L 1700 3725 
L 1700 475 
Q 1700 0 1725 -550 
L 1225 -550 
Q 1250 -25 1250 475 
L 1250 3725 
L 850 3725 
L 850 500 
L 400 500 
Q 425 1000 425 2300 
Q 425 3600 400 4100 
L 1250 4100 
L 1250 4500 
Q 1250 4825 1225 5175 
L 1725 5175 
Q 1700 4825 1700 4500 
L 1700 4100 
L 2600 4100 
Q 2575 3900 2575 3225 
L 2575 1125 
z
M 5575 3925 
Q 5550 3625 5550 3300 
Q 5550 2975 5575 2600 
L 3200 2600 
Q 3225 2975 3225 3300 
Q 3225 3625 3200 3925 
L 5575 3925 
z
M 5250 4800 
Q 5650 4800 6025 4825 
L 6025 4375 
Q 5650 4400 5250 4400 
L 3550 4400 
Q 3075 4400 2800 4375 
L 2800 4825 
Q 3075 4800 3550 4800 
L 5250 4800 
z
M 5150 2975 
L 5150 3575 
L 3625 3575 
L 3625 2975 
L 5150 2975 
z
M 5450 1225 
L 5450 1875 
L 4625 1875 
L 4625 1225 
L 5450 1225 
z
M 4175 200 
L 4175 850 
L 3350 850 
L 3350 200 
L 4175 200 
z
M 5450 200 
L 5450 850 
L 4625 850 
L 4625 200 
L 5450 200 
z
M 4175 1225 
L 4175 1875 
L 3350 1875 
L 3350 1225 
L 4175 1225 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-3bc" d="M 2025 3200 
L 2450 3200 
L 2450 1050 
Q 2625 550 3100 500 
L 3300 500 
Q 3875 575 3800 1750 
L 3800 3200 
L 4200 3200 
L 4200 900 
Q 4225 600 4450 575 
L 4650 575 
L 4650 175 
L 4200 200 
Q 4000 275 3950 450 
L 3925 500 
Q 3575 75 3075 150 
Q 2750 175 2450 450 
L 2450 -750 
L 2025 -750 
L 2025 3200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-56" d="M 3050 4400 
L 1800 75 
L 1300 75 
L 50 4400 
L 650 4400 
L 1525 1125 
L 1575 1125 
L 2450 4400 
L 3050 4400 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-632f"/>
      <use xlink:href="#SimHei-5e45" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-3bc" x="300"/>
      <use xlink:href="#SimHei-56" x="400"/>
      <use xlink:href="#SimHei-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m5c78d351f3" d="M 77.318352 -220.159117 
L 77.318352 -169.759229 
L 78.321339 -174.840866 
L 79.324326 -185.629577 
L 80.327313 -195.868219 
L 81.330299 -201.560743 
L 82.333286 -202.440074 
L 83.336273 -198.810614 
L 84.339259 -192.013365 
L 85.342246 -183.824661 
L 86.345233 -175.329844 
L 87.34822 -167.135189 
L 88.351206 -160.533257 
L 89.354193 -157.230986 
L 90.35718 -158.896884 
L 91.360167 -166.215423 
L 92.363153 -177.029607 
L 93.36614 -186.096881 
L 94.369127 -189.072421 
L 95.372114 -187.18835 
L 96.3751 -183.694406 
L 97.378087 -180.419497 
L 98.381074 -178.979871 
L 99.384061 -180.069427 
L 100.387047 -181.689616 
L 101.390034 -182.318554 
L 102.393021 -183.906088 
L 103.396008 -187.403626 
L 104.398994 -191.87339 
L 105.401981 -196.488357 
L 106.404968 -198.626897 
L 107.407955 -194.080274 
L 108.410941 -184.012819 
L 109.413928 -175.875065 
L 110.416915 -175.789601 
L 111.419902 -184.897009 
L 112.422888 -199.749239 
L 113.425875 -213.336681 
L 114.428862 -218.161789 
L 115.431848 -212.401566 
L 116.434835 -200.248808 
L 117.437822 -187.678915 
L 118.440809 -179.859936 
L 119.443795 -179.437307 
L 120.446782 -184.759329 
L 121.449769 -190.554304 
L 122.452756 -193.24732 
L 123.455742 -195.806443 
L 124.458729 -202.172838 
L 125.461716 -212.577754 
L 126.464703 -223.956198 
L 127.467689 -230.98217 
L 128.470676 -229.045849 
L 129.473663 -218.395736 
L 130.47665 -205.383832 
L 131.479636 -198.766048 
L 132.482623 -202.695557 
L 133.48561 -212.60982 
L 134.488597 -221.448992 
L 135.491583 -225.842976 
L 136.49457 -225.346433 
L 137.497557 -221.590137 
L 138.500544 -217.361487 
L 139.50353 -215.799762 
L 140.506517 -218.541218 
L 141.509504 -222.630212 
L 142.512491 -224.168509 
L 143.515477 -223.471753 
L 144.518464 -222.379032 
L 145.521451 -222.734253 
L 146.524438 -225.956506 
L 147.527424 -230.174144 
L 148.530411 -229.474133 
L 149.533398 -222.101218 
L 150.536384 -213.309021 
L 151.539371 -207.864484 
L 152.542358 -207.412575 
L 153.545345 -212.329307 
L 154.548331 -221.581656 
L 155.551318 -228.877886 
L 156.554305 -228.99602 
L 157.557292 -226.120231 
L 158.560278 -223.176738 
L 159.563265 -217.458009 
L 160.566252 -206.048874 
L 161.569239 -190.252377 
L 162.572225 -171.199103 
L 163.575212 -147.942199 
L 164.578199 -119.274091 
L 165.581186 -87.128576 
L 166.584172 -57.619919 
L 167.587159 -36.920334 
L 168.590146 -28.519846 
L 169.593133 -32.201233 
L 170.596119 -43.58955 
L 171.599106 -57.684948 
L 172.602093 -73.72808 
L 173.60508 -92.85241 
L 174.608066 -114.442021 
L 175.611053 -136.492385 
L 176.61404 -157.532535 
L 177.617027 -177.566454 
L 178.620013 -197.310633 
L 179.623 -216.744397 
L 180.625987 -234.447351 
L 181.628973 -248.1971 
L 182.63196 -256.291332 
L 183.634947 -258.73505 
L 184.637934 -257.558206 
L 185.64092 -256.017415 
L 186.643907 -256.754996 
L 187.646894 -260.085607 
L 188.649881 -264.314597 
L 189.652867 -267.184411 
L 190.655854 -267.135079 
L 191.658841 -264.133304 
L 192.661828 -259.583881 
L 193.664814 -254.750742 
L 194.667801 -249.858411 
L 195.670788 -246.376279 
L 196.673775 -246.997073 
L 197.676761 -252.621006 
L 198.679748 -261.123432 
L 199.682735 -268.773218 
L 200.685722 -272.535661 
L 201.688708 -271.023489 
L 202.691695 -265.035205 
L 203.694682 -258.013569 
L 204.697669 -254.244773 
L 205.700655 -254.873455 
L 206.703642 -256.346984 
L 207.706629 -255.045945 
L 208.709616 -249.595804 
L 209.712602 -238.6024 
L 210.715589 -222.755685 
L 211.718576 -206.947694 
L 212.721563 -196.089949 
L 213.724549 -190.932124 
L 214.727536 -189.241926 
L 215.730523 -189.265219 
L 216.733509 -191.07649 
L 217.736496 -195.678196 
L 218.739483 -203.708258 
L 219.74247 -214.581356 
L 220.745456 -225.86636 
L 221.748443 -233.486419 
L 222.75143 -233.978095 
L 223.754417 -227.437774 
L 224.757403 -218.548221 
L 225.76039 -214.427903 
L 226.763377 -220.05126 
L 227.766364 -234.343593 
L 228.76935 -250.333206 
L 229.772337 -260.365789 
L 230.775324 -261.697868 
L 231.778311 -256.708376 
L 232.781297 -249.847177 
L 233.784284 -244.54572 
L 234.787271 -243.266907 
L 235.790258 -246.812333 
L 236.793244 -250.650119 
L 237.796231 -249.347065 
L 238.799218 -244.10027 
L 239.802205 -240.972265 
L 240.805191 -244.539122 
L 241.808178 -252.956517 
L 242.811165 -261.751408 
L 243.814152 -271.92832 
L 244.817138 -283.090278 
L 245.820125 -291.562859 
L 246.823112 -294.536803 
L 247.826098 -293.457744 
L 248.829085 -292.497527 
L 249.832072 -292.884126 
L 250.835059 -292.213932 
L 251.838045 -287.185647 
L 252.841032 -276.337126 
L 253.844019 -262.390915 
L 254.847006 -251.486489 
L 255.849992 -249.088665 
L 256.852979 -255.758742 
L 257.855966 -266.616321 
L 258.858953 -276.075697 
L 259.861939 -280.593909 
L 260.864926 -277.47466 
L 261.867913 -267.073159 
L 262.8709 -253.572236 
L 263.873886 -242.030796 
L 264.876873 -235.697287 
L 265.87986 -235.115131 
L 266.882847 -237.873519 
L 267.885833 -239.038472 
L 268.88882 -233.817527 
L 269.891807 -221.195053 
L 270.894794 -203.569113 
L 271.89778 -183.935894 
L 272.900767 -165.376808 
L 273.903754 -151.713782 
L 274.906741 -145.814139 
L 275.909727 -146.760356 
L 276.912714 -150.188409 
L 277.915701 -151.441433 
L 278.918688 -147.80825 
L 279.921674 -139.268919 
L 280.924661 -128.437671 
L 281.927648 -119.2313 
L 282.930634 -114.70484 
L 283.933621 -115.262835 
L 284.936608 -118.179432 
L 285.939595 -119.664598 
L 286.942581 -118.509938 
L 287.945568 -116.754183 
L 288.948555 -116.683518 
L 289.951542 -118.828809 
L 290.954528 -122.616253 
L 291.957515 -126.126804 
L 292.960502 -125.658783 
L 293.963489 -118.935658 
L 294.966475 -107.977665 
L 295.969462 -96.672073 
L 296.972449 -87.222774 
L 297.975436 -80.147554 
L 298.978422 -75.622787 
L 299.981409 -73.861891 
L 300.984396 -76.285388 
L 301.987383 -85.898469 
L 302.990369 -104.030794 
L 303.993356 -126.464046 
L 304.996343 -144.935322 
L 305.99933 -155.147439 
L 307.002316 -157.929208 
L 308.005303 -154.011622 
L 309.00829 -144.280574 
L 310.011277 -132.640609 
L 311.014263 -125.518601 
L 312.01725 -126.686164 
L 313.020237 -134.585139 
L 314.023223 -144.766872 
L 315.02621 -152.823929 
L 316.029197 -154.936999 
L 317.032184 -148.621022 
L 318.03517 -134.996928 
L 319.038157 -118.988191 
L 320.041144 -105.97346 
L 321.044131 -99.490533 
L 322.047117 -101.118732 
L 323.050104 -109.941075 
L 324.053091 -121.879942 
L 325.056078 -131.00081 
L 326.059064 -133.423675 
L 327.062051 -130.183983 
L 328.065038 -124.563942 
L 329.068025 -118.738258 
L 330.071011 -113.735224 
L 331.073998 -110.494727 
L 332.076985 -109.308508 
L 333.079972 -109.049609 
L 334.082958 -108.541217 
L 335.085945 -107.515204 
L 336.088932 -105.312659 
L 337.091919 -101.089064 
L 338.094905 -96.367095 
L 339.097892 -94.861037 
L 340.100879 -98.878182 
L 341.103866 -107.037134 
L 342.106852 -114.593995 
L 343.109839 -115.546851 
L 344.112826 -106.069779 
L 345.115813 -88.088452 
L 346.118799 -69.129239 
L 347.121786 -57.324387 
L 348.124773 -56.659522 
L 349.127759 -65.596056 
L 350.130746 -78.792222 
L 351.133733 -90.543541 
L 352.13672 -97.93316 
L 353.139706 -101.789342 
L 354.142693 -105.156728 
L 355.14568 -110.522541 
L 356.148667 -117.112843 
L 357.151653 -120.450868 
L 358.15464 -115.97107 
L 359.157627 -104.151488 
L 360.160614 -91.365172 
L 361.1636 -85.047162 
L 362.166587 -88.416302 
L 363.169574 -99.656298 
L 364.172561 -113.74638 
L 365.175547 -123.661108 
L 366.178534 -123.701164 
L 367.181521 -114.693274 
L 368.184508 -104.362657 
L 369.187494 -100.210613 
L 370.190481 -104.126475 
L 371.193468 -113.320551 
L 372.196455 -123.631805 
L 373.199441 -130.900257 
L 374.202428 -132.532481 
L 375.205415 -130.430257 
L 376.208402 -129.862172 
L 377.211388 -133.148515 
L 378.214375 -136.794304 
L 379.217362 -138.0195 
L 380.220348 -138.559685 
L 381.223335 -139.428854 
L 382.226322 -139.420971 
L 383.229309 -136.698811 
L 384.232295 -132.980115 
L 385.235282 -131.375197 
L 386.238269 -132.023467 
L 387.241256 -133.401472 
L 388.244242 -133.681226 
L 389.247229 -131.975613 
L 390.250216 -130.336527 
L 391.253203 -131.56094 
L 392.256189 -135.933679 
L 393.259176 -141.532998 
L 394.262163 -146.676657 
L 395.26515 -150.321531 
L 396.268136 -150.193114 
L 397.271123 -146.355282 
L 398.27411 -145.297485 
L 399.277097 -151.037357 
L 400.280083 -161.045179 
L 401.28307 -168.960103 
L 402.286057 -172.15451 
L 403.289044 -172.167045 
L 404.29203 -170.597679 
L 405.295017 -168.230464 
L 406.298004 -165.795267 
L 407.300991 -165.502908 
L 408.303977 -165.598172 
L 409.306964 -162.115684 
L 410.309951 -153.478894 
L 411.312938 -142.13423 
L 412.315924 -133.52787 
L 413.318911 -132.94307 
L 414.321898 -141.974582 
L 415.324884 -156.867984 
L 416.327871 -169.709998 
L 417.330858 -173.860046 
L 418.333845 -169.209397 
L 419.336831 -160.969772 
L 420.339818 -156.392628 
L 421.342805 -160.120289 
L 422.345792 -170.266983 
L 423.348778 -179.930958 
L 424.351765 -184.727803 
L 425.354752 -183.31277 
L 426.357739 -176.79505 
L 427.360725 -168.93929 
L 428.363712 -164.030039 
L 429.366699 -164.670509 
L 430.369686 -170.251048 
L 431.372672 -176.126646 
L 432.375659 -176.611424 
L 433.378646 -170.913595 
L 434.381633 -163.737598 
L 435.384619 -159.835557 
L 436.387606 -159.593109 
L 437.390593 -160.695653 
L 438.39358 -162.363078 
L 439.396566 -163.53462 
L 440.399553 -161.15646 
L 441.40254 -154.773279 
L 442.405527 -151.475526 
L 443.408513 -156.062155 
L 444.4115 -165.260805 
L 445.414487 -172.882608 
L 446.417473 -177.563186 
L 447.42046 -181.477699 
L 448.423447 -186.047638 
L 449.426434 -190.651111 
L 450.42942 -193.506145 
L 451.432407 -192.673009 
L 452.435394 -186.570665 
L 453.438381 -175.834618 
L 454.441367 -164.698403 
L 455.444354 -158.50113 
L 456.447341 -159.637428 
L 457.450328 -166.502847 
L 458.453314 -175.241861 
L 459.456301 -181.744201 
L 460.459288 -183.926858 
L 461.462275 -183.336763 
L 462.465261 -183.254051 
L 463.468248 -184.93509 
L 464.471235 -187.128848 
L 465.474222 -187.615038 
L 466.477208 -183.899252 
L 467.480195 -175.080854 
L 468.483182 -164.491151 
L 469.486169 -158.841082 
L 470.489155 -162.378044 
L 471.492142 -173.051997 
L 472.495129 -184.837188 
L 473.498116 -191.806155 
L 474.501102 -190.641889 
L 475.504089 -182.348332 
L 476.507076 -171.848471 
L 477.510063 -164.564346 
L 478.513049 -162.842979 
L 479.516036 -165.890448 
L 480.519023 -171.81153 
L 481.522009 -178.057873 
L 482.524996 -181.514912 
L 483.527983 -180.531963 
L 484.53097 -178.033339 
L 485.533956 -179.28905 
L 486.536943 -185.534511 
L 487.53993 -192.582684 
L 488.542917 -197.37872 
L 489.545903 -200.223543 
L 490.54889 -199.061826 
L 491.551877 -189.930064 
L 492.554864 -176.331597 
L 493.55785 -167.774903 
L 494.560837 -169.579984 
L 495.563824 -180.54723 
L 496.566811 -194.367485 
L 497.569797 -204.531065 
L 498.572784 -209.295607 
L 499.575771 -210.106656 
L 500.578758 -208.855948 
L 501.581744 -207.12522 
L 502.584731 -206.540704 
L 503.587718 -207.591233 
L 504.590705 -207.837616 
L 505.593691 -204.463402 
L 506.596678 -198.762859 
L 507.599665 -194.665083 
L 508.602652 -194.756857 
L 509.605638 -199.490592 
L 510.608625 -207.376743 
L 511.611612 -214.86866 
L 512.614598 -218.426017 
L 513.617585 -217.305444 
L 514.620572 -213.266494 
L 515.623559 -209.616044 
L 516.626545 -211.279381 
L 517.629532 -219.907204 
L 518.632519 -230.800943 
L 519.635506 -236.713153 
L 520.638492 -234.134874 
L 521.641479 -225.150576 
L 522.644466 -214.130954 
L 523.647453 -205.346196 
L 524.650439 -202.043545 
L 525.653426 -204.411924 
L 526.656413 -209.510306 
L 527.6594 -213.632939 
L 528.662386 -214.612818 
L 529.665373 -213.926964 
L 530.66836 -216.224936 
L 531.671347 -224.043448 
L 532.674333 -234.736049 
L 533.67732 -244.025753 
L 534.680307 -250.292159 
L 535.683294 -252.907889 
L 536.68628 -251.049494 
L 537.689267 -246.472955 
L 538.692254 -242.310298 
L 539.695241 -239.300144 
L 540.698227 -236.37841 
L 541.701214 -233.739191 
L 542.704201 -233.16124 
L 543.707188 -235.618579 
L 544.710174 -240.121498 
L 545.713161 -244.291879 
L 546.716148 -245.907947 
L 547.719134 -244.677145 
L 548.722121 -242.181836 
L 549.725108 -240.448407 
L 550.728095 -241.011394 
L 551.731081 -243.844239 
L 552.734068 -246.561925 
L 553.737055 -246.581471 
L 554.740042 -243.892873 
L 555.743028 -240.30792 
L 556.746015 -238.242574 
L 557.749002 -239.87108 
L 558.751989 -244.776765 
L 559.754975 -248.192201 
L 560.757962 -244.37184 
L 561.760949 -234.58749 
L 562.763936 -224.692344 
L 563.766922 -219.325123 
L 564.769909 -220.862713 
L 565.772896 -228.457846 
L 566.775883 -237.346177 
L 567.778869 -241.342719 
L 568.781856 -238.933259 
L 569.784843 -233.481602 
L 570.78783 -229.922491 
L 571.790816 -231.697179 
L 572.793803 -238.503608 
L 573.79679 -247.947126 
L 574.799777 -256.89213 
L 575.802763 -259.798363 
L 576.80575 -248.722753 
L 577.808737 -231.473272 
L 578.811723 -221.568699 
L 579.81471 -223.092705 
L 580.817697 -231.328216 
L 581.820684 -239.548826 
L 582.82367 -244.635314 
L 583.826657 -245.777626 
L 584.829644 -244.286218 
L 585.832631 -243.14319 
L 586.835617 -244.466987 
L 587.838604 -247.269166 
L 588.841591 -247.833175 
L 589.844578 -242.860632 
L 590.847564 -233.204812 
L 591.850551 -223.925859 
L 592.853538 -220.313728 
L 593.856525 -224.286402 
L 594.859511 -233.406206 
L 595.862498 -241.605494 
L 596.865485 -242.98314 
L 597.868472 -237.356294 
L 598.871458 -230.952048 
L 599.874445 -230.753244 
L 600.877432 -236.301875 
L 601.880419 -239.346294 
L 602.883405 -243.454337 
L 603.886392 -246.914429 
L 604.889379 -245.478127 
L 605.892366 -238.735217 
L 606.895352 -230.938414 
L 607.898339 -226.524174 
L 608.901326 -226.276501 
L 609.904313 -228.531757 
L 610.907299 -232.000655 
L 611.910286 -235.971841 
L 612.913273 -239.061546 
L 613.916259 -239.981271 
L 614.919246 -239.891916 
L 615.922233 -239.583687 
L 616.92522 -237.117483 
L 617.928206 -230.481728 
L 618.931193 -222.250464 
L 619.93418 -217.375847 
L 620.937167 -217.011426 
L 621.940153 -218.309445 
L 622.94314 -220.919245 
L 623.946127 -226.306702 
L 624.949114 -232.991361 
L 625.9521 -237.91602 
L 626.955087 -240.352072 
L 627.958074 -241.006882 
L 628.961061 -238.499865 
L 629.964047 -232.439507 
L 630.967034 -227.114787 
L 631.970021 -225.811361 
L 632.973008 -227.07521 
L 633.975994 -227.242427 
L 634.978981 -224.835406 
L 635.981968 -219.716396 
L 636.984955 -212.865746 
L 637.987941 -208.06256 
L 638.990928 -208.817695 
L 639.993915 -214.336147 
L 640.996902 -220.088835 
L 641.999888 -221.773683 
L 643.002875 -217.152395 
L 644.005862 -206.867864 
L 645.008848 -196.09435 
L 646.011835 -192.514954 
L 647.014822 -200.547089 
L 648.017809 -217.383727 
L 649.020795 -233.513536 
L 650.023782 -238.595377 
L 651.026769 -231.45446 
L 652.029756 -217.833249 
L 653.032742 -202.64801 
L 654.035729 -188.925942 
L 655.038716 -179.618051 
L 656.041703 -176.467122 
L 657.044689 -178.183727 
L 658.047676 -181.385807 
L 659.050663 -182.840086 
L 660.05365 -182.252066 
L 661.056636 -182.038829 
L 662.059623 -182.617679 
L 663.06261 -181.339845 
L 664.065597 -176.72012 
L 665.068583 -169.734455 
L 666.07157 -161.569453 
L 667.074557 -153.98773 
L 668.077544 -151.173812 
L 669.08053 -156.573942 
L 670.083517 -168.082271 
L 671.086504 -179.333842 
L 672.089491 -186.930404 
L 673.092477 -191.523874 
L 674.095464 -192.209591 
L 675.098451 -185.186377 
L 676.101438 -172.807891 
L 677.104424 -163.849311 
L 678.107411 -160.879972 
L 679.110398 -161.985742 
L 679.110398 -213.645734 
L 679.110398 -213.645734 
L 678.107411 -206.876665 
L 677.104424 -207.020909 
L 676.101438 -211.51083 
L 675.098451 -217.97121 
L 674.095464 -227.94047 
L 673.092477 -237.07799 
L 672.089491 -238.090186 
L 671.086504 -231.302 
L 670.083517 -223.013579 
L 669.08053 -217.67552 
L 668.077544 -215.602216 
L 667.074557 -217.186986 
L 666.07157 -223.14586 
L 665.068583 -231.33983 
L 664.065597 -237.134305 
L 663.06261 -238.599793 
L 662.059623 -237.993421 
L 661.056636 -237.723216 
L 660.05365 -238.299822 
L 659.050663 -240.174248 
L 658.047676 -243.114454 
L 657.044689 -244.514835 
L 656.041703 -242.380595 
L 655.038716 -238.690782 
L 654.035729 -238.528437 
L 653.032742 -246.037486 
L 652.029756 -259.363187 
L 651.026769 -270.871769 
L 650.023782 -274.689065 
L 649.020795 -272.28123 
L 648.017809 -267.794875 
L 647.014822 -262.984536 
L 646.011835 -260.065358 
L 645.008848 -260.702754 
L 644.005862 -264.370079 
L 643.002875 -268.218973 
L 641.999888 -268.364921 
L 640.996902 -263.367158 
L 639.993915 -256.370113 
L 638.990928 -251.568755 
L 637.987941 -250.259771 
L 636.984955 -251.603661 
L 635.981968 -254.365898 
L 634.978981 -256.963743 
L 633.975994 -258.128645 
L 632.973008 -259.43827 
L 631.970021 -262.990785 
L 630.967034 -267.539389 
L 629.964047 -271.260501 
L 628.961061 -274.863848 
L 627.958074 -279.666135 
L 626.955087 -283.337428 
L 625.9521 -283.852045 
L 624.949114 -282.140981 
L 623.946127 -278.421669 
L 622.94314 -271.718552 
L 621.940153 -263.998691 
L 620.937167 -260.643717 
L 619.93418 -263.681193 
L 618.931193 -269.293196 
L 617.928206 -274.303931 
L 616.92522 -278.189364 
L 615.922233 -279.962365 
L 614.919246 -277.660384 
L 613.916259 -272.100422 
L 612.913273 -266.91256 
L 611.910286 -264.164111 
L 610.907299 -261.89643 
L 609.904313 -258.771808 
L 608.901326 -256.670237 
L 607.898339 -258.540669 
L 606.895352 -265.075081 
L 605.892366 -274.149542 
L 604.889379 -282.145689 
L 603.886392 -284.67347 
L 602.883405 -278.328964 
L 601.880419 -264.69259 
L 600.877432 -253.763233 
L 599.874445 -259.025709 
L 598.871458 -273.190438 
L 597.868472 -286.54265 
L 596.865485 -292.58075 
L 595.862498 -289.081145 
L 594.859511 -278.389594 
L 593.856525 -266.409312 
L 592.853538 -260.009503 
L 591.850551 -262.14568 
L 590.847564 -269.583014 
L 589.844578 -276.476591 
L 588.841591 -279.164052 
L 587.838604 -278.005307 
L 586.835617 -276.505103 
L 585.832631 -278.510216 
L 584.829644 -284.619962 
L 583.826657 -291.007337 
L 582.82367 -291.998689 
L 581.820684 -284.922483 
L 580.817697 -273.78318 
L 579.81471 -266.588632 
L 578.811723 -266.610361 
L 577.808737 -270.961968 
L 576.80575 -275.825296 
L 575.802763 -282.093013 
L 574.799777 -288.25899 
L 573.79679 -285.921955 
L 572.793803 -277.431587 
L 571.790816 -270.072711 
L 570.78783 -267.530407 
L 569.784843 -267.946291 
L 568.781856 -267.702822 
L 567.778869 -264.80343 
L 566.775883 -260.839529 
L 565.772896 -258.78257 
L 564.769909 -259.932694 
L 563.766922 -264.898713 
L 562.763936 -272.651239 
L 561.760949 -280.183846 
L 560.757962 -285.159976 
L 559.754975 -289.103183 
L 558.751989 -293.664509 
L 557.749002 -296.969225 
L 556.746015 -298.30476 
L 555.743028 -298.594835 
L 554.740042 -298.141359 
L 553.737055 -296.30067 
L 552.734068 -293.463163 
L 551.731081 -291.233493 
L 550.728095 -290.583232 
L 549.725108 -291.898684 
L 548.722121 -294.907631 
L 547.719134 -297.883528 
L 546.716148 -298.85789 
L 545.713161 -297.55968 
L 544.710174 -295.331653 
L 543.707188 -293.9542 
L 542.704201 -294.74994 
L 541.701214 -297.923513 
L 540.698227 -302.893194 
L 539.695241 -308.706485 
L 538.692254 -313.501847 
L 537.689267 -315.40758 
L 536.68628 -314.670691 
L 535.683294 -313.062028 
L 534.680307 -310.205957 
L 533.67732 -304.082884 
L 532.674333 -295.752599 
L 531.671347 -288.373876 
L 530.66836 -283.20361 
L 529.665373 -280.343901 
L 528.662386 -280.24142 
L 527.6594 -281.986064 
L 526.656413 -282.244043 
L 525.653426 -278.886427 
L 524.650439 -273.805488 
L 523.647453 -271.538751 
L 522.644466 -275.868065 
L 521.641479 -286.049677 
L 520.638492 -296.15377 
L 519.635506 -300.098409 
L 518.632519 -295.941529 
L 517.629532 -285.376281 
L 516.626545 -272.448856 
L 515.623559 -262.774777 
L 514.620572 -261.054825 
L 513.617585 -266.648702 
L 512.614598 -272.971912 
L 511.611612 -274.368593 
L 510.608625 -269.795893 
L 509.605638 -262.079221 
L 508.602652 -255.896973 
L 507.599665 -254.336845 
L 506.596678 -256.392927 
L 505.593691 -259.182874 
L 504.590705 -261.640517 
L 503.587718 -263.939326 
L 502.584731 -265.892009 
L 501.581744 -268.17447 
L 500.578758 -271.24141 
L 499.575771 -272.509267 
L 498.572784 -267.715321 
L 497.569797 -255.64638 
L 496.566811 -240.460116 
L 495.563824 -228.444813 
L 494.560837 -222.500279 
L 493.55785 -222.116985 
L 492.554864 -225.249322 
L 491.551877 -230.335284 
L 490.54889 -237.685352 
L 489.545903 -243.891166 
L 488.542917 -243.377646 
L 487.53993 -237.845226 
L 486.536943 -234.118264 
L 485.533956 -234.699853 
L 484.53097 -237.044759 
L 483.527983 -239.382683 
L 482.524996 -241.199731 
L 481.522009 -240.457519 
L 480.519023 -234.82891 
L 479.516036 -226.061132 
L 478.513049 -219.892547 
L 477.510063 -221.378812 
L 476.507076 -230.522052 
L 475.504089 -242.597666 
L 474.501102 -251.173168 
L 473.498116 -251.158876 
L 472.495129 -241.692294 
L 471.492142 -227.546527 
L 470.489155 -216.548654 
L 469.486169 -214.066228 
L 468.483182 -220.377681 
L 467.480195 -232.011722 
L 466.477208 -243.282858 
L 465.474222 -248.462201 
L 464.471235 -246.082337 
L 463.468248 -240.493599 
L 462.465261 -237.572062 
L 461.462275 -238.975727 
L 460.459288 -241.79885 
L 459.456301 -242.019436 
L 458.453314 -237.288103 
L 457.450328 -229.035389 
L 456.447341 -222.38521 
L 455.444354 -222.282394 
L 454.441367 -229.462747 
L 453.438381 -240.6996 
L 452.435394 -251.653821 
L 451.432407 -258.982124 
L 450.42942 -261.663623 
L 449.426434 -261.413524 
L 448.423447 -260.225495 
L 447.42046 -257.151232 
L 446.417473 -249.248071 
L 445.414487 -235.870925 
L 444.4115 -221.016245 
L 443.408513 -209.424348 
L 442.405527 -201.722476 
L 441.40254 -197.317659 
L 440.399553 -197.386542 
L 439.396566 -201.588705 
L 438.39358 -203.663342 
L 437.390593 -200.592612 
L 436.387606 -196.847951 
L 435.384619 -197.93891 
L 434.381633 -203.120982 
L 433.378646 -207.779538 
L 432.375659 -208.534679 
L 431.372672 -205.121933 
L 430.369686 -199.304225 
L 429.366699 -194.357305 
L 428.363712 -194.872755 
L 427.360725 -202.883618 
L 426.357739 -214.47934 
L 425.354752 -222.129796 
L 424.351765 -220.302281 
L 423.348778 -209.730675 
L 422.345792 -198.083234 
L 421.342805 -193.445102 
L 420.339818 -196.490371 
L 419.336831 -202.932039 
L 418.333845 -206.689855 
L 417.330858 -203.249102 
L 416.327871 -192.997863 
L 415.324884 -181.303222 
L 414.321898 -173.869254 
L 413.318911 -174.173929 
L 412.315924 -181.910043 
L 411.312938 -192.499602 
L 410.309951 -199.877025 
L 409.306964 -200.29957 
L 408.303977 -194.500477 
L 407.300991 -186.70571 
L 406.298004 -181.464827 
L 405.295017 -182.168609 
L 404.29203 -189.543295 
L 403.289044 -197.644299 
L 402.286057 -200.158903 
L 401.28307 -195.842089 
L 400.280083 -189.204177 
L 399.277097 -184.175176 
L 398.27411 -180.332724 
L 397.271123 -177.135823 
L 396.268136 -176.107584 
L 395.26515 -178.939146 
L 394.262163 -181.753511 
L 393.259176 -181.296489 
L 392.256189 -178.501952 
L 391.253203 -174.89914 
L 390.250216 -170.906254 
L 389.247229 -167.109494 
L 388.244242 -164.892259 
L 387.241256 -164.578963 
L 386.238269 -164.69815 
L 385.235282 -164.496984 
L 384.232295 -164.374845 
L 383.229309 -165.585291 
L 382.226322 -170.176562 
L 381.223335 -176.661932 
L 380.220348 -179.582593 
L 379.217362 -176.174227 
L 378.214375 -169.449309 
L 377.211388 -165.817946 
L 376.208402 -166.747934 
L 375.205415 -169.18088 
L 374.202428 -170.993603 
L 373.199441 -170.708407 
L 372.196455 -166.135596 
L 371.193468 -157.194732 
L 370.190481 -148.528513 
L 369.187494 -145.927077 
L 368.184508 -150.753966 
L 367.181521 -159.355873 
L 366.178534 -166.424889 
L 365.175547 -167.421622 
L 364.172561 -159.702409 
L 363.169574 -145.869357 
L 362.166587 -134.271721 
L 361.1636 -132.803656 
L 360.160614 -142.049573 
L 359.157627 -155.956477 
L 358.15464 -167.422966 
L 357.151653 -172.420623 
L 356.148667 -171.01382 
L 355.14568 -166.704343 
L 354.142693 -164.278297 
L 353.139706 -166.034536 
L 352.13672 -169.373884 
L 351.133733 -168.853735 
L 350.130746 -161.146744 
L 349.127759 -148.283215 
L 348.124773 -136.708111 
L 347.121786 -133.038241 
L 346.118799 -139.725985 
L 345.115813 -153.923024 
L 344.112826 -169.958464 
L 343.109839 -182.403084 
L 342.106852 -187.729745 
L 341.103866 -185.769867 
L 340.100879 -179.599933 
L 339.097892 -173.055643 
L 338.094905 -168.583557 
L 337.091919 -167.075569 
L 336.088932 -168.179133 
L 335.085945 -169.987859 
L 334.082958 -170.713112 
L 333.079972 -171.04924 
L 332.076985 -172.997942 
L 331.073998 -177.200195 
L 330.071011 -183.149573 
L 329.068025 -190.325045 
L 328.065038 -197.406456 
L 327.062051 -201.981222 
L 326.059064 -202.491372 
L 325.056078 -199.791386 
L 324.053091 -195.505711 
L 323.050104 -190.109405 
L 322.047117 -184.74934 
L 321.044131 -182.450752 
L 320.041144 -186.214974 
L 319.038157 -196.237274 
L 318.03517 -209.46394 
L 317.032184 -221.858984 
L 316.029197 -229.82246 
L 315.02621 -230.371067 
L 314.023223 -223.219993 
L 313.020237 -212.414813 
L 312.01725 -203.951443 
L 311.014263 -201.800879 
L 310.011277 -206.835071 
L 309.00829 -217.781256 
L 308.005303 -230.619424 
L 307.002316 -238.283223 
L 305.99933 -235.4222 
L 304.996343 -224.049079 
L 303.993356 -212.569511 
L 302.990369 -206.201826 
L 301.987383 -203.443077 
L 300.984396 -201.90652 
L 299.981409 -201.125629 
L 298.978422 -201.888696 
L 297.975436 -205.27549 
L 296.972449 -212.581025 
L 295.969462 -223.873845 
L 294.966475 -236.13355 
L 293.963489 -245.086945 
L 292.960502 -248.579946 
L 291.957515 -246.929243 
L 290.954528 -241.838459 
L 289.951542 -236.740636 
L 288.948555 -235.249129 
L 287.945568 -237.219007 
L 286.942581 -238.923102 
L 285.939595 -237.75673 
L 284.936608 -234.819143 
L 283.933621 -233.418483 
L 282.930634 -236.819504 
L 281.927648 -246.55249 
L 280.924661 -260.624967 
L 279.921674 -273.596158 
L 278.918688 -279.573069 
L 277.915701 -275.94574 
L 276.912714 -265.270766 
L 275.909727 -253.861393 
L 274.906741 -247.723981 
L 273.903754 -249.586904 
L 272.900767 -259.258845 
L 271.89778 -274.510309 
L 270.894794 -291.077829 
L 269.891807 -304.267949 
L 268.88882 -312.063297 
L 267.885833 -315.834238 
L 266.882847 -317.611457 
L 265.87986 -318.653742 
L 264.876873 -320.761523 
L 263.873886 -325.79508 
L 262.8709 -333.484553 
L 261.867913 -340.850284 
L 260.864926 -344.161084 
L 259.861939 -341.226645 
L 258.858953 -332.740898 
L 257.855966 -323.756248 
L 256.852979 -322.050013 
L 255.849992 -330.54584 
L 254.847006 -345.018264 
L 253.844019 -358.405111 
L 252.841032 -364.874298 
L 251.838045 -362.356447 
L 250.835059 -353.043746 
L 249.832072 -342.129825 
L 248.829085 -335.040465 
L 247.826098 -333.953456 
L 246.823112 -337.415006 
L 245.820125 -341.688953 
L 244.817138 -340.824676 
L 243.814152 -330.616187 
L 242.811165 -313.463875 
L 241.808178 -298.931636 
L 240.805191 -295.609193 
L 239.802205 -299.498584 
L 238.799218 -302.117262 
L 237.796231 -299.054811 
L 236.793244 -291.379674 
L 235.790258 -282.882074 
L 234.787271 -276.605163 
L 233.784284 -276.363972 
L 232.781297 -284.830369 
L 231.778311 -297.553358 
L 230.775324 -305.831225 
L 229.772337 -304.152159 
L 228.76935 -293.6154 
L 227.766364 -280.193365 
L 226.763377 -270.020083 
L 225.76039 -266.837417 
L 224.757403 -270.984865 
L 223.754417 -279.151565 
L 222.75143 -285.980423 
L 221.748443 -287.16588 
L 220.745456 -282.156472 
L 219.74247 -274.347331 
L 218.739483 -268.611918 
L 217.736496 -267.678471 
L 216.733509 -270.06338 
L 215.730523 -271.958202 
L 214.727536 -271.226145 
L 213.724549 -269.281237 
L 212.721563 -269.165683 
L 211.718576 -272.578752 
L 210.715589 -279.260151 
L 209.712602 -287.744073 
L 208.709616 -294.99799 
L 207.706629 -297.586236 
L 206.703642 -296.674248 
L 205.700655 -298.331257 
L 204.697669 -305.994827 
L 203.694682 -317.783024 
L 202.691695 -329.875099 
L 201.688708 -338.161733 
L 200.685722 -339.497222 
L 199.682735 -333.564141 
L 198.679748 -323.305355 
L 197.676761 -312.420314 
L 196.673775 -302.826356 
L 195.670788 -295.27985 
L 194.667801 -291.235885 
L 193.664814 -292.751165 
L 192.661828 -299.775472 
L 191.658841 -309.519485 
L 190.655854 -318.556124 
L 189.652867 -323.462905 
L 188.649881 -322.175615 
L 187.646894 -316.138472 
L 186.643907 -310.446955 
L 185.64092 -311.171254 
L 184.637934 -321.876497 
L 183.634947 -341.421872 
L 182.63196 -364.09647 
L 181.628973 -382.881373 
L 180.625987 -393.504358 
L 179.623 -396.04538 
L 178.620013 -393.20456 
L 177.617027 -386.965928 
L 176.61404 -376.423696 
L 175.611053 -358.234211 
L 174.608066 -329.111258 
L 173.60508 -288.722569 
L 172.602093 -241.344063 
L 171.599106 -195.318637 
L 170.596119 -159.878257 
L 169.593133 -139.387369 
L 168.590146 -131.161795 
L 167.587159 -131.558169 
L 166.584172 -139.901526 
L 165.581186 -156.959858 
L 164.578199 -182.630764 
L 163.575212 -213.651526 
L 162.572225 -242.347036 
L 161.569239 -261.448409 
L 160.566252 -269.529735 
L 159.563265 -271.526932 
L 158.560278 -273.151953 
L 157.557292 -273.306478 
L 156.554305 -268.27968 
L 155.551318 -259.917477 
L 154.548331 -254.406444 
L 153.545345 -252.170988 
L 152.542358 -253.277461 
L 151.539371 -259.448131 
L 150.536384 -268.000598 
L 149.533398 -273.584306 
L 148.530411 -274.739669 
L 147.527424 -275.20385 
L 146.524438 -276.556158 
L 145.521451 -277.459612 
L 144.518464 -277.659527 
L 143.515477 -276.114972 
L 142.512491 -271.153775 
L 141.509504 -264.59384 
L 140.506517 -262.067041 
L 139.50353 -267.174043 
L 138.500544 -280.358113 
L 137.497557 -298.055935 
L 136.49457 -311.082231 
L 135.491583 -310.148877 
L 134.488597 -293.580186 
L 133.48561 -269.867305 
L 132.482623 -252.223547 
L 131.479636 -247.774247 
L 130.47665 -254.8791 
L 129.473663 -267.650965 
L 128.470676 -278.377074 
L 127.467689 -280.706232 
L 126.464703 -272.925457 
L 125.461716 -258.692294 
L 124.458729 -244.362874 
L 123.455742 -234.284216 
L 122.452756 -228.719167 
L 121.449769 -227.212482 
L 120.446782 -229.679753 
L 119.443795 -234.30125 
L 118.440809 -240.640928 
L 117.437822 -249.604922 
L 116.434835 -259.769381 
L 115.431848 -266.231444 
L 114.428862 -264.602125 
L 113.425875 -255.295239 
L 112.422888 -242.344639 
L 111.419902 -230.623665 
L 110.416915 -224.739886 
L 109.413928 -225.560745 
L 108.410941 -229.585522 
L 107.407955 -233.201479 
L 106.404968 -235.337632 
L 105.401981 -234.301554 
L 104.398994 -228.840857 
L 103.396008 -221.77363 
L 102.393021 -216.385512 
L 101.390034 -212.898815 
L 100.387047 -211.494785 
L 99.384061 -213.437431 
L 98.381074 -217.543039 
L 97.378087 -222.330389 
L 96.3751 -227.612031 
L 95.372114 -232.102256 
L 94.369127 -233.822616 
L 93.36614 -233.411839 
L 92.363153 -232.233258 
L 91.360167 -228.807743 
L 90.35718 -222.204648 
L 89.354193 -214.542119 
L 88.351206 -209.860106 
L 87.34822 -211.833735 
L 86.345233 -222.185394 
L 85.342246 -239.224986 
L 84.339259 -257.112142 
L 83.336273 -268.398159 
L 82.333286 -267.742791 
L 81.330299 -254.832485 
L 80.327313 -235.626426 
L 79.324326 -220.602175 
L 78.321339 -216.076002 
L 77.318352 -220.159117 
z
" style="stroke: #808080; stroke-opacity: 0.2"/>
    </defs>
    <g clip-path="url(#p73e34baf4f)">
     <use xlink:href="#m5c78d351f3" x="0" y="432" style="fill: #808080; fill-opacity: 0.2; stroke: #808080; stroke-opacity: 0.2"/>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 428.363712 394.495 
L 528.662386 394.495 
L 528.662386 25.05375 
L 428.363712 25.05375 
z
" clip-path="url(#p73e34baf4f)" style="fill: #0000ff; opacity: 0.2; stroke: #0000ff; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_60">
    <path d="M 77.318352 237.040827 
L 78.321339 236.541566 
L 79.324326 228.884124 
L 81.330299 203.803386 
L 82.333286 196.908567 
L 83.336273 198.395614 
L 84.339259 207.437247 
L 86.345233 233.242381 
L 87.34822 242.515538 
L 88.351206 246.803318 
L 89.354193 246.113448 
L 90.35718 241.449234 
L 92.363153 227.368567 
L 93.36614 222.24564 
L 94.369127 220.552482 
L 95.372114 222.354697 
L 97.378087 230.625057 
L 98.381074 233.738545 
L 99.384061 235.246571 
L 100.387047 235.4078 
L 101.390034 234.391316 
L 102.393021 231.8542 
L 103.396008 227.411372 
L 104.398994 221.642877 
L 105.401981 216.605045 
L 106.404968 215.017736 
L 107.407955 218.359124 
L 109.413928 231.282095 
L 110.416915 231.735257 
L 111.419902 224.239663 
L 113.425875 197.68404 
L 114.428862 190.618043 
L 115.431848 192.683495 
L 116.434835 201.990905 
L 117.437822 213.358081 
L 118.440809 221.749568 
L 119.443795 225.130722 
L 120.446782 224.780459 
L 121.449769 223.116607 
L 122.452756 221.016757 
L 123.455742 216.95467 
L 124.458729 208.732144 
L 126.464703 183.559173 
L 127.467689 176.155799 
L 128.470676 178.288539 
L 129.473663 188.976649 
L 130.47665 201.868534 
L 131.479636 208.729853 
L 132.482623 204.540448 
L 133.48561 190.761438 
L 134.488597 174.485411 
L 135.491583 164.004074 
L 136.49457 163.785668 
L 137.497557 172.176964 
L 138.500544 183.1402 
L 139.50353 190.513098 
L 140.506517 191.69587 
L 141.509504 188.387974 
L 142.512491 184.338858 
L 143.515477 182.206638 
L 144.518464 181.98072 
L 145.521451 181.903067 
L 146.524438 180.743668 
L 147.527424 179.311003 
L 148.530411 179.893099 
L 149.533398 184.157238 
L 151.539371 198.343692 
L 152.542358 201.654982 
L 153.545345 199.749852 
L 154.548331 194.00595 
L 155.551318 187.602319 
L 156.554305 183.36215 
L 157.557292 182.286645 
L 158.560278 183.835654 
L 159.563265 187.50753 
L 160.566252 194.210695 
L 161.569239 206.149607 
L 162.572225 225.226931 
L 163.575212 251.203137 
L 165.581186 309.955783 
L 166.584172 333.239277 
L 167.587159 347.760748 
L 168.590146 352.159179 
L 169.593133 346.205699 
L 170.596119 330.266097 
L 171.599106 305.498207 
L 175.611053 184.636702 
L 176.61404 165.021884 
L 177.617027 149.733809 
L 178.620013 136.742404 
L 179.623 125.605112 
L 180.625987 118.024146 
L 181.628973 116.460763 
L 182.63196 121.806099 
L 184.637934 142.282648 
L 185.64092 148.405665 
L 186.643907 148.399024 
L 187.646894 143.88796 
L 188.649881 138.754894 
L 189.652867 136.676342 
L 190.655854 139.154399 
L 191.658841 145.173606 
L 192.661828 152.320324 
L 193.664814 158.249047 
L 194.667801 161.452852 
L 195.670788 161.171935 
L 196.673775 157.088285 
L 197.676761 149.47934 
L 199.682735 130.831321 
L 200.685722 125.983558 
L 201.688708 127.407389 
L 202.691695 134.544848 
L 203.694682 144.101703 
L 204.697669 151.8802 
L 205.700655 155.397644 
L 207.706629 155.683909 
L 208.709616 159.703103 
L 209.712602 168.826764 
L 211.718576 192.236777 
L 212.721563 199.372184 
L 213.724549 201.89332 
L 214.727536 201.765965 
L 215.730523 201.388289 
L 216.733509 201.430065 
L 217.736496 200.321666 
L 218.739483 195.839912 
L 219.74247 187.535656 
L 220.745456 177.988584 
L 221.748443 171.673851 
L 222.75143 172.020741 
L 223.754417 178.705331 
L 224.757403 187.233457 
L 225.76039 191.36734 
L 226.763377 186.964329 
L 227.766364 174.731521 
L 228.76935 160.025697 
L 229.772337 149.741026 
L 230.775324 148.235454 
L 231.778311 154.869133 
L 232.781297 164.661227 
L 233.784284 171.545154 
L 234.787271 172.063965 
L 235.790258 167.152796 
L 236.793244 160.985104 
L 237.796231 157.799062 
L 238.799218 158.891234 
L 239.802205 161.764575 
L 240.805191 161.925843 
L 241.808178 156.055924 
L 242.811165 144.392359 
L 243.814152 130.727746 
L 244.817138 120.042523 
L 245.820125 115.374094 
L 246.823112 116.024095 
L 247.826098 118.2944 
L 248.829085 118.231004 
L 249.832072 114.493024 
L 250.835059 109.371161 
L 251.838045 107.228953 
L 252.841032 111.394288 
L 253.844019 121.601987 
L 254.847006 133.747624 
L 255.849992 142.182748 
L 256.852979 143.095622 
L 257.855966 136.813715 
L 258.858953 127.591702 
L 259.861939 121.089723 
L 260.864926 121.182128 
L 261.867913 128.038279 
L 263.873886 148.087062 
L 264.876873 153.770595 
L 265.87986 155.115564 
L 266.882847 154.257512 
L 267.885833 154.563645 
L 268.88882 159.059588 
L 269.891807 169.268499 
L 270.894794 184.676529 
L 272.900767 219.682173 
L 273.903754 231.349657 
L 274.906741 235.23094 
L 275.909727 231.689125 
L 276.912714 224.270413 
L 277.915701 218.306414 
L 278.918688 218.309341 
L 279.921674 225.567462 
L 281.927648 249.108105 
L 282.930634 256.237828 
L 283.933621 257.659341 
L 285.939595 253.289336 
L 286.942581 253.28348 
L 287.945568 255.013405 
L 288.948555 256.033676 
L 289.951542 254.215277 
L 291.957515 245.471977 
L 292.960502 244.880635 
L 293.963489 249.988699 
L 294.966475 259.944392 
L 295.969462 271.727041 
L 296.972449 282.0981 
L 297.975436 289.288478 
L 298.978422 293.244258 
L 299.981409 294.50624 
L 300.984396 292.904046 
L 301.987383 287.329227 
L 302.990369 276.88369 
L 304.996343 247.5078 
L 305.99933 236.715181 
L 307.002316 233.893784 
L 308.005303 239.684477 
L 310.011277 262.26216 
L 311.014263 268.34026 
L 312.01725 266.681196 
L 313.020237 258.500024 
L 314.023223 248.006568 
L 315.02621 240.402502 
L 316.029197 239.62027 
L 317.032184 246.759997 
L 318.03517 259.769566 
L 319.038157 274.387268 
L 320.041144 285.905783 
L 321.044131 291.029358 
L 322.047117 289.065964 
L 323.050104 281.97476 
L 324.053091 273.307173 
L 325.056078 266.603902 
L 326.059064 264.042476 
L 327.062051 265.917397 
L 328.065038 271.014801 
L 330.071011 283.557601 
L 331.073998 288.152539 
L 332.076985 290.846775 
L 333.079972 291.950576 
L 334.082958 292.372836 
L 335.085945 293.248468 
L 336.088932 295.254104 
L 337.091919 297.917683 
L 338.094905 299.524674 
L 339.097892 298.04166 
L 340.100879 292.760942 
L 341.103866 285.596499 
L 342.106852 280.83813 
L 343.109839 283.025033 
L 344.112826 293.985879 
L 346.118799 327.572388 
L 347.121786 336.818686 
L 348.124773 335.316184 
L 349.127759 325.060365 
L 350.130746 312.030517 
L 351.133733 302.301362 
L 352.13672 298.346478 
L 353.139706 298.088061 
L 354.142693 297.282488 
L 355.14568 293.386558 
L 356.148667 287.936669 
L 357.151653 285.564254 
L 358.15464 290.302982 
L 359.157627 301.946018 
L 360.160614 315.292628 
L 361.1636 323.074591 
L 362.166587 320.655988 
L 363.169574 309.237172 
L 364.172561 295.275605 
L 365.175547 286.458635 
L 366.178534 286.936973 
L 367.181521 294.975427 
L 368.184508 304.441689 
L 369.187494 308.931155 
L 370.190481 305.672506 
L 372.196455 287.116299 
L 373.199441 281.195668 
L 374.202428 280.236958 
L 375.205415 282.194432 
L 376.208402 283.694947 
L 377.211388 282.51677 
L 379.217362 274.903137 
L 380.220348 272.928861 
L 381.223335 273.954607 
L 382.226322 277.201233 
L 383.229309 280.857949 
L 384.232295 283.32252 
L 385.235282 284.06391 
L 386.238269 283.639191 
L 387.241256 283.009783 
L 389.247229 282.457447 
L 390.250216 281.37861 
L 391.253203 278.76996 
L 393.259176 270.585257 
L 394.262163 267.784916 
L 395.26515 267.369661 
L 397.271123 270.254447 
L 398.27411 269.184895 
L 399.277097 264.393734 
L 401.28307 249.598904 
L 402.286057 245.843294 
L 403.289044 247.094328 
L 405.295017 256.800463 
L 406.298004 258.369953 
L 407.300991 255.895691 
L 408.303977 251.950675 
L 409.306964 250.792373 
L 410.309951 255.322041 
L 412.315924 274.281043 
L 413.318911 278.441501 
L 414.321898 274.078082 
L 416.327871 250.64607 
L 417.330858 243.445426 
L 418.333845 244.050374 
L 420.339818 255.558501 
L 421.342805 255.217305 
L 422.345792 247.824892 
L 423.348778 237.169184 
L 424.351765 229.484958 
L 425.354752 229.278717 
L 426.357739 236.362805 
L 427.360725 246.088546 
L 428.363712 252.548603 
L 429.366699 252.486093 
L 431.372672 241.375711 
L 432.375659 239.426948 
L 433.378646 242.653433 
L 434.381633 248.57071 
L 435.384619 253.112767 
L 436.387606 253.77947 
L 438.39358 248.98679 
L 439.396566 249.438338 
L 441.40254 255.954531 
L 442.405527 255.400999 
L 443.408513 249.256749 
L 446.417473 218.594372 
L 447.42046 212.685535 
L 448.423447 208.863433 
L 449.426434 205.967683 
L 450.42942 204.415116 
L 451.432407 206.172434 
L 452.435394 212.887757 
L 454.441367 234.919425 
L 455.444354 241.608238 
L 456.447341 240.988681 
L 457.450328 234.230882 
L 458.453314 225.735018 
L 459.456301 220.118182 
L 460.459288 219.137146 
L 461.462275 220.843755 
L 462.465261 221.586944 
L 463.468248 219.285656 
L 464.471235 215.394408 
L 465.474222 213.96138 
L 466.477208 218.408945 
L 468.483182 239.565584 
L 469.486169 245.546345 
L 470.489155 242.536651 
L 471.492142 231.700738 
L 472.495129 218.735259 
L 473.498116 210.517485 
L 474.501102 211.092472 
L 475.504089 219.527001 
L 476.507076 230.814739 
L 477.510063 239.028421 
L 478.513049 240.632237 
L 479.516036 236.02421 
L 480.519023 228.67978 
L 481.522009 222.742304 
L 482.524996 220.642679 
L 483.527983 222.042677 
L 484.53097 224.460951 
L 485.533956 225.005548 
L 486.536943 222.173613 
L 488.542917 211.621817 
L 489.545903 209.942645 
L 490.54889 213.626411 
L 491.551877 221.867326 
L 492.554864 231.20954 
L 493.55785 237.054056 
L 494.560837 235.959869 
L 495.563824 227.503978 
L 497.569797 201.911277 
L 498.572784 193.494536 
L 499.575771 190.692038 
L 500.578758 191.951321 
L 501.581744 194.350155 
L 502.584731 195.783644 
L 503.587718 196.23472 
L 504.590705 197.260933 
L 505.593691 200.176862 
L 506.596678 204.422107 
L 507.599665 207.499036 
L 508.602652 206.673085 
L 509.605638 201.215093 
L 510.608625 193.413682 
L 511.611612 187.381373 
L 512.614598 186.301035 
L 513.617585 190.022927 
L 514.620572 194.839341 
L 515.623559 195.80459 
L 516.626545 190.135881 
L 518.632519 168.628764 
L 519.635506 163.594219 
L 520.638492 166.855678 
L 522.644466 187.000491 
L 523.647453 193.557527 
L 524.650439 194.075484 
L 525.653426 190.350824 
L 526.656413 186.122825 
L 527.6594 184.190498 
L 529.665373 184.864568 
L 530.66836 182.285727 
L 531.671347 175.791338 
L 533.67732 157.945681 
L 534.680307 151.750942 
L 535.683294 149.015041 
L 536.68628 149.139907 
L 537.689267 151.059733 
L 538.692254 154.093928 
L 539.695241 157.996685 
L 540.698227 162.364198 
L 541.701214 166.168648 
L 542.704201 168.04441 
L 543.707188 167.213611 
L 545.713161 161.07422 
L 546.716148 159.617081 
L 547.719134 160.719663 
L 548.722121 163.455267 
L 549.725108 165.826454 
L 550.728095 166.202687 
L 551.731081 164.461134 
L 552.734068 161.987456 
L 553.737055 160.55893 
L 554.740042 160.982884 
L 555.743028 162.548622 
L 556.746015 163.726333 
L 557.749002 163.579847 
L 558.751989 162.779363 
L 559.754975 163.352308 
L 560.757962 167.234092 
L 561.760949 174.614332 
L 562.763936 183.328209 
L 563.766922 189.888082 
L 564.769909 191.602297 
L 565.772896 188.379792 
L 566.775883 182.907147 
L 567.778869 178.926926 
L 568.781856 178.681959 
L 569.784843 181.286053 
L 570.78783 183.273551 
L 571.790816 181.115055 
L 572.793803 174.032403 
L 573.79679 165.06546 
L 574.799777 159.42444 
L 575.802763 161.054312 
L 576.80575 169.725976 
L 577.808737 180.78238 
L 578.811723 187.91047 
L 579.81471 187.159332 
L 580.817697 179.444302 
L 581.820684 169.764346 
L 582.82367 163.682999 
L 583.826657 163.607518 
L 585.832631 171.173297 
L 586.835617 171.513955 
L 587.838604 169.362763 
L 588.841591 168.501386 
L 589.844578 172.331389 
L 591.850551 188.964231 
L 592.853538 191.838384 
L 593.856525 186.652143 
L 595.862498 166.656681 
L 596.865485 164.218055 
L 597.868472 170.050528 
L 598.871458 179.928757 
L 599.874445 187.110523 
L 600.877432 186.967446 
L 601.880419 179.980558 
L 602.883405 171.10835 
L 603.886392 166.20605 
L 604.889379 168.188092 
L 605.892366 175.55762 
L 606.895352 183.993253 
L 607.898339 189.467578 
L 608.901326 190.526631 
L 609.904313 188.348217 
L 611.910286 181.932024 
L 614.919246 173.22385 
L 615.922233 172.226974 
L 616.92522 174.346577 
L 617.928206 179.607171 
L 618.931193 186.22817 
L 619.93418 191.47148 
L 620.937167 193.172429 
L 621.940153 190.845932 
L 622.94314 185.681101 
L 623.946127 179.635815 
L 624.949114 174.433829 
L 625.9521 171.115967 
L 626.955087 170.15525 
L 627.958074 171.663492 
L 628.961061 175.318144 
L 630.967034 184.672912 
L 631.970021 187.598927 
L 632.973008 188.74326 
L 633.975994 189.314464 
L 634.978981 191.100426 
L 635.981968 194.958853 
L 636.984955 199.765297 
L 637.987941 202.838834 
L 638.990928 201.806775 
L 639.993915 196.64687 
L 640.996902 190.272003 
L 641.999888 186.930698 
L 643.002875 189.314316 
L 645.008848 203.601448 
L 646.011835 205.709844 
L 647.014822 200.234187 
L 649.020795 179.102617 
L 650.023782 175.357779 
L 651.026769 180.836886 
L 652.029756 193.401782 
L 653.032742 207.657252 
L 654.035729 218.27281 
L 655.038716 222.845584 
L 656.041703 222.576142 
L 657.044689 220.650719 
L 658.047676 219.74987 
L 659.050663 220.492833 
L 660.05365 221.724056 
L 661.056636 222.118978 
L 662.059623 221.69445 
L 663.06261 222.030181 
L 664.065597 225.072788 
L 665.068583 231.462858 
L 666.07157 239.642344 
L 667.074557 246.412642 
L 668.077544 248.611986 
L 669.08053 244.875269 
L 670.083517 236.452075 
L 671.086504 226.682079 
L 672.089491 219.489705 
L 673.092477 217.699068 
L 674.095464 221.924969 
L 677.104424 246.56489 
L 678.107411 248.121681 
L 679.110398 244.184262 
L 679.110398 244.184262 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_61">
    <path d="M 177.617027 394.495 
L 177.617027 25.05375 
" clip-path="url(#p73e34baf4f)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-opacity: 0.5; stroke-width: 1.5"/>
   </g>
   <g id="patch_4">
    <path d="M 47.22875 394.495 
L 47.22875 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 709.2 394.495 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 47.22875 394.495 
L 709.2 394.495 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 47.22875 25.05375 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- test2阶段显著导联的平均HEP波形 -->
    <g transform="translate(303.214375 19.05375) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-74" d="M 2750 200 
Q 2625 150 2462 112 
Q 2300 75 2025 75 
Q 1575 75 1300 325 
Q 1025 575 1025 1025 
L 1025 2525 
L 175 2525 
L 175 2925 
L 1025 2925 
L 1025 3900 
L 1525 3900 
L 1525 2925 
L 2550 2925 
L 2550 2525 
L 1525 2525 
L 1525 1000 
Q 1525 800 1625 662 
Q 1725 525 2000 525 
Q 2275 525 2450 575 
Q 2625 625 2750 700 
L 2750 200 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-65" d="M 2850 1075 
Q 2800 625 2450 350 
Q 2100 75 1625 75 
Q 1025 75 637 462 
Q 250 850 250 1525 
Q 250 2200 637 2587 
Q 1025 2975 1625 2975 
Q 2150 2975 2487 2637 
Q 2825 2300 2825 1525 
L 800 1525 
Q 800 975 1037 750 
Q 1275 525 1625 525 
Q 1900 525 2075 662 
Q 2250 800 2300 1075 
L 2850 1075 
z
M 2250 1925 
Q 2200 2275 2025 2412 
Q 1850 2550 1575 2550 
Q 1325 2550 1125 2412 
Q 925 2275 825 1925 
L 2250 1925 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-9636" d="M 2450 4850 
Q 2350 4575 2175 3962 
Q 2000 3350 1825 2800 
Q 2225 2225 2312 1912 
Q 2400 1600 2375 1250 
Q 2350 900 2137 725 
Q 1925 550 1400 475 
Q 1300 925 1200 1075 
Q 1725 1025 1837 1225 
Q 1950 1425 1875 1775 
Q 1800 2125 1350 2725 
Q 1700 3825 1825 4400 
L 975 4400 
L 975 -500 
L 500 -500 
Q 525 175 525 675 
L 525 3650 
Q 525 4225 500 4850 
L 2450 4850 
z
M 4250 4825 
Q 4600 4100 5187 3637 
Q 5775 3175 6200 3025 
Q 5950 2775 5800 2500 
Q 4975 3075 4600 3512 
Q 4225 3950 4025 4400 
Q 3850 4025 3512 3487 
Q 3175 2950 2625 2350 
Q 2425 2575 2175 2700 
Q 2700 3075 3150 3787 
Q 3600 4500 3850 5275 
Q 4050 5150 4375 5075 
Q 4275 4900 4250 4825 
z
M 3750 2700 
Q 3650 2325 3575 1512 
Q 3500 700 3250 225 
Q 3000 -250 2650 -575 
Q 2425 -400 2175 -300 
Q 2550 -25 2762 337 
Q 2975 700 3087 1200 
Q 3200 1700 3200 2800 
Q 3350 2725 3750 2700 
z
M 5025 2775 
Q 4975 2425 4975 2025 
L 4975 350 
Q 4975 100 5000 -525 
L 4475 -525 
Q 4500 150 4500 375 
L 4500 2025 
Q 4500 2375 4475 2775 
L 5025 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6bb5" d="M 5500 2475 
Q 5350 1775 5100 1237 
Q 4850 700 4600 400 
Q 4825 225 5250 87 
Q 5675 -50 6125 -125 
Q 5950 -275 5825 -650 
Q 5150 -450 4825 -287 
Q 4500 -125 4225 75 
Q 3825 -175 3450 -337 
Q 3075 -500 2625 -650 
Q 2525 -400 2325 -225 
Q 2675 -150 3100 0 
Q 3525 150 3925 425 
Q 3725 675 3525 1150 
Q 3325 1600 3225 2075 
Q 3050 2075 2900 2075 
L 2900 2500 
Q 3375 2475 3800 2475 
L 5500 2475 
z
M 2650 4625 
Q 2300 4550 1887 4475 
Q 1475 4400 1300 4375 
L 1300 3550 
Q 2050 3550 2525 3600 
L 2525 3125 
Q 2050 3150 1300 3150 
L 1300 2350 
Q 2050 2350 2525 2375 
L 2525 1900 
Q 2050 1925 1300 1925 
L 1300 975 
Q 1950 1075 2625 1250 
Q 2600 1000 2600 775 
Q 1900 675 1300 525 
L 1300 100 
Q 1300 -200 1325 -625 
L 800 -625 
Q 825 -200 825 100 
L 825 425 
Q 575 375 350 300 
Q 300 650 250 825 
Q 500 850 825 900 
L 825 3800 
Q 825 4225 800 4725 
Q 1100 4725 1575 4825 
Q 2050 4925 2375 5125 
Q 2475 4850 2650 4625 
z
M 5100 4975 
Q 5075 4600 5075 4275 
L 5075 3650 
Q 5075 3425 5237 3387 
Q 5400 3350 5975 3400 
Q 5875 3225 5850 2950 
L 5075 2950 
Q 4650 2950 4625 3375 
L 4625 4575 
L 3775 4575 
Q 3775 3925 3675 3525 
Q 3575 3125 3200 2725 
Q 3025 2900 2800 3050 
Q 3275 3400 3300 3937 
Q 3325 4475 3300 4975 
L 5100 4975 
z
M 3675 2100 
Q 3775 1675 3962 1287 
Q 4150 900 4275 775 
Q 4450 950 4625 1312 
Q 4800 1675 4900 2100 
L 3800 2100 
L 3675 2100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-663e" d="M 1150 2400 
Q 1175 2975 1175 3700 
Q 1175 4450 1150 4925 
L 5275 4925 
Q 5250 4400 5250 3675 
Q 5250 2975 5275 2400 
L 1150 2400 
z
M 4175 2275 
Q 4150 1750 4150 1075 
L 4150 25 
Q 5525 25 6050 50 
L 6050 -425 
Q 5525 -400 5125 -400 
L 1400 -400 
Q 850 -400 400 -425 
L 400 50 
Q 875 25 2200 25 
L 2200 1050 
Q 2200 1750 2175 2275 
L 2725 2275 
Q 2700 1750 2700 1050 
L 2700 25 
L 3650 25 
L 3650 1075 
Q 3650 1750 3625 2275 
L 4175 2275 
z
M 4750 3850 
L 4750 4500 
L 1675 4500 
L 1675 3850 
L 4750 3850 
z
M 4750 2850 
L 4750 3400 
L 1675 3400 
L 1675 2850 
L 4750 2850 
z
M 650 1625 
Q 900 1725 1100 1900 
Q 1625 1075 1900 500 
Q 1625 375 1425 225 
Q 1200 750 650 1625 
z
M 5750 1575 
Q 5650 1475 5425 1062 
Q 5200 650 4875 200 
Q 4700 350 4450 475 
Q 5000 1250 5225 1900 
Q 5475 1675 5750 1575 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8457" d="M 5225 1650 
Q 5200 1250 5200 550 
Q 5200 -150 5225 -550 
L 4700 -550 
L 4700 -300 
L 2050 -300 
L 2050 -600 
L 1525 -600 
Q 1550 -175 1550 975 
Q 1000 750 575 575 
Q 500 800 225 1025 
Q 775 1075 1550 1400 
L 1550 1650 
L 2250 1650 
Q 2675 1850 3125 2125 
L 1225 2125 
Q 775 2125 300 2100 
L 300 2550 
Q 775 2525 1200 2525 
L 2750 2525 
L 2750 3000 
L 2125 3000 
Q 1575 3000 1175 2975 
L 1175 3400 
Q 1575 3375 2100 3375 
L 2750 3375 
Q 2750 3575 2725 3825 
L 3275 3825 
Q 3250 3575 3250 3375 
Q 3775 3375 4275 3400 
L 4275 3025 
Q 4550 3300 4675 3450 
Q 4800 3600 4925 3800 
Q 5150 3625 5425 3500 
Q 5275 3425 5087 3237 
Q 4900 3050 4350 2525 
L 5300 2525 
Q 5750 2525 6150 2550 
L 6150 2100 
Q 5775 2125 5325 2125 
L 3850 2125 
Q 3500 1850 3100 1650 
L 5225 1650 
z
M 2475 5100 
Q 2450 4875 2450 4625 
L 3850 4625 
Q 3850 4800 3825 5100 
L 4375 5100 
Q 4350 4850 4350 4625 
L 4975 4625 
Q 5475 4625 6000 4650 
L 6000 4225 
Q 5525 4250 4975 4250 
L 4350 4250 
Q 4350 4100 4375 3775 
L 3850 3775 
L 3850 4250 
L 2450 4250 
L 2450 3725 
L 1925 3725 
Q 1950 4025 1950 4250 
L 1425 4250 
Q 875 4250 375 4225 
L 375 4650 
Q 875 4625 1425 4625 
L 1950 4625 
Q 1950 4825 1925 5100 
L 2475 5100 
z
M 4700 850 
L 4700 1275 
L 2050 1275 
L 2050 850 
L 4700 850 
z
M 4700 75 
L 4700 475 
L 2050 475 
L 2050 75 
L 4700 75 
z
M 3700 2525 
Q 3925 2700 4225 2975 
Q 3800 3000 3250 3000 
L 3250 2525 
L 3700 2525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5bfc" d="M 5125 5100 
Q 5100 4850 5100 4350 
Q 5100 3875 5125 3550 
L 1600 3550 
L 1600 3200 
Q 1600 2850 2050 2850 
L 4725 2850 
Q 5100 2850 5212 2950 
Q 5325 3050 5350 3500 
Q 5600 3300 5925 3200 
Q 5700 2625 5575 2525 
Q 5450 2425 5075 2425 
L 1675 2425 
Q 1100 2425 1100 3025 
L 1100 4450 
Q 1100 4775 1050 5100 
L 5125 5100 
z
M 4200 1725 
Q 4200 1950 4175 2300 
L 4725 2300 
Q 4700 1900 4700 1725 
L 5200 1725 
Q 5575 1725 6100 1750 
L 6100 1300 
Q 5575 1325 5200 1325 
L 4700 1325 
L 4700 225 
Q 4700 -225 4450 -375 
Q 4200 -525 3550 -650 
Q 3475 -325 3325 -50 
Q 3975 -50 4087 25 
Q 4200 100 4200 300 
L 4200 1325 
L 1425 1325 
Q 900 1325 350 1300 
L 350 1750 
Q 850 1725 1400 1725 
L 4200 1725 
z
M 4625 3975 
L 4625 4700 
L 1600 4700 
L 1600 3975 
L 4625 3975 
z
M 1625 1150 
Q 2000 725 2375 125 
Q 2100 -50 1925 -175 
Q 1650 325 1250 825 
Q 1450 950 1625 1150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8054" d="M 2325 4850 
Q 2625 4850 2975 4875 
L 2975 4450 
Q 2700 4475 2475 4475 
L 2475 1150 
Q 2750 1225 2975 1275 
Q 2925 950 2950 850 
Q 2650 800 2475 750 
L 2475 175 
Q 2475 -200 2500 -675 
L 1975 -675 
Q 2025 -200 2025 175 
L 2025 650 
Q 1750 600 1337 475 
Q 925 350 450 175 
Q 425 350 275 750 
Q 425 750 800 825 
L 800 4475 
Q 550 4475 325 4425 
L 325 4875 
Q 625 4850 950 4850 
L 2325 4850 
z
M 5450 4950 
Q 5250 4675 5162 4487 
Q 5075 4300 4850 3900 
Q 5375 3900 5825 3925 
L 5825 3475 
Q 5375 3525 5050 3525 
L 4550 3525 
Q 4525 2600 4500 2225 
L 5225 2225 
Q 5525 2225 5950 2250 
L 5950 1800 
Q 5525 1825 5225 1825 
L 4575 1825 
Q 4725 1150 5175 625 
Q 5625 100 6175 -25 
Q 6000 -175 5912 -312 
Q 5825 -450 5800 -625 
Q 5275 -250 4925 200 
Q 4575 650 4350 1325 
Q 4175 750 3900 275 
Q 3625 -200 3200 -675 
Q 3125 -575 2975 -450 
Q 2825 -325 2750 -325 
Q 3100 -100 3412 387 
Q 3725 875 3825 1187 
Q 3925 1500 3975 1825 
L 3675 1825 
Q 3275 1825 2825 1800 
L 2825 2250 
Q 3275 2225 3675 2225 
L 4025 2225 
Q 4075 2950 4075 3525 
L 3825 3525 
Q 3475 3525 3000 3475 
L 3000 3925 
Q 3475 3900 3825 3900 
L 4450 3900 
Q 4750 4600 4900 5225 
Q 5050 5125 5450 4950 
z
M 2025 2225 
L 2025 3200 
L 1250 3200 
L 1250 2225 
L 2025 2225 
z
M 2025 3600 
L 2025 4475 
L 1250 4475 
L 1250 3600 
L 2025 3600 
z
M 2025 1825 
L 1250 1825 
L 1250 900 
Q 1525 950 2025 1050 
L 2025 1825 
z
M 3500 5175 
Q 3950 4475 4075 4250 
Q 3775 4125 3675 4000 
Q 3550 4300 3150 4950 
Q 3225 4975 3500 5175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7684" d="M 2100 4975 
Q 1975 4750 1750 3950 
L 2875 3950 
Q 2850 3525 2850 2875 
L 2850 675 
Q 2850 300 2875 -400 
L 2375 -400 
L 2375 100 
L 1100 100 
L 1100 -450 
L 600 -450 
Q 625 350 625 550 
L 625 2850 
Q 625 3525 600 3950 
L 1325 3950 
Q 1450 4525 1525 5150 
Q 1875 5025 2100 4975 
z
M 5825 3075 
Q 5800 1550 5700 175 
Q 5650 -275 5225 -425 
Q 4800 -575 4275 -625 
Q 4250 -350 4050 -50 
Q 4550 -75 4837 -25 
Q 5125 25 5200 200 
Q 5275 375 5312 1225 
Q 5350 2075 5375 3600 
L 3925 3600 
Q 3725 3125 3350 2475 
Q 3175 2650 2925 2750 
Q 3150 3050 3350 3475 
Q 3550 3900 3700 4400 
Q 3850 4900 3875 5175 
Q 4225 5025 4475 4950 
Q 4350 4750 4262 4512 
Q 4175 4275 4075 4050 
L 5850 4050 
Q 5825 3525 5825 3075 
z
M 2375 525 
L 2375 1850 
L 1100 1850 
L 1100 525 
L 2375 525 
z
M 2375 2275 
L 2375 3525 
L 1100 3525 
L 1100 2275 
L 2375 2275 
z
M 4350 2050 
Q 4500 1750 4700 1275 
Q 4525 1200 4225 1025 
Q 4050 1500 3900 1837 
Q 3750 2175 3600 2400 
Q 3775 2500 4050 2650 
L 4350 2050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5e73" d="M 4525 4825 
Q 4900 4825 5450 4850 
L 5450 4375 
Q 4925 4400 4525 4400 
L 3475 4400 
L 3475 2025 
L 5000 2025 
Q 5550 2025 6025 2050 
L 6025 1575 
Q 5550 1600 5000 1600 
L 3475 1600 
L 3475 450 
Q 3475 -100 3500 -600 
L 2925 -600 
Q 2950 -100 2950 450 
L 2950 1600 
L 1125 1600 
Q 775 1600 375 1575 
L 375 2050 
Q 775 2025 1175 2025 
L 2950 2025 
L 2950 4400 
L 1775 4400 
Q 1400 4400 875 4375 
L 875 4850 
Q 1400 4825 1750 4825 
L 4525 4825 
z
M 3925 2600 
Q 4500 3300 4900 4125 
Q 5150 3950 5400 3825 
Q 4775 2850 4350 2300 
Q 4150 2475 3925 2600 
z
M 1975 2350 
Q 1600 3100 1050 3725 
Q 1250 3875 1450 4050 
Q 2075 3300 2450 2725 
Q 2150 2500 1975 2350 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5747" d="M 5775 275 
Q 5775 -325 5362 -450 
Q 4950 -575 4425 -600 
Q 4375 -275 4250 25 
Q 4950 -25 5125 62 
Q 5300 150 5300 500 
L 5375 3700 
L 3550 3700 
Q 3425 3375 3262 3037 
Q 3100 2700 2925 2400 
Q 2675 2550 2475 2625 
Q 3150 3625 3500 5200 
Q 3800 5100 4075 5025 
Q 3975 4875 3875 4650 
Q 3775 4425 3675 4125 
L 5900 4125 
L 5775 275 
z
M 1250 3625 
L 1250 4375 
Q 1250 4775 1225 5100 
L 1775 5100 
Q 1750 4725 1750 4375 
L 1750 3625 
L 1900 3625 
Q 2200 3625 2525 3650 
L 2525 3150 
Q 2200 3175 1900 3175 
L 1750 3175 
L 1750 1100 
L 2500 1375 
Q 2475 1125 2525 900 
Q 1225 475 962 362 
Q 700 250 475 150 
Q 375 475 250 750 
Q 575 800 800 850 
Q 1025 900 1250 975 
L 1250 3175 
L 1125 3175 
Q 850 3175 400 3150 
L 400 3650 
Q 850 3625 1100 3625 
L 1250 3625 
z
M 4950 1625 
Q 4675 1425 4137 1050 
Q 3600 675 3175 300 
Q 3000 575 2850 775 
Q 3350 1050 3762 1325 
Q 4175 1600 4775 2075 
Q 4825 1850 4950 1625 
z
M 3775 3150 
Q 4125 2800 4475 2300 
Q 4250 2125 4100 1975 
Q 3900 2325 3450 2825 
Q 3600 3000 3775 3150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-48" d="M 2850 125 
L 2275 125 
L 2275 2125 
L 850 2125 
L 850 125 
L 275 125 
L 275 4400 
L 850 4400 
L 850 2600 
L 2275 2600 
L 2275 4400 
L 2850 4400 
L 2850 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-45" d="M 2900 125 
L 350 125 
L 350 4400 
L 2775 4400 
L 2775 3925 
L 925 3925 
L 925 2600 
L 2625 2600 
L 2625 2125 
L 925 2125 
L 925 600 
L 2900 600 
L 2900 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-50" d="M 2950 3100 
Q 2950 2500 2600 2150 
Q 2250 1800 1625 1800 
L 900 1800 
L 900 125 
L 325 125 
L 325 4400 
L 1625 4400 
Q 2250 4400 2600 4050 
Q 2950 3700 2950 3100 
z
M 2375 3100 
Q 2375 3575 2150 3750 
Q 1925 3925 1475 3925 
L 900 3925 
L 900 2275 
L 1475 2275 
Q 1925 2275 2150 2450 
Q 2375 2625 2375 3100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6ce2" d="M 5075 3275 
Q 5175 3600 5225 3850 
L 4325 3850 
L 4325 2750 
L 5550 2750 
Q 5400 2100 5125 1512 
Q 4850 925 4525 550 
Q 4775 350 5200 175 
Q 5625 0 6175 -100 
Q 5900 -300 5775 -600 
Q 5250 -425 4800 -175 
Q 4350 75 4200 225 
Q 3950 25 3537 -212 
Q 3125 -450 2500 -675 
Q 2375 -500 2050 -375 
Q 2525 -250 3062 25 
Q 3600 300 3850 550 
Q 3325 1300 3075 2325 
L 2825 2325 
Q 2800 1400 2487 725 
Q 2175 50 1875 -300 
Q 1700 -150 1375 -50 
Q 1750 325 1950 712 
Q 2150 1100 2262 1475 
Q 2375 1850 2387 2825 
Q 2400 3800 2350 4275 
L 3875 4275 
Q 3875 4850 3850 5200 
L 4350 5200 
Q 4325 4900 4325 4275 
L 5850 4275 
Q 5825 4125 5750 3837 
Q 5675 3550 5600 3200 
Q 5300 3250 5075 3275 
z
M 3525 2325 
Q 3800 1400 4200 925 
Q 4650 1475 4925 2325 
L 3525 2325 
z
M 1950 1700 
Q 1700 1375 1262 712 
Q 825 50 675 -200 
Q 475 50 225 275 
Q 525 525 937 1100 
Q 1350 1675 1550 2100 
L 1950 1700 
z
M 3875 2750 
L 3875 3850 
L 2850 3850 
L 2850 2750 
L 3875 2750 
z
M 925 4925 
Q 1600 4525 2000 4250 
Q 1800 4025 1675 3800 
Q 1250 4150 625 4500 
Q 775 4700 925 4925 
z
M 600 3525 
Q 1125 3250 1725 2850 
Q 1525 2675 1400 2425 
Q 925 2825 350 3100 
Q 450 3275 600 3525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5f62" d="M 2750 4850 
Q 3350 4850 3675 4875 
L 3675 4425 
Q 3400 4450 2950 4450 
L 2950 2700 
Q 3475 2700 3725 2725 
L 3725 2250 
Q 3500 2275 2950 2275 
L 2950 500 
Q 2950 -50 2975 -350 
L 2475 -350 
Q 2500 0 2500 475 
L 2500 2275 
L 1650 2275 
Q 1600 1075 1325 437 
Q 1050 -200 825 -500 
Q 550 -300 300 -250 
Q 700 150 937 762 
Q 1175 1375 1200 2275 
Q 775 2275 425 2250 
L 425 2725 
Q 775 2700 1225 2700 
L 1225 4450 
Q 800 4450 600 4425 
L 600 4875 
Q 800 4850 1375 4850 
L 2750 4850 
z
M 6100 1275 
Q 5850 1050 5150 462 
Q 4450 -125 3800 -525 
Q 3650 -300 3400 -100 
Q 4050 175 4725 750 
Q 5400 1325 5625 1650 
Q 5800 1450 6100 1275 
z
M 2500 2700 
L 2500 4450 
L 1675 4450 
L 1675 2700 
L 2500 2700 
z
M 5875 2975 
Q 5650 2825 5187 2300 
Q 4725 1775 3900 1175 
Q 3750 1375 3475 1550 
Q 4175 1950 4625 2412 
Q 5075 2875 5375 3325 
Q 5700 3050 5875 2975 
z
M 5675 4800 
Q 5425 4575 5062 4087 
Q 4700 3600 4075 2975 
Q 3850 3150 3625 3275 
Q 4175 3750 4475 4125 
Q 4775 4500 5125 5100 
Q 5250 5025 5675 4800 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-74"/>
     <use xlink:href="#SimHei-65" x="50"/>
     <use xlink:href="#SimHei-73" x="100"/>
     <use xlink:href="#SimHei-74" x="150"/>
     <use xlink:href="#SimHei-32" x="200"/>
     <use xlink:href="#SimHei-9636" x="250"/>
     <use xlink:href="#SimHei-6bb5" x="350"/>
     <use xlink:href="#SimHei-663e" x="450"/>
     <use xlink:href="#SimHei-8457" x="550"/>
     <use xlink:href="#SimHei-5bfc" x="650"/>
     <use xlink:href="#SimHei-8054" x="750"/>
     <use xlink:href="#SimHei-7684" x="850"/>
     <use xlink:href="#SimHei-5e73" x="950"/>
     <use xlink:href="#SimHei-5747" x="1050"/>
     <use xlink:href="#SimHei-48" x="1150"/>
     <use xlink:href="#SimHei-45" x="1200"/>
     <use xlink:href="#SimHei-50" x="1250"/>
     <use xlink:href="#SimHei-6ce2" x="1300"/>
     <use xlink:href="#SimHei-5f62" x="1400"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_8">
     <path d="M 630 42.89125 
L 703.6 42.89125 
Q 705.2 42.89125 705.2 41.29125 
L 705.2 30.65375 
Q 705.2 29.05375 703.6 29.05375 
L 630 29.05375 
Q 628.4 29.05375 628.4 30.65375 
L 628.4 41.29125 
Q 628.4 42.89125 630 42.89125 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_62">
     <path d="M 631.6 35.89125 
L 639.6 35.89125 
L 647.6 35.89125 
" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_16">
     <!-- 所有被试平均 -->
     <g transform="translate(654 38.69125) scale(0.08 -0.08)">
      <defs>
       <path id="SimHei-6240" d="M 2925 4450 
Q 2650 4425 1375 4250 
L 1375 3375 
L 2725 3375 
Q 2700 2800 2700 2225 
Q 2700 1675 2725 1325 
L 1350 1325 
Q 1325 875 1175 425 
Q 1025 -25 775 -525 
Q 575 -325 325 -275 
Q 725 325 812 1050 
Q 900 1775 900 2675 
Q 900 3575 875 4575 
Q 2225 4725 2650 4950 
Q 2750 4725 2925 4450 
z
M 5825 4425 
Q 5475 4400 5000 4337 
Q 4525 4275 3800 4225 
L 3800 3025 
L 5225 3025 
Q 5575 3025 5975 3050 
L 5975 2600 
Q 5575 2625 5200 2625 
L 5200 400 
Q 5200 -100 5225 -500 
L 4700 -500 
Q 4725 -50 4725 400 
L 4725 2625 
L 3800 2625 
Q 3750 1625 3600 1062 
Q 3450 500 3250 125 
Q 3050 -250 2725 -625 
Q 2550 -425 2300 -325 
Q 2725 75 2975 612 
Q 3225 1150 3287 1812 
Q 3350 2475 3350 3125 
Q 3350 3800 3325 4600 
Q 3700 4600 4425 4687 
Q 5150 4775 5575 4950 
Q 5700 4675 5825 4425 
z
M 2275 1750 
L 2275 2975 
L 1375 2975 
L 1375 1750 
L 2275 1750 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6709" d="M 275 1625 
Q 850 1975 1400 2537 
Q 1950 3100 2400 3925 
L 1500 3925 
Q 1150 3925 625 3900 
L 625 4375 
Q 1275 4350 1600 4350 
L 2550 4350 
Q 2750 4950 2775 5225 
Q 3150 5125 3375 5075 
Q 3300 5000 3125 4350 
L 5150 4350 
Q 5575 4350 6025 4375 
L 6025 3900 
Q 5575 3925 5225 3925 
L 2975 3925 
Q 2800 3600 2600 3275 
L 5475 3275 
Q 5450 2650 5450 2150 
L 5450 125 
Q 5450 -225 5250 -387 
Q 5050 -550 4400 -600 
Q 4375 -325 4225 0 
Q 4600 -50 4775 -25 
Q 4950 0 4950 250 
L 4950 750 
L 2350 750 
L 2350 -575 
L 1800 -575 
Q 1825 50 1825 500 
L 1825 2275 
Q 1300 1675 725 1225 
Q 625 1400 275 1625 
z
M 4950 2175 
L 4950 2875 
L 2350 2875 
L 2350 2175 
L 4950 2175 
z
M 4950 1150 
L 4950 1775 
L 2350 1775 
L 2350 1150 
L 4950 1150 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-88ab" d="M 4100 4175 
Q 4100 4800 4075 5175 
L 4575 5175 
Q 4550 4825 4550 4175 
L 6025 4175 
Q 5900 3825 5725 3225 
Q 5550 3300 5225 3325 
Q 5375 3675 5400 3800 
L 4550 3800 
L 4550 2650 
L 5575 2650 
Q 5425 2075 5225 1575 
Q 5025 1075 4675 550 
Q 4925 325 5375 150 
Q 5825 -25 6125 -50 
Q 5850 -300 5775 -550 
Q 5325 -375 5000 -187 
Q 4675 0 4400 250 
Q 3975 -200 3225 -625 
Q 3050 -350 2850 -250 
Q 3575 0 4100 575 
Q 3675 1125 3400 2275 
L 3275 2275 
Q 3250 1575 3087 925 
Q 2925 275 2350 -550 
Q 2175 -400 1875 -300 
Q 2375 225 2600 825 
Q 2825 1425 2850 2150 
L 2850 3375 
Q 2850 3900 2825 4175 
L 4100 4175 
z
M 1825 2325 
Q 2075 2600 2300 2950 
Q 2500 2725 2650 2600 
Q 2425 2400 2075 2050 
L 2550 1575 
Q 2400 1450 2200 1225 
Q 1975 1575 1575 1975 
L 1575 525 
Q 1575 0 1600 -625 
L 1125 -625 
Q 1150 175 1150 700 
L 1150 2075 
Q 900 1800 525 1475 
Q 400 1675 200 1850 
Q 675 2175 1037 2600 
Q 1400 3025 1650 3525 
L 1150 3525 
Q 800 3525 400 3500 
L 400 3925 
Q 950 3900 1300 3900 
L 2350 3900 
Q 1950 3075 1550 2550 
Q 1650 2475 1825 2325 
z
M 4100 2650 
L 4100 3800 
L 3275 3800 
L 3275 2650 
L 4100 2650 
z
M 3825 2275 
Q 4050 1375 4400 900 
Q 4600 1200 4737 1512 
Q 4875 1825 5000 2275 
L 3825 2275 
z
M 1500 5250 
Q 1750 4800 1950 4375 
L 1500 4150 
Q 1325 4650 1075 5025 
Q 1300 5100 1500 5250 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-8bd5" d="M 3950 3825 
Q 3925 4325 3925 4550 
Q 3925 4800 3900 5225 
Q 4200 5150 4450 5125 
Q 4425 4825 4425 4600 
L 4425 3825 
L 5150 3825 
Q 5500 3825 5875 3850 
L 5875 3400 
Q 5525 3425 5125 3425 
L 4450 3425 
Q 4500 2750 4587 2212 
Q 4675 1675 4837 1112 
Q 5000 550 5212 225 
Q 5425 -100 5500 200 
Q 5575 500 5600 900 
Q 5800 625 6100 525 
Q 5975 -275 5750 -487 
Q 5525 -700 5250 -537 
Q 4975 -375 4737 62 
Q 4500 500 4337 1075 
Q 4175 1650 4100 2262 
Q 4025 2875 3975 3425 
L 2800 3425 
Q 2300 3425 1875 3400 
L 1875 3850 
Q 2275 3825 2775 3825 
L 3950 3825 
z
M 1500 3175 
Q 1475 2675 1475 2175 
L 1475 625 
Q 1825 950 2125 1225 
Q 2225 1000 2375 800 
Q 1900 425 1125 -350 
Q 1000 -125 825 50 
Q 1025 175 1025 525 
L 1025 2725 
Q 625 2725 300 2700 
L 300 3200 
Q 625 3175 825 3175 
L 1500 3175 
z
M 3775 2175 
Q 3500 2200 3250 2200 
L 3250 575 
Q 3575 675 3900 800 
Q 3900 525 3950 325 
Q 3575 250 3150 125 
Q 2725 0 2275 -175 
Q 2200 100 2025 300 
Q 2425 350 2800 475 
L 2800 2200 
Q 2525 2200 2225 2175 
L 2225 2650 
Q 2600 2625 3000 2625 
Q 3425 2625 3775 2650 
L 3775 2175 
z
M 925 4975 
Q 1275 4650 1700 4100 
Q 1525 3925 1325 3775 
Q 1175 4050 575 4650 
Q 775 4825 925 4975 
z
M 5150 5150 
Q 5325 5000 5850 4375 
Q 5575 4200 5425 4050 
Q 5125 4525 4775 4875 
Q 4925 4975 5150 5150 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-6240"/>
      <use xlink:href="#SimHei-6709" x="100"/>
      <use xlink:href="#SimHei-88ab" x="200"/>
      <use xlink:href="#SimHei-8bd5" x="300"/>
      <use xlink:href="#SimHei-5e73" x="400"/>
      <use xlink:href="#SimHei-5747" x="500"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p73e34baf4f">
   <rect x="47.22875" y="25.05375" width="661.97125" height="369.44125"/>
  </clipPath>
 </defs>
</svg>

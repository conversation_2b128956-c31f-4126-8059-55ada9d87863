#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 压力趋势预测模型

功能：
- 建立专门针对实验阶段压力变化趋势的预测模型
- 考虑静息态和刺激态的非线性压力变化规律
- 提供更精确的模型输入设计和趋势预测

作者：AI助手
日期：2025年5月22日
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import seaborn as sns
from tqdm import tqdm
import h5py
import glob
import nolds
import mne

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_pressure_trend_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-PressureTrend")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

# 定义实验阶段顺序
STAGE_ORDER = ['rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

class StageSequenceDataset(Dataset):
    """实验阶段序列数据集，专为DFA回归设计"""
    def __init__(self, eeg_features, ecg_features, dfa_labels, sequence_length=3):
        self.eeg_features = torch.FloatTensor(eeg_features)
        self.ecg_features = torch.FloatTensor(ecg_features)
        self.dfa_labels = torch.FloatTensor(dfa_labels)
        self.sequence_length = sequence_length
        self.n_samples = len(eeg_features) - sequence_length
    def __len__(self):
        return self.n_samples
    def __getitem__(self, idx):
        eeg_seq = self.eeg_features[idx:idx+self.sequence_length]
        ecg_seq = self.ecg_features[idx:idx+self.sequence_length]
        features_seq = torch.cat([eeg_seq, ecg_seq], dim=1)
        # 预测目标为下一个片段的DFA
        target = self.dfa_labels[idx+self.sequence_length]
        return features_seq, target

class PressureTrendCausalModel(nn.Module):
    """
    压力趋势因果预测模型
    
    功能：
    - 预测下一阶段的压力水平
    - 捕捉EEG和ECG特征与压力变化之间的因果关系
    - 考虑实验阶段的序列关系
    """
    
    def __init__(self, input_dim, hidden_dim=128, output_dim=3, num_layers=2, dropout=0.2):
        """
        初始化模型
        
        参数:
        input_dim (int): 输入特征维度
        hidden_dim (int): 隐藏层维度
        output_dim (int): 输出维度 (默认为3: 压力水平, EEG预测, ECG预测)
        num_layers (int): LSTM层数
        dropout (float): Dropout比例
        """
        super(PressureTrendCausalModel, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        
        # 输入特征编码
        self.feature_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 主干网络 - 双向LSTM以捕获时间依赖
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 自注意力机制捕捉长距离依赖
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim * 2,  # 双向LSTM输出
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 压力预测头
        self.pressure_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
        # EEG预测头
        self.eeg_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
        # ECG预测头
        self.ecg_head = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1)
        )
        
        # 保存注意力权重用于可视化和因果分析
        self.attn_weights = None
        
    def forward(self, x):
        """
        前向传播
        
        参数:
        x (tensor): 输入特征序列，形状为 [batch_size, seq_len, input_dim]
        
        返回:
        tensor: 预测输出，形状为 [batch_size, output_dim]
        """
        batch_size, seq_len, _ = x.size()
        
        # 特征编码
        x = self.feature_encoder(x)
        
        # LSTM编码
        lstm_out, _ = self.lstm(x)
        
        # 自注意力机制
        attn_out, attn_weights = self.attention(lstm_out, lstm_out, lstm_out)
        self.attn_weights = attn_weights  # 保存注意力权重用于分析
        
        # 使用最后一个时间步的隐状态进行预测
        final_hidden = attn_out[:, -1, :]
        
        # 多头预测
        pressure_pred = self.pressure_head(final_hidden)
        eeg_pred = self.eeg_head(final_hidden)
        ecg_pred = self.ecg_head(final_hidden)
        
        # 组合输出
        output = torch.cat([pressure_pred, eeg_pred, ecg_pred], dim=1)
        
        return output

def prepare_stage_data(data_directory, feature_extractors=None):
    """
    准备按实验阶段组织的数据
    
    参数:
    data_directory (str): 数据目录路径
    feature_extractors (dict): 特征提取器字典
    
    返回:
    dict: 按阶段组织的特征数据
    """
    logger.info(f"正在从 {data_directory} 准备阶段数据...")
    
    # TODO: 实现从数据文件中提取特征并按阶段组织
    # 当前使用模拟数据进行测试
    
    # 模拟每个阶段的数据
    stage_data = {}
    
    for stage in STAGE_ORDER:
        # 模拟每个阶段的特征数据，真实实现中应从文件加载
        n_samples = 20
        eeg_features = np.random.random((n_samples, 64))
        ecg_features = np.random.random((n_samples, 16))
        
        # 应用压力趋势影响，使特征模拟实际情况
        pressure_factor = PRESSURE_TREND[stage]
        eeg_features *= (1 + 0.2 * pressure_factor)
        ecg_features *= (1 + 0.5 * pressure_factor)
        
        stage_data[stage] = {
            'eeg_features': eeg_features,
            'ecg_features': ecg_features
        }
    
    logger.info(f"成功准备 {len(stage_data)} 个阶段的数据")
    return stage_data

def combine_stage_data(stage_data):
    """
    将各阶段数据按顺序组合，用于序列建模
    返回: (combined_eeg, combined_ecg, stages, ecg_segments)
    """
    combined_eeg = []
    combined_ecg = []
    stages = []
    ecg_segments = []
    for stage in STAGE_ORDER:
        if stage in stage_data:
            eeg_features = stage_data[stage]['eeg_features']
            ecg_features = stage_data[stage]['ecg_features']
            # 假设ecg_features的每一行是一个片段的特征，原始信号片段需在数据准备阶段传入
            combined_eeg.append(eeg_features)
            combined_ecg.append(ecg_features)
            stages.extend([stage] * len(eeg_features))
            # 假设原始信号片段保存在'signal_segments'字段
            if 'ecg_segments' in stage_data[stage]:
                ecg_segments.append(stage_data[stage]['ecg_segments'])
    combined_eeg = np.vstack(combined_eeg)
    combined_ecg = np.vstack(combined_ecg)
    if ecg_segments:
        ecg_segments = np.vstack(ecg_segments)
    else:
        ecg_segments = None
    return combined_eeg, combined_ecg, stages, ecg_segments

def visualize_pressure_predictions(true_pressure, pred_pressure, stages, output_dir=None):
    """
    可视化压力趋势预测结果
    
    参数:
    true_pressure (np.array): 真实压力值
    pred_pressure (np.array): 预测压力值
    stages (list): 对应的实验阶段
    output_dir (str): 输出目录
    """
    plt.figure(figsize=(12, 8))
    
    # 创建阶段索引
    stage_indices = {}
    current_idx = 0
    for stage in STAGE_ORDER:
        stage_count = stages.count(stage)
        if stage_count > 0:
            stage_indices[stage] = (current_idx, current_idx + stage_count)
            current_idx += stage_count
    
    # 绘制阶段背景区域
    colors = ['#f0f0f0', '#e6f3ff', '#f0f0f0', '#e6f3ff', '#f0f0f0', '#e6f3ff']
    for i, stage in enumerate(STAGE_ORDER):
        if stage in stage_indices:
            start, end = stage_indices[stage]
            plt.axvspan(start, end, color=colors[i], alpha=0.3)
            mid_point = (start + end) / 2
            plt.text(mid_point, 1.1, stage, ha='center', fontproperties=chinese_font, fontsize=10)
    
    # 绘制真实值和预测值
    plt.plot(true_pressure, 'bo-', label='真实压力趋势', markersize=4, alpha=0.7)
    plt.plot(pred_pressure, 'ro-', label='预测压力趋势', markersize=4, alpha=0.7)
    
    # 添加标签和图例
    plt.xlabel('样本索引', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('压力水平', fontproperties=chinese_font, fontsize=10)
    plt.title('压力趋势预测结果比较', fontproperties=chinese_font, fontsize=10)
    plt.legend(prop=chinese_font)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整Y轴范围，使差异更明显
    y_min, y_max = min(min(true_pressure), min(pred_pressure)), max(max(true_pressure), max(pred_pressure))
    margin = (y_max - y_min) * 0.1
    plt.ylim([y_min - margin, y_max + margin])
    
    # 保存图像
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'pressure_trend_prediction.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        logger.info(f"压力趋势预测可视化已保存到: {output_path}")
    
    plt.tight_layout()
    plt.close()

def visualize_causal_heatmap(model, feature_names, output_dir=None):
    """
    可视化特征与压力趋势之间的因果关系热力图
    
    参数:
    model (PressureTrendCausalModel): 训练好的模型
    feature_names (list): 特征名称列表
    output_dir (str): 输出目录
    """
    if model.attn_weights is None:
        logger.warning("模型没有保存注意力权重，无法生成因果热力图")
        return
    
    # 提取注意力权重
    attn_weights = model.attn_weights.detach().cpu().numpy().mean(axis=0)
    
    plt.figure(figsize=(12, 10))
    
    # 绘制热力图
    sns.heatmap(
        attn_weights, 
        cmap='YlOrRd', 
        xticklabels=feature_names if len(feature_names) < 30 else [],
        yticklabels=feature_names if len(feature_names) < 30 else [],
        cbar_kws={'label': '注意力权重 (因果强度)'}
    )
    
    # 添加标签
    plt.xlabel('源特征', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('目标特征', fontproperties=chinese_font, fontsize=10)
    plt.title('特征间因果关系热力图', fontproperties=chinese_font, fontsize=10)
    
    # 保存图像
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'causal_heatmap.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        logger.info(f"因果热力图已保存到: {output_path}")
    
    plt.tight_layout()
    plt.close()

def compute_dfa_for_ecg_segments(ecg_segments):
    # ecg_segments: shape (n_samples, n_times)
    dfa_values = []
    for seg in ecg_segments:
        dfa = nolds.dfa(seg)
        dfa_values.append(dfa)
    return np.array(dfa_values)

def train_pressure_trend_model(stage_data, output_dir='D:/ecgeeg/30-数据分析/5-NeuroKit2/result/causalformer/pressure_trend'):
    """
    训练压力趋势预测模型
    
    参数:
    stage_data (dict): 按阶段组织的特征数据
    output_dir (str): 输出目录
    
    返回:
    PressureTrendCausalModel: 训练好的模型
    """
    os.makedirs(output_dir, exist_ok=True)
    # 组合各阶段数据，获得ecg_segments
    combined_eeg, combined_ecg, stages, ecg_segments = combine_stage_data(stage_data)
    eeg_scaler = StandardScaler()
    ecg_scaler = StandardScaler()
    combined_eeg = eeg_scaler.fit_transform(combined_eeg)
    combined_ecg = ecg_scaler.fit_transform(combined_ecg)
    # 批量计算DFA作为标签
    if ecg_segments is None:
        raise ValueError('ecg_segments（原始ECG片段）未提供，无法计算DFA标签')
    dfa_labels = compute_dfa_for_ecg_segments(ecg_segments)
    # 创建数据集
    sequence_length = 3
    dataset = StageSequenceDataset(
        combined_eeg, 
        combined_ecg, 
        dfa_labels, 
        sequence_length=sequence_length
    )
    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])
    batch_size = 16
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    # 模型参数
    input_dim = combined_eeg.shape[1] + combined_ecg.shape[1]
    hidden_dim = 64
    output_dim = 1  # 单输出DFA
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = PressureTrendCausalModel(
        input_dim=input_dim,
        hidden_dim=hidden_dim,
        output_dim=output_dim
    ).to(device)
    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)
    num_epochs = 100
    early_stop_patience = 10
    best_loss = float('inf')
    no_improve_epochs = 0
    history = {'train_loss': [], 'test_loss': [], 'learning_rate': []}
    logger.info(f"开始训练模型: epochs={num_epochs}, batch_size={batch_size}, device={device}")
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        for features, targets in train_loader:
            features, targets = features.to(device), targets.to(device)
            optimizer.zero_grad()
            outputs = model(features).squeeze(-1)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        train_loss /= len(train_loader)
        model.eval()
        test_loss = 0.0
        all_true = []
        all_pred = []
        with torch.no_grad():
            for features, targets in test_loader:
                features, targets = features.to(device), targets.to(device)
                outputs = model(features).squeeze(-1)
                loss = criterion(outputs, targets)
                test_loss += loss.item()
                all_true.extend(targets.cpu().numpy())
                all_pred.extend(outputs.cpu().numpy())
        test_loss /= len(test_loader)
        scheduler.step(test_loss)
        current_lr = optimizer.param_groups[0]['lr']
        history['train_loss'].append(train_loss)
        history['test_loss'].append(test_loss)
        history['learning_rate'].append(current_lr)
        if test_loss < best_loss:
            best_loss = test_loss
            no_improve_epochs = 0
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'loss': best_loss,
            }, os.path.join(output_dir, 'best_pressure_model.pth'))
        else:
            no_improve_epochs += 1
        logger.info(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.6f}, Test Loss: {test_loss:.6f}, LR: {current_lr:.6f}")
        if no_improve_epochs >= early_stop_patience:
            logger.info(f"Early stopping triggered after {epoch+1} epochs")
            break
    # 训练结束，可视化训练历史
    plt.figure(figsize=(12, 8))
    plt.plot(history['train_loss'], 'b-', label='训练损失')
    plt.plot(history['test_loss'], 'r-', label='测试损失')
    plt.xlabel('Epoch', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('损失', fontproperties=chinese_font, fontsize=10)
    plt.title('训练和测试损失', fontproperties=chinese_font, fontsize=10)
    plt.legend(prop=chinese_font)
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_history.png'), dpi=300, bbox_inches='tight')
    plt.close()
    checkpoint = torch.load(os.path.join(output_dir, 'best_pressure_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    logger.info(f"模型训练完成，最终测试损失: {best_loss:.6f}")
    # 测试集DFA预测可视化
    model.eval()
    all_true = []
    all_pred = []
    with torch.no_grad():
        for features, targets in test_loader:
            features, targets = features.to(device), targets.to(device)
            outputs = model(features).squeeze(-1)
            all_true.extend(targets.cpu().numpy())
            all_pred.extend(outputs.cpu().numpy())
    visualize_dfa_predictions(np.array(all_true), np.array(all_pred), stages, output_dir)
    return model

def prepare_stage_data_with_ecg_segments(data_directory, segment_length=1000, step=500):
    """
    读取真实fif文件，自动分割ECG片段，生成ecg_segments字段
    """
    logger.info(f"正在从 {data_directory} 读取真实fif文件并分割ECG片段...")
    stage_data = {}
    fif_files = [f for f in os.listdir(data_directory) if f.endswith('.fif')]
    for file in fif_files:
        file_path = os.path.join(data_directory, file)
        # 阶段识别
        stage = None
        for key in ['rest', 'test', 'prac']:
            if key in file.lower():
                if key == 'rest':
                    for i in range(1, 4):
                        if f'rest{i}' in file.lower():
                            stage = f'rest{i}'
                            break
                elif key == 'test':
                    for i in range(1, 4):
                        if f'test{i}' in file.lower():
                            stage = f'test{i}'
                            break
                else:
                    stage = 'prac'
                break
        if stage is None:
            logger.warning(f"无法识别文件的阶段: {file}")
            continue
        try:
            raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
            ch_names = raw.ch_names
            # 自动识别ECG通道
            ecg_channels = [ch for ch in ch_names if 'ecg' in ch.lower() or ch.lower().startswith('ecg')]
            if not ecg_channels:
                logger.warning(f"文件 {file} 未找到ECG通道，跳过")
                continue
            # 取第一个ECG通道
            ecg_data = raw.get_data(picks=ecg_channels[0])  # shape: (1, n_times)
            ecg_data = ecg_data.flatten()
            # 分割片段
            ecg_segments = segment_ecg_signal(ecg_data, segment_length, step)
            # EEG特征、ECG特征可用均值/方差等简单统计量（可后续扩展）
            eeg_channels = [ch for ch in ch_names if ch.lower().startswith('eeg') or ch in ['Fz','Cz','Pz','Oz']]
            if eeg_channels:
                eeg_data = raw.get_data(picks=eeg_channels)
                eeg_features = np.mean(eeg_data, axis=2).T  # (n_times, n_eeg_channels) -> (n_samples, n_features)
            else:
                eeg_features = np.zeros((len(ecg_segments), 4))
            # ECG特征
            ecg_features = np.array([[
                np.mean(seg), np.std(seg), np.min(seg), np.max(seg)
            ] for seg in ecg_segments])
            # 只取前n_samples个片段
            n_samples = min(len(ecg_segments), eeg_features.shape[0], ecg_features.shape[0])
            stage_data[stage] = {
                'eeg_features': eeg_features[:n_samples],
                'ecg_features': ecg_features[:n_samples],
                'ecg_segments': ecg_segments[:n_samples]
            }
            logger.info(f"{file} 阶段{stage}: 片段数={n_samples}, EEG特征={eeg_features.shape[1]}, ECG特征={ecg_features.shape[1]}")
        except Exception as e:
            logger.error(f"读取文件 {file} 失败: {e}")
    logger.info(f"所有阶段真实数据准备完成。阶段数: {len(stage_data)}")
    return stage_data

def main():
    """主函数"""
    logger.info("开始执行压力趋势预测模型训练...")
    
    # 准备按阶段组织的数据
    data_directory = 'D:/ecgeeg/19-eegecg手动预处理6-ICA3'
    stage_data = prepare_stage_data_with_ecg_segments(data_directory)
    
    # 训练压力趋势预测模型
    output_dir = 'D:/ecgeeg/30-数据分析/5-NeuroKit2/result/causalformer/pressure_trend'
    model = train_pressure_trend_model(stage_data, output_dir)
    
    logger.info("压力趋势预测模型执行完成!")

if __name__ == "__main__":
    main() 
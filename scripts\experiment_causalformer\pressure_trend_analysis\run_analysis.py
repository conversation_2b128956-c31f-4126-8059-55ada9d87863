#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 压力趋势分析执行脚本

功能：
- 执行压力趋势分析的完整流程
- 加载数据、训练模型、可视化结果
- 分析不同实验阶段的压力变化规律

作者：AI助手
日期：2025年5月23日
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import torch
from torch.utils.data import DataLoader, random_split
from sklearn.model_selection import train_test_split
import matplotlib.font_manager as fm

# 导入自定义模块
from data_preparation import (
    load_fif_data, 
    prepare_stage_data, 
    create_pressure_trend_dataset, 
    visualize_pressure_pattern,
    PressureDataset, 
    STAGE_ORDER, 
    PRESSURE_PATTERN,
    load_h5_epochs_data
)
from pressure_trend_model import (
    PressureTrendModel, 
    train_pressure_trend_model, 
    evaluate_model, 
    extract_causal_strength
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"pressure_trend_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("PressureTrend-Analysis")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

def create_output_dirs(base_dir):
    """
    创建输出目录
    
    参数:
    base_dir (str): 基础输出目录
    
    返回:
    dict: 包含各类输出目录的字典
    """
    dirs = {
        'visualizations': os.path.join(base_dir, 'visualizations'),
        'models': os.path.join(base_dir, 'models'),
        'results': os.path.join(base_dir, 'results'),
        'causal_analysis': os.path.join(base_dir, 'causal_analysis')
    }
    
    for dir_path in dirs.values():
        os.makedirs(dir_path, exist_ok=True)
    
    return dirs

def visualize_training_history(history, output_dir):
    """
    可视化训练历史
    
    参数:
    history (dict): 训练历史字典
    output_dir (str): 输出目录
    
    返回:
    str: 图像保存路径
    """
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 1, 1)
    plt.plot(history['train_loss'], 'b-', label='训练损失')
    plt.plot(history['val_loss'], 'r-', label='验证损失')
    plt.xlabel('Epoch', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('损失', fontproperties=chinese_font, fontsize=10)
    plt.title('训练与验证损失', fontproperties=chinese_font, fontsize=12)
    plt.legend(prop=chinese_font)
    plt.grid(True)
    
    plt.subplot(2, 1, 2)
    plt.plot(history['learning_rate'], 'g-')
    plt.xlabel('Epoch', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('学习率', fontproperties=chinese_font, fontsize=10)
    plt.title('学习率变化', fontproperties=chinese_font, fontsize=12)
    plt.grid(True)
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, 'training_history.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"训练历史可视化已保存到: {output_file}")
    
    return output_file

def visualize_predictions(y_true, y_pred, sample_counts, output_dir):
    """
    可视化压力预测结果
    
    参数:
    y_true (np.ndarray): 真实压力值
    y_pred (np.ndarray): 预测压力值
    sample_counts (list): 各阶段样本数量列表
    output_dir (str): 输出目录
    
    返回:
    str: 图像保存路径
    """
    plt.figure(figsize=(15, 10))
    
    # 确保y_true和y_pred是一维数组
    y_true = y_true.flatten()
    y_pred = y_pred.flatten()
    
    # 预测与真实值对比
    plt.subplot(2, 1, 1)
    plt.scatter(range(len(y_true)), y_true, c='blue', alpha=0.6, label='真实值')
    plt.scatter(range(len(y_pred)), y_pred, c='red', alpha=0.6, label='预测值')
    plt.xlabel('样本索引', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('压力水平', fontproperties=chinese_font, fontsize=10)
    plt.title('压力水平预测结果', fontproperties=chinese_font, fontsize=12)
    plt.legend(prop=chinese_font)
    plt.grid(True)
    
    # 计算累积样本数
    cum_samples = np.cumsum([0] + sample_counts)
    
    # 为每个阶段添加垂直分隔线和标签
    for i, stage in enumerate(STAGE_ORDER):
        if i < len(cum_samples) - 1:
            plt.axvline(x=cum_samples[i], color='gray', linestyle='--')
            plt.text(cum_samples[i] + (cum_samples[i+1] - cum_samples[i])/2, 0.95, 
                     stage, fontproperties=chinese_font, horizontalalignment='center',
                     transform=plt.gca().get_xaxis_transform())
    
    # 预测误差
    plt.subplot(2, 1, 2)
    errors = y_true - y_pred
    plt.bar(range(len(errors)), errors, alpha=0.7)
    plt.xlabel('样本索引', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('预测误差', fontproperties=chinese_font, fontsize=10)
    plt.title('预测误差分布', fontproperties=chinese_font, fontsize=12)
    plt.grid(True, axis='y')
    
    # 为每个阶段添加垂直分隔线
    for i in range(len(cum_samples) - 1):
        plt.axvline(x=cum_samples[i], color='gray', linestyle='--')
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, 'prediction_results.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"预测结果可视化已保存到: {output_file}")
    
    return output_file

def visualize_causal_strength(causal_info, sequence_length, output_dir):
    """
    可视化因果强度
    
    参数:
    causal_info (dict): 因果强度信息字典
    sequence_length (int): 序列长度
    output_dir (str): 输出目录
    
    返回:
    str: 图像保存路径
    """
    eeg_cs = causal_info['eeg_causal_strength']
    ecg_cs = causal_info['ecg_causal_strength']
    cross_cs = causal_info['cross_causal_strength']
    
    plt.figure(figsize=(15, 12))
    
    # EEG因果强度热图
    plt.subplot(2, 2, 1)
    plt.imshow(eeg_cs, cmap='hot', interpolation='nearest')
    plt.colorbar(label='强度')
    plt.title('EEG信号内部因果强度', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('目标时间步', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('源时间步', fontproperties=chinese_font, fontsize=10)
    
    # ECG因果强度热图
    plt.subplot(2, 2, 2)
    plt.imshow(ecg_cs, cmap='hot', interpolation='nearest')
    plt.colorbar(label='强度')
    plt.title('ECG信号内部因果强度', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('目标时间步', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('源时间步', fontproperties=chinese_font, fontsize=10)
    
    # 跨信号因果强度热图
    plt.subplot(2, 2, 3)
    plt.imshow(cross_cs, cmap='hot', interpolation='nearest')
    plt.colorbar(label='强度')
    plt.title('EEG-ECG交互因果强度', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('目标时间步', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('源时间步', fontproperties=chinese_font, fontsize=10)
    
    # 因果总结条形图
    plt.subplot(2, 2, 4)
    
    # 计算每个时间步的平均因果强度
    eeg_mean = np.mean(eeg_cs, axis=0)
    ecg_mean = np.mean(ecg_cs, axis=0)
    cross_mean = np.mean(cross_cs, axis=0)
    
    x = np.arange(sequence_length)
    width = 0.25
    
    plt.bar(x - width, eeg_mean, width, label='EEG内部', color='blue', alpha=0.7)
    plt.bar(x, ecg_mean, width, label='ECG内部', color='red', alpha=0.7)
    plt.bar(x + width, cross_mean, width, label='EEG-ECG交互', color='green', alpha=0.7)
    
    plt.xlabel('时间步', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('平均因果强度', fontproperties=chinese_font, fontsize=10)
    plt.title('各时间步平均因果强度', fontproperties=chinese_font, fontsize=10)
    plt.xticks(x)
    plt.legend(prop=chinese_font)
    plt.grid(True, axis='y')
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, 'causal_strength.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"因果强度可视化已保存到: {output_file}")
    
    return output_file

def visualize_stage_pressure_comparison(y_true, y_pred, stage_splits, output_dir):
    """
    可视化各阶段压力水平比较
    
    参数:
    y_true (np.ndarray): 真实压力值
    y_pred (np.ndarray): 预测压力值
    stage_splits (list): 各阶段样本索引分割点
    output_dir (str): 输出目录
    
    返回:
    str: 图像保存路径
    """
    plt.figure(figsize=(12, 8))
    
    # 确保y_true和y_pred是一维数组
    y_true = y_true.flatten()
    y_pred = y_pred.flatten()
    
    # 获取实际有数据的阶段
    available_stages = []
    for i, stage in enumerate(STAGE_ORDER):
        if i < len(stage_splits) - 1:
            available_stages.append(stage)
    
    # 提取每个阶段的真实值和预测值
    stage_data = {}
    for i, stage in enumerate(available_stages):
        start_idx = stage_splits[i]
        end_idx = stage_splits[i+1] if i+1 < len(stage_splits) else len(y_true)
        
        true_values = y_true[start_idx:end_idx]
        pred_values = y_pred[start_idx:end_idx]
        
        stage_data[stage] = {
            'true': true_values,
            'pred': pred_values,
            'mean_true': np.mean(true_values),
            'mean_pred': np.mean(pred_values),
            'std_true': np.std(true_values),
            'std_pred': np.std(pred_values)
        }
    
    # 绘制各阶段压力水平比较
    x = np.arange(len(available_stages))
    width = 0.35
    
    true_means = [stage_data[stage]['mean_true'] for stage in available_stages]
    pred_means = [stage_data[stage]['mean_pred'] for stage in available_stages]
    true_stds = [stage_data[stage]['std_true'] for stage in available_stages]
    pred_stds = [stage_data[stage]['std_pred'] for stage in available_stages]
    
    plt.bar(x - width/2, true_means, width, label='真实值', color='blue', alpha=0.7, yerr=true_stds, capsize=5)
    plt.bar(x + width/2, pred_means, width, label='预测值', color='red', alpha=0.7, yerr=pred_stds, capsize=5)
    
    plt.xlabel('实验阶段', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('平均压力水平', fontproperties=chinese_font, fontsize=10)
    plt.title('各实验阶段压力水平比较', fontproperties=chinese_font, fontsize=12)
    plt.xticks(x, available_stages)
    plt.legend(prop=chinese_font)
    plt.grid(True, axis='y')
    
    # 添加真实值标注
    for i, v in enumerate(true_means):
        plt.text(i - width/2, v + 0.02, f'{v:.2f}', ha='center', fontproperties=chinese_font, fontsize=9)
    
    # 添加预测值标注
    for i, v in enumerate(pred_means):
        plt.text(i + width/2, v + 0.02, f'{v:.2f}', ha='center', fontproperties=chinese_font, fontsize=9)
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, 'stage_pressure_comparison.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"阶段压力对比可视化已保存到: {output_file}")
    
    return output_file

def run_pressure_trend_analysis(args):
    """
    执行压力趋势分析的完整流程
    
    参数:
    args (Namespace): 命令行参数
    """
    logger.info("开始执行压力趋势分析...")
    
    # 创建输出目录
    output_dirs = create_output_dirs(args.output_dir)
    
    # 可视化压力变化模式
    visualize_pressure_pattern(output_dirs['visualizations'])
    
    # 加载数据
    logger.info(f"从 {args.data_dir} 加载数据...")
    # 使用h5文件目录
    h5_dir = args.h5_dir
    data_by_subject = load_h5_epochs_data(h5_dir, subject_pattern=args.subject)
    
    if not data_by_subject:
        logger.error("未能加载任何数据，请检查数据目录和文件格式。")
        return
    
    # 准备分阶段数据
    logger.info("准备分阶段数据...")
    feature_data = prepare_stage_data(data_by_subject, include_prac=True)
    
    # 创建压力趋势预测数据集
    logger.info("创建压力趋势预测数据集...")
    eeg_combined, ecg_combined, pressure_labels, sample_counts = create_pressure_trend_dataset(
        feature_data, sequence_length=args.sequence_length
    )
    
    # 创建数据集
    dataset = PressureDataset(
        eeg_combined, 
        ecg_combined, 
        pressure_labels, 
        sequence_length=args.sequence_length
    )
    
    # 划分训练集、验证集和测试集
    dataset_size = len(dataset)
    train_size = int(dataset_size * 0.7)
    val_size = int(dataset_size * 0.15)
    test_size = dataset_size - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size]
    )
    
    logger.info(f"数据集划分: 训练集={train_size}, 验证集={val_size}, 测试集={test_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False)
    
    # 获取特征维度
    eeg_dim = eeg_combined.shape[1]
    ecg_dim = ecg_combined.shape[1]
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and not args.cpu else 'cpu')
    
    # 训练模型
    logger.info(f"训练模型: hidden_dim={args.hidden_dim}, num_epochs={args.epochs}, device={device}")
    model, history = train_pressure_trend_model(
        train_loader=train_loader,
        val_loader=val_loader,
        eeg_dim=eeg_dim,
        ecg_dim=ecg_dim,
        hidden_dim=args.hidden_dim,
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        device=device
    )
    
    # 可视化训练历史
    visualize_training_history(history, output_dirs['visualizations'])
    
    # 保存模型
    model_path = os.path.join(output_dirs['models'], 'pressure_trend_model.pth')
    torch.save({
        'model_state_dict': model.state_dict(),
        'eeg_dim': eeg_dim,
        'ecg_dim': ecg_dim,
        'hidden_dim': args.hidden_dim
    }, model_path)
    logger.info(f"模型已保存到: {model_path}")
    
    # 评估模型
    logger.info("评估模型性能...")
    metrics, (y_true, y_pred) = evaluate_model(
        model=model,
        test_loader=test_loader,
        eeg_dim=eeg_dim,
        ecg_dim=ecg_dim,
        device=device
    )
    
    # 保存评估指标
    metrics_df = pd.DataFrame([metrics])
    metrics_path = os.path.join(output_dirs['results'], 'evaluation_metrics.csv')
    metrics_df.to_csv(metrics_path, index=False)
    logger.info(f"评估指标已保存到: {metrics_path}")
    
    # 计算各阶段的样本索引分割点
    stage_splits = [0]
    for count in sample_counts:
        stage_splits.append(stage_splits[-1] + count)
    
    # 可视化预测结果
    visualize_predictions(y_true, y_pred, sample_counts, output_dirs['visualizations'])
    
    # 可视化各阶段压力水平比较
    visualize_stage_pressure_comparison(y_true, y_pred, stage_splits, output_dirs['visualizations'])
    
    # 提取因果强度
    logger.info("提取因果强度信息...")
    causal_info = extract_causal_strength(
        model=model,
        eeg_dim=eeg_dim,
        ecg_dim=ecg_dim,
        sequence_length=args.sequence_length,
        device=device
    )
    
    # 可视化因果强度
    visualize_causal_strength(causal_info, args.sequence_length, output_dirs['causal_analysis'])
    
    # 保存因果强度信息
    for key, value in causal_info.items():
        np.save(os.path.join(output_dirs['causal_analysis'], f"{key}.npy"), value)
    
    logger.info("压力趋势分析完成!")

def parse_args():
    """
    解析命令行参数
    
    返回:
    Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='CausalFormer压力趋势分析')
    
    parser.add_argument('--data_dir', type=str, default=r'D:\ecgeeg\19-eegecg手动预处理6-ICA3',
                        help='数据目录路径')
    parser.add_argument('--h5_dir', type=str, 
                        default=r'D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs',
                        help='H5格式epochs数据目录路径')
    parser.add_argument('--output_dir', type=str, 
                        default=r'D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\pressure_trend_analysis',
                        help='输出目录路径')
    parser.add_argument('--subject', type=str, default=None,
                        help='指定被试ID，如"01_01"，默认为None表示处理所有被试')
    parser.add_argument('--sequence_length', type=int, default=3,
                        help='序列长度')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--epochs', type=int, default=100,
                        help='训练轮数')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--cpu', action='store_true',
                        help='强制使用CPU，即使GPU可用')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    run_pressure_trend_analysis(args)

if __name__ == "__main__":
    main() 
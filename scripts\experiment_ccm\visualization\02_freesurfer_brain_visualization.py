#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用FreeSurfer和nilearn创建高质量脑可视化
参考FreeSurfer的fsaverage模板和nilearn的可视化功能
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from matplotlib.colors import LinearSegmentedColormap
import sys
import nibabel as nib
from nilearn import plotting, datasets
from nilearn.image import get_data

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入工具函数
from utils.nature_visualization import (
    setup_nature_style,
    save_and_validate_figure,
    clear_previous_results
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 定义结果保存路径
RESULTS_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'freesurfer_brain_visualization')
os.makedirs(RESULTS_DIR, exist_ok=True)

# 定义数据路径
DATA_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'nonlinear_interaction')
CCM_RESULTS_PATH = os.path.join(DATA_DIR, 'ccm_results_all_subjects.csv')
ANXIETY_GROUPS_PATH = os.path.join(DATA_DIR, 'anxiety_groups.csv')

def load_data():
    """加载CCM分析结果和焦虑分组信息"""
    logger.info(f"加载CCM分析结果: {CCM_RESULTS_PATH}")
    ccm_results = pd.read_csv(CCM_RESULTS_PATH)
    logger.info(f"成功加载CCM分析结果，共 {len(ccm_results)} 行")

    logger.info(f"加载焦虑分组信息: {ANXIETY_GROUPS_PATH}")
    anxiety_groups = pd.read_csv(ANXIETY_GROUPS_PATH)
    high_anxiety_subjects = anxiety_groups[anxiety_groups['anxiety_group'] == 'high']['subject_id'].tolist()
    low_anxiety_subjects = anxiety_groups[anxiety_groups['anxiety_group'] == 'low']['subject_id'].tolist()
    logger.info(f"成功加载焦虑分组信息: 高焦虑组 {len(high_anxiety_subjects)} 人，低焦虑组 {len(low_anxiety_subjects)} 人")

    return ccm_results, high_anxiety_subjects, low_anxiety_subjects

def compute_brain_regions_values(ccm_results, subjects, direction, stage, band):
    """计算特定条件下各脑区的平均值"""
    # 筛选数据
    mask = (
        ccm_results['subject_id'].isin(subjects) &
        (ccm_results['stage'] == stage) &
        (ccm_results['frequency_band'] == band)
    )
    filtered_data = ccm_results[mask]

    # 根据方向选择相应的列
    value_column = 'directionality'

    # 按通道分组计算平均值
    channel_values = filtered_data.groupby('channel')[value_column].mean().reset_index()
    # 重命名列以保持一致性
    channel_values = channel_values.rename(columns={value_column: 'ccm_value'})

    # 如果是心→脑方向，我们需要反转值的符号
    if direction == 'heart_to_brain':
        channel_values['ccm_value'] = -channel_values['ccm_value']

    # 创建包含所有电极的数据框
    all_channels = pd.DataFrame({'channel': [
        'Fp1', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8',
        'T7', 'C3', 'Cz', 'C4', 'T8',
        'P7', 'P3', 'Pz', 'P4', 'P8',
        'O1', 'O2'
    ]})

    # 合并数据
    result = pd.merge(all_channels, channel_values, on='channel', how='left')
    result['ccm_value'] = result['ccm_value'].fillna(0)

    return result

def create_volume_from_channels(channel_values, template_img):
    """从通道值创建体积图像"""
    # 获取模板图像的形状和仿射变换
    template_data = get_data(template_img)
    affine = template_img.affine

    # 创建一个与模板相同形状的零数组
    volume_data = np.zeros_like(template_data)

    # 定义通道的MNI坐标 (近似值)
    channel_coords = {
        'Fp1': [-22, 70, -8], 'Fp2': [22, 70, -8],
        'F7': [-54, 28, -8], 'F3': [-33, 40, 32], 'Fz': [0, 40, 50], 'F4': [33, 40, 32], 'F8': [54, 28, -8],
        'T7': [-70, -14, 0], 'C3': [-50, -16, 50], 'Cz': [0, -16, 74], 'C4': [50, -16, 50], 'T8': [70, -14, 0],
        'P7': [-54, -68, 0], 'P3': [-33, -60, 50], 'Pz': [0, -60, 50], 'P4': [33, -60, 50], 'P8': [54, -68, 0],
        'O1': [-22, -92, 0], 'O2': [22, -92, 0]
    }

    # 将MNI坐标转换为体素坐标
    from nilearn.image import coord_transform

    # 创建高斯核函数来平滑电极值
    def gaussian_kernel(distance, sigma=10):
        return np.exp(-(distance**2) / (2 * sigma**2))

    # 填充体积数据
    for i, row in channel_values.iterrows():
        channel = row['channel']
        value = row['ccm_value']

        if channel in channel_coords:
            x, y, z = channel_coords[channel]
            i, j, k = coord_transform(x, y, z, np.linalg.inv(affine))
            i, j, k = int(i), int(j), int(k)

            # 确保坐标在体积范围内
            if (0 <= i < volume_data.shape[0] and
                0 <= j < volume_data.shape[1] and
                0 <= k < volume_data.shape[2]):

                # 在电极位置周围创建高斯分布
                radius = 15  # 高斯核的半径
                for di in range(-radius, radius+1):
                    for dj in range(-radius, radius+1):
                        for dk in range(-radius, radius+1):
                            ni, nj, nk = i+di, j+dj, k+dk
                            if (0 <= ni < volume_data.shape[0] and
                                0 <= nj < volume_data.shape[1] and
                                0 <= nk < volume_data.shape[2]):

                                distance = np.sqrt(di**2 + dj**2 + dk**2)
                                weight = gaussian_kernel(distance)
                                volume_data[ni, nj, nk] += value * weight

    # 创建新的NIfTI图像
    volume_img = nib.Nifti1Image(volume_data, affine)

    return volume_img

def create_glass_brain_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects):
    """创建玻璃脑可视化"""
    logger.info("创建玻璃脑可视化")

    # 设置Nature风格
    setup_nature_style()

    # 定义要显示的条件
    directions = ['brain_to_heart', 'heart_to_brain']
    direction_labels = {'brain_to_heart': '脑→心', 'heart_to_brain': '心→脑'}
    stages = ['test3']  # 只选择一个阶段以简化可视化
    bands = ['alpha']  # 只选择一个频段以简化可视化
    groups = ['high', 'low']
    group_labels = {'high': '高焦虑组', 'low': '低焦虑组'}

    # 获取MNI模板
    template_img = datasets.load_mni152_template()

    # 创建自定义颜色映射
    colors_blue = [(0.95, 0.95, 1), (0, 0, 0.8)]  # 从浅蓝到深蓝
    cmap_blue = LinearSegmentedColormap.from_list('custom_blue', colors_blue)

    colors_red = [(1, 0.95, 0.95), (0.8, 0, 0)]  # 从浅红到深红
    cmap_red = LinearSegmentedColormap.from_list('custom_red', colors_red)

    # 定义颜色映射
    cmaps = {'brain_to_heart': cmap_blue, 'heart_to_brain': cmap_red}

    # 定义视角
    views = ['sagittal', 'sagittal', 'coronal', 'coronal', 'axial', 'axial']
    view_labels = ['左视图', '右视图', '前视图', '后视图', '顶视图', '底视图']
    hemispheres = ['left', 'right', None, None, None, None]

    # 计算全局vmin和vmax以保持一致的颜色比例
    all_values = []
    for group in groups:
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects
        for band in bands:
            for stage in stages:
                for direction in directions:
                    values = compute_brain_regions_values(ccm_results, subjects, direction, stage, band)
                    all_values.extend(values['ccm_value'].tolist())

    vmin, vmax = np.percentile(all_values, [5, 95])

    # 为每个组和方向创建一个子图
    for i, group in enumerate(groups):
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects

        for j, direction in enumerate(directions):
            # 计算该条件下的脑区值
            values = compute_brain_regions_values(ccm_results, subjects, direction, stages[0], bands[0])

            # 创建体积图像
            volume_img = create_volume_from_channels(values, template_img)

            # 保存体积图像用于后续处理
            volume_path = os.path.join(RESULTS_DIR, f'{group}_{direction}_volume.nii.gz')
            nib.save(volume_img, volume_path)
            logger.info(f"体积图像已保存至: {volume_path}")

            # 创建一个包含6个视角的子图
            subfig = plt.figure(figsize=(15, 10))
            subfig.suptitle(f"{group_labels[group]}, {direction_labels[direction]}", fontsize=14)

            # 绘制6个不同视角的玻璃脑
            for k, (view, view_label, hemisphere) in enumerate(zip(views, view_labels, hemispheres)):
                ax = subfig.add_subplot(2, 3, k+1)

                # 设置显示模式
                if view == 'sagittal' and hemisphere == 'left':
                    display_mode = 'l'
                elif view == 'sagittal' and hemisphere == 'right':
                    display_mode = 'r'
                elif view == 'coronal' and k == 2:  # 前视图
                    display_mode = 'y'
                elif view == 'coronal' and k == 3:  # 后视图
                    display_mode = 'y'
                elif view == 'axial' and k == 4:  # 顶视图
                    display_mode = 'z'
                elif view == 'axial' and k == 5:  # 底视图
                    display_mode = 'z'
                else:
                    display_mode = view[0]

                # 绘制玻璃脑
                plotting.plot_glass_brain(
                    volume_img,
                    display_mode=display_mode,
                    colorbar=False,
                    threshold=0.1 * vmax,
                    vmax=vmax,
                    vmin=vmin,
                    cmap=cmaps[direction],
                    plot_abs=False,
                    axes=ax,
                    title=view_label,
                )

            # 添加颜色条
            cbar_ax = subfig.add_axes([0.92, 0.3, 0.02, 0.4])
            sm = plt.cm.ScalarMappable(cmap=cmaps[direction], norm=plt.Normalize(vmin=vmin, vmax=vmax))
            sm.set_array([])
            cbar = plt.colorbar(sm, cax=cbar_ax)
            cbar.set_label('CCM值', fontsize=10)

            # 调整布局
            plt.tight_layout(rect=[0, 0, 0.9, 0.95])

            # 保存图形
            output_path = os.path.join(RESULTS_DIR, f'glass_brain_{group}_{direction}.png')
            eps_path = os.path.join(RESULTS_DIR, f'glass_brain_{group}_{direction}.eps')

            # 保存为EPS格式
            plt.savefig(eps_path, format='eps', dpi=300, bbox_inches='tight')
            logger.info(f"图形已保存为eps格式: {eps_path}")

            # 保存为PNG格式并验证
            save_and_validate_figure(subfig, output_path)
            logger.info(f"玻璃脑可视化已成功保存至: {output_path}")

            plt.close(subfig)

    # 创建组合图像
    logger.info("玻璃脑可视化已完成")

def create_surface_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects):
    """创建脑表面可视化"""
    logger.info("创建脑表面可视化")

    # 设置Nature风格
    setup_nature_style()

    # 定义要显示的条件
    directions = ['brain_to_heart', 'heart_to_brain']
    direction_labels = {'brain_to_heart': '脑→心', 'heart_to_brain': '心→脑'}
    stages = ['test3']  # 只选择一个阶段以简化可视化
    bands = ['alpha']  # 只选择一个频段以简化可视化
    groups = ['high', 'low']
    group_labels = {'high': '高焦虑组', 'low': '低焦虑组'}

    # 创建自定义颜色映射
    colors_blue = [(0.95, 0.95, 1), (0, 0, 0.8)]  # 从浅蓝到深蓝
    cmap_blue = LinearSegmentedColormap.from_list('custom_blue', colors_blue)

    colors_red = [(1, 0.95, 0.95), (0.8, 0, 0)]  # 从浅红到深红
    cmap_red = LinearSegmentedColormap.from_list('custom_red', colors_red)

    # 定义颜色映射
    cmaps = {'brain_to_heart': cmap_blue, 'heart_to_brain': cmap_red}

    # 计算全局vmin和vmax以保持一致的颜色比例
    all_values = []
    for group in groups:
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects
        for band in bands:
            for stage in stages:
                for direction in directions:
                    values = compute_brain_regions_values(ccm_results, subjects, direction, stage, band)
                    all_values.extend(values['ccm_value'].tolist())

    vmin, vmax = np.percentile(all_values, [5, 95])

    # 为每个组和方向创建表面可视化
    for group in groups:
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects

        for direction in directions:
            # 计算该条件下的脑区值
            values = compute_brain_regions_values(ccm_results, subjects, direction, stages[0], bands[0])

            # 加载之前创建的体积图像
            volume_path = os.path.join(RESULTS_DIR, f'{group}_{direction}_volume.nii.gz')
            volume_img = nib.load(volume_path)

            # 使用plot_img_on_surf创建多视角表面可视化
            output_path = os.path.join(RESULTS_DIR, f'surface_{group}_{direction}.png')

            plotting.plot_img_on_surf(
                volume_img,
                threshold=0.1 * vmax,
                cmap=cmaps[direction],
                colorbar=True,
                symmetric_cbar=False,
                vmax=vmax,
                vmin=vmin,
                views=['lateral', 'medial', 'dorsal', 'ventral'],
                hemispheres=['left', 'right'],
                title=f'{group_labels[group]}, {direction_labels[direction]} - 脑表面可视化',
                output_file=output_path
            )

            logger.info(f"脑表面可视化已成功保存至: {output_path}")

    # 我们不再需要创建组合图像，因为plot_img_on_surf已经创建了完整的可视化
    logger.info("脑表面可视化已完成")

def create_interactive_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects):
    """创建交互式可视化"""
    logger.info("创建交互式可视化")

    # 定义要显示的条件
    directions = ['brain_to_heart', 'heart_to_brain']
    direction_labels = {'brain_to_heart': '脑→心', 'heart_to_brain': '心→脑'}
    stages = ['test3']  # 只选择一个阶段以简化可视化
    bands = ['alpha']  # 只选择一个频段以简化可视化
    groups = ['high', 'low']
    group_labels = {'high': '高焦虑组', 'low': '低焦虑组'}

    # 定义颜色映射
    cmaps = {'brain_to_heart': 'Blues', 'heart_to_brain': 'Reds'}

    # 计算全局vmin和vmax以保持一致的颜色比例
    all_values = []
    for group in groups:
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects
        for band in bands:
            for stage in stages:
                for direction in directions:
                    values = compute_brain_regions_values(ccm_results, subjects, direction, stage, band)
                    all_values.extend(values['ccm_value'].tolist())

    vmin, vmax = np.percentile(all_values, [5, 95])

    # 为每个组和方向创建交互式可视化
    for group in groups:
        subjects = high_anxiety_subjects if group == 'high' else low_anxiety_subjects

        for direction in directions:
            # 加载之前创建的体积图像
            volume_path = os.path.join(RESULTS_DIR, f'{group}_{direction}_volume.nii.gz')
            volume_img = nib.load(volume_path)

            # 创建交互式可视化
            html_view = plotting.view_img_on_surf(
                volume_img,
                threshold=0.1 * vmax,
                cmap=cmaps[direction],
                vmax=vmax,
                vmin=vmin,
                title=f'{group_labels[group]}, {direction_labels[direction]}',
            )

            # 保存HTML文件
            html_path = os.path.join(RESULTS_DIR, f'interactive_{group}_{direction}.html')
            html_view.save_as_html(html_path)
            logger.info(f"交互式可视化已保存至: {html_path}")

def main():
    """主函数"""
    logger.info("开始执行FreeSurfer脑可视化...")

    # 清除之前的结果
    clear_previous_results(RESULTS_DIR)

    # 加载数据
    ccm_results, high_anxiety_subjects, low_anxiety_subjects = load_data()

    # 创建玻璃脑可视化
    create_glass_brain_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects)

    # 创建脑表面可视化
    create_surface_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects)

    # 创建交互式可视化
    create_interactive_visualization(ccm_results, high_anxiety_subjects, low_anxiety_subjects)

    logger.info("FreeSurfer脑可视化完成！")
    logger.info(f"输出文件保存在: {RESULTS_DIR}")
    logger.info("请检查生成的图像是否符合要求：")
    logger.info("1. 图像是否为空？")
    logger.info("2. 子图是否为空或坐标轴范围过大？")
    logger.info("3. 中文字符是否正常显示？")
    logger.info("4. 图像是否能够分析出有意义的结果？")

if __name__ == "__main__":
    main()

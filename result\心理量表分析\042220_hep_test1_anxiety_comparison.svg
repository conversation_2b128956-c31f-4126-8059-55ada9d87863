<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="720pt" height="432pt" viewBox="0 0 720 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:35.829835</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 720 432 
L 720 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 51.24875 394.495 
L 709.2 394.495 
L 709.2 25.05375 
L 51.24875 25.05375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 81.155625 394.495 
L 81.155625 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m0512d5573a" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0512d5573a" x="81.155625" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- -0.2 -->
      <g transform="translate(73.155625 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-2d" d="M 2975 2125 
L 125 2125 
L 125 2525 
L 2975 2525 
L 2975 2125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-2e" d="M 1075 125 
L 500 125 
L 500 675 
L 1075 675 
L 1075 125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 180.845208 394.495 
L 180.845208 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m0512d5573a" x="180.845208" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g transform="translate(174.845208 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 280.534792 394.495 
L 280.534792 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m0512d5573a" x="280.534792" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.2 -->
      <g transform="translate(274.534792 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 380.224375 394.495 
L 380.224375 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m0512d5573a" x="380.224375" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.4 -->
      <g transform="translate(374.224375 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 479.913958 394.495 
L 479.913958 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m0512d5573a" x="479.913958" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.6 -->
      <g transform="translate(473.913958 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-36" d="M 250 1612 
Q 275 1975 387 2225 
Q 500 2475 725 2850 
L 1750 4450 
L 2325 4450 
L 1275 2800 
Q 1950 2975 2350 2750 
Q 2750 2525 2887 2237 
Q 3025 1950 3037 1612 
Q 3050 1275 2937 950 
Q 2825 625 2537 362 
Q 2250 100 1737 75 
Q 1225 50 862 262 
Q 500 475 362 862 
Q 225 1250 250 1612 
z
M 1025 787 
Q 1250 550 1625 525 
Q 2000 500 2250 775 
Q 2500 1050 2500 1575 
Q 2500 2100 2187 2300 
Q 1875 2500 1487 2450 
Q 1100 2400 925 2075 
Q 750 1750 775 1387 
Q 800 1025 1025 787 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-36" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 579.603542 394.495 
L 579.603542 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m0512d5573a" x="579.603542" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.8 -->
      <g transform="translate(573.603542 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-38" d="M 175 1375 
Q 175 1675 325 1962 
Q 475 2250 825 2425 
Q 525 2600 425 2812 
Q 325 3025 312 3300 
Q 300 3575 387 3775 
Q 475 3975 650 4150 
Q 825 4325 1037 4387 
Q 1250 4450 1500 4450 
Q 1750 4450 1950 4400 
Q 2150 4350 2375 4187 
Q 2600 4025 2700 3725 
Q 2800 3425 2687 3025 
Q 2575 2625 2100 2400 
Q 2525 2275 2700 2012 
Q 2875 1750 2875 1375 
Q 2875 1000 2762 775 
Q 2650 550 2512 400 
Q 2375 250 2137 162 
Q 1900 75 1537 75 
Q 1175 75 912 162 
Q 650 250 475 425 
Q 300 600 237 837 
Q 175 1075 175 1375 
z
M 687 1400 
Q 675 1100 787 875 
Q 900 650 1200 587 
Q 1500 525 1825 600 
Q 2150 675 2275 950 
Q 2400 1225 2362 1500 
Q 2325 1775 2050 1962 
Q 1775 2150 1450 2125 
Q 1125 2100 912 1900 
Q 700 1700 687 1400 
z
M 775 3350 
Q 775 3100 950 2875 
Q 1125 2650 1500 2650 
Q 1875 2650 2062 2875 
Q 2250 3100 2237 3412 
Q 2225 3725 2012 3875 
Q 1800 4025 1437 4000 
Q 1075 3975 925 3787 
Q 775 3600 775 3350 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-38" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 679.293125 394.495 
L 679.293125 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m0512d5573a" x="679.293125" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 1.0 -->
      <g transform="translate(673.293125 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <defs>
       <path id="m0aa98617d7" d="M 0 0 
L 0 2 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m0aa98617d7" x="56.233229" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m0aa98617d7" x="106.078021" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m0aa98617d7" x="131.000417" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_18">
      <g>
       <use xlink:href="#m0aa98617d7" x="155.922813" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_19">
      <g>
       <use xlink:href="#m0aa98617d7" x="205.767604" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_20">
      <g>
       <use xlink:href="#m0aa98617d7" x="230.69" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_21">
      <g>
       <use xlink:href="#m0aa98617d7" x="255.612396" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_22">
      <g>
       <use xlink:href="#m0aa98617d7" x="305.457188" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m0aa98617d7" x="330.379583" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m0aa98617d7" x="355.301979" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_25">
      <g>
       <use xlink:href="#m0aa98617d7" x="405.146771" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_26">
      <g>
       <use xlink:href="#m0aa98617d7" x="430.069167" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_27">
      <g>
       <use xlink:href="#m0aa98617d7" x="454.991563" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_28">
      <g>
       <use xlink:href="#m0aa98617d7" x="504.836354" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_29">
      <g>
       <use xlink:href="#m0aa98617d7" x="529.75875" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_30">
      <g>
       <use xlink:href="#m0aa98617d7" x="554.681146" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_31">
      <g>
       <use xlink:href="#m0aa98617d7" x="604.525938" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_32">
      <g>
       <use xlink:href="#m0aa98617d7" x="629.448333" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_33">
      <g>
       <use xlink:href="#m0aa98617d7" x="654.370729" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_34">
      <g>
       <use xlink:href="#m0aa98617d7" x="704.215521" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- 时间 (s) -->
     <g transform="translate(360.224375 420.002813) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-65f6" d="M 2525 4575 
Q 2500 3675 2500 2400 
Q 2500 1125 2525 400 
L 2000 400 
L 2000 875 
L 1000 875 
L 1000 50 
L 475 50 
Q 500 1200 500 2350 
Q 500 3525 475 4575 
L 2525 4575 
z
M 4875 3825 
Q 4875 4575 4850 5225 
L 5400 5225 
Q 5375 4575 5375 3825 
Q 5750 3825 6175 3850 
L 6175 3375 
Q 5750 3400 5375 3400 
L 5375 50 
Q 5375 -325 5075 -425 
Q 4775 -525 4300 -575 
Q 4300 -275 4025 0 
Q 4500 -50 4687 -25 
Q 4875 0 4875 300 
L 4875 3400 
Q 3625 3400 2775 3375 
L 2775 3850 
Q 3575 3825 4875 3825 
z
M 2000 1300 
L 2000 2550 
L 1000 2550 
L 1000 1300 
L 2000 1300 
z
M 2000 2975 
L 2000 4150 
L 1000 4150 
L 1000 2975 
L 2000 2975 
z
M 3550 2725 
Q 3875 2200 4175 1600 
Q 3925 1525 3650 1375 
Q 3450 1975 3075 2500 
Q 3300 2600 3550 2725 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-95f4" d="M 4450 3550 
Q 4425 3075 4425 2725 
L 4425 1400 
Q 4425 1075 4450 625 
L 1925 625 
Q 1950 1075 1950 1425 
L 1950 2725 
Q 1950 3075 1925 3550 
L 4450 3550 
z
M 5800 4875 
Q 5775 4400 5775 3925 
L 5775 250 
Q 5800 -325 5462 -450 
Q 5125 -575 4725 -625 
Q 4700 -325 4525 -25 
Q 5000 -50 5137 25 
Q 5275 100 5275 350 
L 5275 4450 
L 3775 4450 
Q 3175 4450 2650 4425 
L 2650 4900 
Q 3175 4875 3775 4875 
L 5800 4875 
z
M 550 -525 
Q 600 -25 600 525 
L 600 3150 
Q 600 3675 550 4025 
L 1125 4025 
Q 1100 3650 1100 3300 
L 1100 500 
Q 1100 0 1125 -525 
L 550 -525 
z
M 3950 1025 
L 3950 1950 
L 2425 1950 
L 2425 1025 
L 3950 1025 
z
M 3950 2350 
L 3950 3150 
L 2425 3150 
L 2425 2350 
L 3950 2350 
z
M 1525 5300 
Q 1750 5000 2225 4350 
Q 1950 4225 1700 4075 
Q 1450 4575 1075 5025 
Q 1350 5225 1525 5300 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-20" transform="scale(0.015625)"/>
       <path id="SimHei-28" d="M 2975 -200 
L 2700 -475 
Q 2075 125 1762 775 
Q 1450 1425 1450 2250 
Q 1450 3075 1762 3725 
Q 2075 4375 2700 5000 
L 2975 4725 
Q 2400 4175 2112 3587 
Q 1825 3000 1825 2250 
Q 1825 1500 2112 912 
Q 2400 325 2975 -200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-73" d="M 2750 900 
Q 2750 500 2437 287 
Q 2125 75 1650 75 
Q 1050 75 725 312 
Q 400 550 400 1000 
L 900 1000 
Q 900 700 1112 600 
Q 1325 500 1625 500 
Q 1925 500 2075 612 
Q 2225 725 2225 900 
Q 2225 1025 2100 1150 
Q 1975 1275 1475 1350 
Q 900 1425 687 1637 
Q 475 1850 475 2200 
Q 475 2500 762 2737 
Q 1050 2975 1600 2975 
Q 2100 2975 2387 2750 
Q 2675 2525 2675 2150 
L 2175 2150 
Q 2175 2375 2012 2462 
Q 1850 2550 1600 2550 
Q 1275 2550 1137 2437 
Q 1000 2325 1000 2175 
Q 1000 2000 1125 1900 
Q 1250 1800 1650 1750 
Q 2300 1650 2525 1437 
Q 2750 1225 2750 900 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-29" d="M 1675 2250 
Q 1675 1425 1362 775 
Q 1050 125 425 -475 
L 150 -200 
Q 725 325 1012 912 
Q 1300 1500 1300 2250 
Q 1300 3000 1012 3587 
Q 725 4175 150 4725 
L 425 5000 
Q 1050 4375 1362 3725 
Q 1675 3075 1675 2250 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-65f6"/>
      <use xlink:href="#SimHei-95f4" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-73" x="300"/>
      <use xlink:href="#SimHei-29" x="350"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_35">
      <path d="M 51.24875 392.105011 
L 709.2 392.105011 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <defs>
       <path id="mfbbda4d2e3" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mfbbda4d2e3" x="51.24875" y="392.105011" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- -0.10 -->
      <g transform="translate(24.24875 394.855011) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-31" x="150"/>
       <use xlink:href="#SimHei-30" x="200"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_37">
      <path d="M 51.24875 318.234324 
L 709.2 318.234324 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#mfbbda4d2e3" x="51.24875" y="318.234324" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- -0.05 -->
      <g transform="translate(24.24875 320.984324) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-30" x="150"/>
       <use xlink:href="#SimHei-35" x="200"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_39">
      <path d="M 51.24875 244.363637 
L 709.2 244.363637 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#mfbbda4d2e3" x="51.24875" y="244.363637" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.00 -->
      <g transform="translate(28.24875 247.113637) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
       <use xlink:href="#SimHei-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_41">
      <path d="M 51.24875 170.49295 
L 709.2 170.49295 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mfbbda4d2e3" x="51.24875" y="170.49295" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.05 -->
      <g transform="translate(28.24875 173.24295) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
       <use xlink:href="#SimHei-35" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_43">
      <path d="M 51.24875 96.622263 
L 709.2 96.622263 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mfbbda4d2e3" x="51.24875" y="96.622263" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.10 -->
      <g transform="translate(28.24875 99.372263) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-31" x="100"/>
       <use xlink:href="#SimHei-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_45">
      <defs>
       <path id="m8ed100f04c" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="377.330873" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_46">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="362.556736" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_47">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="347.782598" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_48">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="333.008461" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_49">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="303.460186" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_50">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="288.686049" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_51">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="273.911911" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_52">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="259.137774" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_53">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="229.589499" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_54">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="214.815362" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_55">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="200.041224" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_56">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="185.267087" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_57">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="155.718812" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_58">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="140.944675" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_59">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="126.170537" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_60">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="111.3964" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_61">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="81.848125" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_62">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="67.073988" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_63">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="52.29985" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_64">
      <g>
       <use xlink:href="#m8ed100f04c" x="51.24875" y="37.525713" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_14">
     <!-- 振幅 (μV) -->
     <g transform="translate(18.99875 234.774375) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-632f" d="M 3275 -200 
Q 3400 -100 3437 37 
Q 3475 175 3475 500 
L 3475 2300 
L 3025 2300 
Q 3000 1800 2950 1375 
Q 2900 950 2800 600 
Q 2700 250 2575 -37 
Q 2450 -325 2325 -625 
Q 2175 -475 1825 -350 
Q 2150 -25 2300 412 
Q 2450 850 2500 1350 
Q 2550 1850 2562 2437 
Q 2575 3025 2575 3625 
Q 2575 4250 2525 4825 
L 5150 4825 
Q 5475 4825 5825 4850 
L 5825 4375 
Q 5400 4400 5225 4400 
L 3025 4400 
L 3025 2700 
L 5375 2700 
Q 5600 2700 5975 2725 
L 5975 2275 
Q 5850 2300 5475 2300 
L 4675 2300 
Q 4775 1600 4925 1275 
Q 5125 1450 5262 1587 
Q 5400 1725 5550 1975 
Q 5650 1775 5900 1575 
Q 5750 1500 5575 1337 
Q 5400 1175 5125 875 
Q 5225 650 5500 362 
Q 5775 75 6175 -75 
Q 5875 -250 5775 -525 
Q 5300 -175 5075 100 
Q 4850 375 4687 687 
Q 4525 1000 4412 1387 
Q 4300 1775 4250 2300 
L 3950 2300 
L 3950 150 
Q 4250 300 4575 475 
Q 4625 325 4750 50 
Q 4575 -25 4237 -212 
Q 3900 -400 3600 -650 
Q 3500 -450 3275 -200 
z
M 250 2000 
Q 550 2075 725 2125 
L 1150 2275 
L 1150 3350 
Q 675 3350 325 3325 
L 325 3800 
Q 650 3775 1150 3775 
L 1150 4500 
Q 1150 4850 1125 5150 
L 1675 5150 
Q 1650 4875 1650 4500 
L 1650 3775 
Q 1875 3775 2300 3800 
L 2300 3325 
Q 1900 3350 1650 3350 
L 1650 2425 
Q 2000 2550 2250 2675 
Q 2250 2475 2275 2250 
Q 2100 2200 1650 2000 
L 1650 100 
Q 1600 -250 1450 -362 
Q 1300 -475 725 -525 
Q 700 -325 600 25 
Q 850 0 1000 0 
Q 1150 0 1150 300 
L 1150 1800 
Q 975 1725 850 1662 
Q 725 1600 475 1450 
Q 425 1675 250 2000 
z
M 4950 3775 
Q 5275 3775 5600 3800 
L 5600 3325 
Q 5275 3350 5000 3350 
L 4000 3350 
Q 3725 3350 3375 3325 
L 3375 3800 
Q 3700 3775 4000 3775 
L 4950 3775 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5e45" d="M 5950 2250 
Q 5925 1875 5925 900 
Q 5925 -50 5950 -425 
L 5450 -425 
L 5450 -175 
L 3350 -175 
L 3350 -475 
L 2875 -475 
Q 2900 200 2900 1025 
Q 2900 1875 2875 2250 
L 5950 2250 
z
M 2575 1125 
Q 2575 800 2437 700 
Q 2300 600 1950 525 
Q 1900 800 1750 1000 
Q 1975 1000 2075 1037 
Q 2175 1075 2175 1300 
L 2175 3725 
L 1700 3725 
L 1700 475 
Q 1700 0 1725 -550 
L 1225 -550 
Q 1250 -25 1250 475 
L 1250 3725 
L 850 3725 
L 850 500 
L 400 500 
Q 425 1000 425 2300 
Q 425 3600 400 4100 
L 1250 4100 
L 1250 4500 
Q 1250 4825 1225 5175 
L 1725 5175 
Q 1700 4825 1700 4500 
L 1700 4100 
L 2600 4100 
Q 2575 3900 2575 3225 
L 2575 1125 
z
M 5575 3925 
Q 5550 3625 5550 3300 
Q 5550 2975 5575 2600 
L 3200 2600 
Q 3225 2975 3225 3300 
Q 3225 3625 3200 3925 
L 5575 3925 
z
M 5250 4800 
Q 5650 4800 6025 4825 
L 6025 4375 
Q 5650 4400 5250 4400 
L 3550 4400 
Q 3075 4400 2800 4375 
L 2800 4825 
Q 3075 4800 3550 4800 
L 5250 4800 
z
M 5150 2975 
L 5150 3575 
L 3625 3575 
L 3625 2975 
L 5150 2975 
z
M 5450 1225 
L 5450 1875 
L 4625 1875 
L 4625 1225 
L 5450 1225 
z
M 4175 200 
L 4175 850 
L 3350 850 
L 3350 200 
L 4175 200 
z
M 5450 200 
L 5450 850 
L 4625 850 
L 4625 200 
L 5450 200 
z
M 4175 1225 
L 4175 1875 
L 3350 1875 
L 3350 1225 
L 4175 1225 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-3bc" d="M 2025 3200 
L 2450 3200 
L 2450 1050 
Q 2625 550 3100 500 
L 3300 500 
Q 3875 575 3800 1750 
L 3800 3200 
L 4200 3200 
L 4200 900 
Q 4225 600 4450 575 
L 4650 575 
L 4650 175 
L 4200 200 
Q 4000 275 3950 450 
L 3925 500 
Q 3575 75 3075 150 
Q 2750 175 2450 450 
L 2450 -750 
L 2025 -750 
L 2025 3200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-56" d="M 3050 4400 
L 1800 75 
L 1300 75 
L 50 4400 
L 650 4400 
L 1525 1125 
L 1575 1125 
L 2450 4400 
L 3050 4400 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-632f"/>
      <use xlink:href="#SimHei-5e45" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-3bc" x="300"/>
      <use xlink:href="#SimHei-56" x="400"/>
      <use xlink:href="#SimHei-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="PolyCollection_1">
    <path d="M 81.155625 145.659394 
L 81.155625 276.383768 
L 82.152521 290.461273 
L 83.149417 300.283222 
L 84.146312 300.748644 
L 85.143208 291.220619 
L 86.140104 276.477557 
L 87.137 263.462231 
L 88.133896 256.009506 
L 89.130792 253.743808 
L 90.127688 254.974038 
L 91.124583 258.99477 
L 92.121479 265.691241 
L 93.118375 274.017219 
L 94.115271 283.184496 
L 95.112167 293.594661 
L 96.109062 303.264444 
L 97.105958 306.739088 
L 98.102854 299.775743 
L 99.09975 283.816746 
L 100.096646 266.701233 
L 101.093542 256.038404 
L 102.090437 250.187905 
L 103.087333 244.304984 
L 104.084229 239.790569 
L 105.081125 242.236889 
L 106.078021 248.781598 
L 107.074917 252.669496 
L 108.071813 254.729394 
L 109.068708 261.427076 
L 110.065604 272.674921 
L 111.0625 279.564485 
L 112.059396 278.494495 
L 113.056292 273.90633 
L 114.053187 271.700783 
L 115.050083 274.549844 
L 116.046979 282.351213 
L 117.043875 292.77912 
L 118.040771 301.409892 
L 119.037667 303.932343 
L 120.034563 300.720712 
L 121.031458 298.749007 
L 122.028354 306.571372 
L 123.02525 325.894381 
L 124.022146 348.703289 
L 125.019042 362.530152 
L 126.015938 358.358014 
L 127.012833 335.910313 
L 128.009729 304.349862 
L 129.006625 279.126096 
L 130.003521 275.882111 
L 131.000417 299.572921 
L 131.997313 337.144728 
L 132.994208 368.247758 
L 133.991104 379.03678 
L 134.988 368.484137 
L 135.984896 346.775943 
L 136.981792 327.643982 
L 137.978688 320.531012 
L 138.975583 326.615102 
L 139.972479 337.998458 
L 140.969375 343.343467 
L 141.966271 337.538546 
L 142.963167 324.811999 
L 143.960062 313.859978 
L 144.956958 311.996795 
L 145.953854 321.861188 
L 146.95075 337.351077 
L 147.947646 348.202226 
L 148.944542 351.202154 
L 149.941438 352.231619 
L 150.938333 358.231722 
L 151.935229 370.662707 
L 152.932125 388.339026 
L 153.929021 410.316332 
L 154.925917 434.385084 
L 155.922813 457.84615 
L 156.919708 480.043584 
L 157.916604 501.392693 
L 158.9135 520.414742 
L 159.910396 533.075506 
L 160.907292 534.808694 
L 161.904188 523.046765 
L 162.901083 498.97753 
L 163.897979 467.656906 
L 164.894875 436.730932 
L 165.891771 418.055566 
L 166.888667 431.033126 
L 167.885563 476.504895 
L 168.882458 529.226276 
L 169.879354 571.192996 
L 170.87625 596.725694 
L 171.873146 607.310365 
L 172.870042 606.009678 
L 173.866938 593.604771 
L 174.863833 568.270487 
L 175.860729 528.397237 
L 176.857625 476.061155 
L 177.854521 418.208856 
L 178.851417 364.513591 
L 179.848313 324.140971 
L 180.845208 304.336318 
L 181.842104 309.974574 
L 182.839 341.680746 
L 183.835896 393.048372 
L 184.832792 450.936924 
L 185.829688 500.807749 
L 186.826583 532.784818 
L 187.823479 544.342945 
L 188.820375 538.948184 
L 189.817271 522.385367 
L 190.814167 499.758063 
L 191.811063 475.070559 
L 192.807958 452.094964 
L 193.804854 433.838835 
L 194.80175 420.703749 
L 195.798646 410.419977 
L 196.795542 400.454926 
L 197.792438 389.709383 
L 198.789333 379.193214 
L 199.786229 372.596222 
L 200.783125 374.205978 
L 201.780021 384.824253 
L 202.776917 400.235938 
L 203.773813 413.126594 
L 204.770708 416.006906 
L 205.767604 405.479236 
L 206.7645 386.386228 
L 207.761396 369.510789 
L 208.758292 362.567545 
L 209.755188 364.823619 
L 210.752083 370.568928 
L 211.748979 373.847224 
L 212.745875 370.566457 
L 213.742771 360.929135 
L 214.739667 350.311699 
L 215.736563 344.059948 
L 216.733458 342.439498 
L 217.730354 343.225554 
L 218.72725 345.977915 
L 219.724146 351.915295 
L 220.721042 361.795319 
L 221.717938 375.597943 
L 222.714833 392.204532 
L 223.711729 408.382575 
L 224.708625 420.419688 
L 225.705521 427.159673 
L 226.702417 430.294486 
L 227.699313 432.177535 
L 228.696208 434.230474 
L 229.693104 436.715902 
L 230.69 438.576234 
L 231.686896 437.060913 
L 232.683792 429.061132 
L 233.680688 414.607518 
L 234.677583 398.51503 
L 235.674479 386.366855 
L 236.671375 378.198837 
L 237.668271 370.1749 
L 238.665167 362.351391 
L 239.662063 357.367967 
L 240.658958 353.715524 
L 241.655854 348.785325 
L 242.65275 342.985758 
L 243.649646 337.38388 
L 244.646542 331.922212 
L 245.643438 327.817396 
L 246.640333 327.633107 
L 247.637229 332.374526 
L 248.634125 341.88888 
L 249.631021 355.197198 
L 250.627917 366.897872 
L 251.624813 368.497581 
L 252.621708 358.050683 
L 253.618604 342.263775 
L 254.6155 329.091606 
L 255.612396 324.231987 
L 256.609292 330.63489 
L 257.606188 345.435152 
L 258.603083 359.592807 
L 259.599979 365.336944 
L 260.596875 362.357377 
L 261.593771 355.779868 
L 262.590667 351.966705 
L 263.587563 354.452464 
L 264.584458 360.339027 
L 265.581354 363.657592 
L 266.57825 362.647016 
L 267.575146 359.690496 
L 268.572042 356.842226 
L 269.568938 356.770109 
L 270.565833 361.597187 
L 271.562729 367.389309 
L 272.559625 367.405046 
L 273.556521 360.955755 
L 274.553417 354.129822 
L 275.550313 352.391027 
L 276.547208 356.320944 
L 277.544104 363.934536 
L 278.541 371.906548 
L 279.537896 375.586196 
L 280.534792 372.441953 
L 281.531688 365.464184 
L 282.528583 362.159561 
L 283.525479 369.244384 
L 284.522375 385.990991 
L 285.519271 404.027688 
L 286.516167 415.0494 
L 287.513063 415.962393 
L 288.509958 407.5376 
L 289.506854 393.315414 
L 290.50375 380.10016 
L 291.500646 374.577249 
L 292.497542 378.076131 
L 293.494438 387.332898 
L 294.491333 398.473361 
L 295.488229 407.576801 
L 296.485125 410.910817 
L 297.482021 406.839314 
L 298.478917 396.101064 
L 299.475813 380.43403 
L 300.472708 362.556906 
L 301.469604 346.267541 
L 302.4665 334.561189 
L 303.463396 329.056005 
L 304.460292 331.256274 
L 305.457188 342.068565 
L 306.454083 359.450467 
L 307.450979 377.560151 
L 308.447875 389.254574 
L 309.444771 389.83215 
L 310.441667 379.376901 
L 311.438563 362.664112 
L 312.435458 346.674275 
L 313.432354 337.137362 
L 314.42925 335.869709 
L 315.426146 339.990267 
L 316.423042 344.011148 
L 317.419938 343.797245 
L 318.416833 338.547494 
L 319.413729 329.98288 
L 320.410625 321.713206 
L 321.407521 318.150953 
L 322.404417 320.374533 
L 323.401313 323.517397 
L 324.398208 321.855681 
L 325.395104 315.003459 
L 326.392 307.22603 
L 327.388896 302.786939 
L 328.385792 304.132359 
L 329.382688 311.500739 
L 330.379583 321.828902 
L 331.376479 329.435127 
L 332.373375 328.023573 
L 333.370271 313.306141 
L 334.367167 288.060743 
L 335.364063 264.593796 
L 336.360958 253.436572 
L 337.357854 253.268198 
L 338.35475 259.064971 
L 339.351646 268.95784 
L 340.348542 283.311163 
L 341.345438 296.616747 
L 342.342333 301.153848 
L 343.339229 298.46366 
L 344.336125 295.739403 
L 345.333021 294.980746 
L 346.329917 292.107049 
L 347.326813 284.930725 
L 348.323708 276.805041 
L 349.320604 271.625755 
L 350.3175 269.805597 
L 351.314396 271.956508 
L 352.311292 278.594675 
L 353.308188 286.951667 
L 354.305083 292.229209 
L 355.301979 292.728221 
L 356.298875 290.68899 
L 357.295771 287.818337 
L 358.292667 284.297051 
L 359.289563 280.611393 
L 360.286458 278.085609 
L 361.283354 277.774802 
L 362.28025 279.164849 
L 363.277146 281.542212 
L 364.274042 284.820078 
L 365.270938 287.677286 
L 366.267833 287.800362 
L 367.264729 283.996862 
L 368.261625 276.787858 
L 369.258521 267.750846 
L 370.255417 260.31524 
L 371.252313 259.330964 
L 372.249208 264.858758 
L 373.246104 271.127907 
L 374.243 273.100455 
L 375.239896 269.639688 
L 376.236792 263.777086 
L 377.233688 261.661216 
L 378.230583 267.814258 
L 379.227479 278.796097 
L 380.224375 285.994369 
L 381.221271 284.350614 
L 382.218167 276.064952 
L 383.215063 266.916755 
L 384.211958 259.53573 
L 385.208854 253.03775 
L 386.20575 246.647688 
L 387.202646 240.101409 
L 388.199542 232.005574 
L 389.196438 222.119453 
L 390.193333 215.949858 
L 391.190229 220.690724 
L 392.187125 234.84249 
L 393.184021 249.593266 
L 394.180917 257.169784 
L 395.177813 254.728623 
L 396.174708 244.525328 
L 397.171604 232.944171 
L 398.1685 226.57794 
L 399.165396 227.281438 
L 400.162292 233.803107 
L 401.159188 245.280062 
L 402.156083 258.162339 
L 403.152979 262.465432 
L 404.149875 252.726165 
L 405.146771 236.17354 
L 406.143667 222.050733 
L 407.140563 212.616781 
L 408.137458 209.160216 
L 409.134354 214.124266 
L 410.13125 226.446408 
L 411.128146 240.025333 
L 412.125042 249.248903 
L 413.121938 251.735895 
L 414.118833 247.062279 
L 415.115729 238.50985 
L 416.112625 232.966649 
L 417.109521 236.09928 
L 418.106417 249.861885 
L 419.103313 272.210526 
L 420.100208 293.965013 
L 421.097104 302.634703 
L 422.094 293.209517 
L 423.090896 273.056585 
L 424.087792 256.99383 
L 425.084688 255.901371 
L 426.081583 269.856294 
L 427.078479 290.115372 
L 428.075375 303.134185 
L 429.072271 297.352662 
L 430.069167 271.562356 
L 431.066063 237.166271 
L 432.062958 210.695187 
L 433.059854 202.553425 
L 434.05675 212.485955 
L 435.053646 231.993021 
L 436.050542 248.298749 
L 437.047438 251.958144 
L 438.044333 244.312173 
L 439.041229 235.07291 
L 440.038125 233.254159 
L 441.035021 241.420551 
L 442.031917 255.765263 
L 443.028813 268.307555 
L 444.025708 271.157301 
L 445.022604 262.996199 
L 446.0195 250.339876 
L 447.016396 241.028424 
L 448.013292 237.686385 
L 449.010188 238.321645 
L 450.007083 240.73171 
L 451.003979 244.801897 
L 452.000875 252.18843 
L 452.997771 264.528399 
L 453.994667 280.360995 
L 454.991563 293.644466 
L 455.988458 297.558052 
L 456.985354 290.957084 
L 457.98225 279.603584 
L 458.979146 269.640186 
L 459.976042 263.368008 
L 460.972938 260.564767 
L 461.969833 259.445213 
L 462.966729 257.114727 
L 463.963625 252.035621 
L 464.960521 247.260893 
L 465.957417 247.335008 
L 466.954313 253.107218 
L 467.951208 262.259576 
L 468.948104 271.284608 
L 469.945 275.422963 
L 470.941896 269.846024 
L 471.938792 256.897464 
L 472.935688 247.812412 
L 473.932583 249.409711 
L 474.929479 259.924561 
L 475.926375 276.823935 
L 476.923271 298.785457 
L 477.920167 319.393156 
L 478.917063 326.039666 
L 479.913958 312.906506 
L 480.910854 287.456681 
L 481.90775 262.198911 
L 482.904646 243.795395 
L 483.901542 234.466163 
L 484.898438 236.254316 
L 485.895333 248.770521 
L 486.892229 266.23624 
L 487.889125 280.828956 
L 488.886021 287.431585 
L 489.882917 285.279914 
L 490.879813 278.520978 
L 491.876708 273.853051 
L 492.873604 273.165158 
L 493.8705 272.589978 
L 494.867396 270.491248 
L 495.864292 268.573371 
L 496.861188 267.010766 
L 497.858083 263.977216 
L 498.854979 259.519483 
L 499.851875 256.052525 
L 500.848771 255.150479 
L 501.845667 256.609419 
L 502.842563 259.332968 
L 503.839458 261.342885 
L 504.836354 260.898878 
L 505.83325 257.451485 
L 506.830146 251.465937 
L 507.827042 246.635767 
L 508.823938 250.198505 
L 509.820833 263.557007 
L 510.817729 279.533932 
L 511.814625 290.94779 
L 512.811521 294.462865 
L 513.808417 288.20817 
L 514.805313 273.421549 
L 515.802208 259.406748 
L 516.799104 257.758397 
L 517.796 269.290491 
L 518.792896 285.913057 
L 519.789792 300.650797 
L 520.786688 309.000192 
L 521.783583 309.056326 
L 522.780479 304.497389 
L 523.777375 301.043284 
L 524.774271 298.982256 
L 525.771167 293.769172 
L 526.768063 283.391605 
L 527.764958 271.70306 
L 528.761854 262.721389 
L 529.75875 257.919459 
L 530.755646 260.371337 
L 531.752542 270.495507 
L 532.749438 281.260624 
L 533.746333 284.092683 
L 534.743229 276.558725 
L 535.740125 263.655844 
L 536.737021 253.540366 
L 537.733917 251.414938 
L 538.730813 255.574358 
L 539.727708 258.349908 
L 540.724604 252.79528 
L 541.7215 239.514797 
L 542.718396 227.110335 
L 543.715292 224.57468 
L 544.712188 232.575972 
L 545.709083 245.367316 
L 546.705979 256.561727 
L 547.702875 260.634126 
L 548.699771 257.716801 
L 549.696667 255.317741 
L 550.693563 261.586124 
L 551.690458 278.556074 
L 552.687354 300.019159 
L 553.68425 313.612703 
L 554.681146 310.173357 
L 555.678042 293.461394 
L 556.674938 278.214435 
L 557.671833 276.57351 
L 558.668729 289.162532 
L 559.665625 308.393981 
L 560.662521 320.676462 
L 561.659417 313.704034 
L 562.656313 288.728762 
L 563.653208 263.064864 
L 564.650104 254.017604 
L 565.647 259.9369 
L 566.643896 273.394313 
L 567.640792 287.077291 
L 568.637688 293.126938 
L 569.634583 290.275331 
L 570.631479 287.884514 
L 571.628375 297.636937 
L 572.625271 319.227655 
L 573.622167 340.589866 
L 574.619063 351.219316 
L 575.615958 347.461445 
L 576.612854 331.387205 
L 577.60975 311.128015 
L 578.606646 297.372408 
L 579.603542 295.884456 
L 580.600438 304.259435 
L 581.597333 316.029275 
L 582.594229 326.664585 
L 583.591125 335.770302 
L 584.588021 345.317644 
L 585.584917 356.448372 
L 586.581813 367.394822 
L 587.578708 374.224636 
L 588.575604 373.796362 
L 589.5725 366.672426 
L 590.569396 357.718237 
L 591.566292 353.094179 
L 592.563188 355.42474 
L 593.560083 362.003307 
L 594.556979 367.19729 
L 595.553875 366.183685 
L 596.550771 358.028207 
L 597.547667 346.205487 
L 598.544562 335.512635 
L 599.541458 328.758991 
L 600.538354 326.643627 
L 601.53525 327.105667 
L 602.532146 325.347712 
L 603.529042 318.066227 
L 604.525938 306.453826 
L 605.522833 294.835099 
L 606.519729 287.444109 
L 607.516625 286.715757 
L 608.513521 292.799017 
L 609.510417 302.279919 
L 610.507313 309.725721 
L 611.504208 312.825071 
L 612.501104 312.197848 
L 613.498 308.104152 
L 614.494896 301.954941 
L 615.491792 297.815603 
L 616.488688 299.831581 
L 617.485583 307.919891 
L 618.482479 317.806341 
L 619.479375 325.266507 
L 620.476271 328.202731 
L 621.473167 326.728013 
L 622.470063 323.300847 
L 623.466958 321.286041 
L 624.463854 322.236496 
L 625.46075 326.021432 
L 626.457646 331.77831 
L 627.454542 336.63236 
L 628.451438 336.596671 
L 629.448333 329.94008 
L 630.445229 319.216953 
L 631.442125 310.256834 
L 632.439021 307.522504 
L 633.435917 310.702749 
L 634.432813 317.169062 
L 635.429708 323.868016 
L 636.426604 328.364047 
L 637.4235 329.392057 
L 638.420396 325.261074 
L 639.417292 315.008062 
L 640.414188 302.711876 
L 641.411083 295.303349 
L 642.407979 296.069844 
L 643.404875 303.535493 
L 644.401771 314.931855 
L 645.398667 326.648334 
L 646.395563 333.610338 
L 647.392458 334.044688 
L 648.389354 330.955219 
L 649.38625 327.745634 
L 650.383146 324.939013 
L 651.380042 320.697738 
L 652.376938 313.047579 
L 653.373833 302.93859 
L 654.370729 296.275427 
L 655.367625 299.924752 
L 656.364521 314.199568 
L 657.361417 332.435319 
L 658.358313 346.360056 
L 659.355208 348.833281 
L 660.352104 336.430133 
L 661.349 314.384274 
L 662.345896 295.018654 
L 663.342792 287.082657 
L 664.339688 288.939614 
L 665.336583 294.466237 
L 666.333479 299.621441 
L 667.330375 301.899063 
L 668.327271 300.801792 
L 669.324167 300.02054 
L 670.321063 303.849517 
L 671.317958 312.984911 
L 672.314854 324.205883 
L 673.31175 330.829213 
L 674.308646 326.746659 
L 675.305542 313.641375 
L 676.302438 300.688059 
L 677.299333 295.37459 
L 678.296229 297.50698 
L 679.293125 303.775776 
L 679.293125 194.643217 
L 679.293125 194.643217 
L 678.296229 186.872848 
L 677.299333 183.711941 
L 676.302438 188.339559 
L 675.305542 198.243765 
L 674.308646 209.818172 
L 673.31175 218.841007 
L 672.314854 220.066256 
L 671.317958 211.671209 
L 670.321063 199.450895 
L 669.324167 191.985315 
L 668.327271 192.829263 
L 667.330375 198.783684 
L 666.333479 202.35161 
L 665.336583 197.9012 
L 664.339688 189.114703 
L 663.342792 185.831011 
L 662.345896 193.223676 
L 661.349 208.91591 
L 660.352104 227.445239 
L 659.355208 241.557133 
L 658.358313 243.784862 
L 657.361417 232.808756 
L 656.364521 216.639283 
L 655.367625 205.261022 
L 654.370729 202.362184 
L 653.373833 206.041786 
L 652.376938 212.496471 
L 651.380042 217.438 
L 650.383146 218.871504 
L 649.38625 219.079806 
L 648.389354 221.771186 
L 647.392458 227.930372 
L 646.395563 234.909015 
L 645.398667 237.843582 
L 644.401771 232.297099 
L 643.404875 219.379033 
L 642.407979 207.651612 
L 641.411083 204.952102 
L 640.414188 211.913787 
L 639.417292 223.985415 
L 638.420396 235.236717 
L 637.4235 239.654787 
L 636.426604 234.408098 
L 635.429708 224.07558 
L 634.432813 216.939224 
L 633.435917 217.483957 
L 632.439021 223.801153 
L 631.442125 229.563015 
L 630.445229 229.364459 
L 629.448333 224.345033 
L 628.451438 219.809658 
L 627.454542 219.605247 
L 626.457646 223.57216 
L 625.46075 228.763927 
L 624.463854 232.876655 
L 623.466958 235.329489 
L 622.470063 235.660507 
L 621.473167 233.900602 
L 620.476271 230.762487 
L 619.479375 226.200271 
L 618.482479 219.964686 
L 617.485583 213.044552 
L 616.488688 207.096888 
L 615.491792 203.841961 
L 614.494896 205.964281 
L 613.498 215.142333 
L 612.501104 228.392575 
L 611.504208 238.625584 
L 610.507313 240.183863 
L 609.510417 233.245234 
L 608.513521 220.862887 
L 607.516625 206.409929 
L 606.519729 194.799163 
L 605.522833 190.404021 
L 604.525938 194.186569 
L 603.529042 204.438812 
L 602.532146 218.503824 
L 601.53525 233.307586 
L 600.538354 245.861453 
L 599.541458 255.03863 
L 598.544562 262.242893 
L 597.547667 268.005493 
L 596.550771 270.666014 
L 595.553875 269.147985 
L 594.556979 264.236285 
L 593.560083 258.308442 
L 592.563188 254.404794 
L 591.566292 254.179036 
L 590.569396 256.405618 
L 589.5725 258.087111 
L 588.575604 256.568721 
L 587.578708 251.060631 
L 586.581813 243.527642 
L 585.584917 237.550198 
L 584.588021 235.178918 
L 583.591125 234.573792 
L 582.594229 230.993086 
L 581.597333 220.907675 
L 580.600438 206.070379 
L 579.603542 193.513425 
L 578.606646 191.198421 
L 577.60975 203.036108 
L 576.612854 225.810373 
L 575.615958 248.939636 
L 574.619063 259.33898 
L 573.622167 251.421237 
L 572.625271 232.332987 
L 571.628375 213.216231 
L 570.631479 199.649306 
L 569.634583 193.728228 
L 568.637688 194.912242 
L 567.640792 197.463271 
L 566.643896 195.667473 
L 565.647 192.372018 
L 564.650104 197.765351 
L 563.653208 213.861505 
L 562.656313 230.82717 
L 561.659417 243.928844 
L 560.662521 249.541564 
L 559.665625 244.219183 
L 558.668729 230.969293 
L 557.671833 220.716323 
L 556.674938 221.962324 
L 555.678042 231.524283 
L 554.681146 240.951652 
L 553.68425 242.845238 
L 552.687354 233.253729 
L 551.690458 214.739076 
L 550.693563 196.887301 
L 549.696667 189.569988 
L 548.699771 194.186969 
L 547.702875 203.615227 
L 546.705979 208.146558 
L 545.709083 201.977676 
L 544.712188 188.780537 
L 543.715292 178.6975 
L 542.718396 177.027328 
L 541.7215 182.176207 
L 540.724604 190.301756 
L 539.727708 197.031503 
L 538.730813 199.418623 
L 537.733917 197.762539 
L 536.737021 195.170972 
L 535.740125 194.710744 
L 534.743229 196.355926 
L 533.746333 197.563442 
L 532.749438 196.218597 
L 531.752542 192.443219 
L 530.755646 188.101769 
L 529.75875 186.41982 
L 528.761854 190.133915 
L 527.764958 195.427483 
L 526.768063 193.701297 
L 525.771167 183.950157 
L 524.774271 173.712573 
L 523.777375 169.521471 
L 522.780479 172.14382 
L 521.783583 178.409824 
L 520.786688 183.653617 
L 519.789792 182.357154 
L 518.792896 172.730994 
L 517.796 161.273006 
L 516.799104 156.240534 
L 515.802208 158.716127 
L 514.805313 166.138512 
L 513.808417 176.203919 
L 512.811521 182.853973 
L 511.814625 180.153304 
L 510.817729 170.922079 
L 509.820833 164.076912 
L 508.823938 163.637758 
L 507.827042 165.919985 
L 506.830146 167.585111 
L 505.83325 167.879244 
L 504.836354 165.382544 
L 503.839458 161.131991 
L 502.842563 158.995047 
L 501.845667 161.281588 
L 500.848771 166.701946 
L 499.851875 171.689228 
L 498.854979 172.963924 
L 497.858083 170.83426 
L 496.861188 168.679192 
L 495.864292 168.322123 
L 494.867396 168.913666 
L 493.8705 170.41966 
L 492.873604 173.942095 
L 491.876708 177.563268 
L 490.879813 177.041211 
L 489.882917 172.568854 
L 488.886021 167.284559 
L 487.889125 161.349314 
L 486.892229 152.715352 
L 485.895333 140.503506 
L 484.898438 126.77155 
L 483.901542 117.710769 
L 482.904646 121.129903 
L 481.90775 138.40642 
L 480.910854 160.931316 
L 479.913958 178.068142 
L 478.917063 185.869397 
L 477.920167 183.764678 
L 476.923271 170.43683 
L 475.926375 148.649443 
L 474.929479 131.176949 
L 473.932583 130.46224 
L 472.935688 145.551659 
L 471.938792 163.594684 
L 470.941896 174.071296 
L 469.945 174.548091 
L 468.948104 165.035563 
L 467.951208 150.38024 
L 466.954313 140.698713 
L 465.957417 142.69727 
L 464.960521 153.311391 
L 463.963625 163.237683 
L 462.966729 165.655408 
L 461.969833 159.837975 
L 460.972938 150.904891 
L 459.976042 147.774376 
L 458.979146 156.147565 
L 457.98225 172.45846 
L 456.985354 186.29862 
L 455.988458 190.127547 
L 454.991563 184.093703 
L 453.994667 172.563974 
L 452.997771 160.554495 
L 452.000875 151.745607 
L 451.003979 146.943286 
L 450.007083 144.102388 
L 449.010188 141.404136 
L 448.013292 140.193549 
L 447.016396 143.355666 
L 446.0195 151.27684 
L 445.022604 160.823963 
L 444.025708 167.218073 
L 443.028813 165.81503 
L 442.031917 154.916059 
L 441.035021 139.450159 
L 440.038125 129.78465 
L 439.041229 132.962832 
L 438.044333 146.139744 
L 437.047438 159.407049 
L 436.050542 163.258662 
L 435.053646 154.093211 
L 434.05675 136.995085 
L 433.059854 124.635013 
L 432.062958 128.024102 
L 431.066063 146.885743 
L 430.069167 171.178759 
L 429.072271 189.04803 
L 428.075375 192.983269 
L 427.078479 183.177986 
L 426.081583 167.428222 
L 425.084688 156.306754 
L 424.087792 155.072603 
L 423.090896 160.486887 
L 422.094 166.797614 
L 421.097104 170.256968 
L 420.100208 168.724188 
L 419.103313 161.015623 
L 418.106417 148.048794 
L 417.109521 135.340962 
L 416.112625 129.242383 
L 415.115729 130.568439 
L 414.118833 136.043369 
L 413.121938 140.698688 
L 412.125042 138.545442 
L 411.128146 126.528369 
L 410.13125 107.660817 
L 409.134354 87.988254 
L 408.137458 74.758396 
L 407.140563 76.019041 
L 406.143667 94.290579 
L 405.146771 120.857813 
L 404.149875 141.790487 
L 403.152979 151.43058 
L 402.156083 150.789722 
L 401.159188 140.087371 
L 400.162292 123.871533 
L 399.165396 114.071133 
L 398.1685 117.832321 
L 397.171604 130.322748 
L 396.174708 140.783582 
L 395.177813 141.831582 
L 394.180917 132.681337 
L 393.184021 119.042382 
L 392.187125 109.530055 
L 391.190229 108.719574 
L 390.193333 114.068844 
L 389.196438 121.734381 
L 388.199542 131.003023 
L 387.202646 139.943987 
L 386.20575 145.953292 
L 385.208854 150.463119 
L 384.211958 157.241355 
L 383.215063 166.08595 
L 382.218167 171.661093 
L 381.221271 169.931749 
L 380.224375 162.824623 
L 379.227479 154.834794 
L 378.230583 148.377404 
L 377.233688 142.895542 
L 376.236792 138.763183 
L 375.239896 138.484341 
L 374.243 142.865648 
L 373.246104 150.615094 
L 372.249208 160.009778 
L 371.252313 168.40598 
L 370.255417 172.296917 
L 369.258521 171.371575 
L 368.261625 167.762415 
L 367.264729 162.488544 
L 366.267833 157.462144 
L 365.270938 156.139308 
L 364.274042 160.151749 
L 363.277146 167.082175 
L 362.28025 172.400758 
L 361.283354 172.669589 
L 360.286458 167.37593 
L 359.289563 160.78159 
L 358.292667 160.210467 
L 357.295771 169.585369 
L 356.298875 185.181108 
L 355.301979 197.951879 
L 354.305083 201.280398 
L 353.308188 195.853687 
L 352.311292 186.58992 
L 351.314396 179.197787 
L 350.3175 178.383711 
L 349.320604 184.570305 
L 348.323708 191.741311 
L 347.326813 192.680617 
L 346.329917 188.256714 
L 345.333021 184.751089 
L 344.336125 185.106932 
L 343.339229 186.931257 
L 342.342333 187.679698 
L 341.345438 186.41658 
L 340.348542 179.427843 
L 339.351646 162.198087 
L 338.35475 141.263337 
L 337.357854 132.189309 
L 336.360958 142.998262 
L 335.364063 167.199125 
L 334.367167 190.744257 
L 333.370271 206.395122 
L 332.373375 212.384226 
L 331.376479 207.541996 
L 330.379583 194.642902 
L 329.382688 180.791127 
L 328.385792 172.723113 
L 327.388896 172.854894 
L 326.392 178.296265 
L 325.395104 182.814969 
L 324.398208 182.15227 
L 323.401313 177.208175 
L 322.404417 170.905437 
L 321.407521 164.841092 
L 320.410625 160.401712 
L 319.413729 159.548339 
L 318.416833 161.412856 
L 317.419938 161.460535 
L 316.423042 155.803932 
L 315.426146 144.682435 
L 314.42925 131.783434 
L 313.432354 122.081939 
L 312.435458 119.702432 
L 311.438563 125.491019 
L 310.441667 135.933423 
L 309.444771 144.65737 
L 308.447875 145.414268 
L 307.450979 134.998879 
L 306.454083 114.912406 
L 305.457188 91.133625 
L 304.460292 72.35227 
L 303.463396 66.644187 
L 302.4665 77.033316 
L 301.469604 99.141947 
L 300.472708 123.935192 
L 299.475813 143.634537 
L 298.478917 154.918202 
L 297.482021 158.356743 
L 296.485125 157.333786 
L 295.488229 155.766755 
L 294.491333 155.19431 
L 293.494438 155.09078 
L 292.497542 155.622075 
L 291.500646 157.831364 
L 290.50375 162.187624 
L 289.506854 169.42277 
L 288.509958 179.510545 
L 287.513063 188.310783 
L 286.516167 189.69977 
L 285.519271 182.207201 
L 284.522375 170.443597 
L 283.525479 160.24362 
L 282.528583 155.848106 
L 281.531688 160.138221 
L 280.534792 172.470272 
L 279.537896 187.030593 
L 278.541 196.162008 
L 277.544104 195.721254 
L 276.547208 188.364936 
L 275.550313 180.921485 
L 274.553417 177.152289 
L 273.556521 175.885372 
L 272.559625 176.255423 
L 271.562729 179.483366 
L 270.565833 185.450021 
L 269.568938 192.132395 
L 268.572042 199.26848 
L 267.575146 207.232459 
L 266.57825 212.066006 
L 265.581354 209.689706 
L 264.584458 202.634201 
L 263.587563 196.579574 
L 262.590667 194.961142 
L 261.593771 198.29365 
L 260.596875 203.345183 
L 259.599979 203.142678 
L 258.603083 192.493868 
L 257.606188 172.589957 
L 256.609292 150.172269 
L 255.612396 135.631519 
L 254.6155 138.123687 
L 253.618604 157.010318 
L 252.621708 180.446801 
L 251.624813 195.377743 
L 250.627917 196.323222 
L 249.631021 184.066982 
L 248.634125 164.240405 
L 247.637229 147.081703 
L 246.640333 139.698064 
L 245.643438 139.979866 
L 244.646542 141.50649 
L 243.649646 141.059613 
L 242.65275 139.720173 
L 241.655854 141.372263 
L 240.658958 150.154572 
L 239.662063 164.962398 
L 238.665167 177.733831 
L 237.668271 181.818978 
L 236.671375 179.150945 
L 235.674479 174.019998 
L 234.677583 168.276599 
L 233.680688 164.528131 
L 232.683792 165.873159 
L 231.686896 171.722037 
L 230.69 178.094814 
L 229.693104 182.172533 
L 228.696208 184.998068 
L 227.699313 189.125346 
L 226.702417 194.816555 
L 225.705521 199.532595 
L 224.708625 200.653562 
L 223.711729 197.663591 
L 222.714833 191.56313 
L 221.717938 183.523587 
L 220.721042 174.712461 
L 219.724146 165.961327 
L 218.72725 157.496973 
L 217.730354 150.922417 
L 216.733458 150.059591 
L 215.736563 157.13813 
L 214.739667 169.189533 
L 213.742771 180.260542 
L 212.745875 186.040954 
L 211.748979 184.534581 
L 210.752083 176.51471 
L 209.755188 167.502355 
L 208.758292 165.008843 
L 207.761396 171.960806 
L 206.7645 184.961754 
L 205.767604 198.203965 
L 204.770708 205.663135 
L 203.773813 202.159921 
L 202.776917 187.193702 
L 201.780021 167.412194 
L 200.783125 152.626944 
L 199.786229 149.280901 
L 198.789333 157.476442 
L 197.792438 171.546343 
L 196.795542 182.91634 
L 195.798646 185.005552 
L 194.80175 176.763774 
L 193.804854 161.426028 
L 192.807958 143.636943 
L 191.811063 127.8688 
L 190.814167 116.85835 
L 189.817271 110.5164 
L 188.820375 106.941255 
L 187.823479 104.568967 
L 186.826583 102.913565 
L 185.829688 101.346996 
L 184.832792 97.910473 
L 183.835896 89.891616 
L 182.839 76.003334 
L 181.842104 58.530834 
L 180.845208 42.536219 
L 179.848313 30.869437 
L 178.851417 20.127761 
L 177.854521 2.735664 
L 176.857625 -26.129715 
L 175.860729 -63.089671 
L 174.863833 -97.253157 
L 173.866938 -115.551644 
L 172.870042 -108.646541 
L 171.873146 -74.401362 
L 170.87625 -17.902197 
L 169.879354 51.079234 
L 168.882458 121.149248 
L 167.885563 180.734951 
L 166.888667 216.049767 
L 165.891771 213.609552 
L 164.894875 186.782382 
L 163.897979 161.20756 
L 162.901083 145.440546 
L 161.904188 136.666683 
L 160.907292 128.755317 
L 159.910396 117.478327 
L 158.9135 103.263429 
L 157.916604 90.381924 
L 156.919708 83.743023 
L 155.922813 86.003466 
L 154.925917 95.978507 
L 153.929021 108.887951 
L 152.932125 119.868603 
L 151.935229 128.599824 
L 150.938333 138.147221 
L 149.941438 149.190231 
L 148.944542 160.018466 
L 147.947646 170.602382 
L 146.95075 180.852161 
L 145.953854 187.293769 
L 144.956958 185.762094 
L 143.960062 178.208856 
L 142.963167 170.90595 
L 141.966271 167.076742 
L 140.969375 166.420636 
L 139.972479 166.549779 
L 138.975583 164.355579 
L 137.978688 158.990797 
L 136.981792 154.223883 
L 135.984896 154.453122 
L 134.988 159.300604 
L 133.991104 164.409496 
L 132.994208 165.427543 
L 131.997313 161.318484 
L 131.000417 154.840621 
L 130.003521 149.727289 
L 129.006625 149.621844 
L 128.009729 157.976798 
L 127.012833 171.332068 
L 126.015938 180.198015 
L 125.019042 178.138112 
L 124.022146 167.284315 
L 123.02525 155.901539 
L 122.028354 151.497982 
L 121.031458 156.083852 
L 120.034563 166.595555 
L 119.037667 177.372306 
L 118.040771 182.627774 
L 117.043875 179.962922 
L 116.046979 172.960503 
L 115.050083 169.041213 
L 114.053187 173.012084 
L 113.056292 182.723222 
L 112.059396 191.85546 
L 111.0625 196.918208 
L 110.065604 198.402634 
L 109.068708 195.555498 
L 108.071813 185.887945 
L 107.074917 174.582568 
L 106.078021 169.594297 
L 105.081125 170.335455 
L 104.084229 168.671995 
L 103.087333 162.623668 
L 102.090437 160.599123 
L 101.093542 166.291273 
L 100.096646 174.133642 
L 99.09975 178.243069 
L 98.102854 180.33288 
L 97.105958 183.548177 
L 96.109062 187.694546 
L 95.112167 189.747427 
L 94.115271 186.638767 
L 93.118375 178.388429 
L 92.121479 166.910022 
L 91.124583 153.70208 
L 90.127688 141.99301 
L 89.130792 137.433009 
L 88.133896 143.884269 
L 87.137 159.495286 
L 86.140104 177.112233 
L 85.143208 188.869236 
L 84.146312 190.20713 
L 83.149417 180.588069 
L 82.152521 163.507016 
L 81.155625 145.659394 
z
" clip-path="url(#p1126db6402)" style="fill: #808080; fill-opacity: 0.2; stroke: #808080; stroke-opacity: 0.2"/>
   </g>
   <g id="patch_3">
    <path d="M 430.069167 394.495 
L 529.75875 394.495 
L 529.75875 25.05375 
L 430.069167 25.05375 
z
" clip-path="url(#p1126db6402)" style="fill: #0000ff; opacity: 0.2; stroke: #0000ff; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_65">
    <path d="M 81.155625 211.021581 
L 82.152521 226.984145 
L 83.149417 240.435645 
L 84.146312 245.477887 
L 85.143208 240.044928 
L 86.140104 226.794895 
L 87.137 211.478758 
L 88.133896 199.946887 
L 89.130792 195.588408 
L 90.127688 198.483524 
L 91.124583 206.348425 
L 93.118375 226.202824 
L 94.115271 234.911632 
L 95.112167 241.671044 
L 96.109062 245.479495 
L 97.105958 245.143633 
L 98.102854 240.054312 
L 99.09975 231.029908 
L 100.096646 220.417437 
L 101.093542 211.164839 
L 102.090437 205.393514 
L 103.087333 203.464326 
L 104.084229 204.231282 
L 105.081125 206.286172 
L 106.078021 209.187948 
L 107.074917 213.626032 
L 108.071813 220.30867 
L 109.068708 228.491287 
L 110.065604 235.538777 
L 111.0625 238.241347 
L 112.059396 235.174977 
L 113.056292 228.314776 
L 114.053187 222.356433 
L 115.050083 221.795529 
L 116.046979 227.655858 
L 117.043875 236.371021 
L 118.040771 242.018833 
L 119.037667 240.652324 
L 121.031458 227.416429 
L 122.028354 229.034677 
L 123.02525 240.89796 
L 124.022146 257.993802 
L 125.019042 270.334132 
L 126.015938 269.278015 
L 127.012833 253.621191 
L 128.009729 231.16333 
L 129.006625 214.37397 
L 130.003521 212.8047 
L 131.000417 227.206771 
L 131.997313 249.231606 
L 132.994208 266.837651 
L 133.991104 271.723138 
L 134.988 263.892371 
L 135.984896 250.614532 
L 136.981792 240.933932 
L 137.978688 239.760904 
L 138.975583 245.48534 
L 139.972479 252.274118 
L 140.969375 254.882052 
L 141.966271 252.307644 
L 142.963167 247.858974 
L 143.960062 246.034417 
L 144.956958 248.879444 
L 145.953854 254.577478 
L 146.95075 259.101619 
L 147.947646 259.402304 
L 148.944542 255.61031 
L 149.941438 250.710925 
L 150.938333 248.189472 
L 151.935229 249.631265 
L 152.932125 254.103815 
L 154.925917 265.181795 
L 155.922813 271.924808 
L 156.919708 281.893304 
L 157.916604 295.887309 
L 158.9135 311.839086 
L 159.910396 325.276917 
L 160.907292 331.782006 
L 161.904188 329.856724 
L 163.897979 314.432233 
L 164.894875 311.756657 
L 165.891771 315.832559 
L 166.888667 323.541447 
L 167.885563 328.619923 
L 168.882458 325.187762 
L 169.879354 311.136115 
L 171.873146 266.454502 
L 172.870042 248.681568 
L 173.866938 239.026563 
L 174.863833 235.508665 
L 175.860729 232.653783 
L 176.857625 224.96572 
L 177.854521 210.47226 
L 178.851417 192.320676 
L 179.848313 177.505204 
L 180.845208 173.436268 
L 181.842104 184.252704 
L 182.839 208.84204 
L 184.832792 274.423699 
L 185.829688 301.077372 
L 186.826583 317.849192 
L 187.823479 324.455956 
L 188.820375 322.944719 
L 189.817271 316.450883 
L 190.814167 308.308207 
L 191.811063 301.46968 
L 192.807958 297.865954 
L 193.804854 297.632431 
L 194.80175 298.733761 
L 195.798646 297.712764 
L 196.795542 291.685633 
L 198.789333 268.334828 
L 199.786229 260.938561 
L 200.783125 263.416461 
L 201.780021 276.118224 
L 202.776917 293.71482 
L 203.773813 307.643257 
L 204.770708 310.83502 
L 205.767604 301.841601 
L 207.761396 270.735798 
L 208.758292 263.788194 
L 209.755188 266.162987 
L 210.752083 273.541819 
L 211.748979 279.190902 
L 212.745875 278.303706 
L 213.742771 270.594839 
L 214.739667 259.750616 
L 215.736563 250.599039 
L 216.733458 246.249545 
L 217.730354 247.073986 
L 218.72725 251.737444 
L 219.724146 258.938311 
L 220.721042 268.25389 
L 223.711729 303.023083 
L 224.708625 310.536625 
L 225.705521 313.346134 
L 226.702417 312.55552 
L 227.699313 310.65144 
L 228.696208 309.614271 
L 229.693104 309.444217 
L 230.69 308.335524 
L 231.686896 304.391475 
L 232.683792 297.467146 
L 233.680688 289.567824 
L 234.677583 283.395815 
L 235.674479 280.193426 
L 236.671375 278.674891 
L 237.668271 275.996939 
L 238.665167 270.042611 
L 240.658958 251.935048 
L 241.655854 245.078794 
L 242.65275 241.352965 
L 243.649646 239.221746 
L 244.646542 236.714351 
L 245.643438 233.898631 
L 246.640333 233.665586 
L 247.637229 239.728114 
L 248.634125 253.064642 
L 249.631021 269.63209 
L 250.627917 281.610547 
L 251.624813 281.937662 
L 252.621708 269.248742 
L 253.618604 249.637046 
L 254.6155 233.607647 
L 255.612396 229.931753 
L 256.609292 240.40358 
L 258.603083 276.043338 
L 259.599979 284.239811 
L 260.596875 282.85128 
L 261.593771 277.036759 
L 262.590667 273.463924 
L 263.587563 275.516019 
L 264.584458 281.486614 
L 265.581354 286.673649 
L 266.57825 287.356511 
L 267.575146 283.461478 
L 268.572042 278.055353 
L 269.568938 274.451252 
L 270.565833 273.523604 
L 271.562729 273.436337 
L 272.559625 271.830234 
L 273.556521 268.420564 
L 274.553417 265.641056 
L 275.550313 266.656256 
L 276.547208 272.34294 
L 277.544104 279.827895 
L 278.541 284.034278 
L 279.537896 281.308395 
L 281.531688 262.801203 
L 282.528583 259.003833 
L 283.525479 264.744002 
L 285.519271 293.117445 
L 286.516167 302.374585 
L 287.513063 302.136588 
L 288.509958 293.524073 
L 289.506854 281.369092 
L 290.50375 271.143892 
L 291.500646 266.204307 
L 292.497542 266.849103 
L 293.494438 271.211839 
L 294.491333 276.833836 
L 295.488229 281.671778 
L 296.485125 284.122302 
L 297.482021 282.598029 
L 298.478917 275.509633 
L 299.475813 262.034283 
L 302.4665 205.797252 
L 303.463396 197.850096 
L 304.460292 201.804272 
L 305.457188 216.601095 
L 307.450979 256.279515 
L 308.447875 267.334421 
L 309.444771 267.24476 
L 310.441667 257.655162 
L 311.438563 244.077566 
L 312.435458 233.188353 
L 313.432354 229.609651 
L 314.42925 233.826572 
L 316.423042 249.90754 
L 317.419938 252.62889 
L 318.416833 249.980175 
L 319.413729 244.76561 
L 320.410625 241.057459 
L 321.407521 241.496023 
L 322.404417 245.639985 
L 323.401313 250.362786 
L 324.398208 252.003975 
L 325.395104 248.909214 
L 326.392 242.761147 
L 327.388896 237.820916 
L 328.385792 238.427736 
L 329.382688 246.145933 
L 330.379583 258.235902 
L 331.376479 268.488561 
L 332.373375 270.203899 
L 333.370271 259.850632 
L 334.367167 239.4025 
L 335.364063 215.89646 
L 336.360958 198.217417 
L 337.357854 192.728754 
L 338.35475 200.164154 
L 340.348542 231.369503 
L 341.345438 241.516664 
L 342.342333 244.416773 
L 343.339229 242.697459 
L 344.336125 240.423168 
L 345.333021 239.865917 
L 346.329917 240.181882 
L 347.326813 238.805671 
L 348.323708 234.273176 
L 349.320604 228.09803 
L 350.3175 224.094654 
L 351.314396 225.577148 
L 352.311292 232.592297 
L 353.308188 241.402677 
L 354.305083 246.754804 
L 355.301979 245.34005 
L 356.298875 237.935049 
L 357.295771 228.701853 
L 358.292667 222.253759 
L 359.289563 220.696492 
L 360.286458 222.73077 
L 361.283354 225.222195 
L 362.28025 225.782803 
L 363.277146 224.312193 
L 364.274042 222.485913 
L 365.270938 221.908297 
L 367.264729 223.242703 
L 368.261625 222.275136 
L 369.258521 219.56121 
L 370.255417 216.306078 
L 371.252313 213.868472 
L 373.246104 210.8715 
L 374.243 207.983052 
L 375.239896 204.062014 
L 376.236792 201.270135 
L 377.233688 202.278379 
L 378.230583 208.095831 
L 379.227479 216.815446 
L 380.224375 224.409496 
L 381.221271 227.141182 
L 382.218167 223.863022 
L 385.208854 201.750434 
L 386.20575 196.30049 
L 387.202646 190.022698 
L 388.199542 181.504299 
L 389.196438 171.926917 
L 390.193333 165.009351 
L 391.190229 164.705149 
L 392.187125 172.186272 
L 393.184021 184.317824 
L 394.180917 194.925561 
L 395.177813 198.280103 
L 396.174708 192.654455 
L 397.171604 181.63346 
L 398.1685 172.205131 
L 399.165396 170.676286 
L 400.162292 178.83732 
L 401.159188 192.683716 
L 402.156083 204.476031 
L 403.152979 206.948006 
L 404.149875 197.258326 
L 406.143667 158.170656 
L 407.140563 144.317911 
L 408.137458 141.959306 
L 409.134354 151.05626 
L 411.128146 183.276851 
L 412.125042 193.897173 
L 413.121938 196.217292 
L 414.118833 191.552824 
L 415.115729 184.539145 
L 416.112625 181.104516 
L 417.109521 185.720121 
L 418.106417 198.955339 
L 419.103313 216.613074 
L 420.100208 231.344601 
L 421.097104 236.445836 
L 422.094 230.003566 
L 423.090896 216.771736 
L 424.087792 206.033217 
L 425.084688 206.104063 
L 426.081583 218.642258 
L 427.078479 236.646679 
L 428.075375 248.058727 
L 429.072271 243.200346 
L 430.069167 221.370558 
L 431.066063 192.026007 
L 432.062958 169.359645 
L 433.059854 163.594219 
L 434.05675 174.74052 
L 435.053646 193.043116 
L 436.050542 205.778705 
L 437.047438 205.682596 
L 439.041229 184.017871 
L 440.038125 181.519404 
L 441.035021 190.435355 
L 442.031917 205.340661 
L 443.028813 217.061293 
L 444.025708 219.187687 
L 445.022604 211.910081 
L 446.0195 200.808358 
L 447.016396 192.192045 
L 448.013292 188.939967 
L 449.010188 189.862891 
L 450.007083 192.417049 
L 451.003979 195.872592 
L 452.000875 201.967018 
L 452.997771 212.541447 
L 454.991563 238.869085 
L 455.988458 243.842799 
L 456.985354 238.627852 
L 458.979146 212.893875 
L 459.976042 205.571192 
L 460.972938 205.734829 
L 461.969833 209.641594 
L 462.966729 211.385068 
L 463.963625 207.636652 
L 464.960521 200.286142 
L 465.957417 195.016139 
L 466.954313 196.902966 
L 467.951208 206.319908 
L 468.948104 218.160085 
L 469.945 224.985527 
L 470.941896 221.95866 
L 471.938792 210.246074 
L 472.935688 196.682035 
L 473.932583 189.935976 
L 474.929479 195.550755 
L 475.926375 212.736689 
L 476.923271 234.611144 
L 477.920167 251.578917 
L 478.917063 255.954531 
L 479.913958 245.487324 
L 480.910854 224.193998 
L 481.90775 200.302665 
L 482.904646 182.462649 
L 483.901542 176.088466 
L 484.898438 181.512933 
L 485.895333 194.637013 
L 486.892229 209.475796 
L 487.889125 221.089135 
L 488.886021 227.358072 
L 489.882917 228.924384 
L 490.879813 227.781094 
L 494.867396 219.702457 
L 495.864292 218.447747 
L 496.861188 217.844979 
L 497.858083 217.405738 
L 498.854979 216.241704 
L 499.851875 213.870877 
L 500.848771 210.926212 
L 501.845667 208.945504 
L 502.842563 209.164007 
L 504.836354 213.140711 
L 505.83325 212.665365 
L 507.827042 206.277876 
L 508.823938 206.918131 
L 509.820833 213.816959 
L 511.814625 235.550547 
L 512.811521 238.658419 
L 513.808417 232.206044 
L 514.805313 219.780031 
L 515.802208 209.061438 
L 516.799104 206.999465 
L 517.796 215.281749 
L 518.792896 229.322025 
L 519.789792 241.503975 
L 520.786688 246.326904 
L 521.783583 243.733075 
L 522.780479 238.320605 
L 523.777375 235.282378 
L 524.774271 236.347414 
L 525.771167 238.859665 
L 526.768063 238.546451 
L 527.764958 233.565271 
L 528.761854 226.427652 
L 529.75875 222.16964 
L 530.755646 224.236553 
L 532.749438 238.73961 
L 533.746333 240.828063 
L 534.743229 236.457325 
L 535.740125 229.183294 
L 536.737021 224.355669 
L 537.733917 224.588738 
L 538.730813 227.496491 
L 539.727708 227.690706 
L 540.724604 221.548518 
L 541.7215 210.845502 
L 542.718396 202.068831 
L 543.715292 201.63609 
L 544.712188 210.678254 
L 545.709083 223.672496 
L 546.705979 232.354142 
L 547.702875 232.124676 
L 548.699771 225.951885 
L 549.696667 222.443865 
L 550.693563 229.236712 
L 551.690458 246.647575 
L 552.687354 266.636444 
L 553.68425 278.228971 
L 554.681146 275.562505 
L 556.674938 250.088379 
L 557.671833 248.644916 
L 558.668729 260.065913 
L 559.665625 276.306582 
L 560.662521 285.109013 
L 561.659417 278.816439 
L 562.656313 259.777966 
L 563.653208 238.463185 
L 564.650104 225.891478 
L 565.647 226.154459 
L 567.640792 242.270281 
L 568.637688 244.01959 
L 569.634583 242.001779 
L 570.631479 243.76691 
L 571.628375 255.426584 
L 573.622167 296.005551 
L 574.619063 305.279148 
L 575.615958 298.200541 
L 577.60975 257.082061 
L 578.606646 244.285414 
L 579.603542 244.69894 
L 580.600438 255.164907 
L 581.597333 268.468475 
L 582.594229 278.828836 
L 583.591125 285.172047 
L 584.588021 290.248281 
L 585.584917 296.999285 
L 586.581813 305.461232 
L 587.578708 312.642634 
L 588.575604 315.182541 
L 589.5725 312.379768 
L 590.569396 307.061927 
L 591.566292 303.636607 
L 592.563188 304.914767 
L 594.556979 315.716788 
L 595.553875 317.665835 
L 596.550771 314.34711 
L 597.547667 307.10549 
L 598.544562 298.877764 
L 599.541458 291.898811 
L 601.53525 280.206626 
L 602.532146 271.925768 
L 604.525938 250.320198 
L 605.522833 242.61956 
L 606.519729 241.121636 
L 607.516625 246.562843 
L 609.510417 267.762576 
L 610.507313 274.954792 
L 611.504208 275.725328 
L 612.501104 270.295212 
L 613.498 261.623242 
L 614.494896 253.959611 
L 615.491792 250.828782 
L 616.488688 253.464234 
L 617.485583 260.482222 
L 618.482479 268.885514 
L 619.479375 275.733389 
L 620.476271 279.482609 
L 621.473167 280.314307 
L 622.470063 279.480677 
L 623.466958 278.307765 
L 624.463854 277.556576 
L 625.46075 277.392679 
L 626.457646 277.675235 
L 627.454542 278.118804 
L 628.451438 278.203164 
L 629.448333 277.142556 
L 630.445229 274.290706 
L 632.439021 265.661829 
L 633.435917 264.093353 
L 634.432813 267.054143 
L 636.426604 281.386073 
L 637.4235 284.523422 
L 638.420396 280.248896 
L 639.417292 269.496739 
L 640.414188 257.312832 
L 641.411083 250.127725 
L 642.407979 251.860728 
L 643.404875 261.457263 
L 644.401771 273.614477 
L 645.398667 282.245958 
L 646.395563 284.259677 
L 647.392458 280.98753 
L 648.389354 276.363203 
L 649.38625 273.41272 
L 650.383146 271.905259 
L 651.380042 269.067869 
L 652.376938 262.772025 
L 653.373833 254.490188 
L 654.370729 249.318805 
L 655.367625 252.592887 
L 656.364521 265.419425 
L 657.361417 282.622038 
L 658.358313 295.072459 
L 659.355208 295.195207 
L 660.352104 281.937686 
L 661.349 261.650092 
L 662.345896 244.121165 
L 663.342792 236.456834 
L 664.339688 239.027159 
L 665.336583 246.183718 
L 666.333479 250.986525 
L 667.330375 250.341374 
L 668.327271 246.815528 
L 669.324167 246.002927 
L 670.321063 251.650206 
L 672.314854 272.13607 
L 673.31175 274.83511 
L 674.308646 268.282416 
L 676.302438 244.513809 
L 677.299333 239.543266 
L 678.296229 242.189914 
L 679.293125 249.209497 
L 679.293125 249.209497 
" clip-path="url(#p1126db6402)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_66">
    <path d="M 180.845208 394.495 
L 180.845208 25.05375 
" clip-path="url(#p1126db6402)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-opacity: 0.5; stroke-width: 1.5"/>
   </g>
   <g id="patch_4">
    <path d="M 51.24875 394.495 
L 51.24875 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 709.2 394.495 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 51.24875 394.495 
L 709.2 394.495 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 51.24875 25.05375 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- test1阶段显著导联的平均HEP波形 -->
    <g transform="translate(305.224375 19.05375) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-74" d="M 2750 200 
Q 2625 150 2462 112 
Q 2300 75 2025 75 
Q 1575 75 1300 325 
Q 1025 575 1025 1025 
L 1025 2525 
L 175 2525 
L 175 2925 
L 1025 2925 
L 1025 3900 
L 1525 3900 
L 1525 2925 
L 2550 2925 
L 2550 2525 
L 1525 2525 
L 1525 1000 
Q 1525 800 1625 662 
Q 1725 525 2000 525 
Q 2275 525 2450 575 
Q 2625 625 2750 700 
L 2750 200 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-65" d="M 2850 1075 
Q 2800 625 2450 350 
Q 2100 75 1625 75 
Q 1025 75 637 462 
Q 250 850 250 1525 
Q 250 2200 637 2587 
Q 1025 2975 1625 2975 
Q 2150 2975 2487 2637 
Q 2825 2300 2825 1525 
L 800 1525 
Q 800 975 1037 750 
Q 1275 525 1625 525 
Q 1900 525 2075 662 
Q 2250 800 2300 1075 
L 2850 1075 
z
M 2250 1925 
Q 2200 2275 2025 2412 
Q 1850 2550 1575 2550 
Q 1325 2550 1125 2412 
Q 925 2275 825 1925 
L 2250 1925 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-9636" d="M 2450 4850 
Q 2350 4575 2175 3962 
Q 2000 3350 1825 2800 
Q 2225 2225 2312 1912 
Q 2400 1600 2375 1250 
Q 2350 900 2137 725 
Q 1925 550 1400 475 
Q 1300 925 1200 1075 
Q 1725 1025 1837 1225 
Q 1950 1425 1875 1775 
Q 1800 2125 1350 2725 
Q 1700 3825 1825 4400 
L 975 4400 
L 975 -500 
L 500 -500 
Q 525 175 525 675 
L 525 3650 
Q 525 4225 500 4850 
L 2450 4850 
z
M 4250 4825 
Q 4600 4100 5187 3637 
Q 5775 3175 6200 3025 
Q 5950 2775 5800 2500 
Q 4975 3075 4600 3512 
Q 4225 3950 4025 4400 
Q 3850 4025 3512 3487 
Q 3175 2950 2625 2350 
Q 2425 2575 2175 2700 
Q 2700 3075 3150 3787 
Q 3600 4500 3850 5275 
Q 4050 5150 4375 5075 
Q 4275 4900 4250 4825 
z
M 3750 2700 
Q 3650 2325 3575 1512 
Q 3500 700 3250 225 
Q 3000 -250 2650 -575 
Q 2425 -400 2175 -300 
Q 2550 -25 2762 337 
Q 2975 700 3087 1200 
Q 3200 1700 3200 2800 
Q 3350 2725 3750 2700 
z
M 5025 2775 
Q 4975 2425 4975 2025 
L 4975 350 
Q 4975 100 5000 -525 
L 4475 -525 
Q 4500 150 4500 375 
L 4500 2025 
Q 4500 2375 4475 2775 
L 5025 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6bb5" d="M 5500 2475 
Q 5350 1775 5100 1237 
Q 4850 700 4600 400 
Q 4825 225 5250 87 
Q 5675 -50 6125 -125 
Q 5950 -275 5825 -650 
Q 5150 -450 4825 -287 
Q 4500 -125 4225 75 
Q 3825 -175 3450 -337 
Q 3075 -500 2625 -650 
Q 2525 -400 2325 -225 
Q 2675 -150 3100 0 
Q 3525 150 3925 425 
Q 3725 675 3525 1150 
Q 3325 1600 3225 2075 
Q 3050 2075 2900 2075 
L 2900 2500 
Q 3375 2475 3800 2475 
L 5500 2475 
z
M 2650 4625 
Q 2300 4550 1887 4475 
Q 1475 4400 1300 4375 
L 1300 3550 
Q 2050 3550 2525 3600 
L 2525 3125 
Q 2050 3150 1300 3150 
L 1300 2350 
Q 2050 2350 2525 2375 
L 2525 1900 
Q 2050 1925 1300 1925 
L 1300 975 
Q 1950 1075 2625 1250 
Q 2600 1000 2600 775 
Q 1900 675 1300 525 
L 1300 100 
Q 1300 -200 1325 -625 
L 800 -625 
Q 825 -200 825 100 
L 825 425 
Q 575 375 350 300 
Q 300 650 250 825 
Q 500 850 825 900 
L 825 3800 
Q 825 4225 800 4725 
Q 1100 4725 1575 4825 
Q 2050 4925 2375 5125 
Q 2475 4850 2650 4625 
z
M 5100 4975 
Q 5075 4600 5075 4275 
L 5075 3650 
Q 5075 3425 5237 3387 
Q 5400 3350 5975 3400 
Q 5875 3225 5850 2950 
L 5075 2950 
Q 4650 2950 4625 3375 
L 4625 4575 
L 3775 4575 
Q 3775 3925 3675 3525 
Q 3575 3125 3200 2725 
Q 3025 2900 2800 3050 
Q 3275 3400 3300 3937 
Q 3325 4475 3300 4975 
L 5100 4975 
z
M 3675 2100 
Q 3775 1675 3962 1287 
Q 4150 900 4275 775 
Q 4450 950 4625 1312 
Q 4800 1675 4900 2100 
L 3800 2100 
L 3675 2100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-663e" d="M 1150 2400 
Q 1175 2975 1175 3700 
Q 1175 4450 1150 4925 
L 5275 4925 
Q 5250 4400 5250 3675 
Q 5250 2975 5275 2400 
L 1150 2400 
z
M 4175 2275 
Q 4150 1750 4150 1075 
L 4150 25 
Q 5525 25 6050 50 
L 6050 -425 
Q 5525 -400 5125 -400 
L 1400 -400 
Q 850 -400 400 -425 
L 400 50 
Q 875 25 2200 25 
L 2200 1050 
Q 2200 1750 2175 2275 
L 2725 2275 
Q 2700 1750 2700 1050 
L 2700 25 
L 3650 25 
L 3650 1075 
Q 3650 1750 3625 2275 
L 4175 2275 
z
M 4750 3850 
L 4750 4500 
L 1675 4500 
L 1675 3850 
L 4750 3850 
z
M 4750 2850 
L 4750 3400 
L 1675 3400 
L 1675 2850 
L 4750 2850 
z
M 650 1625 
Q 900 1725 1100 1900 
Q 1625 1075 1900 500 
Q 1625 375 1425 225 
Q 1200 750 650 1625 
z
M 5750 1575 
Q 5650 1475 5425 1062 
Q 5200 650 4875 200 
Q 4700 350 4450 475 
Q 5000 1250 5225 1900 
Q 5475 1675 5750 1575 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8457" d="M 5225 1650 
Q 5200 1250 5200 550 
Q 5200 -150 5225 -550 
L 4700 -550 
L 4700 -300 
L 2050 -300 
L 2050 -600 
L 1525 -600 
Q 1550 -175 1550 975 
Q 1000 750 575 575 
Q 500 800 225 1025 
Q 775 1075 1550 1400 
L 1550 1650 
L 2250 1650 
Q 2675 1850 3125 2125 
L 1225 2125 
Q 775 2125 300 2100 
L 300 2550 
Q 775 2525 1200 2525 
L 2750 2525 
L 2750 3000 
L 2125 3000 
Q 1575 3000 1175 2975 
L 1175 3400 
Q 1575 3375 2100 3375 
L 2750 3375 
Q 2750 3575 2725 3825 
L 3275 3825 
Q 3250 3575 3250 3375 
Q 3775 3375 4275 3400 
L 4275 3025 
Q 4550 3300 4675 3450 
Q 4800 3600 4925 3800 
Q 5150 3625 5425 3500 
Q 5275 3425 5087 3237 
Q 4900 3050 4350 2525 
L 5300 2525 
Q 5750 2525 6150 2550 
L 6150 2100 
Q 5775 2125 5325 2125 
L 3850 2125 
Q 3500 1850 3100 1650 
L 5225 1650 
z
M 2475 5100 
Q 2450 4875 2450 4625 
L 3850 4625 
Q 3850 4800 3825 5100 
L 4375 5100 
Q 4350 4850 4350 4625 
L 4975 4625 
Q 5475 4625 6000 4650 
L 6000 4225 
Q 5525 4250 4975 4250 
L 4350 4250 
Q 4350 4100 4375 3775 
L 3850 3775 
L 3850 4250 
L 2450 4250 
L 2450 3725 
L 1925 3725 
Q 1950 4025 1950 4250 
L 1425 4250 
Q 875 4250 375 4225 
L 375 4650 
Q 875 4625 1425 4625 
L 1950 4625 
Q 1950 4825 1925 5100 
L 2475 5100 
z
M 4700 850 
L 4700 1275 
L 2050 1275 
L 2050 850 
L 4700 850 
z
M 4700 75 
L 4700 475 
L 2050 475 
L 2050 75 
L 4700 75 
z
M 3700 2525 
Q 3925 2700 4225 2975 
Q 3800 3000 3250 3000 
L 3250 2525 
L 3700 2525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5bfc" d="M 5125 5100 
Q 5100 4850 5100 4350 
Q 5100 3875 5125 3550 
L 1600 3550 
L 1600 3200 
Q 1600 2850 2050 2850 
L 4725 2850 
Q 5100 2850 5212 2950 
Q 5325 3050 5350 3500 
Q 5600 3300 5925 3200 
Q 5700 2625 5575 2525 
Q 5450 2425 5075 2425 
L 1675 2425 
Q 1100 2425 1100 3025 
L 1100 4450 
Q 1100 4775 1050 5100 
L 5125 5100 
z
M 4200 1725 
Q 4200 1950 4175 2300 
L 4725 2300 
Q 4700 1900 4700 1725 
L 5200 1725 
Q 5575 1725 6100 1750 
L 6100 1300 
Q 5575 1325 5200 1325 
L 4700 1325 
L 4700 225 
Q 4700 -225 4450 -375 
Q 4200 -525 3550 -650 
Q 3475 -325 3325 -50 
Q 3975 -50 4087 25 
Q 4200 100 4200 300 
L 4200 1325 
L 1425 1325 
Q 900 1325 350 1300 
L 350 1750 
Q 850 1725 1400 1725 
L 4200 1725 
z
M 4625 3975 
L 4625 4700 
L 1600 4700 
L 1600 3975 
L 4625 3975 
z
M 1625 1150 
Q 2000 725 2375 125 
Q 2100 -50 1925 -175 
Q 1650 325 1250 825 
Q 1450 950 1625 1150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8054" d="M 2325 4850 
Q 2625 4850 2975 4875 
L 2975 4450 
Q 2700 4475 2475 4475 
L 2475 1150 
Q 2750 1225 2975 1275 
Q 2925 950 2950 850 
Q 2650 800 2475 750 
L 2475 175 
Q 2475 -200 2500 -675 
L 1975 -675 
Q 2025 -200 2025 175 
L 2025 650 
Q 1750 600 1337 475 
Q 925 350 450 175 
Q 425 350 275 750 
Q 425 750 800 825 
L 800 4475 
Q 550 4475 325 4425 
L 325 4875 
Q 625 4850 950 4850 
L 2325 4850 
z
M 5450 4950 
Q 5250 4675 5162 4487 
Q 5075 4300 4850 3900 
Q 5375 3900 5825 3925 
L 5825 3475 
Q 5375 3525 5050 3525 
L 4550 3525 
Q 4525 2600 4500 2225 
L 5225 2225 
Q 5525 2225 5950 2250 
L 5950 1800 
Q 5525 1825 5225 1825 
L 4575 1825 
Q 4725 1150 5175 625 
Q 5625 100 6175 -25 
Q 6000 -175 5912 -312 
Q 5825 -450 5800 -625 
Q 5275 -250 4925 200 
Q 4575 650 4350 1325 
Q 4175 750 3900 275 
Q 3625 -200 3200 -675 
Q 3125 -575 2975 -450 
Q 2825 -325 2750 -325 
Q 3100 -100 3412 387 
Q 3725 875 3825 1187 
Q 3925 1500 3975 1825 
L 3675 1825 
Q 3275 1825 2825 1800 
L 2825 2250 
Q 3275 2225 3675 2225 
L 4025 2225 
Q 4075 2950 4075 3525 
L 3825 3525 
Q 3475 3525 3000 3475 
L 3000 3925 
Q 3475 3900 3825 3900 
L 4450 3900 
Q 4750 4600 4900 5225 
Q 5050 5125 5450 4950 
z
M 2025 2225 
L 2025 3200 
L 1250 3200 
L 1250 2225 
L 2025 2225 
z
M 2025 3600 
L 2025 4475 
L 1250 4475 
L 1250 3600 
L 2025 3600 
z
M 2025 1825 
L 1250 1825 
L 1250 900 
Q 1525 950 2025 1050 
L 2025 1825 
z
M 3500 5175 
Q 3950 4475 4075 4250 
Q 3775 4125 3675 4000 
Q 3550 4300 3150 4950 
Q 3225 4975 3500 5175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7684" d="M 2100 4975 
Q 1975 4750 1750 3950 
L 2875 3950 
Q 2850 3525 2850 2875 
L 2850 675 
Q 2850 300 2875 -400 
L 2375 -400 
L 2375 100 
L 1100 100 
L 1100 -450 
L 600 -450 
Q 625 350 625 550 
L 625 2850 
Q 625 3525 600 3950 
L 1325 3950 
Q 1450 4525 1525 5150 
Q 1875 5025 2100 4975 
z
M 5825 3075 
Q 5800 1550 5700 175 
Q 5650 -275 5225 -425 
Q 4800 -575 4275 -625 
Q 4250 -350 4050 -50 
Q 4550 -75 4837 -25 
Q 5125 25 5200 200 
Q 5275 375 5312 1225 
Q 5350 2075 5375 3600 
L 3925 3600 
Q 3725 3125 3350 2475 
Q 3175 2650 2925 2750 
Q 3150 3050 3350 3475 
Q 3550 3900 3700 4400 
Q 3850 4900 3875 5175 
Q 4225 5025 4475 4950 
Q 4350 4750 4262 4512 
Q 4175 4275 4075 4050 
L 5850 4050 
Q 5825 3525 5825 3075 
z
M 2375 525 
L 2375 1850 
L 1100 1850 
L 1100 525 
L 2375 525 
z
M 2375 2275 
L 2375 3525 
L 1100 3525 
L 1100 2275 
L 2375 2275 
z
M 4350 2050 
Q 4500 1750 4700 1275 
Q 4525 1200 4225 1025 
Q 4050 1500 3900 1837 
Q 3750 2175 3600 2400 
Q 3775 2500 4050 2650 
L 4350 2050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5e73" d="M 4525 4825 
Q 4900 4825 5450 4850 
L 5450 4375 
Q 4925 4400 4525 4400 
L 3475 4400 
L 3475 2025 
L 5000 2025 
Q 5550 2025 6025 2050 
L 6025 1575 
Q 5550 1600 5000 1600 
L 3475 1600 
L 3475 450 
Q 3475 -100 3500 -600 
L 2925 -600 
Q 2950 -100 2950 450 
L 2950 1600 
L 1125 1600 
Q 775 1600 375 1575 
L 375 2050 
Q 775 2025 1175 2025 
L 2950 2025 
L 2950 4400 
L 1775 4400 
Q 1400 4400 875 4375 
L 875 4850 
Q 1400 4825 1750 4825 
L 4525 4825 
z
M 3925 2600 
Q 4500 3300 4900 4125 
Q 5150 3950 5400 3825 
Q 4775 2850 4350 2300 
Q 4150 2475 3925 2600 
z
M 1975 2350 
Q 1600 3100 1050 3725 
Q 1250 3875 1450 4050 
Q 2075 3300 2450 2725 
Q 2150 2500 1975 2350 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5747" d="M 5775 275 
Q 5775 -325 5362 -450 
Q 4950 -575 4425 -600 
Q 4375 -275 4250 25 
Q 4950 -25 5125 62 
Q 5300 150 5300 500 
L 5375 3700 
L 3550 3700 
Q 3425 3375 3262 3037 
Q 3100 2700 2925 2400 
Q 2675 2550 2475 2625 
Q 3150 3625 3500 5200 
Q 3800 5100 4075 5025 
Q 3975 4875 3875 4650 
Q 3775 4425 3675 4125 
L 5900 4125 
L 5775 275 
z
M 1250 3625 
L 1250 4375 
Q 1250 4775 1225 5100 
L 1775 5100 
Q 1750 4725 1750 4375 
L 1750 3625 
L 1900 3625 
Q 2200 3625 2525 3650 
L 2525 3150 
Q 2200 3175 1900 3175 
L 1750 3175 
L 1750 1100 
L 2500 1375 
Q 2475 1125 2525 900 
Q 1225 475 962 362 
Q 700 250 475 150 
Q 375 475 250 750 
Q 575 800 800 850 
Q 1025 900 1250 975 
L 1250 3175 
L 1125 3175 
Q 850 3175 400 3150 
L 400 3650 
Q 850 3625 1100 3625 
L 1250 3625 
z
M 4950 1625 
Q 4675 1425 4137 1050 
Q 3600 675 3175 300 
Q 3000 575 2850 775 
Q 3350 1050 3762 1325 
Q 4175 1600 4775 2075 
Q 4825 1850 4950 1625 
z
M 3775 3150 
Q 4125 2800 4475 2300 
Q 4250 2125 4100 1975 
Q 3900 2325 3450 2825 
Q 3600 3000 3775 3150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-48" d="M 2850 125 
L 2275 125 
L 2275 2125 
L 850 2125 
L 850 125 
L 275 125 
L 275 4400 
L 850 4400 
L 850 2600 
L 2275 2600 
L 2275 4400 
L 2850 4400 
L 2850 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-45" d="M 2900 125 
L 350 125 
L 350 4400 
L 2775 4400 
L 2775 3925 
L 925 3925 
L 925 2600 
L 2625 2600 
L 2625 2125 
L 925 2125 
L 925 600 
L 2900 600 
L 2900 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-50" d="M 2950 3100 
Q 2950 2500 2600 2150 
Q 2250 1800 1625 1800 
L 900 1800 
L 900 125 
L 325 125 
L 325 4400 
L 1625 4400 
Q 2250 4400 2600 4050 
Q 2950 3700 2950 3100 
z
M 2375 3100 
Q 2375 3575 2150 3750 
Q 1925 3925 1475 3925 
L 900 3925 
L 900 2275 
L 1475 2275 
Q 1925 2275 2150 2450 
Q 2375 2625 2375 3100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6ce2" d="M 5075 3275 
Q 5175 3600 5225 3850 
L 4325 3850 
L 4325 2750 
L 5550 2750 
Q 5400 2100 5125 1512 
Q 4850 925 4525 550 
Q 4775 350 5200 175 
Q 5625 0 6175 -100 
Q 5900 -300 5775 -600 
Q 5250 -425 4800 -175 
Q 4350 75 4200 225 
Q 3950 25 3537 -212 
Q 3125 -450 2500 -675 
Q 2375 -500 2050 -375 
Q 2525 -250 3062 25 
Q 3600 300 3850 550 
Q 3325 1300 3075 2325 
L 2825 2325 
Q 2800 1400 2487 725 
Q 2175 50 1875 -300 
Q 1700 -150 1375 -50 
Q 1750 325 1950 712 
Q 2150 1100 2262 1475 
Q 2375 1850 2387 2825 
Q 2400 3800 2350 4275 
L 3875 4275 
Q 3875 4850 3850 5200 
L 4350 5200 
Q 4325 4900 4325 4275 
L 5850 4275 
Q 5825 4125 5750 3837 
Q 5675 3550 5600 3200 
Q 5300 3250 5075 3275 
z
M 3525 2325 
Q 3800 1400 4200 925 
Q 4650 1475 4925 2325 
L 3525 2325 
z
M 1950 1700 
Q 1700 1375 1262 712 
Q 825 50 675 -200 
Q 475 50 225 275 
Q 525 525 937 1100 
Q 1350 1675 1550 2100 
L 1950 1700 
z
M 3875 2750 
L 3875 3850 
L 2850 3850 
L 2850 2750 
L 3875 2750 
z
M 925 4925 
Q 1600 4525 2000 4250 
Q 1800 4025 1675 3800 
Q 1250 4150 625 4500 
Q 775 4700 925 4925 
z
M 600 3525 
Q 1125 3250 1725 2850 
Q 1525 2675 1400 2425 
Q 925 2825 350 3100 
Q 450 3275 600 3525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5f62" d="M 2750 4850 
Q 3350 4850 3675 4875 
L 3675 4425 
Q 3400 4450 2950 4450 
L 2950 2700 
Q 3475 2700 3725 2725 
L 3725 2250 
Q 3500 2275 2950 2275 
L 2950 500 
Q 2950 -50 2975 -350 
L 2475 -350 
Q 2500 0 2500 475 
L 2500 2275 
L 1650 2275 
Q 1600 1075 1325 437 
Q 1050 -200 825 -500 
Q 550 -300 300 -250 
Q 700 150 937 762 
Q 1175 1375 1200 2275 
Q 775 2275 425 2250 
L 425 2725 
Q 775 2700 1225 2700 
L 1225 4450 
Q 800 4450 600 4425 
L 600 4875 
Q 800 4850 1375 4850 
L 2750 4850 
z
M 6100 1275 
Q 5850 1050 5150 462 
Q 4450 -125 3800 -525 
Q 3650 -300 3400 -100 
Q 4050 175 4725 750 
Q 5400 1325 5625 1650 
Q 5800 1450 6100 1275 
z
M 2500 2700 
L 2500 4450 
L 1675 4450 
L 1675 2700 
L 2500 2700 
z
M 5875 2975 
Q 5650 2825 5187 2300 
Q 4725 1775 3900 1175 
Q 3750 1375 3475 1550 
Q 4175 1950 4625 2412 
Q 5075 2875 5375 3325 
Q 5700 3050 5875 2975 
z
M 5675 4800 
Q 5425 4575 5062 4087 
Q 4700 3600 4075 2975 
Q 3850 3150 3625 3275 
Q 4175 3750 4475 4125 
Q 4775 4500 5125 5100 
Q 5250 5025 5675 4800 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-74"/>
     <use xlink:href="#SimHei-65" x="50"/>
     <use xlink:href="#SimHei-73" x="100"/>
     <use xlink:href="#SimHei-74" x="150"/>
     <use xlink:href="#SimHei-31" x="200"/>
     <use xlink:href="#SimHei-9636" x="250"/>
     <use xlink:href="#SimHei-6bb5" x="350"/>
     <use xlink:href="#SimHei-663e" x="450"/>
     <use xlink:href="#SimHei-8457" x="550"/>
     <use xlink:href="#SimHei-5bfc" x="650"/>
     <use xlink:href="#SimHei-8054" x="750"/>
     <use xlink:href="#SimHei-7684" x="850"/>
     <use xlink:href="#SimHei-5e73" x="950"/>
     <use xlink:href="#SimHei-5747" x="1050"/>
     <use xlink:href="#SimHei-48" x="1150"/>
     <use xlink:href="#SimHei-45" x="1200"/>
     <use xlink:href="#SimHei-50" x="1250"/>
     <use xlink:href="#SimHei-6ce2" x="1300"/>
     <use xlink:href="#SimHei-5f62" x="1400"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_8">
     <path d="M 630 42.89125 
L 703.6 42.89125 
Q 705.2 42.89125 705.2 41.29125 
L 705.2 30.65375 
Q 705.2 29.05375 703.6 29.05375 
L 630 29.05375 
Q 628.4 29.05375 628.4 30.65375 
L 628.4 41.29125 
Q 628.4 42.89125 630 42.89125 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_67">
     <path d="M 631.6 35.89125 
L 639.6 35.89125 
L 647.6 35.89125 
" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_16">
     <!-- 所有被试平均 -->
     <g transform="translate(654 38.69125) scale(0.08 -0.08)">
      <defs>
       <path id="SimHei-6240" d="M 2925 4450 
Q 2650 4425 1375 4250 
L 1375 3375 
L 2725 3375 
Q 2700 2800 2700 2225 
Q 2700 1675 2725 1325 
L 1350 1325 
Q 1325 875 1175 425 
Q 1025 -25 775 -525 
Q 575 -325 325 -275 
Q 725 325 812 1050 
Q 900 1775 900 2675 
Q 900 3575 875 4575 
Q 2225 4725 2650 4950 
Q 2750 4725 2925 4450 
z
M 5825 4425 
Q 5475 4400 5000 4337 
Q 4525 4275 3800 4225 
L 3800 3025 
L 5225 3025 
Q 5575 3025 5975 3050 
L 5975 2600 
Q 5575 2625 5200 2625 
L 5200 400 
Q 5200 -100 5225 -500 
L 4700 -500 
Q 4725 -50 4725 400 
L 4725 2625 
L 3800 2625 
Q 3750 1625 3600 1062 
Q 3450 500 3250 125 
Q 3050 -250 2725 -625 
Q 2550 -425 2300 -325 
Q 2725 75 2975 612 
Q 3225 1150 3287 1812 
Q 3350 2475 3350 3125 
Q 3350 3800 3325 4600 
Q 3700 4600 4425 4687 
Q 5150 4775 5575 4950 
Q 5700 4675 5825 4425 
z
M 2275 1750 
L 2275 2975 
L 1375 2975 
L 1375 1750 
L 2275 1750 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6709" d="M 275 1625 
Q 850 1975 1400 2537 
Q 1950 3100 2400 3925 
L 1500 3925 
Q 1150 3925 625 3900 
L 625 4375 
Q 1275 4350 1600 4350 
L 2550 4350 
Q 2750 4950 2775 5225 
Q 3150 5125 3375 5075 
Q 3300 5000 3125 4350 
L 5150 4350 
Q 5575 4350 6025 4375 
L 6025 3900 
Q 5575 3925 5225 3925 
L 2975 3925 
Q 2800 3600 2600 3275 
L 5475 3275 
Q 5450 2650 5450 2150 
L 5450 125 
Q 5450 -225 5250 -387 
Q 5050 -550 4400 -600 
Q 4375 -325 4225 0 
Q 4600 -50 4775 -25 
Q 4950 0 4950 250 
L 4950 750 
L 2350 750 
L 2350 -575 
L 1800 -575 
Q 1825 50 1825 500 
L 1825 2275 
Q 1300 1675 725 1225 
Q 625 1400 275 1625 
z
M 4950 2175 
L 4950 2875 
L 2350 2875 
L 2350 2175 
L 4950 2175 
z
M 4950 1150 
L 4950 1775 
L 2350 1775 
L 2350 1150 
L 4950 1150 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-88ab" d="M 4100 4175 
Q 4100 4800 4075 5175 
L 4575 5175 
Q 4550 4825 4550 4175 
L 6025 4175 
Q 5900 3825 5725 3225 
Q 5550 3300 5225 3325 
Q 5375 3675 5400 3800 
L 4550 3800 
L 4550 2650 
L 5575 2650 
Q 5425 2075 5225 1575 
Q 5025 1075 4675 550 
Q 4925 325 5375 150 
Q 5825 -25 6125 -50 
Q 5850 -300 5775 -550 
Q 5325 -375 5000 -187 
Q 4675 0 4400 250 
Q 3975 -200 3225 -625 
Q 3050 -350 2850 -250 
Q 3575 0 4100 575 
Q 3675 1125 3400 2275 
L 3275 2275 
Q 3250 1575 3087 925 
Q 2925 275 2350 -550 
Q 2175 -400 1875 -300 
Q 2375 225 2600 825 
Q 2825 1425 2850 2150 
L 2850 3375 
Q 2850 3900 2825 4175 
L 4100 4175 
z
M 1825 2325 
Q 2075 2600 2300 2950 
Q 2500 2725 2650 2600 
Q 2425 2400 2075 2050 
L 2550 1575 
Q 2400 1450 2200 1225 
Q 1975 1575 1575 1975 
L 1575 525 
Q 1575 0 1600 -625 
L 1125 -625 
Q 1150 175 1150 700 
L 1150 2075 
Q 900 1800 525 1475 
Q 400 1675 200 1850 
Q 675 2175 1037 2600 
Q 1400 3025 1650 3525 
L 1150 3525 
Q 800 3525 400 3500 
L 400 3925 
Q 950 3900 1300 3900 
L 2350 3900 
Q 1950 3075 1550 2550 
Q 1650 2475 1825 2325 
z
M 4100 2650 
L 4100 3800 
L 3275 3800 
L 3275 2650 
L 4100 2650 
z
M 3825 2275 
Q 4050 1375 4400 900 
Q 4600 1200 4737 1512 
Q 4875 1825 5000 2275 
L 3825 2275 
z
M 1500 5250 
Q 1750 4800 1950 4375 
L 1500 4150 
Q 1325 4650 1075 5025 
Q 1300 5100 1500 5250 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-8bd5" d="M 3950 3825 
Q 3925 4325 3925 4550 
Q 3925 4800 3900 5225 
Q 4200 5150 4450 5125 
Q 4425 4825 4425 4600 
L 4425 3825 
L 5150 3825 
Q 5500 3825 5875 3850 
L 5875 3400 
Q 5525 3425 5125 3425 
L 4450 3425 
Q 4500 2750 4587 2212 
Q 4675 1675 4837 1112 
Q 5000 550 5212 225 
Q 5425 -100 5500 200 
Q 5575 500 5600 900 
Q 5800 625 6100 525 
Q 5975 -275 5750 -487 
Q 5525 -700 5250 -537 
Q 4975 -375 4737 62 
Q 4500 500 4337 1075 
Q 4175 1650 4100 2262 
Q 4025 2875 3975 3425 
L 2800 3425 
Q 2300 3425 1875 3400 
L 1875 3850 
Q 2275 3825 2775 3825 
L 3950 3825 
z
M 1500 3175 
Q 1475 2675 1475 2175 
L 1475 625 
Q 1825 950 2125 1225 
Q 2225 1000 2375 800 
Q 1900 425 1125 -350 
Q 1000 -125 825 50 
Q 1025 175 1025 525 
L 1025 2725 
Q 625 2725 300 2700 
L 300 3200 
Q 625 3175 825 3175 
L 1500 3175 
z
M 3775 2175 
Q 3500 2200 3250 2200 
L 3250 575 
Q 3575 675 3900 800 
Q 3900 525 3950 325 
Q 3575 250 3150 125 
Q 2725 0 2275 -175 
Q 2200 100 2025 300 
Q 2425 350 2800 475 
L 2800 2200 
Q 2525 2200 2225 2175 
L 2225 2650 
Q 2600 2625 3000 2625 
Q 3425 2625 3775 2650 
L 3775 2175 
z
M 925 4975 
Q 1275 4650 1700 4100 
Q 1525 3925 1325 3775 
Q 1175 4050 575 4650 
Q 775 4825 925 4975 
z
M 5150 5150 
Q 5325 5000 5850 4375 
Q 5575 4200 5425 4050 
Q 5125 4525 4775 4875 
Q 4925 4975 5150 5150 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-6240"/>
      <use xlink:href="#SimHei-6709" x="100"/>
      <use xlink:href="#SimHei-88ab" x="200"/>
      <use xlink:href="#SimHei-8bd5" x="300"/>
      <use xlink:href="#SimHei-5e73" x="400"/>
      <use xlink:href="#SimHei-5747" x="500"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p1126db6402">
   <rect x="51.24875" y="25.05375" width="657.95125" height="369.44125"/>
  </clipPath>
 </defs>
</svg>

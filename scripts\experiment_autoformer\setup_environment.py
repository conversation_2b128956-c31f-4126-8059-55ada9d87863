"""
设置Autoformer实验环境
"""
import os
import sys
import subprocess
import platform

def run_command(command):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # 实时打印输出
    for line in process.stdout:
        print(line.strip())
    
    # 等待命令完成
    process.wait()
    
    # 检查是否有错误
    if process.returncode != 0:
        print("命令执行失败，错误信息:")
        for line in process.stderr:
            print(line.strip())
        return False
    
    return True

def setup_environment():
    """设置环境"""
    print("开始设置Autoformer实验环境...")
    
    # 检测操作系统
    system = platform.system()
    print(f"操作系统: {system}")
    
    # 激活conda环境
    if system == "Windows":
        activate_cmd = "conda activate tdspy_data"
    else:
        activate_cmd = "source activate tdspy_data"
    
    # 安装依赖
    dependencies = [
        "pandas",
        "scikit-learn",
        "numpy",
        "matplotlib",
        "torch",
        "torchvision",
        "reformer_pytorch"
    ]
    
    for dep in dependencies:
        cmd = f"conda run -n tdspy_data pip install {dep}"
        if not run_command(cmd):
            print(f"安装 {dep} 失败")
            return False
    
    print("环境设置完成!")
    return True

if __name__ == "__main__":
    setup_environment()

"""
检查GPU是否可用并正确设置
"""
import torch
import os
import subprocess

def check_gpu():
    """检查GPU是否可用"""
    print("检查GPU是否可用...")
    
    # 检查CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print(f"CUDA是否可用: {cuda_available}")
    
    if cuda_available:
        # 获取GPU数量
        gpu_count = torch.cuda.device_count()
        print(f"可用GPU数量: {gpu_count}")
        
        # 获取当前GPU设备
        current_device = torch.cuda.current_device()
        print(f"当前GPU设备: {current_device}")
        
        # 获取GPU名称
        for i in range(gpu_count):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        
        # 获取GPU内存
        for i in range(gpu_count):
            try:
                print(f"GPU {i} 总内存: {torch.cuda.get_device_properties(i).total_memory / 1024 / 1024 / 1024:.2f} GB")
                print(f"GPU {i} 已用内存: {(torch.cuda.get_device_properties(i).total_memory - torch.cuda.memory_reserved(i)) / 1024 / 1024 / 1024:.2f} GB")
                print(f"GPU {i} 可用内存: {torch.cuda.memory_reserved(i) / 1024 / 1024 / 1024:.2f} GB")
            except Exception as e:
                print(f"无法获取GPU {i} 内存信息: {e}")
        
        # 测试GPU是否可用
        try:
            x = torch.rand(10, 10).cuda()
            y = torch.rand(10, 10).cuda()
            z = x + y
            print("GPU测试成功!")
        except Exception as e:
            print(f"GPU测试失败: {e}")
    else:
        print("CUDA不可用，将使用CPU进行计算")
    
    return cuda_available

def run_nvidia_smi():
    """运行nvidia-smi命令"""
    try:
        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        if result.returncode == 0:
            print("\nnvidia-smi输出:")
            print(result.stdout)
        else:
            print("\nnvidia-smi命令失败:")
            print(result.stderr)
    except Exception as e:
        print(f"\n无法运行nvidia-smi命令: {e}")

if __name__ == "__main__":
    cuda_available = check_gpu()
    
    if cuda_available:
        run_nvidia_smi()
    
    print("\nGPU检查完成!")

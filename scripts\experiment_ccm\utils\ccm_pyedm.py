#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用 pyEDM 库实现收敛交叉映射 (CCM)
"""

import numpy as np
import pandas as pd
from scipy.stats import pearsonr
import pyEDM

def ccm_correlation(x, y, embed_dim, tau, lib_sizes):
    """
    使用 pyEDM 库计算两个时间序列之间的收敛交叉映射 (CCM) 相关性。

    参数:
    x (np.ndarray): 第一个时间序列 (例如 EEG 包络)。
    y (np.ndarray): 第二个时间序列 (例如 心率序列)。
    embed_dim (int): 嵌入维度 (E)。
    tau (int): 时间延迟。
    lib_sizes (np.ndarray or list): 要评估的库大小列表。

    返回:
    tuple: (lib_sizes, rho_x_y, rho_y_x)
           - lib_sizes (np.ndarray): 使用的库大小。
           - rho_x_y (np.ndarray): X 对 Y 的预测技能 (X -> Y 的相关性)。
           - rho_y_x (np.ndarray): Y 对 X 的预测技能 (Y -> X 的相关性)。
    """
    # 确保输入是 numpy 数组
    x = np.asarray(x)
    y = np.asarray(y)
    lib_sizes = np.asarray(lib_sizes)

    # 确保数据是一维的
    if x.ndim != 1 or y.ndim != 1:
        raise ValueError("输入时间序列必须是一维的")

    # 检查数据长度
    if len(x) < 10 or len(y) < 10:
        raise ValueError(f"输入时间序列太短: x长度={len(x)}, y长度={len(y)}。需要至少10个数据点。")

    # 确保数据长度相同
    if len(x) != len(y):
        raise ValueError(f"输入时间序列长度不同: x长度={len(x)}, y长度={len(y)}")

    # 检查是否有 NaN 或 Inf
    if np.any(np.isnan(x)) or np.any(np.isinf(x)) or np.any(np.isnan(y)) or np.any(np.isinf(y)):
        raise ValueError("输入数据包含 NaN 或 Inf，无法进行CCM计算")

    # 检查数据是否有足够的变化
    if np.std(x) < 1e-10 or np.std(y) < 1e-10:
        raise ValueError(f"输入数据几乎没有变化，无法进行CCM计算: x标准差={np.std(x)}, y标准差={np.std(y)}")

    # 创建时间索引
    time_index = np.arange(len(x))

    # 创建DataFrame用于pyEDM
    df = pd.DataFrame({
        'time': time_index,
        'x': x,
        'y': y
    })

    # 初始化结果数组
    rho_x_y_array = np.zeros(len(lib_sizes))
    rho_y_x_array = np.zeros(len(lib_sizes))

    # 对每个库大小进行计算
    for i, lib_size in enumerate(lib_sizes):
        # 确保库大小不超过数据长度
        if lib_size > len(x) - (embed_dim-1)*tau:
            raise ValueError(f"库大小 {lib_size} 超过有效数据长度 {len(x) - (embed_dim-1)*tau}")

        # 使用pyEDM进行CCM计算 - X预测Y (X->Y)和Y预测X (Y->X)
        # 根据pyEDM-2.2.1版本的API调整参数
        # 注意：pyEDM的CCM函数会同时计算两个方向的因果关系
        ccm_result = pyEDM.CCM(
            dataFrame=df,
            E=embed_dim,
            Tp=0,  # 预测当前时间点
            knn=0,  # 使用默认的k近邻设置
            tau=tau,
            columns="x y",  # 指定列名，使用空格分隔
            target="y",  # 目标是y
            libSizes=f"{lib_size}",  # 使用固定的库大小
            sample=100  # 使用100个随机样本
        )

        # 提取相关系数
        if 'x:y' in ccm_result.columns:
            rho_x_y_array[i] = ccm_result['x:y'].iloc[0]
        else:
            rho_x_y_array[i] = np.nan

        if 'y:x' in ccm_result.columns:
            rho_y_x_array[i] = ccm_result['y:x'].iloc[0]
        else:
            rho_y_x_array[i] = np.nan

    return lib_sizes, rho_x_y_array, rho_y_x_array

# 测试用例
if __name__ == '__main__':
    import os
    import matplotlib.pyplot as plt
    print("运行 pyEDM CCM 接口测试...")

    # 创建耦合的 logistic map 数据
    def logistic_map(r, x):
        return r * x * (1 - x)

    N = 500
    ts = np.arange(N)
    r1, r2 = 3.8, 3.5
    beta_xy, beta_yx = 0.1, 0.02  # Y 对 X 的影响更大
    x = np.zeros(N)
    y = np.zeros(N)
    x[0], y[0] = 0.4, 0.6

    for i in range(N - 1):
        x[i+1] = logistic_map(r1, x[i]) * (1 - beta_yx) + logistic_map(r2, y[i]) * beta_yx
        y[i+1] = logistic_map(r2, y[i]) * (1 - beta_xy) + logistic_map(r1, x[i]) * beta_xy
        # 添加少量噪声
        x[i+1] += np.random.randn() * 0.001
        y[i+1] += np.random.randn() * 0.001
        x[i+1] = np.clip(x[i+1], 0, 1)
        y[i+1] = np.clip(y[i+1], 0, 1)

    # 设置参数
    embed_dim = 3
    tau = 2
    lib_sizes_test = np.linspace(50, N - (embed_dim - 1) * tau - 10, 10, dtype=int)

    print(f"测试参数: E={embed_dim}, tau={tau}, LibSizes={lib_sizes_test}")

    libs, rho_xy, rho_yx = ccm_correlation(x, y, embed_dim, tau, lib_sizes_test)

    print("\nCCM 计算结果:")
    print(f"库大小: {libs}")
    print(f"X -> Y (rho_x_y): {rho_xy}")
    print(f"Y -> X (rho_y_x): {rho_yx}")

    # 简单的可视化
    plt.figure(figsize=(8, 5))
    plt.plot(libs, rho_xy, 'o-', label='X -> Y')
    plt.plot(libs, rho_yx, 's-', label='Y -> X')
    plt.xlabel('Library Size')
    plt.ylabel('Correlation (rho)')
    plt.title('CCM Test Results (Logistic Map) - pyEDM Implementation')
    plt.legend()
    plt.grid(True)

    # 保存图形到 result/nonlinear_interaction 目录（符合项目规范）
    output_fig_dir = os.path.join("D:/ecgeeg/30-数据分析/5-NeuroKit2/result", 'nonlinear_interaction')
    os.makedirs(output_fig_dir, exist_ok=True)
    fig_path = os.path.join(output_fig_dir, 'ccm_pyedm_test_plot.png')
    plt.savefig(fig_path)
    print(f"\n测试图已保存至: {fig_path}")

    plt.show()

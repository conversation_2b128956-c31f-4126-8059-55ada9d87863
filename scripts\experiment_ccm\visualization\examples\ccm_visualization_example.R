# ============================================================================
# CCM分析结果可视化示例脚本
# 作者: Augment Agent
# 日期: 2025-05-03
# 描述: 此脚本展示如何读取CCM分析结果并创建高质量的科研图表
# ============================================================================

# 加载必要的包
if (!require(tidyverse)) install.packages("tidyverse")
if (!require(ggplot2)) install.packages("ggplot2")
if (!require(viridis)) install.packages("viridis")
if (!require(patchwork)) install.packages("patchwork")
if (!require(ggsci)) install.packages("ggsci")
if (!require(scales)) install.packages("scales")
if (!require(RColorBrewer)) install.packages("RColorBrewer")
if (!require(ggrepel)) install.packages("ggrepel")

library(tidyverse)
library(ggplot2)
library(viridis)
library(patchwork)
library(ggsci)
library(scales)
library(RColorBrewer)
library(ggrepel)

# 设置主题 - 使用IEEE论文风格
theme_ieee <- function(base_size = 12, base_family = "Arial") {
  theme_minimal(base_size = base_size, base_family = base_family) +
    theme(
      # 文本元素
      plot.title = element_text(size = base_size * 1.2, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = base_size, color = "gray30", hjust = 0.5),
      axis.title = element_text(size = base_size, face = "bold"),
      axis.text = element_text(size = base_size * 0.9),
      legend.title = element_text(size = base_size, face = "bold"),
      legend.text = element_text(size = base_size * 0.9),
      
      # 网格线和背景
      panel.grid.major = element_line(color = "gray90", size = 0.3),
      panel.grid.minor = element_blank(),
      panel.border = element_rect(fill = NA, color = "gray70", size = 0.5),
      
      # 图例位置
      legend.position = "bottom",
      legend.box = "horizontal",
      
      # 其他
      plot.margin = margin(10, 10, 10, 10)
    )
}

# 设置主题
theme_set(theme_ieee())

# ============================================================================
# 第1部分: 创建模拟数据（如果没有真实数据可用）
# ============================================================================

# 创建模拟CCM结果数据
create_mock_ccm_data <- function(n_subjects = 29, n_eeg_channels = 10, n_ecg_channels = 5) {
  # 定义频段
  bands <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  
  # 定义阶段
  stages <- c("prac", "test1", "rest1")
  
  # 创建所有组合
  combinations <- expand.grid(
    subject_id = sprintf("%02d_%02d", rep(1:n_subjects, each = 1), 1),
    stage = stages,
    eeg_channel = paste0("EEG", 1:n_eeg_channels),
    ecg_channel = paste0("ECG", 1:n_ecg_channels),
    band = bands,
    stringsAsFactors = FALSE
  )
  
  # 添加CCM结果
  set.seed(42)  # 设置随机种子以确保可重复性
  
  # 为不同频段设置不同的基线值
  band_effects <- c(
    delta = 0.3,
    theta = 0.25,
    alpha = 0.4,
    beta = 0.35,
    gamma = 0.2,
    high_gamma = 0.15
  )
  
  # 为不同阶段设置不同的效应
  stage_effects <- c(
    prac = 0.9,
    test1 = 1.1,
    rest1 = 1.0
  )
  
  # 生成CCM结果
  n_rows <- nrow(combinations)
  
  # 基础噪声
  noise_eeg_to_heart <- rnorm(n_rows, 0, 0.05)
  noise_heart_to_eeg <- rnorm(n_rows, 0, 0.05)
  
  # 为每个被试添加随机效应
  subject_effects <- rnorm(n_subjects, 1, 0.1)
  subject_idx <- as.numeric(gsub("^(\\d+)_.*$", "\\1", combinations$subject_id))
  
  # 计算CCM值
  combinations$eeg_to_heart <- pmax(0, pmin(1, 
    0.2 + 
    band_effects[combinations$band] * 
    stage_effects[combinations$stage] * 
    subject_effects[subject_idx] + 
    noise_eeg_to_heart
  ))
  
  combinations$heart_to_eeg <- pmax(0, pmin(1,
    0.3 + 
    band_effects[combinations$band] * 1.2 *  # 心脏到大脑的影响略强
    stage_effects[combinations$stage] * 
    subject_effects[subject_idx] + 
    noise_heart_to_eeg
  ))
  
  # 计算方向性指数
  combinations$directionality <- (combinations$heart_to_eeg - combinations$eeg_to_heart) / 
                                (combinations$heart_to_eeg + combinations$eeg_to_heart + 1e-10)
  
  return(combinations)
}

# 创建或加载数据
# 如果有真实数据，取消注释下面的代码并提供正确的文件路径
# ccm_data <- read_csv("D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction/ccm_results_all_subjects.csv")

# 如果没有真实数据，使用模拟数据
ccm_data <- create_mock_ccm_data(n_subjects = 29, n_eeg_channels = 10, n_ecg_channels = 5)

# ============================================================================
# 第2部分: 数据预处理和汇总
# ============================================================================

# 计算每个频段的平均值和标准误
band_summary <- ccm_data %>%
  group_by(band) %>%
  summarize(
    mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
    se_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE) / sqrt(n()),
    mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
    se_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE) / sqrt(n()),
    mean_directionality = mean(directionality, na.rm = TRUE),
    se_directionality = sd(directionality, na.rm = TRUE) / sqrt(n()),
    .groups = "drop"
  )

# 设置频段顺序
band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
band_summary$band <- factor(band_summary$band, levels = band_levels)
ccm_data$band <- factor(ccm_data$band, levels = band_levels)

# 计算每个EEG通道的平均值
eeg_summary <- ccm_data %>%
  group_by(eeg_channel, band) %>%
  summarize(
    mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
    mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
    mean_directionality = mean(directionality, na.rm = TRUE),
    .groups = "drop"
  )

# 计算每个阶段的平均值
stage_summary <- ccm_data %>%
  group_by(stage, band) %>%
  summarize(
    mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
    se_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE) / sqrt(n()),
    mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
    se_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE) / sqrt(n()),
    .groups = "drop"
  )

# 设置阶段顺序和标签
stage_labels <- c(prac = "练习", test1 = "测试", rest1 = "休息")
stage_summary$stage <- factor(stage_summary$stage, levels = c("prac", "test1", "rest1"))

# ============================================================================
# 第3部分: 创建可视化
# ============================================================================

# 图1: 不同频段的因果强度比较
plot_frequency_bands <- function(data) {
  # 准备绘图数据
  plot_data <- data %>%
    select(band, mean_eeg_to_heart, se_eeg_to_heart, mean_heart_to_eeg, se_heart_to_eeg) %>%
    pivot_longer(
      cols = c(mean_eeg_to_heart, mean_heart_to_eeg),
      names_to = "direction",
      values_to = "causality"
    ) %>%
    mutate(
      se = ifelse(direction == "mean_eeg_to_heart", se_eeg_to_heart, se_heart_to_eeg),
      direction = case_when(
        direction == "mean_eeg_to_heart" ~ "脑 → 心",
        direction == "mean_heart_to_eeg" ~ "心 → 脑",
        TRUE ~ direction
      )
    )
  
  # 设置频段标签
  band_labels <- c(
    delta = "Delta (1-4 Hz)",
    theta = "Theta (4-8 Hz)",
    alpha = "Alpha (8-13 Hz)",
    beta = "Beta (13-30 Hz)",
    gamma = "Gamma (30-45 Hz)",
    high_gamma = "High Gamma (45-100 Hz)"
  )
  
  # 绘制条形图
  p <- ggplot(plot_data, aes(x = band, y = causality, fill = direction)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    geom_errorbar(
      aes(ymin = causality - se, ymax = causality + se),
      position = position_dodge(width = 0.8),
      width = 0.25
    ) +
    scale_x_discrete(labels = band_labels) +
    scale_fill_nejm() +  # New England Journal of Medicine配色
    labs(
      title = "不同频段的因果强度比较",
      subtitle = "基于CCM分析的脑-心交互",
      x = "频段",
      y = "因果强度",
      fill = "方向"
    ) +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "bottom"
    )
  
  return(p)
}

# 图2: 方向性指数比较
plot_directionality <- function(data) {
  # 准备绘图数据
  plot_data <- data %>%
    select(band, mean_directionality, se_directionality)
  
  # 设置频段标签
  band_labels <- c(
    delta = "Delta\n(1-4 Hz)",
    theta = "Theta\n(4-8 Hz)",
    alpha = "Alpha\n(8-13 Hz)",
    beta = "Beta\n(13-30 Hz)",
    gamma = "Gamma\n(30-45 Hz)",
    high_gamma = "High Gamma\n(45-100 Hz)"
  )
  
  # 绘制条形图
  p <- ggplot(plot_data, aes(x = band, y = mean_directionality, fill = mean_directionality)) +
    geom_bar(stat = "identity", width = 0.7) +
    geom_errorbar(
      aes(ymin = mean_directionality - se_directionality, 
          ymax = mean_directionality + se_directionality),
      width = 0.25
    ) +
    geom_hline(yintercept = 0, linetype = "dashed", color = "gray50") +
    scale_x_discrete(labels = band_labels) +
    scale_fill_gradient2(
      low = "#2166AC", 
      mid = "white", 
      high = "#B2182B", 
      midpoint = 0,
      guide = "none"
    ) +
    labs(
      title = "脑-心交互的方向性指数",
      subtitle = "正值表示心→脑方向更强，负值表示脑→心方向更强",
      x = "频段",
      y = "方向性指数",
      caption = "方向性指数 = (心→脑 - 脑→心)/(心→脑 + 脑→心)"
    ) +
    theme(
      axis.text.x = element_text(angle = 0, hjust = 0.5),
      panel.grid.major.x = element_blank()
    ) +
    ylim(-0.5, 0.5)  # 设置y轴范围，使图形更加平衡
  
  return(p)
}

# 图3: 不同阶段的因果强度比较
plot_stages <- function(data) {
  # 准备绘图数据
  plot_data <- data %>%
    pivot_longer(
      cols = c(mean_eeg_to_heart, mean_heart_to_eeg),
      names_to = "direction",
      values_to = "causality"
    ) %>%
    mutate(
      se = ifelse(direction == "mean_eeg_to_heart", se_eeg_to_heart, se_heart_to_eeg),
      direction = case_when(
        direction == "mean_eeg_to_heart" ~ "脑 → 心",
        direction == "mean_heart_to_eeg" ~ "心 → 脑",
        TRUE ~ direction
      )
    )
  
  # 绘制条形图
  p <- ggplot(plot_data, aes(x = stage, y = causality, fill = direction)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    geom_errorbar(
      aes(ymin = causality - se, ymax = causality + se),
      position = position_dodge(width = 0.8),
      width = 0.25
    ) +
    scale_x_discrete(labels = stage_labels) +
    scale_fill_jama() +  # Journal of the American Medical Association配色
    labs(
      title = "不同任务阶段的因果强度比较",
      x = "任务阶段",
      y = "因果强度",
      fill = "方向"
    ) +
    theme(legend.position = "bottom") +
    facet_wrap(~ band, nrow = 2)
  
  return(p)
}

# 图4: EEG通道的方向性热图
plot_eeg_heatmap <- function(data, selected_band = "alpha") {
  # 筛选指定频段的数据
  heatmap_data <- data %>%
    filter(band == selected_band) %>%
    select(eeg_channel, mean_directionality)
  
  # 提取通道编号并排序
  heatmap_data$channel_num <- as.numeric(gsub("EEG", "", heatmap_data$eeg_channel))
  heatmap_data <- heatmap_data %>%
    arrange(channel_num) %>%
    mutate(eeg_channel = factor(eeg_channel, levels = unique(eeg_channel)))
  
  # 绘制热图
  p <- ggplot(heatmap_data, aes(x = 1, y = eeg_channel, fill = mean_directionality)) +
    geom_tile() +
    scale_fill_gradient2(
      low = "#2166AC", 
      mid = "white", 
      high = "#B2182B", 
      midpoint = 0,
      name = "方向性指数"
    ) +
    labs(
      title = paste(selected_band, "频段的EEG通道方向性"),
      subtitle = "正值表示心→脑方向更强，负值表示脑→心方向更强",
      y = "EEG通道",
      x = NULL
    ) +
    theme(
      axis.text.x = element_blank(),
      axis.ticks.x = element_blank(),
      panel.grid = element_blank()
    )
  
  return(p)
}

# ============================================================================
# 第4部分: 生成和保存图表
# ============================================================================

# 创建图表
p1 <- plot_frequency_bands(band_summary)
p2 <- plot_directionality(band_summary)
p3 <- plot_stages(stage_summary)
p4 <- plot_eeg_heatmap(eeg_summary, "alpha")

# 组合图表
combined_plot <- (p1 + p2) / (p4 + plot_spacer())
combined_plot <- combined_plot + plot_annotation(
  title = "脑-心交互的因果分析",
  subtitle = "基于收敛交叉映射(CCM)方法",
  caption = "数据来源: 29名被试的EEG和ECG记录",
  theme = theme(plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
                plot.subtitle = element_text(size = 14, hjust = 0.5))
)

# 保存图表
ggsave("ccm_analysis_summary.png", combined_plot, width = 12, height = 10, dpi = 300)
ggsave("ccm_analysis_summary.pdf", combined_plot, width = 12, height = 10)

# 单独保存阶段比较图（因为它比较大）
ggsave("ccm_stages_comparison.png", p3, width = 12, height = 8, dpi = 300)
ggsave("ccm_stages_comparison.pdf", p3, width = 12, height = 8)

# 打印完成消息
cat("绘图完成！图表已保存为:\n")
cat("1. ccm_analysis_summary.png/pdf\n")
cat("2. ccm_stages_comparison.png/pdf\n")

# 显示组合图表
print(combined_plot)

# ============================================================================
# 第5部分: 统计分析
# ============================================================================

# 执行简单的统计分析
cat("\n\n统计分析结果:\n")
cat("====================\n")

# 比较不同频段的方向性
cat("不同频段的方向性指数比较:\n")
band_stats <- band_summary %>%
  select(band, mean_directionality) %>%
  arrange(desc(mean_directionality))

print(band_stats)

# 比较心→脑和脑→心方向的强度
cat("\n心→脑和脑→心方向的强度比较:\n")
direction_stats <- band_summary %>%
  group_by(band) %>%
  summarize(
    heart_to_brain = mean_heart_to_eeg,
    brain_to_heart = mean_eeg_to_heart,
    difference = mean_heart_to_eeg - mean_eeg_to_heart,
    percent_diff = (mean_heart_to_eeg - mean_eeg_to_heart) / mean_eeg_to_heart * 100,
    .groups = "drop"
  ) %>%
  arrange(desc(difference))

print(direction_stats)

# 保存统计结果
write_csv(band_summary, "ccm_band_summary.csv")
write_csv(eeg_summary, "ccm_eeg_channel_summary.csv")
write_csv(stage_summary, "ccm_stage_summary.csv")

cat("\n统计结果已保存为CSV文件\n")

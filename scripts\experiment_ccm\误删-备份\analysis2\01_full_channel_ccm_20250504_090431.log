#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全通道CCM分析脚本

此脚本用于运行完整的63个EEG通道和58个ECG通道之间的因果关系分析。
分析所有通道组合的因果关系，以获取更全面的空间信息。

EEG通道使用10-20坐标系，共63个通道：
['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8',
'Fz', 'Cz', 'Pz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'TP9',
'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz',
'CPz', 'POz', 'Oz']

ECG通道共58个：
['ECG1', 'ECG2', ..., 'ECG58']

主要特点：
1. 支持批处理被试、通道和频段
2. 支持断点续传，可以在中断后从上次停止的地方继续
3. 优化内存使用，避免内存溢出
4. 详细的进度报告和估计完成时间
5. 自动根据系统配置调整参数
6. 不使用try-except块，任何错误都会立即停止程序
7. 不使用警告语句，任何异常情况都视为错误

作者：AI助手
日期：2024年
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import multiprocessing as mp
from tqdm import tqdm
import warnings
import logging
import json
import shutil
import pyarrow as pa
import pyarrow.parquet as pq
import gc

# 定义EEG通道和频段
ALL_EEG_CHANNELS = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8',
                    'Fz', 'Cz', 'Pz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'TP9',
                    'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
                    'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz',
                    'CPz', 'POz', 'Oz']  # 共63个EEG通道，使用10-20坐标系

# 定义ECG通道
ALL_ECG_CHANNELS = ['ECG1', 'ECG2', 'ECG3', 'ECG4', 'ECG5', 'ECG6', 'ECG7', 'ECG8', 'ECG9', 'ECG10',
                    'ECG11', 'ECG12', 'ECG13', 'ECG14', 'ECG15', 'ECG16', 'ECG17', 'ECG18', 'ECG19', 'ECG20',
                    'ECG21', 'ECG22', 'ECG23', 'ECG24', 'ECG25', 'ECG26', 'ECG27', 'ECG28', 'ECG29', 'ECG30',
                    'ECG31', 'ECG32', 'ECG33', 'ECG34', 'ECG35', 'ECG36', 'ECG37', 'ECG38', 'ECG39', 'ECG40',
                    'ECG41', 'ECG42', 'ECG43', 'ECG44', 'ECG45', 'ECG46', 'ECG47', 'ECG48', 'ECG49', 'ECG50',
                    'ECG51', 'ECG52', 'ECG53', 'ECG54', 'ECG55', 'ECG56', 'ECG57', 'ECG58']  # 共58个ECG通道

FREQUENCY_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8),
    'alpha': (8, 13),
    'beta': (13, 30),
    'gamma': (30, 45),
    'high_gamma': (45, 100)
}

# 设置路径
BASE_DIR = "D:/ecgeeg/30-数据分析/5-NeuroKit2"
DATA_DIR = "D:/ecgeeg/19-eegecg手动预处理6-ICA3"
RESULTS_DIR = os.path.join(BASE_DIR, "result/ccm_analysis")
LOG_DIR = os.path.join(BASE_DIR, "logs")

# 确保目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# 设置日志
log_file = os.path.join(LOG_DIR, f"full_channel_ccm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("Full_Channel_CCM")

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message="invalid value encountered in true_divide")
warnings.filterwarnings("ignore", category=RuntimeWarning, message="Mean of empty slice")

# CCM参数
CCM_EMBED_DIM = 3  # 嵌入维度
CCM_TAU = 1  # 时间延迟
CCM_LIBRARY_SIZES = [10, 20, 50, 100, 200, 300, 400, 500]  # 库大小

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='全通道CCM分析')
    parser.add_argument('--subjects', type=str, default='all', help='要分析的被试ID，用逗号分隔，或"all"表示所有被试')
    parser.add_argument('--stages', type=str, default='all', help='要分析的实验阶段，用逗号分隔，或"all"表示所有阶段')
    parser.add_argument('--eeg-channels', type=str, default='all', help='要分析的EEG通道，用逗号分隔，或"all"表示所有通道')
    parser.add_argument('--ecg-channels', type=str, default='all', help='要分析的ECG通道，用逗号分隔，或"all"表示所有通道')
    parser.add_argument('--bands', type=str, default='all', help='要分析的频段，用逗号分隔，或"all"表示所有频段')
    parser.add_argument('--force-recompute', action='store_true', help='强制重新计算所有结果')
    parser.add_argument('--max-cores', type=int, default=None, help='最大使用的CPU核心数')
    parser.add_argument('--batch-size', type=int, default=5, help='每批处理的被试数量')
    parser.add_argument('--pause-time', type=float, default=1.0, help='批次之间的暂停时间（秒）')
    parser.add_argument('--max-memory', type=int, default=None, help='最大内存使用量（MB）')

    return parser.parse_args()

def get_subject_ids():
    """获取所有被试ID"""
    subject_ids = []
    for filename in os.listdir(DATA_DIR):
        if filename.endswith('.fif') and 'raw' in filename:
            parts = filename.split('_')
            if len(parts) >= 2:
                subject_id = parts[0]
                if subject_id not in subject_ids and subject_id.isdigit():
                    subject_ids.append(subject_id)

    return sorted(subject_ids)

def get_subject_files(subject_id):
    """获取指定被试的所有文件"""
    files = {}
    for filename in os.listdir(DATA_DIR):
        if filename.startswith(f"{subject_id}_") and filename.endswith('.fif'):
            for stage in ['prac', 'test1', 'rest1', 'test2', 'rest2', 'test3', 'rest3']:
                if stage in filename:
                    files[stage] = os.path.join(DATA_DIR, filename)
                    break

    return files

def load_data(file_path):
    """加载EEG和ECG数据"""
    try:
        import mne
        raw = mne.io.read_raw_fif(file_path, preload=True)
        return raw
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None

def extract_frequency_bands(eeg_data, sfreq, bands=None):
    """提取EEG频段"""
    if bands is None:
        bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 45)
        }

    import mne
    band_data = {}
    for band_name, (low_freq, high_freq) in bands.items():
        # 使用MNE的滤波函数
        filtered_data = mne.filter.filter_data(
            eeg_data.copy(), sfreq, low_freq, high_freq,
            method='fir', phase='zero-double'
        )
        band_data[band_name] = filtered_data

    return band_data

def compute_ccm(x, y, embed_dim=CCM_EMBED_DIM, tau=CCM_TAU, lib_sizes=CCM_LIBRARY_SIZES):
    """
    计算收敛交叉映射（CCM）

    参数:
    - x, y: 输入时间序列
    - embed_dim: 嵌入维度
    - tau: 时间延迟
    - lib_sizes: 库大小列表

    返回:
    - x_to_y_skill: x预测y的技能
    - y_to_x_skill: y预测x的技能
    """
    try:
        # 导入utils目录下的ccm_pyedm模块
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'utils'))
        from ccm_pyedm import compute_ccm as pyedm_compute_ccm

        # 调用ccm_pyedm模块中的compute_ccm函数
        _, x_to_y_skill, y_to
import os
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class Dataset_Custom(Dataset):
    """
    自定义数据集类，用于加载CSV格式的时间序列数据
    """
    def __init__(self, root_path, data_path, flag='train', size=None, 
                 features='S', target='OT', freq='h'):
        # 初始化参数
        self.root_path = root_path
        self.data_path = data_path
        self.flag = flag
        self.features = features
        self.target = target
        self.freq = freq
        
        # 设置序列长度
        self.seq_len = size[0]
        self.label_len = size[1]
        self.pred_len = size[2]
        
        # 读取数据
        self.__read_data__()
        
    def __read_data__(self):
        # 读取CSV文件
        df_raw = pd.read_csv(os.path.join(self.root_path, self.data_path))
        
        # 设置时间索引
        if 'date' in df_raw.columns:
            df_raw = df_raw.set_index('date')
        elif 'timestamp' in df_raw.columns:
            df_raw = df_raw.set_index('timestamp')
            
        # 根据特征类型选择列
        if self.features == 'S':
            # 单变量预测
            cols = [self.target]
        else:
            # 多变量预测
            cols = df_raw.columns.tolist()
            
        # 数据分割
        num_train = int(len(df_raw) * 0.7)
        num_val = int(len(df_raw) * 0.2)
        num_test = len(df_raw) - num_train - num_val
        
        # 根据标志选择数据
        if self.flag == 'train':
            data = df_raw.iloc[:num_train][cols]
        elif self.flag == 'val':
            data = df_raw.iloc[num_train:num_train+num_val][cols]
        elif self.flag == 'test':
            data = df_raw.iloc[-num_test:][cols]
        else:
            raise ValueError(f"Invalid flag: {self.flag}")
            
        # 标准化
        self.scaler = StandardScaler()
        data = self.scaler.fit_transform(data.values)
        
        # 转换为张量
        self.data = torch.FloatTensor(data)
        
    def __getitem__(self, index):
        # 获取输入序列
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len
        
        # 确保索引不越界
        if r_end > len(self.data):
            return None
            
        seq_x = self.data[s_begin:s_end]
        seq_y = self.data[r_begin:r_end]
        
        return seq_x, seq_y
    
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def inverse_transform(self, data):
        """
        反标准化
        """
        return self.scaler.inverse_transform(data)

class Dataset_Synthetic(Dataset):
    """
    合成数据集类，用于生成具有趋势和季节性的时间序列数据
    """
    def __init__(self, root_path, flag='train', size=None, features='M'):
        # 初始化参数
        self.root_path = root_path
        self.flag = flag
        self.features = features
        
        # 设置序列长度
        self.seq_len = size[0]
        self.label_len = size[1]
        self.pred_len = size[2]
        
        # 生成或读取数据
        self.__generate_data__()
        
    def __generate_data__(self):
        """
        生成合成数据或从文件读取
        """
        data_file = os.path.join(self.root_path, 'synthetic_data.csv')
        
        if os.path.exists(data_file):
            # 从文件读取
            df_raw = pd.read_csv(data_file)
            if 'timestamp' in df_raw.columns:
                df_raw = df_raw.set_index('timestamp')
        else:
            # 生成合成数据
            n_samples = 2000
            time_idx = np.arange(n_samples)
            
            # 趋势成分
            trend = 0.01 * time_idx
            
            # 季节性成分
            season1 = 1.0 * np.sin(2 * np.pi * time_idx / 50)  # 周期为50
            season2 = 0.5 * np.sin(2 * np.pi * time_idx / 100)  # 周期为100
            
            # 噪声
            noise = 0.2 * np.random.randn(n_samples)
            
            # 合成时间序列
            ts = trend + season1 + season2 + noise
            
            # 创建多变量时间序列
            ts2 = ts + 0.5 * np.roll(ts, 10) + 0.1 * np.random.randn(n_samples)
            ts3 = ts + 0.7 * np.roll(ts, -20) + 0.1 * np.random.randn(n_samples)
            
            # 创建DataFrame
            df_raw = pd.DataFrame({
                'timestamp': pd.date_range(start='2021-01-01', periods=n_samples, freq='H'),
                'var1': ts,
                'var2': ts2,
                'var3': ts3
            })
            
            df_raw.set_index('timestamp', inplace=True)
            
            # 保存到文件
            df_raw.to_csv(data_file)
            
        # 选择列
        if self.features == 'S':
            cols = ['var1']
        else:
            cols = df_raw.columns.tolist()
            
        # 数据分割
        num_train = int(len(df_raw) * 0.7)
        num_val = int(len(df_raw) * 0.2)
        num_test = len(df_raw) - num_train - num_val
        
        # 根据标志选择数据
        if self.flag == 'train':
            data = df_raw.iloc[:num_train][cols]
        elif self.flag == 'val':
            data = df_raw.iloc[num_train:num_train+num_val][cols]
        elif self.flag == 'test':
            data = df_raw.iloc[-num_test:][cols]
        else:
            raise ValueError(f"Invalid flag: {self.flag}")
            
        # 标准化
        self.scaler = StandardScaler()
        data = self.scaler.fit_transform(data.values)
        
        # 转换为张量
        self.data = torch.FloatTensor(data)
        
    def __getitem__(self, index):
        # 获取输入序列
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len
        
        # 确保索引不越界
        if r_end > len(self.data):
            return None
            
        seq_x = self.data[s_begin:s_end]
        seq_y = self.data[r_begin:r_end]
        
        return seq_x, seq_y
    
    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1
    
    def inverse_transform(self, data):
        """
        反标准化
        """
        return self.scaler.inverse_transform(data)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 模型输入准备模块

功能：
- 将提取的特征转换为CausalFormer模型所需的输入格式
- 构建训练、验证和测试数据集
- 提供数据集质量评估和可视化功能

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
import torch
from torch.utils.data import Dataset, DataLoader
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_model_input_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-ModelInput")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

class ModelInputError(Exception):
    """模型输入准备错误类"""
    pass

class CausalFormerDataset(Dataset):
    """
    CausalFormer模型数据集类
    """
    def __init__(self, X, y=None, transform=None):
        """
        初始化数据集
        
        参数:
        X (np.ndarray): 输入特征，形状为 (samples, time_steps, features)
        y (np.ndarray, optional): 目标值，形状为 (samples, time_steps, targets)
        transform (callable, optional): 数据变换函数
        """
        self.X = torch.FloatTensor(X)
        self.y = torch.FloatTensor(y) if y is not None else None
        self.transform = transform
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        if torch.is_tensor(idx):
            idx = idx.tolist()
        
        sample_X = self.X[idx]
        
        if self.transform:
            sample_X = self.transform(sample_X)
        
        if self.y is not None:
            sample_y = self.y[idx]
            return sample_X, sample_y
        else:
            return sample_X

def features_to_sequences(features_data, sequence_length=10, stride=1):
    """
    将特征转换为序列数据
    
    参数:
    features_data (dict): 特征数据字典
    sequence_length (int): 序列长度
    stride (int): 滑动步长
    
    返回:
    dict: 序列数据字典
    """
    if not isinstance(features_data, dict):
        error_msg = f"输入数据类型错误: {type(features_data)}, 应为 dict"
        logger.error(error_msg)
        raise ModelInputError(error_msg)
    
    logger.info(f"将特征转换为序列数据: 序列长度 {sequence_length}, 步长 {stride}")
    
    sequences_data = {}
    
    for stage, stage_features in features_data.items():
        logger.info(f"处理阶段 {stage} 的特征...")
        
        eeg_features = stage_features['eeg_features']
        ecg_features = stage_features['ecg_features']
        
        # 合并时域和频域特征
        eeg_combined = []
        ecg_combined = []
        
        if 'time' in eeg_features:
            time_features = eeg_features['time']
            # 将字典中的特征值合并为一个数组
            eeg_time_array = np.concatenate([feature_values for feature_values in time_features.values()], axis=1)
            eeg_combined.append(eeg_time_array)
        
        if 'frequency' in eeg_features:
            freq_features = eeg_features['frequency']
            # 处理频域特征，排除形状不匹配的特征
            freq_arrays = []
            for feature_values in freq_features.values():
                if len(feature_values.shape) == 2:  # 只处理2D数组
                    freq_arrays.append(feature_values)
                elif len(feature_values.shape) == 3:  # 处理3D数组
                    # 将3D数组展平为2D
                    reshaped = feature_values.reshape(feature_values.shape[0], -1)
                    freq_arrays.append(reshaped)
            
            if freq_arrays:
                eeg_freq_array = np.concatenate(freq_arrays, axis=1)
                eeg_combined.append(eeg_freq_array)
        
        if 'time' in ecg_features:
            time_features = ecg_features['time']
            ecg_time_array = np.concatenate([feature_values for feature_values in time_features.values()], axis=1)
            ecg_combined.append(ecg_time_array)
        
        if 'frequency' in ecg_features:
            freq_features = ecg_features['frequency']
            freq_arrays = []
            for feature_values in freq_features.values():
                if len(feature_values.shape) == 2:
                    freq_arrays.append(feature_values)
                elif len(feature_values.shape) == 3:
                    reshaped = feature_values.reshape(feature_values.shape[0], -1)
                    freq_arrays.append(reshaped)
            
            if freq_arrays:
                ecg_freq_array = np.concatenate(freq_arrays, axis=1)
                ecg_combined.append(ecg_freq_array)
        
        # 合并所有特征
        if eeg_combined:
            eeg_features_array = np.concatenate(eeg_combined, axis=1)
        else:
            error_msg = f"阶段 {stage} 没有有效的EEG特征"
            logger.error(error_msg)
            raise ModelInputError(error_msg)
        
        if ecg_combined:
            ecg_features_array = np.concatenate(ecg_combined, axis=1)
        else:
            error_msg = f"阶段 {stage} 没有有效的ECG特征"
            logger.error(error_msg)
            raise ModelInputError(error_msg)
        
        # 创建序列
        num_samples = eeg_features_array.shape[0]
        num_sequences = max(1, (num_samples - sequence_length) // stride + 1)
        
        eeg_sequences = np.zeros((num_sequences, sequence_length, eeg_features_array.shape[1]))
        ecg_sequences = np.zeros((num_sequences, sequence_length, ecg_features_array.shape[1]))
        
        for i in range(num_sequences):
            start_idx = i * stride
            end_idx = start_idx + sequence_length
            
            if end_idx <= num_samples:
                eeg_sequences[i] = eeg_features_array[start_idx:end_idx]
                ecg_sequences[i] = ecg_features_array[start_idx:end_idx]
        
        sequences_data[stage] = {
            'eeg_sequences': eeg_sequences,
            'ecg_sequences': ecg_sequences,
            'sequence_length': sequence_length,
            'stride': stride,
            'eeg_feature_dim': eeg_features_array.shape[1],
            'ecg_feature_dim': ecg_features_array.shape[1]
        }
        
        logger.info(f"  - EEG序列形状: {eeg_sequences.shape}")
        logger.info(f"  - ECG序列形状: {ecg_sequences.shape}")
    
    logger.info("序列数据转换完成")
    return sequences_data

def prepare_model_inputs(sequences_data, test_size=0.2, val_size=0.1, batch_size=32, random_state=42):
    """
    准备模型输入数据，使用时序划分方法划分数据集
    
    参数:
    sequences_data (dict): 序列数据字典
    test_size (float): 测试集比例，将从时间序列的末尾取这一比例的数据
    val_size (float): 验证集比例，将从时间序列的中间取这一比例的数据
    batch_size (int): 批次大小
    random_state (int): 随机种子，仅用于随机初始化，不影响划分
    
    返回:
    dict: 模型输入数据字典
    """
    if not isinstance(sequences_data, dict):
        error_msg = f"输入数据类型错误: {type(sequences_data)}, 应为 dict"
        logger.error(error_msg)
        raise ModelInputError(error_msg)
    
    logger.info(f"准备模型输入数据: 测试集比例 {test_size}, 验证集比例 {val_size}, 批次大小 {batch_size}")
    logger.info(f"采用时序划分方法: 前{int((1-test_size-val_size)*100)}%为训练集, 接着{int(val_size*100)}%为验证集, 最后{int(test_size*100)}%为测试集")
    
    model_inputs = {}
    
    for stage, stage_sequences in sequences_data.items():
        logger.info(f"处理阶段 {stage} 的序列数据...")
        
        eeg_sequences = stage_sequences['eeg_sequences']
        ecg_sequences = stage_sequences['ecg_sequences']
        
        # 将EEG序列作为输入，ECG序列作为目标
        X = eeg_sequences
        y = ecg_sequences
        
        # 计算时序划分的索引
        n_samples = len(X)
        train_end = int(n_samples * (1 - test_size - val_size))
        val_end = int(n_samples * (1 - test_size))
        
        # 按照时间顺序划分训练集、验证集和测试集
        X_train = X[:train_end]
        y_train = y[:train_end]
        
        X_val = X[train_end:val_end]
        y_val = y[train_end:val_end]
        
        X_test = X[val_end:]
        y_test = y[val_end:]
        
        logger.info(f"使用时序划分: 训练集 [0:{train_end}], 验证集 [{train_end}:{val_end}], 测试集 [{val_end}:{n_samples}]")
        
        # 标准化特征 - 注意：应该只使用训练集的统计信息来标准化所有数据
        scaler = StandardScaler()
        X_train_reshaped = X_train.reshape(-1, X_train.shape[-1])
        scaler.fit(X_train_reshaped)  # 只在训练集上拟合
        
        # 使用训练集的统计信息来转换所有数据集
        X_train_scaled = scaler.transform(X_train_reshaped).reshape(X_train.shape)
        
        X_val_reshaped = X_val.reshape(-1, X_val.shape[-1])
        X_val_scaled = scaler.transform(X_val_reshaped).reshape(X_val.shape)
        
        X_test_reshaped = X_test.reshape(-1, X_test.shape[-1])
        X_test_scaled = scaler.transform(X_test_reshaped).reshape(X_test.shape)
        
        # 创建数据集
        train_dataset = CausalFormerDataset(X_train_scaled, y_train)
        val_dataset = CausalFormerDataset(X_val_scaled, y_val)
        test_dataset = CausalFormerDataset(X_test_scaled, y_test)
        
        # 创建数据加载器 - 训练集shuffle=False以保持时序性
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False)
        val_loader = DataLoader(val_dataset, batch_size=batch_size)
        test_loader = DataLoader(test_dataset, batch_size=batch_size)
        
        model_inputs[stage] = {
            'train_loader': train_loader,
            'val_loader': val_loader,
            'test_loader': test_loader,
            'scaler': scaler,
            'input_dim': X.shape[-1],
            'output_dim': y.shape[-1],
            'sequence_length': stage_sequences['sequence_length'],
            'train_samples': len(X_train),
            'val_samples': len(X_val),
            'test_samples': len(X_test),
            'time_split': {
                'train_range': [0, train_end],
                'val_range': [train_end, val_end],
                'test_range': [val_end, n_samples]
            }
        }
        
        logger.info(f"  - 训练集样本数: {len(X_train)}")
        logger.info(f"  - 验证集样本数: {len(X_val)}")
        logger.info(f"  - 测试集样本数: {len(X_test)}")
        logger.info(f"  - 输入维度: {X.shape[-1]}")
        logger.info(f"  - 输出维度: {y.shape[-1]}")
    
    logger.info("模型输入数据准备完成")
    return model_inputs

def visualize_model_inputs(model_inputs, stage, output_dir=None):
    """
    可视化模型输入数据，明确EEG(输入)到ECG(目标)的映射关系
    
    参数:
    model_inputs (dict): 模型输入数据字典
    stage (str): 要可视化的阶段
    output_dir (str): 输出目录
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\model_inputs")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if stage not in model_inputs:
        error_msg = f"阶段 {stage} 不在模型输入数据中"
        logger.error(error_msg)
        raise ModelInputError(error_msg)
    
    stage_inputs = model_inputs[stage]
    train_loader = stage_inputs['train_loader']
    input_dim = stage_inputs['input_dim']
    output_dim = stage_inputs['output_dim']
    
    # 获取一批数据
    X_batch, y_batch = next(iter(train_loader))
    X_batch = X_batch.numpy()
    y_batch = y_batch.numpy()
    
    # 创建图形
    fig = plt.figure(figsize=(15, 12))
    
    # 添加总标题，说明模型任务
    fig.suptitle(f'CausalFormer模型输入/输出可视化 - EEG→ECG因果预测\n'
                 f'输入：EEG特征 ({input_dim}维) → 输出：ECG特征 ({output_dim}维)\n',
                 fontproperties=chinese_font, fontsize=12)
    
    # 可视化输入特征
    plt.subplot(3, 1, 1)
    im1 = plt.imshow(X_batch[0].T, aspect='auto', cmap='viridis')
    plt.colorbar(im1, label='特征值 / Feature Value')
    plt.title(f'输入特征 / Input Features (EEG)\n'
              f'形状: {X_batch.shape} [样本数, 时间步, EEG特征维度]', 
              fontproperties=chinese_font, fontsize=10)
    plt.xlabel('时间步 / Time Steps', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('EEG特征维度 / EEG Feature Dimensions', fontproperties=chinese_font, fontsize=10)
    
    # 可视化目标特征
    plt.subplot(3, 1, 2)
    im2 = plt.imshow(y_batch[0].T, aspect='auto', cmap='viridis')
    plt.colorbar(im2, label='特征值 / Feature Value')
    plt.title(f'目标特征 / Target Features (ECG)\n'
              f'形状: {y_batch.shape} [样本数, 时间步, ECG特征维度]', 
              fontproperties=chinese_font, fontsize=10)
    plt.xlabel('时间步 / Time Steps', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('ECG特征维度 / ECG Feature Dimensions', fontproperties=chinese_font, fontsize=10)
    
    # 添加特征维度对比图
    plt.subplot(3, 1, 3)
    # 创建一个简单的条形图来比较输入和输出维度
    dims = [input_dim, output_dim]
    bars = plt.bar(['EEG特征 (输入)', 'ECG特征 (目标)'], dims, color=['skyblue', 'salmon'])
    
    # 在条形上方添加数字
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{int(height)}',
                ha='center', va='bottom', fontproperties=chinese_font, fontsize=10)
    
    plt.title('特征维度对比 / Feature Dimension Comparison', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('特征维度 / Feature Dimensions', fontproperties=chinese_font, fontsize=10)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 添加数据集划分信息
    plt.figtext(0.5, 0.02, 
                f"数据集划分 (时序划分):\n"
                f"训练集: {stage_inputs['train_samples']}样本 (时间段 {stage_inputs['time_split']['train_range']})\n"
                f"验证集: {stage_inputs['val_samples']}样本 (时间段 {stage_inputs['time_split']['val_range']})\n"
                f"测试集: {stage_inputs['test_samples']}样本 (时间段 {stage_inputs['time_split']['test_range']})",
                ha='center', fontproperties=chinese_font, fontsize=10, 
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgrey', alpha=0.5))
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    
    # 保存图像
    output_file = os.path.join(output_dir, f"model_inputs_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"模型输入可视化结果已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def test_model_input_preparation(features_data):
    """
    测试模型输入准备模块
    
    参数:
    features_data (dict): 特征数据
    
    返回:
    bool: 测试结果
    """
    logger.info("开始测试模型输入准备模块...")
    
    try:
        # 将特征转换为序列
        sequence_length = 10
        stride = 1
        sequences_data = features_to_sequences(features_data, sequence_length, stride)
        
        # 准备模型输入
        batch_size = 32
        model_inputs = prepare_model_inputs(sequences_data, batch_size=batch_size)
        
        # 可视化模型输入
        for stage in model_inputs.keys():
            visualize_model_inputs(model_inputs, stage)
            break  # 只可视化第一个阶段
        
        logger.info("模型输入准备模块测试成功!")
        return True
    
    except ModelInputError as e:
        logger.error(f"模型输入准备失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 导入数据加载、通道处理、标准化、分段和特征提取模块
    from data_loader import load_hep_data
    from channel_processor import process_channels
    from data_normalizer import normalize_data
    from data_segmentation import segment_data_fixed_length
    from feature_extractor import extract_features
    
    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)
    
    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)
    
    # 标准化数据
    normalized_data = normalize_data(processed_data, method='z_score')
    
    # 分段数据
    segmented_data = {}
    for stage, data in normalized_data.items():
        eeg_data = data['eeg']
        ecg_data = data['ecg']
        
        segmented_data[stage] = data.copy()
        segmented_data[stage]['eeg'] = segment_data_fixed_length(eeg_data, segment_length=500)
        segmented_data[stage]['ecg'] = segment_data_fixed_length(ecg_data, segment_length=500)
    
    # 提取特征
    features_data = extract_features(segmented_data, feature_types=['time', 'frequency'])
    
    # 测试模型输入准备
    test_model_input_preparation(features_data)

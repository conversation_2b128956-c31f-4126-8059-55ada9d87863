<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="854.925015pt" height="426.469375pt" viewBox="0 0 854.925015 426.469375" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-21T15:59:13.753413</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M -0 426.469375 
L 854.925015 426.469375 
L 854.925015 0 
L -0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 37.559375 389.7475 
L 549.991469 389.7475 
L 549.991469 66.66 
L 37.559375 66.66 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1"/>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m8b47929062" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m8b47929062" x="60.851743" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −0.2 -->
      <g transform="translate(51.351743 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-2212" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m8b47929062" x="119.082663" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −0.1 -->
      <g transform="translate(109.582663 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m8b47929062" x="177.313582" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.0 -->
      <g transform="translate(169.563582 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m8b47929062" x="235.544502" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.1 -->
      <g transform="translate(227.794502 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m8b47929062" x="293.775422" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.2 -->
      <g transform="translate(286.025422 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m8b47929062" x="352.006342" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.3 -->
      <g transform="translate(344.256342 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m8b47929062" x="410.237261" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 0.4 -->
      <g transform="translate(402.487261 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m8b47929062" x="468.468181" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0.5 -->
      <g transform="translate(460.718181 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m8b47929062" x="526.699101" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 0.6 -->
      <g transform="translate(518.949101 403.817812) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-36" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_10">
      <defs>
       <path id="mf73567bf41" d="M 0 0 
L 0 2 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#mf73567bf41" x="89.967203" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_11">
      <g>
       <use xlink:href="#mf73567bf41" x="148.198122" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_12">
      <g>
       <use xlink:href="#mf73567bf41" x="206.429042" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_13">
      <g>
       <use xlink:href="#mf73567bf41" x="264.659962" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_14">
      <g>
       <use xlink:href="#mf73567bf41" x="322.890882" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_15">
      <g>
       <use xlink:href="#mf73567bf41" x="381.121801" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_16">
      <g>
       <use xlink:href="#mf73567bf41" x="439.352721" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_17">
      <g>
       <use xlink:href="#mf73567bf41" x="497.583641" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- 时间 (秒) -->
     <g transform="translate(273.525422 417.549062) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-65f6" d="M 5094 -13 
L 5114 -224 
Q 5114 -358 5040 -432 
Q 4966 -506 4892 -534 
Q 4819 -563 4774 -563 
Q 4730 -563 4576 -489 
Q 4422 -416 4243 -304 
Q 4064 -192 3904 -70 
Q 3744 51 3641 150 
Q 3539 250 3539 294 
Q 3539 339 3600 339 
Q 3661 339 3821 269 
Q 4237 70 4685 -38 
L 4666 3091 
L 3142 3002 
Q 3104 2995 3072 2995 
L 3002 2995 
Q 2822 2995 2771 3046 
Q 2650 3168 2605 3341 
Q 2598 3360 2598 3389 
Q 2598 3418 2617 3418 
Q 2637 3418 2717 3389 
Q 2797 3360 2950 3360 
L 2995 3360 
L 4666 3456 
L 4659 4589 
Q 4659 4774 4579 4883 
Q 4499 4992 4499 5030 
Q 4499 5069 4579 5069 
Q 4659 5069 4828 5008 
Q 4998 4947 5033 4899 
Q 5069 4851 5069 4762 
L 5075 3482 
L 5562 3507 
Q 5766 3526 5827 3552 
Q 5888 3578 5933 3578 
Q 5978 3578 6064 3523 
Q 6150 3469 6217 3392 
Q 6285 3315 6285 3264 
Q 6285 3213 6237 3197 
Q 6189 3181 6125 3174 
L 5075 3117 
L 5094 -13 
z
M 4032 1389 
Q 3885 1254 3811 1254 
Q 3738 1254 3674 1350 
Q 3398 1824 3059 2246 
Q 3021 2298 3021 2346 
Q 3021 2394 3075 2438 
Q 3130 2483 3190 2508 
Q 3251 2534 3280 2534 
Q 3309 2534 3341 2496 
Q 3373 2458 3446 2368 
Q 3520 2278 3629 2150 
Q 3738 2022 3843 1891 
Q 3949 1760 4019 1654 
Q 4090 1549 4090 1497 
Q 4090 1446 4032 1389 
z
M 2003 2400 
L 1094 2355 
L 1120 1088 
L 1978 1114 
L 2003 2400 
z
M 2042 3974 
L 1062 3891 
L 1088 2694 
L 2016 2739 
L 2042 3974 
z
M 691 3885 
Q 691 4058 646 4160 
Q 602 4262 602 4313 
Q 602 4365 678 4365 
Q 755 4365 1050 4230 
L 2144 4320 
L 2214 4320 
Q 2278 4320 2377 4272 
Q 2477 4224 2477 4109 
Q 2477 4070 2464 4032 
Q 2451 3994 2445 3962 
L 2342 1126 
Q 2522 928 2522 867 
Q 2522 806 2464 796 
Q 2406 787 2336 781 
L 1126 742 
L 1133 378 
L 1133 358 
Q 1133 224 1024 224 
Q 883 224 800 304 
Q 717 384 717 429 
L 717 493 
Q 717 512 736 589 
Q 755 666 755 896 
L 691 3885 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-95f4" d="M 2643 1139 
L 3795 1190 
L 3840 1990 
L 2611 1920 
L 2643 1139 
z
M 2598 2253 
L 3859 2323 
L 3904 3053 
L 2566 2976 
L 2598 2253 
z
M 2547 435 
Q 2522 435 2496 444 
Q 2470 454 2394 480 
Q 2266 525 2266 653 
L 2266 1062 
Q 2176 2995 2166 3100 
Q 2157 3206 2125 3280 
Q 2093 3354 2093 3395 
Q 2093 3437 2163 3437 
Q 2234 3437 2573 3341 
L 3987 3437 
Q 4147 3437 4249 3360 
Q 4352 3283 4352 3213 
L 4179 1197 
Q 4352 992 4352 931 
Q 4352 870 4285 857 
Q 4218 845 2656 774 
L 2662 563 
Q 2662 435 2547 435 
z
M 4154 282 
Q 4154 333 4205 333 
Q 4256 333 4371 275 
Q 4787 58 5280 -96 
L 5331 4378 
L 3066 4230 
Q 2976 4211 2877 4211 
Q 2778 4211 2739 4256 
Q 2701 4301 2621 4419 
Q 2541 4538 2541 4598 
Q 2541 4659 2573 4659 
Q 2579 4659 2588 4652 
Q 2598 4646 2716 4630 
Q 2835 4614 2938 4614 
Q 5446 4774 5545 4774 
Q 5645 4774 5712 4694 
Q 5779 4614 5779 4563 
L 5747 4397 
L 5696 -109 
L 5702 -275 
Q 5702 -461 5590 -557 
Q 5478 -653 5408 -653 
Q 5338 -653 5120 -531 
Q 4902 -410 4528 -106 
Q 4154 198 4154 282 
z
M 1805 3987 
Q 1619 4288 1165 4800 
Q 1114 4864 1114 4931 
Q 1114 4998 1194 5056 
Q 1274 5114 1312 5114 
Q 1408 5114 1914 4550 
Q 2176 4262 2176 4154 
Q 2176 4128 2086 4022 
Q 1997 3917 1926 3917 
Q 1856 3917 1805 3987 
z
M 1165 -486 
Q 1120 -486 1030 -435 
Q 800 -320 800 -166 
Q 800 -128 825 16 
Q 851 160 851 301 
L 851 3430 
Q 851 3584 736 3738 
Q 704 3782 704 3795 
Q 704 3834 803 3834 
Q 902 3834 1049 3782 
Q 1197 3731 1232 3689 
Q 1267 3648 1267 3565 
L 1274 -326 
Q 1274 -486 1165 -486 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-28" d="M 2016 -896 
Q 2016 -1018 1914 -1018 
Q 1862 -1018 1715 -883 
Q 1350 -544 1037 102 
Q 640 902 640 1846 
Q 640 2790 998 3644 
Q 1357 4499 1728 4902 
Q 1882 5062 1946 5062 
Q 2067 5062 2067 4954 
Q 2067 4902 2035 4870 
Q 1619 4384 1369 3478 
Q 1120 2573 1120 1865 
Q 1120 1158 1350 384 
Q 1581 -390 1965 -774 
Q 2016 -826 2016 -896 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-79d2" d="M 4627 2323 
L 4621 1734 
Q 4621 1574 4506 1587 
Q 4346 1587 4237 1709 
Q 4192 1760 4192 1811 
Q 4192 1862 4205 1942 
Q 4218 2022 4224 2150 
L 4256 4787 
Q 4256 4954 4182 5043 
Q 4109 5133 4109 5171 
Q 4109 5210 4182 5210 
Q 4256 5210 4413 5158 
Q 4570 5107 4611 5065 
Q 4653 5024 4653 4928 
L 4627 2323 
z
M 5722 2195 
Q 5222 1133 4441 438 
Q 3661 -256 2528 -589 
Q 2445 -614 2361 -614 
Q 2278 -614 2278 -570 
Q 2278 -493 2470 -410 
Q 3398 6 4108 652 
Q 4819 1299 5171 2131 
Q 5254 2330 5254 2554 
Q 5254 2637 5306 2637 
Q 5370 2637 5472 2563 
Q 5574 2490 5657 2397 
Q 5741 2304 5741 2265 
Q 5741 2227 5722 2195 
z
M 1434 -326 
Q 1478 32 1478 237 
L 1478 397 
L 1491 1805 
Q 1491 1901 1500 2019 
Q 1510 2138 1517 2227 
L 1523 2310 
Q 1286 1715 794 1101 
Q 582 838 416 681 
Q 250 525 192 525 
Q 147 525 147 576 
Q 147 627 218 736 
Q 928 1734 1427 2938 
L 877 2893 
Q 800 2886 742 2880 
Q 685 2874 627 2874 
Q 570 2874 470 2922 
Q 371 2970 294 3194 
Q 288 3206 288 3235 
Q 288 3264 333 3264 
L 634 3232 
L 691 3232 
L 1498 3296 
L 1504 4051 
Q 781 3802 518 3802 
Q 429 3802 429 3856 
Q 429 3910 550 3955 
Q 557 3962 717 4026 
Q 2074 4582 2253 4934 
Q 2278 4998 2326 4998 
Q 2374 4998 2505 4844 
Q 2637 4691 2637 4620 
Q 2637 4550 2598 4531 
Q 2240 4339 1882 4198 
L 1882 3328 
L 2157 3347 
Q 2304 3360 2381 3385 
Q 2458 3411 2502 3411 
Q 2547 3411 2630 3360 
Q 2842 3232 2842 3123 
Q 2842 3053 2733 3040 
L 1882 2970 
L 1882 2522 
Q 1958 2566 1987 2566 
Q 2016 2566 2112 2483 
Q 2310 2330 2576 2026 
Q 2842 1722 2842 1626 
Q 2842 1587 2765 1491 
Q 2688 1395 2605 1395 
Q 2522 1395 2432 1536 
Q 2208 1907 1882 2246 
L 1882 -416 
Q 1882 -582 1766 -582 
Q 1619 -582 1526 -499 
Q 1434 -416 1434 -326 
z
M 5773 2624 
Q 5370 3238 4902 3770 
Q 4858 3821 4858 3872 
Q 4858 3923 4938 3996 
Q 5018 4070 5082 4070 
Q 5146 4070 5190 4013 
Q 5677 3507 5920 3187 
Q 6163 2867 6163 2809 
Q 6163 2752 6105 2694 
Q 6048 2637 5977 2598 
Q 5907 2560 5862 2560 
Q 5818 2560 5773 2624 
z
M 3123 2854 
Q 3462 3482 3462 3898 
Q 3462 3987 3516 3987 
Q 3571 3987 3673 3920 
Q 3776 3853 3862 3769 
Q 3949 3686 3949 3641 
Q 3949 3597 3923 3546 
Q 3616 2938 3357 2621 
Q 3098 2304 2934 2192 
Q 2771 2080 2736 2080 
Q 2701 2080 2701 2131 
Q 2701 2182 2835 2374 
Q 2970 2566 3123 2854 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-29" d="M 518 -883 
Q 371 -1018 320 -1018 
Q 218 -1018 218 -896 
Q 218 -826 269 -774 
Q 653 -390 883 384 
Q 1114 1158 1114 1865 
Q 1114 2573 864 3478 
Q 614 4384 198 4870 
Q 166 4902 166 4954 
Q 166 5062 288 5062 
Q 358 5062 512 4902 
Q 877 4499 1203 3738 
Q 1594 2790 1594 1849 
Q 1594 909 1238 179 
Q 883 -550 518 -883 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-65f6"/>
      <use xlink:href="#LXGWWenKai-Regular-95f4" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-79d2" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="369.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_18">
      <defs>
       <path id="mb6db3c8428" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="381.931618" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −6 -->
      <g transform="translate(21.059375 385.466775) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-36" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_19">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="332.3243" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −4 -->
      <g transform="translate(21.059375 335.859457) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_20">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="282.716982" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −2 -->
      <g transform="translate(21.059375 286.252139) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_21">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="233.109664" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 0 -->
      <g transform="translate(24.559375 236.644821) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_22">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="183.502346" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 2 -->
      <g transform="translate(24.559375 187.037503) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_23">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="133.895028" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 4 -->
      <g transform="translate(24.559375 137.430185) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_24">
      <g>
       <use xlink:href="#mb6db3c8428" x="37.559375" y="84.28771" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 6 -->
      <g transform="translate(24.559375 87.822867) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_25">
      <defs>
       <path id="mf25b24706b" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="357.127959" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_26">
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="307.520641" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_27">
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="257.913323" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_28">
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="208.306005" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_29">
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="158.698687" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_30">
      <g>
       <use xlink:href="#mf25b24706b" x="37.559375" y="109.091369" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_18">
     <!-- 振幅 (μV) -->
     <g transform="translate(15.159375 249.363906) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-632f" d="M 326 3590 
Q 429 3565 506 3565 
L 627 3565 
Q 672 3565 723 3571 
L 1402 3616 
L 1408 4621 
Q 1408 4794 1293 4973 
Q 1274 5005 1274 5024 
Q 1274 5069 1341 5069 
Q 1408 5069 1517 5043 
Q 1805 4966 1805 4800 
L 1798 3642 
L 1958 3654 
Q 2048 3661 2137 3699 
Q 2227 3738 2259 3738 
Q 2291 3738 2374 3686 
Q 2598 3546 2598 3450 
Q 2598 3366 2438 3354 
L 1792 3302 
L 1786 2227 
Q 2042 2349 2275 2477 
Q 2509 2605 2569 2605 
Q 2630 2605 2630 2560 
Q 2630 2445 1786 1901 
L 1773 122 
L 1786 -141 
Q 1786 -301 1680 -400 
Q 1574 -499 1466 -499 
Q 1261 -499 902 -108 
Q 544 282 544 384 
Q 544 416 585 416 
Q 627 416 838 272 
Q 1050 128 1382 6 
L 1389 1658 
Q 666 1235 531 1235 
Q 493 1235 454 1254 
Q 282 1350 173 1517 
Q 160 1542 160 1555 
Q 160 1594 243 1603 
Q 326 1613 659 1734 
Q 992 1856 1395 2042 
L 1402 3270 
L 851 3226 
Q 710 3213 611 3213 
Q 512 3213 474 3245 
Q 384 3334 345 3424 
Q 307 3514 300 3523 
Q 294 3533 294 3561 
Q 294 3590 326 3590 
z
M 3174 4282 
Q 3174 3142 3123 2547 
L 5280 2656 
Q 5427 2669 5504 2697 
Q 5581 2726 5622 2726 
Q 5664 2726 5801 2627 
Q 5939 2528 5939 2438 
Q 5939 2368 5786 2355 
L 4307 2278 
Q 4557 1722 4723 1440 
Q 5203 1920 5216 2150 
Q 5229 2246 5289 2246 
Q 5350 2246 5420 2176 
Q 5491 2106 5539 2029 
Q 5587 1952 5587 1913 
Q 5587 1875 5466 1741 
Q 5216 1446 4902 1178 
Q 5446 474 6240 13 
Q 6317 -32 6317 -70 
Q 6317 -90 6259 -154 
Q 6106 -339 5990 -339 
Q 5965 -339 5926 -314 
Q 5466 26 5107 384 
Q 4410 1088 3968 2259 
L 3782 2253 
L 3763 166 
Q 4147 288 4406 406 
Q 4666 525 4730 525 
Q 4794 525 4794 486 
Q 4794 378 4285 90 
Q 3776 -198 3481 -323 
Q 3187 -448 3123 -448 
Q 3059 -448 2966 -361 
Q 2874 -275 2813 -185 
Q 2752 -96 2752 -67 
Q 2752 -38 2816 -38 
L 2906 -38 
Q 3149 -38 3373 51 
L 3392 2234 
L 3091 2214 
Q 2976 1203 2541 307 
Q 2362 -58 2211 -262 
Q 2061 -467 2006 -467 
Q 1952 -467 1952 -403 
Q 1952 -339 1997 -250 
Q 2592 1024 2714 2342 
Q 2771 2970 2771 3456 
Q 2771 3942 2768 4227 
Q 2765 4512 2717 4598 
Q 2669 4685 2669 4733 
Q 2669 4781 2771 4781 
Q 2874 4781 3194 4621 
L 5069 4742 
Q 5216 4755 5283 4780 
Q 5350 4806 5401 4806 
Q 5453 4806 5530 4749 
Q 5728 4621 5728 4518 
Q 5728 4448 5574 4435 
L 3174 4282 
z
M 3546 3616 
L 4742 3686 
Q 4890 3699 4957 3724 
Q 5024 3750 5075 3750 
Q 5126 3750 5210 3699 
Q 5402 3565 5402 3462 
Q 5402 3386 5248 3373 
L 3821 3283 
Q 3770 3277 3725 3277 
L 3642 3277 
Q 3514 3277 3491 3302 
Q 3469 3328 3385 3417 
Q 3302 3507 3302 3571 
Q 3302 3635 3334 3635 
L 3546 3616 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e45" d="M 2336 1325 
Q 2170 1325 1747 1824 
L 1728 -429 
Q 1728 -595 1619 -595 
Q 1562 -595 1440 -521 
Q 1318 -448 1318 -326 
L 1318 -294 
Q 1318 -275 1337 -156 
Q 1357 -38 1370 550 
L 1395 3322 
L 986 3290 
L 973 1414 
Q 973 1254 867 1254 
Q 762 1254 659 1350 
Q 557 1446 557 1491 
Q 557 1536 579 1638 
Q 602 1741 602 1862 
L 614 3264 
Q 614 3482 589 3558 
L 550 3661 
Q 544 3693 544 3712 
Q 544 3757 611 3757 
Q 678 3757 979 3629 
L 1402 3661 
L 1408 4640 
Q 1408 4858 1306 5005 
Q 1280 5037 1280 5065 
Q 1280 5094 1350 5094 
Q 1421 5094 1571 5056 
Q 1722 5018 1750 4957 
Q 1779 4896 1779 4826 
L 1766 3686 
L 2336 3725 
L 2400 3725 
Q 2547 3725 2618 3597 
Q 2637 3558 2637 3526 
L 2618 3398 
L 2624 1805 
L 2630 1632 
Q 2630 1472 2524 1398 
Q 2419 1325 2336 1325 
z
M 2893 4576 
L 5293 4717 
Q 5402 4723 5472 4745 
Q 5542 4768 5590 4768 
Q 5638 4768 5708 4710 
Q 5779 4653 5830 4582 
Q 5882 4512 5882 4474 
Q 5882 4397 5722 4384 
L 3117 4224 
L 3040 4224 
Q 2880 4224 2797 4285 
Q 2714 4346 2653 4445 
Q 2592 4544 2592 4569 
Q 2592 4595 2630 4595 
L 2688 4595 
L 2893 4576 
z
M 3194 3475 
Q 3174 3642 3113 3728 
Q 3053 3814 3053 3849 
Q 3053 3885 3158 3885 
Q 3264 3885 3552 3795 
L 5133 3891 
L 5190 3891 
Q 5293 3891 5366 3824 
Q 5440 3757 5440 3709 
Q 5440 3661 5427 3632 
Q 5414 3603 5402 3565 
L 5286 2938 
Q 5421 2778 5421 2723 
Q 5421 2669 5366 2656 
Q 5312 2643 5229 2637 
L 3667 2560 
L 3667 2502 
Q 3667 2355 3565 2355 
Q 3520 2355 3430 2394 
Q 3270 2458 3270 2566 
L 3270 2592 
Q 3283 2669 3283 2720 
L 3277 2810 
L 3194 3475 
z
M 4992 3552 
L 3578 3469 
L 3635 2880 
L 4915 2950 
L 4992 3552 
z
M 2234 3379 
L 1766 3347 
L 1754 1984 
Q 1958 1875 2240 1760 
L 2234 3379 
z
M 5581 2170 
L 5638 2176 
Q 5728 2176 5805 2093 
Q 5882 2010 5882 1958 
Q 5882 1907 5869 1878 
Q 5856 1850 5850 1818 
L 5670 173 
Q 5869 -32 5869 -86 
Q 5869 -141 5821 -150 
Q 5773 -160 5690 -166 
L 3347 -224 
L 3354 -371 
L 3354 -397 
Q 3354 -531 3264 -531 
Q 3130 -531 3043 -448 
Q 2957 -365 2957 -269 
L 2957 -237 
Q 2970 -96 2970 32 
L 2970 96 
L 2886 1677 
Q 2874 1875 2819 1987 
Q 2765 2099 2765 2128 
Q 2765 2157 2841 2157 
Q 2918 2157 3232 2054 
L 5581 2170 
z
M 5446 1830 
L 4506 1786 
L 4499 1165 
L 5395 1197 
L 5446 1830 
z
M 4147 1773 
L 3245 1728 
L 3277 1114 
L 4141 1152 
L 4147 1773 
z
M 5370 883 
L 4499 851 
L 4493 147 
L 5306 166 
L 5370 883 
z
M 4141 838 
L 3290 806 
L 3328 115 
L 4134 134 
L 4141 838 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-3bc" d="M 1658 -102 
Q 1190 -102 998 307 
L 979 -499 
Q 973 -826 979 -1171 
Q 979 -1216 886 -1216 
Q 794 -1216 656 -1155 
Q 518 -1094 512 -1018 
L 512 -1005 
Q 538 -691 538 -448 
L 627 2349 
Q 646 2790 634 3027 
Q 640 3098 736 3098 
Q 832 3098 957 3030 
Q 1082 2963 1094 2874 
L 1094 2854 
Q 1094 2771 1081 2604 
Q 1069 2438 1062 2298 
L 1030 1235 
Q 1030 1146 1046 1002 
Q 1062 858 1123 694 
Q 1184 531 1305 419 
Q 1427 307 1683 307 
Q 1939 307 2179 508 
Q 2419 710 2438 1101 
L 2496 2323 
Q 2509 2637 2496 2995 
Q 2496 3046 2589 3046 
Q 2682 3046 2816 2982 
Q 2950 2918 2963 2835 
L 2963 2822 
Q 2957 2733 2947 2582 
Q 2938 2432 2931 2272 
L 2874 890 
Q 2867 666 2880 576 
Q 2918 250 3132 250 
Q 3347 250 3468 304 
Q 3590 358 3657 358 
Q 3725 358 3731 300 
Q 3738 243 3648 154 
Q 3430 -83 3110 -83 
Q 2906 -83 2736 6 
Q 2566 96 2528 333 
Q 2381 109 2144 3 
Q 1907 -102 1658 -102 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-56" d="M 70 4403 
Q 70 4506 307 4506 
Q 410 4506 506 4474 
Q 602 4442 614 4378 
Q 653 4230 944 3494 
Q 1235 2758 1946 634 
Q 2765 2816 3059 3539 
Q 3354 4262 3411 4480 
Q 3430 4550 3536 4550 
Q 3642 4550 3770 4473 
Q 3898 4397 3898 4307 
Q 3898 4275 3821 4147 
Q 3744 4019 3667 3840 
Q 2387 813 2240 224 
Q 2214 109 2202 38 
Q 2182 -109 2035 -109 
Q 1933 -109 1817 -9 
Q 1702 90 1606 374 
Q 1510 659 1344 1126 
Q 429 3667 77 4378 
Q 70 4390 70 4403 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-632f"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="326.199936"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="388.199921"/>
     </g>
    </g>
    <g id="text_19">
     <!-- 1e−6 -->
     <g transform="translate(37.559375 63.66) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-65" d="M 992 1613 
Q 1709 1613 2317 1779 
Q 2547 1850 2614 1933 
Q 2682 2016 2682 2182 
Q 2682 2739 2035 2739 
Q 1594 2739 1274 2371 
Q 954 2003 864 1613 
L 992 1613 
z
M 384 1626 
L 442 1632 
Q 538 2138 992 2624 
Q 1446 3110 2042 3110 
Q 2438 3110 2675 2970 
Q 3104 2720 3104 2176 
Q 3104 1958 3069 1830 
Q 3034 1702 2906 1622 
Q 2778 1542 2490 1453 
Q 1850 1254 896 1254 
L 813 1254 
L 813 1190 
Q 813 774 1027 518 
Q 1242 262 1606 262 
Q 2413 262 2912 800 
Q 2995 896 3059 896 
Q 3123 896 3123 829 
Q 3123 762 3030 614 
Q 2938 467 2746 294 
Q 2278 -115 1562 -115 
Q 1235 -115 969 48 
Q 704 211 550 518 
Q 397 826 397 1248 
L 397 1338 
Q 262 1440 262 1542 
Q 262 1626 384 1626 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="line2d_31">
    <path d="M 60.851743 167.853699 
L 62.016361 168.75441 
L 64.345598 176.014333 
L 65.510216 178.850562 
L 66.674835 183.223279 
L 67.839453 191.098546 
L 69.004072 201.14985 
L 70.16869 208.498319 
L 71.333308 208.047539 
L 72.497927 199.07435 
L 73.662545 187.310743 
L 74.827164 182.364106 
L 75.991782 191.837439 
L 77.1564 216.179914 
L 78.321019 247.95378 
L 79.485637 276.055841 
L 80.650256 291.889518 
L 81.814874 293.269303 
L 82.979492 283.792752 
L 84.144111 268.902785 
L 86.473348 234.191451 
L 88.802584 198.109591 
L 89.967203 187.070344 
L 91.131821 186.794597 
L 92.29644 198.021261 
L 94.625676 234.360435 
L 95.790295 245.929257 
L 96.954913 249.454992 
L 98.119532 247.866643 
L 99.28415 245.551233 
L 100.448768 244.777889 
L 101.613387 244.366776 
L 102.778005 241.418115 
L 103.942623 234.453797 
L 105.107242 225.298365 
L 106.27186 218.16865 
L 107.436479 216.730784 
L 108.601097 221.45378 
L 109.765715 229.245637 
L 110.930334 235.504072 
L 112.094952 236.904843 
L 113.259571 232.905141 
L 114.424189 225.192304 
L 116.753426 206.937076 
L 117.918044 199.121772 
L 119.082663 194.082818 
L 120.247281 193.936331 
L 121.411899 200.13717 
L 123.741136 223.846657 
L 124.905755 230.995559 
L 126.070373 229.165538 
L 128.39961 209.72001 
L 129.564228 207.70248 
L 130.728847 218.868224 
L 133.058083 264.887261 
L 134.222702 279.436936 
L 135.38732 277.508784 
L 136.551939 259.773201 
L 138.881175 208.953638 
L 140.045794 191.915133 
L 141.210412 183.448629 
L 142.375031 180.214488 
L 144.704267 176.7134 
L 145.868886 176.674811 
L 147.033504 180.333344 
L 148.198122 187.691155 
L 149.362741 196.006778 
L 150.527359 201.576448 
L 151.691978 202.512557 
L 152.856596 200.224644 
L 154.021214 198.325253 
L 155.185833 199.704306 
L 156.350451 203.971334 
L 157.51507 207.249963 
L 158.679688 204.61135 
L 159.844306 193.525952 
L 162.173543 157.742385 
L 163.338162 145.372216 
L 164.50278 142.567321 
L 165.667398 148.277937 
L 166.832017 157.422924 
L 167.996635 163.71088 
L 169.161254 162.921676 
L 170.325872 154.948911 
L 171.49049 143.711499 
L 172.655109 135.143834 
L 173.819727 134.328435 
L 174.984346 143.127364 
L 176.148964 159.390388 
L 177.313582 178.097479 
L 178.478201 193.896796 
L 179.642819 203.780863 
L 180.807438 208.484204 
L 181.972056 211.78068 
L 183.136674 217.99322 
L 184.301293 229.097067 
L 185.465911 243.127727 
L 186.63053 254.901028 
L 187.795148 258.698536 
L 188.959766 251.389452 
L 190.124385 234.211385 
L 192.453621 192.071713 
L 193.61824 178.757452 
L 194.782858 174.125939 
L 195.947477 176.607547 
L 198.276713 188.439624 
L 199.441332 191.501767 
L 200.60595 190.844131 
L 201.770569 186.709882 
L 202.935187 179.93439 
L 205.264424 162.926419 
L 206.429042 155.798355 
L 207.593661 152.467863 
L 208.758279 155.095026 
L 209.922897 164.545105 
L 212.252134 194.734132 
L 213.416753 205.224282 
L 214.581371 205.85656 
L 215.745989 195.552489 
L 218.075226 160.729051 
L 219.239845 150.377772 
L 220.404463 150.068454 
L 221.569081 157.348973 
L 222.7337 166.185465 
L 223.898318 171.17183 
L 225.062937 171.345753 
L 226.227555 170.979369 
L 227.392173 176.650514 
L 228.556792 192.42506 
L 230.886029 240.65836 
L 232.050647 255.745832 
L 233.215265 255.424905 
L 234.379884 240.359553 
L 235.544502 217.701635 
L 236.70912 197.373855 
L 237.873739 187.271478 
L 239.038357 189.940051 
L 240.202976 202.117237 
L 241.367594 216.887153 
L 242.532212 227.102755 
L 243.696831 228.53122 
L 244.861449 221.575918 
L 246.026068 210.972962 
L 247.190686 203.441475 
L 248.355304 204.017231 
L 249.519923 212.650296 
L 250.684541 223.077282 
L 251.84916 225.286697 
L 253.013778 210.978594 
L 254.178396 179.237969 
L 255.343015 138.763826 
L 256.507633 104.496715 
L 257.672252 89.842582 
L 258.83687 98.842169 
L 261.166107 147.015158 
L 262.330725 155.330067 
L 263.495344 143.570036 
L 264.659962 119.642935 
L 265.82458 98.665765 
L 266.989199 93.167068 
L 268.153817 105.157687 
L 269.318436 125.17213 
L 270.483054 138.815303 
L 271.647672 136.460977 
L 272.812291 119.755778 
L 273.976909 100.723774 
L 275.141528 94.073339 
L 276.306146 107.618713 
L 278.635383 167.03693 
L 279.800001 181.575107 
L 280.964619 171.606077 
L 282.129238 141.227537 
L 283.293856 105.371943 
L 284.458475 81.714224 
L 285.623093 81.345795 
L 286.787711 103.469939 
L 287.95233 136.858527 
L 289.116948 166.81331 
L 290.281567 183.276094 
L 291.446185 185.334241 
L 292.610803 179.849831 
L 293.775422 175.706519 
L 294.94004 177.817894 
L 297.269277 191.955052 
L 298.433895 195.665546 
L 299.598514 197.592394 
L 300.763132 203.367587 
L 301.927751 217.962385 
L 304.256987 265.540446 
L 305.421606 282.938238 
L 306.586224 288.4489 
L 307.750843 285.307147 
L 308.915461 282.644097 
L 310.080079 288.844783 
L 311.244698 305.050543 
L 312.409316 323.797774 
L 313.573935 334.164521 
L 314.738553 329.903342 
L 317.06779 299.612501 
L 318.232408 296.567373 
L 319.397027 308.837072 
L 320.561645 329.158873 
L 321.726263 344.672417 
L 322.890882 345.991888 
L 324.0555 333.715778 
L 325.220118 317.717875 
L 326.384737 309.89507 
L 327.549355 315.804121 
L 329.878592 345.97849 
L 331.04321 351.395745 
L 332.207829 346.242279 
L 333.372447 336.2979 
L 334.537066 328.784379 
L 335.701684 326.256173 
L 336.866302 324.835058 
L 338.030921 317.942145 
L 339.195539 302.230816 
L 341.524776 260.972456 
L 342.689394 248.904584 
L 343.854013 244.885742 
L 345.018631 243.711817 
L 346.18325 239.396432 
L 348.512486 221.432547 
L 349.677105 219.32833 
L 350.841723 227.853371 
L 353.17096 259.453901 
L 354.335578 266.39581 
L 355.500197 262.230106 
L 357.829434 239.414388 
L 358.994052 232.990444 
L 360.15867 232.13147 
L 361.323289 233.715583 
L 362.487907 234.339985 
L 363.652526 232.900771 
L 365.981762 227.965177 
L 367.146381 224.802194 
L 368.310999 219.264342 
L 370.640236 203.520758 
L 371.804854 201.482766 
L 372.969473 208.463037 
L 375.298709 236.961695 
L 376.463328 242.996128 
L 377.627946 236.805257 
L 379.957183 207.236044 
L 381.121801 201.176567 
L 382.28642 205.763509 
L 383.451038 215.302835 
L 384.615657 220.405599 
L 385.780275 214.631461 
L 386.944893 199.109716 
L 388.109512 182.031822 
L 389.27413 173.549058 
L 390.438749 179.659037 
L 391.603367 199.028048 
L 392.767985 224.352363 
L 393.932604 246.742495 
L 395.097222 259.981592 
L 396.261841 262.288364 
L 397.426459 255.441084 
L 398.591077 242.89419 
L 399.755696 228.591662 
L 400.920314 216.822373 
L 402.084933 212.100376 
L 403.249551 217.970267 
L 404.414169 234.881309 
L 406.743406 281.501326 
L 407.908025 295.222824 
L 409.072643 295.936073 
L 410.237261 286.362614 
L 411.40188 274.441623 
L 412.566498 268.606085 
L 413.731116 272.640285 
L 416.060353 293.497896 
L 417.224972 295.780194 
L 418.38959 288.923446 
L 419.554208 278.023667 
L 420.718827 271.166375 
L 421.883445 273.756982 
L 424.212682 296.314956 
L 425.3773 300.38402 
L 426.541919 292.747237 
L 428.871156 262.046532 
L 430.035774 257.238206 
L 431.200392 265.969579 
L 433.529629 302.43855 
L 434.694248 312.750446 
L 435.858866 311.943092 
L 437.023484 303.159153 
L 438.188103 292.732948 
L 439.352721 285.796477 
L 440.51734 283.613904 
L 441.681958 284.176535 
L 444.011195 285.968106 
L 445.175813 288.992832 
L 446.340432 296.453223 
L 447.50505 308.336246 
L 448.669668 321.463892 
L 449.834287 331.145075 
L 450.998905 334.029005 
L 452.163524 329.942594 
L 453.328142 321.357044 
L 455.657379 300.130753 
L 456.821997 287.791882 
L 459.151234 259.411518 
L 460.315852 250.313889 
L 461.480471 251.57513 
L 462.645089 265.070788 
L 464.974326 307.736502 
L 466.138944 318.744938 
L 467.303563 315.407145 
L 468.468181 300.975122 
L 469.632799 284.60131 
L 470.797418 276.060623 
L 471.962036 280.119837 
L 473.126655 294.028349 
L 474.291273 309.664557 
L 475.455891 318.947174 
L 476.62051 319.07064 
L 477.785128 314.239266 
L 478.949747 312.771828 
L 480.114365 321.368111 
L 481.278983 340.157391 
L 482.443602 361.745685 
L 483.60822 375.061705 
L 484.772839 371.821398 
L 485.937457 351.726291 
L 487.102075 323.161881 
L 488.266694 298.836271 
L 489.431312 288.877048 
L 490.595931 295.474409 
L 492.925167 328.299891 
L 494.089786 335.011859 
L 495.254404 330.226012 
L 497.583641 307.479563 
L 498.748259 302.014621 
L 499.912878 302.025761 
L 501.077496 303.09003 
L 502.242114 300.16854 
L 503.406733 291.084229 
L 508.065206 236.706912 
L 510.394443 213.215739 
L 511.559062 205.411537 
L 512.72368 205.833813 
L 513.888298 217.086156 
L 516.217535 257.229801 
L 517.382154 269.403034 
L 518.546772 267.722584 
L 519.71139 253.777495 
L 520.876009 235.586987 
L 522.040627 222.887301 
L 523.205246 221.255885 
L 524.369864 228.763251 
L 525.534482 237.267934 
L 526.699101 237.514649 
L 526.699101 237.514649 
" clip-path="url(#p55e789c2b0)" style="fill: none; stroke: #e63946; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_32">
    <path d="M 322.890882 389.7475 
L 322.890882 66.66 
" clip-path="url(#p55e789c2b0)" style="fill: none; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #ff0000"/>
   </g>
   <g id="line2d_33">
    <path d="M 439.352721 389.7475 
L 439.352721 66.66 
" clip-path="url(#p55e789c2b0)" style="fill: none; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #ff0000"/>
   </g>
   <g id="line2d_34">
    <path d="M 37.559375 233.109664 
L 549.991469 233.109664 
" clip-path="url(#p55e789c2b0)" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_35">
    <path d="M 177.313582 389.7475 
L 177.313582 66.66 
" clip-path="url(#p55e789c2b0)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 37.559375 389.7475 
L 37.559375 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 549.991469 389.7475 
L 549.991469 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 37.559375 389.7475 
L 549.991469 389.7475 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 37.559375 66.66 
L 549.991469 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_20">
    <!-- Fz通道的心跳诱发电位 -->
    <g transform="translate(243.415266 60.66) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-46" d="M 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4230 
Q 3565 4128 3482 4128 
L 3085 4128 
Q 2912 4128 2822 4122 
L 1126 4006 
L 1126 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1126 2202 
L 1126 685 
L 1146 6 
Q 1146 -58 1027 -58 
Q 909 -58 787 -10 
Q 666 38 666 122 
L 678 691 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7a" d="M 1862 384 
Q 2579 384 2682 390 
L 2688 390 
Q 2758 390 2816 275 
Q 2874 160 2874 57 
Q 2874 -45 2803 -45 
Q 2541 -19 2285 -6 
L 1421 -6 
Q 845 -6 672 -35 
Q 499 -64 428 -64 
Q 358 -64 297 35 
Q 237 134 237 214 
Q 237 294 259 348 
Q 282 403 355 454 
Q 429 506 589 717 
Q 1376 1779 2163 2656 
L 1222 2586 
Q 1050 2579 781 2541 
L 768 2541 
Q 646 2541 550 2752 
Q 512 2842 512 2909 
Q 512 2976 557 2976 
L 570 2976 
Q 736 2963 870 2963 
L 1075 2963 
Q 1152 2963 1216 2970 
L 2214 3040 
L 2445 3078 
L 2470 3078 
Q 2611 3066 2726 2874 
Q 2778 2797 2778 2742 
Q 2778 2688 2694 2643 
Q 2611 2598 2573 2554 
Q 1613 1453 806 352 
Q 1126 384 1370 384 
L 1862 384 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-901a" d="M 2368 755 
Q 2400 979 2400 1210 
L 2419 3347 
Q 2419 3526 2374 3641 
Q 2330 3757 2330 3792 
Q 2330 3827 2394 3827 
Q 2458 3827 2765 3693 
L 3661 3744 
Q 3514 3866 3168 4090 
Q 3098 4134 3098 4173 
Q 3174 4371 3254 4371 
Q 3334 4371 3789 4013 
Q 4154 4243 4589 4576 
L 2816 4448 
Q 2771 4442 2733 4442 
L 2662 4442 
Q 2554 4442 2464 4515 
Q 2374 4589 2342 4685 
L 2310 4774 
Q 2310 4806 2362 4806 
Q 2374 4806 2387 4806 
L 2605 4781 
L 4845 4922 
L 4909 4922 
Q 5050 4922 5123 4838 
Q 5197 4755 5197 4694 
Q 5197 4634 5149 4598 
Q 5101 4563 4845 4358 
Q 4589 4154 4032 3821 
L 4096 3763 
L 5139 3821 
Q 5165 3827 5219 3827 
Q 5274 3827 5360 3766 
Q 5446 3706 5446 3622 
L 5414 3475 
L 5427 858 
L 5434 672 
Q 5434 550 5344 435 
Q 5254 320 5145 320 
Q 5037 320 4662 624 
Q 4288 928 4288 1018 
Q 4288 1056 4339 1056 
Q 4390 1056 4576 969 
Q 4762 883 5062 794 
L 5056 1754 
L 4045 1709 
L 4045 768 
Q 4045 627 3955 627 
Q 3942 627 3866 653 
Q 3661 736 3661 890 
Q 3693 1114 3693 1370 
L 3693 1690 
L 2758 1645 
L 2758 685 
Q 2758 621 2742 576 
Q 2726 531 2662 531 
L 2586 550 
Q 2368 608 2368 755 
z
M 1453 3718 
Q 1203 4070 768 4493 
Q 704 4550 704 4611 
Q 704 4672 787 4736 
Q 870 4800 902 4800 
Q 934 4800 1049 4710 
Q 1165 4621 1305 4483 
Q 1446 4346 1580 4202 
Q 1715 4058 1804 3942 
Q 1894 3827 1894 3772 
Q 1894 3718 1801 3632 
Q 1709 3546 1641 3546 
Q 1574 3546 1453 3718 
z
M 5050 3494 
L 4045 3437 
L 4045 2906 
L 5050 2957 
L 5050 3494 
z
M 3706 3418 
L 2758 3366 
L 2758 2848 
L 3699 2893 
L 3706 3418 
z
M 390 3072 
Q 544 3046 595 3046 
L 672 3046 
Q 698 3046 723 3053 
L 1677 3149 
L 1754 3155 
Q 1830 3155 1913 3081 
Q 1997 3008 1997 2950 
Q 1997 2893 1958 2854 
L 1882 2778 
Q 1824 2701 1725 2573 
Q 1626 2445 1456 2221 
Q 1286 1997 1286 1965 
Q 1286 1933 1318 1914 
Q 1830 1606 1920 1421 
Q 1965 1344 1965 1280 
Q 1965 1043 1408 582 
Q 1619 570 2147 464 
Q 2675 358 3900 169 
Q 5126 -19 5958 -19 
L 5990 -19 
Q 6131 -19 6131 -70 
Q 6131 -90 6093 -179 
Q 5984 -422 5811 -422 
L 5773 -422 
Q 4973 -397 4070 -250 
Q 2528 13 1990 128 
Q 1453 243 1165 243 
Q 877 243 544 128 
Q 480 109 441 109 
Q 403 109 339 147 
Q 275 186 275 371 
Q 275 474 454 512 
Q 634 550 909 576 
Q 1274 896 1536 1235 
Q 1549 1261 1549 1280 
Q 1549 1331 1318 1485 
Q 1114 1638 1075 1658 
Q 909 1766 909 1884 
Q 909 2003 1053 2217 
Q 1197 2432 1434 2771 
L 838 2694 
Q 749 2682 717 2682 
Q 685 2682 617 2694 
Q 550 2707 473 2771 
Q 397 2835 358 2989 
Q 352 3002 352 3027 
Q 352 3072 390 3072 
z
M 5056 2656 
L 4045 2598 
L 4045 2003 
L 5056 2042 
L 5056 2656 
z
M 3699 2586 
L 2758 2534 
L 2758 1946 
L 3699 1984 
L 3699 2586 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-9053" d="M 3942 3776 
Q 4026 3693 4026 3645 
Q 4026 3597 3997 3545 
Q 3968 3494 3629 3187 
L 4864 3258 
L 4928 3258 
Q 5107 3258 5197 3098 
Q 5229 3040 5229 3014 
Q 5229 2989 5216 2957 
Q 5203 2925 5197 2893 
L 5069 1011 
Q 5248 813 5248 752 
Q 5248 691 5197 681 
Q 5146 672 5062 666 
L 3200 608 
L 3206 525 
L 3206 499 
Q 3206 371 3110 371 
Q 2976 371 2893 441 
Q 2810 512 2810 589 
L 2810 634 
Q 2822 774 2822 941 
L 2822 1030 
L 2752 2790 
Q 2733 3021 2697 3101 
Q 2662 3181 2662 3216 
Q 2662 3251 2739 3251 
Q 2816 3251 3110 3155 
L 3219 3162 
Q 3590 3578 3648 3757 
L 2554 3693 
Q 2426 3680 2323 3680 
Q 2221 3680 2150 3763 
Q 2029 3917 2029 3968 
Q 2029 4019 2080 4019 
L 2112 4019 
Q 2221 4000 2323 4000 
L 3923 4096 
Q 4224 4429 4467 4845 
Q 4512 4915 4538 5082 
Q 4550 5152 4611 5152 
Q 4672 5152 4813 5018 
Q 4954 4896 4954 4825 
Q 4954 4755 4800 4569 
Q 4646 4384 4352 4122 
L 5133 4173 
Q 5325 4186 5401 4211 
Q 5478 4237 5526 4237 
Q 5574 4237 5651 4192 
Q 5728 4147 5785 4083 
Q 5843 4019 5843 3968 
Q 5843 3898 5690 3885 
L 3942 3776 
z
M 3264 4211 
Q 3104 4429 2790 4730 
Q 2746 4774 2746 4809 
Q 2746 4845 2797 4931 
Q 2848 5018 2909 5018 
Q 2970 5018 3082 4928 
Q 3194 4838 3325 4713 
Q 3456 4589 3549 4480 
Q 3642 4371 3642 4326 
Q 3642 4282 3558 4192 
Q 3475 4102 3411 4102 
Q 3347 4102 3264 4211 
z
M 1434 3712 
Q 1107 4160 749 4506 
Q 678 4576 678 4617 
Q 678 4659 729 4736 
Q 781 4813 841 4813 
Q 902 4813 1014 4720 
Q 1126 4627 1273 4489 
Q 1421 4352 1555 4205 
Q 1690 4058 1779 3946 
Q 1869 3834 1869 3776 
Q 1869 3718 1779 3635 
Q 1690 3552 1619 3552 
Q 1549 3552 1434 3712 
z
M 403 3072 
Q 557 3046 614 3046 
L 691 3046 
Q 717 3046 742 3053 
L 1811 3162 
Q 1862 3162 1958 3094 
Q 2054 3027 2054 2960 
Q 2054 2893 2010 2854 
L 1939 2778 
Q 1338 2016 1338 1958 
Q 1338 1933 1370 1914 
Q 1741 1677 1885 1536 
Q 2029 1395 2029 1267 
Q 2029 1139 1875 966 
Q 1722 794 1446 550 
Q 1645 538 2105 438 
Q 2566 339 3827 144 
Q 5088 -51 5971 -51 
L 6003 -51 
Q 6150 -51 6150 -109 
Q 6150 -115 6118 -205 
Q 6010 -454 5837 -454 
L 5786 -454 
Q 4410 -416 1805 134 
Q 1466 211 1155 211 
Q 845 211 518 96 
Q 454 77 416 77 
Q 378 77 314 115 
Q 250 154 250 269 
Q 250 384 288 422 
Q 371 506 934 550 
Q 1280 870 1594 1229 
Q 1606 1254 1606 1261 
Q 1606 1338 1126 1651 
Q 954 1766 954 1888 
Q 954 2010 1114 2246 
Q 1274 2483 1485 2784 
L 858 2707 
Q 819 2701 794 2701 
L 730 2701 
Q 570 2701 467 2822 
Q 365 2944 365 3008 
Q 365 3072 403 3072 
z
M 4800 2931 
L 3117 2842 
L 3130 2451 
L 4781 2528 
L 4800 2931 
z
M 4768 2221 
L 3142 2144 
L 3162 1734 
L 4749 1798 
L 4768 2221 
z
M 4736 1498 
L 3174 1427 
L 3187 954 
L 4717 998 
L 4736 1498 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7684" d="M 3846 4890 
L 3834 4992 
Q 3834 5069 3898 5069 
Q 3917 5069 4019 5024 
Q 4307 4896 4307 4762 
Q 4307 4589 3898 3738 
L 5504 3834 
L 5530 3834 
Q 5613 3834 5696 3773 
Q 5779 3712 5779 3616 
L 5760 3469 
Q 5709 1747 5530 301 
Q 5427 -544 5082 -544 
Q 4934 -544 4841 -483 
Q 4749 -422 4605 -313 
Q 4461 -205 4301 -70 
Q 4141 64 4025 179 
Q 3910 294 3910 345 
Q 3910 397 3984 397 
Q 4058 397 4282 262 
Q 4506 128 4947 -45 
L 4960 -45 
Q 4998 -45 5018 6 
Q 5299 1107 5338 3469 
L 3731 3366 
Q 3450 2880 3232 2595 
Q 3014 2310 2944 2310 
Q 2912 2310 2912 2371 
Q 2912 2432 3104 2787 
Q 3296 3142 3571 3875 
Q 3846 4608 3846 4845 
L 3846 4890 
z
M 1715 4749 
Q 1715 4768 1702 4845 
Q 1702 4922 1779 4922 
Q 1920 4922 2035 4832 
Q 2150 4742 2150 4665 
Q 2150 4589 1920 4160 
Q 1715 3802 1581 3603 
L 2483 3654 
L 2560 3654 
Q 2746 3654 2803 3507 
Q 2822 3462 2822 3456 
L 2790 3328 
L 2688 480 
Q 2842 282 2842 214 
Q 2842 147 2790 134 
Q 2739 122 2656 115 
L 1229 64 
L 1235 -147 
L 1235 -166 
Q 1235 -307 1126 -307 
Q 979 -307 892 -233 
Q 806 -160 806 -77 
L 851 243 
L 794 3232 
Q 794 3418 717 3539 
Q 672 3629 672 3664 
Q 672 3699 768 3699 
Q 864 3699 1165 3584 
L 1222 3584 
Q 1715 4512 1715 4749 
z
M 2400 3302 
L 1171 3238 
L 1190 2061 
L 2368 2118 
L 2400 3302 
z
M 4755 1446 
Q 4755 1318 4589 1229 
Q 4525 1190 4483 1190 
Q 4442 1190 4358 1299 
Q 4160 1619 3913 1913 
Q 3667 2208 3564 2307 
Q 3462 2406 3462 2464 
Q 3462 2522 3532 2589 
Q 3603 2656 3657 2656 
Q 3712 2656 3869 2505 
Q 4026 2355 4390 1939 
Q 4755 1523 4755 1446 
z
M 2355 1773 
L 1197 1722 
L 1222 422 
L 2317 461 
L 2355 1773 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8df3" d="M 4755 2931 
Q 5370 3597 5370 3859 
Q 5370 3955 5424 3955 
Q 5478 3955 5555 3891 
Q 5760 3712 5760 3571 
Q 5760 3533 5728 3488 
Q 5190 2899 4755 2637 
L 4755 2298 
Q 5734 1722 5811 1562 
Q 5830 1517 5830 1469 
Q 5830 1421 5756 1328 
Q 5683 1235 5632 1235 
Q 5581 1235 5357 1436 
Q 5133 1638 4749 1907 
L 4736 454 
Q 4736 320 4762 237 
Q 4826 70 5280 70 
Q 5734 70 5830 179 
Q 5965 333 6054 1069 
Q 6080 1325 6163 1325 
Q 6246 1325 6252 1101 
Q 6259 877 6259 643 
Q 6259 410 6240 198 
Q 6221 -13 6134 -128 
Q 6048 -243 5849 -284 
Q 5651 -326 5296 -326 
Q 4941 -326 4733 -268 
Q 4525 -211 4435 -54 
Q 4346 102 4346 384 
L 4378 4672 
Q 4378 4838 4330 4905 
Q 4282 4973 4282 5011 
Q 4282 5050 4349 5050 
Q 4416 5050 4518 5030 
Q 4774 4979 4774 4870 
L 4755 2931 
z
M 3974 4710 
Q 3987 4301 3987 3802 
Q 3987 1971 3763 1274 
Q 3488 416 2854 -90 
Q 2598 -294 2406 -387 
Q 2214 -480 2169 -480 
Q 2125 -480 2125 -429 
Q 2125 -378 2403 -144 
Q 2682 90 2931 416 
Q 3373 1005 3514 1933 
Q 2816 1325 2675 1325 
Q 2630 1325 2540 1373 
Q 2451 1421 2380 1485 
Q 2310 1549 2310 1587 
Q 2310 1626 2368 1645 
Q 2426 1664 2560 1715 
Q 2893 1843 3565 2349 
Q 3616 2778 3616 3690 
Q 3616 4602 3520 4749 
Q 3482 4806 3482 4832 
Q 3482 4883 3552 4883 
Q 3622 4883 3712 4870 
Q 3974 4832 3974 4710 
z
M 768 2950 
L 787 3110 
L 787 3302 
Q 787 3341 781 3379 
L 710 4237 
Q 698 4410 602 4550 
Q 589 4570 589 4582 
Q 589 4621 672 4621 
Q 755 4621 1082 4525 
L 2227 4614 
L 2272 4614 
Q 2381 4614 2470 4547 
Q 2560 4480 2560 4429 
Q 2560 4378 2541 4352 
Q 2522 4326 2515 4275 
L 2394 3328 
Q 2554 3181 2554 3113 
Q 2554 3046 2512 3030 
Q 2470 3014 2406 3008 
L 1850 2976 
L 1843 2189 
L 1920 2195 
Q 2080 2208 2179 2233 
Q 2278 2259 2336 2259 
Q 2394 2259 2467 2208 
Q 2541 2157 2589 2089 
Q 2637 2022 2637 1978 
Q 2637 1907 2528 1894 
L 1837 1856 
L 1824 608 
Q 2106 698 2371 790 
Q 2637 883 2704 883 
Q 2771 883 2771 845 
Q 2771 768 2310 531 
Q 1850 294 1283 64 
Q 717 -166 608 -166 
Q 499 -166 390 -67 
Q 282 32 237 112 
Q 192 192 192 211 
Q 192 256 320 256 
Q 448 256 755 307 
L 704 2061 
Q 704 2195 656 2284 
Q 608 2374 608 2412 
Q 608 2451 707 2451 
Q 806 2451 986 2394 
Q 1088 2355 1088 2272 
L 1114 397 
Q 1267 435 1446 493 
L 1466 2950 
L 1178 2931 
L 1178 2854 
Q 1178 2720 1082 2720 
Q 1037 2720 902 2781 
Q 768 2842 768 2950 
z
M 2131 4282 
L 1082 4211 
L 1152 3245 
L 2048 3309 
L 2131 4282 
z
M 3174 2656 
Q 2944 3155 2669 3469 
Q 2611 3539 2611 3596 
Q 2611 3654 2688 3702 
Q 2765 3750 2819 3750 
Q 2874 3750 2966 3654 
Q 3059 3558 3161 3417 
Q 3264 3277 3353 3129 
Q 3443 2982 3500 2876 
Q 3558 2771 3558 2729 
Q 3558 2688 3465 2617 
Q 3373 2547 3299 2547 
Q 3226 2547 3174 2656 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8bf1" d="M 1472 2586 
L 806 2515 
Q 768 2509 742 2509 
L 678 2509 
Q 518 2509 416 2630 
Q 314 2752 314 2816 
Q 314 2880 352 2880 
Q 506 2854 563 2854 
L 640 2854 
Q 666 2854 691 2861 
L 1677 2976 
Q 1728 2976 1824 2909 
Q 1920 2842 1920 2774 
Q 1920 2707 1875 2669 
L 1837 2630 
L 1760 550 
Q 1869 627 2192 883 
Q 2515 1139 2585 1142 
Q 2656 1146 2659 1107 
Q 2662 1069 2556 947 
Q 2451 826 2124 445 
Q 1798 64 1651 -29 
Q 1504 -122 1437 -131 
Q 1370 -141 1267 -70 
Q 1165 0 1085 86 
Q 1005 173 998 211 
Q 998 243 1113 265 
Q 1229 288 1395 358 
L 1472 2586 
z
M 4288 2336 
Q 4288 2195 4211 2195 
Q 4173 2195 4096 2246 
Q 4019 2298 3974 2342 
Q 3930 2387 3930 2435 
Q 3930 2483 3952 2579 
Q 3974 2675 3974 2912 
L 3974 3027 
Q 3974 3098 3984 3187 
Q 3994 3277 3949 3277 
Q 3814 3002 3350 2659 
Q 2886 2317 2614 2185 
Q 2342 2054 2285 2054 
Q 2253 2054 2253 2108 
Q 2253 2163 2362 2246 
Q 3098 2746 3654 3373 
L 2963 3334 
L 2886 3334 
Q 2746 3334 2650 3382 
Q 2554 3430 2496 3622 
Q 2490 3642 2490 3661 
L 2490 3699 
Q 2496 3699 2573 3680 
Q 2650 3661 2829 3661 
L 2944 3661 
L 3968 3712 
L 3968 4262 
Q 2982 4064 2844 4064 
Q 2707 4064 2707 4122 
Q 2707 4198 2918 4250 
Q 3974 4518 4883 4922 
Q 4986 4960 5043 5046 
Q 5101 5133 5129 5133 
Q 5158 5133 5222 5046 
Q 5286 4960 5331 4857 
Q 5376 4755 5376 4707 
Q 5376 4659 5318 4646 
Q 4685 4429 4275 4333 
L 4282 3731 
L 5229 3782 
Q 5440 3802 5526 3827 
Q 5613 3853 5645 3853 
Q 5677 3853 5741 3821 
Q 5933 3725 5933 3613 
Q 5933 3501 5824 3494 
L 4518 3424 
Q 5267 2867 6208 2445 
Q 6266 2419 6266 2384 
Q 6266 2349 6227 2298 
Q 6074 2131 6019 2131 
Q 5965 2131 5741 2253 
Q 5126 2586 4486 3072 
Q 4422 3142 4301 3283 
Q 4250 3283 4266 3187 
Q 4282 3091 4282 3008 
Q 4282 2925 4285 2758 
Q 4288 2592 4294 2464 
Q 4301 2336 4288 2336 
z
M 2784 2112 
Q 2963 2080 3194 2080 
L 3290 2080 
L 4704 2170 
Q 4736 2176 4749 2176 
L 4794 2176 
Q 4832 2176 4931 2109 
Q 5030 2042 5030 1955 
Q 5030 1869 4992 1827 
Q 4954 1786 4934 1754 
L 4659 1197 
L 5376 1242 
L 5427 1242 
Q 5498 1242 5603 1174 
Q 5709 1107 5709 1033 
Q 5709 960 5673 905 
Q 5638 851 5626 794 
Q 5459 32 5299 -285 
Q 5139 -602 4902 -602 
Q 4749 -602 4374 -323 
Q 4000 -45 4000 70 
Q 4000 109 4041 109 
Q 4083 109 4345 0 
Q 4608 -109 4883 -166 
L 4909 -166 
Q 4954 -166 4986 -128 
Q 5203 371 5293 877 
L 4768 851 
Q 4672 845 4611 825 
Q 4550 806 4502 806 
Q 4454 806 4400 851 
Q 4346 896 4307 957 
Q 4269 1018 4269 1050 
Q 4269 1082 4294 1133 
L 4608 1824 
L 3853 1779 
Q 3680 979 3296 448 
Q 2912 -83 2272 -448 
Q 2182 -499 2138 -499 
Q 2118 -499 2118 -438 
Q 2118 -378 2246 -269 
Q 2726 122 3027 595 
Q 3328 1069 3482 1747 
L 3290 1741 
Q 3245 1734 3206 1734 
L 3104 1734 
Q 2982 1747 2944 1773 
Q 2835 1869 2771 2035 
Q 2758 2054 2758 2083 
Q 2758 2112 2771 2112 
L 2784 2112 
z
M 1811 3654 
Q 1485 4102 1126 4448 
Q 1056 4518 1056 4560 
Q 1056 4602 1107 4678 
Q 1158 4755 1219 4755 
Q 1280 4755 1392 4662 
Q 1504 4570 1651 4432 
Q 1798 4294 1932 4147 
Q 2067 4000 2156 3888 
Q 2246 3776 2246 3718 
Q 2246 3661 2156 3577 
Q 2067 3494 1996 3494 
Q 1926 3494 1811 3654 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-53d1" d="M 3482 563 
Q 2829 96 2048 -205 
Q 1267 -506 992 -506 
Q 890 -506 890 -448 
Q 890 -365 1178 -262 
Q 1466 -160 1837 13 
Q 2656 390 3174 794 
Q 2720 1190 2170 1843 
Q 1594 941 883 346 
Q 595 109 524 109 
Q 454 109 454 163 
Q 454 218 672 432 
Q 890 646 1184 1024 
Q 1798 1824 2093 2432 
Q 2246 2758 2394 3162 
L 1421 3104 
Q 1350 3098 1305 3072 
Q 1261 3046 1193 3046 
Q 1126 3046 1020 3107 
Q 915 3168 915 3232 
Q 915 3296 928 3347 
Q 1549 4378 1587 4538 
L 1606 4755 
L 1581 4954 
Q 1587 4998 1644 4998 
Q 1702 4998 1878 4896 
Q 2054 4794 2054 4672 
Q 2054 4550 1421 3462 
L 2522 3533 
Q 2816 4570 2816 4758 
Q 2816 4947 2790 5030 
Q 2765 5114 2765 5133 
Q 2765 5197 2841 5197 
Q 2918 5197 3027 5165 
Q 3315 5062 3315 4909 
Q 3181 4218 2963 3558 
L 5050 3686 
Q 5184 3693 5254 3718 
Q 5325 3744 5379 3744 
Q 5434 3744 5520 3680 
Q 5606 3616 5664 3542 
Q 5722 3469 5722 3437 
Q 5715 3366 5562 3354 
L 2842 3194 
Q 2739 2925 2496 2432 
L 4589 2541 
Q 4736 2541 4848 2489 
Q 4960 2438 4960 2355 
L 4960 2298 
Q 4960 2246 4902 2192 
Q 4845 2138 4723 1942 
Q 4602 1747 4333 1420 
Q 4064 1094 3776 819 
Q 4819 160 6010 -141 
Q 6099 -173 6099 -259 
Q 6099 -346 5888 -486 
Q 5811 -538 5734 -538 
Q 5658 -538 4966 -240 
Q 4275 58 3482 563 
z
M 4634 3949 
Q 4358 4352 3981 4691 
Q 3923 4742 3923 4777 
Q 3923 4813 3923 4848 
Q 3923 4883 4012 4944 
Q 4102 5005 4134 5005 
Q 4166 5005 4275 4928 
Q 4384 4851 4512 4729 
Q 4640 4608 4761 4480 
Q 4883 4352 4963 4246 
Q 5043 4141 5043 4102 
Q 5043 4064 4998 4006 
Q 4890 3846 4800 3846 
Q 4710 3846 4634 3949 
z
M 3462 1043 
Q 4013 1562 4371 2176 
L 2451 2080 
Q 2925 1466 3462 1043 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7535" d="M 992 3482 
Q 979 3699 924 3801 
Q 870 3904 870 3949 
Q 870 3994 966 3994 
Q 1062 3994 1395 3878 
L 2803 3949 
L 2810 4870 
Q 2810 5030 2733 5152 
Q 2720 5178 2720 5197 
Q 2720 5235 2768 5235 
Q 2816 5235 2925 5216 
Q 3226 5171 3226 5011 
L 3219 3968 
L 4774 4045 
L 4832 4045 
Q 4992 4045 5081 3952 
Q 5171 3859 5171 3792 
Q 5171 3725 5152 3686 
Q 5133 3648 5126 3610 
L 4915 1600 
Q 5094 1446 5094 1344 
Q 5094 1267 4922 1254 
L 3206 1184 
L 3200 448 
Q 3200 256 3280 176 
Q 3360 96 3574 77 
Q 3789 58 4413 58 
Q 5037 58 5331 96 
Q 5478 115 5549 211 
Q 5690 378 5805 1242 
Q 5843 1536 5914 1536 
Q 6010 1536 6010 1037 
Q 6010 538 5971 266 
Q 5933 -6 5814 -140 
Q 5696 -275 5443 -320 
Q 5190 -365 4464 -365 
Q 3738 -365 3478 -349 
Q 3219 -333 3085 -269 
Q 2784 -128 2784 326 
L 2790 1171 
L 1530 1120 
L 1542 954 
L 1542 941 
Q 1542 838 1411 838 
Q 1280 838 1193 915 
Q 1107 992 1107 1075 
L 1107 1126 
Q 1114 1190 1114 1242 
L 1114 1440 
Q 1114 1491 1107 1549 
L 992 3482 
z
M 4531 1587 
L 4602 2502 
L 3213 2438 
L 3206 1536 
L 4531 1587 
z
M 4634 2854 
L 4704 3686 
L 3219 3622 
L 3213 2784 
L 4634 2854 
z
M 2790 1517 
L 2797 2419 
L 1459 2355 
L 1510 1472 
L 2790 1517 
z
M 2797 2765 
L 2803 3603 
L 1395 3533 
L 1440 2694 
L 2797 2765 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-4f4d" d="M 1907 4864 
L 1907 4954 
Q 1907 4979 1888 5056 
Q 1888 5107 1955 5107 
Q 2022 5107 2128 5056 
Q 2234 5005 2314 4934 
Q 2394 4864 2394 4806 
Q 2394 4749 2173 4275 
Q 1952 3802 1670 3360 
L 1670 -493 
Q 1670 -621 1536 -621 
Q 1402 -621 1309 -521 
Q 1216 -422 1216 -364 
Q 1216 -307 1235 -220 
Q 1254 -134 1254 77 
L 1280 2810 
Q 1056 2522 806 2266 
Q 352 1779 237 1779 
Q 186 1779 186 1843 
Q 186 1907 269 1997 
Q 992 2893 1472 3814 
Q 1683 4218 1795 4506 
Q 1907 4794 1907 4864 
z
M 2643 3181 
L 2451 3174 
Q 2330 3174 2291 3206 
Q 2080 3392 2080 3546 
Q 2080 3584 2112 3584 
Q 2144 3584 2214 3568 
Q 2285 3552 2426 3552 
L 2509 3552 
L 3699 3616 
L 3680 4627 
Q 3680 4768 3565 4941 
Q 3546 4973 3546 4992 
Q 3546 5043 3648 5043 
Q 3750 5043 3894 4995 
Q 4038 4947 4076 4905 
Q 4115 4864 4115 4800 
L 4109 3635 
L 5133 3686 
Q 5280 3699 5366 3728 
Q 5453 3757 5475 3757 
Q 5498 3757 5587 3706 
Q 5837 3565 5837 3443 
Q 5837 3366 5690 3347 
L 2643 3181 
z
M 2336 -173 
L 2240 -173 
Q 2086 -173 2029 -128 
Q 1901 -19 1859 93 
Q 1818 205 1818 230 
Q 1818 256 1856 256 
Q 1894 256 1990 230 
Q 2086 205 2240 205 
L 3955 256 
Q 4346 1331 4531 2470 
Q 4570 2682 4570 2742 
Q 4570 2803 4531 2883 
Q 4493 2963 4493 2989 
Q 4493 3046 4582 3046 
L 4659 3034 
Q 4845 3014 4957 2947 
Q 5069 2880 5069 2822 
Q 5069 2765 4989 2416 
Q 4909 2067 4723 1436 
Q 4538 806 4314 262 
L 5517 301 
Q 5658 301 5750 329 
Q 5843 358 5875 358 
Q 5907 358 5990 307 
Q 6227 179 6227 45 
Q 6227 -58 6061 -70 
L 2336 -173 
z
M 3674 960 
Q 3712 781 3712 713 
Q 3712 646 3638 601 
Q 3565 557 3494 550 
L 3418 538 
Q 3302 538 3270 710 
Q 3072 1798 2778 2611 
Q 2752 2669 2752 2736 
Q 2752 2803 2857 2851 
Q 2963 2899 3008 2899 
Q 3091 2899 3149 2752 
Q 3526 1728 3674 960 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-46"/>
     <use xlink:href="#LXGWWenKai-Regular-7a" x="58.199997"/>
     <use xlink:href="#LXGWWenKai-Regular-901a" x="107.199982"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="207.199966"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="307.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-5fc3" x="407.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-8df3" x="507.199921"/>
     <use xlink:href="#LXGWWenKai-Regular-8bf1" x="607.199905"/>
     <use xlink:href="#LXGWWenKai-Regular-53d1" x="707.19989"/>
     <use xlink:href="#LXGWWenKai-Regular-7535" x="807.199875"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4d" x="907.19986"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="line2d_36">
     <path d="M 472.991469 79.639687 
L 482.991469 79.639687 
L 492.991469 79.639687 
" style="fill: none; stroke: #e63946; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="text_21">
     <!-- 练习阶段 -->
     <g transform="translate(500.991469 83.139687) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-7ec3" d="M 1805 1888 
Q 2074 1933 2240 1961 
Q 2406 1990 2496 1984 
Q 2586 1978 2586 1926 
Q 2573 1837 2201 1680 
Q 1830 1523 1404 1414 
Q 979 1306 912 1312 
Q 845 1318 800 1357 
Q 685 1504 653 1609 
Q 621 1715 621 1728 
Q 621 1786 685 1779 
L 781 1773 
Q 928 1760 1062 1766 
Q 1267 2131 1446 2490 
L 1510 2605 
Q 1050 2938 602 3149 
Q 499 3200 486 3251 
Q 474 3302 515 3401 
Q 557 3501 614 3513 
Q 672 3526 781 3482 
Q 800 3475 813 3469 
Q 1286 4198 1389 4480 
Q 1498 4774 1498 4915 
L 1498 4979 
Q 1498 5050 1542 5050 
Q 1574 5050 1658 4998 
Q 1888 4851 1888 4717 
Q 1850 4557 1651 4186 
Q 1478 3859 1120 3296 
L 1274 3206 
Q 1523 3059 1690 2944 
Q 2048 3654 2080 3840 
Q 2086 3898 2089 3994 
Q 2093 4090 2125 4090 
Q 2157 4090 2240 4045 
Q 2470 3917 2477 3776 
Q 2483 3731 2464 3686 
Q 1990 2707 1485 1830 
Q 1638 1862 1805 1888 
z
M 1485 627 
Q 2003 845 2304 989 
Q 2605 1133 2662 1139 
Q 2720 1146 2720 1126 
Q 2733 986 1840 442 
Q 947 -102 794 -115 
Q 749 -122 669 -45 
Q 589 32 531 128 
Q 474 224 467 275 
Q 467 301 525 307 
L 589 314 
Q 685 320 864 378 
L 1485 627 
z
M 2829 1882 
L 3213 2803 
L 2976 2790 
Q 2701 2752 2656 2803 
Q 2611 2854 2524 2966 
Q 2438 3078 2438 3148 
Q 2438 3219 2458 3219 
Q 2650 3174 2810 3174 
L 2848 3181 
L 3379 3219 
Q 3520 3603 3610 3866 
L 3168 3834 
L 3059 3834 
Q 2854 3834 2803 3885 
Q 2650 4019 2650 4192 
Q 2650 4224 2669 4224 
L 3002 4192 
L 3053 4192 
L 3731 4224 
Q 3891 4768 3910 4873 
Q 3930 4979 3926 5062 
Q 3923 5146 3926 5190 
Q 3930 5235 3994 5235 
Q 4058 5235 4160 5178 
Q 4371 5075 4352 4941 
L 4352 4928 
Q 4326 4877 4147 4250 
L 5114 4307 
Q 5171 4314 5244 4317 
Q 5318 4320 5372 4339 
Q 5427 4358 5500 4358 
Q 5574 4358 5692 4265 
Q 5811 4173 5811 4083 
Q 5811 3994 5651 3974 
L 4032 3891 
Q 3942 3622 3808 3258 
L 4301 3302 
L 4365 3302 
Q 4493 3302 4560 3222 
Q 4627 3142 4627 3085 
L 4595 2918 
L 4589 2176 
L 5037 2208 
Q 5101 2214 5174 2217 
Q 5248 2221 5296 2240 
Q 5344 2259 5421 2259 
Q 5498 2259 5613 2163 
Q 5728 2067 5728 1977 
Q 5728 1888 5568 1875 
L 4595 1811 
L 4576 1811 
L 4589 -45 
L 4602 -250 
Q 4602 -429 4422 -499 
Q 4358 -538 4307 -538 
Q 4256 -538 4080 -445 
Q 3904 -352 3731 -230 
Q 3277 77 3277 218 
Q 3277 256 3325 256 
Q 3373 256 3577 157 
Q 3782 58 4198 -38 
L 4179 1786 
L 3411 1722 
L 3270 1702 
Q 3213 1696 3168 1680 
Q 3123 1664 3052 1661 
Q 2982 1658 2896 1712 
Q 2810 1766 2829 1882 
z
M 2502 218 
Q 3194 934 3194 1274 
Q 3194 1402 3251 1402 
Q 3309 1402 3398 1338 
Q 3635 1190 3635 1075 
Q 3635 1043 3510 860 
Q 3386 678 3130 438 
Q 2874 198 2630 51 
Q 2387 -96 2348 -96 
Q 2310 -96 2310 -41 
Q 2310 13 2502 218 
z
M 4973 1062 
Q 4890 1146 4890 1213 
Q 4890 1280 4973 1344 
Q 5056 1408 5094 1408 
Q 5133 1408 5254 1305 
Q 5376 1203 5533 1046 
Q 5690 890 5830 736 
Q 5971 582 6057 483 
Q 6144 384 6144 301 
Q 6144 218 6045 131 
Q 5946 45 5898 45 
Q 5850 45 5818 73 
Q 5786 102 5542 432 
Q 5299 762 4973 1062 
z
M 4179 2144 
L 4186 2893 
L 3648 2848 
Q 3603 2701 3488 2438 
L 3341 2086 
L 4179 2144 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-4e60" d="M 4173 2182 
Q 4192 2010 2950 1264 
Q 1709 518 1523 499 
Q 1446 486 1299 589 
Q 883 896 1114 928 
Q 1485 998 1734 1100 
Q 1984 1203 2678 1516 
Q 3373 1830 3763 2054 
Q 4154 2278 4173 2182 
z
M 1811 3264 
Q 1670 3347 1670 3424 
Q 1670 3501 1750 3584 
Q 1830 3667 1891 3667 
Q 1952 3667 2262 3529 
Q 2573 3392 3405 2899 
Q 3558 2803 3558 2720 
Q 3558 2637 3475 2515 
Q 3392 2394 3312 2394 
Q 3232 2394 3030 2544 
Q 2829 2694 1811 3264 
z
M 5382 4294 
Q 5363 2342 5082 653 
Q 4947 -192 4873 -336 
Q 4800 -480 4713 -566 
Q 4627 -653 4528 -653 
Q 4429 -653 4352 -627 
Q 4275 -602 4067 -464 
Q 3859 -326 3066 358 
Q 2957 448 2957 525 
Q 2957 582 3027 582 
Q 3098 582 3293 460 
Q 3488 339 3901 112 
Q 4314 -115 4410 -115 
Q 4448 -90 4448 -70 
Q 4902 1549 4947 4320 
L 1478 4154 
Q 1376 4141 1286 4141 
Q 1126 4141 1011 4314 
Q 928 4422 928 4483 
Q 928 4544 963 4544 
Q 998 4544 1056 4528 
Q 1114 4512 1235 4512 
L 5018 4704 
L 5120 4710 
Q 5229 4710 5328 4630 
Q 5427 4550 5427 4480 
Q 5427 4410 5404 4368 
Q 5382 4326 5382 4294 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-9636" d="M 4090 4160 
Q 3392 3091 2726 2496 
Q 2483 2272 2348 2201 
Q 2214 2131 2185 2131 
Q 2157 2131 2157 2182 
Q 2157 2234 2266 2355 
Q 3328 3590 3789 4499 
Q 3994 4890 3994 5043 
L 3994 5082 
Q 3994 5190 4048 5190 
Q 4102 5190 4192 5139 
Q 4448 4992 4435 4845 
Q 4435 4826 4374 4675 
Q 4314 4525 4282 4486 
Q 4710 3898 5184 3427 
Q 5658 2957 6170 2522 
Q 6240 2451 6237 2412 
Q 6234 2374 6170 2317 
Q 5990 2176 5907 2176 
Q 5882 2182 5709 2323 
Q 5338 2630 4698 3373 
Q 4410 3706 4090 4160 
z
M 4582 2522 
Q 4582 2675 4515 2761 
Q 4448 2848 4448 2867 
Q 4448 2918 4547 2918 
Q 4646 2918 4777 2867 
Q 4909 2816 4944 2774 
Q 4979 2733 4979 2650 
L 4986 -474 
Q 4986 -640 4870 -640 
Q 4736 -640 4595 -474 
Q 4531 -403 4531 -358 
L 4531 -294 
Q 4531 -275 4556 -160 
Q 4582 -45 4582 122 
L 4582 2522 
z
M 3635 2234 
Q 3635 1446 3507 966 
Q 3341 262 2790 -179 
Q 2573 -358 2406 -441 
Q 2240 -525 2208 -525 
Q 2176 -525 2176 -483 
Q 2176 -442 2291 -320 
Q 2906 275 3091 928 
Q 3232 1408 3232 2182 
L 3232 2387 
Q 3232 2541 3181 2624 
Q 3130 2707 3130 2752 
Q 3130 2797 3213 2797 
Q 3469 2746 3552 2688 
Q 3635 2630 3635 2560 
L 3635 2234 
z
M 1146 4653 
L 2266 4736 
Q 2349 4736 2435 4672 
Q 2522 4608 2522 4550 
Q 2522 4493 2490 4457 
Q 2458 4422 2378 4265 
Q 2298 4109 2077 3725 
Q 1856 3341 1779 3225 
Q 1702 3110 1702 3084 
Q 1702 3059 1785 2931 
Q 1869 2803 1939 2688 
Q 2208 2157 2208 1600 
Q 2208 1293 2099 1069 
Q 2042 947 1920 947 
Q 1798 947 1664 1065 
Q 1530 1184 1398 1344 
Q 1267 1504 1184 1651 
Q 1101 1798 1101 1840 
Q 1101 1882 1155 1882 
Q 1210 1882 1325 1766 
Q 1542 1587 1651 1523 
Q 1760 1459 1773 1459 
Q 1818 1459 1818 1740 
Q 1818 2022 1731 2278 
Q 1645 2534 1533 2704 
Q 1421 2874 1414 2880 
Q 1338 2963 1338 3056 
Q 1338 3149 1411 3286 
Q 1485 3424 1693 3779 
Q 1901 4134 2003 4371 
L 1114 4314 
L 1056 -422 
Q 1056 -595 947 -595 
Q 819 -595 726 -505 
Q 634 -416 634 -371 
L 634 -307 
Q 634 -288 656 -189 
Q 678 -90 678 218 
L 730 4166 
Q 730 4467 678 4592 
Q 627 4717 627 4758 
Q 627 4800 704 4800 
Q 781 4800 1146 4653 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6bb5" d="M 4890 2714 
Q 4525 2771 4525 3136 
L 4525 3149 
L 4544 4320 
L 3680 4256 
L 3680 4128 
Q 3680 3738 3622 3446 
Q 3565 3155 3401 2908 
Q 3238 2662 3078 2537 
Q 2918 2413 2867 2413 
Q 2829 2413 2829 2461 
Q 2829 2509 2880 2579 
Q 3155 2995 3219 3353 
Q 3283 3712 3283 3865 
Q 3283 4019 3276 4240 
Q 3270 4461 3232 4553 
Q 3194 4646 3194 4688 
Q 3194 4730 3264 4730 
Q 3334 4730 3654 4595 
L 4602 4678 
Q 4634 4685 4659 4685 
Q 4685 4685 4710 4685 
Q 4838 4685 4902 4592 
Q 4966 4499 4966 4467 
L 4947 4365 
L 4902 3213 
L 4902 3194 
Q 4902 3130 4953 3107 
Q 5005 3085 5133 3085 
Q 5261 3085 5286 3091 
Q 5312 3098 5376 3107 
Q 5440 3117 5501 3139 
Q 5562 3162 5603 3162 
Q 5645 3162 5734 3104 
Q 5958 2950 5958 2842 
Q 5958 2765 5805 2752 
Q 5318 2656 4890 2714 
z
M 1056 704 
L 915 653 
Q 678 557 608 557 
Q 538 557 461 627 
Q 384 698 329 781 
Q 275 864 275 899 
Q 275 934 384 947 
Q 493 960 1056 1114 
L 1024 3898 
Q 1024 4019 969 4121 
Q 915 4224 915 4243 
Q 915 4301 1001 4301 
Q 1088 4301 1206 4256 
Q 1325 4211 1331 4205 
Q 1843 4422 2182 4691 
Q 2342 4819 2377 4908 
Q 2413 4998 2461 4998 
Q 2509 4998 2579 4931 
Q 2650 4864 2701 4777 
Q 2752 4691 2752 4633 
Q 2752 4576 2682 4525 
Q 2048 4115 1421 3910 
L 1427 3392 
L 2112 3443 
Q 2266 3456 2336 3488 
Q 2406 3520 2451 3520 
Q 2496 3520 2573 3465 
Q 2650 3411 2707 3340 
Q 2765 3270 2765 3226 
Q 2765 3142 2624 3130 
L 1427 3053 
L 1434 2483 
L 2112 2522 
Q 2246 2534 2320 2563 
Q 2394 2592 2442 2592 
Q 2490 2592 2566 2547 
Q 2765 2419 2765 2304 
Q 2765 2221 2624 2208 
L 1440 2131 
L 1446 1229 
Q 2074 1427 2771 1683 
Q 2867 1715 2940 1715 
Q 3014 1715 3014 1657 
Q 3014 1600 2880 1510 
Q 2426 1216 1446 851 
L 1459 -378 
Q 1459 -531 1338 -531 
Q 1235 -531 1123 -464 
Q 1011 -397 1011 -282 
L 1011 -250 
Q 1011 -230 1036 -96 
Q 1062 38 1062 224 
L 1056 704 
z
M 3232 2323 
L 5069 2413 
L 5139 2413 
Q 5267 2413 5340 2345 
Q 5414 2278 5414 2201 
Q 5414 2125 5139 1632 
Q 4864 1139 4422 710 
Q 5158 102 6067 -224 
Q 6195 -269 6195 -313 
Q 6195 -358 6124 -422 
Q 6054 -486 5971 -534 
Q 5888 -582 5859 -582 
Q 5830 -582 5673 -518 
Q 5517 -454 5274 -320 
Q 4666 0 4154 454 
Q 3443 -128 2701 -435 
Q 2400 -563 2301 -563 
Q 2202 -563 2202 -518 
Q 2202 -442 2387 -346 
Q 3206 96 3878 710 
Q 3462 1146 3308 1363 
Q 3155 1581 3155 1629 
Q 3155 1677 3228 1747 
Q 3302 1818 3363 1818 
Q 3424 1818 3494 1728 
Q 3776 1325 4147 960 
Q 4659 1498 4877 2054 
L 3475 1978 
L 3309 1971 
Q 3181 1971 3123 2016 
Q 2944 2182 2944 2323 
Q 2944 2355 2973 2355 
Q 3002 2355 3059 2339 
Q 3117 2323 3232 2323 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
      <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_7">
    <path d="M 591.508969 389.7475 
L 847.725015 389.7475 
L 847.725015 66.66 
L 591.508969 66.66 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_2"/>
   <g id="matplotlib.axis_3">
    <g id="xtick_18">
     <g id="line2d_37">
      <g>
       <use xlink:href="#m8b47929062" x="603.155153" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 0.25 -->
      <g transform="translate(592.405153 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_38">
      <g>
       <use xlink:href="#m8b47929062" x="661.386072" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 0.30 -->
      <g transform="translate(650.636072 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_39">
      <g>
       <use xlink:href="#m8b47929062" x="719.616992" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 0.35 -->
      <g transform="translate(708.866992 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_40">
      <g>
       <use xlink:href="#m8b47929062" x="777.847912" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 0.40 -->
      <g transform="translate(767.097912 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_41">
      <g>
       <use xlink:href="#m8b47929062" x="836.078832" y="389.7475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0.45 -->
      <g transform="translate(825.328832 403.817812) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_42">
      <g>
       <use xlink:href="#mf73567bf41" x="632.270612" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_43">
      <g>
       <use xlink:href="#mf73567bf41" x="690.501532" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_44">
      <g>
       <use xlink:href="#mf73567bf41" x="748.732452" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_45">
      <g>
       <use xlink:href="#mf73567bf41" x="806.963372" y="389.7475" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_27">
     <!-- 时间 (秒) -->
     <g transform="translate(699.366992 417.549062) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-65f6"/>
      <use xlink:href="#LXGWWenKai-Regular-95f4" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-79d2" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="369.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_14">
     <g id="line2d_46">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="384.528524" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- −5 -->
      <g transform="translate(575.008969 388.06368) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_47">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="343.564988" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- −4 -->
      <g transform="translate(575.008969 347.100144) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_48">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="302.601451" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_30">
      <!-- −3 -->
      <g transform="translate(575.008969 306.136608) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_49">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="261.637915" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_31">
      <!-- −2 -->
      <g transform="translate(575.008969 265.173071) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_50">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="220.674379" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_32">
      <!-- −1 -->
      <g transform="translate(575.008969 224.209535) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_51">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="179.710842" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 0 -->
      <g transform="translate(578.508969 183.245999) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_52">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="138.747306" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 1 -->
      <g transform="translate(578.508969 142.282462) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_53">
      <g>
       <use xlink:href="#mb6db3c8428" x="591.508969" y="97.78377" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 2 -->
      <g transform="translate(578.508969 101.318926) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_54">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="364.046756" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_55">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="323.083219" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_56">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="282.119683" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_57">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="241.156147" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_58">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="200.19261" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_59">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="159.229074" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_28">
     <g id="line2d_60">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="118.265538" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_29">
     <g id="line2d_61">
      <g>
       <use xlink:href="#mf25b24706b" x="591.508969" y="77.302001" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_36">
     <!-- 振幅 (μV) -->
     <g transform="translate(569.108969 249.363906) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-632f"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="234.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="269.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="326.199936"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="388.199921"/>
     </g>
    </g>
    <g id="text_37">
     <!-- 1e−6 -->
     <g transform="translate(591.508969 63.66) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="line2d_62">
    <path d="M 603.155153 366.13717 
L 605.484389 345.86303 
L 607.813626 319.442303 
L 610.142863 306.522847 
L 612.4721 316.281716 
L 614.801337 341.783444 
L 617.130573 366.115043 
L 619.45981 375.061705 
L 621.789047 366.550695 
L 624.118284 350.127434 
L 626.447521 337.718766 
L 628.776757 333.543404 
L 631.105994 331.196415 
L 633.435231 319.812687 
L 635.764468 293.865243 
L 638.093704 258.436242 
L 640.422941 225.726572 
L 642.752178 205.79634 
L 645.081415 199.159174 
L 647.410652 197.220422 
L 649.739888 190.093513 
L 652.069125 175.333231 
L 654.398362 160.425944 
L 656.727599 156.950805 
L 659.056836 171.030012 
L 661.386072 197.63252 
L 663.715309 223.218661 
L 666.044546 234.683306 
L 668.373783 227.803596 
L 670.70302 209.072397 
L 673.032256 190.123168 
L 675.361493 179.513948 
L 677.69073 178.095343 
L 680.019967 180.711525 
L 682.349203 181.742731 
L 684.67844 179.365852 
L 687.007677 175.297983 
L 689.336914 171.21466 
L 691.666151 165.990957 
L 693.995387 156.845128 
L 696.324624 143.452673 
L 698.653861 130.844413 
L 700.983098 127.478646 
L 703.312335 139.006646 
L 705.641571 162.456956 
L 707.970808 186.072517 
L 710.300045 196.038454 
L 712.629282 185.814158 
L 714.958519 161.204708 
L 717.287755 136.980252 
L 719.616992 126.972954 
L 721.946229 134.548343 
L 724.275466 150.302652 
L 726.604702 158.729928 
L 728.933939 149.193871 
L 731.263176 123.559525 
L 733.592413 95.355181 
L 735.92165 81.345795 
L 738.250886 91.436498 
L 740.580123 123.424649 
L 742.90936 165.248056 
L 745.238597 202.225624 
L 747.567834 224.090148 
L 749.89707 227.89981 
L 752.226307 216.591446 
L 754.555544 195.870103 
L 756.884781 172.249308 
L 759.214017 152.812188 
L 761.543254 145.013754 
L 763.872491 154.707949 
L 766.201728 182.636735 
L 768.530965 221.951428 
L 770.860201 259.630244 
L 773.189438 282.291461 
L 775.518675 283.4694 
L 777.847912 267.658719 
L 780.177149 247.97106 
L 782.506385 238.333602 
L 784.835622 244.996129 
L 787.164859 262.901208 
L 789.494096 279.442722 
L 791.823333 283.211963 
L 794.152569 271.887963 
L 796.481806 253.886849 
L 798.811043 242.561951 
L 801.14028 246.840369 
L 803.469516 264.704138 
L 805.798753 284.095128 
L 808.12799 290.815237 
L 810.457227 278.202999 
L 812.786464 252.069568 
L 815.1157 227.500422 
L 817.444937 219.559415 
L 819.774174 233.979381 
L 822.103411 263.848518 
L 824.432648 294.208317 
L 826.761884 311.238536 
L 829.091121 309.90518 
L 831.420358 295.398402 
L 833.749595 278.179401 
L 836.078832 266.723736 
" clip-path="url(#p9b9df197bb)" style="fill: none; stroke: #e63946; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_63">
    <path d="M 591.508969 179.710842 
L 847.725015 179.710842 
" clip-path="url(#p9b9df197bb)" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linecap: square"/>
   </g>
   <g id="patch_8">
    <path d="M 591.508969 389.7475 
L 591.508969 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 847.725015 389.7475 
L 847.725015 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 591.508969 389.7475 
L 847.725015 389.7475 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 591.508969 66.66 
L 847.725015 66.66 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_38">
    <!-- Fz通道在250-450ms的HEP成分 -->
    <g transform="translate(648.097461 60.66) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-5728" d="M 2874 5030 
Q 2874 5107 2867 5171 
Q 2861 5235 2912 5235 
Q 2938 5235 3040 5197 
Q 3334 5088 3334 4954 
Q 3334 4934 3232 4649 
Q 3130 4365 2944 3987 
L 4979 4115 
Q 5197 4134 5261 4153 
Q 5325 4173 5363 4173 
Q 5402 4173 5491 4122 
Q 5734 3994 5734 3872 
Q 5734 3789 5613 3776 
L 2752 3603 
Q 2394 2925 1914 2298 
L 1894 -416 
Q 1894 -582 1786 -582 
Q 1709 -582 1587 -515 
Q 1466 -448 1466 -333 
Q 1504 -26 1504 218 
L 1523 1837 
Q 1261 1542 947 1286 
Q 378 819 218 819 
Q 179 819 179 864 
Q 179 909 403 1101 
Q 627 1293 937 1635 
Q 1248 1978 1530 2362 
L 1536 2605 
Q 1536 2701 1485 2800 
Q 1434 2899 1434 2937 
Q 1434 2976 1485 2976 
Q 1536 2976 1587 2970 
L 1696 2944 
Q 1830 2931 1888 2874 
Q 2150 3290 2304 3578 
L 1376 3520 
Q 1261 3507 1158 3507 
Q 1056 3507 957 3555 
Q 858 3603 768 3827 
Q 762 3840 762 3866 
Q 762 3910 813 3910 
Q 973 3878 1146 3878 
L 1229 3878 
L 2496 3955 
Q 2874 4806 2874 5030 
z
M 5376 384 
Q 5446 390 5491 393 
Q 5536 397 5722 429 
Q 5830 429 6003 243 
Q 6074 173 6074 122 
Q 6074 32 5920 19 
L 2464 -115 
L 2355 -115 
Q 2157 -115 2099 -38 
Q 1946 141 1946 282 
Q 1946 314 1968 314 
Q 1990 314 2073 282 
Q 2157 250 2304 250 
L 2336 250 
L 3597 307 
L 3610 1728 
L 2835 1683 
L 2714 1677 
Q 2541 1677 2486 1737 
Q 2432 1798 2374 1904 
Q 2317 2010 2317 2054 
Q 2317 2099 2339 2099 
Q 2362 2099 2445 2067 
Q 2528 2035 2675 2035 
L 2707 2035 
L 3610 2099 
L 3616 3104 
Q 3616 3290 3539 3418 
Q 3526 3437 3526 3465 
Q 3526 3494 3596 3494 
Q 3667 3494 3808 3440 
Q 3949 3386 3984 3344 
Q 4019 3302 4019 3213 
L 4013 2131 
L 4730 2182 
Q 4800 2189 4877 2192 
Q 4954 2195 5002 2211 
Q 5050 2227 5088 2227 
Q 5126 2227 5206 2176 
Q 5286 2125 5353 2051 
Q 5421 1978 5421 1926 
Q 5421 1843 5274 1830 
L 4006 1754 
L 3994 320 
L 5376 384 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2d" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6d" d="M 4186 109 
L 4211 672 
L 4211 1875 
Q 4211 2259 4083 2492 
Q 3955 2726 3674 2726 
Q 3482 2726 3280 2585 
Q 3078 2445 2940 2214 
Q 2803 1984 2803 1715 
L 2803 672 
L 2835 -6 
Q 2835 -70 2720 -70 
Q 2605 -70 2489 -22 
Q 2374 26 2374 109 
L 2400 672 
L 2400 1875 
Q 2400 2726 1926 2726 
Q 1594 2726 1344 2432 
Q 1043 2061 1024 1344 
Q 1011 998 1005 672 
L 1037 -6 
Q 1037 -70 921 -70 
Q 806 -70 688 -22 
Q 570 26 570 109 
L 582 672 
L 582 2278 
L 563 3021 
Q 563 3085 678 3085 
Q 794 3085 912 3037 
Q 1030 2989 1030 2915 
Q 1030 2842 1027 2755 
Q 1024 2669 1024 2541 
Q 1152 2803 1398 2956 
Q 1645 3110 1965 3110 
Q 2285 3110 2470 2931 
Q 2656 2752 2714 2515 
Q 2880 2810 3149 2960 
Q 3418 3110 3722 3110 
Q 4026 3110 4234 2934 
Q 4442 2758 4538 2480 
Q 4634 2202 4634 1901 
L 4634 672 
L 4653 -6 
Q 4653 -70 4537 -70 
Q 4422 -70 4304 -22 
Q 4186 26 4186 109 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-73" d="M 1248 1549 
Q 531 1715 531 2278 
Q 531 2528 691 2714 
Q 1030 3110 1689 3110 
Q 2349 3110 2726 2707 
Q 2739 2694 2739 2630 
Q 2739 2566 2672 2464 
Q 2605 2362 2550 2362 
Q 2496 2362 2432 2445 
Q 2227 2758 1702 2758 
Q 1331 2758 1139 2614 
Q 947 2470 947 2304 
Q 947 2138 1033 2048 
Q 1120 1958 1382 1894 
L 1997 1747 
Q 2413 1651 2653 1433 
Q 2893 1216 2893 819 
Q 2893 563 2742 352 
Q 2592 141 2317 13 
Q 2042 -115 1667 -115 
Q 1293 -115 1046 -25 
Q 800 64 653 185 
Q 506 307 442 409 
Q 378 512 378 576 
Q 378 640 470 745 
Q 563 851 620 851 
Q 678 851 698 806 
Q 749 678 845 550 
Q 1056 256 1664 256 
Q 2054 256 2262 422 
Q 2470 589 2470 809 
Q 2470 1030 2355 1171 
Q 2240 1312 1888 1395 
L 1248 1549 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-48" d="M 3456 115 
L 3469 685 
L 3469 2317 
L 3430 2310 
L 2918 2330 
L 1165 2227 
L 1126 2227 
L 1126 672 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 678 
L 678 2176 
Q 659 2170 634 2170 
L 627 2170 
Q 544 2170 483 2288 
Q 422 2406 422 2508 
Q 422 2611 493 2611 
L 499 2611 
L 678 2605 
L 678 3770 
L 659 4448 
Q 659 4512 780 4512 
Q 902 4512 1020 4464 
Q 1139 4416 1139 4333 
L 1126 3763 
L 1126 2605 
L 1158 2605 
L 2765 2701 
Q 2912 2714 3065 2726 
Q 3219 2739 3296 2752 
L 3315 2752 
Q 3411 2752 3469 2598 
L 3469 3770 
L 3450 4448 
Q 3450 4512 3571 4512 
Q 3693 4512 3811 4464 
Q 3930 4416 3930 4333 
L 3917 3763 
L 3917 678 
L 3936 0 
Q 3936 -64 3817 -64 
Q 3699 -64 3577 -16 
Q 3456 32 3456 115 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-45" d="M 3021 410 
Q 3264 410 3552 429 
L 3558 429 
Q 3642 429 3702 310 
Q 3763 192 3763 89 
Q 3763 -13 3693 -13 
L 3686 -13 
Q 3533 6 3130 6 
L 3027 6 
L 1248 -19 
Q 1146 -19 1072 -41 
Q 998 -64 905 -64 
Q 813 -64 723 48 
Q 634 160 634 237 
L 678 538 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4233 
Q 3565 4134 3482 4134 
L 3085 4134 
Q 2912 4134 2822 4128 
L 1114 4013 
L 1114 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1114 2202 
L 1114 378 
L 3021 410 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-50" d="M 2035 4147 
Q 1581 4147 1126 4019 
L 1126 2317 
Q 1165 2342 1210 2342 
Q 2278 2413 2748 2729 
Q 3219 3046 3219 3462 
Q 3219 4147 2035 4147 
z
M 2048 4550 
Q 3680 4550 3680 3469 
Q 3680 2938 3328 2605 
Q 2733 2048 1376 2048 
L 1280 2048 
Q 1190 2048 1126 2080 
L 1126 736 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 742 
L 678 3955 
L 672 3955 
Q 576 3955 480 4070 
Q 384 4186 384 4259 
Q 384 4333 442 4339 
Q 1504 4544 1689 4547 
Q 1875 4550 2048 4550 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6210" d="M 1530 3379 
Q 1530 3098 1498 2598 
L 2579 2675 
Q 2611 2682 2630 2682 
L 2720 2682 
Q 2790 2682 2864 2611 
Q 2938 2541 2938 2489 
Q 2938 2438 2922 2403 
Q 2906 2368 2896 2240 
Q 2886 2112 2816 1561 
Q 2746 1011 2611 608 
Q 2566 474 2464 381 
Q 2362 288 2262 288 
Q 2163 288 2029 429 
Q 1722 749 1581 957 
Q 1440 1165 1440 1209 
Q 1440 1254 1488 1254 
Q 1536 1254 1731 1104 
Q 1926 954 2195 787 
L 2202 781 
L 2208 781 
Q 2259 781 2364 1283 
Q 2470 1786 2509 2330 
L 1472 2259 
Q 1395 1408 1200 902 
Q 1005 397 816 80 
Q 627 -237 473 -416 
Q 320 -595 265 -595 
Q 211 -595 211 -544 
Q 211 -493 256 -410 
Q 960 794 1069 2234 
Q 1114 2842 1114 3222 
Q 1114 3603 1066 3721 
Q 1018 3840 1018 3878 
Q 1018 3917 1088 3917 
Q 1158 3917 1555 3738 
L 3174 3840 
Q 3066 4378 3027 4669 
Q 2989 4960 2960 5011 
Q 2931 5062 2861 5133 
Q 2829 5178 2829 5190 
Q 2829 5248 2931 5248 
Q 3034 5248 3165 5213 
Q 3296 5178 3331 5133 
Q 3366 5088 3417 4701 
Q 3469 4314 3565 3866 
L 4851 3949 
Q 5050 3968 5114 3990 
Q 5178 4013 5226 4013 
Q 5274 4013 5350 3955 
Q 5555 3821 5555 3712 
Q 5555 3642 5414 3629 
L 3642 3514 
Q 3846 2586 4224 1760 
Q 4224 1766 4349 1942 
Q 4474 2118 4608 2381 
Q 4819 2752 4819 2925 
Q 4819 2976 4813 2995 
L 4813 3014 
Q 4813 3091 4873 3091 
Q 4934 3091 5030 3034 
Q 5280 2893 5280 2790 
Q 5280 2714 4989 2221 
Q 4698 1728 4416 1370 
Q 4928 422 5504 -70 
Q 5523 -90 5542 -90 
Q 5562 -90 5574 -58 
Q 5786 486 5907 1120 
Q 5946 1306 6016 1306 
Q 6086 1306 6086 1133 
L 6086 1101 
Q 6074 346 5946 -294 
Q 5888 -608 5670 -608 
Q 5408 -608 5222 -435 
Q 5037 -262 4877 -64 
Q 4435 512 4128 1056 
Q 3667 531 3104 112 
Q 2541 -307 2400 -307 
Q 2349 -307 2349 -269 
Q 2349 -211 2451 -134 
Q 3251 506 3949 1408 
Q 3885 1523 3670 2035 
Q 3456 2547 3251 3488 
L 1530 3379 
z
M 3814 4698 
Q 3725 4749 3725 4797 
Q 3725 4845 3763 4928 
Q 3802 5011 3882 5011 
Q 3962 5011 4218 4873 
Q 4474 4736 4774 4518 
Q 4883 4442 4883 4371 
Q 4883 4358 4857 4291 
Q 4832 4224 4790 4166 
Q 4749 4109 4694 4109 
Q 4640 4109 4422 4294 
Q 4205 4480 3814 4698 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-46"/>
     <use xlink:href="#LXGWWenKai-Regular-7a" x="58.199997"/>
     <use xlink:href="#LXGWWenKai-Regular-901a" x="107.199982"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="207.199966"/>
     <use xlink:href="#LXGWWenKai-Regular-5728" x="307.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="407.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="467.199921"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="527.199905"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="587.19989"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="622.199875"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="682.19986"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="742.199844"/>
     <use xlink:href="#LXGWWenKai-Regular-6d" x="802.199829"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="883.79982"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="934.799805"/>
     <use xlink:href="#LXGWWenKai-Regular-48" x="1034.799789"/>
     <use xlink:href="#LXGWWenKai-Regular-45" x="1104.799774"/>
     <use xlink:href="#LXGWWenKai-Regular-50" x="1168.599762"/>
     <use xlink:href="#LXGWWenKai-Regular-6210" x="1230.39975"/>
     <use xlink:href="#LXGWWenKai-Regular-5206" x="1330.399734"/>
    </g>
   </g>
  </g>
  <g id="text_39">
   <!-- Fz导联的心跳诱发电位及250-450ms处的成分 -->
   <g transform="translate(305.954063 16.355625) scale(0.12 -0.12)">
    <defs>
     <path id="LXGWWenKai-Regular-5bfc" d="M 4134 3776 
L 4256 4486 
L 1818 4346 
L 1811 3667 
L 4134 3776 
z
M 2682 250 
Q 2598 147 2544 144 
Q 2490 141 2426 198 
Q 2086 550 1677 826 
Q 1606 877 1603 925 
Q 1600 973 1677 1065 
Q 1754 1158 1802 1161 
Q 1850 1165 2102 1008 
Q 2355 851 2682 557 
Q 2835 448 2682 250 
z
M 3840 2048 
Q 3840 2176 3802 2285 
Q 2810 2240 2016 2304 
Q 1709 2323 1545 2486 
Q 1382 2650 1382 2918 
L 1382 2931 
L 1402 4365 
Q 1402 4589 1347 4685 
Q 1293 4781 1293 4819 
Q 1293 4858 1379 4858 
Q 1466 4858 1824 4710 
L 4410 4851 
Q 4666 4851 4723 4691 
Q 4742 4634 4742 4611 
Q 4742 4589 4723 4563 
Q 4704 4538 4698 4512 
L 4525 3757 
Q 4678 3578 4678 3507 
Q 4678 3437 4493 3424 
L 1811 3302 
L 1805 3008 
Q 1805 2707 2074 2688 
Q 3014 2611 4147 2682 
Q 4474 2701 4922 2797 
Q 5107 2816 5180 2902 
Q 5254 2989 5308 3104 
Q 5363 3219 5420 3520 
Q 5478 3821 5564 3821 
Q 5651 3821 5651 3590 
Q 5651 3181 5632 2931 
Q 5613 2682 5472 2550 
Q 5331 2419 5037 2400 
Q 4602 2330 4262 2304 
Q 4269 2285 4269 2259 
L 4269 1760 
L 5350 1805 
Q 5530 1818 5590 1840 
Q 5651 1862 5721 1862 
Q 5792 1862 5872 1801 
Q 5952 1741 6000 1670 
Q 6048 1600 6048 1562 
Q 6048 1485 5888 1466 
L 4262 1395 
L 4256 -173 
L 4275 -422 
Q 4275 -582 4169 -659 
Q 4064 -736 3968 -736 
Q 3699 -736 3149 -282 
Q 2842 -13 2842 48 
Q 2842 109 2896 109 
Q 2950 109 3228 -22 
Q 3507 -154 3827 -211 
L 3834 1376 
L 883 1248 
L 678 1242 
Q 563 1242 531 1274 
Q 346 1472 346 1600 
Q 346 1645 397 1645 
Q 422 1645 473 1625 
Q 525 1606 678 1606 
L 749 1606 
L 3840 1741 
L 3840 2048 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-8054" d="M 2394 -397 
Q 2394 -544 2278 -544 
Q 2144 -544 2051 -441 
Q 1958 -339 1958 -284 
Q 1958 -230 1984 -137 
Q 2010 -45 2010 282 
L 2010 1114 
Q 1427 813 678 582 
Q 531 538 474 538 
Q 358 538 192 774 
Q 186 794 176 810 
Q 166 826 166 838 
Q 166 877 240 883 
Q 314 890 819 1018 
L 832 4250 
Q 704 4237 678 4237 
L 627 4237 
Q 403 4237 314 4448 
Q 282 4525 282 4553 
Q 282 4582 326 4582 
L 544 4557 
L 2374 4666 
Q 2470 4672 2544 4701 
Q 2618 4730 2669 4730 
Q 2720 4730 2793 4669 
Q 2867 4608 2915 4541 
Q 2963 4474 2963 4454 
Q 2963 4384 2784 4365 
L 2387 4339 
L 2394 -397 
z
M 2022 4314 
L 1203 4269 
L 1197 3501 
L 2022 3539 
L 2022 4314 
z
M 2016 3226 
L 1197 3187 
L 1197 2400 
L 2016 2438 
L 2016 3226 
z
M 2016 2118 
L 1197 2086 
L 1190 1133 
Q 1350 1190 1580 1257 
Q 1811 1325 2016 1408 
L 2016 2118 
z
M 4928 4960 
Q 4966 4960 5062 4912 
Q 5158 4864 5241 4790 
Q 5325 4717 5325 4669 
Q 5325 4621 5312 4582 
Q 5069 4090 4634 3494 
L 5069 3520 
Q 5120 3526 5171 3529 
Q 5222 3533 5276 3552 
Q 5331 3571 5369 3571 
Q 5408 3571 5485 3520 
Q 5677 3392 5677 3290 
Q 5677 3206 5517 3194 
L 4467 3130 
Q 4448 2630 4390 2202 
L 5382 2246 
Q 5587 2266 5625 2278 
Q 5664 2291 5699 2291 
Q 5734 2291 5811 2253 
Q 6016 2150 6016 2010 
Q 6016 1920 5862 1907 
L 4602 1850 
Q 5203 563 6246 -179 
Q 6323 -237 6323 -272 
Q 6323 -307 6220 -419 
Q 6118 -531 6010 -531 
Q 5965 -531 5779 -374 
Q 5594 -218 5338 70 
Q 4723 762 4301 1690 
Q 4154 986 3712 429 
Q 3270 -128 2765 -390 
Q 2560 -493 2493 -493 
Q 2426 -493 2426 -454 
Q 2426 -390 2573 -282 
Q 3277 262 3622 966 
Q 3859 1434 3923 1818 
L 3104 1779 
L 3021 1779 
Q 2784 1779 2752 1837 
Q 2618 2003 2618 2086 
Q 2618 2170 2656 2170 
Q 2675 2170 2729 2150 
Q 2784 2131 2912 2131 
L 2970 2131 
L 3987 2182 
Q 4038 2560 4058 3104 
L 3437 3066 
Q 3398 3059 3360 3059 
L 3296 3059 
Q 3021 3059 2957 3309 
Q 2931 3411 2931 3418 
Q 2931 3456 2960 3456 
Q 2989 3456 3062 3437 
Q 3136 3418 3264 3418 
L 3315 3418 
L 4250 3475 
Q 4595 4026 4739 4365 
Q 4883 4704 4883 4755 
Q 4883 4806 4876 4883 
Q 4870 4960 4928 4960 
z
M 3763 3648 
Q 3520 4058 3168 4493 
Q 3104 4576 3104 4624 
Q 3104 4672 3197 4729 
Q 3290 4787 3328 4787 
Q 3366 4787 3462 4694 
Q 3558 4602 3676 4458 
Q 3795 4314 3904 4170 
Q 4013 4026 4083 3914 
Q 4154 3802 4154 3750 
Q 4154 3699 4054 3622 
Q 3955 3546 3891 3546 
Q 3827 3546 3763 3648 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-53ca" d="M 1382 4506 
L 1594 4512 
L 4211 4685 
L 4243 4685 
Q 4371 4685 4470 4614 
Q 4570 4544 4570 4483 
Q 4570 4422 4534 4374 
Q 4499 4326 4467 4262 
L 3930 3110 
L 4890 3187 
Q 4915 3194 4941 3194 
L 4979 3194 
Q 5178 3194 5261 3034 
Q 5286 2976 5286 2941 
Q 5286 2906 5248 2861 
Q 5210 2816 5146 2672 
Q 5082 2528 4902 2227 
Q 4486 1536 3987 1043 
Q 4902 320 5978 -115 
Q 6093 -160 6093 -205 
Q 6093 -224 6042 -294 
Q 5901 -499 5782 -499 
Q 5664 -499 5190 -230 
Q 4243 307 3686 787 
Q 2854 90 2003 -256 
Q 1389 -506 1178 -506 
Q 1114 -506 1114 -461 
Q 1114 -397 1280 -320 
Q 2074 51 2541 377 
Q 3008 704 3392 1043 
Q 2720 1677 2227 2406 
Q 2182 2291 2147 2185 
Q 2112 2080 1984 1808 
Q 1856 1536 1590 1171 
Q 1325 806 1065 540 
Q 806 275 617 131 
Q 429 -13 384 -13 
Q 339 -13 339 32 
Q 339 77 461 224 
Q 1286 1190 1692 2076 
Q 2099 2963 2298 4205 
L 1632 4160 
Q 1574 4154 1523 4147 
Q 1472 4141 1389 4141 
Q 1306 4141 1178 4265 
Q 1050 4390 1050 4499 
Q 1050 4525 1088 4525 
L 1114 4525 
Q 1261 4506 1382 4506 
z
M 3955 2758 
Q 3891 2752 3830 2729 
Q 3770 2707 3677 2707 
Q 3584 2707 3520 2806 
Q 3456 2906 3456 2947 
Q 3456 2989 3482 3046 
L 4038 4320 
L 2739 4230 
Q 2605 3450 2400 2835 
Q 3034 1869 3674 1293 
Q 4102 1722 4467 2330 
Q 4634 2598 4736 2822 
L 3955 2758 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5904" d="M 4064 4666 
Q 4064 4845 3974 4954 
Q 3955 4992 3955 5011 
Q 3955 5075 4038 5075 
Q 4122 5075 4253 5020 
Q 4384 4966 4416 4934 
Q 4448 4902 4448 4832 
L 4442 3405 
Q 4480 3443 4518 3443 
Q 4691 3443 5875 2650 
Q 5997 2566 5997 2489 
Q 5997 2413 5926 2323 
Q 5856 2234 5801 2234 
Q 5747 2234 5670 2291 
Q 5050 2771 4442 3110 
L 4429 864 
Q 4429 710 4326 710 
Q 4198 710 4099 784 
Q 4000 858 4000 912 
Q 4000 966 4022 1078 
Q 4045 1190 4045 1376 
L 4064 4666 
z
M 2982 3872 
Q 3034 3872 3066 3885 
Q 3155 3885 3283 3817 
Q 3411 3750 3411 3667 
Q 3411 3584 3376 3529 
Q 3341 3475 3334 3450 
Q 3085 2406 2630 1619 
L 2490 1402 
Q 3066 678 3981 371 
Q 4781 96 6221 13 
Q 6336 6 6336 -64 
Q 6336 -96 6272 -173 
Q 6112 -397 5952 -397 
L 5869 -390 
Q 4864 -301 4102 -77 
Q 2982 256 2278 1088 
Q 1837 499 1210 6 
Q 915 -230 697 -358 
Q 480 -486 441 -486 
Q 403 -486 403 -441 
Q 403 -397 544 -269 
Q 1466 525 2029 1389 
Q 1747 1862 1622 2131 
Q 1498 2400 1370 2726 
Q 1075 2227 844 2012 
Q 614 1798 579 1798 
Q 544 1798 544 1840 
Q 544 1882 745 2202 
Q 947 2522 1148 2982 
Q 1350 3443 1510 3987 
Q 1670 4531 1670 4662 
Q 1670 4794 1651 4842 
Q 1632 4890 1632 4902 
Q 1632 4966 1709 4966 
Q 1734 4966 1830 4928 
Q 2118 4819 2118 4666 
L 2118 4640 
Q 2029 4243 1862 3770 
L 2982 3872 
z
M 1728 3437 
Q 1696 3341 1651 3254 
Q 1606 3168 1568 3085 
Q 1805 2496 2234 1728 
L 2394 2016 
Q 2758 2771 2931 3546 
L 1728 3437 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#LXGWWenKai-Regular-46"/>
    <use xlink:href="#LXGWWenKai-Regular-7a" x="58.199997"/>
    <use xlink:href="#LXGWWenKai-Regular-5bfc" x="107.199982"/>
    <use xlink:href="#LXGWWenKai-Regular-8054" x="207.199966"/>
    <use xlink:href="#LXGWWenKai-Regular-7684" x="307.199951"/>
    <use xlink:href="#LXGWWenKai-Regular-5fc3" x="407.199936"/>
    <use xlink:href="#LXGWWenKai-Regular-8df3" x="507.199921"/>
    <use xlink:href="#LXGWWenKai-Regular-8bf1" x="607.199905"/>
    <use xlink:href="#LXGWWenKai-Regular-53d1" x="707.19989"/>
    <use xlink:href="#LXGWWenKai-Regular-7535" x="807.199875"/>
    <use xlink:href="#LXGWWenKai-Regular-4f4d" x="907.19986"/>
    <use xlink:href="#LXGWWenKai-Regular-53ca" x="1007.199844"/>
    <use xlink:href="#LXGWWenKai-Regular-32" x="1107.199829"/>
    <use xlink:href="#LXGWWenKai-Regular-35" x="1167.199814"/>
    <use xlink:href="#LXGWWenKai-Regular-30" x="1227.199799"/>
    <use xlink:href="#LXGWWenKai-Regular-2d" x="1287.199783"/>
    <use xlink:href="#LXGWWenKai-Regular-34" x="1322.199768"/>
    <use xlink:href="#LXGWWenKai-Regular-35" x="1382.199753"/>
    <use xlink:href="#LXGWWenKai-Regular-30" x="1442.199738"/>
    <use xlink:href="#LXGWWenKai-Regular-6d" x="1502.199722"/>
    <use xlink:href="#LXGWWenKai-Regular-73" x="1583.799713"/>
    <use xlink:href="#LXGWWenKai-Regular-5904" x="1634.799698"/>
    <use xlink:href="#LXGWWenKai-Regular-7684" x="1734.799683"/>
    <use xlink:href="#LXGWWenKai-Regular-6210" x="1834.799667"/>
    <use xlink:href="#LXGWWenKai-Regular-5206" x="1934.799652"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p55e789c2b0">
   <rect x="37.559375" y="66.66" width="512.432094" height="323.0875"/>
  </clipPath>
  <clipPath id="p9b9df197bb">
   <rect x="591.508969" y="66.66" width="256.216047" height="323.0875"/>
  </clipPath>
 </defs>
</svg>

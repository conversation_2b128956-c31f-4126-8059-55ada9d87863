#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据加载模块

功能：
- 测试数据加载模块的各个功能
- 验证数据加载的正确性
- 输出测试结果

作者：AI助手
日期：2024年
"""

import os
import sys
import logging
from datetime import datetime
from data_loader import load_hep_data, DataLoadError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_data_loader_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-DataLoader")

def test_load_hep_data(data_dir):
    """
    测试加载HEP数据
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    bool: 测试结果
    """
    logger.info(f"测试加载HEP数据: {data_dir}")
    
    try:
        # 加载数据
        stages_data = load_hep_data(data_dir)
        
        # 验证返回的数据结构
        if not isinstance(stages_data, dict):
            logger.error(f"返回的数据类型不正确: {type(stages_data)}")
            return False
        
        if not stages_data:
            logger.error("返回的数据为空")
            return False
        
        # 验证各阶段数据
        for stage, data in stages_data.items():
            logger.info(f"验证阶段 {stage} 的数据")
            
            # 验证必要的字段
            required_fields = ['eeg', 'ecg', 'eeg_channels', 'ecg_channels', 'times', 'r_peaks', 'sfreq']
            for field in required_fields:
                if field not in data:
                    logger.error(f"阶段 {stage} 的数据缺少必要字段: {field}")
                    return False
            
            # 验证数据维度
            if len(data['eeg'].shape) != 3:
                logger.error(f"阶段 {stage} 的EEG数据维度不正确: {data['eeg'].shape}")
                return False
            
            if len(data['ecg'].shape) != 3:
                logger.error(f"阶段 {stage} 的ECG数据维度不正确: {data['ecg'].shape}")
                return False
            
            # 验证通道数量
            if data['eeg'].shape[1] != len(data['eeg_channels']):
                logger.error(f"阶段 {stage} 的EEG通道数量与通道名称数量不匹配: {data['eeg'].shape[1]} vs {len(data['eeg_channels'])}")
                return False
            
            if data['ecg'].shape[1] != len(data['ecg_channels']):
                logger.error(f"阶段 {stage} 的ECG通道数量与通道名称数量不匹配: {data['ecg'].shape[1]} vs {len(data['ecg_channels'])}")
                return False
            
            # 验证R波位置
            if len(data['r_peaks']) != data['eeg'].shape[0]:
                logger.error(f"阶段 {stage} 的R波位置数量与样本数量不匹配: {len(data['r_peaks'])} vs {data['eeg'].shape[0]}")
                return False
            
            # 打印数据基本信息
            logger.info(f"  - 样本数: {data['eeg'].shape[0]}")
            logger.info(f"  - EEG通道数: {data['eeg'].shape[1]}")
            logger.info(f"  - ECG通道数: {data['ecg'].shape[1]}")
            logger.info(f"  - 时间点数: {data['eeg'].shape[2]}")
            logger.info(f"  - 采样率: {data['sfreq']} Hz")
            logger.info(f"  - EEG通道: {data['eeg_channels'][:5]}...")
            logger.info(f"  - ECG通道: {data['ecg_channels']}")
        
        logger.info("测试成功: 所有数据加载正确")
        return True
    
    except DataLoadError as e:
        logger.error(f"数据加载失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def test_invalid_data_dir():
    """
    测试无效的数据目录
    
    返回:
    bool: 测试结果
    """
    logger.info("测试无效的数据目录")
    
    try:
        # 测试不存在的目录
        invalid_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\non_existent_dir"
        load_hep_data(invalid_dir)
        
        # 如果没有抛出异常，则测试失败
        logger.error("测试失败: 应该抛出异常但没有")
        return False
    
    except DataLoadError:
        # 应该抛出DataLoadError异常
        logger.info("测试成功: 正确抛出异常")
        return True
    except Exception as e:
        logger.error(f"测试失败: 抛出了意外的异常: {type(e)}, {str(e)}")
        return False

def run_all_tests():
    """
    运行所有测试
    """
    logger.info("开始运行所有测试...")
    
    # 测试有效的数据目录
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    test1_result = test_load_hep_data(data_dir)
    
    # 测试无效的数据目录
    test2_result = test_invalid_data_dir()
    
    # 输出总结
    logger.info("测试结果总结:")
    logger.info(f"  - 测试加载HEP数据: {'通过' if test1_result else '失败'}")
    logger.info(f"  - 测试无效的数据目录: {'通过' if test2_result else '失败'}")
    
    # 总体结果
    overall_result = test1_result and test2_result
    logger.info(f"总体测试结果: {'通过' if overall_result else '失败'}")
    
    return overall_result

if __name__ == "__main__":
    run_all_tests()

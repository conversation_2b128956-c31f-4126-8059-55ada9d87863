#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CCM PyEDM工具模块

该模块提供了使用PyEDM库进行收敛交叉映射（CCM）分析的工具函数。

主要功能：
1. 计算两个时间序列之间的CCM关系
2. 可视化CCM收敛曲线
3. 提供CCM结果的统计分析

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pyEDM import CCM
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message="invalid value encountered in true_divide")
warnings.filterwarnings("ignore", category=RuntimeWarning, message="Mean of empty slice")

def compute_ccm(x, y, embed_dim=3, tau=1, lib_sizes=None, sample=100, random=True, replacement=False):
    """
    计算收敛交叉映射（CCM）
    
    参数:
    - x, y: 输入时间序列
    - embed_dim: 嵌入维度
    - tau: 时间延迟
    - lib_sizes: 库大小列表
    - sample: 每个库大小的样本数
    - random: 是否随机选择库点
    - replacement: 是否允许替换
    
    返回:
    - ccm_result: CCM结果DataFrame
    - x_to_y_skill: x预测y的技能
    - y_to_x_skill: y预测x的技能
    """
    # 标准化数据
    x = (x - np.mean(x)) / np.std(x)
    y = (y - np.mean(y)) / np.std(y)
    
    # 创建数据框
    data = pd.DataFrame({'x': x, 'y': y})
    
    # 设置默认库大小
    if lib_sizes is None:
        max_lib_size = min(len(x) - embed_dim * tau, 500)
        lib_sizes = np.linspace(10, max_lib_size, 10, dtype=int)
    
    # 运行CCM
    ccm_result = CCM(
        dataFrame=data,
        E=embed_dim,
        Tp=0,
        columns='x,y',
        target='x',
        libSizes=','.join(map(str, lib_sizes)),
        sample=sample,
        random=random,
        replacement=replacement
    )
    
    # 提取结果
    x_to_y = ccm_result[ccm_result['Library'] == 'x']
    y_to_x = ccm_result[ccm_result['Library'] == 'y']
    
    # 计算预测技能（使用最大库大小的结果）
    x_to_y_skill = x_to_y['rho'].iloc[-1]
    y_to_x_skill = y_to_x['rho'].iloc[-1]
    
    return ccm_result, x_to_y_skill, y_to_x_skill

def plot_ccm_convergence(ccm_result, title=None, save_path=None, show=True):
    """
    绘制CCM收敛曲线
    
    参数:
    - ccm_result: CCM结果DataFrame
    - title: 图表标题
    - save_path: 保存路径
    - show: 是否显示图表
    """
    # 提取结果
    x_to_y = ccm_result[ccm_result['Library'] == 'x']
    y_to_x = ccm_result[ccm_result['Library'] == 'y']
    
    # 创建图表
    plt.figure(figsize=(10, 6))
    
    # 绘制收敛曲线
    plt.plot(x_to_y['LibSize'], x_to_y['rho'], 'b-', label='脑→心')
    plt.plot(y_to_x['LibSize'], y_to_x['rho'], 'r-', label='心→脑')
    
    # 添加误差条
    plt.errorbar(x_to_y['LibSize'], x_to_y['rho'], yerr=x_to_y['SD'], fmt='b.', capsize=3)
    plt.errorbar(y_to_x['LibSize'], y_to_x['rho'], yerr=y_to_x['SD'], fmt='r.', capsize=3)
    
    # 设置标签和标题
    plt.xlabel('库大小', fontsize=12)
    plt.ylabel('预测技能 (ρ)', fontsize=12)
    if title:
        plt.title(title, fontsize=14)
    else:
        plt.title('CCM收敛曲线', fontsize=14)
    
    # 添加图例
    plt.legend(fontsize=12)
    
    # 添加网格
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 保存图形
    if save_path:
        # 保存图形到 result/nonlinear_interaction 目录（符合项目规范）
        output_fig_dir = os.path.join("D:/ecgeeg/30-数据分析/5-NeuroKit2/result", 'nonlinear_interaction')
        os.makedirs(output_fig_dir, exist_ok=True)
        plt.savefig(os.path.join(output_fig_dir, save_path), dpi=300, bbox_inches='tight')
    
    # 显示图表
    if show:
        plt.tight_layout()
        plt.show()
    else:
        plt.close()

def test_ccm_with_sine_waves():
    """测试CCM与正弦波"""
    # 创建时间序列
    t = np.linspace(0, 10 * np.pi, 1000)
    x = np.sin(t)
    y = np.sin(t + np.pi/4)  # 相位差
    
    # 计算CCM
    ccm_result, x_to_y_skill, y_to_x_skill = compute_ccm(x, y)
    
    # 绘制收敛曲线
    plot_ccm_convergence(ccm_result, title='正弦波CCM测试', save_path='ccm_test_plot.png')
    
    print(f"脑→心 CCM技能: {x_to_y_skill:.4f}")
    print(f"心→脑 CCM技能: {y_to_x_skill:.4f}")
    print(f"差异 (脑→心 - 心→脑): {x_to_y_skill - y_to_x_skill:.4f}")

if __name__ == "__main__":
    test_ccm_with_sine_waves()

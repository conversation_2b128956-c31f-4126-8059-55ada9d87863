#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心因果关系非线性交互分析结果可视化脚本。
这个脚本将加载保存的CCM分析结果，并生成适合发表的可视化图表。
"""

import os
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import matplotlib as mpl

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置matplotlib参数，使图表更适合发表
mpl.rcParams['font.size'] = 11
mpl.rcParams['axes.linewidth'] = 1.5
mpl.rcParams['xtick.major.width'] = 1.5
mpl.rcParams['ytick.major.width'] = 1.5
mpl.rcParams['xtick.major.size'] = 4
mpl.rcParams['ytick.major.size'] = 4

# 定义文件路径
RESULTS_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'nonlinear_interaction')  # 使用绝对路径
RESULTS_FILE = os.path.join(RESULTS_DIR, 'ccm_results_all_subjects.csv')  # 使用CSV文件
ANXIETY_COMPARISON_FILE = os.path.join(RESULTS_DIR, 'ccm_anxiety_group_comparison.csv')  # 焦虑组比较文件
FIGURES_DIR = os.path.join(RESULTS_DIR, 'figures')

# 确保figures目录存在
os.makedirs(FIGURES_DIR, exist_ok=True)

def parse_array_string(s):
    """解析数组字符串为numpy数组"""
    try:
        # 移除方括号并分割
        s = s.strip('[]')
        # 处理科学计数法
        values = []
        for val in s.split():
            try:
                values.append(float(val))
            except ValueError:
                # 处理科学计数法
                if 'e' in val.lower():
                    values.append(float(val))
                else:
                    # 忽略无法解析的值
                    pass
        return np.array(values)
    except Exception as e:
        print(f"解析数组字符串时出错: {s}, 错误: {e}")
        return np.array([0.0])

def load_results():
    """加载保存的CCM分析结果"""
    try:
        # 加载CCM结果
        print(f"从 {RESULTS_FILE} 加载CCM结果...")
        ccm_results = pd.read_csv(RESULTS_FILE)

        # 构建结果字典
        results = {
            'ccm_results': ccm_results
        }

        # 根据subject_id前两位数字将被试分为高焦虑组和低焦虑组
        # 假设01-15为高焦虑组，16-31为低焦虑组
        ccm_results['anxiety_group'] = ccm_results['subject_id'].apply(
            lambda x: 'high' if int(str(x).zfill(2)[:2]) <= 15 else 'low'
        )

        # 构建用于可视化的数据结构
        high_anxiety_results = {}
        low_anxiety_results = {}

        # 提取高焦虑组数据
        high_anxiety_data = ccm_results[ccm_results['anxiety_group'] == 'high']
        # 提取低焦虑组数据
        low_anxiety_data = ccm_results[ccm_results['anxiety_group'] == 'low']

        # 提取心脏→大脑和大脑→心脏的数据
        high_anxiety_results['heart_to_brain'] = []
        high_anxiety_results['brain_to_heart'] = []

        for _, row in high_anxiety_data.iterrows():
            # 解析hr_to_eeg和eeg_to_hr列
            hr_to_eeg = parse_array_string(row['hr_to_eeg'])
            eeg_to_hr = parse_array_string(row['eeg_to_hr'])

            # 计算平均值并添加到列表
            high_anxiety_results['heart_to_brain'].append(np.mean(hr_to_eeg))
            high_anxiety_results['brain_to_heart'].append(np.mean(eeg_to_hr))

        low_anxiety_results['heart_to_brain'] = []
        low_anxiety_results['brain_to_heart'] = []

        for _, row in low_anxiety_data.iterrows():
            # 解析hr_to_eeg和eeg_to_hr列
            hr_to_eeg = parse_array_string(row['hr_to_eeg'])
            eeg_to_hr = parse_array_string(row['eeg_to_hr'])

            # 计算平均值并添加到列表
            low_anxiety_results['heart_to_brain'].append(np.mean(hr_to_eeg))
            low_anxiety_results['brain_to_heart'].append(np.mean(eeg_to_hr))

        # 添加到结果字典
        results['high_anxiety'] = high_anxiety_results
        results['low_anxiety'] = low_anxiety_results

        # 提取频段信息
        frequency_bands = {}
        for band in ccm_results['frequency_band'].unique():
            band_data = ccm_results[ccm_results['frequency_band'] == band]

            heart_to_brain_values = []
            brain_to_heart_values = []

            for _, row in band_data.iterrows():
                hr_to_eeg = parse_array_string(row['hr_to_eeg'])
                eeg_to_hr = parse_array_string(row['eeg_to_hr'])

                heart_to_brain_values.append(np.mean(hr_to_eeg))
                brain_to_heart_values.append(np.mean(eeg_to_hr))

            frequency_bands[band] = {
                'heart_to_brain': heart_to_brain_values,
                'brain_to_heart': brain_to_heart_values
            }

        results['frequency_bands'] = frequency_bands

        print(f"成功加载数据，包含 {len(ccm_results)} 条CCM结果")
        print(f"高焦虑组: {len(high_anxiety_data)} 条数据")
        print(f"低焦虑组: {len(low_anxiety_data)} 条数据")
        print(f"频段: {list(frequency_bands.keys())}")

        return results

    except Exception as e:
        import traceback
        print(f"加载数据时出错: {e}")
        traceback.print_exc()
        return None

def plot_ccm_comparison(results, save_path=None):
    """绘制高焦虑组和低焦虑组的CCM比较图"""
    # 解析结果
    high_anxiety_results = results.get('high_anxiety', {})
    low_anxiety_results = results.get('low_anxiety', {})

    # 提取CCM值
    heart_to_brain_high = high_anxiety_results.get('heart_to_brain', [])
    brain_to_heart_high = high_anxiety_results.get('brain_to_heart', [])
    heart_to_brain_low = low_anxiety_results.get('heart_to_brain', [])
    brain_to_heart_low = low_anxiety_results.get('brain_to_heart', [])

    # 创建DataFrame便于可视化
    data = []

    # 高焦虑组数据
    for value in heart_to_brain_high:
        data.append({'群组': '高焦虑', '因果方向': '心脏→大脑', 'CCM值': value})
    for value in brain_to_heart_high:
        data.append({'群组': '高焦虑', '因果方向': '大脑→心脏', 'CCM值': value})

    # 低焦虑组数据
    for value in heart_to_brain_low:
        data.append({'群组': '低焦虑', '因果方向': '心脏→大脑', 'CCM值': value})
    for value in brain_to_heart_low:
        data.append({'群组': '低焦虑', '因果方向': '大脑→心脏', 'CCM值': value})

    df = pd.DataFrame(data)

    # 计算统计差异
    # 高焦虑组内部比较：心脏→大脑 vs 大脑→心脏
    high_anxiety_ttest = stats.ttest_ind(heart_to_brain_high, brain_to_heart_high)
    high_anxiety_pvalue = high_anxiety_ttest.pvalue

    # 低焦虑组内部比较：心脏→大脑 vs 大脑→心脏
    low_anxiety_ttest = stats.ttest_ind(heart_to_brain_low, brain_to_heart_low)
    low_anxiety_pvalue = low_anxiety_ttest.pvalue

    # 心脏→大脑比较：高焦虑 vs 低焦虑
    heart_to_brain_ttest = stats.ttest_ind(heart_to_brain_high, heart_to_brain_low)
    heart_to_brain_pvalue = heart_to_brain_ttest.pvalue

    # 大脑→心脏比较：高焦虑 vs 低焦虑
    brain_to_heart_ttest = stats.ttest_ind(brain_to_heart_high, brain_to_heart_low)
    brain_to_heart_pvalue = brain_to_heart_ttest.pvalue

    # 绘制图表
    plt.figure(figsize=(10, 8))

    # 绘制箱线图和小提琴图的组合
    ax = sns.violinplot(x='群组', y='CCM值', hue='因果方向', data=df,
                    split=True, inner='quartile', palette='Set2')

    # 添加统计显著性标记
    # 高焦虑组内部比较
    x1, x2 = 0, 0
    y1, y2 = np.mean(heart_to_brain_high), np.mean(brain_to_heart_high)
    h = max(y1, y2) + 0.05
    plt.plot([x1, x1, x2, x2], [y1, h, h, y2], 'k-')
    p_text = f"p = {high_anxiety_pvalue:.3f}"
    if high_anxiety_pvalue < 0.05:
        p_text += '*'
    if high_anxiety_pvalue < 0.01:
        p_text += '*'
    if high_anxiety_pvalue < 0.001:
        p_text += '*'
    plt.text((x1+x2)/2, h+0.01, p_text, ha='center')

    # 低焦虑组内部比较
    x1, x2 = 1, 1
    y1, y2 = np.mean(heart_to_brain_low), np.mean(brain_to_heart_low)
    h = max(y1, y2) + 0.05
    plt.plot([x1, x1, x2, x2], [y1, h, h, y2], 'k-')
    p_text = f"p = {low_anxiety_pvalue:.3f}"
    if low_anxiety_pvalue < 0.05:
        p_text += '*'
    if low_anxiety_pvalue < 0.01:
        p_text += '*'
    if low_anxiety_pvalue < 0.001:
        p_text += '*'
    plt.text((x1+x2)/2, h+0.01, p_text, ha='center')

    # 设置图表标题和标签
    plt.title('高焦虑与低焦虑群组脑-心非线性因果关系比较', fontsize=14)
    plt.ylabel('趋同交叉映射强度 (CCM)', fontsize=12)
    plt.xlabel('焦虑水平群组', fontsize=12)

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至 {save_path}")

    plt.show()

    # 打印统计结果
    print("\n统计分析结果:")
    print(f"高焦虑组内部比较 (心脏→大脑 vs 大脑→心脏): p = {high_anxiety_pvalue:.4f}")
    print(f"低焦虑组内部比较 (心脏→大脑 vs 大脑→心脏): p = {low_anxiety_pvalue:.4f}")
    print(f"心脏→大脑比较 (高焦虑 vs 低焦虑): p = {heart_to_brain_pvalue:.4f}")
    print(f"大脑→心脏比较 (高焦虑 vs 低焦虑): p = {brain_to_heart_pvalue:.4f}")

    return df, {
        'high_anxiety_internal': high_anxiety_pvalue,
        'low_anxiety_internal': low_anxiety_pvalue,
        'heart_to_brain_comparison': heart_to_brain_pvalue,
        'brain_to_heart_comparison': brain_to_heart_pvalue
    }

def plot_ccm_heatmap(results, save_path=None):
    """绘制CCM强度热图，展示不同EEG频段与心率变异性的因果关系强度"""
    # 假设结果包含不同频段的CCM值
    frequency_bands = results.get('frequency_bands', {})

    if not frequency_bands:
        print("结果中没有频段相关的CCM数据")
        return

    # 创建热图数据
    band_names = list(frequency_bands.keys())
    heart_to_brain_values = [np.mean(frequency_bands[band].get('heart_to_brain', [0])) for band in band_names]
    brain_to_heart_values = [np.mean(frequency_bands[band].get('brain_to_heart', [0])) for band in band_names]

    # 创建热图数据矩阵
    heatmap_data = np.array([heart_to_brain_values, brain_to_heart_values])

    # 绘制热图
    plt.figure(figsize=(10, 6))
    ax = sns.heatmap(heatmap_data, annot=True, cmap='YlOrRd',
                    xticklabels=band_names,
                    yticklabels=['心脏→大脑', '大脑→心脏'],
                    fmt='.3f', linewidths=0.5)

    plt.title('不同EEG频段与心率变异性的非线性因果关系强度', fontsize=14)
    plt.tight_layout()

    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"热图已保存至 {save_path}")

    plt.show()

def main():
    """主函数"""
    print("加载CCM分析结果...")
    results = load_results()

    print("生成CCM比较图...")
    df, stats_results = plot_ccm_comparison(
        results,
        save_path=os.path.join(FIGURES_DIR, 'ccm_anxiety_comparison.png')
    )

    # 如果结果中包含频段信息，生成热图
    if 'frequency_bands' in results:
        print("生成CCM频段热图...")
        plot_ccm_heatmap(
            results,
            save_path=os.path.join(FIGURES_DIR, 'ccm_frequency_heatmap.png')
        )

    print("\n分析摘要:")
    print("==================================")

    # 总结主要发现
    has_significant_diff = any(p < 0.05 for p in stats_results.values())
    if has_significant_diff:
        print("✓ 发现统计显著的脑-心非线性因果关系差异")

        if stats_results['high_anxiety_internal'] < 0.05:
            print("✓ 高焦虑组中，心脏→大脑和大脑→心脏的因果关系强度存在显著差异")

        if stats_results['low_anxiety_internal'] < 0.05:
            print("✓ 低焦虑组中，心脏→大脑和大脑→心脏的因果关系强度存在显著差异")

        if stats_results['heart_to_brain_comparison'] < 0.05:
            print("✓ 高焦虑组和低焦虑组的心脏→大脑因果关系强度存在显著差异")

        if stats_results['brain_to_heart_comparison'] < 0.05:
            print("✓ 高焦虑组和低焦虑组的大脑→心脏因果关系强度存在显著差异")

        print("\n这些结果显示脑-心交互在焦虑状态下的重要差异，具有发表潜力。")
    else:
        print("× 未发现统计显著的脑-心非线性因果关系差异")
        print("\n建议增加样本量或调整分析参数后再次尝试。")

    print("==================================")
    print(f"可视化结果已保存至 {FIGURES_DIR} 目录")

if __name__ == "__main__":
    main()
# CausalFormer心电-脑电因果关系分析项目实验方案

## 项目概述

本项目旨在使用CausalFormer模型分析心电图(ECG)与脑电图(EEG)数据之间的因果关系，特别关注心理韧性如何调节内感受加工的神经机制。项目采用增量开发方法，分为八个阶段逐步实现。

## 数据处理流程设计

### 1. 数据处理流程总体架构

#### 1.1 核心处理模块
- **数据加载模块**：负责从原始文件读取数据
- **数据验证模块**：负责在各处理阶段验证数据质量和格式
- **通道处理模块**：负责通道选择和信号质量评估
- **数据标准化模块**：负责数据归一化和标准化
- **数据分段模块**：负责基于R波的数据分段
- **数据集构建模块**：负责构建训练和验证数据集
- **错误处理模块**：负责统一的错误处理和日志记录

#### 1.2 数据流转路径
```
原始数据文件 → 数据加载 → 数据验证 → 通道选择 → 数据验证 →
数据标准化 → 数据验证 → 数据分段 → 数据验证 →
数据集构建 → 数据验证 → 模型输入
```

#### 1.3 错误处理流程
```
错误检测 → 错误信息构建(位置+类型+原因+解决方案) →
错误日志记录 → 程序终止 → 返回错误代码
```

### 2. 详细模块设计

#### 2.1 数据加载模块

##### 2.1.1 功能描述
- 从HEP数据集中加载EEG和ECG数据
- 解析数据文件的元数据（通道信息、采样率、被试信息等）
- 提取实验阶段信息（rest1, rest2, rest3, test1, test2, test3）

##### 2.1.2 输入验证
- 验证数据文件路径是否存在
- 验证数据文件格式是否正确（h5格式）
- 验证数据文件是否可读取

##### 2.1.3 处理逻辑
1. 扫描数据目录，识别所有有效的数据文件
2. 根据文件名提取实验阶段信息
3. 读取每个文件的数据和元数据
4. 提取通道信息，区分EEG和ECG通道
5. 构建统一的数据结构，包含所有必要信息

##### 2.1.4 输出验证
- 验证加载的数据是否完整
- 验证EEG和ECG通道是否正确识别
- 验证各实验阶段的数据是否均已加载
- 验证数据维度是否符合预期

##### 2.1.5 错误处理
- 文件不存在：终止程序，返回文件路径错误
- 文件格式错误：终止程序，返回文件格式错误
- 通道识别失败：终止程序，返回通道识别错误
- 数据不完整：终止程序，返回数据完整性错误

#### 2.2 通道处理模块

##### 2.2.1 功能描述
- 选择关键EEG通道（Fz, Cz, Pz等中线导联）
- 评估通道信号质量
- 处理可能的通道缺失情况

##### 2.2.2 输入验证
- 验证输入数据结构是否完整
- 验证通道信息是否存在
- 验证关键通道是否在数据中

##### 2.2.3 处理逻辑
1. 根据通道名称识别关键通道的索引
2. 提取关键通道的数据
3. 评估每个通道的信号质量（信噪比、缺失值比例等）
4. 对信号质量不佳的通道进行标记或处理

##### 2.2.4 输出验证
- 验证选择的通道数量是否符合预期
- 验证关键通道是否均已选择
- 验证通道数据的维度是否正确
- 验证通道信号质量是否达标

##### 2.2.5 错误处理
- 关键通道缺失：终止程序，返回通道缺失错误
- 信号质量不佳：终止程序，返回信号质量错误
- 通道数据不完整：终止程序，返回数据完整性错误

#### 2.3 数据标准化模块

##### 2.3.1 功能描述
- 对EEG和ECG数据进行标准化处理
- 确保数据在相同的数值范围内
- 处理异常值和离群点

##### 2.3.2 输入验证
- 验证输入数据的维度和类型
- 验证数据是否包含无效值（NaN, Inf）
- 验证数据分布是否合理

##### 2.3.3 处理逻辑
1. 对每个通道的数据计算均值和标准差
2. 使用Z-score方法进行标准化（减均值除标准差）
3. 检测并处理异常值（超过±3个标准差的值）
4. 验证标准化后的数据分布

##### 2.3.4 输出验证
- 验证标准化后的数据均值是否接近0
- 验证标准化后的数据标准差是否接近1
- 验证是否存在异常值
- 验证数据分布是否符合预期

##### 2.3.5 错误处理
- 数据包含无效值：终止程序，返回数据无效错误
- 标准差为0：终止程序，返回数据变异性错误
- 异常值比例过高：终止程序，返回数据质量错误

#### 2.4 数据分段模块

##### 2.4.1 功能描述
- 基于R波位置进行数据分段
- 确保每个数据片段包含完整的心电-脑电信息
- 处理片段边界和重叠问题

##### 2.4.2 输入验证
- 验证R波位置信息是否存在
- 验证R波位置是否在有效范围内
- 验证数据长度是否足够进行分段

##### 2.4.3 处理逻辑
1. 根据R波位置计算每个片段的起始和结束位置
2. 提取每个片段的EEG和ECG数据
3. 确保每个片段包含R波前后的完整信息
4. 处理片段边界问题（如不完整片段的处理）

##### 2.4.4 输出验证
- 验证分段后的数据片段数量是否符合预期
- 验证每个片段的长度是否一致
- 验证R波在每个片段中的位置是否正确
- 验证片段数据的完整性

##### 2.4.5 错误处理
- R波位置无效：终止程序，返回R波位置错误
- 数据长度不足：终止程序，返回数据长度错误
- 片段不完整：终止程序，返回片段完整性错误

#### 2.5 数据集构建模块

##### 2.5.1 功能描述
- 构建符合CausalFormer模型输入要求的数据集
- 划分训练集和验证集
- 实现批处理功能

##### 2.5.2 输入验证
- 验证数据片段的数量和维度
- 验证数据片段的格式是否一致
- 验证数据是否满足模型输入要求

##### 2.5.3 处理逻辑
1. 将数据片段转换为模型所需的输入格式
2. 按照指定比例划分训练集和验证集
3. 实现数据批处理功能，生成批次数据
4. 确保每个批次的数据维度一致

##### 2.5.4 输出验证
- 验证训练集和验证集的大小是否符合预期
- 验证批处理后的数据维度是否正确
- 验证数据格式是否符合模型输入要求
- 验证数据加载器是否正常工作

##### 2.5.5 错误处理
- 数据格式不符：终止程序，返回数据格式错误
- 批处理失败：终止程序，返回批处理错误
- 数据集划分错误：终止程序，返回数据集划分错误

### 3. CausalFormer模型输入要求分析

#### 3.1 输入数据格式
- 时间序列数据：多变量时间序列，包含多个通道
- 输入维度：[batch_size, series_num, input_window, feature_dim]
  - batch_size：批次大小
  - series_num：时间序列数量（通道数）
  - input_window：输入窗口长度（时间点数量）
  - feature_dim：每个时间点的特征维度

#### 3.2 数据预处理要求
- 数据标准化：所有输入数据应标准化到相似的数值范围
- 因果关系保持：数据处理不应破坏原始数据中的因果关系
- 时间对齐：确保不同通道的数据在时间上对齐
- 缺失值处理：模型不接受缺失值，必须进行适当处理

#### 3.3 批处理要求
- 批次内维度一致：同一批次内的所有样本维度必须一致
- 序列长度固定：每个输入序列的长度必须固定
- 批次大小优化：根据GPU内存和计算效率选择合适的批次大小

#### 3.4 与我们数据的匹配分析
- EEG通道对应series_num的一部分
- ECG通道对应series_num的另一部分
- R波中心化片段长度对应input_window
- 每个时间点的原始值对应feature_dim（通常为1）
- 需要确保数据维度顺序与模型要求一致

### 4. 数据验证机制设计

#### 4.1 验证点设置
- 数据加载后：验证数据完整性、通道数量和采样率
- 通道选择后：验证关键通道存在性和信号质量
- 数据标准化后：验证数据分布和异常值情况
- 数据分段后：验证片段长度、R波位置和完整性
- 数据集构建前：验证所有片段的维度一致性
- 模型输入前：验证数据格式和维度与模型要求的匹配度

#### 4.2 验证方法
- 数据完整性验证：检查数据是否包含所有必要字段和值
- 维度验证：检查数据维度是否符合预期
- 数值范围验证：检查数据值是否在合理范围内
- 分布验证：检查数据分布是否符合预期（均值、标准差等）
- 异常值验证：检查是否存在异常值或离群点
- 格式验证：检查数据格式是否符合模型要求

#### 4.3 验证结果处理
- 验证通过：继续下一步处理
- 验证失败：立即终止程序，返回详细的错误信息
- 验证警告：记录警告信息，但不终止程序（仅用于非关键问题）

### 5. 错误处理机制设计

#### 5.1 错误类型定义
- 数据文件错误：文件不存在、格式错误、读取失败等
- 数据内容错误：数据不完整、通道缺失、信号质量差等
- 数据处理错误：标准化失败、分段错误、批处理错误等
- 格式匹配错误：数据格式与模型要求不匹配

#### 5.2 错误信息构建
- 错误位置：指明错误发生在哪个处理阶段和函数
- 错误类型：指明错误的具体类型
- 错误原因：详细说明导致错误的原因
- 解决方案：提供可能的解决方法或建议

#### 5.3 错误处理流程
1. 检测到错误
2. 构建详细的错误信息
3. 记录错误日志
4. 终止当前处理流程
5. 返回错误代码和信息

### 6. 实现计划

#### 6.1 阶段一：基础数据处理实现
- 实现数据加载模块
- 实现通道处理模块
- 实现数据标准化模块
- 实现基本的错误处理机制
- 实现关键验证点

#### 6.2 阶段二：数据分段与数据集构建
- 实现数据分段模块
- 实现数据集构建模块
- 完善错误处理机制
- 增加更多验证点

#### 6.3 阶段三：与CausalFormer模型集成
- 确保数据格式与模型要求匹配
- 实现模型输入前的最终验证
- 测试数据处理流程与模型的集成
- 优化数据处理性能

#### 6.4 阶段四：测试与优化
- 全面测试数据处理流程
- 识别并修复潜在问题
- 优化处理性能和内存使用
- 完善文档和注释

### 7. 关键成功指标

#### 7.1 数据质量指标
- 数据完整率：成功处理的数据比例
- 异常值比例：数据中异常值的比例
- 信号质量评分：通道信号质量的综合评分

#### 7.2 处理效率指标
- 处理时间：完整数据处理流程的耗时
- 内存使用：数据处理过程中的峰值内存使用
- 批处理效率：每秒处理的样本数量

#### 7.3 模型兼容性指标
- 格式匹配度：数据格式与模型要求的匹配程度
- 训练稳定性：使用处理后数据训练模型的稳定性
- 预测性能：模型在处理后数据上的预测性能

## 阶段一：基础数据处理与模型框架搭建

### 具体目标
- 建立数据加载和预处理管道
- 搭建CausalFormer模型的基础框架
- 实现简单的单变量预测功能

### 技术实现要点
- 实现数据加载函数，支持EEG(63通道)和ECG数据的读取
- 实现基本的数据预处理：滤波、R波检测、数据分段和标准化
- 搭建基础Transformer编码器结构和因果自注意力机制
- 实现简单的预测头，支持单变量时间序列预测

### 验证标准
- 模型能够成功加载数据并完成训练
- 单变量预测性能优于简单基线(如AR模型)
- 可视化注意力权重，确认因果掩码正常工作

### 预期结果
- 功能完整的数据处理管道
- 可训练的基础CausalFormer模型
- 单变量预测的基线性能指标

## 阶段二：多变量联合建模与交叉通道预测

### 具体目标
- 扩展模型以支持心电和脑电信号的联合建模
- 实现交叉通道预测功能
- 初步分析心电-脑电之间的信息流

### 技术实现要点
- 扩展数据处理管道，支持多变量数据的联合处理
- 修改模型架构，支持多通道输入和输出
- 实现交叉通道预测，使ECG能预测EEG，EEG能预测ECG
- 开发注意力矩阵分析工具，提取跨通道关系

### 验证标准
- 多变量预测性能评估(MSE, MAE)优于单变量模型
- 交叉通道预测准确性评估
- 注意力矩阵中能观察到跨通道关系

### 预期结果
- 支持多变量建模的CausalFormer模型
- 交叉通道预测性能指标
- 初步的跨通道注意力可视化

## 阶段三：分层因果建模与注意力增强

### 具体目标
- 实现分层因果建模架构
- 增强心电-脑电之间的因果关系捕捉
- 提取初步的因果关系图

### 技术实现要点
- 开发信号特定编码器(ECG编码器和EEG编码器)
- 实现跨信号注意力层，专注于捕捉信号间交互
- 设计并实现生理学引导的注意力掩码，增强关键时间窗口(0.4-0.6s和0.5-0.7s)的权重
- 开发因果关系图提取工具

### 验证标准
- 分层模型的预测性能优于非分层模型
- 干预实验显示ECG信息对EEG预测有显著影响
- 提取的因果关系图符合基本生理学知识

### 预期结果
- 分层因果建模架构的CausalFormer模型
- 干预实验结果报告
- 初步的心电-脑电因果关系图

## 阶段四：因果发现与评估

### 具体目标
- 从训练好的模型中提取因果关系
- 量化心电→脑电的因果强度
- 评估因果发现的准确性

### 技术实现要点
- 开发注意力权重分析工具，提取因果关系
- 实现因果强度量化方法，计算不同通道间的因果强度
- 开发因果评估指标，评估因果发现的准确性
- 实现与其他因果发现方法(如Granger因果性)的比较

### 验证标准
- 提取的因果关系与已知生理学知识一致
- 反事实干预实验验证因果关系的有效性
- 与其他因果发现方法的比较结果

### 预期结果
- 心电-脑电因果关系的详细描述
- 因果强度量化结果
- 与其他方法的比较报告

## 阶段五：静息态vs刺激态因果差异分析

### 具体目标
- 比较静息态和刺激态条件下的因果关系差异
- 识别压力条件下变化最显著的因果路径
- 量化条件间的因果强度变化

### 技术实现要点
- 按条件(静息态/刺激态)分组训练模型
- 开发条件间因果差异分析工具
- 实现条件特定因果图的比较方法
- 开发条件间因果强度变化的统计分析

### 验证标准
- 条件间因果强度差异的统计显著性
- 因果路径变化的一致性
- 与主观压力评分的相关性

### 预期结果
- 静息态和刺激态条件下的因果关系图
- 条件间因果差异的统计分析报告
- 关键因果路径变化的可视化

## 阶段六：心理韧性调节效应分析

### 具体目标
- 分析心理韧性如何调节静息态-刺激态因果关系变化
- 识别受心理韧性调节最显著的因果路径
- 建立心理韧性-因果强度的预测模型

### 技术实现要点
- 整合心理韧性量表数据(总分及三个维度)
- 开发调节效应分析工具，分析心理韧性对因果关系的调节
- 实现高/低心理韧性分组比较
- 开发预测模型，预测心理韧性对因果变化的影响

### 验证标准
- 调节效应的统计显著性
- 高/低心理韧性组的因果模式差异
- 预测模型的交叉验证性能

### 预期结果
- 心理韧性调节效应的统计分析报告
- 高/低心理韧性组的因果关系比较
- 心理韧性-因果强度预测模型

## 阶段七：整合分析与结果可视化

### 具体目标
- 整合所有分析结果
- 创建高质量的可视化图表
- 生成综合研究报告

### 技术实现要点
- 开发结果整合框架，汇总前六个阶段的结果
- 实现高级可视化工具，创建清晰直观的图表
- 开发报告生成模块，自动生成研究报告

### 验证标准
- 结果的内部一致性
- 可视化图表的清晰度和信息量
- 研究报告的完整性和科学性

### 预期结果
- 综合研究报告
- 高质量可视化图表集
- 结果数据库

## 阶段八：模型优化与扩展应用

### 具体目标
- 优化模型性能和计算效率
- 扩展模型应用范围
- 探索个性化因果模型

### 技术实现要点
- 实现模型压缩与加速技术(如量化、剪枝)
- 开发迁移学习框架，支持模型在新数据上的应用
- 实现个性化参数调整机制，适应个体差异

### 验证标准
- 优化前后的性能和效率比较
- 在新数据上的泛化能力
- 个性化模型的预测准确性

### 预期结果
- 优化后的CausalFormer模型
- 扩展应用示例
- 个性化因果模型原型

## 项目进度跟踪

### 阶段一：基础数据处理与模型框架搭建
- 状态：失败
- 开始日期：2025-05-20
- 完成日期：2025-05-20
- 主要成果：
- 无
- 遇到的挑战：
- 执行过程中遇到错误，详见日志文件
- 解决方案：
- 需要修复错误后重新运行
- 对后续阶段的影响：
- 延迟后续阶段的开始

### 阶段二：多变量联合建模与交叉通道预测
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段三：分层因果建模与注意力增强
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段四：因果发现与评估
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段五：静息态vs刺激态因果差异分析
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段六：心理韧性调节效应分析
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段七：整合分析与结果可视化
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

### 阶段八：模型优化与扩展应用
- 状态：未开始
- 开始日期：
- 完成日期：
- 主要成果：
- 遇到的挑战：
- 解决方案：
- 对后续阶段的影响：

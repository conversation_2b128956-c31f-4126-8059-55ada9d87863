#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脑-心因果分析实验系统的各个模块

功能：
- 测试数据分段模块
- 测试特征提取模块
- 测试模型输入准备模块
- 测试CausalFormer模型训练模块
- 测试结果可视化模块

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import torch
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_modules_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-Modules")

# 设置随机种子
random.seed(42)
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed_all(42)

def create_synthetic_data():
    """创建合成数据用于测试"""
    logger.info("创建合成数据...")

    # 创建合成EEG和ECG数据
    epochs = 10
    eeg_channels = 9  # Fz, Cz, Pz, F3, F4, C3, C4, P3, P4
    ecg_channels = 1  # ECG
    time_points = 1000  # 2秒 @ 500Hz

    # 创建随机EEG数据
    eeg_data = np.random.randn(epochs, eeg_channels, time_points)

    # 创建随机ECG数据
    ecg_data = np.random.randn(epochs, ecg_channels, time_points)

    # 添加一些结构（正弦波）
    t = np.linspace(0, 2*np.pi, time_points)
    for i in range(epochs):
        # 添加alpha波 (8-13 Hz)
        alpha_freq = 10  # Hz
        alpha_amp = 0.5
        alpha_wave = alpha_amp * np.sin(alpha_freq * t)

        # 添加心跳 (~1 Hz)
        heart_freq = 1.2  # Hz
        heart_amp = 1.0
        heart_wave = heart_amp * np.sin(heart_freq * t)

        # 将波形添加到数据中
        for ch in range(eeg_channels):
            eeg_data[i, ch, :] += alpha_wave

        for ch in range(ecg_channels):
            ecg_data[i, ch, :] += heart_wave

    # 创建数据字典
    synthetic_data = {
        'test': {
            'eeg': eeg_data,
            'ecg': ecg_data,
            'sampling_rate': 500,
            'channel_names': {
                'eeg': ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4'],
                'ecg': ['ECG']
            }
        }
    }

    logger.info(f"合成数据创建完成: EEG形状={eeg_data.shape}, ECG形状={ecg_data.shape}")
    return synthetic_data

def test_data_segmentation(data):
    """测试数据分段模块"""
    logger.info("=== 测试数据分段模块 ===")

    from data_segmentation import (
        segment_data_fixed_length, segment_data_event_related,
        segment_data_adaptive, visualize_segmentation
    )

    # 获取测试数据
    stage = list(data.keys())[0]
    eeg_data = data[stage]['eeg']
    ecg_data = data[stage]['ecg']

    # 测试固定长度分段
    logger.info("测试固定长度分段...")
    segment_length = 200  # 0.4秒 @ 500Hz
    overlap = 0.5  # 50%重叠

    try:
        eeg_segments = segment_data_fixed_length(eeg_data, segment_length, overlap)
        ecg_segments = segment_data_fixed_length(ecg_data, segment_length, overlap)

        logger.info(f"原始EEG数据形状: {eeg_data.shape}")
        logger.info(f"分段后EEG数据形状: {eeg_segments.shape}")
        logger.info(f"原始ECG数据形状: {ecg_data.shape}")
        logger.info(f"分段后ECG数据形状: {ecg_segments.shape}")

        # 可视化分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments, method='fixed')
        logger.info(f"固定长度分段可视化结果已保存到: {output_file}")

        # 测试事件相关分段
        logger.info("测试事件相关分段...")
        time_points = eeg_data.shape[2]
        event_indices = [int(time_points * 0.25), int(time_points * 0.5), int(time_points * 0.75)]
        pre_event = 150  # 0.3秒 @ 500Hz
        post_event = 500  # 1.0秒 @ 500Hz

        eeg_segments_event = segment_data_event_related(eeg_data, event_indices, pre_event, post_event)
        ecg_segments_event = segment_data_event_related(ecg_data, event_indices, pre_event, post_event)

        logger.info(f"事件相关分段后EEG数据形状: {eeg_segments_event.shape}")
        logger.info(f"事件相关分段后ECG数据形状: {ecg_segments_event.shape}")
        logger.info(f"事件前取样点数: {pre_event} (0.3秒 @ 500Hz), 事件后取样点数: {post_event} (1.0秒 @ 500Hz)")

        # 可视化事件相关分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments_event, method='event')
        logger.info(f"事件相关分段可视化结果已保存到: {output_file}")

        # 测试自适应分段
        logger.info("测试自适应分段...")
        method = 'variance'
        threshold = 0.8
        min_length = 100
        max_length = 300

        eeg_segments_adaptive, eeg_segment_points = segment_data_adaptive(
            eeg_data, method, threshold, min_length, max_length
        )

        logger.info(f"自适应分段后EEG数据数量: {len(eeg_segments_adaptive)}")
        logger.info(f"自适应分段点数量: {len(eeg_segment_points)}")

        # 可视化自适应分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments_adaptive, eeg_segment_points, method='adaptive')
        logger.info(f"自适应分段可视化结果已保存到: {output_file}")

        logger.info("数据分段模块测试成功")
        return eeg_segments, ecg_segments

    except Exception as e:
        logger.error(f"数据分段模块测试失败: {str(e)}")
        return None, None

def test_feature_extraction(data, segmented_eeg, segmented_ecg):
    """测试特征提取模块"""
    logger.info("=== 测试特征提取模块 ===")

    from feature_extractor import (
        extract_time_domain_features, extract_frequency_domain_features, 
        extract_nonlinear_features, extract_features
    )

    # 获取测试数据
    stage = list(data.keys())[0]
    sampling_rate = data[stage]['sampling_rate']

    # 创建分段数据字典
    segmented_data = {
        stage: {
            'eeg': segmented_eeg,
            'ecg': segmented_ecg,
            'sampling_rate': sampling_rate,
            'channel_names': data[stage]['channel_names']
        }
    }

    try:
        # 测试时域特征提取
        logger.info("测试时域特征提取...")
        eeg_time_features = extract_time_domain_features(segmented_eeg, sampling_rate)

        logger.info(f"EEG时域特征数量: {len(eeg_time_features)}")
        for feature_name, feature_values in eeg_time_features.items():
            logger.info(f"  - {feature_name}: 形状 {feature_values.shape}")

        # 测试频域特征提取
        logger.info("测试频域特征提取...")
        eeg_freq_features = extract_frequency_domain_features(segmented_eeg, sampling_rate)

        logger.info(f"EEG频域特征数量: {len(eeg_freq_features)}")
        for feature_name, feature_values in eeg_freq_features.items():
            if len(feature_values.shape) == 3:
                logger.info(f"  - {feature_name}: 形状 {feature_values.shape} (segments, channels, bands)")
            else:
                logger.info(f"  - {feature_name}: 形状 {feature_values.shape}")
        
        # 测试非线性特征提取
        logger.info("测试非线性特征提取...")
        eeg_nonlinear_features = extract_nonlinear_features(segmented_eeg, sampling_rate)
        
        logger.info(f"EEG非线性特征数量: {len(eeg_nonlinear_features)}")
        for feature_name, feature_values in eeg_nonlinear_features.items():
            logger.info(f"  - {feature_name}: 形状 {feature_values.shape}")

        # 测试完整特征提取
        logger.info("测试完整特征提取...")
        features_data = extract_features(
            segmented_data, 
            feature_types=['time', 'frequency', 'nonlinear'], 
            sampling_rate=sampling_rate
        )

        logger.info(f"特征数据阶段: {list(features_data.keys())}")
        logger.info(f"特征类型: {features_data[stage]['feature_types']}")
        logger.info(f"EEG特征类型: {list(features_data[stage]['eeg_features'].keys())}")
        logger.info(f"ECG特征类型: {list(features_data[stage]['ecg_features'].keys())}")
        
        # 分析提取的特征总维度
        eeg_feature_dim = 0
        ecg_feature_dim = 0
        
        for feature_type, features in features_data[stage]['eeg_features'].items():
            type_dim = 0
            for feature_name, feature_values in features.items():
                if len(feature_values.shape) == 3:  # 对于3D特征，计算最后两个维度的乘积
                    type_dim += feature_values.shape[1] * feature_values.shape[2]
                else:  # 对于2D特征，计算第二个维度
                    type_dim += feature_values.shape[1]
            logger.info(f"EEG {feature_type} 特征总维度: {type_dim}")
            eeg_feature_dim += type_dim
            
        for feature_type, features in features_data[stage]['ecg_features'].items():
            type_dim = 0
            for feature_name, feature_values in features.items():
                if len(feature_values.shape) == 3:
                    type_dim += feature_values.shape[1] * feature_values.shape[2]
                else:
                    type_dim += feature_values.shape[1]
            logger.info(f"ECG {feature_type} 特征总维度: {type_dim}")
            ecg_feature_dim += type_dim
        
        logger.info(f"EEG特征总维度: {eeg_feature_dim}")
        logger.info(f"ECG特征总维度: {ecg_feature_dim}")
        logger.info(f"模型输入维度将为 {eeg_feature_dim}，输出维度将为 {ecg_feature_dim}")

        logger.info("特征提取模块测试成功")
        return features_data

    except Exception as e:
        logger.error(f"特征提取模块测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def test_model_input_preparation(features_data):
    """测试模型输入准备模块"""
    logger.info("=== 测试模型输入准备模块 ===")

    from model_input_preparer import (
        features_to_sequences, prepare_model_inputs, visualize_model_inputs
    )

    try:
        # 测试特征序列化
        logger.info("测试特征序列化...")
        sequence_length = 5
        stride = 1

        sequences_data = features_to_sequences(features_data, sequence_length, stride)

        stage = list(sequences_data.keys())[0]
        logger.info(f"序列数据阶段: {list(sequences_data.keys())}")
        logger.info(f"EEG序列形状: {sequences_data[stage]['eeg_sequences'].shape}")
        logger.info(f"ECG序列形状: {sequences_data[stage]['ecg_sequences'].shape}")
        logger.info(f"序列长度: {sequences_data[stage]['sequence_length']}")
        logger.info(f"EEG特征维度: {sequences_data[stage]['eeg_feature_dim']}")
        logger.info(f"ECG特征维度: {sequences_data[stage]['ecg_feature_dim']}")

        # 测试模型输入准备
        logger.info("测试模型输入准备...")
        batch_size = 4

        model_inputs = prepare_model_inputs(sequences_data, batch_size=batch_size)

        logger.info(f"模型输入阶段: {list(model_inputs.keys())}")
        logger.info(f"训练样本数: {model_inputs[stage]['train_samples']}")
        logger.info(f"验证样本数: {model_inputs[stage]['val_samples']}")
        logger.info(f"测试样本数: {model_inputs[stage]['test_samples']}")
        logger.info(f"输入维度: {model_inputs[stage]['input_dim']}")
        logger.info(f"输出维度: {model_inputs[stage]['output_dim']}")

        # 测试模型输入可视化
        logger.info("测试模型输入可视化...")
        output_file = visualize_model_inputs(model_inputs, stage)
        logger.info(f"模型输入可视化结果已保存到: {output_file}")

        logger.info("模型输入准备模块测试成功")
        return model_inputs

    except Exception as e:
        logger.error(f"模型输入准备模块测试失败: {str(e)}")
        return None

def test_causal_former_model(model_inputs):
    """测试CausalFormer模型训练模块"""
    logger.info("=== 测试CausalFormer模型训练模块 ===")

    from causal_former import (
        CausalFormer, train_model, evaluate_model, visualize_training_history
    )

    try:
        # 获取测试数据
        stage = list(model_inputs.keys())[0]
        stage_inputs = model_inputs[stage]

        # 创建模型
        logger.info("创建CausalFormer模型...")
        input_dim = stage_inputs['input_dim']
        output_dim = stage_inputs['output_dim']
        d_model = 32
        n_heads = 2
        n_layers = 2
        d_ff = 64
        dropout = 0.1

        model = CausalFormer(
            input_dim=input_dim,
            output_dim=output_dim,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            d_ff=d_ff,
            dropout=dropout
        )

        logger.info(f"模型创建成功: input_dim={input_dim}, output_dim={output_dim}")

        # 测试模型训练
        logger.info("测试模型训练...")
        train_loader = stage_inputs['train_loader']
        val_loader = stage_inputs['val_loader']

        epochs = 3  # 测试时使用较少的轮次
        lr = 0.001
        device = 'cuda' if torch.cuda.is_available() else 'cpu'

        history, trained_model = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=epochs,
            lr=lr,
            device=device
        )

        logger.info(f"模型训练完成: 训练轮次={epochs}, 最终训练损失={history['train_loss'][-1]:.6f}, 最终验证损失={history['val_loss'][-1]:.6f}")

        # 测试训练历史可视化
        logger.info("测试训练历史可视化...")
        output_file = visualize_training_history(history)
        logger.info(f"训练历史可视化结果已保存到: {output_file}")

        # 测试模型评估
        logger.info("测试模型评估...")
        test_loader = stage_inputs['test_loader']

        eval_results = evaluate_model(trained_model, test_loader, device=device)

        logger.info(f"模型评估结果: MSE={eval_results['mse']:.6f}, RMSE={eval_results['rmse']:.6f}, MAE={eval_results['mae']:.6f}, R²={eval_results['r2']:.6f}")

        logger.info("CausalFormer模型训练模块测试成功")
        return eval_results

    except Exception as e:
        logger.error(f"CausalFormer模型训练模块测试失败: {str(e)}")
        return None

def test_result_visualization(eval_results):
    """测试结果可视化模块"""
    logger.info("=== 测试结果可视化模块 ===")

    from result_visualizer import (
        visualize_predictions, visualize_causal_matrix, visualize_causal_graph,
        visualize_causal_strength_by_frequency, visualize_causal_strength_by_time
    )

    try:
        # 测试预测结果可视化
        logger.info("测试预测结果可视化...")
        predictions = eval_results['predictions']
        targets = eval_results['targets']

        output_file = visualize_predictions(predictions, targets)
        logger.info(f"预测结果可视化已保存到: {output_file}")

        # 测试因果矩阵可视化
        logger.info("测试因果矩阵可视化...")
        channel_names = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'ECG']
        n_channels = len(channel_names)

        # 创建随机因果矩阵
        causal_matrix = np.random.rand(n_channels, n_channels) * 2 - 1  # 范围 [-1, 1]

        output_file = visualize_causal_matrix(causal_matrix, channel_names)
        logger.info(f"因果矩阵可视化已保存到: {output_file}")

        # 测试因果图可视化
        logger.info("测试因果图可视化...")
        output_file = visualize_causal_graph(causal_matrix, channel_names, threshold=0.3)
        logger.info(f"因果图可视化已保存到: {output_file}")

        # 测试频段因果强度可视化
        logger.info("测试频段因果强度可视化...")
        frequency_bands = ['delta', 'theta', 'alpha', 'beta', 'gamma']
        channel_pairs = [('Fz', 'ECG'), ('Cz', 'ECG'), ('Pz', 'ECG')]

        # 创建随机频段因果强度
        causal_strength = np.random.rand(len(channel_pairs), len(frequency_bands))

        output_file = visualize_causal_strength_by_frequency(causal_strength, frequency_bands, channel_pairs)
        logger.info(f"频段因果强度可视化已保存到: {output_file}")

        # 测试时间因果强度可视化
        logger.info("测试时间因果强度可视化...")
        time_points = np.linspace(0, 10, 100)

        # 创建随机时间因果强度
        causal_strength_time = np.random.rand(len(channel_pairs), len(time_points))

        output_file = visualize_causal_strength_by_time(causal_strength_time, time_points, channel_pairs)
        logger.info(f"时间因果强度可视化已保存到: {output_file}")

        logger.info("结果可视化模块测试成功")
        return True

    except Exception as e:
        logger.error(f"结果可视化模块测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始测试脑-心因果分析实验系统的各个模块...")

    # 创建合成数据
    data = create_synthetic_data()

    # 测试数据分段模块
    segmented_eeg, segmented_ecg = test_data_segmentation(data)
    if segmented_eeg is None or segmented_ecg is None:
        logger.error("数据分段模块测试失败，终止后续测试")
        return

    # 测试特征提取模块
    features_data = test_feature_extraction(data, segmented_eeg, segmented_ecg)
    if features_data is None:
        logger.error("特征提取模块测试失败，终止后续测试")
        return

    # 测试模型输入准备模块
    model_inputs = test_model_input_preparation(features_data)
    if model_inputs is None:
        logger.error("模型输入准备模块测试失败，终止后续测试")
        return

    # 测试CausalFormer模型训练模块
    eval_results = test_causal_former_model(model_inputs)
    if eval_results is None:
        logger.error("CausalFormer模型训练模块测试失败，终止后续测试")
        return

    # 测试结果可视化模块
    success = test_result_visualization(eval_results)
    if not success:
        logger.error("结果可视化模块测试失败")
        return

    logger.info("所有模块测试完成!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CCM分析一站式处理脚本

该脚本整合了CCM处理和可视化流程，一键完成从数据处理到结果可视化的全过程。
主要功能：
1. 检测系统配置，优化处理参数
2. 根据设置的参数执行CCM处理
3. 生成IEEE风格的可视化结果

作者: AI助手
日期: 2023年11月
"""

import os
import sys
import time
import argparse
import subprocess
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ccm_analysis_run.log')
    ]
)

logger = logging.getLogger(__name__)

def create_timestamp_folder(base_dir):
    """创建基于时间戳的目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    folder_path = os.path.join(base_dir, f"ccm_analysis_{timestamp}")
    os.makedirs(folder_path, exist_ok=True)
    return folder_path

def run_command(command):
    """运行命令并记录日志"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                               text=True)
        logger.info(f"命令执行成功，输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败，错误: {e.stderr}")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="CCM分析一站式处理脚本")
    
    # 数据选择参数
    parser.add_argument("--subjects", type=str, required=True,
                      help="要处理的被试ID列表，逗号分隔，例如'01_01,02_01'")
    
    parser.add_argument("--stages", type=str, default="test1,test2,test3",
                      help="要处理的阶段列表，逗号分隔")
    
    parser.add_argument("--channels", type=str,
                      help="要处理的通道列表，逗号分隔，默认使用所有通道")
    
    parser.add_argument("--bands", type=str, default="delta,theta,alpha,beta,gamma",
                      help="要处理的频段列表，逗号分隔")
    
    # 目录参数
    parser.add_argument("--data_dir", type=str, required=True,
                      help="数据目录路径")
    
    parser.add_argument("--output_dir", type=str, default="results/ccm",
                      help="输出目录路径")
    
    # 处理参数
    parser.add_argument("--workers", type=int,
                      help="工作线程数，默认根据系统自动确定")
    
    parser.add_argument("--use_gpu", action="store_true",
                      help="是否使用GPU（如可用）")
    
    parser.add_argument("--skip_processing", action="store_true",
                      help="跳过处理阶段，仅进行可视化（需要已有结果文件）")
    
    parser.add_argument("--skip_visualization", action="store_true",
                      help="跳过可视化阶段，仅进行数据处理")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建基于时间戳的输出目录
    output_dir = create_timestamp_folder(args.output_dir)
    logger.info(f"所有结果将保存在: {output_dir}")
    
    # 处理阶段
    if not args.skip_processing:
        logger.info("=" * 80)
        logger.info("开始CCM数据处理阶段")
        logger.info("=" * 80)
        
        # 构建命令
        cmd_parts = [
            f"python scripts/ccm_advanced_processor.py",
            f"--subjects {args.subjects}",
            f"--stages {args.stages}",
            f"--data_dir {args.data_dir}",
            f"--output_dir {output_dir}"
        ]
        
        # 添加可选参数
        if args.channels:
            cmd_parts.append(f"--channels {args.channels}")
        
        if args.bands:
            cmd_parts.append(f"--bands {args.bands}")
        
        if args.workers:
            cmd_parts.append(f"--workers {args.workers}")
        
        if args.use_gpu:
            cmd_parts.append("--use_gpu")
        
        # 组合命令并执行
        cmd = " ".join(cmd_parts)
        processing_success = run_command(cmd)
        
        if not processing_success:
            logger.error("CCM处理阶段失败，终止执行")
            sys.exit(1)
    else:
        logger.info("根据设置跳过CCM处理阶段")
    
    # 可视化阶段
    if not args.skip_visualization:
        logger.info("=" * 80)
        logger.info("开始CCM结果可视化阶段")
        logger.info("=" * 80)
        
        # 查找最新的结果文件
        result_file = os.path.join(output_dir, "ccm_results.parquet")
        
        if not os.path.exists(result_file) and args.skip_processing:
            # 如果跳过了处理阶段且找不到结果文件，尝试在输出目录下查找
            all_results = list(Path(args.output_dir).glob("**/ccm_results.parquet"))
            if all_results:
                # 使用最近修改的文件
                result_file = str(sorted(all_results, key=os.path.getmtime, reverse=True)[0])
                logger.info(f"找到最新的结果文件: {result_file}")
            else:
                logger.error("找不到结果文件，无法进行可视化")
                sys.exit(1)
        
        # 创建图表目录
        figures_dir = os.path.join(output_dir, "figures")
        os.makedirs(figures_dir, exist_ok=True)
        
        # 构建命令
        vis_cmd = (
            f"python scripts/ccm_ieee_visualization.py "
            f"--input {result_file} "
            f"--output_dir {figures_dir} "
            f"--prefix ccm"
        )
        
        # 执行可视化命令
        visualization_success = run_command(vis_cmd)
        
        if not visualization_success:
            logger.error("CCM可视化阶段失败")
            sys.exit(1)
    else:
        logger.info("根据设置跳过CCM可视化阶段")
    
    logger.info("=" * 80)
    logger.info("CCM分析流程完成")
    logger.info(f"所有结果保存在: {output_dir}")
    logger.info("=" * 80)

if __name__ == "__main__":
    # 记录开始时间
    start_time = time.time()
    
    try:
        main()
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        # 记录总用时
        elapsed_time = time.time() - start_time
        logger.info(f"总用时: {elapsed_time:.2f}秒 ({elapsed_time/60:.2f}分钟)") 
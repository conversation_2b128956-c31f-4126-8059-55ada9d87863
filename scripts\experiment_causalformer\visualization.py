#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 可视化模块

功能：
- 可视化EEG和ECG数据
- 可视化标准化结果
- 可视化异常值检测结果
- 可视化质量评估结果

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
from scipy import stats
import seaborn as sns
from matplotlib.gridspec import GridSpec

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-Visualization")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

def visualize_normalization_methods(data, sampling_rate=500, output_dir=None, stage='rest1', channel_idx=0, epoch_idx=0):
    """
    可视化不同标准化方法的效果
    
    参数:
    data (dict): 原始数据
    sampling_rate (int): 采样率
    output_dir (str): 输出目录
    stage (str): 阶段名称
    channel_idx (int): 通道索引
    epoch_idx (int): 时间段索引
    
    返回:
    str: 保存的图像文件路径
    """
    from data_normalizer import (
        z_score_normalize, min_max_normalize, robust_normalize,
        handle_outliers, detect_outliers
    )
    
    # 创建输出目录
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\normalization")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取数据
    eeg_data = data[stage]['eeg'][epoch_idx, channel_idx, :]
    channel_name = data[stage]['eeg_channels'][channel_idx]
    
    # 计算时间轴
    time = np.arange(len(eeg_data)) / sampling_rate
    
    # 应用不同的标准化方法
    z_score_data = z_score_normalize(eeg_data)
    min_max_data = min_max_normalize(eeg_data)
    robust_data = robust_normalize(eeg_data)
    
    # 应用分段标准化（30秒窗口）
    window_size = 30 * sampling_rate
    z_score_windowed = z_score_normalize(eeg_data, window_size=window_size)
    
    # 检测异常值
    outlier_mask, outlier_count, outlier_ratio = detect_outliers(eeg_data, threshold=3.0)
    
    # 处理异常值
    clipped_data = handle_outliers(eeg_data, threshold=3.0, method='clip')
    removed_data = handle_outliers(eeg_data, threshold=3.0, method='remove')
    
    # 使用自适应阈值处理异常值
    from data_normalizer import calculate_adaptive_threshold
    adaptive_threshold = calculate_adaptive_threshold(eeg_data)
    adaptive_clipped = handle_outliers(eeg_data, threshold=adaptive_threshold, method='clip')
    
    # 创建图形
    plt.figure(figsize=(15, 12))
    
    # 设置网格
    gs = GridSpec(4, 2, height_ratios=[1, 1, 1, 1])
    
    # 原始数据
    ax1 = plt.subplot(gs[0, 0])
    ax1.plot(time, eeg_data, label='原始数据')
    ax1.set_title(f'原始数据 - {channel_name}', fontproperties=chinese_font, fontsize=10)
    ax1.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax1.set_ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
    ax1.legend(prop=chinese_font)
    
    # Z-score标准化
    ax2 = plt.subplot(gs[0, 1])
    ax2.plot(time, z_score_data, label='Z-score标准化', color='orange')
    ax2.set_title('Z-score标准化 / Z-score Normalization', fontproperties=chinese_font, fontsize=10)
    ax2.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax2.set_ylabel('标准化振幅 / Normalized Amplitude', fontproperties=chinese_font, fontsize=10)
    ax2.legend(prop=chinese_font)
    
    # Min-Max标准化
    ax3 = plt.subplot(gs[1, 0])
    ax3.plot(time, min_max_data, label='Min-Max标准化', color='green')
    ax3.set_title('Min-Max标准化 / Min-Max Normalization', fontproperties=chinese_font, fontsize=10)
    ax3.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax3.set_ylabel('标准化振幅 / Normalized Amplitude', fontproperties=chinese_font, fontsize=10)
    ax3.legend(prop=chinese_font)
    
    # 稳健标准化
    ax4 = plt.subplot(gs[1, 1])
    ax4.plot(time, robust_data, label='稳健标准化', color='red')
    ax4.set_title('稳健标准化 / Robust Normalization', fontproperties=chinese_font, fontsize=10)
    ax4.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax4.set_ylabel('标准化振幅 / Normalized Amplitude', fontproperties=chinese_font, fontsize=10)
    ax4.legend(prop=chinese_font)
    
    # 分段标准化
    ax5 = plt.subplot(gs[2, 0])
    ax5.plot(time, z_score_windowed, label='分段Z-score标准化', color='purple')
    ax5.set_title('分段Z-score标准化 / Windowed Z-score', fontproperties=chinese_font, fontsize=10)
    ax5.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax5.set_ylabel('标准化振幅 / Normalized Amplitude', fontproperties=chinese_font, fontsize=10)
    ax5.legend(prop=chinese_font)
    
    # 异常值处理
    ax6 = plt.subplot(gs[2, 1])
    ax6.plot(time, eeg_data, label='原始数据', alpha=0.5)
    ax6.plot(time, clipped_data, label='截断异常值', color='cyan')
    ax6.plot(time, removed_data, label='移除异常值', color='magenta', linestyle='--')
    ax6.set_title('异常值处理 / Outlier Handling', fontproperties=chinese_font, fontsize=10)
    ax6.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax6.set_ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
    ax6.legend(prop=chinese_font)
    
    # 自适应阈值
    ax7 = plt.subplot(gs[3, 0])
    ax7.plot(time, eeg_data, label='原始数据', alpha=0.5)
    ax7.plot(time, adaptive_clipped, label=f'自适应阈值 ({adaptive_threshold:.2f}σ)', color='brown')
    ax7.set_title('自适应阈值 / Adaptive Threshold', fontproperties=chinese_font, fontsize=10)
    ax7.set_xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    ax7.set_ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
    ax7.legend(prop=chinese_font)
    
    # 分布直方图
    ax8 = plt.subplot(gs[3, 1])
    sns.histplot(eeg_data, label='原始数据', alpha=0.3, ax=ax8)
    sns.histplot(z_score_data, label='Z-score', alpha=0.3, ax=ax8, color='orange')
    sns.histplot(min_max_data, label='Min-Max', alpha=0.3, ax=ax8, color='green')
    sns.histplot(robust_data, label='稳健标准化', alpha=0.3, ax=ax8, color='red')
    ax8.set_title('数据分布 / Data Distribution', fontproperties=chinese_font, fontsize=10)
    ax8.set_xlabel('值 / Value', fontproperties=chinese_font, fontsize=10)
    ax8.set_ylabel('频数 / Frequency', fontproperties=chinese_font, fontsize=10)
    ax8.legend(prop=chinese_font)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"normalization_results_{stage}_ch{channel_idx}_ep{epoch_idx}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"标准化可视化结果已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_quality_assessment(quality_assessment, output_dir=None, stage='rest1'):
    """
    可视化标准化质量评估结果
    
    参数:
    quality_assessment (dict): 质量评估结果
    output_dir (str): 输出目录
    stage (str): 阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    # 创建输出目录
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\normalization")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取数据
    eeg_normality = quality_assessment['eeg_normality']
    eeg_p_value = quality_assessment['eeg_p_value']
    eeg_skewness = quality_assessment['eeg_skewness']
    eeg_kurtosis = quality_assessment['eeg_kurtosis']
    eeg_outlier_ratio = quality_assessment['eeg_outlier_ratio']
    
    ecg_normality = quality_assessment['ecg_normality']
    ecg_p_value = quality_assessment['ecg_p_value']
    ecg_skewness = quality_assessment['ecg_skewness']
    ecg_kurtosis = quality_assessment['ecg_kurtosis']
    ecg_outlier_ratio = quality_assessment['ecg_outlier_ratio']
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 设置网格
    gs = GridSpec(2, 2)
    
    # 正态性检验结果
    ax1 = plt.subplot(gs[0, 0])
    labels = ['EEG', 'ECG']
    normality = [eeg_normality, ecg_normality]
    colors = ['green' if n else 'red' for n in normality]
    ax1.bar(labels, [1, 1], color=colors, alpha=0.5)
    ax1.set_ylim(0, 1.2)
    for i, label in enumerate(labels):
        status = "通过" if normality[i] else "未通过"
        ax1.text(i, 1.05, status, ha='center', fontproperties=chinese_font, fontsize=10)
        ax1.text(i, 0.9, f"p={quality_assessment[f'{label.lower()}_p_value']:.4f}", ha='center')
    ax1.set_title('正态性检验 / Normality Test', fontproperties=chinese_font, fontsize=10)
    ax1.set_ylabel('状态 / Status', fontproperties=chinese_font, fontsize=10)
    
    # 偏度和峰度
    ax2 = plt.subplot(gs[0, 1])
    x = np.arange(2)
    width = 0.35
    ax2.bar(x - width/2, [eeg_skewness, ecg_skewness], width, label='偏度 / Skewness')
    ax2.bar(x + width/2, [eeg_kurtosis, ecg_kurtosis], width, label='峰度 / Kurtosis')
    ax2.set_xticks(x)
    ax2.set_xticklabels(labels)
    ax2.set_title('偏度和峰度 / Skewness & Kurtosis', fontproperties=chinese_font, fontsize=10)
    ax2.set_ylabel('值 / Value', fontproperties=chinese_font, fontsize=10)
    ax2.legend(prop=chinese_font)
    
    # 异常值比例
    ax3 = plt.subplot(gs[1, 0])
    ax3.bar(labels, [eeg_outlier_ratio, ecg_outlier_ratio], color='orange')
    ax3.set_title('异常值比例 / Outlier Ratio', fontproperties=chinese_font, fontsize=10)
    ax3.set_ylabel('比例 / Ratio', fontproperties=chinese_font, fontsize=10)
    for i, ratio in enumerate([eeg_outlier_ratio, ecg_outlier_ratio]):
        ax3.text(i, ratio + 0.01, f"{ratio:.2%}", ha='center')
    
    # 质量评分
    ax4 = plt.subplot(gs[1, 1])
    # 计算质量评分（0-100）
    eeg_score = 100 - (0 if eeg_normality else 20) - min(eeg_outlier_ratio * 100, 30) - min(abs(eeg_skewness) * 10, 30)
    ecg_score = 100 - (0 if ecg_normality else 20) - min(ecg_outlier_ratio * 100, 30) - min(abs(ecg_skewness) * 10, 30)
    
    scores = [eeg_score, ecg_score]
    colors = ['green' if s > 80 else 'orange' if s > 60 else 'red' for s in scores]
    ax4.bar(labels, scores, color=colors)
    ax4.set_title('质量评分 / Quality Score', fontproperties=chinese_font, fontsize=10)
    ax4.set_ylabel('评分 / Score', fontproperties=chinese_font, fontsize=10)
    ax4.set_ylim(0, 100)
    for i, score in enumerate(scores):
        ax4.text(i, score + 2, f"{score:.1f}", ha='center')
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"quality_assessment_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"质量评估可视化结果已保存到: {output_file}")
    
    plt.close()
    
    return output_file

if __name__ == "__main__":
    # 导入数据加载和标准化模块
    from data_loader import load_hep_data
    from channel_processor import process_channels
    from data_normalizer import normalize_data
    
    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)
    
    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)
    
    # 可视化标准化结果
    visualize_normalization_methods(processed_data)
    
    # 标准化数据
    normalized_data = normalize_data(processed_data, method='z_score')
    
    # 可视化质量评估结果
    for stage in normalized_data.keys():
        visualize_quality_assessment(normalized_data[stage]['quality_assessment'], stage=stage)

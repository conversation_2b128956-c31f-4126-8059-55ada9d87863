"""
Autoformer复现实验主脚本
"""
import os
import argparse
import subprocess
from datetime import datetime

def run_command(command, description=None):
    """运行命令并打印输出"""
    if description:
        print(f"\n===== {description} =====")
    
    print(f"执行命令: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        encoding='utf-8',
        errors='replace'
    )
    
    # 实时打印输出
    for line in process.stdout:
        print(line.strip())
    
    # 等待命令完成
    process.wait()
    
    # 检查是否有错误
    if process.returncode != 0:
        print("命令执行失败，错误信息:")
        for line in process.stderr:
            print(line.strip())
        return False
    
    return True

def setup_environment():
    """设置环境"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    setup_script = os.path.join(script_dir, "setup_environment.py")
    
    return run_command(f"python {setup_script}", "设置环境")

def prepare_data():
    """准备数据"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    data_script = os.path.join(script_dir, "download_data.py")
    
    return run_command(f"python {data_script}", "准备数据")

def fix_autoformer():
    """修复Autoformer代码"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    fix_script = os.path.join(script_dir, "fix_autoformer.py")
    
    return run_command(f"python {fix_script}", "修复Autoformer代码")

def run_autoformer(args):
    """运行Autoformer模型"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    run_script = os.path.join(script_dir, "run_autoformer.py")
    
    # 构建命令
    command = f"python {run_script} "
    command += f"--data {args.data} "
    command += f"--features {args.features} "
    command += f"--seq_len {args.seq_len} "
    command += f"--label_len {args.label_len} "
    command += f"--pred_len {args.pred_len} "
    command += f"--use_gpu {args.use_gpu} "
    command += f"--gpu {args.gpu} "
    command += f"--des {args.des} "
    
    if args.skip_training:
        command += "--skip_training "
    
    return run_command(command, "运行Autoformer模型")

def compare_datasets(args):
    """比较不同数据集上的性能"""
    if not args.compare_datasets:
        return True
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    compare_script = os.path.join(script_dir, "compare_datasets.py")
    
    # 构建命令
    command = f"python {compare_script} "
    command += f"--datasets {' '.join(args.datasets)} "
    command += f"--features {args.features} "
    command += f"--seq_len {args.seq_len} "
    command += f"--label_len {args.label_len} "
    command += f"--pred_len {args.pred_len} "
    command += f"--use_gpu {args.use_gpu} "
    command += f"--gpu {args.gpu} "
    
    if args.skip_training:
        command += "--skip_training "
    
    return run_command(command, "比较不同数据集上的性能")

def analyze_models(args):
    """分析模型结构"""
    if not args.analyze_models:
        return True
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    analyze_script = os.path.join(script_dir, "analyze_models.py")
    
    return run_command(f"python {analyze_script}", "分析模型结构")

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Autoformer复现实验')
    
    # 基本参数
    parser.add_argument('--data', type=str, default='ETTh1', help='数据集名称: ETTh1, ETTh2, ETTm1, ETTm2, synthetic')
    parser.add_argument('--features', type=str, default='M', help='预测特征: M(多变量), S(单变量)')
    
    # 序列长度参数
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--label_len', type=int, default=48, help='标签序列长度')
    parser.add_argument('--pred_len', type=int, default=24, help='预测序列长度')
    
    # 硬件参数
    parser.add_argument('--use_gpu', type=bool, default=True, help='是否使用GPU')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    # 运行控制参数
    parser.add_argument('--skip_training', action='store_true', help='如果存在已训练的模型，则跳过训练阶段')
    parser.add_argument('--skip_setup', action='store_true', help='跳过环境设置')
    parser.add_argument('--skip_data_prep', action='store_true', help='跳过数据准备')
    parser.add_argument('--skip_fix', action='store_true', help='跳过代码修复')
    
    # 比较和分析参数
    parser.add_argument('--compare_datasets', action='store_true', help='比较不同数据集上的性能')
    parser.add_argument('--datasets', nargs='+', default=['ETTh1', 'ETTh2', 'ETTm1', 'ETTm2'],
                        help='要比较的数据集列表')
    parser.add_argument('--analyze_models', action='store_true', help='分析模型结构')
    
    # 其他参数
    parser.add_argument('--des', type=str, default='experiment', help='实验描述')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 记录开始时间
    start_time = datetime.now()
    print(f"实验开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置环境
    if not args.skip_setup:
        if not setup_environment():
            print("环境设置失败，实验终止")
            return
    
    # 准备数据
    if not args.skip_data_prep:
        if not prepare_data():
            print("数据准备失败，实验终止")
            return
    
    # 修复Autoformer代码
    if not args.skip_fix:
        if not fix_autoformer():
            print("代码修复失败，实验终止")
            return
    
    # 运行Autoformer模型
    if not run_autoformer(args):
        print("Autoformer运行失败，但继续执行后续步骤")
    
    # 比较不同数据集上的性能
    if args.compare_datasets:
        if not compare_datasets(args):
            print("数据集比较失败，但继续执行后续步骤")
    
    # 分析模型结构
    if args.analyze_models:
        if not analyze_models(args):
            print("模型分析失败，但继续执行后续步骤")
    
    # 记录结束时间
    end_time = datetime.now()
    duration = end_time - start_time
    print(f"\n实验结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总耗时: {duration}")
    
    print("\nAutoformer复现实验完成!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 数据加载模块

功能：
- 从HEP数据集中加载EEG和ECG数据
- 解析数据文件的元数据（通道信息、采样率、被试信息等）
- 提取实验阶段信息（rest1, rest2, rest3, test1, test2, test3）
- 验证数据完整性和格式

作者：AI助手
日期：2024年
"""

import os
import sys
import glob
import h5py
import numpy as np
import pandas as pd
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_data_loader_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-DataLoader")

class DataLoadError(Exception):
    """数据加载错误的自定义异常类"""
    pass

def validate_file_path(file_path):
    """
    验证文件路径是否存在且格式正确

    参数:
    file_path (str): 文件路径

    返回:
    bool: 验证结果

    异常:
    DataLoadError: 文件路径不存在或格式不正确
    """
    if not os.path.exists(file_path):
        error_msg = f"文件路径不存在: {file_path}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

    if not file_path.endswith('.h5'):
        error_msg = f"文件格式不正确，应为h5格式: {file_path}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

    return True

def extract_stage_info(file_name):
    """
    从文件名中提取实验阶段信息

    参数:
    file_name (str): 文件名

    返回:
    str: 实验阶段
    """
    stage_mapping = {
        "rest1": "rest1",
        "rest2": "rest2",
        "rest3": "rest3",
        "test1": "test1",
        "test2": "test2",
        "test3": "test3",
        "prac": "practice"
    }

    for key, value in stage_mapping.items():
        if key in file_name.lower():
            return value

    return "unknown"

def load_h5_file(file_path):
    """
    加载h5文件并提取数据

    参数:
    file_path (str): 文件路径

    返回:
    dict: 包含数据和元数据的字典

    异常:
    DataLoadError: 文件读取失败或数据不完整
    """
    try:
        with h5py.File(file_path, 'r') as f:
            # 检查必要的数据集是否存在
            required_datasets = ['data', 'times', 'ch_names']
            for dataset in required_datasets:
                if dataset not in f:
                    error_msg = f"文件缺少必要的数据集 '{dataset}': {file_path}"
                    logger.error(error_msg)
                    raise DataLoadError(error_msg)

            # 提取数据
            data = f['data'][:]
            times = f['times'][:]
            ch_names = [ch.decode('utf-8') if isinstance(ch, bytes) else ch for ch in f['ch_names'][:]]

            # 提取被试ID（如果存在）
            subject_ids = None
            if 'subject_ids' in f:
                subject_ids = [subj.decode('utf-8') if isinstance(subj, bytes) else subj for subj in f['subject_ids'][:]]

            # 验证数据维度
            if len(data.shape) != 3:
                error_msg = f"数据维度不正确，应为3维 [n_epochs, n_channels, n_times]: {data.shape}"
                logger.error(error_msg)
                raise DataLoadError(error_msg)

            # 验证通道数量与通道名称数量是否匹配
            if data.shape[1] != len(ch_names):
                error_msg = f"通道数量与通道名称数量不匹配: {data.shape[1]} vs {len(ch_names)}"
                logger.error(error_msg)
                raise DataLoadError(error_msg)

            # 验证时间点数量与times长度是否匹配
            if data.shape[2] != len(times):
                error_msg = f"时间点数量与times长度不匹配: {data.shape[2]} vs {len(times)}"
                logger.error(error_msg)
                raise DataLoadError(error_msg)

            # 构建返回数据
            result = {
                'data': data,
                'times': times,
                'ch_names': ch_names,
                'subject_ids': subject_ids,
                'n_epochs': data.shape[0],
                'n_channels': data.shape[1],
                'n_times': data.shape[2],
                'sfreq': 1.0 / (times[1] - times[0]) if len(times) > 1 else None
            }

            return result

    except (IOError, OSError) as e:
        error_msg = f"文件读取失败: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)
    except Exception as e:
        error_msg = f"处理文件时发生未知错误: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

def classify_eeg_channels(eeg_channels):
    """
    按照10-20系统标准对EEG通道进行分类

    参数:
    eeg_channels (list): EEG通道名称列表

    返回:
    dict: 分类后的通道字典
    """
    # 定义各类通道（同时包含新旧命名）
    midline_channels = ['Fz', 'Cz', 'Pz', 'Oz', 'FCz', 'CPz', 'POz', 'AFz', 'Fpz', 'Nz']

    left_hemisphere_channels = [
        'Fp1', 'F3', 'C3', 'P3', 'O1', 'F7', 'T3', 'T5', 'FC1', 'CP1', 'FC5', 'CP5',
        'AF3', 'F1', 'C1', 'P1', 'PO3', 'AF7', 'F5', 'C5', 'P5', 'PO7', 'FT7', 'TP7', 'PO9',
        # 新命名系统
        'T7', 'P7', 'FC3', 'CP3', 'FT9', 'TP9'
    ]

    right_hemisphere_channels = [
        'Fp2', 'F4', 'C4', 'P4', 'O2', 'F8', 'T4', 'T6', 'FC2', 'CP2', 'FC6', 'CP6',
        'AF4', 'F2', 'C2', 'P2', 'PO4', 'AF8', 'F6', 'C6', 'P6', 'PO8', 'FT8', 'TP8', 'PO10',
        # 新命名系统
        'T8', 'P8', 'FC4', 'CP4', 'FT10', 'TP10'
    ]

    # 创建新旧命名映射
    naming_map = {
        'T7': 'T3', 'T8': 'T4', 'P7': 'T5', 'P8': 'T6',
        'T3': 'T7', 'T4': 'T8', 'T5': 'P7', 'T6': 'P8'
    }

    # 初始化分类结果
    classified = {
        'midline': [],
        'left_hemisphere': [],
        'right_hemisphere': [],
        'other': []
    }

    # 过滤掉非EEG通道（如EOG、EMG等）
    filtered_channels = []
    for ch in eeg_channels:
        if 'EOG' in ch.upper() or 'EMG' in ch.upper():
            classified['other'].append(ch)
        else:
            filtered_channels.append(ch)

    # 分类通道
    for ch in filtered_channels:
        ch_upper = ch.upper()
        classified_flag = False

        # 特殊处理FCz和AFz通道（大小写不敏感）
        if ch_upper == 'FCZ':
            classified['midline'].append(ch)
            classified_flag = True
        elif ch_upper == 'AFZ':
            classified['midline'].append(ch)
            classified_flag = True

        if classified_flag:
            continue

        # 检查中线导联
        for mid in midline_channels:
            if mid.upper() == ch_upper:
                classified['midline'].append(ch)
                classified_flag = True
                break

        if classified_flag:
            continue

        # 检查左半球导联
        for left in left_hemisphere_channels:
            if left.upper() == ch_upper:
                classified['left_hemisphere'].append(ch)
                classified_flag = True
                break

        if classified_flag:
            continue

        # 检查右半球导联
        for right in right_hemisphere_channels:
            if right.upper() == ch_upper:
                classified['right_hemisphere'].append(ch)
                classified_flag = True
                break

        if classified_flag:
            continue

        # 检查命名映射
        if ch in naming_map:
            alt_name = naming_map[ch]
            alt_name_upper = alt_name.upper()

            # 检查中线导联
            for mid in midline_channels:
                if mid.upper() == alt_name_upper:
                    classified['midline'].append(ch)
                    classified_flag = True
                    break

            if classified_flag:
                continue

            # 检查左半球导联
            for left in left_hemisphere_channels:
                if left.upper() == alt_name_upper:
                    classified['left_hemisphere'].append(ch)
                    classified_flag = True
                    break

            if classified_flag:
                continue

            # 检查右半球导联
            for right in right_hemisphere_channels:
                if right.upper() == alt_name_upper:
                    classified['right_hemisphere'].append(ch)
                    classified_flag = True
                    break

        # 如果仍未分类，尝试基于通道名称的模式进行分类
        if not classified_flag:
            # 中线通道通常以'z'结尾
            if ch_upper.endswith('Z'):
                classified['midline'].append(ch)
            # 左半球通道通常以奇数结尾
            elif any(ch_upper.endswith(str(i)) for i in range(1, 10, 2)):
                classified['left_hemisphere'].append(ch)
            # 右半球通道通常以偶数结尾
            elif any(ch_upper.endswith(str(i)) for i in range(2, 11, 2)):
                classified['right_hemisphere'].append(ch)
            else:
                classified['other'].append(ch)

    return classified

def verify_channel_identification(ch_names, identified_channels):
    """
    验证通道识别的准确性

    参数:
    ch_names (list): 原始通道名称列表
    identified_channels (dict): 识别后的通道信息

    返回:
    dict: 验证结果
    """
    # 标准10-20系统脑电极位置名称（包含新旧命名）
    standard_eeg_patterns = [
        # 基本10-20系统
        'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
        'F7', 'F8', 'Fz', 'Cz', 'Pz', 'Oz', 'Fpz',
        # 扩展10-20系统
        'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6',
        'AF3', 'AF4', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2',
        'PO3', 'PO4', 'FCz', 'CPz', 'POz', 'AFz',
        'AF7', 'AF8', 'F5', 'F6', 'FC3', 'FC4', 'C5', 'C6', 'CP3', 'CP4',
        'P5', 'P6', 'PO7', 'PO8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO9', 'PO10',
        # 旧命名
        'T3', 'T4', 'T5', 'T6',
        # 新命名
        'T7', 'T8', 'P7', 'P8',
        # 额外通道
        'FT9', 'FT10', 'TP9', 'TP10', 'Nz'
    ]

    # 关键通道（必须正确识别）
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']

    # 获取识别到的EEG通道名称
    identified_eeg = identified_channels['eeg_names']

    # 统计识别结果
    identified_standard = []
    unidentified = []
    ambiguous = []
    identified_key = []
    missing_key = []

    # 创建新旧命名映射
    naming_map = {
        'T7': 'T3', 'T8': 'T4', 'P7': 'T5', 'P8': 'T6',
        'T3': 'T7', 'T4': 'T8', 'T5': 'P7', 'T6': 'P8'
    }

    # 检查标准通道识别情况
    for pattern in standard_eeg_patterns:
        pattern_upper = pattern.upper()
        found = False
        ambiguous_match = False

        # 直接匹配
        for ch in identified_eeg:
            ch_upper = ch.upper()
            if pattern_upper == ch_upper:
                identified_standard.append(pattern)
                found = True
                break
            elif pattern_upper in ch_upper and pattern_upper != ch_upper:
                ambiguous.append((pattern, ch))
                ambiguous_match = True

        # 如果没有直接匹配，检查命名映射
        if not found and not ambiguous_match and pattern in naming_map:
            alt_name = naming_map[pattern]
            alt_name_upper = alt_name.upper()

            for ch in identified_eeg:
                ch_upper = ch.upper()
                if alt_name_upper == ch_upper:
                    identified_standard.append(pattern)
                    found = True
                    break
                elif alt_name_upper in ch_upper and alt_name_upper != ch_upper:
                    ambiguous.append((pattern, ch))
                    ambiguous_match = True

        if not found and not ambiguous_match:
            # 检查是否是新旧命名冲突
            is_naming_conflict = False
            for old_name, new_name in naming_map.items():
                if pattern == old_name and new_name in identified_eeg:
                    is_naming_conflict = True
                    break
                elif pattern == new_name and old_name in identified_eeg:
                    is_naming_conflict = True
                    break

            if not is_naming_conflict:
                unidentified.append(pattern)

    # 检查关键通道识别情况
    for key in key_channels:
        key_upper = key.upper()
        found = False

        for ch in identified_eeg:
            ch_upper = ch.upper()
            if key_upper == ch_upper or key_upper in ch_upper:
                identified_key.append(key)
                found = True
                break

        if not found:
            missing_key.append(key)

    # 计算统计信息
    total_standard = len(standard_eeg_patterns)
    identified_count = len(identified_standard)
    unidentified_count = len(unidentified)
    ambiguous_count = len(ambiguous)

    # 准备验证结果
    verification_result = {
        'total_standard': total_standard,
        'identified_count': identified_count,
        'unidentified_count': unidentified_count,
        'ambiguous_count': ambiguous_count,
        'identified_standard': identified_standard,
        'unidentified': unidentified,
        'ambiguous': ambiguous,
        'identified_key': identified_key,
        'missing_key': missing_key,
        'success_rate': identified_count / total_standard if total_standard > 0 else 0,
        'key_channel_success_rate': len(identified_key) / len(key_channels) if key_channels else 0
    }

    return verification_result

def identify_channel_types(ch_names):
    """
    识别通道类型（EEG、ECG等）

    参数:
    ch_names (list): 通道名称列表

    返回:
    dict: 包含各类型通道索引的字典
    """
    # 打印完整的通道名称列表，帮助调试
    logger.info(f"通道名称列表: {ch_names}")

    # 标准10-20系统脑电极位置名称（包含新旧命名）
    standard_eeg_patterns = [
        # 基本10-20系统
        'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
        'F7', 'F8', 'Fz', 'Cz', 'Pz', 'Oz', 'Fpz',
        # 扩展10-20系统
        'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6',
        'AF3', 'AF4', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2',
        'PO3', 'PO4', 'FCz', 'CPz', 'POz', 'AFz',
        'AF7', 'AF8', 'F5', 'F6', 'FC3', 'FC4', 'C5', 'C6', 'CP3', 'CP4',
        'P5', 'P6', 'PO7', 'PO8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO9', 'PO10',
        # 旧命名
        'T3', 'T4', 'T5', 'T6',
        # 新命名
        'T7', 'T8', 'P7', 'P8',
        # 额外通道
        'FT9', 'FT10', 'TP9', 'TP10', 'Nz'
    ]

    # ECG相关通道名称模式
    ecg_patterns = ['ECG', 'EKG', 'CARDIO', 'HEART']

    eeg_indices = []
    ecg_indices = []
    other_indices = []

    # 记录每个通道的匹配模式，用于详细报告
    channel_match_info = {}

    # 创建新旧命名映射
    naming_map = {
        'T7': 'T3', 'T8': 'T4', 'P7': 'T5', 'P8': 'T6',
        'T3': 'T7', 'T4': 'T8', 'T5': 'P7', 'T6': 'P8'
    }

    for i, ch in enumerate(ch_names):
        ch_str = str(ch).upper()  # 转换为字符串并大写，确保一致性

        # 检查是否是标准EEG通道名称
        is_eeg = False
        matched_pattern = None

        # 直接匹配
        for pattern in standard_eeg_patterns:
            if pattern.upper() == ch_str:
                eeg_indices.append(i)
                is_eeg = True
                matched_pattern = pattern
                break

        # 如果没有直接匹配，检查部分匹配
        if not is_eeg:
            for pattern in standard_eeg_patterns:
                if pattern.upper() in ch_str:
                    eeg_indices.append(i)
                    is_eeg = True
                    matched_pattern = pattern
                    break

        # 特殊处理FCz和AFz通道（这些通道在某些数据集中可能有特殊命名）
        if not is_eeg:
            if ch_str == 'FCZ':
                eeg_indices.append(i)
                is_eeg = True
                matched_pattern = 'FCz'
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': 'FCz', 'match_type': 'case_insensitive'}
            elif ch_str == 'AFZ':
                eeg_indices.append(i)
                is_eeg = True
                matched_pattern = 'AFz'
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': 'AFz', 'match_type': 'case_insensitive'}
            # 检查通道名称是否为FCz或AFz（不区分大小写）
            elif ch_str.upper() == 'FCZ':
                eeg_indices.append(i)
                is_eeg = True
                matched_pattern = 'FCz'
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': 'FCz', 'match_type': 'case_insensitive'}
            elif ch_str.upper() == 'AFZ':
                eeg_indices.append(i)
                is_eeg = True
                matched_pattern = 'AFz'
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': 'AFz', 'match_type': 'case_insensitive'}

        # 如果仍然没有匹配，检查命名映射
        if not is_eeg and ch in naming_map:
            alt_name = naming_map[ch]
            for pattern in standard_eeg_patterns:
                if pattern.upper() == alt_name.upper():
                    eeg_indices.append(i)
                    is_eeg = True
                    matched_pattern = pattern
                    channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': matched_pattern, 'match_type': 'alternative_naming'}
                    break

        if is_eeg:
            if ch not in channel_match_info:  # 避免覆盖已设置的映射信息
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': matched_pattern, 'match_type': 'standard'}
            continue

        # 检查是否是ECG通道
        is_ecg = False
        matched_pattern = None

        for pattern in ecg_patterns:
            if pattern in ch_str:
                ecg_indices.append(i)
                is_ecg = True
                matched_pattern = pattern
                break

        if is_ecg:
            channel_match_info[ch] = {'type': 'ecg', 'matched_pattern': matched_pattern, 'match_type': 'standard'}
            continue

        # 如果不是已知的EEG或ECG通道，检查其他常见模式
        if 'EEG' in ch_str:
            eeg_indices.append(i)
            channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': 'EEG', 'match_type': 'prefix'}
        elif any(p in ch_str for p in ['E', 'EL', 'ELECTRODE']) and not any(p in ch_str for p in ecg_patterns):
            # 检查是否是EOG或EMG通道
            if 'EOG' in ch_str or 'EMG' in ch_str:
                other_indices.append(i)
                channel_match_info[ch] = {'type': 'other', 'matched_pattern': None, 'match_type': 'non_eeg_electrode'}
            else:
                # 如果包含电极相关词汇但不是ECG/EOG/EMG，可能是EEG
                eeg_indices.append(i)
                matched_prefix = next((p for p in ['E', 'EL', 'ELECTRODE'] if p in ch_str), None)
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': matched_prefix, 'match_type': 'electrode_keyword'}
        else:
            other_indices.append(i)
            channel_match_info[ch] = {'type': 'other', 'matched_pattern': None, 'match_type': 'unknown'}

    # 如果没有找到EEG通道，尝试使用启发式方法
    if not eeg_indices:
        logger.warning("未找到标准EEG通道名称，尝试使用启发式方法识别...")

        # 假设非ECG通道都是EEG通道
        for i, ch in enumerate(ch_names):
            if i not in ecg_indices and i not in eeg_indices:
                eeg_indices.append(i)
                channel_match_info[ch] = {'type': 'eeg', 'matched_pattern': None, 'match_type': 'heuristic'}

        if not eeg_indices:
            error_msg = "无法识别任何EEG通道，请检查通道命名"
            logger.error(error_msg)
            raise DataLoadError(error_msg)
        else:
            logger.info(f"使用启发式方法识别出 {len(eeg_indices)} 个EEG通道")
    else:
        logger.info(f"识别出 {len(eeg_indices)} 个标准EEG通道")

    # 如果没有找到ECG通道，尝试使用启发式方法
    if not ecg_indices:
        logger.warning("未找到标准ECG通道名称，尝试使用启发式方法识别...")

        # 查找可能包含"C"的通道（可能是心脏相关）
        for i, ch in enumerate(ch_names):
            ch_str = str(ch).upper()
            if 'C' in ch_str and i not in eeg_indices and i not in ecg_indices:
                ecg_indices.append(i)
                channel_match_info[ch] = {'type': 'ecg', 'matched_pattern': 'C', 'match_type': 'heuristic'}

        if not ecg_indices:
            error_msg = "无法识别任何ECG通道，请检查通道命名"
            logger.error(error_msg)
            raise DataLoadError(error_msg)
        else:
            logger.info(f"使用启发式方法识别出 {len(ecg_indices)} 个ECG通道")
    else:
        logger.info(f"识别出 {len(ecg_indices)} 个标准ECG通道")

    # 准备结果
    result = {
        'eeg': eeg_indices,
        'ecg': ecg_indices,
        'other': other_indices,
        'eeg_names': [ch_names[i] for i in eeg_indices],
        'ecg_names': [ch_names[i] for i in ecg_indices],
        'other_names': [ch_names[i] for i in other_indices],
        'channel_match_info': channel_match_info
    }

    # 生成详细的通道识别报告
    generate_channel_identification_report(ch_names, result)

    # 验证通道识别的准确性
    verification_result = verify_channel_identification(ch_names, result)

    # 添加验证结果到返回值
    result['verification'] = verification_result

    return result

def generate_channel_identification_report(ch_names, identification_result):
    """
    生成详细的通道识别报告

    参数:
    ch_names (list): 原始通道名称列表
    identification_result (dict): 识别结果
    """
    eeg_names = identification_result['eeg_names']
    ecg_names = identification_result['ecg_names']
    other_names = identification_result['other_names']
    channel_match_info = identification_result['channel_match_info']

    # 按照10-20系统标准对EEG通道进行分类
    classified_eeg = classify_eeg_channels(eeg_names)

    # 打印完整的EEG通道列表
    logger.info("===== 详细通道识别报告 =====")
    logger.info(f"总通道数: {len(ch_names)}")
    logger.info(f"识别出的EEG通道数: {len(eeg_names)}")
    logger.info(f"识别出的ECG通道数: {len(ecg_names)}")
    logger.info(f"其他通道数: {len(other_names)}")

    # 打印分类后的EEG通道
    logger.info("\n----- EEG通道分类 -----")
    logger.info(f"中线导联 ({len(classified_eeg['midline'])}): {classified_eeg['midline']}")
    logger.info(f"左半球导联 ({len(classified_eeg['left_hemisphere'])}): {classified_eeg['left_hemisphere']}")
    logger.info(f"右半球导联 ({len(classified_eeg['right_hemisphere'])}): {classified_eeg['right_hemisphere']}")
    if classified_eeg['other']:
        logger.info(f"其他EEG导联 ({len(classified_eeg['other'])}): {classified_eeg['other']}")

    # 打印关键通道识别情况
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    identified_key = [ch for ch in eeg_names if any(key.upper() == ch.upper() or key.upper() in ch.upper() for key in key_channels)]
    missing_key = [key for key in key_channels if not any(key.upper() == ch.upper() or key.upper() in ch.upper() for ch in eeg_names)]

    logger.info("\n----- 关键通道识别情况 -----")
    logger.info(f"已识别关键通道 ({len(identified_key)}): {identified_key}")
    if missing_key:
        logger.warning(f"未识别关键通道 ({len(missing_key)}): {missing_key}")
    else:
        logger.info("所有关键通道均已识别")

    # 打印完整的ECG通道列表
    logger.info("\n----- ECG通道列表 -----")
    logger.info(f"ECG通道 ({len(ecg_names)}): {ecg_names}")

    # 打印通道匹配详情
    logger.info("\n----- 通道匹配详情 -----")
    match_types = {}
    for ch, info in channel_match_info.items():
        match_type = info['match_type']
        if match_type not in match_types:
            match_types[match_type] = []
        match_types[match_type].append((ch, info['matched_pattern']))

    for match_type, channels in match_types.items():
        logger.info(f"{match_type} 匹配 ({len(channels)}): {channels[:10]}{'...' if len(channels) > 10 else ''}")

    logger.info("===== 通道识别报告结束 =====\n")

def load_hep_data(data_dir):
    """
    加载HEP数据集

    参数:
    data_dir (str): 数据目录

    返回:
    dict: 包含各阶段数据的字典

    异常:
    DataLoadError: 数据加载失败
    """
    # 验证数据目录是否存在
    if not os.path.exists(data_dir):
        error_msg = f"数据目录不存在: {data_dir}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

    # 查找所有h5文件
    h5_files = glob.glob(os.path.join(data_dir, "*.h5"))

    if not h5_files:
        error_msg = f"未找到h5文件: {data_dir}"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

    logger.info(f"找到 {len(h5_files)} 个h5文件")

    # 存储各阶段数据
    stages_data = {}

    for file_path in h5_files:
        # 验证文件路径
        validate_file_path(file_path)

        # 提取阶段信息
        file_name = os.path.basename(file_path)
        stage = extract_stage_info(file_name)

        logger.info(f"处理文件: {file_name}, 阶段: {stage}")

        # 加载数据
        file_data = load_h5_file(file_path)

        # 识别通道类型
        channel_types = identify_channel_types(file_data['ch_names'])

        # 提取EEG和ECG数据
        eeg_data = file_data['data'][:, channel_types['eeg'], :]
        ecg_data = file_data['data'][:, channel_types['ecg'], :]

        # 计算R波位置（假设在时间序列中间）
        r_peaks = [int(file_data['n_times'] / 2) for _ in range(file_data['n_epochs'])]

        # 存储数据
        stages_data[stage] = {
            'eeg': eeg_data,
            'ecg': ecg_data,
            'eeg_channels': channel_types['eeg_names'],
            'ecg_channels': channel_types['ecg_names'],
            'times': file_data['times'],
            'r_peaks': r_peaks,
            'subject_ids': file_data['subject_ids'],
            'sfreq': file_data['sfreq'],
            'n_epochs': file_data['n_epochs'],
            'n_eeg_channels': len(channel_types['eeg']),
            'n_ecg_channels': len(channel_types['ecg']),
            'n_times': file_data['n_times']
        }

        logger.info(f"成功加载 {stage} 阶段数据:")
        logger.info(f"  - 样本数: {file_data['n_epochs']}")
        logger.info(f"  - EEG通道数: {len(channel_types['eeg'])}")
        logger.info(f"  - ECG通道数: {len(channel_types['ecg'])}")
        logger.info(f"  - 时间点数: {file_data['n_times']}")
        logger.info(f"  - 采样率: {file_data['sfreq']} Hz")

    # 验证是否加载了数据
    if not stages_data:
        error_msg = "未成功加载任何数据"
        logger.error(error_msg)
        raise DataLoadError(error_msg)

    # 验证各阶段数据的一致性
    validate_stages_consistency(stages_data)

    logger.info(f"成功加载 {len(stages_data)} 个阶段的数据")

    return stages_data

def validate_stages_consistency(stages_data):
    """
    验证各阶段数据的一致性

    参数:
    stages_data (dict): 各阶段数据

    异常:
    DataLoadError: 数据不一致
    """
    if len(stages_data) <= 1:
        return

    # 获取第一个阶段的数据作为参考
    reference_stage = next(iter(stages_data.values()))
    reference_sfreq = reference_stage['sfreq']
    reference_n_times = reference_stage['n_times']

    # 验证各阶段的采样率和时间点数是否一致
    for stage, data in stages_data.items():
        if data['sfreq'] != reference_sfreq:
            error_msg = f"阶段 {stage} 的采样率与参考不一致: {data['sfreq']} vs {reference_sfreq}"
            logger.error(error_msg)
            raise DataLoadError(error_msg)

        if data['n_times'] != reference_n_times:
            error_msg = f"阶段 {stage} 的时间点数与参考不一致: {data['n_times']} vs {reference_n_times}"
            logger.error(error_msg)
            raise DataLoadError(error_msg)

def test_data_loader(data_dir):
    """
    测试数据加载模块

    参数:
    data_dir (str): 数据目录
    """
    logger.info("开始测试数据加载模块...")

    try:
        # 加载数据
        stages_data = load_hep_data(data_dir)

        # 打印各阶段数据的基本信息
        for stage, data in stages_data.items():
            logger.info(f"阶段: {stage}")
            logger.info(f"  - 样本数: {data['n_epochs']}")
            logger.info(f"  - EEG通道数: {data['n_eeg_channels']}")
            logger.info(f"  - ECG通道数: {data['n_ecg_channels']}")
            logger.info(f"  - 时间点数: {data['n_times']}")
            logger.info(f"  - 采样率: {data['sfreq']} Hz")
            logger.info(f"  - EEG数据形状: {data['eeg'].shape}")
            logger.info(f"  - ECG数据形状: {data['ecg'].shape}")

        logger.info("数据加载模块测试成功!")
        return True

    except DataLoadError as e:
        logger.error(f"数据加载失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试数据加载模块
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    test_data_loader(data_dir)

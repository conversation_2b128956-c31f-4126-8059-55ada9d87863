<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="720pt" height="432pt" viewBox="0 0 720 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:38.467884</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 720 432 
L 720 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 51.24875 394.495 
L 709.2 394.495 
L 709.2 25.05375 
L 51.24875 25.05375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 81.155625 394.495 
L 81.155625 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="me2b1c60609" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me2b1c60609" x="81.155625" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- -0.2 -->
      <g transform="translate(73.155625 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-2d" d="M 2975 2125 
L 125 2125 
L 125 2525 
L 2975 2525 
L 2975 2125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-2e" d="M 1075 125 
L 500 125 
L 500 675 
L 1075 675 
L 1075 125 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-32" x="150"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 180.845208 394.495 
L 180.845208 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#me2b1c60609" x="180.845208" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 0.0 -->
      <g transform="translate(174.845208 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 280.534792 394.495 
L 280.534792 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#me2b1c60609" x="280.534792" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.2 -->
      <g transform="translate(274.534792 406.995) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-32" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 380.224375 394.495 
L 380.224375 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#me2b1c60609" x="380.224375" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.4 -->
      <g transform="translate(374.224375 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-34" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 479.913958 394.495 
L 479.913958 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#me2b1c60609" x="479.913958" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0.6 -->
      <g transform="translate(473.913958 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-36" d="M 250 1612 
Q 275 1975 387 2225 
Q 500 2475 725 2850 
L 1750 4450 
L 2325 4450 
L 1275 2800 
Q 1950 2975 2350 2750 
Q 2750 2525 2887 2237 
Q 3025 1950 3037 1612 
Q 3050 1275 2937 950 
Q 2825 625 2537 362 
Q 2250 100 1737 75 
Q 1225 50 862 262 
Q 500 475 362 862 
Q 225 1250 250 1612 
z
M 1025 787 
Q 1250 550 1625 525 
Q 2000 500 2250 775 
Q 2500 1050 2500 1575 
Q 2500 2100 2187 2300 
Q 1875 2500 1487 2450 
Q 1100 2400 925 2075 
Q 750 1750 775 1387 
Q 800 1025 1025 787 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-36" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 579.603542 394.495 
L 579.603542 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#me2b1c60609" x="579.603542" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 0.8 -->
      <g transform="translate(573.603542 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-38" d="M 175 1375 
Q 175 1675 325 1962 
Q 475 2250 825 2425 
Q 525 2600 425 2812 
Q 325 3025 312 3300 
Q 300 3575 387 3775 
Q 475 3975 650 4150 
Q 825 4325 1037 4387 
Q 1250 4450 1500 4450 
Q 1750 4450 1950 4400 
Q 2150 4350 2375 4187 
Q 2600 4025 2700 3725 
Q 2800 3425 2687 3025 
Q 2575 2625 2100 2400 
Q 2525 2275 2700 2012 
Q 2875 1750 2875 1375 
Q 2875 1000 2762 775 
Q 2650 550 2512 400 
Q 2375 250 2137 162 
Q 1900 75 1537 75 
Q 1175 75 912 162 
Q 650 250 475 425 
Q 300 600 237 837 
Q 175 1075 175 1375 
z
M 687 1400 
Q 675 1100 787 875 
Q 900 650 1200 587 
Q 1500 525 1825 600 
Q 2150 675 2275 950 
Q 2400 1225 2362 1500 
Q 2325 1775 2050 1962 
Q 1775 2150 1450 2125 
Q 1125 2100 912 1900 
Q 700 1700 687 1400 
z
M 775 3350 
Q 775 3100 950 2875 
Q 1125 2650 1500 2650 
Q 1875 2650 2062 2875 
Q 2250 3100 2237 3412 
Q 2225 3725 2012 3875 
Q 1800 4025 1437 4000 
Q 1075 3975 925 3787 
Q 775 3600 775 3350 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-38" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 679.293125 394.495 
L 679.293125 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#me2b1c60609" x="679.293125" y="394.495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 1.0 -->
      <g transform="translate(673.293125 406.995) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <defs>
       <path id="m1841778106" d="M 0 0 
L 0 2 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m1841778106" x="56.233229" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m1841778106" x="106.078021" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m1841778106" x="131.000417" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_18">
      <g>
       <use xlink:href="#m1841778106" x="155.922813" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_19">
      <g>
       <use xlink:href="#m1841778106" x="205.767604" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_20">
      <g>
       <use xlink:href="#m1841778106" x="230.69" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_21">
      <g>
       <use xlink:href="#m1841778106" x="255.612396" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_22">
      <g>
       <use xlink:href="#m1841778106" x="305.457188" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_23">
      <g>
       <use xlink:href="#m1841778106" x="330.379583" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_24">
      <g>
       <use xlink:href="#m1841778106" x="355.301979" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_25">
      <g>
       <use xlink:href="#m1841778106" x="405.146771" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_26">
      <g>
       <use xlink:href="#m1841778106" x="430.069167" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_27">
      <g>
       <use xlink:href="#m1841778106" x="454.991563" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_28">
      <g>
       <use xlink:href="#m1841778106" x="504.836354" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_29">
      <g>
       <use xlink:href="#m1841778106" x="529.75875" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_30">
      <g>
       <use xlink:href="#m1841778106" x="554.681146" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_31">
      <g>
       <use xlink:href="#m1841778106" x="604.525938" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_32">
      <g>
       <use xlink:href="#m1841778106" x="629.448333" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_33">
      <g>
       <use xlink:href="#m1841778106" x="654.370729" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_34">
      <g>
       <use xlink:href="#m1841778106" x="704.215521" y="394.495" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_8">
     <!-- 时间 (s) -->
     <g transform="translate(360.224375 420.002813) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-65f6" d="M 2525 4575 
Q 2500 3675 2500 2400 
Q 2500 1125 2525 400 
L 2000 400 
L 2000 875 
L 1000 875 
L 1000 50 
L 475 50 
Q 500 1200 500 2350 
Q 500 3525 475 4575 
L 2525 4575 
z
M 4875 3825 
Q 4875 4575 4850 5225 
L 5400 5225 
Q 5375 4575 5375 3825 
Q 5750 3825 6175 3850 
L 6175 3375 
Q 5750 3400 5375 3400 
L 5375 50 
Q 5375 -325 5075 -425 
Q 4775 -525 4300 -575 
Q 4300 -275 4025 0 
Q 4500 -50 4687 -25 
Q 4875 0 4875 300 
L 4875 3400 
Q 3625 3400 2775 3375 
L 2775 3850 
Q 3575 3825 4875 3825 
z
M 2000 1300 
L 2000 2550 
L 1000 2550 
L 1000 1300 
L 2000 1300 
z
M 2000 2975 
L 2000 4150 
L 1000 4150 
L 1000 2975 
L 2000 2975 
z
M 3550 2725 
Q 3875 2200 4175 1600 
Q 3925 1525 3650 1375 
Q 3450 1975 3075 2500 
Q 3300 2600 3550 2725 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-95f4" d="M 4450 3550 
Q 4425 3075 4425 2725 
L 4425 1400 
Q 4425 1075 4450 625 
L 1925 625 
Q 1950 1075 1950 1425 
L 1950 2725 
Q 1950 3075 1925 3550 
L 4450 3550 
z
M 5800 4875 
Q 5775 4400 5775 3925 
L 5775 250 
Q 5800 -325 5462 -450 
Q 5125 -575 4725 -625 
Q 4700 -325 4525 -25 
Q 5000 -50 5137 25 
Q 5275 100 5275 350 
L 5275 4450 
L 3775 4450 
Q 3175 4450 2650 4425 
L 2650 4900 
Q 3175 4875 3775 4875 
L 5800 4875 
z
M 550 -525 
Q 600 -25 600 525 
L 600 3150 
Q 600 3675 550 4025 
L 1125 4025 
Q 1100 3650 1100 3300 
L 1100 500 
Q 1100 0 1125 -525 
L 550 -525 
z
M 3950 1025 
L 3950 1950 
L 2425 1950 
L 2425 1025 
L 3950 1025 
z
M 3950 2350 
L 3950 3150 
L 2425 3150 
L 2425 2350 
L 3950 2350 
z
M 1525 5300 
Q 1750 5000 2225 4350 
Q 1950 4225 1700 4075 
Q 1450 4575 1075 5025 
Q 1350 5225 1525 5300 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-20" transform="scale(0.015625)"/>
       <path id="SimHei-28" d="M 2975 -200 
L 2700 -475 
Q 2075 125 1762 775 
Q 1450 1425 1450 2250 
Q 1450 3075 1762 3725 
Q 2075 4375 2700 5000 
L 2975 4725 
Q 2400 4175 2112 3587 
Q 1825 3000 1825 2250 
Q 1825 1500 2112 912 
Q 2400 325 2975 -200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-73" d="M 2750 900 
Q 2750 500 2437 287 
Q 2125 75 1650 75 
Q 1050 75 725 312 
Q 400 550 400 1000 
L 900 1000 
Q 900 700 1112 600 
Q 1325 500 1625 500 
Q 1925 500 2075 612 
Q 2225 725 2225 900 
Q 2225 1025 2100 1150 
Q 1975 1275 1475 1350 
Q 900 1425 687 1637 
Q 475 1850 475 2200 
Q 475 2500 762 2737 
Q 1050 2975 1600 2975 
Q 2100 2975 2387 2750 
Q 2675 2525 2675 2150 
L 2175 2150 
Q 2175 2375 2012 2462 
Q 1850 2550 1600 2550 
Q 1275 2550 1137 2437 
Q 1000 2325 1000 2175 
Q 1000 2000 1125 1900 
Q 1250 1800 1650 1750 
Q 2300 1650 2525 1437 
Q 2750 1225 2750 900 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-29" d="M 1675 2250 
Q 1675 1425 1362 775 
Q 1050 125 425 -475 
L 150 -200 
Q 725 325 1012 912 
Q 1300 1500 1300 2250 
Q 1300 3000 1012 3587 
Q 725 4175 150 4725 
L 425 5000 
Q 1050 4375 1362 3725 
Q 1675 3075 1675 2250 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-65f6"/>
      <use xlink:href="#SimHei-95f4" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-73" x="300"/>
      <use xlink:href="#SimHei-29" x="350"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_35">
      <path d="M 51.24875 377.74993 
L 709.2 377.74993 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <defs>
       <path id="m5c2b72b29d" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5c2b72b29d" x="51.24875" y="377.74993" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- -0.15 -->
      <g transform="translate(24.24875 380.49993) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-31" x="150"/>
       <use xlink:href="#SimHei-35" x="200"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_37">
      <path d="M 51.24875 301.115363 
L 709.2 301.115363 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m5c2b72b29d" x="51.24875" y="301.115363" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- -0.10 -->
      <g transform="translate(24.24875 303.865363) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-31" x="150"/>
       <use xlink:href="#SimHei-30" x="200"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_39">
      <path d="M 51.24875 224.480795 
L 709.2 224.480795 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m5c2b72b29d" x="51.24875" y="224.480795" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- -0.05 -->
      <g transform="translate(24.24875 227.230795) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-2d"/>
       <use xlink:href="#SimHei-30" x="50"/>
       <use xlink:href="#SimHei-2e" x="100"/>
       <use xlink:href="#SimHei-30" x="150"/>
       <use xlink:href="#SimHei-35" x="200"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_41">
      <path d="M 51.24875 147.846228 
L 709.2 147.846228 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m5c2b72b29d" x="51.24875" y="147.846228" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 0.00 -->
      <g transform="translate(28.24875 150.596228) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
       <use xlink:href="#SimHei-30" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_43">
      <path d="M 51.24875 71.21166 
L 709.2 71.21166 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m5c2b72b29d" x="51.24875" y="71.21166" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 0.05 -->
      <g transform="translate(28.24875 73.96166) scale(0.08 -0.08)">
       <use xlink:href="#SimHei-30"/>
       <use xlink:href="#SimHei-2e" x="50"/>
       <use xlink:href="#SimHei-30" x="100"/>
       <use xlink:href="#SimHei-35" x="150"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_45">
      <defs>
       <path id="mb2a2056593" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="393.076844" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_46">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="362.423017" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_47">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="347.096103" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_48">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="331.76919" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_49">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="316.442276" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_50">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="285.788449" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_51">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="270.461536" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_52">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="255.134622" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_53">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="239.807709" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_54">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="209.153882" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_55">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="193.826968" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_56">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="178.500055" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_57">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="163.173141" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_58">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="132.519314" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_59">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="117.192401" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_60">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="101.865487" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_61">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="86.538574" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_62">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="55.884747" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_63">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="40.557833" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_64">
      <g>
       <use xlink:href="#mb2a2056593" x="51.24875" y="25.23092" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_14">
     <!-- 振幅 (μV) -->
     <g transform="translate(18.99875 234.774375) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-632f" d="M 3275 -200 
Q 3400 -100 3437 37 
Q 3475 175 3475 500 
L 3475 2300 
L 3025 2300 
Q 3000 1800 2950 1375 
Q 2900 950 2800 600 
Q 2700 250 2575 -37 
Q 2450 -325 2325 -625 
Q 2175 -475 1825 -350 
Q 2150 -25 2300 412 
Q 2450 850 2500 1350 
Q 2550 1850 2562 2437 
Q 2575 3025 2575 3625 
Q 2575 4250 2525 4825 
L 5150 4825 
Q 5475 4825 5825 4850 
L 5825 4375 
Q 5400 4400 5225 4400 
L 3025 4400 
L 3025 2700 
L 5375 2700 
Q 5600 2700 5975 2725 
L 5975 2275 
Q 5850 2300 5475 2300 
L 4675 2300 
Q 4775 1600 4925 1275 
Q 5125 1450 5262 1587 
Q 5400 1725 5550 1975 
Q 5650 1775 5900 1575 
Q 5750 1500 5575 1337 
Q 5400 1175 5125 875 
Q 5225 650 5500 362 
Q 5775 75 6175 -75 
Q 5875 -250 5775 -525 
Q 5300 -175 5075 100 
Q 4850 375 4687 687 
Q 4525 1000 4412 1387 
Q 4300 1775 4250 2300 
L 3950 2300 
L 3950 150 
Q 4250 300 4575 475 
Q 4625 325 4750 50 
Q 4575 -25 4237 -212 
Q 3900 -400 3600 -650 
Q 3500 -450 3275 -200 
z
M 250 2000 
Q 550 2075 725 2125 
L 1150 2275 
L 1150 3350 
Q 675 3350 325 3325 
L 325 3800 
Q 650 3775 1150 3775 
L 1150 4500 
Q 1150 4850 1125 5150 
L 1675 5150 
Q 1650 4875 1650 4500 
L 1650 3775 
Q 1875 3775 2300 3800 
L 2300 3325 
Q 1900 3350 1650 3350 
L 1650 2425 
Q 2000 2550 2250 2675 
Q 2250 2475 2275 2250 
Q 2100 2200 1650 2000 
L 1650 100 
Q 1600 -250 1450 -362 
Q 1300 -475 725 -525 
Q 700 -325 600 25 
Q 850 0 1000 0 
Q 1150 0 1150 300 
L 1150 1800 
Q 975 1725 850 1662 
Q 725 1600 475 1450 
Q 425 1675 250 2000 
z
M 4950 3775 
Q 5275 3775 5600 3800 
L 5600 3325 
Q 5275 3350 5000 3350 
L 4000 3350 
Q 3725 3350 3375 3325 
L 3375 3800 
Q 3700 3775 4000 3775 
L 4950 3775 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5e45" d="M 5950 2250 
Q 5925 1875 5925 900 
Q 5925 -50 5950 -425 
L 5450 -425 
L 5450 -175 
L 3350 -175 
L 3350 -475 
L 2875 -475 
Q 2900 200 2900 1025 
Q 2900 1875 2875 2250 
L 5950 2250 
z
M 2575 1125 
Q 2575 800 2437 700 
Q 2300 600 1950 525 
Q 1900 800 1750 1000 
Q 1975 1000 2075 1037 
Q 2175 1075 2175 1300 
L 2175 3725 
L 1700 3725 
L 1700 475 
Q 1700 0 1725 -550 
L 1225 -550 
Q 1250 -25 1250 475 
L 1250 3725 
L 850 3725 
L 850 500 
L 400 500 
Q 425 1000 425 2300 
Q 425 3600 400 4100 
L 1250 4100 
L 1250 4500 
Q 1250 4825 1225 5175 
L 1725 5175 
Q 1700 4825 1700 4500 
L 1700 4100 
L 2600 4100 
Q 2575 3900 2575 3225 
L 2575 1125 
z
M 5575 3925 
Q 5550 3625 5550 3300 
Q 5550 2975 5575 2600 
L 3200 2600 
Q 3225 2975 3225 3300 
Q 3225 3625 3200 3925 
L 5575 3925 
z
M 5250 4800 
Q 5650 4800 6025 4825 
L 6025 4375 
Q 5650 4400 5250 4400 
L 3550 4400 
Q 3075 4400 2800 4375 
L 2800 4825 
Q 3075 4800 3550 4800 
L 5250 4800 
z
M 5150 2975 
L 5150 3575 
L 3625 3575 
L 3625 2975 
L 5150 2975 
z
M 5450 1225 
L 5450 1875 
L 4625 1875 
L 4625 1225 
L 5450 1225 
z
M 4175 200 
L 4175 850 
L 3350 850 
L 3350 200 
L 4175 200 
z
M 5450 200 
L 5450 850 
L 4625 850 
L 4625 200 
L 5450 200 
z
M 4175 1225 
L 4175 1875 
L 3350 1875 
L 3350 1225 
L 4175 1225 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-3bc" d="M 2025 3200 
L 2450 3200 
L 2450 1050 
Q 2625 550 3100 500 
L 3300 500 
Q 3875 575 3800 1750 
L 3800 3200 
L 4200 3200 
L 4200 900 
Q 4225 600 4450 575 
L 4650 575 
L 4650 175 
L 4200 200 
Q 4000 275 3950 450 
L 3925 500 
Q 3575 75 3075 150 
Q 2750 175 2450 450 
L 2450 -750 
L 2025 -750 
L 2025 3200 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-56" d="M 3050 4400 
L 1800 75 
L 1300 75 
L 50 4400 
L 650 4400 
L 1525 1125 
L 1575 1125 
L 2450 4400 
L 3050 4400 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-632f"/>
      <use xlink:href="#SimHei-5e45" x="100"/>
      <use xlink:href="#SimHei-20" x="200"/>
      <use xlink:href="#SimHei-28" x="250"/>
      <use xlink:href="#SimHei-3bc" x="300"/>
      <use xlink:href="#SimHei-56" x="400"/>
      <use xlink:href="#SimHei-29" x="450"/>
     </g>
    </g>
   </g>
   <g id="PolyCollection_1">
    <path d="M 81.155625 148.942004 
L 81.155625 262.068939 
L 82.152521 275.721819 
L 83.149417 285.384018 
L 84.146312 287.93164 
L 85.143208 282.385606 
L 86.140104 270.584297 
L 87.137 256.865414 
L 88.133896 247.522429 
L 89.130792 247.991819 
L 90.127688 258.755539 
L 91.124583 276.748138 
L 92.121479 297.157484 
L 93.118375 313.161764 
L 94.115271 318.083383 
L 95.112167 311.002017 
L 96.109062 299.670932 
L 97.105958 293.953276 
L 98.102854 296.336031 
L 99.09975 301.662389 
L 100.096646 301.810158 
L 101.093542 291.139591 
L 102.090437 272.647634 
L 103.087333 257.492439 
L 104.084229 251.195213 
L 105.081125 251.151952 
L 106.078021 255.767372 
L 107.074917 264.262644 
L 108.071813 269.399328 
L 109.068708 263.791454 
L 110.065604 254.224831 
L 111.0625 253.858654 
L 112.059396 260.525054 
L 113.056292 266.383565 
L 114.053187 269.969214 
L 115.050083 272.311044 
L 116.046979 270.747682 
L 117.043875 263.889313 
L 118.040771 255.978415 
L 119.037667 252.735509 
L 120.034563 254.594558 
L 121.031458 256.400391 
L 122.028354 254.337274 
L 123.02525 249.318281 
L 124.022146 245.47804 
L 125.019042 246.559017 
L 126.015938 253.165346 
L 127.012833 263.493828 
L 128.009729 275.061773 
L 129.006625 285.035357 
L 130.003521 291.078667 
L 131.000417 292.269827 
L 131.997313 288.614134 
L 132.994208 280.495564 
L 133.991104 269.412194 
L 134.988 258.599597 
L 135.984896 251.750594 
L 136.981792 251.165744 
L 137.978688 256.678115 
L 138.975583 265.290989 
L 139.972479 272.510087 
L 140.969375 275.542494 
L 141.966271 275.350752 
L 142.963167 274.412882 
L 143.960062 272.884554 
L 144.956958 269.066985 
L 145.953854 263.118032 
L 146.95075 257.218494 
L 147.947646 251.878221 
L 148.944542 247.151851 
L 149.941438 244.865177 
L 150.938333 245.098938 
L 151.935229 244.641562 
L 152.932125 241.714617 
L 153.929021 238.792755 
L 154.925917 239.287956 
L 155.922813 245.087251 
L 156.919708 256.102292 
L 157.916604 267.103311 
L 158.9135 266.215851 
L 159.910396 242.939083 
L 160.907292 199.026882 
L 161.904188 150.301981 
L 162.901083 119.734659 
L 163.897979 127.080001 
L 164.894875 171.352304 
L 165.891771 230.160853 
L 166.888667 283.7884 
L 167.885563 326.415615 
L 168.882458 364.158135 
L 169.879354 406.602471 
L 170.87625 457.852538 
L 171.873146 512.392791 
L 172.870042 558.090912 
L 173.866938 583.813908 
L 174.863833 586.01305 
L 175.860729 570.523387 
L 176.857625 549.725632 
L 177.854521 535.965161 
L 178.851417 533.24442 
L 179.848313 534.154678 
L 180.845208 526.26691 
L 181.842104 501.203101 
L 182.839 459.469404 
L 183.835896 410.621345 
L 184.832792 368.689261 
L 185.829688 343.171308 
L 186.826583 331.782106 
L 187.823479 322.855502 
L 188.820375 304.880168 
L 189.817271 274.271798 
L 190.814167 236.994674 
L 191.811063 204.623622 
L 192.807958 187.714024 
L 193.804854 189.445486 
L 194.80175 203.109293 
L 195.798646 216.497786 
L 196.795542 220.037896 
L 197.792438 212.201717 
L 198.789333 198.833158 
L 199.786229 188.09357 
L 200.783125 186.033333 
L 201.780021 193.789423 
L 202.776917 206.004312 
L 203.773813 214.247011 
L 204.770708 213.136636 
L 205.767604 203.46431 
L 206.7645 190.7889 
L 207.761396 181.626509 
L 208.758292 180.148263 
L 209.755188 186.382364 
L 210.752083 195.638804 
L 211.748979 200.246168 
L 212.745875 194.392195 
L 213.742771 178.903203 
L 214.739667 161.006056 
L 215.736563 147.595597 
L 216.733458 139.454469 
L 217.730354 134.074485 
L 218.72725 130.396204 
L 219.724146 129.868444 
L 220.721042 135.423881 
L 221.717938 148.097886 
L 222.714833 163.510687 
L 223.711729 174.413961 
L 224.708625 176.764793 
L 225.705521 172.324088 
L 226.702417 166.059246 
L 227.699313 162.927832 
L 228.696208 166.624523 
L 229.693104 176.261477 
L 230.69 185.17491 
L 231.686896 187.048589 
L 232.683792 180.466822 
L 233.680688 166.590154 
L 234.677583 148.31122 
L 235.674479 132.607855 
L 236.671375 127.363991 
L 237.668271 133.744472 
L 238.665167 145.756786 
L 239.662063 157.654155 
L 240.658958 166.59951 
L 241.655854 171.106188 
L 242.65275 171.873169 
L 243.649646 171.621701 
L 244.646542 171.446556 
L 245.643438 168.347438 
L 246.640333 158.287817 
L 247.637229 141.106371 
L 248.634125 120.59018 
L 249.631021 100.520659 
L 250.627917 82.719137 
L 251.624813 68.026294 
L 252.621708 57.505776 
L 253.618604 52.417875 
L 254.6155 52.709211 
L 255.612396 56.610848 
L 256.609292 62.185853 
L 257.606188 67.93667 
L 258.603083 72.928068 
L 259.599979 77.506919 
L 260.596875 82.908405 
L 261.593771 90.141812 
L 262.590667 100.189907 
L 263.587563 113.459295 
L 264.584458 126.684404 
L 265.581354 133.286046 
L 266.57825 129.579714 
L 267.575146 117.666402 
L 268.572042 103.182307 
L 269.568938 93.137068 
L 270.565833 93.443799 
L 271.562729 104.789828 
L 272.559625 121.62016 
L 273.556521 137.171103 
L 274.553417 148.158297 
L 275.550313 154.745387 
L 276.547208 159.015943 
L 277.544104 163.74039 
L 278.541 170.196934 
L 279.537896 176.565055 
L 280.534792 179.976564 
L 281.531688 180.091436 
L 282.528583 179.811961 
L 283.525479 183.709296 
L 284.522375 195.595574 
L 285.519271 214.958342 
L 286.516167 235.118735 
L 287.513063 247.578995 
L 288.509958 248.968472 
L 289.506854 242.680907 
L 290.50375 235.48238 
L 291.500646 233.740865 
L 292.497542 240.037039 
L 293.494438 250.81482 
L 294.491333 258.394894 
L 295.488229 256.914208 
L 296.485125 246.385261 
L 297.482021 231.970743 
L 298.478917 220.660506 
L 299.475813 217.300671 
L 300.472708 221.723227 
L 301.469604 229.63392 
L 302.4665 236.357964 
L 303.463396 239.037774 
L 304.460292 236.382113 
L 305.457188 228.593595 
L 306.454083 217.137842 
L 307.450979 203.564521 
L 308.447875 189.449436 
L 309.444771 177.317666 
L 310.441667 169.737185 
L 311.438563 167.4647 
L 312.435458 169.660794 
L 313.432354 174.014607 
L 314.42925 175.808685 
L 315.426146 170.63396 
L 316.423042 159.507014 
L 317.419938 149.372117 
L 318.416833 146.879309 
L 319.413729 151.654231 
L 320.410625 157.539364 
L 321.407521 159.051079 
L 322.404417 154.679194 
L 323.401313 145.184334 
L 324.398208 133.024431 
L 325.395104 125.093789 
L 326.392 129.213452 
L 327.388896 143.400295 
L 328.385792 156.458726 
L 329.382688 160.08643 
L 330.379583 154.864925 
L 331.376479 147.108483 
L 332.373375 143.014297 
L 333.370271 146.53199 
L 334.367167 157.338709 
L 335.364063 170.226912 
L 336.360958 179.620026 
L 337.357854 183.112688 
L 338.35475 181.853755 
L 339.351646 179.919594 
L 340.348542 181.239977 
L 341.345438 185.053606 
L 342.342333 187.114139 
L 343.339229 184.481232 
L 344.336125 177.514169 
L 345.333021 169.305659 
L 346.329917 164.194557 
L 347.326813 165.017889 
L 348.323708 170.760308 
L 349.320604 177.551082 
L 350.3175 181.231374 
L 351.314396 179.017663 
L 352.311292 171.513501 
L 353.308188 163.468472 
L 354.305083 159.333285 
L 355.301979 159.803959 
L 356.298875 165.238583 
L 357.295771 178.742938 
L 358.292667 201.868823 
L 359.289563 227.521827 
L 360.286458 245.538478 
L 361.283354 250.337656 
L 362.28025 241.282363 
L 363.277146 222.201696 
L 364.274042 202.44364 
L 365.270938 191.93442 
L 366.267833 194.445314 
L 367.264729 209.975896 
L 368.261625 234.352919 
L 369.258521 257.452974 
L 370.255417 266.889067 
L 371.252313 256.734038 
L 372.249208 234.007941 
L 373.246104 214.738167 
L 374.243 208.81608 
L 375.239896 216.392998 
L 376.236792 231.478715 
L 377.233688 244.51952 
L 378.230583 249.602784 
L 379.227479 247.185713 
L 380.224375 237.873273 
L 381.221271 227.040069 
L 382.218167 226.419226 
L 383.215063 239.994595 
L 384.211958 256.267778 
L 385.208854 262.014496 
L 386.20575 255.016852 
L 387.202646 243.914378 
L 388.199542 236.515794 
L 389.196438 235.701778 
L 390.193333 243.726333 
L 391.190229 258.956583 
L 392.187125 273.997289 
L 393.184021 282.874671 
L 394.180917 285.60455 
L 395.177813 285.446878 
L 396.174708 286.513288 
L 397.171604 292.74254 
L 398.1685 303.728374 
L 399.165396 312.969933 
L 400.162292 315.314349 
L 401.159188 312.271814 
L 402.156083 308.655149 
L 403.152979 307.8856 
L 404.149875 310.966891 
L 405.146771 316.813184 
L 406.143667 322.717376 
L 407.140563 325.647446 
L 408.137458 323.115617 
L 409.134354 313.256064 
L 410.13125 296.574754 
L 411.128146 277.721281 
L 412.125042 264.010587 
L 413.121938 261.490873 
L 414.118833 271.413498 
L 415.115729 289.056523 
L 416.112625 305.484462 
L 417.109521 312.090714 
L 418.106417 305.587258 
L 419.103313 290.04943 
L 420.100208 274.467496 
L 421.097104 266.922176 
L 422.094 269.228882 
L 423.090896 276.328781 
L 424.087792 279.950326 
L 425.084688 273.516038 
L 426.081583 256.413422 
L 427.078479 235.153925 
L 428.075375 219.554901 
L 429.072271 216.496799 
L 430.069167 226.815059 
L 431.066063 245.936116 
L 432.062958 265.844234 
L 433.059854 278.627795 
L 434.05675 281.024194 
L 435.053646 275.483005 
L 436.050542 266.272732 
L 437.047438 256.950266 
L 438.044333 251.450974 
L 439.041229 253.780282 
L 440.038125 264.513731 
L 441.035021 279.373037 
L 442.031917 293.554823 
L 443.028813 304.746338 
L 444.025708 310.842619 
L 445.022604 309.840562 
L 446.0195 303.416189 
L 447.016396 296.910573 
L 448.013292 295.577911 
L 449.010188 301.427861 
L 450.007083 311.848459 
L 451.003979 320.468782 
L 452.000875 321.724846 
L 452.997771 315.295174 
L 453.994667 304.751007 
L 454.991563 294.058143 
L 455.988458 287.108108 
L 456.985354 286.194194 
L 457.98225 288.134259 
L 458.979146 286.725537 
L 459.976042 281.886913 
L 460.972938 280.502147 
L 461.969833 285.701652 
L 462.966729 293.919053 
L 463.963625 300.372577 
L 464.960521 300.61316 
L 465.957417 291.83857 
L 466.954313 277.133181 
L 467.951208 265.768172 
L 468.948104 266.089557 
L 469.945 280.672614 
L 470.941896 306.04069 
L 471.938792 332.12272 
L 472.935688 346.104753 
L 473.932583 341.651029 
L 474.929479 323.846984 
L 475.926375 304.1368 
L 476.923271 291.074477 
L 477.920167 286.559249 
L 478.917063 287.351189 
L 479.913958 288.096419 
L 480.910854 285.666166 
L 481.90775 281.954267 
L 482.904646 282.179046 
L 483.901542 290.395588 
L 484.898438 305.859701 
L 485.895333 322.91494 
L 486.892229 334.550567 
L 487.889125 336.66413 
L 488.886021 330.008817 
L 489.882917 319.353713 
L 490.879813 310.48251 
L 491.876708 306.331148 
L 492.873604 305.561325 
L 493.8705 304.910942 
L 494.867396 301.405345 
L 495.864292 293.174253 
L 496.861188 281.411237 
L 497.858083 270.668552 
L 498.854979 265.155101 
L 499.851875 264.896362 
L 500.848771 266.776906 
L 501.845667 268.907578 
L 502.842563 272.073662 
L 503.839458 277.365839 
L 504.836354 284.217217 
L 505.83325 289.82754 
L 506.830146 289.569469 
L 507.827042 280.699973 
L 508.823938 267.512682 
L 509.820833 259.318463 
L 510.817729 261.296648 
L 511.814625 272.293041 
L 512.811521 288.025917 
L 513.808417 302.204945 
L 514.805313 307.318257 
L 515.802208 299.995342 
L 516.799104 287.398183 
L 517.796 281.224275 
L 518.792896 285.123253 
L 519.789792 294.835055 
L 520.786688 303.812364 
L 521.783583 306.757173 
L 522.780479 301.934297 
L 523.777375 292.304992 
L 524.774271 283.878814 
L 525.771167 281.336121 
L 526.768063 284.576308 
L 527.764958 288.047951 
L 528.761854 283.383539 
L 529.75875 265.94418 
L 530.755646 239.989352 
L 531.752542 215.326374 
L 532.749438 199.93279 
L 533.746333 196.873723 
L 534.743229 204.009647 
L 535.740125 214.212686 
L 536.737021 219.426538 
L 537.733917 217.484059 
L 538.730813 212.456144 
L 539.727708 208.943297 
L 540.724604 209.292457 
L 541.7215 213.590979 
L 542.718396 219.426327 
L 543.715292 223.850228 
L 544.712188 226.786086 
L 545.709083 230.13117 
L 546.705979 233.715399 
L 547.702875 234.766356 
L 548.699771 230.801022 
L 549.696667 221.326544 
L 550.693563 207.234315 
L 551.690458 191.19721 
L 552.687354 178.594277 
L 553.68425 175.126622 
L 554.681146 182.377299 
L 555.678042 196.01543 
L 556.674938 209.331148 
L 557.671833 217.871305 
L 558.668729 220.552443 
L 559.665625 219.642688 
L 560.662521 220.119524 
L 561.659417 225.503499 
L 562.656313 232.748018 
L 563.653208 234.110473 
L 564.650104 225.056109 
L 565.647 208.075124 
L 566.643896 189.746037 
L 567.640792 177.212753 
L 568.637688 175.208109 
L 569.634583 181.513633 
L 570.631479 188.086176 
L 571.628375 188.475311 
L 572.625271 182.158101 
L 573.622167 172.630781 
L 574.619063 165.613949 
L 575.615958 167.978561 
L 576.612854 179.975369 
L 577.60975 194.397017 
L 578.606646 204.717044 
L 579.603542 208.120669 
L 580.600438 203.34245 
L 581.597333 191.231219 
L 582.594229 177.567202 
L 583.591125 169.055729 
L 584.588021 167.161916 
L 585.584917 170.195992 
L 586.581813 176.232402 
L 587.578708 181.337409 
L 588.575604 180.575223 
L 589.5725 173.293049 
L 590.569396 163.378629 
L 591.566292 153.87455 
L 592.563188 146.925586 
L 593.560083 146.748554 
L 594.556979 156.510933 
L 595.553875 173.422738 
L 596.550771 190.377517 
L 597.547667 201.04461 
L 598.544562 202.331177 
L 599.541458 194.725402 
L 600.538354 181.902827 
L 601.53525 168.979044 
L 602.532146 159.602863 
L 603.529042 154.368571 
L 604.525938 152.197537 
L 605.522833 152.550273 
L 606.519729 155.551146 
L 607.516625 160.915278 
L 608.513521 167.816188 
L 609.510417 174.652907 
L 610.507313 178.554634 
L 611.504208 176.850196 
L 612.501104 170.100605 
L 613.498 163.045974 
L 614.494896 161.284618 
L 615.491792 166.539534 
L 616.488688 175.89903 
L 617.485583 184.531956 
L 618.482479 188.396557 
L 619.479375 186.27698 
L 620.476271 180.676397 
L 621.473167 176.314392 
L 622.470063 176.567932 
L 623.466958 180.458933 
L 624.463854 183.049219 
L 625.46075 179.679089 
L 626.457646 170.642933 
L 627.454542 161.665455 
L 628.451438 159.331137 
L 629.448333 165.886847 
L 630.445229 177.974954 
L 631.442125 189.190483 
L 632.439021 193.105238 
L 633.435917 185.797356 
L 634.432813 169.521211 
L 635.429708 152.41072 
L 636.426604 142.260793 
L 637.4235 141.751324 
L 638.420396 149.212225 
L 639.417292 161.422728 
L 640.414188 173.752335 
L 641.411083 181.695337 
L 642.407979 184.691385 
L 643.404875 184.074205 
L 644.401771 179.994691 
L 645.398667 172.748372 
L 646.395563 165.686043 
L 647.392458 164.287282 
L 648.389354 170.136815 
L 649.38625 181.619297 
L 650.383146 197.093276 
L 651.380042 211.920046 
L 652.376938 219.462938 
L 653.373833 216.093969 
L 654.370729 204.050892 
L 655.367625 189.638788 
L 656.364521 178.60681 
L 657.361417 173.786064 
L 658.358313 176.021342 
L 659.355208 183.994495 
L 660.352104 191.628605 
L 661.349 191.350186 
L 662.345896 183.829278 
L 663.342792 179.014302 
L 664.339688 181.426108 
L 665.336583 185.168772 
L 666.333479 185.742514 
L 667.330375 184.911731 
L 668.327271 183.979384 
L 669.324167 179.378533 
L 670.321063 171.884657 
L 671.317958 168.03949 
L 672.314854 170.068721 
L 673.31175 173.739499 
L 674.308646 174.881733 
L 675.305542 172.144085 
L 676.302438 165.320841 
L 677.299333 156.278161 
L 678.296229 150.378127 
L 679.293125 151.563499 
L 679.293125 14.782642 
L 679.293125 14.782642 
L 678.296229 16.931177 
L 677.299333 22.279444 
L 676.302438 25.532574 
L 675.305542 22.597085 
L 674.308646 12.226267 
L 673.31175 -0.418152 
L 672.314854 -6.51748 
L 671.317958 -2.154934 
L 670.321063 9.061571 
L 669.324167 22.231102 
L 668.327271 33.892503 
L 667.330375 38.58939 
L 666.333479 34.206852 
L 665.336583 29.783404 
L 664.339688 35.43904 
L 663.342792 49.654421 
L 662.345896 61.949366 
L 661.349 67.85138 
L 660.352104 70.283729 
L 659.355208 70.083089 
L 658.358313 67.433722 
L 657.361417 66.745205 
L 656.364521 72.948166 
L 655.367625 84.488601 
L 654.370729 94.294632 
L 653.373833 96.66607 
L 652.376938 91.703016 
L 651.380042 83.83364 
L 650.383146 77.67103 
L 649.38625 75.033824 
L 648.389354 74.951858 
L 647.392458 73.93306 
L 646.395563 66.539968 
L 645.398667 53.063482 
L 644.401771 41.68118 
L 643.404875 39.468931 
L 642.407979 46.558719 
L 641.411083 56.695953 
L 640.414188 61.657738 
L 639.417292 55.295189 
L 638.420396 37.498193 
L 637.4235 17.755644 
L 636.426604 9.076913 
L 635.429708 17.576018 
L 634.432813 38.961561 
L 633.435917 62.145103 
L 632.439021 75.295303 
L 631.442125 71.625366 
L 630.445229 54.363245 
L 629.448333 35.445985 
L 628.451438 26.241525 
L 627.454542 29.886216 
L 626.457646 41.209243 
L 625.46075 52.044411 
L 624.463854 56.655231 
L 623.466958 54.271054 
L 622.470063 48.636628 
L 621.473167 45.219983 
L 620.476271 47.426179 
L 619.479375 54.24615 
L 618.482479 61.265751 
L 617.485583 64.3222 
L 616.488688 62.816619 
L 615.491792 59.684684 
L 614.494896 57.949614 
L 613.498 57.800574 
L 612.501104 57.621961 
L 611.504208 56.273374 
L 610.507313 53.364103 
L 609.510417 49.0557 
L 608.513521 44.307717 
L 607.516625 40.584049 
L 606.519729 38.39348 
L 605.522833 36.516131 
L 604.525938 33.899233 
L 603.529042 31.749782 
L 602.532146 32.480762 
L 601.53525 37.31613 
L 600.538354 45.376174 
L 599.541458 53.990328 
L 598.544562 59.40521 
L 597.547667 58.441416 
L 596.550771 50.811044 
L 595.553875 39.740381 
L 594.556979 29.597282 
L 593.560083 23.415379 
L 592.563188 23.467339 
L 591.566292 30.992991 
L 590.569396 42.637981 
L 589.5725 51.003851 
L 588.575604 51.818588 
L 587.578708 46.872027 
L 586.581813 39.32914 
L 585.584917 32.212539 
L 584.588021 30.122039 
L 583.591125 36.269003 
L 582.594229 48.041165 
L 581.597333 59.957072 
L 580.600438 68.787842 
L 579.603542 72.209802 
L 578.606646 69.006189 
L 577.60975 63.088611 
L 576.612854 61.308866 
L 575.615958 65.876315 
L 574.619063 72.340687 
L 573.622167 76.149299 
L 572.625271 74.890454 
L 571.628375 66.61468 
L 570.631479 54.158324 
L 569.634583 44.514345 
L 568.637688 42.800133 
L 567.640792 49.485859 
L 566.643896 62.13154 
L 565.647 76.013981 
L 564.650104 84.852263 
L 563.653208 85.700551 
L 562.656313 80.892175 
L 561.659417 74.569258 
L 560.662521 70.254184 
L 559.665625 70.93158 
L 558.668729 76.831718 
L 557.671833 82.852414 
L 556.674938 81.931796 
L 555.678042 71.811852 
L 554.681146 57.090111 
L 553.68425 45.422545 
L 552.687354 43.411003 
L 551.690458 53.37548 
L 550.693563 71.2578 
L 549.696667 88.366867 
L 548.699771 97.277054 
L 547.702875 97.527602 
L 546.705979 94.482116 
L 545.709083 92.761914 
L 544.712188 92.51434 
L 543.715292 91.510923 
L 542.718396 88.552993 
L 541.7215 84.222932 
L 540.724604 81.021921 
L 539.727708 82.645384 
L 538.730813 89.852767 
L 537.733917 97.602547 
L 536.737021 99.082427 
L 535.740125 91.555306 
L 534.743229 77.579169 
L 533.746333 64.681363 
L 532.749438 63.175255 
L 531.752542 78.515803 
L 530.755646 105.381911 
L 529.75875 131.698599 
L 528.761854 147.899667 
L 527.764958 151.576016 
L 526.768063 147.427867 
L 525.771167 144.70758 
L 524.774271 150.62071 
L 523.777375 163.931655 
L 522.780479 175.86303 
L 521.783583 177.473436 
L 520.786688 167.196264 
L 519.789792 153.244578 
L 518.792896 147.838088 
L 517.796 156.68747 
L 516.799104 173.206789 
L 515.802208 185.191003 
L 514.805313 185.712518 
L 513.808417 173.364579 
L 512.811521 152.179508 
L 511.814625 133.12292 
L 510.817729 127.478838 
L 509.820833 136.960384 
L 508.823938 152.376413 
L 507.827042 163.394611 
L 506.830146 166.545753 
L 505.83325 162.748998 
L 504.836354 154.640129 
L 503.839458 145.60041 
L 502.842563 137.329707 
L 501.845667 128.687126 
L 500.848771 118.487627 
L 499.851875 108.167518 
L 498.854979 100.868381 
L 497.858083 99.647803 
L 496.861188 106.497256 
L 495.864292 120.455374 
L 494.867396 136.054048 
L 493.8705 145.844762 
L 492.873604 146.360694 
L 491.876708 140.84166 
L 490.879813 135.105483 
L 489.882917 132.597328 
L 488.886021 132.684612 
L 487.889125 131.489544 
L 486.892229 125.195131 
L 485.895333 113.972811 
L 484.898438 102.635082 
L 483.901542 97.265895 
L 482.904646 100.978406 
L 481.90775 111.888688 
L 480.910854 124.475609 
L 479.913958 133.479368 
L 478.917063 137.440928 
L 477.920167 139.395967 
L 476.923271 144.355171 
L 475.926375 154.359096 
L 474.929479 165.269442 
L 473.932583 170.258033 
L 472.935688 165.533006 
L 471.938792 151.753223 
L 470.941896 133.206995 
L 469.945 117.217577 
L 468.948104 111.486439 
L 467.951208 117.635016 
L 466.954313 128.724894 
L 465.957417 136.337536 
L 464.960521 136.923666 
L 463.963625 131.940435 
L 462.966729 126.953585 
L 461.969833 128.013541 
L 460.972938 134.813516 
L 459.976042 139.810536 
L 458.979146 138.267772 
L 457.98225 132.731231 
L 456.985354 126.506807 
L 455.988458 122.16159 
L 454.991563 123.657929 
L 453.994667 132.587511 
L 452.997771 143.673174 
L 452.000875 148.826295 
L 451.003979 144.545188 
L 450.007083 133.123005 
L 449.010188 119.770274 
L 448.013292 110.541743 
L 447.016396 109.783339 
L 446.0195 117.562755 
L 445.022604 130.04564 
L 444.025708 141.510997 
L 443.028813 145.891999 
L 442.031917 139.650997 
L 441.035021 125.281363 
L 440.038125 109.10752 
L 439.041229 96.086974 
L 438.044333 89.756277 
L 437.047438 93.694915 
L 436.050542 108.177816 
L 435.053646 126.939207 
L 434.05675 140.222701 
L 433.059854 141.686949 
L 432.062958 131.585499 
L 431.066063 115.130934 
L 430.069167 100.373379 
L 429.072271 95.153396 
L 428.075375 102.546392 
L 427.078479 118.823682 
L 426.081583 136.640465 
L 425.084688 149.685648 
L 424.087792 154.945355 
L 423.090896 153.269062 
L 422.094 148.466429 
L 421.097104 144.622759 
L 420.100208 143.487831 
L 419.103313 144.425757 
L 418.106417 145.736352 
L 417.109521 145.329356 
L 416.112625 141.345085 
L 415.115729 133.058856 
L 414.118833 121.738045 
L 413.121938 110.936971 
L 412.125042 105.381013 
L 411.128146 108.669667 
L 410.13125 121.155339 
L 409.134354 138.737048 
L 408.137458 153.554616 
L 407.140563 157.716007 
L 406.143667 148.776472 
L 405.146771 131.8979 
L 404.149875 116.418769 
L 403.152979 110.597421 
L 402.156083 117.203017 
L 401.159188 132.018078 
L 400.162292 147.422841 
L 399.165396 158.146545 
L 398.1685 162.663205 
L 397.171604 161.152779 
L 396.174708 156.352404 
L 395.177813 153.829131 
L 394.180917 155.598595 
L 393.184021 157.490241 
L 392.187125 154.951921 
L 391.190229 147.721331 
L 390.193333 138.747046 
L 389.196438 133.177654 
L 388.199542 136.412975 
L 387.202646 146.344766 
L 386.20575 152.323494 
L 385.208854 148.795599 
L 384.211958 141.115276 
L 383.215063 136.883795 
L 382.218167 139.185851 
L 381.221271 147.374485 
L 380.224375 162.096182 
L 379.227479 178.688686 
L 378.230583 183.824314 
L 377.233688 170.245481 
L 376.236792 147.70858 
L 375.239896 131.56524 
L 374.243 131.981877 
L 373.246104 148.246726 
L 372.249208 167.57541 
L 371.252313 176.442948 
L 370.255417 171.084889 
L 369.258521 153.870424 
L 368.261625 131.481708 
L 367.264729 113.579553 
L 366.267833 109.205385 
L 365.270938 121.049216 
L 364.274042 141.814536 
L 363.277146 159.152884 
L 362.28025 166.380366 
L 361.283354 162.321523 
L 360.286458 148.71762 
L 359.289563 129.898365 
L 358.292667 109.294515 
L 357.295771 86.937174 
L 356.298875 64.878141 
L 355.301979 50.813105 
L 354.305083 49.219094 
L 353.308188 56.211091 
L 352.311292 63.787058 
L 351.314396 66.666494 
L 350.3175 63.249776 
L 349.320604 54.242762 
L 348.323708 43.189041 
L 347.326813 34.846166 
L 346.329917 31.84685 
L 345.333021 33.909799 
L 344.336125 39.142826 
L 343.339229 45.277663 
L 342.342333 51.217322 
L 341.345438 57.568256 
L 340.348542 64.543043 
L 339.351646 70.255575 
L 338.35475 72.685604 
L 337.357854 71.332055 
L 336.360958 66.238156 
L 335.364063 58.871587 
L 334.367167 52.512468 
L 333.370271 49.419974 
L 332.373375 48.897199 
L 331.376479 48.073512 
L 330.379583 42.40171 
L 329.382688 29.299947 
L 328.385792 12.499521 
L 327.388896 -1.178551 
L 326.392 -8.375039 
L 325.395104 -10.586011 
L 324.398208 -8.466965 
L 323.401313 -2.278011 
L 322.404417 2.434291 
L 321.407521 -1.002867 
L 320.410625 -12.506858 
L 319.413729 -26.397091 
L 318.416833 -38.546166 
L 317.419938 -49.334903 
L 316.423042 -60.065705 
L 315.426146 -69.799042 
L 314.42925 -77.40901 
L 313.432354 -83.989673 
L 312.435458 -91.787353 
L 311.438563 -101.399267 
L 310.441667 -111.876968 
L 309.444771 -122.791172 
L 308.447875 -133.29553 
L 307.450979 -140.747005 
L 306.454083 -142.752789 
L 305.457188 -139.692933 
L 304.460292 -135.04019 
L 303.463396 -134.215798 
L 302.4665 -141.861139 
L 301.469604 -157.764669 
L 300.472708 -175.693185 
L 299.475813 -187.395058 
L 298.478917 -187.648153 
L 297.482021 -176.837907 
L 296.485125 -160.974185 
L 295.488229 -148.737992 
L 294.491333 -146.087622 
L 293.494438 -152.579928 
L 292.497542 -162.221563 
L 291.500646 -166.949215 
L 290.50375 -160.995584 
L 289.506854 -145.269375 
L 288.509958 -127.889198 
L 287.513063 -118.577159 
L 286.516167 -122.032063 
L 285.519271 -136.257791 
L 284.522375 -154.409568 
L 283.525479 -167.447369 
L 282.528583 -169.119537 
L 281.531688 -160.880786 
L 280.534792 -150.694644 
L 279.537896 -146.589778 
L 278.541 -151.524901 
L 277.544104 -162.490825 
L 276.547208 -172.761784 
L 275.550313 -176.886092 
L 274.553417 -175.122782 
L 273.556521 -172.198732 
L 272.559625 -171.662654 
L 271.562729 -172.562106 
L 270.565833 -170.700657 
L 269.568938 -160.811413 
L 268.572042 -139.349279 
L 267.575146 -108.756378 
L 266.57825 -77.835348 
L 265.581354 -55.837128 
L 264.584458 -47.043598 
L 263.587563 -50.877245 
L 262.590667 -63.058973 
L 261.593771 -76.326824 
L 260.596875 -85.178335 
L 259.599979 -89.508822 
L 258.603083 -91.956723 
L 257.606188 -93.923999 
L 256.609292 -95.11959 
L 255.612396 -94.971717 
L 254.6155 -93.686862 
L 253.618604 -92.635357 
L 252.621708 -92.685122 
L 251.624813 -91.63538 
L 250.627917 -85.461131 
L 249.631021 -72.575887 
L 248.634125 -56.37786 
L 247.637229 -43.674134 
L 246.640333 -39.704501 
L 245.643438 -44.749607 
L 244.646542 -55.266684 
L 243.649646 -66.022664 
L 242.65275 -71.315301 
L 241.655854 -67.739333 
L 240.658958 -57.817479 
L 239.662063 -49.039072 
L 238.665167 -46.85755 
L 237.668271 -49.486874 
L 236.671375 -51.433986 
L 235.674479 -48.128333 
L 234.677583 -36.118049 
L 233.680688 -15.974872 
L 232.683792 4.657334 
L 231.686896 16.472969 
L 230.69 17.665072 
L 229.693104 13.685587 
L 228.696208 9.328481 
L 227.699313 5.70896 
L 226.702417 2.057006 
L 225.705521 -4.04968 
L 224.708625 -14.54416 
L 223.711729 -26.944664 
L 222.714833 -36.226655 
L 221.717938 -40.222392 
L 220.721042 -41.221441 
L 219.724146 -42.140192 
L 218.72725 -42.974159 
L 217.730354 -41.6302 
L 216.733458 -35.772122 
L 215.736563 -25.04651 
L 214.739667 -12.927396 
L 213.742771 -3.795208 
L 212.745875 1.320062 
L 211.748979 3.260919 
L 210.752083 2.314603 
L 209.755188 -0.979102 
L 208.758292 -4.420181 
L 207.761396 -5.143912 
L 206.7645 -2.387083 
L 205.767604 1.77595 
L 204.770708 4.934634 
L 203.773813 6.93441 
L 202.776917 9.654029 
L 201.780021 14.545308 
L 200.783125 20.789771 
L 199.786229 25.827643 
L 198.789333 26.253908 
L 197.792438 18.578824 
L 196.795542 2.418137 
L 195.798646 -17.817569 
L 194.80175 -35.530306 
L 193.804854 -45.673984 
L 192.807958 -46.621947 
L 191.811063 -39.596511 
L 190.814167 -27.64057 
L 189.817271 -14.855769 
L 188.820375 -4.205965 
L 187.823479 4.877045 
L 186.826583 16.362859 
L 185.829688 34.82263 
L 184.832792 62.579482 
L 183.835896 98.824291 
L 182.839 138.801765 
L 181.842104 173.39863 
L 180.845208 193.258889 
L 179.848313 195.135087 
L 178.851417 183.141172 
L 177.854521 163.598183 
L 176.857625 139.694618 
L 175.860729 109.406698 
L 174.863833 66.641917 
L 173.866938 7.624479 
L 172.870042 -60.771317 
L 171.873146 -120.053115 
L 170.87625 -149.639287 
L 169.879354 -139.892435 
L 168.882458 -98.95087 
L 167.885563 -48.365574 
L 166.888667 -10.160099 
L 165.891771 5.74907 
L 164.894875 4.318127 
L 163.897979 -3.499141 
L 162.901083 -9.873109 
L 161.904188 -5.871975 
L 160.907292 11.790716 
L 159.910396 34.312456 
L 158.9135 50.275973 
L 157.916604 53.615167 
L 156.919708 46.254179 
L 155.922813 37.150272 
L 154.925917 35.651738 
L 153.929021 42.857895 
L 152.932125 52.232336 
L 151.935229 58.796228 
L 150.938333 63.672354 
L 149.941438 70.167465 
L 148.944542 80.303351 
L 147.947646 93.862254 
L 146.95075 106.695231 
L 145.953854 112.177003 
L 144.956958 108.670782 
L 143.960062 101.589269 
L 142.963167 95.771656 
L 141.966271 90.921731 
L 140.969375 84.333554 
L 139.972479 75.068719 
L 138.975583 65.045051 
L 137.978688 58.182768 
L 136.981792 58.566188 
L 135.984896 67.396593 
L 134.988 80.859142 
L 133.991104 92.020001 
L 132.994208 95.965597 
L 131.997313 93.161504 
L 131.000417 88.441951 
L 130.003521 87.312687 
L 129.006625 91.916675 
L 128.009729 99.465389 
L 127.012833 105.0653 
L 126.015938 106.845864 
L 125.019042 107.646351 
L 124.022146 111.624119 
L 123.02525 120.784581 
L 122.028354 133.726657 
L 121.031458 146.232929 
L 120.034563 153.823311 
L 119.037667 154.149069 
L 118.040771 148.684797 
L 117.043875 143.758833 
L 116.046979 145.201601 
L 115.050083 151.777533 
L 114.053187 156.35077 
L 113.056292 156.010303 
L 112.059396 157.958511 
L 111.0625 168.184479 
L 110.065604 180.5186 
L 109.068708 185.918093 
L 108.071813 186.658682 
L 107.074917 183.393907 
L 106.078021 173.353847 
L 105.081125 162.610917 
L 104.084229 163.21753 
L 103.087333 176.401308 
L 102.090437 189.924441 
L 101.093542 194.028841 
L 100.096646 190.032564 
L 99.09975 183.483483 
L 98.102854 181.185341 
L 97.105958 187.331708 
L 96.109062 199.093653 
L 95.112167 209.07815 
L 94.115271 212.029952 
L 93.118375 206.090953 
L 92.121479 192.735502 
L 91.124583 177.51096 
L 90.127688 167.10683 
L 89.130792 163.406141 
L 88.133896 161.273936 
L 87.137 154.84022 
L 86.140104 144.994576 
L 85.143208 137.545583 
L 84.146312 137.5144 
L 83.149417 144.282839 
L 82.152521 150.751274 
L 81.155625 148.942004 
z
" clip-path="url(#pa8402a1052)" style="fill: #808080; fill-opacity: 0.2; stroke: #808080; stroke-opacity: 0.2"/>
   </g>
   <g id="patch_3">
    <path d="M 430.069167 394.495 
L 529.75875 394.495 
L 529.75875 25.05375 
L 430.069167 25.05375 
z
" clip-path="url(#pa8402a1052)" style="fill: #0000ff; opacity: 0.2; stroke: #0000ff; stroke-linejoin: miter"/>
   </g>
   <g id="line2d_65">
    <path d="M 81.155625 205.505472 
L 82.152521 213.236547 
L 83.149417 214.833428 
L 84.146312 212.72302 
L 85.143208 209.965595 
L 87.137 205.852817 
L 88.133896 204.398182 
L 89.130792 205.69898 
L 90.127688 212.931185 
L 91.124583 227.129549 
L 92.121479 244.946493 
L 93.118375 259.626359 
L 94.115271 265.056668 
L 95.112167 260.040084 
L 96.109062 249.382293 
L 97.105958 240.642492 
L 98.102854 238.760686 
L 99.09975 242.572936 
L 100.096646 245.921361 
L 101.093542 242.584216 
L 102.090437 231.286037 
L 103.087333 216.946873 
L 104.084229 207.206371 
L 105.081125 206.881435 
L 106.078021 214.560609 
L 107.074917 223.828275 
L 108.071813 228.029005 
L 109.068708 224.854774 
L 110.065604 217.371715 
L 111.0625 211.021567 
L 112.059396 209.241782 
L 114.053187 213.159992 
L 115.050083 212.044288 
L 117.043875 203.824073 
L 118.040771 202.331606 
L 119.037667 203.442289 
L 120.034563 204.208934 
L 121.031458 201.31666 
L 122.028354 194.031965 
L 123.02525 185.051431 
L 124.022146 178.55108 
L 125.019042 177.102684 
L 126.015938 180.005605 
L 127.012833 184.279564 
L 128.009729 187.263581 
L 129.006625 188.476016 
L 130.003521 189.195677 
L 131.000417 190.355889 
L 131.997313 190.887819 
L 132.994208 188.23058 
L 133.991104 180.716098 
L 135.984896 159.573594 
L 136.981792 154.865966 
L 137.978688 157.430441 
L 138.975583 165.16802 
L 139.972479 173.789403 
L 140.969375 179.938024 
L 141.966271 183.136241 
L 144.956958 188.868884 
L 145.953854 187.647518 
L 146.95075 181.956862 
L 148.944542 163.727601 
L 149.941438 157.516321 
L 150.938333 154.385646 
L 151.935229 151.718895 
L 152.932125 146.973476 
L 153.929021 140.825325 
L 154.925917 137.469847 
L 155.922813 141.118762 
L 157.916604 160.359239 
L 158.9135 158.245912 
L 159.910396 138.625769 
L 161.904188 72.215003 
L 162.901083 54.930775 
L 163.897979 61.79043 
L 164.894875 87.835215 
L 165.891771 117.954961 
L 166.888667 136.81415 
L 167.885563 139.02502 
L 168.882458 132.603633 
L 169.879354 133.355018 
L 170.87625 154.106626 
L 171.873146 196.169838 
L 173.866938 295.719193 
L 174.863833 326.327483 
L 175.860729 339.965043 
L 177.854521 349.781672 
L 178.851417 358.192796 
L 179.848313 364.644883 
L 180.845208 359.7629 
L 181.842104 337.300866 
L 182.839 299.135584 
L 183.835896 254.722818 
L 184.832792 215.634371 
L 185.829688 188.996969 
L 186.826583 174.072483 
L 187.823479 163.866274 
L 188.820375 150.337101 
L 189.817271 129.708015 
L 190.814167 104.677052 
L 191.811063 82.513556 
L 192.807958 70.546038 
L 193.804854 71.885751 
L 194.80175 83.789493 
L 195.798646 99.340108 
L 196.795542 111.228016 
L 197.792438 115.390271 
L 198.789333 112.543533 
L 199.786229 106.960606 
L 200.783125 103.411552 
L 201.780021 104.167365 
L 202.776917 107.82917 
L 203.773813 110.590711 
L 204.770708 109.035635 
L 205.767604 102.62013 
L 206.7645 94.200908 
L 207.761396 88.241298 
L 208.758292 87.864041 
L 209.755188 92.701631 
L 210.752083 98.976703 
L 211.748979 101.753543 
L 212.745875 97.856128 
L 213.742771 87.553997 
L 215.736563 61.274543 
L 216.733458 51.841173 
L 217.730354 46.222142 
L 218.72725 43.711022 
L 219.724146 43.864126 
L 220.721042 47.10122 
L 221.717938 53.937747 
L 223.711729 73.734648 
L 224.708625 81.110317 
L 225.705521 84.137204 
L 226.702417 84.058126 
L 227.699313 84.318396 
L 228.696208 87.976502 
L 230.69 101.419991 
L 231.686896 101.760779 
L 232.683792 92.562078 
L 233.680688 75.307641 
L 234.677583 56.096586 
L 235.674479 42.239761 
L 236.671375 37.965003 
L 237.668271 42.128799 
L 238.665167 49.449618 
L 239.662063 54.307541 
L 240.658958 54.391015 
L 241.655854 51.683427 
L 242.65275 50.278934 
L 243.649646 52.799519 
L 244.646542 58.089936 
L 245.643438 61.798915 
L 246.640333 59.291658 
L 247.637229 48.716119 
L 250.603812 -1 
M 260.613605 -1 
L 261.593771 6.907494 
L 263.587563 31.291025 
L 264.584458 39.820403 
L 265.581354 38.724459 
L 266.57825 25.872183 
L 267.816425 -1 
M 277.328073 -1 
L 277.544104 0.624783 
L 278.541 9.336016 
L 279.537896 14.987638 
L 280.534792 14.64096 
L 281.531688 9.605325 
L 282.528583 5.346212 
L 283.525479 8.130964 
L 284.522375 20.593003 
L 286.516167 56.543336 
L 287.513063 64.500918 
L 288.509958 60.539637 
L 290.50375 37.243398 
L 291.500646 33.395825 
L 292.497542 38.907738 
L 293.494438 49.117446 
L 294.491333 56.153636 
L 295.488229 54.088108 
L 296.485125 42.705538 
L 297.482021 27.566418 
L 298.478917 16.506177 
L 299.475813 14.952807 
L 300.472708 23.015021 
L 301.469604 35.934625 
L 302.4665 47.248413 
L 303.463396 52.410988 
L 304.460292 50.670962 
L 305.457188 44.450331 
L 306.454083 37.192527 
L 307.450979 31.408758 
L 308.447875 28.076953 
L 309.444771 27.263247 
L 310.441667 28.930109 
L 311.438563 33.032717 
L 313.432354 45.012467 
L 314.42925 49.199838 
L 315.426146 50.417459 
L 316.423042 49.720654 
L 317.419938 50.018607 
L 318.416833 54.166571 
L 319.413729 62.62857 
L 320.410625 72.516253 
L 321.407521 79.024106 
L 322.404417 78.556743 
L 323.401313 71.453162 
L 324.398208 62.278733 
L 325.395104 57.253889 
L 326.392 60.419206 
L 327.388896 71.110872 
L 328.385792 84.479123 
L 329.382688 94.693188 
L 330.379583 98.633318 
L 331.376479 97.590998 
L 332.373375 95.955748 
L 333.370271 97.975982 
L 334.367167 104.925589 
L 335.364063 114.549249 
L 336.360958 122.929091 
L 337.357854 127.222371 
L 338.35475 127.269679 
L 340.348542 122.89151 
L 341.345438 121.310931 
L 342.342333 119.16573 
L 343.339229 114.879447 
L 345.333021 101.607729 
L 346.329917 98.020703 
L 347.326813 99.932028 
L 348.323708 106.974674 
L 349.320604 115.896922 
L 350.3175 122.240575 
L 351.314396 122.842079 
L 352.311292 117.65028 
L 353.308188 109.839782 
L 354.305083 104.27619 
L 355.301979 105.308532 
L 356.298875 115.058362 
L 357.295771 132.840056 
L 359.289563 178.710096 
L 360.286458 197.128049 
L 361.283354 206.329589 
L 362.28025 203.831365 
L 363.277146 190.67729 
L 364.274042 172.129088 
L 365.270938 156.491818 
L 366.267833 151.82535 
L 367.264729 161.777725 
L 369.258521 205.661699 
L 370.255417 218.986978 
L 371.252313 216.588493 
L 372.249208 200.791675 
L 373.246104 181.492446 
L 374.243 170.398979 
L 375.239896 173.979119 
L 376.236792 189.593648 
L 377.233688 207.382501 
L 378.230583 216.713549 
L 379.227479 212.9372 
L 381.221271 187.207277 
L 382.218167 182.802539 
L 383.215063 188.439195 
L 384.211958 198.691527 
L 385.208854 205.405048 
L 386.20575 203.670173 
L 388.199542 186.464385 
L 389.196438 184.439716 
L 390.193333 191.236689 
L 392.187125 214.474605 
L 393.184021 220.182456 
L 394.180917 220.601573 
L 395.177813 219.638005 
L 396.174708 221.432846 
L 397.171604 226.947659 
L 398.1685 233.19579 
L 399.165396 235.558239 
L 400.162292 231.368595 
L 402.156083 212.929083 
L 403.152979 209.241511 
L 404.149875 213.69283 
L 406.143667 235.746924 
L 407.140563 241.681726 
L 408.137458 238.335117 
L 409.134354 225.996556 
L 411.128146 193.195474 
L 412.125042 184.6958 
L 413.121938 186.213922 
L 414.118833 196.575771 
L 415.115729 211.057689 
L 416.112625 223.414774 
L 417.109521 228.710035 
L 418.106417 225.661805 
L 420.100208 208.977664 
L 421.097104 205.772468 
L 422.094 208.847656 
L 423.090896 214.798921 
L 424.087792 217.447841 
L 425.084688 211.600843 
L 426.081583 196.526944 
L 427.078479 176.988804 
L 428.075375 161.050647 
L 429.072271 155.825097 
L 430.069167 163.594219 
L 432.062958 198.714866 
L 433.059854 210.157372 
L 434.05675 210.623448 
L 435.053646 201.211106 
L 436.050542 187.225274 
L 437.047438 175.32259 
L 438.044333 170.603625 
L 439.041229 174.933628 
L 440.038125 186.810625 
L 442.031917 216.60291 
L 443.028813 225.319169 
L 444.025708 226.176808 
L 445.022604 219.943101 
L 446.0195 210.489472 
L 447.016396 203.346956 
L 448.013292 203.059827 
L 449.010188 210.599067 
L 450.007083 222.485732 
L 451.003979 232.506985 
L 452.000875 235.275571 
L 452.997771 229.484174 
L 454.991563 208.858036 
L 455.988458 204.634849 
L 456.985354 206.3505 
L 457.98225 210.432745 
L 458.979146 212.496655 
L 459.976042 210.848725 
L 460.972938 207.657832 
L 461.969833 206.857597 
L 462.966729 210.436319 
L 463.963625 216.156506 
L 464.960521 218.768413 
L 465.957417 214.088053 
L 467.951208 191.701594 
L 468.948104 188.787998 
L 469.945 198.945096 
L 471.938792 241.937972 
L 472.935688 255.818879 
L 473.932583 255.954531 
L 474.929479 244.558213 
L 475.926375 229.247948 
L 476.923271 217.714824 
L 477.920167 212.977608 
L 478.917063 212.396059 
L 479.913958 210.787894 
L 480.910854 205.070888 
L 481.90775 196.921478 
L 482.904646 191.578726 
L 483.901542 193.830742 
L 484.898438 204.247391 
L 485.895333 218.443876 
L 486.892229 229.872849 
L 487.889125 234.076837 
L 488.886021 231.346714 
L 489.882917 225.975521 
L 490.879813 222.793997 
L 491.876708 223.586404 
L 492.873604 225.96101 
L 493.8705 225.377852 
L 494.867396 218.729696 
L 496.861188 193.954247 
L 497.858083 185.158177 
L 498.854979 183.011741 
L 499.851875 186.53194 
L 503.839458 211.483124 
L 504.836354 219.428673 
L 505.83325 226.288269 
L 506.830146 228.057611 
L 507.827042 222.047292 
L 509.820833 198.139424 
L 510.817729 194.387743 
L 511.814625 202.70798 
L 513.808417 237.784762 
L 514.805313 246.515387 
L 515.802208 242.593173 
L 517.796 218.955872 
L 518.792896 216.48067 
L 519.789792 224.039817 
L 520.786688 235.504314 
L 521.783583 242.115304 
L 522.780479 238.898663 
L 524.774271 217.249762 
L 525.771167 213.02185 
L 526.768063 216.002087 
L 527.764958 219.811983 
L 528.761854 215.641603 
L 529.75875 198.821389 
L 531.752542 146.921088 
L 532.749438 131.554022 
L 533.746333 130.777543 
L 534.743229 140.794408 
L 535.740125 152.883996 
L 536.737021 159.254483 
L 537.733917 157.543303 
L 538.730813 151.154456 
L 539.727708 145.794341 
L 540.724604 145.157189 
L 541.7215 148.906955 
L 542.718396 153.98966 
L 543.715292 157.680575 
L 545.709083 161.446542 
L 546.705979 164.098757 
L 547.702875 166.146979 
L 548.699771 164.039038 
L 549.696667 154.846706 
L 551.690458 122.286345 
L 552.687354 111.00264 
L 553.68425 110.274583 
L 554.681146 119.733705 
L 555.678042 133.913641 
L 556.674938 145.631472 
L 557.671833 150.36186 
L 558.668729 148.692081 
L 559.665625 145.287134 
L 560.662521 145.186854 
L 561.659417 150.036378 
L 562.656313 156.820096 
L 563.653208 159.905512 
L 564.650104 154.954186 
L 565.647 142.044553 
L 566.643896 125.938789 
L 567.640792 113.349306 
L 568.637688 109.004121 
L 569.634583 113.013989 
L 570.631479 121.12225 
L 571.628375 127.544996 
L 572.625271 128.524278 
L 573.622167 124.39004 
L 574.619063 118.977318 
L 575.615958 116.927438 
L 576.612854 120.642118 
L 578.606646 136.861616 
L 579.603542 140.165236 
L 580.600438 136.065146 
L 581.597333 125.594146 
L 582.594229 112.804184 
L 583.591125 102.662366 
L 584.588021 98.641978 
L 585.584917 101.204265 
L 587.578708 114.104718 
L 588.575604 116.196906 
L 589.5725 112.14845 
L 590.569396 103.008305 
L 591.566292 92.43377 
L 592.563188 85.196462 
L 593.560083 85.081966 
L 594.556979 93.054107 
L 596.550771 120.594281 
L 597.547667 129.743013 
L 598.544562 130.868193 
L 599.541458 124.357865 
L 601.53525 103.147587 
L 602.532146 96.041813 
L 603.529042 93.059176 
L 604.525938 93.048385 
L 605.522833 94.533202 
L 606.519729 96.972313 
L 607.516625 100.749664 
L 609.510417 111.854304 
L 610.507313 115.959368 
L 611.504208 116.561785 
L 612.501104 113.861283 
L 613.498 110.423274 
L 614.494896 109.617116 
L 615.491792 113.112109 
L 616.488688 119.357825 
L 617.485583 124.427078 
L 618.482479 124.831154 
L 619.479375 120.261565 
L 620.476271 114.051288 
L 621.473167 110.767187 
L 622.470063 112.60228 
L 623.466958 117.364994 
L 624.463854 119.852225 
L 625.46075 115.86175 
L 627.454542 95.775836 
L 628.451438 92.786331 
L 629.448333 100.666416 
L 631.442125 130.407924 
L 632.439021 134.200271 
L 633.435917 123.971229 
L 635.429708 84.993369 
L 636.426604 75.668853 
L 637.4235 79.753484 
L 639.417292 108.358958 
L 640.414188 117.705036 
L 641.411083 119.195645 
L 643.404875 111.771568 
L 644.401771 110.837936 
L 645.398667 112.905927 
L 648.389354 122.544337 
L 649.38625 128.32656 
L 650.383146 137.382153 
L 651.380042 147.876843 
L 652.376938 155.582977 
L 653.373833 156.380019 
L 654.370729 149.172762 
L 656.364521 125.777488 
L 657.361417 120.265635 
L 658.358313 121.727532 
L 659.355208 127.038792 
L 660.352104 130.956167 
L 661.349 129.600783 
L 662.345896 122.889322 
L 663.342792 114.334362 
L 664.339688 108.432574 
L 665.336583 107.476088 
L 666.333479 109.974683 
L 667.330375 111.75056 
L 668.327271 108.935943 
L 669.324167 100.804817 
L 670.321063 90.473114 
L 671.317958 82.942278 
L 672.314854 81.775621 
L 673.31175 86.660673 
L 674.308646 93.554 
L 675.305542 97.370585 
L 676.302438 95.426708 
L 678.296229 83.654652 
L 679.293125 83.173071 
L 679.293125 83.173071 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
   </g>
   <g id="line2d_66">
    <path d="M 180.845208 394.495 
L 180.845208 25.05375 
" clip-path="url(#pa8402a1052)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #000000; stroke-opacity: 0.5; stroke-width: 1.5"/>
   </g>
   <g id="patch_4">
    <path d="M 51.24875 394.495 
L 51.24875 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_5">
    <path d="M 709.2 394.495 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 51.24875 394.495 
L 709.2 394.495 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 51.24875 25.05375 
L 709.2 25.05375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- test3阶段显著导联的平均HEP波形 -->
    <g transform="translate(305.224375 19.05375) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-74" d="M 2750 200 
Q 2625 150 2462 112 
Q 2300 75 2025 75 
Q 1575 75 1300 325 
Q 1025 575 1025 1025 
L 1025 2525 
L 175 2525 
L 175 2925 
L 1025 2925 
L 1025 3900 
L 1525 3900 
L 1525 2925 
L 2550 2925 
L 2550 2525 
L 1525 2525 
L 1525 1000 
Q 1525 800 1625 662 
Q 1725 525 2000 525 
Q 2275 525 2450 575 
Q 2625 625 2750 700 
L 2750 200 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-65" d="M 2850 1075 
Q 2800 625 2450 350 
Q 2100 75 1625 75 
Q 1025 75 637 462 
Q 250 850 250 1525 
Q 250 2200 637 2587 
Q 1025 2975 1625 2975 
Q 2150 2975 2487 2637 
Q 2825 2300 2825 1525 
L 800 1525 
Q 800 975 1037 750 
Q 1275 525 1625 525 
Q 1900 525 2075 662 
Q 2250 800 2300 1075 
L 2850 1075 
z
M 2250 1925 
Q 2200 2275 2025 2412 
Q 1850 2550 1575 2550 
Q 1325 2550 1125 2412 
Q 925 2275 825 1925 
L 2250 1925 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-9636" d="M 2450 4850 
Q 2350 4575 2175 3962 
Q 2000 3350 1825 2800 
Q 2225 2225 2312 1912 
Q 2400 1600 2375 1250 
Q 2350 900 2137 725 
Q 1925 550 1400 475 
Q 1300 925 1200 1075 
Q 1725 1025 1837 1225 
Q 1950 1425 1875 1775 
Q 1800 2125 1350 2725 
Q 1700 3825 1825 4400 
L 975 4400 
L 975 -500 
L 500 -500 
Q 525 175 525 675 
L 525 3650 
Q 525 4225 500 4850 
L 2450 4850 
z
M 4250 4825 
Q 4600 4100 5187 3637 
Q 5775 3175 6200 3025 
Q 5950 2775 5800 2500 
Q 4975 3075 4600 3512 
Q 4225 3950 4025 4400 
Q 3850 4025 3512 3487 
Q 3175 2950 2625 2350 
Q 2425 2575 2175 2700 
Q 2700 3075 3150 3787 
Q 3600 4500 3850 5275 
Q 4050 5150 4375 5075 
Q 4275 4900 4250 4825 
z
M 3750 2700 
Q 3650 2325 3575 1512 
Q 3500 700 3250 225 
Q 3000 -250 2650 -575 
Q 2425 -400 2175 -300 
Q 2550 -25 2762 337 
Q 2975 700 3087 1200 
Q 3200 1700 3200 2800 
Q 3350 2725 3750 2700 
z
M 5025 2775 
Q 4975 2425 4975 2025 
L 4975 350 
Q 4975 100 5000 -525 
L 4475 -525 
Q 4500 150 4500 375 
L 4500 2025 
Q 4500 2375 4475 2775 
L 5025 2775 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6bb5" d="M 5500 2475 
Q 5350 1775 5100 1237 
Q 4850 700 4600 400 
Q 4825 225 5250 87 
Q 5675 -50 6125 -125 
Q 5950 -275 5825 -650 
Q 5150 -450 4825 -287 
Q 4500 -125 4225 75 
Q 3825 -175 3450 -337 
Q 3075 -500 2625 -650 
Q 2525 -400 2325 -225 
Q 2675 -150 3100 0 
Q 3525 150 3925 425 
Q 3725 675 3525 1150 
Q 3325 1600 3225 2075 
Q 3050 2075 2900 2075 
L 2900 2500 
Q 3375 2475 3800 2475 
L 5500 2475 
z
M 2650 4625 
Q 2300 4550 1887 4475 
Q 1475 4400 1300 4375 
L 1300 3550 
Q 2050 3550 2525 3600 
L 2525 3125 
Q 2050 3150 1300 3150 
L 1300 2350 
Q 2050 2350 2525 2375 
L 2525 1900 
Q 2050 1925 1300 1925 
L 1300 975 
Q 1950 1075 2625 1250 
Q 2600 1000 2600 775 
Q 1900 675 1300 525 
L 1300 100 
Q 1300 -200 1325 -625 
L 800 -625 
Q 825 -200 825 100 
L 825 425 
Q 575 375 350 300 
Q 300 650 250 825 
Q 500 850 825 900 
L 825 3800 
Q 825 4225 800 4725 
Q 1100 4725 1575 4825 
Q 2050 4925 2375 5125 
Q 2475 4850 2650 4625 
z
M 5100 4975 
Q 5075 4600 5075 4275 
L 5075 3650 
Q 5075 3425 5237 3387 
Q 5400 3350 5975 3400 
Q 5875 3225 5850 2950 
L 5075 2950 
Q 4650 2950 4625 3375 
L 4625 4575 
L 3775 4575 
Q 3775 3925 3675 3525 
Q 3575 3125 3200 2725 
Q 3025 2900 2800 3050 
Q 3275 3400 3300 3937 
Q 3325 4475 3300 4975 
L 5100 4975 
z
M 3675 2100 
Q 3775 1675 3962 1287 
Q 4150 900 4275 775 
Q 4450 950 4625 1312 
Q 4800 1675 4900 2100 
L 3800 2100 
L 3675 2100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-663e" d="M 1150 2400 
Q 1175 2975 1175 3700 
Q 1175 4450 1150 4925 
L 5275 4925 
Q 5250 4400 5250 3675 
Q 5250 2975 5275 2400 
L 1150 2400 
z
M 4175 2275 
Q 4150 1750 4150 1075 
L 4150 25 
Q 5525 25 6050 50 
L 6050 -425 
Q 5525 -400 5125 -400 
L 1400 -400 
Q 850 -400 400 -425 
L 400 50 
Q 875 25 2200 25 
L 2200 1050 
Q 2200 1750 2175 2275 
L 2725 2275 
Q 2700 1750 2700 1050 
L 2700 25 
L 3650 25 
L 3650 1075 
Q 3650 1750 3625 2275 
L 4175 2275 
z
M 4750 3850 
L 4750 4500 
L 1675 4500 
L 1675 3850 
L 4750 3850 
z
M 4750 2850 
L 4750 3400 
L 1675 3400 
L 1675 2850 
L 4750 2850 
z
M 650 1625 
Q 900 1725 1100 1900 
Q 1625 1075 1900 500 
Q 1625 375 1425 225 
Q 1200 750 650 1625 
z
M 5750 1575 
Q 5650 1475 5425 1062 
Q 5200 650 4875 200 
Q 4700 350 4450 475 
Q 5000 1250 5225 1900 
Q 5475 1675 5750 1575 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8457" d="M 5225 1650 
Q 5200 1250 5200 550 
Q 5200 -150 5225 -550 
L 4700 -550 
L 4700 -300 
L 2050 -300 
L 2050 -600 
L 1525 -600 
Q 1550 -175 1550 975 
Q 1000 750 575 575 
Q 500 800 225 1025 
Q 775 1075 1550 1400 
L 1550 1650 
L 2250 1650 
Q 2675 1850 3125 2125 
L 1225 2125 
Q 775 2125 300 2100 
L 300 2550 
Q 775 2525 1200 2525 
L 2750 2525 
L 2750 3000 
L 2125 3000 
Q 1575 3000 1175 2975 
L 1175 3400 
Q 1575 3375 2100 3375 
L 2750 3375 
Q 2750 3575 2725 3825 
L 3275 3825 
Q 3250 3575 3250 3375 
Q 3775 3375 4275 3400 
L 4275 3025 
Q 4550 3300 4675 3450 
Q 4800 3600 4925 3800 
Q 5150 3625 5425 3500 
Q 5275 3425 5087 3237 
Q 4900 3050 4350 2525 
L 5300 2525 
Q 5750 2525 6150 2550 
L 6150 2100 
Q 5775 2125 5325 2125 
L 3850 2125 
Q 3500 1850 3100 1650 
L 5225 1650 
z
M 2475 5100 
Q 2450 4875 2450 4625 
L 3850 4625 
Q 3850 4800 3825 5100 
L 4375 5100 
Q 4350 4850 4350 4625 
L 4975 4625 
Q 5475 4625 6000 4650 
L 6000 4225 
Q 5525 4250 4975 4250 
L 4350 4250 
Q 4350 4100 4375 3775 
L 3850 3775 
L 3850 4250 
L 2450 4250 
L 2450 3725 
L 1925 3725 
Q 1950 4025 1950 4250 
L 1425 4250 
Q 875 4250 375 4225 
L 375 4650 
Q 875 4625 1425 4625 
L 1950 4625 
Q 1950 4825 1925 5100 
L 2475 5100 
z
M 4700 850 
L 4700 1275 
L 2050 1275 
L 2050 850 
L 4700 850 
z
M 4700 75 
L 4700 475 
L 2050 475 
L 2050 75 
L 4700 75 
z
M 3700 2525 
Q 3925 2700 4225 2975 
Q 3800 3000 3250 3000 
L 3250 2525 
L 3700 2525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5bfc" d="M 5125 5100 
Q 5100 4850 5100 4350 
Q 5100 3875 5125 3550 
L 1600 3550 
L 1600 3200 
Q 1600 2850 2050 2850 
L 4725 2850 
Q 5100 2850 5212 2950 
Q 5325 3050 5350 3500 
Q 5600 3300 5925 3200 
Q 5700 2625 5575 2525 
Q 5450 2425 5075 2425 
L 1675 2425 
Q 1100 2425 1100 3025 
L 1100 4450 
Q 1100 4775 1050 5100 
L 5125 5100 
z
M 4200 1725 
Q 4200 1950 4175 2300 
L 4725 2300 
Q 4700 1900 4700 1725 
L 5200 1725 
Q 5575 1725 6100 1750 
L 6100 1300 
Q 5575 1325 5200 1325 
L 4700 1325 
L 4700 225 
Q 4700 -225 4450 -375 
Q 4200 -525 3550 -650 
Q 3475 -325 3325 -50 
Q 3975 -50 4087 25 
Q 4200 100 4200 300 
L 4200 1325 
L 1425 1325 
Q 900 1325 350 1300 
L 350 1750 
Q 850 1725 1400 1725 
L 4200 1725 
z
M 4625 3975 
L 4625 4700 
L 1600 4700 
L 1600 3975 
L 4625 3975 
z
M 1625 1150 
Q 2000 725 2375 125 
Q 2100 -50 1925 -175 
Q 1650 325 1250 825 
Q 1450 950 1625 1150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-8054" d="M 2325 4850 
Q 2625 4850 2975 4875 
L 2975 4450 
Q 2700 4475 2475 4475 
L 2475 1150 
Q 2750 1225 2975 1275 
Q 2925 950 2950 850 
Q 2650 800 2475 750 
L 2475 175 
Q 2475 -200 2500 -675 
L 1975 -675 
Q 2025 -200 2025 175 
L 2025 650 
Q 1750 600 1337 475 
Q 925 350 450 175 
Q 425 350 275 750 
Q 425 750 800 825 
L 800 4475 
Q 550 4475 325 4425 
L 325 4875 
Q 625 4850 950 4850 
L 2325 4850 
z
M 5450 4950 
Q 5250 4675 5162 4487 
Q 5075 4300 4850 3900 
Q 5375 3900 5825 3925 
L 5825 3475 
Q 5375 3525 5050 3525 
L 4550 3525 
Q 4525 2600 4500 2225 
L 5225 2225 
Q 5525 2225 5950 2250 
L 5950 1800 
Q 5525 1825 5225 1825 
L 4575 1825 
Q 4725 1150 5175 625 
Q 5625 100 6175 -25 
Q 6000 -175 5912 -312 
Q 5825 -450 5800 -625 
Q 5275 -250 4925 200 
Q 4575 650 4350 1325 
Q 4175 750 3900 275 
Q 3625 -200 3200 -675 
Q 3125 -575 2975 -450 
Q 2825 -325 2750 -325 
Q 3100 -100 3412 387 
Q 3725 875 3825 1187 
Q 3925 1500 3975 1825 
L 3675 1825 
Q 3275 1825 2825 1800 
L 2825 2250 
Q 3275 2225 3675 2225 
L 4025 2225 
Q 4075 2950 4075 3525 
L 3825 3525 
Q 3475 3525 3000 3475 
L 3000 3925 
Q 3475 3900 3825 3900 
L 4450 3900 
Q 4750 4600 4900 5225 
Q 5050 5125 5450 4950 
z
M 2025 2225 
L 2025 3200 
L 1250 3200 
L 1250 2225 
L 2025 2225 
z
M 2025 3600 
L 2025 4475 
L 1250 4475 
L 1250 3600 
L 2025 3600 
z
M 2025 1825 
L 1250 1825 
L 1250 900 
Q 1525 950 2025 1050 
L 2025 1825 
z
M 3500 5175 
Q 3950 4475 4075 4250 
Q 3775 4125 3675 4000 
Q 3550 4300 3150 4950 
Q 3225 4975 3500 5175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7684" d="M 2100 4975 
Q 1975 4750 1750 3950 
L 2875 3950 
Q 2850 3525 2850 2875 
L 2850 675 
Q 2850 300 2875 -400 
L 2375 -400 
L 2375 100 
L 1100 100 
L 1100 -450 
L 600 -450 
Q 625 350 625 550 
L 625 2850 
Q 625 3525 600 3950 
L 1325 3950 
Q 1450 4525 1525 5150 
Q 1875 5025 2100 4975 
z
M 5825 3075 
Q 5800 1550 5700 175 
Q 5650 -275 5225 -425 
Q 4800 -575 4275 -625 
Q 4250 -350 4050 -50 
Q 4550 -75 4837 -25 
Q 5125 25 5200 200 
Q 5275 375 5312 1225 
Q 5350 2075 5375 3600 
L 3925 3600 
Q 3725 3125 3350 2475 
Q 3175 2650 2925 2750 
Q 3150 3050 3350 3475 
Q 3550 3900 3700 4400 
Q 3850 4900 3875 5175 
Q 4225 5025 4475 4950 
Q 4350 4750 4262 4512 
Q 4175 4275 4075 4050 
L 5850 4050 
Q 5825 3525 5825 3075 
z
M 2375 525 
L 2375 1850 
L 1100 1850 
L 1100 525 
L 2375 525 
z
M 2375 2275 
L 2375 3525 
L 1100 3525 
L 1100 2275 
L 2375 2275 
z
M 4350 2050 
Q 4500 1750 4700 1275 
Q 4525 1200 4225 1025 
Q 4050 1500 3900 1837 
Q 3750 2175 3600 2400 
Q 3775 2500 4050 2650 
L 4350 2050 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5e73" d="M 4525 4825 
Q 4900 4825 5450 4850 
L 5450 4375 
Q 4925 4400 4525 4400 
L 3475 4400 
L 3475 2025 
L 5000 2025 
Q 5550 2025 6025 2050 
L 6025 1575 
Q 5550 1600 5000 1600 
L 3475 1600 
L 3475 450 
Q 3475 -100 3500 -600 
L 2925 -600 
Q 2950 -100 2950 450 
L 2950 1600 
L 1125 1600 
Q 775 1600 375 1575 
L 375 2050 
Q 775 2025 1175 2025 
L 2950 2025 
L 2950 4400 
L 1775 4400 
Q 1400 4400 875 4375 
L 875 4850 
Q 1400 4825 1750 4825 
L 4525 4825 
z
M 3925 2600 
Q 4500 3300 4900 4125 
Q 5150 3950 5400 3825 
Q 4775 2850 4350 2300 
Q 4150 2475 3925 2600 
z
M 1975 2350 
Q 1600 3100 1050 3725 
Q 1250 3875 1450 4050 
Q 2075 3300 2450 2725 
Q 2150 2500 1975 2350 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5747" d="M 5775 275 
Q 5775 -325 5362 -450 
Q 4950 -575 4425 -600 
Q 4375 -275 4250 25 
Q 4950 -25 5125 62 
Q 5300 150 5300 500 
L 5375 3700 
L 3550 3700 
Q 3425 3375 3262 3037 
Q 3100 2700 2925 2400 
Q 2675 2550 2475 2625 
Q 3150 3625 3500 5200 
Q 3800 5100 4075 5025 
Q 3975 4875 3875 4650 
Q 3775 4425 3675 4125 
L 5900 4125 
L 5775 275 
z
M 1250 3625 
L 1250 4375 
Q 1250 4775 1225 5100 
L 1775 5100 
Q 1750 4725 1750 4375 
L 1750 3625 
L 1900 3625 
Q 2200 3625 2525 3650 
L 2525 3150 
Q 2200 3175 1900 3175 
L 1750 3175 
L 1750 1100 
L 2500 1375 
Q 2475 1125 2525 900 
Q 1225 475 962 362 
Q 700 250 475 150 
Q 375 475 250 750 
Q 575 800 800 850 
Q 1025 900 1250 975 
L 1250 3175 
L 1125 3175 
Q 850 3175 400 3150 
L 400 3650 
Q 850 3625 1100 3625 
L 1250 3625 
z
M 4950 1625 
Q 4675 1425 4137 1050 
Q 3600 675 3175 300 
Q 3000 575 2850 775 
Q 3350 1050 3762 1325 
Q 4175 1600 4775 2075 
Q 4825 1850 4950 1625 
z
M 3775 3150 
Q 4125 2800 4475 2300 
Q 4250 2125 4100 1975 
Q 3900 2325 3450 2825 
Q 3600 3000 3775 3150 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-48" d="M 2850 125 
L 2275 125 
L 2275 2125 
L 850 2125 
L 850 125 
L 275 125 
L 275 4400 
L 850 4400 
L 850 2600 
L 2275 2600 
L 2275 4400 
L 2850 4400 
L 2850 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-45" d="M 2900 125 
L 350 125 
L 350 4400 
L 2775 4400 
L 2775 3925 
L 925 3925 
L 925 2600 
L 2625 2600 
L 2625 2125 
L 925 2125 
L 925 600 
L 2900 600 
L 2900 125 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-50" d="M 2950 3100 
Q 2950 2500 2600 2150 
Q 2250 1800 1625 1800 
L 900 1800 
L 900 125 
L 325 125 
L 325 4400 
L 1625 4400 
Q 2250 4400 2600 4050 
Q 2950 3700 2950 3100 
z
M 2375 3100 
Q 2375 3575 2150 3750 
Q 1925 3925 1475 3925 
L 900 3925 
L 900 2275 
L 1475 2275 
Q 1925 2275 2150 2450 
Q 2375 2625 2375 3100 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-6ce2" d="M 5075 3275 
Q 5175 3600 5225 3850 
L 4325 3850 
L 4325 2750 
L 5550 2750 
Q 5400 2100 5125 1512 
Q 4850 925 4525 550 
Q 4775 350 5200 175 
Q 5625 0 6175 -100 
Q 5900 -300 5775 -600 
Q 5250 -425 4800 -175 
Q 4350 75 4200 225 
Q 3950 25 3537 -212 
Q 3125 -450 2500 -675 
Q 2375 -500 2050 -375 
Q 2525 -250 3062 25 
Q 3600 300 3850 550 
Q 3325 1300 3075 2325 
L 2825 2325 
Q 2800 1400 2487 725 
Q 2175 50 1875 -300 
Q 1700 -150 1375 -50 
Q 1750 325 1950 712 
Q 2150 1100 2262 1475 
Q 2375 1850 2387 2825 
Q 2400 3800 2350 4275 
L 3875 4275 
Q 3875 4850 3850 5200 
L 4350 5200 
Q 4325 4900 4325 4275 
L 5850 4275 
Q 5825 4125 5750 3837 
Q 5675 3550 5600 3200 
Q 5300 3250 5075 3275 
z
M 3525 2325 
Q 3800 1400 4200 925 
Q 4650 1475 4925 2325 
L 3525 2325 
z
M 1950 1700 
Q 1700 1375 1262 712 
Q 825 50 675 -200 
Q 475 50 225 275 
Q 525 525 937 1100 
Q 1350 1675 1550 2100 
L 1950 1700 
z
M 3875 2750 
L 3875 3850 
L 2850 3850 
L 2850 2750 
L 3875 2750 
z
M 925 4925 
Q 1600 4525 2000 4250 
Q 1800 4025 1675 3800 
Q 1250 4150 625 4500 
Q 775 4700 925 4925 
z
M 600 3525 
Q 1125 3250 1725 2850 
Q 1525 2675 1400 2425 
Q 925 2825 350 3100 
Q 450 3275 600 3525 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5f62" d="M 2750 4850 
Q 3350 4850 3675 4875 
L 3675 4425 
Q 3400 4450 2950 4450 
L 2950 2700 
Q 3475 2700 3725 2725 
L 3725 2250 
Q 3500 2275 2950 2275 
L 2950 500 
Q 2950 -50 2975 -350 
L 2475 -350 
Q 2500 0 2500 475 
L 2500 2275 
L 1650 2275 
Q 1600 1075 1325 437 
Q 1050 -200 825 -500 
Q 550 -300 300 -250 
Q 700 150 937 762 
Q 1175 1375 1200 2275 
Q 775 2275 425 2250 
L 425 2725 
Q 775 2700 1225 2700 
L 1225 4450 
Q 800 4450 600 4425 
L 600 4875 
Q 800 4850 1375 4850 
L 2750 4850 
z
M 6100 1275 
Q 5850 1050 5150 462 
Q 4450 -125 3800 -525 
Q 3650 -300 3400 -100 
Q 4050 175 4725 750 
Q 5400 1325 5625 1650 
Q 5800 1450 6100 1275 
z
M 2500 2700 
L 2500 4450 
L 1675 4450 
L 1675 2700 
L 2500 2700 
z
M 5875 2975 
Q 5650 2825 5187 2300 
Q 4725 1775 3900 1175 
Q 3750 1375 3475 1550 
Q 4175 1950 4625 2412 
Q 5075 2875 5375 3325 
Q 5700 3050 5875 2975 
z
M 5675 4800 
Q 5425 4575 5062 4087 
Q 4700 3600 4075 2975 
Q 3850 3150 3625 3275 
Q 4175 3750 4475 4125 
Q 4775 4500 5125 5100 
Q 5250 5025 5675 4800 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-74"/>
     <use xlink:href="#SimHei-65" x="50"/>
     <use xlink:href="#SimHei-73" x="100"/>
     <use xlink:href="#SimHei-74" x="150"/>
     <use xlink:href="#SimHei-33" x="200"/>
     <use xlink:href="#SimHei-9636" x="250"/>
     <use xlink:href="#SimHei-6bb5" x="350"/>
     <use xlink:href="#SimHei-663e" x="450"/>
     <use xlink:href="#SimHei-8457" x="550"/>
     <use xlink:href="#SimHei-5bfc" x="650"/>
     <use xlink:href="#SimHei-8054" x="750"/>
     <use xlink:href="#SimHei-7684" x="850"/>
     <use xlink:href="#SimHei-5e73" x="950"/>
     <use xlink:href="#SimHei-5747" x="1050"/>
     <use xlink:href="#SimHei-48" x="1150"/>
     <use xlink:href="#SimHei-45" x="1200"/>
     <use xlink:href="#SimHei-50" x="1250"/>
     <use xlink:href="#SimHei-6ce2" x="1300"/>
     <use xlink:href="#SimHei-5f62" x="1400"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_8">
     <path d="M 630 42.89125 
L 703.6 42.89125 
Q 705.2 42.89125 705.2 41.29125 
L 705.2 30.65375 
Q 705.2 29.05375 703.6 29.05375 
L 630 29.05375 
Q 628.4 29.05375 628.4 30.65375 
L 628.4 41.29125 
Q 628.4 42.89125 630 42.89125 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_67">
     <path d="M 631.6 35.89125 
L 639.6 35.89125 
L 647.6 35.89125 
" style="fill: none; stroke: #000000; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_16">
     <!-- 所有被试平均 -->
     <g transform="translate(654 38.69125) scale(0.08 -0.08)">
      <defs>
       <path id="SimHei-6240" d="M 2925 4450 
Q 2650 4425 1375 4250 
L 1375 3375 
L 2725 3375 
Q 2700 2800 2700 2225 
Q 2700 1675 2725 1325 
L 1350 1325 
Q 1325 875 1175 425 
Q 1025 -25 775 -525 
Q 575 -325 325 -275 
Q 725 325 812 1050 
Q 900 1775 900 2675 
Q 900 3575 875 4575 
Q 2225 4725 2650 4950 
Q 2750 4725 2925 4450 
z
M 5825 4425 
Q 5475 4400 5000 4337 
Q 4525 4275 3800 4225 
L 3800 3025 
L 5225 3025 
Q 5575 3025 5975 3050 
L 5975 2600 
Q 5575 2625 5200 2625 
L 5200 400 
Q 5200 -100 5225 -500 
L 4700 -500 
Q 4725 -50 4725 400 
L 4725 2625 
L 3800 2625 
Q 3750 1625 3600 1062 
Q 3450 500 3250 125 
Q 3050 -250 2725 -625 
Q 2550 -425 2300 -325 
Q 2725 75 2975 612 
Q 3225 1150 3287 1812 
Q 3350 2475 3350 3125 
Q 3350 3800 3325 4600 
Q 3700 4600 4425 4687 
Q 5150 4775 5575 4950 
Q 5700 4675 5825 4425 
z
M 2275 1750 
L 2275 2975 
L 1375 2975 
L 1375 1750 
L 2275 1750 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6709" d="M 275 1625 
Q 850 1975 1400 2537 
Q 1950 3100 2400 3925 
L 1500 3925 
Q 1150 3925 625 3900 
L 625 4375 
Q 1275 4350 1600 4350 
L 2550 4350 
Q 2750 4950 2775 5225 
Q 3150 5125 3375 5075 
Q 3300 5000 3125 4350 
L 5150 4350 
Q 5575 4350 6025 4375 
L 6025 3900 
Q 5575 3925 5225 3925 
L 2975 3925 
Q 2800 3600 2600 3275 
L 5475 3275 
Q 5450 2650 5450 2150 
L 5450 125 
Q 5450 -225 5250 -387 
Q 5050 -550 4400 -600 
Q 4375 -325 4225 0 
Q 4600 -50 4775 -25 
Q 4950 0 4950 250 
L 4950 750 
L 2350 750 
L 2350 -575 
L 1800 -575 
Q 1825 50 1825 500 
L 1825 2275 
Q 1300 1675 725 1225 
Q 625 1400 275 1625 
z
M 4950 2175 
L 4950 2875 
L 2350 2875 
L 2350 2175 
L 4950 2175 
z
M 4950 1150 
L 4950 1775 
L 2350 1775 
L 2350 1150 
L 4950 1150 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-88ab" d="M 4100 4175 
Q 4100 4800 4075 5175 
L 4575 5175 
Q 4550 4825 4550 4175 
L 6025 4175 
Q 5900 3825 5725 3225 
Q 5550 3300 5225 3325 
Q 5375 3675 5400 3800 
L 4550 3800 
L 4550 2650 
L 5575 2650 
Q 5425 2075 5225 1575 
Q 5025 1075 4675 550 
Q 4925 325 5375 150 
Q 5825 -25 6125 -50 
Q 5850 -300 5775 -550 
Q 5325 -375 5000 -187 
Q 4675 0 4400 250 
Q 3975 -200 3225 -625 
Q 3050 -350 2850 -250 
Q 3575 0 4100 575 
Q 3675 1125 3400 2275 
L 3275 2275 
Q 3250 1575 3087 925 
Q 2925 275 2350 -550 
Q 2175 -400 1875 -300 
Q 2375 225 2600 825 
Q 2825 1425 2850 2150 
L 2850 3375 
Q 2850 3900 2825 4175 
L 4100 4175 
z
M 1825 2325 
Q 2075 2600 2300 2950 
Q 2500 2725 2650 2600 
Q 2425 2400 2075 2050 
L 2550 1575 
Q 2400 1450 2200 1225 
Q 1975 1575 1575 1975 
L 1575 525 
Q 1575 0 1600 -625 
L 1125 -625 
Q 1150 175 1150 700 
L 1150 2075 
Q 900 1800 525 1475 
Q 400 1675 200 1850 
Q 675 2175 1037 2600 
Q 1400 3025 1650 3525 
L 1150 3525 
Q 800 3525 400 3500 
L 400 3925 
Q 950 3900 1300 3900 
L 2350 3900 
Q 1950 3075 1550 2550 
Q 1650 2475 1825 2325 
z
M 4100 2650 
L 4100 3800 
L 3275 3800 
L 3275 2650 
L 4100 2650 
z
M 3825 2275 
Q 4050 1375 4400 900 
Q 4600 1200 4737 1512 
Q 4875 1825 5000 2275 
L 3825 2275 
z
M 1500 5250 
Q 1750 4800 1950 4375 
L 1500 4150 
Q 1325 4650 1075 5025 
Q 1300 5100 1500 5250 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-8bd5" d="M 3950 3825 
Q 3925 4325 3925 4550 
Q 3925 4800 3900 5225 
Q 4200 5150 4450 5125 
Q 4425 4825 4425 4600 
L 4425 3825 
L 5150 3825 
Q 5500 3825 5875 3850 
L 5875 3400 
Q 5525 3425 5125 3425 
L 4450 3425 
Q 4500 2750 4587 2212 
Q 4675 1675 4837 1112 
Q 5000 550 5212 225 
Q 5425 -100 5500 200 
Q 5575 500 5600 900 
Q 5800 625 6100 525 
Q 5975 -275 5750 -487 
Q 5525 -700 5250 -537 
Q 4975 -375 4737 62 
Q 4500 500 4337 1075 
Q 4175 1650 4100 2262 
Q 4025 2875 3975 3425 
L 2800 3425 
Q 2300 3425 1875 3400 
L 1875 3850 
Q 2275 3825 2775 3825 
L 3950 3825 
z
M 1500 3175 
Q 1475 2675 1475 2175 
L 1475 625 
Q 1825 950 2125 1225 
Q 2225 1000 2375 800 
Q 1900 425 1125 -350 
Q 1000 -125 825 50 
Q 1025 175 1025 525 
L 1025 2725 
Q 625 2725 300 2700 
L 300 3200 
Q 625 3175 825 3175 
L 1500 3175 
z
M 3775 2175 
Q 3500 2200 3250 2200 
L 3250 575 
Q 3575 675 3900 800 
Q 3900 525 3950 325 
Q 3575 250 3150 125 
Q 2725 0 2275 -175 
Q 2200 100 2025 300 
Q 2425 350 2800 475 
L 2800 2200 
Q 2525 2200 2225 2175 
L 2225 2650 
Q 2600 2625 3000 2625 
Q 3425 2625 3775 2650 
L 3775 2175 
z
M 925 4975 
Q 1275 4650 1700 4100 
Q 1525 3925 1325 3775 
Q 1175 4050 575 4650 
Q 775 4825 925 4975 
z
M 5150 5150 
Q 5325 5000 5850 4375 
Q 5575 4200 5425 4050 
Q 5125 4525 4775 4875 
Q 4925 4975 5150 5150 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-6240"/>
      <use xlink:href="#SimHei-6709" x="100"/>
      <use xlink:href="#SimHei-88ab" x="200"/>
      <use xlink:href="#SimHei-8bd5" x="300"/>
      <use xlink:href="#SimHei-5e73" x="400"/>
      <use xlink:href="#SimHei-5747" x="500"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pa8402a1052">
   <rect x="51.24875" y="25.05375" width="657.95125" height="369.44125"/>
  </clipPath>
 </defs>
</svg>

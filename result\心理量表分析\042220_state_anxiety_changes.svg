<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="576pt" height="432pt" viewBox="0 0 576 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T17:35:34.190780</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 576 432 
L 576 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 39.29375 393.2725 
L 565.2 393.2725 
L 565.2 25.09125 
L 39.29375 25.09125 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 161.630231 393.2725 
L 161.630231 25.09125 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m1b760a0460" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m1b760a0460" x="161.630231" y="393.2725" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 实验前 -->
      <g transform="translate(149.630231 406.74125) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-5b9e" d="M 4125 3500 
Q 4050 3200 4000 2675 
Q 3950 2150 3850 1550 
L 5025 1550 
Q 5475 1550 5975 1575 
L 5975 1100 
Q 5450 1125 5000 1125 
L 3725 1125 
Q 3500 625 3050 250 
Q 2600 -125 2000 -337 
Q 1400 -550 925 -675 
Q 825 -375 500 -200 
Q 1225 -125 2050 212 
Q 2875 550 3200 1125 
L 1425 1125 
Q 975 1125 425 1100 
L 425 1575 
Q 1000 1550 1425 1550 
L 3375 1550 
Q 3450 1975 3487 2462 
Q 3525 2950 3500 3650 
Q 3800 3550 4125 3500 
z
M 3250 5250 
Q 3450 4850 3600 4375 
L 5775 4375 
Q 5750 4100 5750 3825 
Q 5750 3575 5775 3275 
L 5200 3275 
L 5200 3975 
L 1225 3975 
L 1225 3225 
L 675 3225 
Q 700 3525 700 3800 
Q 700 4100 675 4375 
L 2975 4375 
Q 2900 4675 2700 5075 
Q 2900 5150 3250 5250 
z
M 4000 825 
Q 4550 525 5050 262 
Q 5550 0 5925 -225 
Q 5750 -425 5550 -650 
Q 5100 -325 4575 -37 
Q 4050 250 3675 400 
Q 3875 650 4000 825 
z
M 1175 2750 
Q 1600 2550 2350 2050 
Q 2250 1900 2050 1650 
Q 1350 2175 925 2350 
Q 1075 2500 1175 2750 
z
M 3150 2825 
Q 2975 2625 2850 2425 
Q 2050 3000 1675 3125 
Q 1800 3300 1950 3525 
L 3150 2825 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-9a8c" d="M 2300 4725 
Q 2250 4500 2225 4075 
L 2050 2125 
L 2525 2125 
Q 2500 1925 2475 1450 
L 2375 75 
Q 2375 -450 2075 -525 
Q 1775 -600 1250 -675 
Q 1175 -325 1050 -100 
Q 1575 -100 1737 -87 
Q 1900 -75 1950 150 
L 2025 1700 
L 550 1700 
Q 575 2175 587 2737 
Q 600 3300 600 3800 
L 1100 3800 
Q 1050 3525 1025 3087 
Q 1000 2650 1000 2125 
L 1625 2125 
L 1800 4325 
L 1125 4325 
Q 725 4325 350 4300 
L 350 4750 
Q 725 4725 1125 4725 
L 2300 4725 
z
M 4325 4875 
Q 4725 4125 5150 3775 
Q 5575 3425 6150 3225 
Q 5925 3025 5800 2725 
Q 5450 2925 5125 3200 
L 5125 2775 
Q 4800 2800 4200 2800 
Q 3600 2800 3225 2775 
L 3225 3175 
Q 3075 2975 2800 2650 
Q 2575 2875 2325 3000 
Q 2725 3275 2937 3550 
Q 3150 3825 3312 4125 
Q 3475 4425 3587 4675 
Q 3700 4925 3775 5225 
Q 4100 5150 4450 5075 
L 4325 4875 
z
M 5600 2150 
Q 5475 2000 5400 1737 
Q 5325 1475 5162 912 
Q 5000 350 4900 25 
L 5250 25 
Q 5425 25 5800 50 
L 5800 -400 
Q 5475 -375 5250 -375 
L 3325 -375 
Q 2875 -375 2675 -400 
L 2675 50 
Q 2975 25 3350 25 
L 4475 25 
Q 4675 575 4800 1150 
L 5050 2300 
Q 5250 2225 5600 2150 
z
M 5100 3225 
Q 4800 3450 4550 3775 
Q 4300 4100 4050 4600 
Q 3850 4200 3687 3912 
Q 3525 3625 3250 3225 
L 5100 3225 
z
M 3250 2175 
Q 3350 1825 3462 1375 
Q 3575 925 3675 550 
Q 3450 525 3250 425 
Q 3175 900 3087 1225 
Q 3000 1550 2850 2050 
Q 3050 2125 3250 2175 
z
M 1775 1125 
Q 1750 950 1725 700 
Q 1350 625 1112 575 
Q 875 525 375 375 
Q 300 650 200 925 
Q 550 950 925 1000 
Q 1300 1050 1775 1125 
z
M 4300 1650 
Q 4350 1325 4425 825 
L 4025 725 
Q 3925 1275 3875 1562 
Q 3825 1850 3750 2225 
Q 3975 2275 4200 2300 
L 4300 1650 
z
" transform="scale(0.015625)"/>
        <path id="SimHei-524d" d="M 2075 -100 
Q 2400 -100 2537 -87 
Q 2675 -75 2675 100 
L 2675 875 
L 1325 875 
L 1325 -650 
L 800 -650 
Q 850 -200 850 350 
L 850 2350 
Q 850 2850 825 3300 
L 3175 3300 
Q 3150 2875 3150 2375 
L 3150 -150 
Q 3150 -400 2950 -475 
Q 2750 -550 2275 -625 
Q 2200 -350 2075 -100 
z
M 4950 5075 
Q 4825 4975 4687 4750 
Q 4550 4525 4375 4225 
L 5400 4225 
Q 5750 4225 6175 4250 
L 6175 3775 
Q 5775 3825 5300 3825 
L 1350 3825 
Q 700 3825 250 3775 
L 250 4250 
Q 775 4225 1350 4225 
L 3875 4225 
Q 4300 5025 4350 5300 
Q 4625 5150 4950 5075 
z
M 4375 -75 
Q 4825 -100 4937 -75 
Q 5050 -50 5050 150 
L 5050 2750 
Q 5050 3050 5025 3400 
L 5550 3400 
Q 5525 3075 5525 2750 
L 5525 -25 
Q 5525 -350 5362 -450 
Q 5200 -550 4600 -600 
Q 4550 -275 4375 -75 
z
M 3775 575 
Q 3800 950 3800 1225 
L 3800 2375 
Q 3800 2775 3775 3075 
L 4300 3075 
Q 4275 2775 4275 2375 
L 4275 1250 
Q 4275 875 4300 575 
L 3775 575 
z
M 2675 2250 
L 2675 2925 
L 1325 2925 
L 1325 2250 
L 2675 2250 
z
M 2675 1250 
L 2675 1875 
L 1325 1875 
L 1325 1250 
L 2675 1250 
z
M 2225 4300 
Q 2125 4575 1750 5050 
Q 2025 5175 2200 5300 
Q 2550 4850 2725 4550 
Q 2500 4450 2225 4300 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-5b9e"/>
       <use xlink:href="#SimHei-9a8c" x="100"/>
       <use xlink:href="#SimHei-524d" x="200"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 442.863519 393.2725 
L 442.863519 25.09125 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m1b760a0460" x="442.863519" y="393.2725" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 实验后 -->
      <g transform="translate(430.863519 406.67875) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-540e" d="M 5475 2150 
Q 5450 1750 5450 775 
Q 5450 -175 5500 -550 
L 4975 -550 
L 4975 -25 
L 2525 -25 
L 2525 -600 
L 2025 -600 
Q 2050 -175 2050 775 
Q 2050 1750 2025 2150 
L 5475 2150 
z
M 5175 5075 
Q 5300 4800 5525 4525 
Q 4950 4475 3750 4337 
Q 2550 4200 1575 4175 
L 1575 3375 
L 5200 3375 
Q 5550 3375 5975 3400 
L 5975 2900 
Q 5575 2925 5200 2925 
L 1575 2925 
Q 1550 2425 1487 1875 
Q 1425 1325 1250 712 
Q 1075 100 800 -325 
Q 650 -200 225 -50 
Q 625 325 825 1062 
Q 1025 1800 1050 2825 
Q 1075 3850 1075 4575 
Q 2300 4600 3625 4775 
Q 4950 4950 5175 5075 
z
M 4975 375 
L 4975 1725 
L 2525 1725 
L 2525 375 
L 4975 375 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-5b9e"/>
       <use xlink:href="#SimHei-9a8c" x="100"/>
       <use xlink:href="#SimHei-540e" x="200"/>
      </g>
     </g>
    </g>
    <g id="text_3">
     <!-- 测量时间点 -->
     <g transform="translate(277.246875 419.749062) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-6d4b" d="M 4025 4725 
Q 4000 4475 4000 4000 
L 4000 2225 
Q 4000 1775 4025 1350 
L 3550 1350 
L 3550 4325 
L 2350 4325 
L 2350 1325 
L 1900 1325 
Q 1925 1775 1925 2225 
L 1925 4000 
Q 1925 4200 1900 4725 
L 4025 4725 
z
M 5900 5075 
Q 5875 4800 5875 4375 
L 5875 0 
Q 5875 -325 5650 -425 
Q 5425 -525 4975 -600 
Q 4925 -325 4775 -50 
Q 5125 -50 5262 -25 
Q 5400 0 5400 250 
L 5400 4375 
Q 5400 4775 5375 5075 
L 5900 5075 
z
M 3275 3725 
Q 3225 3400 3200 2325 
Q 3175 1250 2837 550 
Q 2500 -150 2075 -600 
Q 1850 -400 1600 -325 
Q 2200 250 2475 962 
Q 2750 1675 2762 2475 
Q 2775 3275 2725 3800 
Q 3000 3750 3275 3725 
z
M 4950 4275 
Q 4925 4000 4925 3575 
L 4925 1825 
Q 4925 1300 4950 850 
L 4450 850 
Q 4475 1150 4475 1825 
L 4475 3575 
Q 4475 4000 4450 4275 
L 4950 4275 
z
M 1700 1675 
Q 1375 1075 1112 487 
Q 850 -100 725 -400 
Q 500 -200 200 -25 
Q 475 300 762 850 
Q 1050 1400 1250 2000 
Q 1475 1825 1700 1675 
z
M 3400 1050 
Q 3800 650 4300 -125 
Q 4100 -225 3875 -450 
Q 3475 275 3025 750 
Q 3225 875 3400 1050 
z
M 600 3525 
Q 1125 3125 1500 2775 
Q 1300 2575 1150 2375 
Q 825 2775 300 3150 
Q 450 3300 600 3525 
z
M 1350 4000 
Q 950 4375 475 4675 
Q 625 4850 775 5050 
Q 1300 4700 1675 4375 
Q 1500 4250 1350 4000 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-91cf" d="M 5250 2500 
Q 5225 2200 5225 1700 
Q 5225 1225 5250 950 
L 3425 950 
L 3425 625 
L 4575 625 
Q 5000 625 5375 650 
L 5375 275 
Q 5025 300 4575 300 
L 3425 300 
L 3425 -75 
L 5125 -75 
Q 5525 -75 5925 -50 
L 5925 -425 
Q 5525 -400 5125 -400 
L 1275 -400 
Q 900 -400 475 -425 
L 475 -50 
Q 925 -50 1275 -75 
L 2975 -75 
L 2975 300 
L 1725 300 
Q 1350 300 975 250 
L 975 650 
Q 1375 625 1725 625 
L 2975 625 
L 2975 950 
L 1150 950 
Q 1175 1375 1175 1775 
Q 1175 2175 1150 2500 
L 5250 2500 
z
M 5200 4950 
Q 5175 4575 5175 4250 
Q 5175 3925 5200 3400 
L 1200 3400 
Q 1225 3925 1225 4250 
Q 1225 4575 1200 4950 
L 5200 4950 
z
M 4900 3100 
Q 5500 3125 6000 3150 
L 6000 2750 
Q 5500 2775 4925 2775 
L 1650 2775 
Q 975 2775 325 2750 
L 325 3150 
Q 975 3125 1650 3100 
L 4900 3100 
z
M 4725 3700 
L 4725 4025 
L 1675 4025 
L 1675 3700 
L 4725 3700 
z
M 4725 4350 
L 4725 4650 
L 1675 4650 
L 1675 4350 
L 4725 4350 
z
M 4750 1875 
L 4750 2175 
L 3425 2175 
L 3425 1875 
L 4750 1875 
z
M 2975 1875 
L 2975 2175 
L 1650 2175 
L 1650 1875 
L 2975 1875 
z
M 2975 1275 
L 2975 1550 
L 1650 1550 
L 1650 1275 
L 2975 1275 
z
M 4750 1275 
L 4750 1550 
L 3425 1550 
L 3425 1275 
L 4750 1275 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-65f6" d="M 2525 4575 
Q 2500 3675 2500 2400 
Q 2500 1125 2525 400 
L 2000 400 
L 2000 875 
L 1000 875 
L 1000 50 
L 475 50 
Q 500 1200 500 2350 
Q 500 3525 475 4575 
L 2525 4575 
z
M 4875 3825 
Q 4875 4575 4850 5225 
L 5400 5225 
Q 5375 4575 5375 3825 
Q 5750 3825 6175 3850 
L 6175 3375 
Q 5750 3400 5375 3400 
L 5375 50 
Q 5375 -325 5075 -425 
Q 4775 -525 4300 -575 
Q 4300 -275 4025 0 
Q 4500 -50 4687 -25 
Q 4875 0 4875 300 
L 4875 3400 
Q 3625 3400 2775 3375 
L 2775 3850 
Q 3575 3825 4875 3825 
z
M 2000 1300 
L 2000 2550 
L 1000 2550 
L 1000 1300 
L 2000 1300 
z
M 2000 2975 
L 2000 4150 
L 1000 4150 
L 1000 2975 
L 2000 2975 
z
M 3550 2725 
Q 3875 2200 4175 1600 
Q 3925 1525 3650 1375 
Q 3450 1975 3075 2500 
Q 3300 2600 3550 2725 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-95f4" d="M 4450 3550 
Q 4425 3075 4425 2725 
L 4425 1400 
Q 4425 1075 4450 625 
L 1925 625 
Q 1950 1075 1950 1425 
L 1950 2725 
Q 1950 3075 1925 3550 
L 4450 3550 
z
M 5800 4875 
Q 5775 4400 5775 3925 
L 5775 250 
Q 5800 -325 5462 -450 
Q 5125 -575 4725 -625 
Q 4700 -325 4525 -25 
Q 5000 -50 5137 25 
Q 5275 100 5275 350 
L 5275 4450 
L 3775 4450 
Q 3175 4450 2650 4425 
L 2650 4900 
Q 3175 4875 3775 4875 
L 5800 4875 
z
M 550 -525 
Q 600 -25 600 525 
L 600 3150 
Q 600 3675 550 4025 
L 1125 4025 
Q 1100 3650 1100 3300 
L 1100 500 
Q 1100 0 1125 -525 
L 550 -525 
z
M 3950 1025 
L 3950 1950 
L 2425 1950 
L 2425 1025 
L 3950 1025 
z
M 3950 2350 
L 3950 3150 
L 2425 3150 
L 2425 2350 
L 3950 2350 
z
M 1525 5300 
Q 1750 5000 2225 4350 
Q 1950 4225 1700 4075 
Q 1450 4575 1075 5025 
Q 1350 5225 1525 5300 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-70b9" d="M 3325 5200 
Q 3300 4925 3300 4375 
L 4900 4375 
Q 5275 4375 5725 4400 
L 5725 3925 
Q 5300 3950 4900 3950 
L 3300 3950 
L 3300 3225 
L 5225 3225 
Q 5200 2700 5200 2150 
Q 5200 1625 5225 1175 
L 1175 1175 
Q 1200 1625 1200 2175 
Q 1200 2725 1175 3225 
L 2775 3225 
L 2775 4500 
Q 2775 4900 2750 5200 
L 3325 5200 
z
M 4700 1600 
L 4700 2800 
L 1725 2800 
L 1725 1600 
L 4700 1600 
z
M 1500 750 
Q 1025 -250 825 -625 
Q 625 -475 325 -375 
Q 650 50 950 925 
Q 1200 800 1500 750 
z
M 5325 850 
Q 5775 150 6075 -350 
Q 5925 -425 5575 -625 
Q 5375 -150 4900 575 
Q 5150 700 5325 850 
z
M 3975 -575 
Q 3775 -100 3400 575 
Q 3575 675 3825 825 
Q 4350 -75 4450 -325 
Q 4200 -450 3975 -575 
z
M 2375 -575 
Q 2350 -300 1975 550 
Q 2200 625 2425 775 
Q 2800 -75 2900 -350 
Q 2625 -450 2375 -575 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-6d4b"/>
      <use xlink:href="#SimHei-91cf" x="100"/>
      <use xlink:href="#SimHei-65f6" x="200"/>
      <use xlink:href="#SimHei-95f4" x="300"/>
      <use xlink:href="#SimHei-70b9" x="400"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_5">
      <path d="M 39.29375 393.2725 
L 565.2 393.2725 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <defs>
       <path id="m5e1675a712" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="393.2725" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0 -->
      <g transform="translate(28.29375 396.0225) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-30" d="M 225 2537 
Q 250 3200 412 3587 
Q 575 3975 875 4225 
Q 1175 4475 1612 4475 
Q 2050 4475 2375 4112 
Q 2700 3750 2800 3200 
Q 2900 2650 2862 1937 
Q 2825 1225 2612 775 
Q 2400 325 1975 150 
Q 1550 -25 1125 187 
Q 700 400 525 750 
Q 350 1100 275 1487 
Q 200 1875 225 2537 
z
M 750 2687 
Q 675 2000 800 1462 
Q 925 925 1212 700 
Q 1500 475 1800 612 
Q 2100 750 2237 1162 
Q 2375 1575 2375 2062 
Q 2375 2550 2337 2950 
Q 2300 3350 2112 3675 
Q 1925 4000 1612 4012 
Q 1300 4025 1062 3700 
Q 825 3375 750 2687 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_7">
      <path d="M 39.29375 326.550441 
L 565.2 326.550441 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="326.550441" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 10 -->
      <g transform="translate(24.29375 329.300441) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-31" d="M 1400 3600 
Q 1075 3275 575 2975 
L 575 3450 
Q 1200 3875 1600 4450 
L 1900 4450 
L 1900 150 
L 1400 150 
L 1400 3600 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-31"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_9">
      <path d="M 39.29375 259.828383 
L 565.2 259.828383 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="259.828383" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 20 -->
      <g transform="translate(24.29375 262.578383) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-32" d="M 300 250 
Q 325 625 650 925 
Q 975 1225 1475 1862 
Q 1975 2500 2125 2850 
Q 2275 3200 2237 3450 
Q 2200 3700 2000 3862 
Q 1800 4025 1537 4000 
Q 1275 3975 1037 3800 
Q 800 3625 675 3275 
L 200 3350 
Q 400 3925 712 4187 
Q 1025 4450 1450 4475 
Q 1700 4500 1900 4462 
Q 2100 4425 2312 4287 
Q 2525 4150 2662 3875 
Q 2800 3600 2762 3212 
Q 2725 2825 2375 2287 
Q 2025 1750 1025 600 
L 2825 600 
L 2825 150 
L 300 150 
L 300 250 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-32"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <path d="M 39.29375 193.106324 
L 565.2 193.106324 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="193.106324" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 30 -->
      <g transform="translate(24.29375 195.856324) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-33" d="M 250 1225 
L 700 1300 
Q 800 975 1025 762 
Q 1250 550 1587 562 
Q 1925 575 2125 837 
Q 2325 1100 2300 1437 
Q 2275 1775 2037 1962 
Q 1800 2150 1275 2225 
L 1275 2550 
Q 1800 2600 2037 2825 
Q 2275 3050 2250 3412 
Q 2225 3775 1925 3937 
Q 1625 4100 1287 3975 
Q 950 3850 750 3275 
L 300 3350 
Q 450 3800 712 4100 
Q 975 4400 1425 4450 
Q 1875 4500 2212 4337 
Q 2550 4175 2687 3837 
Q 2825 3500 2725 3100 
Q 2625 2700 2150 2400 
Q 2500 2250 2687 1950 
Q 2875 1650 2812 1162 
Q 2750 675 2375 375 
Q 2000 75 1525 87 
Q 1050 100 700 387 
Q 350 675 250 1225 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-33"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_13">
      <path d="M 39.29375 126.384265 
L 565.2 126.384265 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="126.384265" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 40 -->
      <g transform="translate(24.29375 129.134265) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-34" d="M 2000 1100 
L 75 1100 
L 75 1525 
L 2100 4450 
L 2475 4450 
L 2475 1525 
L 3075 1525 
L 3075 1100 
L 2475 1100 
L 2475 150 
L 2000 150 
L 2000 1100 
z
M 2000 1525 
L 2000 3500 
L 600 1525 
L 2000 1525 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-34"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_15">
      <path d="M 39.29375 59.662206 
L 565.2 59.662206 
" clip-path="url(#p174293f248)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m5e1675a712" x="39.29375" y="59.662206" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 50 -->
      <g transform="translate(24.29375 62.412206) scale(0.08 -0.08)">
       <defs>
        <path id="SimHei-35" d="M 550 1325 
Q 725 650 1150 575 
Q 1575 500 1837 662 
Q 2100 825 2212 1087 
Q 2325 1350 2312 1675 
Q 2300 2000 2137 2225 
Q 1975 2450 1725 2525 
Q 1475 2600 1162 2525 
Q 850 2450 650 2175 
L 225 2225 
Q 275 2375 700 4375 
L 2675 4375 
L 2675 3925 
L 1075 3925 
Q 950 3250 825 2850 
Q 1200 3025 1525 3012 
Q 1850 3000 2150 2862 
Q 2450 2725 2587 2487 
Q 2725 2250 2787 2012 
Q 2850 1775 2837 1500 
Q 2825 1225 2725 937 
Q 2625 650 2425 462 
Q 2225 275 1937 162 
Q 1650 50 1275 75 
Q 900 100 562 350 
Q 225 600 100 1200 
L 550 1325 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#SimHei-35"/>
       <use xlink:href="#SimHei-30" x="50"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- 状态焦虑分数 -->
     <g transform="translate(19.04375 239.181875) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="SimHei-72b6" d="M 2225 -275 
Q 2675 25 2975 475 
Q 3275 925 3450 1350 
Q 3625 1775 3700 2175 
Q 3775 2575 3800 2875 
L 3225 2875 
Q 2850 2875 2500 2850 
L 2500 3325 
Q 2850 3300 3200 3300 
L 3825 3300 
Q 3825 3975 3812 4375 
Q 3800 4775 3775 5075 
L 4425 5075 
Q 4350 4750 4337 4412 
Q 4325 4075 4325 3300 
L 5350 3300 
Q 5625 3300 5975 3325 
L 5975 2850 
Q 5625 2875 5350 2875 
L 4375 2875 
Q 4675 1450 5012 962 
Q 5350 475 5625 262 
Q 5900 50 6125 -75 
Q 5775 -225 5625 -525 
Q 5225 -200 4987 112 
Q 4750 425 4587 725 
Q 4425 1025 4312 1337 
Q 4200 1650 4125 2000 
Q 4000 1400 3812 975 
Q 3625 550 3425 237 
Q 3225 -75 2750 -625 
Q 2525 -400 2225 -275 
z
M 2225 5200 
Q 2200 4825 2200 4250 
L 2200 775 
Q 2200 50 2225 -625 
L 1675 -625 
Q 1700 75 1700 775 
L 1700 1950 
Q 1525 1750 1200 1350 
Q 875 950 700 750 
Q 500 1000 275 1150 
Q 575 1375 912 1712 
Q 1250 2050 1700 2600 
L 1700 4250 
Q 1700 4825 1675 5200 
L 2225 5200 
z
M 625 4325 
Q 800 4125 1000 3875 
Q 1200 3625 1375 3375 
Q 1175 3225 1050 3075 
Q 850 3350 250 4050 
Q 400 4175 625 4325 
z
M 5475 3650 
Q 5250 3975 4700 4475 
Q 4900 4625 5025 4750 
Q 5250 4575 5425 4387 
Q 5600 4200 5825 3975 
Q 5625 3800 5475 3650 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6001" d="M 2575 4325 
Q 2700 4800 2725 5250 
Q 3075 5125 3350 5100 
Q 3225 4875 3100 4325 
L 5025 4325 
Q 5375 4325 5825 4375 
L 5825 3850 
Q 5350 3875 5000 3875 
L 3975 3875 
Q 4225 3300 4575 2987 
Q 4925 2675 5250 2525 
Q 5575 2375 6100 2275 
Q 5800 2075 5675 1775 
Q 4900 2100 4525 2412 
Q 4150 2725 3900 3075 
Q 3650 3425 3500 3875 
L 2975 3875 
Q 2675 3200 2187 2687 
Q 1700 2175 725 1625 
Q 650 1850 300 2075 
Q 900 2275 1512 2750 
Q 2125 3225 2425 3875 
L 1300 3875 
Q 900 3875 450 3850 
L 450 4375 
Q 875 4325 1300 4325 
L 2575 4325 
z
M 2525 1350 
Q 2500 875 2500 575 
Q 2500 300 2512 150 
Q 2525 0 2800 0 
L 3825 0 
Q 4025 -25 4175 62 
Q 4325 150 4400 625 
Q 4650 450 4975 375 
Q 4775 -150 4625 -312 
Q 4475 -475 4000 -450 
L 2500 -450 
Q 2050 -400 2012 -162 
Q 1975 75 1975 425 
Q 1975 800 1950 1350 
L 2525 1350 
z
M 1425 1175 
Q 1175 450 825 -325 
Q 625 -175 325 -125 
Q 775 625 875 1375 
Q 1175 1250 1425 1175 
z
M 5175 1525 
Q 5600 950 6050 350 
Q 5800 175 5600 0 
Q 5225 700 4750 1250 
Q 5000 1350 5175 1525 
z
M 2700 3050 
Q 3175 2675 3500 2325 
Q 3275 2175 3100 1975 
Q 2825 2350 2350 2700 
L 2700 3050 
z
M 3375 1750 
Q 3625 1350 3825 925 
Q 3625 800 3375 675 
Q 3200 1175 2950 1525 
Q 3175 1600 3375 1750 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-7126" d="M 2375 4900 
Q 2125 4625 1975 4300 
L 5075 4300 
Q 5400 4300 5900 4325 
L 5900 3850 
Q 5400 3875 5075 3875 
L 3875 3875 
L 3875 3300 
L 4700 3300 
Q 5200 3300 5600 3325 
L 5600 2875 
Q 5200 2900 4700 2900 
L 3875 2900 
L 3875 2325 
L 4750 2325 
Q 5150 2325 5600 2350 
L 5600 1900 
Q 5150 1925 4750 1925 
L 3875 1925 
L 3875 1400 
L 5025 1400 
Q 5500 1400 5950 1425 
L 5950 950 
Q 5500 1000 5050 1000 
L 1750 1000 
L 1750 675 
L 1200 675 
Q 1225 1250 1225 1700 
L 1225 3075 
Q 925 2625 600 2275 
Q 400 2525 200 2675 
Q 800 3225 1225 3950 
Q 1650 4675 1800 5200 
Q 2100 5025 2375 4900 
z
M 3350 2325 
L 3350 2900 
L 1750 2900 
L 1750 2325 
L 3350 2325 
z
M 3350 3300 
L 3350 3875 
L 1750 3875 
L 1750 3300 
L 3350 3300 
z
M 3350 1400 
L 3350 1925 
L 1750 1925 
L 1750 1400 
L 3350 1400 
z
M 350 -500 
Q 775 -25 975 525 
Q 1225 450 1500 375 
Q 1250 -25 925 -700 
Q 700 -575 350 -500 
z
M 3000 -525 
Q 2675 -575 2450 -650 
Q 2375 -175 2225 375 
Q 2450 425 2775 525 
L 3000 -525 
z
M 5675 -575 
Q 5500 -225 5125 425 
Q 5350 525 5550 700 
Q 5850 150 6175 -250 
Q 5850 -400 5675 -575 
z
M 4175 600 
Q 4425 -100 4525 -425 
Q 4225 -500 4000 -575 
Q 3900 -150 3650 450 
Q 3950 525 4175 600 
z
M 3600 5275 
Q 3800 5025 4175 4625 
Q 3925 4525 3700 4350 
Q 3500 4650 3200 5025 
Q 3400 5125 3600 5275 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-8651" d="M 3225 5275 
Q 3200 5000 3200 4825 
L 4550 4825 
Q 4900 4825 5325 4850 
L 5325 4425 
Q 4900 4450 4550 4450 
L 3200 4450 
L 3200 3950 
L 5975 3950 
Q 5825 3550 5700 3000 
Q 5450 3075 5175 3100 
Q 5250 3350 5325 3575 
L 3300 3575 
L 3300 3050 
Q 4175 3150 4725 3225 
L 4825 2850 
Q 4350 2800 3300 2650 
L 3300 2400 
Q 3325 2150 3650 2150 
L 4500 2150 
Q 4750 2150 4862 2175 
Q 4975 2200 5075 2675 
Q 5225 2525 5575 2400 
Q 5400 1975 5237 1850 
Q 5075 1725 4675 1750 
L 3475 1750 
Q 2850 1750 2850 2225 
L 2850 2600 
Q 1850 2450 1550 2400 
L 1525 2850 
Q 1850 2850 2850 2975 
L 2850 3575 
L 1350 3575 
Q 1350 1775 1300 1287 
Q 1250 800 1112 362 
Q 975 -75 725 -600 
Q 575 -475 250 -325 
Q 475 50 650 450 
Q 825 850 862 1287 
Q 900 1725 900 2625 
Q 900 3525 875 3950 
L 2750 3950 
L 2750 4475 
Q 2750 4825 2725 5275 
L 3225 5275 
z
M 3050 1125 
Q 3025 750 3025 500 
L 3025 150 
Q 3025 -150 3350 -150 
L 4050 -150 
Q 4350 -150 4475 -87 
Q 4600 -25 4650 400 
Q 4875 175 5150 125 
Q 4975 -300 4862 -425 
Q 4750 -550 4300 -550 
L 3175 -550 
Q 2575 -550 2575 -100 
L 2575 300 
Q 2575 750 2550 1125 
L 3050 1125 
z
M 2350 1000 
Q 1900 50 1700 -325 
Q 1500 -225 1250 -75 
Q 1475 225 1875 1225 
Q 2150 1075 2350 1000 
z
M 5225 1350 
Q 5500 1050 6125 200 
Q 5900 50 5725 -75 
Q 5425 350 4875 1050 
Q 4975 1125 5225 1350 
z
M 3700 1575 
Q 4200 925 4350 675 
Q 4075 525 3925 400 
Q 3800 650 3325 1325 
Q 3500 1425 3700 1575 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-5206" d="M 1000 -650 
Q 825 -375 575 -225 
Q 975 -75 1300 137 
Q 1625 350 1875 662 
Q 2125 975 2250 1350 
Q 2375 1725 2425 2275 
Q 1850 2275 1500 2250 
L 1500 2775 
Q 1900 2750 2525 2750 
L 5025 2750 
Q 4975 2375 4950 2100 
L 4750 250 
Q 4700 -200 4350 -350 
Q 4000 -500 3575 -525 
Q 3550 -275 3325 50 
Q 3850 25 4037 87 
Q 4225 150 4275 525 
L 4450 2275 
L 2950 2275 
Q 2875 1625 2737 1187 
Q 2600 750 2350 425 
Q 2100 100 1750 -175 
Q 1400 -450 1000 -650 
z
M 3950 5175 
Q 4125 4650 4662 3975 
Q 5200 3300 6175 2800 
Q 5975 2625 5775 2275 
Q 5325 2600 4987 2887 
Q 4650 3175 4425 3425 
Q 4200 3675 3925 4137 
Q 3650 4600 3475 5000 
Q 3750 5075 3950 5175 
z
M 2700 4825 
Q 2475 4425 2325 4125 
Q 2175 3825 2025 3587 
Q 1875 3350 1575 2950 
Q 1275 2550 850 2125 
Q 625 2350 400 2475 
Q 900 2925 1187 3300 
Q 1475 3675 1737 4112 
Q 2000 4550 2200 5075 
Q 2400 4925 2700 4825 
z
" transform="scale(0.015625)"/>
       <path id="SimHei-6570" d="M 4525 4925 
Q 4475 4800 4400 4587 
Q 4325 4375 4175 3825 
L 5325 3825 
Q 5575 3825 5925 3850 
L 5925 3400 
Q 5650 3425 5500 3425 
Q 5500 3025 5362 2162 
Q 5225 1300 4875 675 
Q 5125 375 5437 137 
Q 5750 -100 6025 -200 
Q 5725 -425 5625 -625 
Q 5325 -450 5075 -225 
Q 4825 0 4575 325 
Q 4300 25 3987 -187 
Q 3675 -400 3200 -650 
Q 3075 -425 2850 -300 
Q 3225 -150 3650 125 
Q 4075 400 4300 675 
Q 4125 1025 3962 1425 
Q 3800 1825 3675 2475 
Q 3600 2275 3425 1950 
Q 3250 2050 3000 2150 
Q 3400 2850 3650 3675 
Q 3900 4500 3975 5100 
Q 4275 4975 4525 4925 
z
M 2100 2250 
Q 2000 2150 1875 1925 
L 3250 1925 
Q 3100 1300 2725 675 
Q 3125 525 3350 400 
Q 3225 225 3125 25 
Q 2900 175 2450 325 
Q 1950 -225 725 -650 
Q 600 -375 400 -275 
Q 1575 0 2000 475 
Q 1300 650 850 775 
Q 1000 1000 1250 1525 
Q 1000 1525 425 1500 
L 425 1950 
Q 900 1925 1400 1925 
Q 1500 2150 1550 2425 
Q 1825 2325 2100 2250 
z
M 1775 3950 
Q 1775 4550 1750 5100 
L 2250 5100 
Q 2225 4575 2225 3950 
Q 3075 3950 3425 3975 
L 3425 3525 
Q 3075 3550 2225 3550 
Q 2225 2825 2250 2425 
L 1750 2425 
Q 1775 2775 1775 3300 
Q 1650 3100 1300 2787 
Q 950 2475 650 2300 
Q 525 2525 275 2625 
Q 500 2700 875 2975 
Q 1250 3250 1450 3550 
Q 950 3550 500 3525 
L 500 3975 
Q 925 3950 1775 3950 
z
M 3975 3175 
Q 4200 1700 4600 1125 
Q 4850 1700 4937 2287 
Q 5025 2875 5050 3425 
L 4075 3425 
L 3975 3175 
z
M 1450 1000 
Q 1750 925 2275 800 
Q 2475 1050 2650 1525 
L 1725 1525 
Q 1600 1275 1450 1000 
z
M 3325 4825 
Q 3200 4625 3125 4450 
Q 3050 4275 2925 4025 
Q 2725 4125 2525 4175 
Q 2725 4500 2900 5000 
Q 3150 4875 3325 4825 
z
M 2575 3375 
Q 2900 3050 3200 2725 
Q 3050 2600 2875 2425 
Q 2525 2850 2300 3100 
Q 2450 3225 2575 3375 
z
M 925 5000 
Q 1275 4650 1500 4300 
L 1125 4075 
Q 950 4425 600 4725 
Q 825 4875 925 5000 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#SimHei-72b6"/>
      <use xlink:href="#SimHei-6001" x="100"/>
      <use xlink:href="#SimHei-7126" x="200"/>
      <use xlink:href="#SimHei-8651" x="300"/>
      <use xlink:href="#SimHei-5206" x="400"/>
      <use xlink:href="#SimHei-6570" x="500"/>
     </g>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 63.19858 393.2725 
L 161.630231 393.2725 
L 161.630231 137.373781 
L 63.19858 137.373781 
z
" clip-path="url(#p174293f248)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_4">
    <path d="M 344.431868 393.2725 
L 442.863519 393.2725 
L 442.863519 53.774966 
L 344.431868 53.774966 
z
" clip-path="url(#p174293f248)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_5">
    <path d="M 161.630231 393.2725 
L 260.061882 393.2725 
L 260.061882 163.303804 
L 161.630231 163.303804 
z
" clip-path="url(#p174293f248)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_6">
    <path d="M 442.863519 393.2725 
L 541.29517 393.2725 
L 541.29517 117.043177 
L 442.863519 117.043177 
z
" clip-path="url(#p174293f248)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="LineCollection_1">
    <path d="M 112.414405 146.963539 
L 112.414405 127.784022 
" clip-path="url(#p174293f248)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 393.647694 64.926241 
L 393.647694 42.62369 
" clip-path="url(#p174293f248)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_17">
    <defs>
     <path id="m4a252d1556" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p174293f248)">
     <use xlink:href="#m4a252d1556" x="112.414405" y="146.963539" style="stroke: #000000"/>
     <use xlink:href="#m4a252d1556" x="393.647694" y="64.926241" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_18">
    <g clip-path="url(#p174293f248)">
     <use xlink:href="#m4a252d1556" x="112.414405" y="127.784022" style="stroke: #000000"/>
     <use xlink:href="#m4a252d1556" x="393.647694" y="42.62369" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 210.846056 181.661659 
L 210.846056 144.945949 
" clip-path="url(#p174293f248)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 492.079345 137.163317 
L 492.079345 96.923037 
" clip-path="url(#p174293f248)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_19">
    <g clip-path="url(#p174293f248)">
     <use xlink:href="#m4a252d1556" x="210.846056" y="181.661659" style="stroke: #000000"/>
     <use xlink:href="#m4a252d1556" x="492.079345" y="137.163317" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_20">
    <g clip-path="url(#p174293f248)">
     <use xlink:href="#m4a252d1556" x="210.846056" y="144.945949" style="stroke: #000000"/>
     <use xlink:href="#m4a252d1556" x="492.079345" y="96.923037" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_7">
    <path d="M 39.29375 393.2725 
L 39.29375 25.09125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_8">
    <path d="M 565.2 393.2725 
L 565.2 25.09125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 39.29375 393.2725 
L 565.2 393.2725 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 39.29375 25.09125 
L 565.2 25.09125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_11">
    <!-- 高低焦虑组状态焦虑前后变化 -->
    <g transform="translate(237.246875 19.09125) scale(0.1 -0.1)">
     <defs>
      <path id="SimHei-9ad8" d="M 5825 -25 
Q 5850 -400 5550 -500 
Q 5250 -600 4975 -625 
Q 4875 -325 4725 -100 
Q 4975 -100 5175 -87 
Q 5375 -75 5350 250 
L 5350 1825 
L 1100 1825 
L 1100 -575 
L 600 -575 
Q 625 -225 625 850 
Q 625 1950 600 2200 
L 5850 2200 
Q 5825 1850 5825 1425 
L 5825 -25 
z
M 4925 3800 
Q 4900 3475 4900 3175 
Q 4900 2900 4925 2600 
L 1400 2600 
Q 1425 2825 1425 3175 
Q 1425 3525 1400 3800 
L 4925 3800 
z
M 4500 1400 
Q 4475 1125 4475 800 
Q 4475 500 4500 200 
L 1950 200 
Q 2000 500 2000 800 
Q 2000 1125 1950 1400 
L 4500 1400 
z
M 425 4600 
Q 750 4575 1125 4575 
L 2925 4575 
Q 2850 4875 2750 5100 
Q 3050 5125 3350 5200 
Q 3400 4950 3525 4575 
L 5250 4575 
Q 5600 4575 5975 4600 
L 5975 4150 
Q 5600 4175 5050 4175 
L 1150 4175 
Q 750 4175 425 4150 
L 425 4600 
z
M 4450 2975 
L 4450 3425 
L 1900 3425 
L 1900 2975 
L 4450 2975 
z
M 4025 550 
L 4025 1050 
L 2400 1050 
L 2400 550 
L 4025 550 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-4f4e" d="M 2300 4550 
Q 2750 4525 3750 4637 
Q 4750 4750 5275 5000 
Q 5425 4725 5600 4475 
Q 5125 4400 4350 4300 
Q 4350 3825 4400 2850 
L 5000 2850 
Q 5425 2850 5850 2875 
L 5850 2425 
Q 5425 2450 5025 2450 
L 4450 2450 
Q 4575 1325 4862 762 
Q 5150 200 5325 75 
Q 5500 -50 5562 125 
Q 5625 300 5675 775 
Q 5925 575 6150 525 
Q 6000 -100 5850 -337 
Q 5700 -575 5350 -500 
Q 5000 -425 4587 262 
Q 4175 950 4000 2450 
L 2775 2450 
L 2775 500 
Q 3275 950 3575 1275 
Q 3675 975 3775 800 
Q 2775 -50 2500 -350 
Q 2325 -175 2175 25 
Q 2300 150 2325 500 
L 2325 3725 
Q 2325 4075 2300 4550 
z
M 2050 5075 
Q 1900 4900 1600 3950 
L 1600 425 
Q 1600 -75 1625 -625 
L 1100 -625 
Q 1125 -75 1125 450 
L 1125 3025 
Q 875 2500 625 2125 
Q 400 2350 225 2425 
Q 600 2900 962 3687 
Q 1325 4475 1500 5275 
Q 1575 5200 2050 5075 
z
M 3950 2850 
Q 3900 3775 3900 4225 
Q 3125 4175 2775 4150 
L 2775 2850 
L 3950 2850 
z
M 3850 -625 
Q 3450 -75 3250 100 
Q 3450 275 3575 425 
Q 3925 50 4225 -300 
Q 4025 -450 3850 -625 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-7ec4" d="M 6125 -425 
Q 5750 -400 5225 -400 
L 3275 -400 
Q 2775 -400 2400 -425 
L 2400 50 
Q 2725 25 2950 25 
L 2950 3775 
Q 2950 4375 2900 4625 
L 5525 4625 
Q 5500 4400 5500 3775 
L 5500 25 
Q 5825 25 6125 50 
L 6125 -425 
z
M 1725 4825 
Q 1500 4550 1287 4137 
Q 1075 3725 725 3050 
Q 950 3050 1550 3075 
Q 1800 3550 1900 3975 
Q 2075 3825 2450 3675 
Q 2225 3350 1937 2875 
Q 1650 2400 1150 1625 
Q 1950 1725 2425 1775 
L 2425 1350 
Q 2100 1325 1575 1250 
Q 1050 1175 575 1050 
Q 550 1300 400 1575 
Q 675 1675 912 1975 
Q 1150 2275 1375 2725 
Q 1225 2725 950 2687 
Q 675 2650 425 2525 
Q 325 2825 225 3050 
Q 450 3300 712 3862 
Q 975 4425 1150 5075 
Q 1425 4925 1725 4825 
z
M 5025 25 
L 5025 1275 
L 3425 1275 
L 3425 25 
L 5025 25 
z
M 5025 3125 
L 5025 4225 
L 3425 4225 
L 3425 3125 
L 5025 3125 
z
M 5025 1700 
L 5025 2725 
L 3425 2725 
L 3425 1700 
L 5025 1700 
z
M 2375 175 
Q 1875 100 1375 -12 
Q 875 -125 450 -300 
Q 375 -75 250 250 
Q 925 325 1162 375 
Q 1400 425 2375 600 
L 2375 175 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-53d8" d="M 5200 1975 
Q 4950 1600 4537 1137 
Q 4125 675 3675 375 
Q 3925 225 4600 87 
Q 5275 -50 6000 -75 
Q 5700 -400 5650 -625 
Q 4850 -525 4212 -312 
Q 3575 -100 3225 125 
Q 2800 -125 2150 -325 
Q 1500 -525 700 -675 
Q 600 -425 400 -175 
Q 950 -150 1687 12 
Q 2425 175 2825 375 
Q 2275 775 1675 1575 
Q 1350 1575 1000 1550 
L 1000 2000 
Q 1450 1975 2075 1975 
L 5200 1975 
z
M 3225 5275 
Q 3400 4900 3550 4475 
L 4675 4475 
Q 5400 4475 5975 4500 
L 5975 4025 
Q 5375 4050 4725 4050 
L 4125 4050 
L 4125 2950 
Q 4125 2550 4150 2150 
L 3600 2150 
Q 3625 2550 3625 2950 
L 3625 4050 
L 2750 4050 
L 2750 2950 
Q 2750 2675 2775 2150 
L 2225 2150 
Q 2250 2675 2250 2950 
L 2250 4050 
L 1700 4050 
Q 975 4050 425 4025 
L 425 4500 
Q 950 4475 1700 4475 
L 2975 4475 
Q 2925 4675 2700 5125 
Q 2950 5150 3225 5275 
z
M 2200 1575 
Q 2650 975 3250 600 
Q 3850 1000 4275 1575 
L 2200 1575 
z
M 4850 3750 
Q 5375 3275 5950 2675 
Q 5750 2525 5500 2300 
Q 5075 2800 4450 3400 
Q 4675 3575 4850 3750 
z
M 1825 3500 
Q 1700 3375 1425 3025 
Q 1150 2675 825 2300 
Q 575 2475 375 2600 
Q 975 3150 1300 3825 
Q 1525 3650 1825 3500 
z
" transform="scale(0.015625)"/>
      <path id="SimHei-5316" d="M 3925 5025 
Q 3900 4900 3900 4400 
L 3900 2625 
Q 4375 3025 4775 3450 
Q 5175 3875 5400 4300 
Q 5600 4100 5900 3925 
Q 5700 3700 5225 3212 
Q 4750 2725 4487 2512 
Q 4225 2300 3900 2025 
L 3900 450 
Q 3900 50 4225 50 
L 5200 50 
Q 5400 75 5462 262 
Q 5525 450 5575 850 
Q 5800 650 6150 600 
Q 6000 -100 5825 -250 
Q 5650 -400 5350 -400 
L 3950 -400 
Q 3425 -400 3400 200 
L 3400 1675 
Q 3225 1525 2962 1350 
Q 2700 1175 2375 975 
Q 2225 1225 2025 1400 
Q 2350 1550 2737 1787 
Q 3125 2025 3400 2250 
L 3400 4500 
Q 3400 4800 3375 5025 
L 3925 5025 
z
M 2400 4850 
Q 2175 4550 2075 4337 
Q 1975 4125 1825 3775 
L 1825 675 
Q 1825 -75 1850 -550 
L 1325 -550 
Q 1350 -125 1350 650 
L 1350 3050 
Q 1125 2725 962 2525 
Q 800 2325 675 2175 
Q 500 2375 275 2525 
Q 625 2850 875 3212 
Q 1125 3575 1312 3912 
Q 1500 4250 1625 4575 
Q 1750 4900 1825 5100 
Q 1975 4975 2400 4850 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#SimHei-9ad8"/>
     <use xlink:href="#SimHei-4f4e" x="100"/>
     <use xlink:href="#SimHei-7126" x="200"/>
     <use xlink:href="#SimHei-8651" x="300"/>
     <use xlink:href="#SimHei-7ec4" x="400"/>
     <use xlink:href="#SimHei-72b6" x="500"/>
     <use xlink:href="#SimHei-6001" x="600"/>
     <use xlink:href="#SimHei-7126" x="700"/>
     <use xlink:href="#SimHei-8651" x="800"/>
     <use xlink:href="#SimHei-524d" x="900"/>
     <use xlink:href="#SimHei-540e" x="1000"/>
     <use xlink:href="#SimHei-53d8" x="1100"/>
     <use xlink:href="#SimHei-5316" x="1200"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 502 54.42875 
L 559.6 54.42875 
Q 561.2 54.42875 561.2 52.82875 
L 561.2 30.69125 
Q 561.2 29.09125 559.6 29.09125 
L 502 29.09125 
Q 500.4 29.09125 500.4 30.69125 
L 500.4 52.82875 
Q 500.4 54.42875 502 54.42875 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_12">
     <path d="M 503.6 38.76 
L 519.6 38.76 
L 519.6 33.16 
L 503.6 33.16 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_12">
     <!-- 高焦虑组 -->
     <g transform="translate(526 38.76) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-9ad8"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
    <g id="patch_13">
     <path d="M 503.6 50.22875 
L 519.6 50.22875 
L 519.6 44.62875 
L 503.6 44.62875 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_13">
     <!-- 低焦虑组 -->
     <g transform="translate(526 50.22875) scale(0.08 -0.08)">
      <use xlink:href="#SimHei-4f4e"/>
      <use xlink:href="#SimHei-7126" x="100"/>
      <use xlink:href="#SimHei-8651" x="200"/>
      <use xlink:href="#SimHei-7ec4" x="300"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p174293f248">
   <rect x="39.29375" y="25.09125" width="525.90625" height="368.18125"/>
  </clipPath>
 </defs>
</svg>

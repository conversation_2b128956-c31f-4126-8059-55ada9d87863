#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试改进后的数据标准化模块

功能：
- 测试各种标准化方法
- 生成可视化结果
- 验证改进的有效性

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_normalization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-Normalization")

# 导入模块
from data_loader import load_hep_data
from channel_processor import process_channels
from data_normalizer import normalize_data, detect_outliers
from visualization import visualize_normalization_methods, visualize_quality_assessment

def main():
    """主函数"""
    # 设置输出目录
    output_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\normalization"
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    logger.info("加载数据...")
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)
    
    # 打印数据信息
    logger.info(f"成功加载数据，包含 {len(stages_data)} 个阶段")
    for stage in stages_data:
        logger.info(f"阶段 {stage}: EEG形状 {stages_data[stage]['eeg'].shape}, ECG形状 {stages_data[stage]['ecg'].shape}")
    
    # 处理通道
    logger.info("处理通道...")
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)
    
    # 测试1：标准Z-score标准化
    logger.info("测试1：标准Z-score标准化...")
    z_score_data = normalize_data(processed_data, method='z_score')
    
    # 测试2：分段标准化（30秒无重叠窗口）
    logger.info("测试2：分段标准化（30秒无重叠窗口）...")
    sampling_rate = 500  # Hz
    window_size = 30 * sampling_rate  # 30秒
    windowed_data = normalize_data(processed_data, method='z_score', window_size=window_size)
    
    # 测试3：异常值处理（使用自适应阈值）
    logger.info("测试3：异常值处理（使用自适应阈值）...")
    adaptive_data = normalize_data(processed_data, method='z_score', 
                                  handle_outliers_method='clip', 
                                  adaptive_threshold=True)
    
    # 测试4：组合功能（分段标准化 + 自适应异常值阈值）
    logger.info("测试4：组合功能（分段标准化 + 自适应异常值阈值）...")
    combined_data = normalize_data(processed_data, method='z_score', 
                                  handle_outliers_method='clip',
                                  window_size=window_size, 
                                  adaptive_threshold=True)
    
    # 生成可视化结果
    logger.info("生成可视化结果...")
    
    # 为每个阶段的第一个通道生成标准化方法对比图
    for stage in processed_data:
        logger.info(f"生成阶段 {stage} 的标准化方法对比图...")
        visualize_normalization_methods(processed_data, sampling_rate=sampling_rate, 
                                       output_dir=output_dir, stage=stage, 
                                       channel_idx=0, epoch_idx=0)
    
    # 为每个阶段生成质量评估报告
    for stage in combined_data:
        logger.info(f"生成阶段 {stage} 的质量评估报告...")
        visualize_quality_assessment(combined_data[stage]['quality_assessment'], 
                                    output_dir=output_dir, stage=stage)
    
    # 验证改进的有效性
    logger.info("验证改进的有效性...")
    
    # 1. 验证分段标准化对非平稳数据的处理效果
    logger.info("1. 验证分段标准化对非平稳数据的处理效果...")
    for stage in ['rest1', 'test1']:  # 选择两个阶段进行比较
        # 计算标准Z-score和分段Z-score的标准差
        z_score_std = np.std(z_score_data[stage]['eeg'])
        windowed_std = np.std(windowed_data[stage]['eeg'])
        
        logger.info(f"阶段 {stage}:")
        logger.info(f"  - 标准Z-score标准差: {z_score_std:.4f}")
        logger.info(f"  - 分段Z-score标准差: {windowed_std:.4f}")
        logger.info(f"  - 改进比例: {(z_score_std - windowed_std) / z_score_std:.2%}")
    
    # 2. 验证自适应异常值阈值的效果
    logger.info("2. 验证自适应异常值阈值的效果...")
    for stage in ['rest1', 'test1']:
        # 计算固定阈值和自适应阈值的异常值比例
        fixed_outliers, fixed_count, fixed_ratio = detect_outliers(processed_data[stage]['eeg'], threshold=3.0)
        
        # 获取自适应阈值处理后的异常值比例
        adaptive_ratio = adaptive_data[stage]['quality_assessment']['eeg_outlier_ratio']
        
        logger.info(f"阶段 {stage}:")
        logger.info(f"  - 固定阈值(3.0)异常值比例: {fixed_ratio:.4%}")
        logger.info(f"  - 自适应阈值异常值比例: {adaptive_ratio:.4%}")
        logger.info(f"  - 改进比例: {(fixed_ratio - adaptive_ratio) / fixed_ratio if fixed_ratio > 0 else 0:.2%}")
    
    # 3. 检查标准化后数据的正态性
    logger.info("3. 检查标准化后数据的正态性...")
    for stage in ['rest1', 'test1']:
        # 获取不同方法的正态性检验结果
        z_score_normality = z_score_data[stage]['quality_assessment']['eeg_normality']
        windowed_normality = windowed_data[stage]['quality_assessment']['eeg_normality']
        adaptive_normality = adaptive_data[stage]['quality_assessment']['eeg_normality']
        combined_normality = combined_data[stage]['quality_assessment']['eeg_normality']
        
        logger.info(f"阶段 {stage}:")
        logger.info(f"  - 标准Z-score正态性: {'通过' if z_score_normality else '未通过'}")
        logger.info(f"  - 分段Z-score正态性: {'通过' if windowed_normality else '未通过'}")
        logger.info(f"  - 自适应阈值正态性: {'通过' if adaptive_normality else '未通过'}")
        logger.info(f"  - 组合方法正态性: {'通过' if combined_normality else '未通过'}")
    
    # 4. 评估标准化对后续脑-心因果分析的影响
    logger.info("4. 评估标准化对后续脑-心因果分析的影响...")
    for stage in ['rest1', 'test1']:
        # 计算EEG和ECG数据的相关性
        eeg_data = processed_data[stage]['eeg'][:, 0, :]  # 使用第一个通道
        ecg_data = processed_data[stage]['ecg'][:, 0, :]  # 使用第一个通道
        
        # 原始数据相关性
        raw_corr = np.corrcoef(eeg_data.flatten(), ecg_data.flatten())[0, 1]
        
        # 标准Z-score相关性
        z_score_eeg = z_score_data[stage]['eeg'][:, 0, :]
        z_score_ecg = z_score_data[stage]['ecg'][:, 0, :]
        z_score_corr = np.corrcoef(z_score_eeg.flatten(), z_score_ecg.flatten())[0, 1]
        
        # 分段Z-score相关性
        windowed_eeg = windowed_data[stage]['eeg'][:, 0, :]
        windowed_ecg = windowed_data[stage]['ecg'][:, 0, :]
        windowed_corr = np.corrcoef(windowed_eeg.flatten(), windowed_ecg.flatten())[0, 1]
        
        # 组合方法相关性
        combined_eeg = combined_data[stage]['eeg'][:, 0, :]
        combined_ecg = combined_data[stage]['ecg'][:, 0, :]
        combined_corr = np.corrcoef(combined_eeg.flatten(), combined_ecg.flatten())[0, 1]
        
        logger.info(f"阶段 {stage}:")
        logger.info(f"  - 原始数据相关性: {raw_corr:.4f}")
        logger.info(f"  - 标准Z-score相关性: {z_score_corr:.4f}")
        logger.info(f"  - 分段Z-score相关性: {windowed_corr:.4f}")
        logger.info(f"  - 组合方法相关性: {combined_corr:.4f}")
    
    logger.info("测试完成！")
    
    # 返回生成的图像文件路径
    return [
        os.path.join(output_dir, f"normalization_results_rest1_ch0_ep0.png"),
        os.path.join(output_dir, f"quality_assessment_rest1.png")
    ]

if __name__ == "__main__":
    main()

<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1435.429688pt" height="382.501562pt" viewBox="0 0 1435.429688 382.501562" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-21T12:18:37.744399</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 382.501562 
L 1435.429688 382.501562 
L 1435.429688 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 33.229688 345.6 
L 258.229688 345.6 
L 258.229688 68.4 
L 33.229688 68.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m93f0096b0c" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #0072bd; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p103214e778)">
     <use xlink:href="#m93f0096b0c" x="113.791921" y="333" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="134.201881" y="333" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="195.627152" y="151.654206" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="202.896702" y="151.654206" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="210.746856" y="90.420561" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="219.89831" y="90.420561" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="176.85332" y="128.102804" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="207.733774" y="128.102804" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="151.593555" y="149.299065" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="140.117574" y="149.299065" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="203.717861" y="132.813084" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="170.954562" y="132.813084" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="113.53477" y="165.785047" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="120.832235" y="165.785047" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="126.500978" y="161.074766" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="121.207161" y="161.074766" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="141.39019" y="116.327103" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="141.667718" y="116.327103" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="123.344317" y="109.261682" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="152.637629" y="109.261682" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="148.079937" y="125.747664" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="149.862574" y="125.747664" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="181.283911" y="146.943925" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="173.506167" y="146.943925" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="199.170793" y="132.813084" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="217.410108" y="132.813084" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="197.326123" y="149.299065" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="205.198468" y="149.299065" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="124.993701" y="83.35514" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="128.142963" y="83.35514" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="146.086823" y="128.102804" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="136.425562" y="128.102804" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="140.47773" y="85.71028" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="145.384094" y="85.71028" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="43.45696" y="92.775701" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="62.308897" y="92.775701" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="165.744481" y="118.682243" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="248.002415" y="118.682243" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="131.521876" y="104.551402" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="149.357038" y="104.551402" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="223.791765" y="81" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="187.501502" y="81" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="210.0895" y="121.037383" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="237.699214" y="121.037383" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="166.607503" y="109.261682" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="165.311865" y="109.261682" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="198.266262" y="118.682243" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="152.476584" y="118.682243" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="198.591137" y="116.327103" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="191.508276" y="116.327103" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="173.690267" y="139.878505" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="172.984862" y="139.878505" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="184.664803" y="161.074766" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="191.019421" y="161.074766" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="188.876426" y="142.233645" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="195.782053" y="142.233645" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="124.081816" y="130.457944" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m93f0096b0c" x="139.470119" y="130.457944" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_1">
    <path d="M 43.45696 106.259884 
L 43.45696 201.71509 
L 45.523076 200.732724 
L 47.589192 199.750359 
L 49.655307 198.767993 
L 51.721423 197.708125 
L 53.787539 196.496945 
L 55.853654 195.285764 
L 57.91977 194.074583 
L 59.985886 192.863402 
L 62.052002 191.652221 
L 64.118117 190.44104 
L 66.184233 189.229859 
L 68.250349 188.018679 
L 70.316464 186.901078 
L 72.38258 185.948055 
L 74.448696 184.995032 
L 76.514811 184.042009 
L 78.580927 183.088986 
L 80.647043 182.133726 
L 82.713159 181.176354 
L 84.779274 180.218982 
L 86.84539 179.26161 
L 88.911506 178.304238 
L 90.977621 177.346866 
L 93.043737 176.389494 
L 95.109853 175.432122 
L 97.175968 174.47475 
L 99.242084 173.517379 
L 101.3082 172.527231 
L 103.374316 171.531153 
L 105.440431 170.598377 
L 107.506547 169.665601 
L 109.572663 168.732825 
L 111.638778 167.798777 
L 113.704894 166.846856 
L 115.77101 165.894935 
L 117.837126 164.943014 
L 119.903241 164.07157 
L 121.969357 163.14155 
L 124.035473 162.169521 
L 126.101588 161.199868 
L 128.167704 160.264616 
L 130.23382 159.385495 
L 132.299935 158.484563 
L 134.366051 157.560112 
L 136.432167 156.628199 
L 138.498283 155.692907 
L 140.564398 154.757157 
L 142.630514 153.757611 
L 144.69663 152.782597 
L 146.762745 151.957078 
L 148.828861 150.963353 
L 150.894977 149.97684 
L 152.961092 148.826494 
L 155.027208 148.103673 
L 157.093324 147.169768 
L 159.15944 146.359398 
L 161.225555 145.428006 
L 163.291671 144.541694 
L 165.357787 143.74284 
L 167.423902 143.108269 
L 169.490018 142.285058 
L 171.556134 141.698437 
L 173.622249 141.112784 
L 175.688365 140.497475 
L 177.754481 139.718617 
L 179.820597 139.16281 
L 181.886712 138.569595 
L 183.952828 138.244407 
L 186.018944 137.836639 
L 188.085059 137.678041 
L 190.151175 137.204183 
L 192.217291 137.083408 
L 194.283407 136.961978 
L 196.349522 136.873473 
L 198.415638 136.666435 
L 200.481754 136.549102 
L 202.547869 136.857826 
L 204.613985 136.919439 
L 206.680101 137.102293 
L 208.746216 137.054319 
L 210.812332 137.211219 
L 212.878448 137.381972 
L 214.944564 137.55317 
L 217.010679 137.75523 
L 219.076795 137.93045 
L 221.142911 138.031643 
L 223.209026 138.129502 
L 225.275142 138.178407 
L 227.341258 138.248138 
L 229.407373 138.362249 
L 231.473489 138.652793 
L 233.539605 138.974531 
L 235.605721 139.058829 
L 237.671836 139.392021 
L 239.737952 139.580328 
L 241.804068 139.878138 
L 243.870183 140.05785 
L 245.936299 140.275999 
L 248.002415 140.55625 
L 248.002415 98.435688 
L 248.002415 98.435688 
L 245.936299 99.324914 
L 243.870183 100.217131 
L 241.804068 101.209876 
L 239.737952 102.204373 
L 237.671836 103.198871 
L 235.605721 104.193368 
L 233.539605 105.187865 
L 231.473489 106.182363 
L 229.407373 107.180771 
L 227.341258 108.189463 
L 225.275142 108.887728 
L 223.209026 109.754693 
L 221.142911 110.536369 
L 219.076795 111.355484 
L 217.010679 112.189679 
L 214.944564 113.079355 
L 212.878448 114.012999 
L 210.812332 114.787141 
L 208.746216 115.676421 
L 206.680101 116.636966 
L 204.613985 117.335978 
L 202.547869 117.970403 
L 200.481754 118.573964 
L 198.415638 119.132956 
L 196.349522 120.030461 
L 194.283407 120.700953 
L 192.217291 121.258751 
L 190.151175 121.686541 
L 188.085059 121.923375 
L 186.018944 122.224424 
L 183.952828 122.30428 
L 181.886712 122.526884 
L 179.820597 122.426404 
L 177.754481 122.357658 
L 175.688365 122.310628 
L 173.622249 122.261553 
L 171.556134 122.262525 
L 169.490018 122.175596 
L 167.423902 122.076714 
L 165.357787 121.652733 
L 163.291671 121.548299 
L 161.225555 121.452927 
L 159.15944 121.206737 
L 157.093324 121.086476 
L 155.027208 120.954413 
L 152.961092 120.773125 
L 150.894977 120.723114 
L 148.828861 120.503395 
L 146.762745 120.244199 
L 144.69663 120.01576 
L 142.630514 119.66662 
L 140.564398 119.37662 
L 138.498283 119.228403 
L 136.432167 119.047532 
L 134.366051 118.855449 
L 132.299935 118.684205 
L 130.23382 118.377359 
L 128.167704 117.910976 
L 126.101588 117.660522 
L 124.035473 117.518283 
L 121.969357 117.23623 
L 119.903241 116.972827 
L 117.837126 116.652783 
L 115.77101 116.3641 
L 113.704894 116.133613 
L 111.638778 115.864143 
L 109.572663 115.57042 
L 107.506547 115.277796 
L 105.440431 115.065279 
L 103.374316 114.748379 
L 101.3082 114.39561 
L 99.242084 114.162563 
L 97.175968 113.874359 
L 95.109853 113.571033 
L 93.043737 113.288367 
L 90.977621 113.049989 
L 88.911506 112.811612 
L 86.84539 112.573234 
L 84.779274 112.334857 
L 82.713159 112.096554 
L 80.647043 111.76824 
L 78.580927 111.390337 
L 76.514811 111.01245 
L 74.448696 110.634564 
L 72.38258 110.302634 
L 70.316464 110.013866 
L 68.250349 109.70714 
L 66.184233 109.399326 
L 64.118117 109.10581 
L 62.052002 108.8127 
L 59.985886 108.51959 
L 57.91977 108.247061 
L 55.853654 107.982177 
L 53.787539 107.703723 
L 51.721423 107.414955 
L 49.655307 107.126187 
L 47.589192 106.837419 
L 45.523076 106.548651 
L 43.45696 106.259884 
z
" clip-path="url(#p103214e778)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 51.870199 345.6 
L 51.870199 68.4 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mfff21835cf" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mfff21835cf" x="51.870199" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- -1.0 -->
      <g transform="translate(42.370199 359.670312) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-2d" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 98.691994 345.6 
L 98.691994 68.4 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mfff21835cf" x="98.691994" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- -0.5 -->
      <g transform="translate(89.191994 359.670312) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 145.513789 345.6 
L 145.513789 68.4 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mfff21835cf" x="145.513789" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 0.0 -->
      <g transform="translate(137.763789 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 192.335584 345.6 
L 192.335584 68.4 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mfff21835cf" x="192.335584" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 0.5 -->
      <g transform="translate(184.585584 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 239.15738 345.6 
L 239.15738 68.4 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mfff21835cf" x="239.15738" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 1.0 -->
      <g transform="translate(231.40738 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="text_6">
     <!-- 前额叶HEP振幅 (第一阶段刺激态) -->
     <g transform="translate(70.7 373.58125) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-524d" d="M 474 3904 
Q 698 3872 819 3872 
L 902 3872 
L 2592 3968 
L 2534 4038 
Q 2298 4346 1875 4698 
Q 1811 4755 1811 4812 
Q 1811 4870 1878 4944 
Q 1946 5018 1984 5018 
Q 2022 5018 2124 4950 
Q 2227 4883 2361 4777 
Q 2496 4672 2624 4553 
Q 2752 4435 2835 4336 
Q 2918 4237 2918 4179 
Q 2918 4122 2867 4054 
Q 2816 3987 2803 3981 
L 3373 4013 
Q 3866 4480 4058 4765 
Q 4250 5050 4250 5165 
L 4250 5178 
Q 4250 5248 4288 5248 
Q 4326 5248 4409 5190 
Q 4493 5133 4566 5046 
Q 4640 4960 4640 4883 
Q 4640 4806 4377 4524 
Q 4115 4243 3891 4045 
L 5280 4122 
Q 5453 4128 5549 4163 
Q 5645 4198 5686 4198 
Q 5728 4198 5818 4147 
Q 6042 4006 6042 3885 
Q 6042 3814 5914 3802 
L 1050 3539 
Q 922 3526 832 3526 
Q 627 3526 528 3670 
Q 429 3814 429 3859 
Q 429 3904 474 3904 
z
M 5408 -70 
L 5414 -294 
Q 5414 -448 5305 -528 
Q 5197 -608 5120 -608 
Q 5043 -608 4908 -537 
Q 4774 -467 4617 -361 
Q 4461 -256 4320 -137 
Q 4179 -19 4086 73 
Q 3994 166 3994 211 
Q 3994 256 4058 256 
Q 4122 256 4397 115 
Q 4672 -26 4986 -102 
L 5005 3014 
Q 5005 3213 4922 3347 
Q 4890 3411 4890 3433 
Q 4890 3456 4934 3456 
Q 4979 3456 5155 3398 
Q 5331 3341 5376 3289 
Q 5421 3238 5421 3162 
L 5408 -70 
z
M 1011 -301 
Q 1126 621 1126 2637 
Q 1126 2886 1088 2992 
Q 1050 3098 1050 3123 
Q 1050 3168 1107 3168 
Q 1165 3168 1504 3059 
L 2867 3142 
L 2912 3142 
Q 3002 3142 3091 3088 
Q 3181 3034 3181 2931 
L 3162 2790 
L 3181 -51 
L 3194 -250 
Q 3194 -358 3101 -454 
Q 3008 -550 2931 -550 
Q 2790 -550 2364 -265 
Q 1939 19 1939 122 
Q 1939 160 1987 160 
Q 2035 160 2243 80 
Q 2451 0 2790 -90 
L 2784 954 
L 1466 890 
L 1440 -390 
Q 1440 -544 1325 -544 
Q 1210 -544 1110 -461 
Q 1011 -378 1011 -301 
z
M 4282 742 
Q 4282 576 4179 576 
Q 4173 576 4090 602 
Q 3846 678 3846 832 
Q 3846 877 3862 963 
Q 3878 1050 3878 1325 
L 3872 2522 
Q 3872 2739 3830 2819 
Q 3789 2899 3789 2931 
Q 3789 2963 3846 2963 
Q 3904 2963 4057 2905 
Q 4211 2848 4233 2793 
Q 4256 2739 4256 2675 
L 4282 742 
z
M 2778 2797 
L 1498 2714 
L 1485 2150 
L 2778 2214 
L 2778 2797 
z
M 2784 1882 
L 1478 1818 
L 1472 1235 
L 2784 1299 
L 2784 1882 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-989d" d="M 1414 2694 
Q 1453 2675 1952 2362 
Q 2176 2630 2336 2938 
L 1555 2886 
L 1414 2694 
z
M 2432 941 
L 1408 890 
L 1459 134 
L 2362 160 
L 2432 941 
z
M 3098 4294 
L 3162 4301 
Q 3258 4301 3350 4214 
Q 3443 4128 3443 4064 
Q 3443 4000 3139 3625 
Q 2835 3251 2720 3251 
Q 2682 3251 2682 3308 
Q 2682 3366 2781 3548 
Q 2880 3731 2957 3962 
L 1011 3821 
Q 858 3174 749 3072 
Q 704 3027 653 3027 
Q 602 3027 512 3097 
Q 422 3168 422 3219 
Q 422 3270 486 3411 
Q 614 3661 749 4314 
Q 774 4454 864 4454 
Q 979 4454 1036 4409 
Q 1094 4365 1094 4333 
L 1094 4282 
L 1069 4160 
L 1069 4147 
L 1760 4198 
L 1747 4762 
Q 1747 4832 1696 4905 
Q 1645 4979 1645 5008 
Q 1645 5037 1715 5037 
Q 1786 5037 1875 5030 
Q 2125 5005 2125 4838 
L 2118 4224 
L 3098 4294 
z
M 3123 4717 
Q 3258 4691 3398 4691 
L 3494 4691 
L 5344 4826 
Q 5498 4838 5562 4864 
Q 5626 4890 5683 4890 
Q 5741 4890 5817 4829 
Q 5894 4768 5945 4701 
Q 5997 4634 5997 4608 
Q 5997 4550 5882 4538 
L 4512 4435 
Q 4678 4320 4678 4230 
Q 4678 4141 4307 3667 
L 5357 3744 
L 5459 3750 
Q 5549 3750 5625 3670 
Q 5702 3590 5702 3545 
Q 5702 3501 5680 3472 
Q 5658 3443 5658 3411 
L 5606 1306 
L 5613 986 
L 5613 902 
Q 5613 768 5510 768 
Q 5427 768 5321 845 
Q 5216 922 5216 998 
Q 5242 1158 5242 1216 
L 5242 1350 
L 5274 3411 
L 3776 3315 
L 3821 1613 
L 3827 1235 
L 3840 915 
L 3840 832 
Q 3840 698 3738 698 
Q 3654 698 3548 771 
Q 3443 845 3443 928 
L 3462 1133 
L 3462 1280 
L 3418 3251 
Q 3405 3450 3369 3562 
Q 3334 3674 3334 3693 
Q 3334 3738 3404 3738 
Q 3475 3738 3776 3629 
L 3923 3642 
Q 4077 3878 4166 4080 
Q 4256 4282 4256 4339 
Q 4256 4397 4250 4416 
L 3616 4365 
Q 3488 4352 3392 4352 
Q 3296 4352 3190 4464 
Q 3085 4576 3085 4646 
Q 3085 4717 3123 4717 
z
M 5754 -461 
Q 5382 13 4755 550 
Q 4672 621 4672 669 
Q 4672 717 4749 800 
Q 4806 877 4857 877 
Q 4909 877 5203 640 
Q 5498 403 5808 92 
Q 6118 -218 6118 -307 
Q 6118 -352 6041 -451 
Q 5965 -550 5894 -550 
Q 5824 -550 5754 -461 
z
M 2842 -70 
Q 2842 -141 2714 -141 
L 1478 -166 
L 1491 -339 
L 1491 -358 
Q 1491 -448 1402 -448 
Q 1338 -448 1229 -390 
Q 1120 -333 1120 -230 
L 1120 -192 
Q 1133 -102 1133 -12 
Q 1133 77 1120 192 
L 1069 909 
Q 1056 1037 1005 1184 
Q 787 1062 585 976 
Q 384 890 336 890 
Q 288 890 288 941 
Q 288 992 403 1062 
Q 1184 1523 1747 2125 
Q 1408 2349 1210 2477 
Q 1043 2298 873 2179 
Q 704 2061 653 2061 
Q 602 2061 602 2105 
Q 602 2150 765 2323 
Q 928 2496 1120 2793 
Q 1312 3091 1421 3331 
Q 1530 3571 1530 3661 
L 1523 3725 
L 1523 3738 
Q 1523 3789 1574 3789 
Q 1600 3789 1683 3744 
Q 1920 3629 1920 3514 
Q 1920 3469 1754 3194 
L 2515 3258 
L 2560 3258 
Q 2733 3258 2790 3165 
Q 2848 3072 2848 3030 
Q 2848 2989 2806 2953 
Q 2765 2918 2643 2710 
Q 2522 2502 2240 2176 
Q 2982 1690 3078 1555 
Q 3130 1491 3130 1433 
Q 3130 1376 3050 1286 
Q 2970 1197 2915 1197 
Q 2861 1197 2582 1456 
Q 2304 1715 2010 1933 
Q 1677 1600 1133 1254 
Q 1248 1235 1389 1184 
L 2534 1248 
L 2592 1248 
Q 2720 1248 2787 1168 
Q 2854 1088 2854 1046 
Q 2854 1005 2835 976 
Q 2816 947 2810 922 
L 2707 154 
Q 2842 13 2842 -70 
z
M 2950 -602 
Q 2899 -627 2867 -627 
Q 2835 -627 2835 -553 
Q 2835 -480 2938 -416 
Q 3699 51 4006 621 
Q 4186 947 4262 1382 
Q 4339 1818 4339 2733 
Q 4339 2810 4288 2877 
Q 4237 2944 4237 2976 
Q 4237 3046 4320 3046 
Q 4403 3046 4528 3008 
Q 4653 2970 4697 2934 
Q 4742 2899 4742 2822 
Q 4723 1920 4656 1449 
Q 4589 979 4403 602 
Q 4038 -154 2950 -602 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-53f6" d="M 3053 2682 
Q 2893 2682 2768 2736 
Q 2643 2790 2598 3072 
Q 2598 3104 2617 3104 
Q 2637 3104 2704 3075 
Q 2771 3046 2918 3046 
L 2995 3046 
L 4141 3104 
L 4141 4787 
Q 4141 4966 4089 5030 
Q 4038 5094 4038 5126 
Q 4038 5178 4131 5178 
Q 4224 5178 4358 5136 
Q 4493 5094 4531 5059 
Q 4570 5024 4570 4941 
L 4570 3130 
L 5478 3174 
Q 5683 3194 5744 3216 
Q 5805 3238 5833 3238 
Q 5862 3238 5939 3181 
Q 6157 3021 6157 2912 
Q 6157 2842 6016 2829 
L 4570 2752 
L 4576 -454 
Q 4576 -621 4454 -621 
Q 4429 -621 4339 -589 
Q 4250 -557 4170 -486 
Q 4090 -416 4090 -339 
Q 4090 -262 4115 -147 
Q 4141 -32 4141 141 
L 4141 2733 
L 3174 2682 
L 3053 2682 
z
M 1165 1178 
Q 1165 1018 1024 1018 
Q 954 1018 832 1091 
Q 710 1165 710 1286 
Q 749 1555 749 1664 
L 749 1709 
L 685 3386 
Q 672 3571 621 3683 
Q 570 3795 570 3840 
Q 570 3885 656 3885 
Q 742 3885 1069 3763 
L 2189 3846 
Q 2221 3853 2240 3853 
L 2285 3853 
Q 2342 3853 2444 3801 
Q 2547 3750 2547 3635 
Q 2547 3590 2534 3555 
Q 2522 3520 2515 3494 
L 2381 1939 
Q 2541 1747 2541 1673 
Q 2541 1600 2486 1587 
Q 2432 1574 2349 1568 
L 1158 1498 
L 1165 1197 
L 1165 1178 
z
M 2093 3482 
L 1094 3411 
L 1146 1862 
L 1997 1920 
L 2093 3482 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-48" d="M 3456 115 
L 3469 685 
L 3469 2317 
L 3430 2310 
L 2918 2330 
L 1165 2227 
L 1126 2227 
L 1126 672 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 678 
L 678 2176 
Q 659 2170 634 2170 
L 627 2170 
Q 544 2170 483 2288 
Q 422 2406 422 2508 
Q 422 2611 493 2611 
L 499 2611 
L 678 2605 
L 678 3770 
L 659 4448 
Q 659 4512 780 4512 
Q 902 4512 1020 4464 
Q 1139 4416 1139 4333 
L 1126 3763 
L 1126 2605 
L 1158 2605 
L 2765 2701 
Q 2912 2714 3065 2726 
Q 3219 2739 3296 2752 
L 3315 2752 
Q 3411 2752 3469 2598 
L 3469 3770 
L 3450 4448 
Q 3450 4512 3571 4512 
Q 3693 4512 3811 4464 
Q 3930 4416 3930 4333 
L 3917 3763 
L 3917 678 
L 3936 0 
Q 3936 -64 3817 -64 
Q 3699 -64 3577 -16 
Q 3456 32 3456 115 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-45" d="M 3021 410 
Q 3264 410 3552 429 
L 3558 429 
Q 3642 429 3702 310 
Q 3763 192 3763 89 
Q 3763 -13 3693 -13 
L 3686 -13 
Q 3533 6 3130 6 
L 3027 6 
L 1248 -19 
Q 1146 -19 1072 -41 
Q 998 -64 905 -64 
Q 813 -64 723 48 
Q 634 160 634 237 
L 678 538 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4233 
Q 3565 4134 3482 4134 
L 3085 4134 
Q 2912 4134 2822 4128 
L 1114 4013 
L 1114 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1114 2202 
L 1114 378 
L 3021 410 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-50" d="M 2035 4147 
Q 1581 4147 1126 4019 
L 1126 2317 
Q 1165 2342 1210 2342 
Q 2278 2413 2748 2729 
Q 3219 3046 3219 3462 
Q 3219 4147 2035 4147 
z
M 2048 4550 
Q 3680 4550 3680 3469 
Q 3680 2938 3328 2605 
Q 2733 2048 1376 2048 
L 1280 2048 
Q 1190 2048 1126 2080 
L 1126 736 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 742 
L 678 3955 
L 672 3955 
Q 576 3955 480 4070 
Q 384 4186 384 4259 
Q 384 4333 442 4339 
Q 1504 4544 1689 4547 
Q 1875 4550 2048 4550 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-632f" d="M 326 3590 
Q 429 3565 506 3565 
L 627 3565 
Q 672 3565 723 3571 
L 1402 3616 
L 1408 4621 
Q 1408 4794 1293 4973 
Q 1274 5005 1274 5024 
Q 1274 5069 1341 5069 
Q 1408 5069 1517 5043 
Q 1805 4966 1805 4800 
L 1798 3642 
L 1958 3654 
Q 2048 3661 2137 3699 
Q 2227 3738 2259 3738 
Q 2291 3738 2374 3686 
Q 2598 3546 2598 3450 
Q 2598 3366 2438 3354 
L 1792 3302 
L 1786 2227 
Q 2042 2349 2275 2477 
Q 2509 2605 2569 2605 
Q 2630 2605 2630 2560 
Q 2630 2445 1786 1901 
L 1773 122 
L 1786 -141 
Q 1786 -301 1680 -400 
Q 1574 -499 1466 -499 
Q 1261 -499 902 -108 
Q 544 282 544 384 
Q 544 416 585 416 
Q 627 416 838 272 
Q 1050 128 1382 6 
L 1389 1658 
Q 666 1235 531 1235 
Q 493 1235 454 1254 
Q 282 1350 173 1517 
Q 160 1542 160 1555 
Q 160 1594 243 1603 
Q 326 1613 659 1734 
Q 992 1856 1395 2042 
L 1402 3270 
L 851 3226 
Q 710 3213 611 3213 
Q 512 3213 474 3245 
Q 384 3334 345 3424 
Q 307 3514 300 3523 
Q 294 3533 294 3561 
Q 294 3590 326 3590 
z
M 3174 4282 
Q 3174 3142 3123 2547 
L 5280 2656 
Q 5427 2669 5504 2697 
Q 5581 2726 5622 2726 
Q 5664 2726 5801 2627 
Q 5939 2528 5939 2438 
Q 5939 2368 5786 2355 
L 4307 2278 
Q 4557 1722 4723 1440 
Q 5203 1920 5216 2150 
Q 5229 2246 5289 2246 
Q 5350 2246 5420 2176 
Q 5491 2106 5539 2029 
Q 5587 1952 5587 1913 
Q 5587 1875 5466 1741 
Q 5216 1446 4902 1178 
Q 5446 474 6240 13 
Q 6317 -32 6317 -70 
Q 6317 -90 6259 -154 
Q 6106 -339 5990 -339 
Q 5965 -339 5926 -314 
Q 5466 26 5107 384 
Q 4410 1088 3968 2259 
L 3782 2253 
L 3763 166 
Q 4147 288 4406 406 
Q 4666 525 4730 525 
Q 4794 525 4794 486 
Q 4794 378 4285 90 
Q 3776 -198 3481 -323 
Q 3187 -448 3123 -448 
Q 3059 -448 2966 -361 
Q 2874 -275 2813 -185 
Q 2752 -96 2752 -67 
Q 2752 -38 2816 -38 
L 2906 -38 
Q 3149 -38 3373 51 
L 3392 2234 
L 3091 2214 
Q 2976 1203 2541 307 
Q 2362 -58 2211 -262 
Q 2061 -467 2006 -467 
Q 1952 -467 1952 -403 
Q 1952 -339 1997 -250 
Q 2592 1024 2714 2342 
Q 2771 2970 2771 3456 
Q 2771 3942 2768 4227 
Q 2765 4512 2717 4598 
Q 2669 4685 2669 4733 
Q 2669 4781 2771 4781 
Q 2874 4781 3194 4621 
L 5069 4742 
Q 5216 4755 5283 4780 
Q 5350 4806 5401 4806 
Q 5453 4806 5530 4749 
Q 5728 4621 5728 4518 
Q 5728 4448 5574 4435 
L 3174 4282 
z
M 3546 3616 
L 4742 3686 
Q 4890 3699 4957 3724 
Q 5024 3750 5075 3750 
Q 5126 3750 5210 3699 
Q 5402 3565 5402 3462 
Q 5402 3386 5248 3373 
L 3821 3283 
Q 3770 3277 3725 3277 
L 3642 3277 
Q 3514 3277 3491 3302 
Q 3469 3328 3385 3417 
Q 3302 3507 3302 3571 
Q 3302 3635 3334 3635 
L 3546 3616 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e45" d="M 2336 1325 
Q 2170 1325 1747 1824 
L 1728 -429 
Q 1728 -595 1619 -595 
Q 1562 -595 1440 -521 
Q 1318 -448 1318 -326 
L 1318 -294 
Q 1318 -275 1337 -156 
Q 1357 -38 1370 550 
L 1395 3322 
L 986 3290 
L 973 1414 
Q 973 1254 867 1254 
Q 762 1254 659 1350 
Q 557 1446 557 1491 
Q 557 1536 579 1638 
Q 602 1741 602 1862 
L 614 3264 
Q 614 3482 589 3558 
L 550 3661 
Q 544 3693 544 3712 
Q 544 3757 611 3757 
Q 678 3757 979 3629 
L 1402 3661 
L 1408 4640 
Q 1408 4858 1306 5005 
Q 1280 5037 1280 5065 
Q 1280 5094 1350 5094 
Q 1421 5094 1571 5056 
Q 1722 5018 1750 4957 
Q 1779 4896 1779 4826 
L 1766 3686 
L 2336 3725 
L 2400 3725 
Q 2547 3725 2618 3597 
Q 2637 3558 2637 3526 
L 2618 3398 
L 2624 1805 
L 2630 1632 
Q 2630 1472 2524 1398 
Q 2419 1325 2336 1325 
z
M 2893 4576 
L 5293 4717 
Q 5402 4723 5472 4745 
Q 5542 4768 5590 4768 
Q 5638 4768 5708 4710 
Q 5779 4653 5830 4582 
Q 5882 4512 5882 4474 
Q 5882 4397 5722 4384 
L 3117 4224 
L 3040 4224 
Q 2880 4224 2797 4285 
Q 2714 4346 2653 4445 
Q 2592 4544 2592 4569 
Q 2592 4595 2630 4595 
L 2688 4595 
L 2893 4576 
z
M 3194 3475 
Q 3174 3642 3113 3728 
Q 3053 3814 3053 3849 
Q 3053 3885 3158 3885 
Q 3264 3885 3552 3795 
L 5133 3891 
L 5190 3891 
Q 5293 3891 5366 3824 
Q 5440 3757 5440 3709 
Q 5440 3661 5427 3632 
Q 5414 3603 5402 3565 
L 5286 2938 
Q 5421 2778 5421 2723 
Q 5421 2669 5366 2656 
Q 5312 2643 5229 2637 
L 3667 2560 
L 3667 2502 
Q 3667 2355 3565 2355 
Q 3520 2355 3430 2394 
Q 3270 2458 3270 2566 
L 3270 2592 
Q 3283 2669 3283 2720 
L 3277 2810 
L 3194 3475 
z
M 4992 3552 
L 3578 3469 
L 3635 2880 
L 4915 2950 
L 4992 3552 
z
M 2234 3379 
L 1766 3347 
L 1754 1984 
Q 1958 1875 2240 1760 
L 2234 3379 
z
M 5581 2170 
L 5638 2176 
Q 5728 2176 5805 2093 
Q 5882 2010 5882 1958 
Q 5882 1907 5869 1878 
Q 5856 1850 5850 1818 
L 5670 173 
Q 5869 -32 5869 -86 
Q 5869 -141 5821 -150 
Q 5773 -160 5690 -166 
L 3347 -224 
L 3354 -371 
L 3354 -397 
Q 3354 -531 3264 -531 
Q 3130 -531 3043 -448 
Q 2957 -365 2957 -269 
L 2957 -237 
Q 2970 -96 2970 32 
L 2970 96 
L 2886 1677 
Q 2874 1875 2819 1987 
Q 2765 2099 2765 2128 
Q 2765 2157 2841 2157 
Q 2918 2157 3232 2054 
L 5581 2170 
z
M 5446 1830 
L 4506 1786 
L 4499 1165 
L 5395 1197 
L 5446 1830 
z
M 4147 1773 
L 3245 1728 
L 3277 1114 
L 4141 1152 
L 4147 1773 
z
M 5370 883 
L 4499 851 
L 4493 147 
L 5306 166 
L 5370 883 
z
M 4141 838 
L 3290 806 
L 3328 115 
L 4134 134 
L 4141 838 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-28" d="M 2016 -896 
Q 2016 -1018 1914 -1018 
Q 1862 -1018 1715 -883 
Q 1350 -544 1037 102 
Q 640 902 640 1846 
Q 640 2790 998 3644 
Q 1357 4499 1728 4902 
Q 1882 5062 1946 5062 
Q 2067 5062 2067 4954 
Q 2067 4902 2035 4870 
Q 1619 4384 1369 3478 
Q 1120 2573 1120 1865 
Q 1120 1158 1350 384 
Q 1581 -390 1965 -774 
Q 2016 -826 2016 -896 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7b2c" d="M 3904 5069 
L 3910 5146 
Q 3923 5248 3984 5248 
Q 4045 5248 4131 5190 
Q 4218 5133 4288 5062 
Q 4358 4992 4358 4947 
Q 4358 4902 4333 4870 
L 4147 4576 
L 5325 4666 
Q 5485 4685 5542 4704 
Q 5600 4723 5629 4723 
Q 5658 4723 5728 4685 
Q 5920 4570 5920 4454 
Q 5920 4384 5786 4371 
L 4550 4288 
Q 5114 3923 5114 3821 
Q 5114 3776 5046 3683 
Q 4979 3590 4918 3590 
Q 4858 3590 4755 3692 
Q 4653 3795 4397 3987 
Q 4301 4058 4301 4118 
Q 4301 4179 4397 4275 
L 3904 4243 
Q 3590 3866 3258 3616 
Q 3110 3507 3056 3507 
Q 3002 3507 3002 3546 
Q 3002 3603 3162 3779 
Q 3322 3955 3494 4218 
Q 3840 4730 3904 5069 
z
M 1773 5152 
Q 1850 5152 2000 5037 
Q 2150 4922 2150 4858 
Q 2150 4794 1907 4474 
L 2867 4538 
Q 2989 4550 3069 4576 
Q 3149 4602 3197 4602 
Q 3245 4602 3315 4550 
Q 3386 4499 3434 4435 
Q 3482 4371 3482 4339 
Q 3482 4262 3341 4250 
L 2259 4179 
Q 2342 4128 2585 3945 
Q 2829 3763 2829 3693 
Q 2829 3680 2797 3622 
Q 2714 3469 2618 3469 
Q 2579 3469 2428 3613 
Q 2278 3757 2048 3930 
Q 1978 3981 1978 4032 
Q 1978 4083 2061 4166 
L 1651 4141 
Q 1184 3558 736 3258 
Q 563 3142 515 3142 
Q 467 3142 467 3174 
Q 467 3219 557 3328 
Q 1178 3987 1651 4787 
Q 1709 4890 1709 5011 
L 1709 5062 
Q 1709 5152 1773 5152 
z
M 5280 1824 
L 5370 1830 
Q 5440 1830 5529 1763 
Q 5619 1696 5619 1632 
Q 5619 1568 5593 1542 
Q 5568 1517 5562 1485 
Q 5523 1165 5427 761 
Q 5331 358 5305 243 
Q 5280 128 5190 22 
Q 5101 -83 5021 -83 
Q 4941 -83 4720 13 
Q 4499 109 4102 358 
Q 3706 608 3706 717 
Q 3706 749 3766 749 
Q 3827 749 4166 611 
Q 4506 474 4870 384 
L 4890 384 
Q 4922 384 4928 416 
Q 5114 1037 5178 1485 
L 3398 1382 
L 3392 -525 
Q 3392 -666 3277 -666 
Q 3142 -666 3052 -586 
Q 2963 -506 2963 -454 
L 2963 -384 
Q 2963 -365 2979 -310 
Q 2995 -256 2995 -64 
L 2995 38 
L 3008 1133 
Q 2246 371 1363 -64 
Q 998 -243 755 -323 
Q 512 -403 448 -403 
Q 384 -403 384 -378 
Q 384 -314 595 -198 
Q 1843 474 2694 1344 
L 1638 1280 
Q 1555 1274 1507 1258 
Q 1459 1242 1408 1242 
Q 1357 1242 1277 1296 
Q 1197 1350 1197 1414 
Q 1197 1478 1216 1523 
L 1440 2182 
Q 1472 2266 1472 2368 
L 1453 2573 
L 1453 2611 
Q 1453 2682 1504 2682 
Q 1581 2682 1837 2515 
L 3021 2579 
L 3027 3098 
L 1830 3021 
Q 1792 3014 1754 3014 
L 1690 3014 
Q 1542 3014 1465 3091 
Q 1389 3168 1366 3248 
Q 1344 3328 1344 3350 
Q 1344 3373 1376 3373 
L 1645 3347 
L 1715 3347 
L 4794 3539 
L 4851 3539 
Q 5037 3539 5088 3462 
Q 5139 3386 5139 3354 
Q 5139 3322 5107 3283 
Q 5075 3245 5062 3206 
L 4928 2656 
Q 5082 2483 5082 2428 
Q 5082 2374 5027 2368 
Q 4973 2362 4896 2355 
L 3405 2272 
L 3405 1722 
L 5280 1824 
z
M 4672 3206 
L 3411 3123 
L 3405 2598 
L 4557 2662 
L 4672 3206 
z
M 3014 2246 
L 1805 2176 
L 1632 1619 
L 3008 1696 
L 3014 2246 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-4e00" d="M 704 2451 
L 5357 2688 
Q 5510 2701 5574 2726 
Q 5638 2752 5676 2752 
Q 5715 2752 5804 2697 
Q 5894 2643 5968 2563 
Q 6042 2483 6042 2419 
Q 6042 2317 5894 2304 
L 947 2048 
Q 896 2042 851 2042 
L 774 2042 
Q 646 2042 582 2086 
Q 474 2176 426 2304 
Q 378 2432 378 2464 
Q 378 2496 400 2496 
Q 422 2496 499 2473 
Q 576 2451 704 2451 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-9636" d="M 4090 4160 
Q 3392 3091 2726 2496 
Q 2483 2272 2348 2201 
Q 2214 2131 2185 2131 
Q 2157 2131 2157 2182 
Q 2157 2234 2266 2355 
Q 3328 3590 3789 4499 
Q 3994 4890 3994 5043 
L 3994 5082 
Q 3994 5190 4048 5190 
Q 4102 5190 4192 5139 
Q 4448 4992 4435 4845 
Q 4435 4826 4374 4675 
Q 4314 4525 4282 4486 
Q 4710 3898 5184 3427 
Q 5658 2957 6170 2522 
Q 6240 2451 6237 2412 
Q 6234 2374 6170 2317 
Q 5990 2176 5907 2176 
Q 5882 2182 5709 2323 
Q 5338 2630 4698 3373 
Q 4410 3706 4090 4160 
z
M 4582 2522 
Q 4582 2675 4515 2761 
Q 4448 2848 4448 2867 
Q 4448 2918 4547 2918 
Q 4646 2918 4777 2867 
Q 4909 2816 4944 2774 
Q 4979 2733 4979 2650 
L 4986 -474 
Q 4986 -640 4870 -640 
Q 4736 -640 4595 -474 
Q 4531 -403 4531 -358 
L 4531 -294 
Q 4531 -275 4556 -160 
Q 4582 -45 4582 122 
L 4582 2522 
z
M 3635 2234 
Q 3635 1446 3507 966 
Q 3341 262 2790 -179 
Q 2573 -358 2406 -441 
Q 2240 -525 2208 -525 
Q 2176 -525 2176 -483 
Q 2176 -442 2291 -320 
Q 2906 275 3091 928 
Q 3232 1408 3232 2182 
L 3232 2387 
Q 3232 2541 3181 2624 
Q 3130 2707 3130 2752 
Q 3130 2797 3213 2797 
Q 3469 2746 3552 2688 
Q 3635 2630 3635 2560 
L 3635 2234 
z
M 1146 4653 
L 2266 4736 
Q 2349 4736 2435 4672 
Q 2522 4608 2522 4550 
Q 2522 4493 2490 4457 
Q 2458 4422 2378 4265 
Q 2298 4109 2077 3725 
Q 1856 3341 1779 3225 
Q 1702 3110 1702 3084 
Q 1702 3059 1785 2931 
Q 1869 2803 1939 2688 
Q 2208 2157 2208 1600 
Q 2208 1293 2099 1069 
Q 2042 947 1920 947 
Q 1798 947 1664 1065 
Q 1530 1184 1398 1344 
Q 1267 1504 1184 1651 
Q 1101 1798 1101 1840 
Q 1101 1882 1155 1882 
Q 1210 1882 1325 1766 
Q 1542 1587 1651 1523 
Q 1760 1459 1773 1459 
Q 1818 1459 1818 1740 
Q 1818 2022 1731 2278 
Q 1645 2534 1533 2704 
Q 1421 2874 1414 2880 
Q 1338 2963 1338 3056 
Q 1338 3149 1411 3286 
Q 1485 3424 1693 3779 
Q 1901 4134 2003 4371 
L 1114 4314 
L 1056 -422 
Q 1056 -595 947 -595 
Q 819 -595 726 -505 
Q 634 -416 634 -371 
L 634 -307 
Q 634 -288 656 -189 
Q 678 -90 678 218 
L 730 4166 
Q 730 4467 678 4592 
Q 627 4717 627 4758 
Q 627 4800 704 4800 
Q 781 4800 1146 4653 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6bb5" d="M 4890 2714 
Q 4525 2771 4525 3136 
L 4525 3149 
L 4544 4320 
L 3680 4256 
L 3680 4128 
Q 3680 3738 3622 3446 
Q 3565 3155 3401 2908 
Q 3238 2662 3078 2537 
Q 2918 2413 2867 2413 
Q 2829 2413 2829 2461 
Q 2829 2509 2880 2579 
Q 3155 2995 3219 3353 
Q 3283 3712 3283 3865 
Q 3283 4019 3276 4240 
Q 3270 4461 3232 4553 
Q 3194 4646 3194 4688 
Q 3194 4730 3264 4730 
Q 3334 4730 3654 4595 
L 4602 4678 
Q 4634 4685 4659 4685 
Q 4685 4685 4710 4685 
Q 4838 4685 4902 4592 
Q 4966 4499 4966 4467 
L 4947 4365 
L 4902 3213 
L 4902 3194 
Q 4902 3130 4953 3107 
Q 5005 3085 5133 3085 
Q 5261 3085 5286 3091 
Q 5312 3098 5376 3107 
Q 5440 3117 5501 3139 
Q 5562 3162 5603 3162 
Q 5645 3162 5734 3104 
Q 5958 2950 5958 2842 
Q 5958 2765 5805 2752 
Q 5318 2656 4890 2714 
z
M 1056 704 
L 915 653 
Q 678 557 608 557 
Q 538 557 461 627 
Q 384 698 329 781 
Q 275 864 275 899 
Q 275 934 384 947 
Q 493 960 1056 1114 
L 1024 3898 
Q 1024 4019 969 4121 
Q 915 4224 915 4243 
Q 915 4301 1001 4301 
Q 1088 4301 1206 4256 
Q 1325 4211 1331 4205 
Q 1843 4422 2182 4691 
Q 2342 4819 2377 4908 
Q 2413 4998 2461 4998 
Q 2509 4998 2579 4931 
Q 2650 4864 2701 4777 
Q 2752 4691 2752 4633 
Q 2752 4576 2682 4525 
Q 2048 4115 1421 3910 
L 1427 3392 
L 2112 3443 
Q 2266 3456 2336 3488 
Q 2406 3520 2451 3520 
Q 2496 3520 2573 3465 
Q 2650 3411 2707 3340 
Q 2765 3270 2765 3226 
Q 2765 3142 2624 3130 
L 1427 3053 
L 1434 2483 
L 2112 2522 
Q 2246 2534 2320 2563 
Q 2394 2592 2442 2592 
Q 2490 2592 2566 2547 
Q 2765 2419 2765 2304 
Q 2765 2221 2624 2208 
L 1440 2131 
L 1446 1229 
Q 2074 1427 2771 1683 
Q 2867 1715 2940 1715 
Q 3014 1715 3014 1657 
Q 3014 1600 2880 1510 
Q 2426 1216 1446 851 
L 1459 -378 
Q 1459 -531 1338 -531 
Q 1235 -531 1123 -464 
Q 1011 -397 1011 -282 
L 1011 -250 
Q 1011 -230 1036 -96 
Q 1062 38 1062 224 
L 1056 704 
z
M 3232 2323 
L 5069 2413 
L 5139 2413 
Q 5267 2413 5340 2345 
Q 5414 2278 5414 2201 
Q 5414 2125 5139 1632 
Q 4864 1139 4422 710 
Q 5158 102 6067 -224 
Q 6195 -269 6195 -313 
Q 6195 -358 6124 -422 
Q 6054 -486 5971 -534 
Q 5888 -582 5859 -582 
Q 5830 -582 5673 -518 
Q 5517 -454 5274 -320 
Q 4666 0 4154 454 
Q 3443 -128 2701 -435 
Q 2400 -563 2301 -563 
Q 2202 -563 2202 -518 
Q 2202 -442 2387 -346 
Q 3206 96 3878 710 
Q 3462 1146 3308 1363 
Q 3155 1581 3155 1629 
Q 3155 1677 3228 1747 
Q 3302 1818 3363 1818 
Q 3424 1818 3494 1728 
Q 3776 1325 4147 960 
Q 4659 1498 4877 2054 
L 3475 1978 
L 3309 1971 
Q 3181 1971 3123 2016 
Q 2944 2182 2944 2323 
Q 2944 2355 2973 2355 
Q 3002 2355 3059 2339 
Q 3117 2323 3232 2323 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-523a" d="M 5638 -109 
L 5645 -294 
Q 5645 -474 5539 -563 
Q 5434 -653 5354 -653 
Q 5274 -653 5136 -576 
Q 4998 -499 4841 -380 
Q 4685 -262 4537 -137 
Q 4390 -13 4297 92 
Q 4205 198 4205 243 
Q 4205 288 4262 288 
Q 4320 288 4595 144 
Q 4870 0 5229 -122 
L 5242 4794 
Q 5242 4934 5146 5082 
Q 5120 5126 5120 5164 
Q 5120 5203 5200 5203 
Q 5280 5203 5433 5148 
Q 5587 5094 5619 5052 
Q 5651 5011 5651 4941 
L 5638 -109 
z
M 1869 -371 
Q 1914 -13 1914 198 
L 1920 1632 
Q 1466 902 858 435 
Q 608 243 441 156 
Q 275 70 233 70 
Q 192 70 192 124 
Q 192 179 333 294 
Q 1229 1082 1926 2285 
L 1926 2989 
L 1274 2950 
L 1306 1824 
L 1306 1805 
Q 1306 1715 1216 1715 
Q 1146 1715 1024 1776 
Q 902 1837 902 1946 
Q 902 1984 921 2067 
Q 941 2150 941 2310 
L 941 2387 
L 922 2874 
Q 902 3219 880 3283 
Q 858 3347 848 3369 
Q 838 3392 838 3411 
Q 838 3456 924 3456 
Q 1011 3456 1344 3302 
L 1933 3334 
L 1933 3878 
L 1107 3834 
Q 992 3821 915 3821 
Q 838 3821 736 3865 
Q 634 3910 563 4128 
Q 550 4166 550 4188 
Q 550 4211 585 4211 
Q 621 4211 685 4195 
Q 749 4179 870 4179 
L 947 4179 
L 1939 4237 
L 1939 4736 
Q 1939 4909 1888 4976 
Q 1837 5043 1837 5069 
Q 1837 5120 1949 5120 
Q 2061 5120 2192 5072 
Q 2323 5024 2323 4954 
L 2323 4256 
L 3072 4307 
Q 3232 4314 3299 4336 
Q 3366 4358 3395 4358 
Q 3424 4358 3507 4314 
Q 3738 4192 3738 4070 
Q 3738 3994 3622 3974 
L 2323 3898 
L 2317 3360 
L 3174 3405 
L 3226 3405 
Q 3334 3405 3420 3331 
Q 3507 3258 3507 3197 
Q 3507 3136 3491 3101 
Q 3475 3066 3469 3021 
L 3443 2323 
L 3443 2150 
Q 3443 1952 3340 1865 
Q 3238 1779 3174 1779 
Q 3053 1779 2749 2038 
Q 2445 2298 2445 2413 
Q 2445 2445 2489 2445 
Q 2534 2445 2694 2355 
Q 2854 2266 3059 2195 
L 3091 3053 
L 2317 3008 
L 2317 1837 
Q 2374 1901 2422 1901 
Q 2470 1901 2684 1737 
Q 2899 1574 3270 1203 
Q 3642 832 3642 733 
Q 3642 634 3494 525 
Q 3437 480 3398 480 
Q 3360 480 3213 666 
Q 2861 1107 2317 1581 
L 2310 -454 
Q 2310 -634 2189 -634 
Q 2086 -634 1977 -541 
Q 1869 -448 1869 -371 
z
M 4058 3680 
Q 4058 3834 4006 3917 
Q 3955 4000 3955 4038 
Q 3955 4077 4044 4077 
Q 4134 4077 4250 4026 
Q 4384 3981 4416 3939 
Q 4448 3898 4448 3821 
L 4467 966 
Q 4467 819 4346 819 
Q 4243 819 4137 896 
Q 4032 973 4032 1094 
Q 4032 1139 4048 1232 
Q 4064 1325 4064 1504 
L 4058 3680 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6fc0" d="M 2029 2560 
Q 2054 2739 2054 2778 
L 1984 3910 
Q 1971 4038 1929 4134 
Q 1888 4230 1888 4275 
Q 1888 4320 1968 4320 
Q 2048 4320 2336 4205 
L 2400 4211 
Q 2528 4474 2595 4694 
Q 2662 4915 2662 4986 
L 2637 5146 
Q 2637 5190 2694 5190 
Q 2701 5190 2790 5158 
Q 3078 5075 3078 4941 
Q 3078 4845 2950 4598 
Q 2822 4352 2746 4237 
L 3366 4282 
Q 3398 4288 3424 4288 
L 3507 4288 
Q 3565 4288 3641 4217 
Q 3718 4147 3718 4099 
Q 3718 4051 3702 4025 
Q 3686 4000 3680 3968 
L 3546 2867 
Q 3680 2707 3680 2649 
Q 3680 2592 3632 2585 
Q 3584 2579 3514 2573 
L 2400 2515 
L 2400 2438 
Q 2400 2336 2301 2336 
Q 2202 2336 2115 2409 
Q 2029 2483 2029 2560 
z
M 4941 4794 
Q 4941 4602 4550 3648 
L 5517 3712 
Q 5632 3725 5699 3750 
Q 5766 3776 5798 3776 
Q 5830 3776 5901 3738 
Q 6106 3622 6106 3514 
Q 6106 3430 5978 3418 
L 5587 3392 
Q 5414 2214 5056 1408 
Q 5517 461 6202 -211 
Q 6253 -262 6253 -297 
Q 6253 -333 6195 -384 
Q 6061 -512 5939 -512 
Q 5843 -512 5472 16 
Q 5101 544 4870 1011 
Q 4506 326 3987 -179 
Q 3776 -390 3625 -496 
Q 3475 -602 3430 -602 
Q 3386 -602 3386 -550 
Q 3386 -499 3456 -429 
Q 4192 371 4672 1434 
Q 4390 2086 4192 2880 
Q 4154 2816 4083 2688 
Q 3763 2112 3661 2112 
Q 3622 2112 3622 2176 
Q 3622 2240 3674 2355 
Q 4147 3360 4442 4480 
Q 4506 4730 4506 4816 
Q 4506 4902 4486 4944 
Q 4467 4986 4467 4998 
Q 4467 5050 4531 5050 
Q 4570 5050 4672 5018 
Q 4941 4928 4941 4794 
z
M 1443 4297 
Q 1574 4173 1660 4077 
Q 1747 3981 1747 3945 
Q 1747 3910 1667 3808 
Q 1587 3706 1520 3706 
Q 1453 3706 1357 3821 
Q 1261 3936 1053 4141 
Q 845 4346 717 4442 
Q 589 4538 589 4592 
Q 589 4646 656 4723 
Q 723 4800 771 4800 
Q 819 4800 928 4723 
Q 1037 4646 1174 4534 
Q 1312 4422 1443 4297 
z
M 3315 3987 
L 2330 3917 
L 2349 3526 
L 3277 3584 
L 3315 3987 
z
M 1126 2451 
Q 672 2842 326 3085 
Q 256 3136 256 3184 
Q 256 3232 320 3318 
Q 384 3405 435 3405 
Q 486 3405 736 3245 
Q 986 3085 1389 2758 
Q 1459 2707 1459 2659 
Q 1459 2611 1424 2550 
Q 1389 2490 1337 2438 
Q 1286 2387 1248 2387 
Q 1210 2387 1126 2451 
z
M 4410 3315 
Q 4595 2522 4851 1882 
Q 5069 2426 5178 3366 
L 4410 3315 
z
M 3251 3290 
L 2362 3238 
L 2381 2803 
L 3206 2854 
L 3251 3290 
z
M 2835 1690 
L 2701 1344 
L 3334 1389 
L 3418 1389 
Q 3571 1389 3635 1296 
Q 3699 1203 3699 1174 
Q 3699 1146 3683 1120 
Q 3667 1094 3654 1062 
Q 3501 358 3277 -166 
Q 3232 -275 3123 -339 
Q 3040 -397 2970 -397 
Q 2829 -397 2522 -90 
Q 2221 198 2221 294 
Q 2221 326 2259 326 
Q 2298 326 2467 233 
Q 2637 141 2768 83 
Q 2899 26 2915 26 
Q 2931 26 2950 64 
Q 3168 544 3264 1075 
L 2554 1030 
Q 2304 538 2029 234 
Q 1754 -70 1552 -208 
Q 1350 -346 1308 -346 
Q 1267 -346 1267 -304 
Q 1267 -262 1344 -179 
Q 2080 608 2426 1664 
L 2067 1638 
Q 2029 1632 1997 1632 
L 1901 1632 
Q 1837 1632 1760 1664 
Q 1594 1811 1594 1946 
Q 1594 1971 1619 1971 
Q 1645 1971 1689 1958 
Q 1734 1946 1862 1946 
L 1952 1946 
L 2694 1990 
L 2694 2150 
Q 2694 2291 2640 2358 
Q 2586 2426 2586 2445 
Q 2586 2490 2646 2490 
Q 2707 2490 2803 2470 
Q 3046 2419 3046 2272 
L 3040 2010 
L 3520 2042 
Q 3661 2054 3709 2073 
Q 3757 2093 3808 2093 
Q 3859 2093 3929 2045 
Q 4000 1997 4048 1936 
Q 4096 1875 4096 1843 
Q 4096 1779 3968 1760 
L 2835 1690 
z
M 602 -186 
Q 371 -154 297 -83 
Q 224 -13 224 9 
Q 224 32 282 64 
Q 422 134 934 1114 
Q 1158 1549 1363 2022 
Q 1434 2195 1504 2195 
Q 1542 2195 1542 2121 
Q 1542 2048 1411 1571 
Q 1280 1094 1030 515 
Q 781 -64 739 -125 
Q 698 -186 627 -186 
L 602 -186 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6001" d="M 3354 2125 
Q 3040 2522 2726 2771 
Q 2630 2854 2630 2908 
Q 2630 2963 2704 3030 
Q 2778 3098 2813 3098 
Q 2848 3098 3014 2982 
Q 3181 2867 3469 2592 
Q 3757 2317 3757 2246 
Q 3757 2176 3664 2083 
Q 3571 1990 3520 1990 
Q 3469 1990 3354 2125 
z
M 1075 288 
Q 973 45 893 -115 
Q 813 -275 742 -275 
Q 736 -275 666 -250 
Q 461 -173 461 -51 
Q 461 0 573 182 
Q 685 365 829 707 
Q 973 1050 1062 1389 
Q 1094 1498 1171 1498 
Q 1222 1498 1324 1466 
Q 1427 1434 1427 1357 
Q 1427 1158 1075 288 
z
M 4352 1030 
Q 4429 1030 4518 858 
Q 4698 480 5011 45 
Q 5120 -115 5120 -166 
Q 5120 -218 5075 -288 
Q 4960 -474 4422 -474 
Q 3264 -474 2573 -77 
Q 2138 173 1920 794 
Q 1830 1043 1798 1219 
Q 1766 1395 1766 1408 
Q 1766 1549 2010 1549 
Q 2131 1549 2150 1408 
Q 2221 1037 2352 745 
Q 2483 454 2780 281 
Q 3078 109 3465 22 
Q 3853 -64 4214 -64 
Q 4576 -64 4576 -25 
Q 4576 13 4435 413 
Q 4294 813 4294 921 
Q 4294 1030 4352 1030 
z
M 4800 1376 
Q 4717 1446 4717 1504 
Q 4717 1562 4787 1635 
Q 4858 1709 4906 1709 
Q 4954 1709 5082 1613 
Q 5210 1517 5379 1369 
Q 5549 1222 5705 1065 
Q 5862 909 5968 784 
Q 6074 659 6074 614 
Q 6074 570 6029 509 
Q 5984 448 5926 403 
Q 5869 358 5824 358 
Q 5779 358 5516 668 
Q 5254 979 4800 1376 
z
M 3539 704 
Q 3174 1139 2816 1440 
Q 2720 1517 2720 1574 
Q 2720 1632 2781 1702 
Q 2842 1773 2883 1773 
Q 2925 1773 3097 1657 
Q 3270 1542 3577 1241 
Q 3885 941 3885 854 
Q 3885 768 3801 694 
Q 3718 621 3667 621 
Q 3616 621 3539 704 
z
M 5152 4096 
L 5299 4141 
Q 5421 4141 5581 3962 
Q 5638 3891 5638 3834 
Q 5638 3750 5504 3738 
L 3891 3642 
Q 4352 3168 4902 2745 
Q 5453 2323 6246 1933 
Q 6355 1875 6355 1830 
Q 6355 1786 6278 1725 
Q 6202 1664 6115 1619 
Q 6029 1574 5984 1574 
Q 5939 1574 5901 1600 
Q 5062 2048 4476 2547 
Q 3891 3046 3373 3610 
L 2778 3571 
Q 2298 2816 1613 2214 
Q 1082 1741 563 1440 
Q 365 1325 320 1325 
Q 275 1325 275 1382 
Q 275 1440 403 1542 
Q 1626 2528 2266 3546 
L 1248 3482 
L 1133 3475 
Q 979 3475 921 3532 
Q 864 3590 813 3696 
Q 762 3802 762 3840 
Q 762 3878 787 3878 
Q 813 3878 867 3859 
Q 922 3840 1062 3840 
L 1114 3840 
L 2483 3923 
L 2586 4115 
Q 2797 4550 2870 4816 
Q 2944 5082 2944 5107 
L 2925 5248 
Q 2925 5325 2995 5325 
Q 3142 5325 3366 5210 
Q 3462 5158 3462 5081 
Q 3462 5005 3331 4685 
Q 3200 4365 3078 4122 
L 2989 3955 
L 5018 4077 
Q 5094 4083 5152 4096 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-29" d="M 518 -883 
Q 371 -1018 320 -1018 
Q 218 -1018 218 -896 
Q 218 -826 269 -774 
Q 653 -390 883 384 
Q 1114 1158 1114 1865 
Q 1114 2573 864 3478 
Q 614 4384 198 4870 
Q 166 4902 166 4954 
Q 166 5062 288 5062 
Q 358 5062 512 4902 
Q 877 4499 1203 3738 
Q 1594 2790 1594 1849 
Q 1594 909 1238 179 
Q 883 -550 518 -883 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="695.599884"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="730.599869"/>
      <use xlink:href="#LXGWWenKai-Regular-7b2c" x="765.599854"/>
      <use xlink:href="#LXGWWenKai-Regular-4e00" x="865.599838"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="965.599823"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1065.599808"/>
      <use xlink:href="#LXGWWenKai-Regular-523a" x="1165.599792"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1265.599777"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="1365.599762"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="1465.599747"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_11">
      <path d="M 33.229688 333 
L 258.229688 333 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <defs>
       <path id="me46b57928a" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me46b57928a" x="33.229688" y="333" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 0 -->
      <g transform="translate(20.229688 336.535156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_13">
      <path d="M 33.229688 274.121495 
L 258.229688 274.121495 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#me46b57928a" x="33.229688" y="274.121495" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 1 -->
      <g transform="translate(20.229688 277.656652) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_15">
      <path d="M 33.229688 215.242991 
L 258.229688 215.242991 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#me46b57928a" x="33.229688" y="215.242991" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 2 -->
      <g transform="translate(20.229688 218.778147) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_17">
      <path d="M 33.229688 156.364486 
L 258.229688 156.364486 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#me46b57928a" x="33.229688" y="156.364486" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 3 -->
      <g transform="translate(20.229688 159.899642) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_19">
      <path d="M 33.229688 97.485981 
L 258.229688 97.485981 
" clip-path="url(#p103214e778)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#me46b57928a" x="33.229688" y="97.485981" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 4 -->
      <g transform="translate(20.229688 101.021138) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_12">
     <!-- 心理韧性 -->
     <g transform="translate(14.509375 227) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7406" d="M 2918 2214 
L 2918 2438 
Q 2918 2490 2912 2554 
L 2790 4186 
Q 2784 4365 2726 4502 
Q 2669 4640 2669 4691 
Q 2669 4742 2752 4742 
Q 2835 4742 3136 4614 
L 5306 4762 
L 5344 4762 
Q 5466 4762 5536 4691 
Q 5651 4608 5651 4531 
Q 5651 4454 5632 4409 
Q 5613 4365 5606 4326 
L 5427 2515 
Q 5568 2349 5568 2281 
Q 5568 2214 5529 2198 
Q 5491 2182 5434 2176 
L 4333 2131 
L 4333 1331 
L 4992 1357 
Q 5082 1376 5139 1385 
Q 5197 1395 5251 1414 
Q 5306 1434 5334 1434 
Q 5363 1434 5434 1389 
Q 5626 1267 5626 1161 
Q 5626 1056 5530 1043 
L 4333 992 
L 4333 173 
L 5594 211 
Q 5683 218 5747 218 
L 5939 256 
Q 5997 256 6131 160 
Q 6246 45 6246 -48 
Q 6246 -141 6118 -141 
L 2547 -250 
Q 2362 -250 2291 -179 
Q 2157 -19 2138 134 
Q 2138 173 2163 173 
L 2189 160 
Q 2272 147 2349 128 
Q 2426 109 2509 109 
L 2534 109 
L 3968 160 
L 3968 973 
L 3270 934 
L 3117 928 
Q 3066 928 2966 969 
Q 2867 1011 2797 1248 
L 2797 1274 
Q 2797 1312 2829 1312 
L 2842 1312 
Q 2970 1274 3046 1274 
L 3149 1274 
Q 3168 1274 3194 1280 
L 3962 1312 
L 3962 2112 
L 3315 2086 
L 3328 1920 
L 3328 1907 
Q 3328 1792 3219 1792 
Q 3206 1792 3126 1824 
Q 3046 1856 2976 1917 
Q 2906 1978 2912 2064 
Q 2918 2150 2918 2214 
z
M 685 4397 
L 1926 4480 
Q 1984 4493 2045 4502 
Q 2106 4512 2157 4531 
Q 2208 4550 2249 4550 
Q 2291 4550 2368 4502 
Q 2445 4454 2502 4387 
Q 2560 4320 2560 4275 
Q 2560 4186 2406 4173 
L 1709 4115 
L 1696 2957 
L 1920 2976 
Q 2074 2989 2138 3014 
Q 2202 3040 2230 3040 
Q 2259 3040 2339 2989 
Q 2419 2938 2483 2867 
Q 2547 2797 2547 2752 
Q 2547 2662 2394 2650 
L 1696 2598 
L 1683 1299 
Q 1869 1382 2077 1484 
Q 2285 1587 2429 1657 
Q 2573 1728 2637 1728 
Q 2701 1728 2701 1677 
Q 2701 1606 2541 1491 
Q 1658 890 960 563 
Q 666 422 586 422 
Q 506 422 422 489 
Q 339 557 278 640 
Q 218 723 218 771 
Q 218 819 384 835 
Q 550 851 1312 1139 
L 1318 2573 
L 1024 2554 
Q 934 2541 873 2541 
Q 813 2541 794 2547 
Q 730 2547 656 2627 
Q 582 2707 550 2784 
Q 518 2861 518 2886 
Q 518 2931 557 2931 
Q 570 2931 637 2915 
Q 704 2899 819 2899 
L 896 2899 
L 1325 2925 
L 1331 4090 
L 928 4058 
Q 826 4045 733 4045 
Q 640 4045 608 4077 
Q 442 4192 410 4365 
Q 403 4378 403 4384 
Q 403 4422 448 4422 
L 685 4397 
z
M 5203 4416 
L 4333 4358 
L 4333 3616 
L 5152 3654 
L 5203 4416 
z
M 3955 4339 
L 3162 4288 
L 3213 3546 
L 3955 3584 
L 3955 4339 
z
M 5126 3322 
L 4333 3277 
L 4333 2464 
L 5069 2502 
L 5126 3322 
z
M 3962 3251 
L 3238 3213 
L 3296 2413 
L 3962 2445 
L 3962 3251 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-97e7" d="M 5037 -19 
Q 5075 -19 5075 26 
Q 5402 1184 5446 4045 
L 4704 3994 
Q 4666 3072 4544 2489 
Q 4422 1907 4307 1626 
Q 3872 518 3238 -51 
Q 2989 -275 2813 -364 
Q 2637 -454 2585 -454 
Q 2534 -454 2534 -403 
Q 2534 -352 2662 -243 
Q 3469 435 3949 1760 
Q 4141 2278 4237 3142 
Q 4288 3552 4301 3968 
L 3520 3923 
Q 3418 3910 3293 3910 
Q 3168 3910 3085 4026 
Q 2970 4198 2970 4249 
Q 2970 4301 3021 4301 
Q 3046 4301 3104 4285 
Q 3162 4269 3283 4269 
L 5504 4416 
L 5574 4416 
Q 5722 4416 5789 4329 
Q 5856 4243 5856 4179 
L 5830 4019 
Q 5811 2381 5645 845 
Q 5562 154 5478 -166 
Q 5440 -314 5321 -429 
Q 5203 -544 5113 -544 
Q 5024 -544 4829 -432 
Q 4634 -320 4330 -29 
Q 4026 262 4026 371 
Q 4026 416 4086 416 
Q 4147 416 4387 269 
Q 4627 122 5005 -13 
Q 5018 -19 5037 -19 
z
M 813 1638 
L 659 1632 
Q 538 1626 464 1706 
Q 390 1786 361 1885 
Q 333 1984 333 2003 
Q 333 2022 368 2022 
Q 403 2022 467 2000 
Q 531 1978 646 1984 
L 698 1984 
L 1370 2029 
L 1363 2707 
L 1094 2694 
Q 1050 2682 1005 2682 
L 934 2682 
Q 819 2682 764 2736 
Q 710 2790 662 2886 
Q 614 2982 614 3014 
Q 614 3046 649 3046 
Q 685 3046 742 3030 
Q 800 3014 922 3014 
L 966 3014 
L 1363 3046 
L 1363 3757 
L 941 3725 
Q 832 3712 745 3709 
Q 659 3706 588 3782 
Q 518 3859 486 3945 
Q 454 4032 454 4064 
Q 454 4096 489 4096 
Q 525 4096 589 4077 
Q 653 4058 755 4058 
L 1363 4102 
L 1363 4550 
Q 1363 4717 1312 4803 
Q 1261 4890 1261 4909 
Q 1261 4960 1341 4963 
Q 1421 4966 1542 4915 
Q 1664 4864 1693 4822 
Q 1722 4781 1722 4717 
L 1722 4128 
L 2266 4166 
Q 2438 4186 2476 4205 
Q 2515 4224 2560 4224 
Q 2605 4224 2678 4176 
Q 2752 4128 2803 4061 
Q 2854 3994 2854 3949 
Q 2854 3872 2714 3859 
L 1722 3782 
L 1728 3078 
L 2221 3110 
Q 2259 3117 2297 3123 
Q 2336 3130 2374 3146 
Q 2413 3162 2461 3162 
Q 2509 3162 2579 3117 
Q 2752 3008 2752 2893 
Q 2752 2822 2611 2810 
L 1728 2739 
L 1728 2048 
L 2509 2106 
Q 2528 2112 2547 2112 
L 2579 2112 
Q 2701 2112 2742 2054 
Q 2784 1997 2803 1946 
Q 2803 1907 2816 1894 
L 2816 1888 
Q 2810 1862 2800 1827 
Q 2790 1792 2784 1747 
L 2726 685 
L 2726 499 
Q 2726 403 2659 288 
Q 2592 173 2505 173 
Q 2419 173 2342 237 
Q 2131 358 1875 742 
Q 1830 819 1830 848 
Q 1830 877 1881 880 
Q 1933 883 2064 796 
Q 2195 710 2368 646 
L 2419 1741 
L 1728 1702 
L 1734 -506 
Q 1734 -646 1632 -653 
Q 1574 -653 1456 -589 
Q 1338 -525 1338 -435 
L 1338 -378 
Q 1338 -358 1354 -272 
Q 1370 -186 1370 -13 
L 1370 1677 
L 813 1638 
z
M 3814 3130 
Q 3814 2957 3638 2393 
Q 3462 1830 3414 1702 
Q 3366 1574 3340 1545 
Q 3315 1517 3283 1517 
Q 3251 1517 3181 1536 
Q 2982 1600 2982 1690 
Q 2982 1722 3065 1894 
Q 3149 2067 3270 2457 
Q 3392 2848 3427 3040 
Q 3462 3232 3481 3261 
Q 3501 3290 3552 3290 
Q 3603 3290 3708 3274 
Q 3814 3258 3814 3155 
L 3814 3130 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6027" d="M 1274 4742 
Q 1274 4902 1219 4972 
Q 1165 5043 1165 5075 
Q 1165 5126 1248 5126 
Q 1267 5126 1370 5107 
Q 1677 5037 1677 4909 
L 1658 -512 
Q 1658 -640 1568 -640 
L 1472 -614 
Q 1184 -538 1184 -365 
Q 1184 -320 1213 -185 
Q 1242 -51 1242 128 
L 1274 4742 
z
M 6240 0 
Q 6240 -83 6086 -83 
L 2630 -154 
L 2522 -154 
Q 2323 -154 2246 -61 
Q 2170 32 2141 131 
Q 2112 230 2112 252 
Q 2112 275 2134 275 
Q 2157 275 2253 243 
Q 2349 211 2502 211 
L 3962 243 
L 3981 1562 
L 3258 1530 
Q 3149 1530 3056 1581 
Q 2963 1632 2893 1850 
Q 2880 1869 2880 1894 
Q 2880 1920 2902 1920 
Q 2925 1920 2973 1901 
Q 3021 1882 3181 1882 
L 3264 1882 
L 3981 1920 
L 3994 3027 
L 3098 2976 
Q 2848 2458 2534 2086 
Q 2400 1926 2352 1926 
Q 2304 1926 2304 1980 
Q 2304 2035 2355 2138 
Q 2714 2810 2880 3482 
Q 2957 3763 2957 3904 
L 2931 4102 
Q 2931 4160 2988 4160 
Q 3046 4160 3155 4109 
Q 3450 3955 3450 3834 
Q 3450 3814 3443 3802 
L 3264 3347 
L 4000 3386 
L 4013 4608 
Q 4013 4781 3968 4864 
Q 3923 4947 3923 4969 
Q 3923 4992 3993 4992 
Q 4064 4992 4205 4941 
Q 4346 4890 4381 4848 
Q 4416 4806 4416 4717 
L 4397 3411 
L 5126 3450 
Q 5261 3456 5318 3481 
Q 5376 3507 5421 3507 
Q 5466 3507 5549 3452 
Q 5632 3398 5696 3324 
Q 5760 3251 5760 3200 
Q 5760 3130 5619 3110 
L 4397 3046 
L 4384 1939 
L 4890 1965 
Q 5107 1978 5158 2000 
Q 5210 2022 5254 2022 
Q 5299 2022 5376 1971 
Q 5581 1830 5581 1709 
Q 5581 1632 5427 1619 
L 4378 1574 
L 4358 256 
L 5542 282 
Q 5722 282 5795 304 
Q 5869 326 5904 326 
Q 5939 326 6022 272 
Q 6106 218 6173 141 
Q 6240 64 6240 0 
z
M 1811 3789 
Q 1811 3878 2029 3930 
Q 2112 3930 2237 3632 
Q 2362 3334 2438 3088 
Q 2515 2842 2515 2806 
Q 2515 2771 2435 2720 
Q 2355 2669 2256 2669 
Q 2157 2669 2125 2784 
Q 2029 3238 1837 3693 
Q 1830 3725 1820 3747 
Q 1811 3770 1811 3789 
z
M 730 3789 
L 794 3782 
Q 896 3770 928 3734 
Q 960 3699 960 3629 
L 960 3603 
Q 922 2867 730 2067 
Q 698 1952 595 1952 
Q 493 1952 406 2006 
Q 320 2061 320 2105 
Q 320 2150 384 2349 
Q 538 2854 595 3642 
Q 602 3789 730 3789 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="line2d_21">
    <path d="M 43.45696 147.129416 
L 45.523076 146.870698 
L 47.589192 146.61198 
L 49.655307 146.353261 
L 51.721423 146.094543 
L 53.787539 145.835824 
L 55.853654 145.577106 
L 57.91977 145.318387 
L 59.985886 145.059669 
L 62.052002 144.80095 
L 64.118117 144.542232 
L 66.184233 144.283513 
L 68.250349 144.024795 
L 70.316464 143.766076 
L 72.38258 143.507358 
L 74.448696 143.24864 
L 76.514811 142.989921 
L 78.580927 142.731203 
L 80.647043 142.472484 
L 82.713159 142.213766 
L 84.779274 141.955047 
L 86.84539 141.696329 
L 88.911506 141.43761 
L 90.977621 141.178892 
L 93.043737 140.920173 
L 95.109853 140.661455 
L 97.175968 140.402736 
L 99.242084 140.144018 
L 101.3082 139.8853 
L 103.374316 139.626581 
L 105.440431 139.367863 
L 107.506547 139.109144 
L 109.572663 138.850426 
L 111.638778 138.591707 
L 113.704894 138.332989 
L 115.77101 138.07427 
L 117.837126 137.815552 
L 119.903241 137.556833 
L 121.969357 137.298115 
L 124.035473 137.039396 
L 126.101588 136.780678 
L 128.167704 136.52196 
L 130.23382 136.263241 
L 132.299935 136.004523 
L 134.366051 135.745804 
L 136.432167 135.487086 
L 138.498283 135.228367 
L 140.564398 134.969649 
L 142.630514 134.71093 
L 144.69663 134.452212 
L 146.762745 134.193493 
L 148.828861 133.934775 
L 150.894977 133.676056 
L 152.961092 133.417338 
L 155.027208 133.15862 
L 157.093324 132.899901 
L 159.15944 132.641183 
L 161.225555 132.382464 
L 163.291671 132.123746 
L 165.357787 131.865027 
L 167.423902 131.606309 
L 169.490018 131.34759 
L 171.556134 131.088872 
L 173.622249 130.830153 
L 175.688365 130.571435 
L 177.754481 130.312716 
L 179.820597 130.053998 
L 181.886712 129.79528 
L 183.952828 129.536561 
L 186.018944 129.277843 
L 188.085059 129.019124 
L 190.151175 128.760406 
L 192.217291 128.501687 
L 194.283407 128.242969 
L 196.349522 127.98425 
L 198.415638 127.725532 
L 200.481754 127.466813 
L 202.547869 127.208095 
L 204.613985 126.949376 
L 206.680101 126.690658 
L 208.746216 126.43194 
L 210.812332 126.173221 
L 212.878448 125.914503 
L 214.944564 125.655784 
L 217.010679 125.397066 
L 219.076795 125.138347 
L 221.142911 124.879629 
L 223.209026 124.62091 
L 225.275142 124.362192 
L 227.341258 124.103473 
L 229.407373 123.844755 
L 231.473489 123.586036 
L 233.539605 123.327318 
L 235.605721 123.0686 
L 237.671836 122.809881 
L 239.737952 122.551163 
L 241.804068 122.292444 
L 243.870183 122.033726 
L 245.936299 121.775007 
L 248.002415 121.516289 
" clip-path="url(#p103214e778)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 33.229688 345.6 
L 33.229688 68.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 33.229687 345.6 
L 258.229688 345.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_13">
    <!-- r = 0.112, p = 0.404 -->
    <g transform="translate(86.9175 62.4) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-72" d="M 589 115 
L 602 678 
L 602 2349 
L 582 3034 
Q 582 3098 697 3098 
Q 813 3098 931 3050 
Q 1050 3002 1050 2928 
Q 1050 2854 1040 2777 
Q 1030 2701 1030 2592 
Q 1158 2803 1395 2956 
Q 1632 3110 1894 3110 
Q 2157 3110 2323 3046 
Q 2490 2982 2595 2892 
Q 2701 2803 2701 2713 
Q 2701 2624 2627 2509 
Q 2554 2394 2486 2394 
Q 2419 2394 2381 2458 
Q 2317 2579 2179 2636 
Q 2042 2694 1946 2694 
Q 1677 2694 1446 2528 
Q 1114 2285 1050 1702 
Q 1024 1472 1024 678 
L 1056 0 
Q 1056 -64 941 -64 
Q 826 -64 707 -16 
Q 589 32 589 115 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-3d" d="M 486 1197 
Q 678 1184 1011 1184 
L 2848 1184 
Q 3034 1184 3302 1203 
L 3309 1203 
Q 3379 1203 3417 1120 
Q 3456 1037 3456 918 
Q 3456 800 3360 800 
L 2842 813 
L 1005 813 
Q 634 813 518 794 
L 512 794 
Q 454 794 419 877 
Q 384 960 384 1043 
Q 384 1197 486 1197 
z
M 486 2400 
Q 678 2387 1011 2387 
L 2848 2387 
Q 3034 2387 3302 2406 
L 3309 2406 
Q 3379 2406 3417 2323 
Q 3456 2240 3456 2121 
Q 3456 2003 3360 2003 
L 2842 2016 
L 1005 2016 
Q 634 2016 518 1997 
L 512 1997 
Q 454 1997 419 2080 
Q 384 2163 384 2246 
Q 384 2400 486 2400 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2c" d="M 730 -666 
Q 646 -666 646 -576 
Q 646 -512 720 -464 
Q 794 -416 902 -285 
Q 1011 -154 1050 -51 
Q 954 -13 915 6 
Q 749 154 749 317 
Q 749 480 861 566 
Q 973 653 1117 653 
Q 1261 653 1360 531 
Q 1459 410 1478 243 
L 1478 192 
Q 1478 -90 1200 -378 
Q 922 -666 730 -666 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-70" d="M 1005 646 
Q 1030 659 1050 659 
Q 1120 659 1216 518 
Q 1389 262 1766 262 
Q 2144 262 2512 672 
Q 2880 1082 2880 1738 
Q 2880 2394 2534 2624 
Q 2387 2726 2169 2726 
Q 1952 2726 1712 2585 
Q 1472 2445 1267 2147 
Q 1062 1850 1024 1338 
Q 1005 1069 1005 646 
z
M 576 -922 
L 589 -352 
L 589 2355 
Q 589 2694 570 3021 
Q 570 3085 685 3085 
Q 800 3085 921 3037 
Q 1043 2989 1043 2906 
Q 1043 2867 1036 2764 
Q 1030 2662 1027 2550 
Q 1024 2438 1018 2362 
Q 1274 2797 1571 2953 
Q 1869 3110 2221 3110 
Q 2701 3110 3008 2771 
Q 3315 2432 3315 1837 
Q 3315 1242 3113 813 
Q 2912 384 2566 137 
Q 2221 -109 1786 -109 
Q 1562 -109 1338 0 
Q 1114 109 1011 243 
L 1011 -358 
L 1043 -1037 
Q 1043 -1101 928 -1101 
Q 813 -1101 694 -1053 
Q 576 -1005 576 -922 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="232.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="327.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="387.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="447.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="575.199829"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="670.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="765.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="860.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="920.199722"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_5">
    <path d="M 325.729688 345.6 
L 550.729687 345.6 
L 550.729687 68.4 
L 325.729688 68.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_2">
    <defs>
     <path id="mf17fdd2089" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #d95319; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p518e0415ac)">
     <use xlink:href="#mf17fdd2089" x="406.291921" y="212.04" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="426.701881" y="212.04" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="488.127152" y="292.68" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="495.396702" y="292.68" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="503.246856" y="322.92" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="512.39831" y="322.92" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="469.35332" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="500.233774" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="444.093555" y="141.48" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="432.617574" y="141.48" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="496.217861" y="181.8" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="463.454562" y="181.8" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="406.03477" y="81" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="413.332235" y="81" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="419.000978" y="111.24" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="413.707161" y="111.24" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="433.89019" y="222.12" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="434.167718" y="222.12" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="415.844317" y="312.84" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="445.137629" y="312.84" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="440.579937" y="242.28" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="442.362574" y="242.28" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="473.783911" y="111.24" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="466.006167" y="111.24" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="491.670793" y="101.16" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="509.910108" y="101.16" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="489.826123" y="141.48" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="497.698468" y="141.48" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="417.493701" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="420.642963" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="438.586823" y="222.12" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="428.925562" y="222.12" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="432.97773" y="333" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="437.884094" y="333" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="335.95696" y="272.52" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="354.808897" y="272.52" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="458.244481" y="272.52" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="540.502415" y="272.52" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="424.021876" y="282.6" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="441.857038" y="282.6" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="516.291765" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="480.001502" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="502.5895" y="282.6" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="530.199214" y="282.6" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="459.107503" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="457.811865" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="490.766262" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="444.976584" y="262.44" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="491.091137" y="171.72" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="484.008276" y="171.72" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="466.190267" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="465.484862" y="191.88" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="477.164803" y="242.28" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="483.519421" y="242.28" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="481.376426" y="121.32" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="488.282053" y="121.32" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="416.581816" y="232.2" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mf17fdd2089" x="431.970119" y="232.2" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_2">
    <path d="M 335.95696 135.724557 
L 335.95696 260.74328 
L 338.023076 260.115445 
L 340.089192 259.487609 
L 342.155307 258.859456 
L 344.221423 258.157283 
L 346.287539 257.4222 
L 348.353654 256.686518 
L 350.41977 255.950836 
L 352.485886 255.323677 
L 354.552002 254.615681 
L 356.618117 253.895399 
L 358.684233 253.173125 
L 360.750349 252.455514 
L 362.816464 251.744453 
L 364.88258 251.029206 
L 366.948696 250.305508 
L 369.014811 249.485069 
L 371.080927 248.594015 
L 373.147043 247.858333 
L 375.213159 247.213419 
L 377.279274 246.798935 
L 379.34539 246.480906 
L 381.411506 245.878728 
L 383.477621 245.168397 
L 385.543737 244.458066 
L 387.609853 244.093732 
L 389.675968 243.800672 
L 391.742084 243.491515 
L 393.8082 242.693573 
L 395.874316 242.473716 
L 397.940431 241.976102 
L 400.006547 241.349516 
L 402.072663 240.721312 
L 404.138778 240.092424 
L 406.204894 239.400424 
L 408.27101 239.087638 
L 410.337126 238.742033 
L 412.403241 238.210041 
L 414.469357 237.924481 
L 416.535473 237.259185 
L 418.601588 237.068893 
L 420.667704 236.552963 
L 422.73382 235.964651 
L 424.799935 235.485361 
L 426.866051 235.058939 
L 428.932167 234.627787 
L 430.998283 234.132495 
L 433.064398 234.007175 
L 435.130514 233.884058 
L 437.19663 233.424599 
L 439.262745 233.033716 
L 441.328861 232.994002 
L 443.394977 233.390523 
L 445.461092 233.321063 
L 447.527208 233.30781 
L 449.593324 233.020058 
L 451.65944 232.895829 
L 453.725555 232.756719 
L 455.791671 232.721571 
L 457.857787 232.815914 
L 459.923902 232.965009 
L 461.990018 233.441205 
L 464.056134 233.698789 
L 466.122249 234.067714 
L 468.188365 234.876197 
L 470.254481 234.936064 
L 472.320597 235.138992 
L 474.386712 235.883348 
L 476.452828 236.627885 
L 478.518944 237.378105 
L 480.585059 237.790592 
L 482.651175 238.883994 
L 484.717291 239.632593 
L 486.783407 240.284467 
L 488.849522 241.045403 
L 490.915638 241.753854 
L 492.981754 242.640018 
L 495.047869 243.144417 
L 497.113985 244.168971 
L 499.180101 245.215596 
L 501.246216 246.242038 
L 503.312332 247.265625 
L 505.378448 248.501175 
L 507.444564 249.770137 
L 509.510679 250.920549 
L 511.576795 251.473924 
L 513.642911 252.404291 
L 515.709026 253.311964 
L 517.775142 254.047275 
L 519.841258 254.941135 
L 521.907373 255.967956 
L 523.973489 257.135698 
L 526.039605 258.089197 
L 528.105721 259.301681 
L 530.171836 260.510277 
L 532.237952 261.662787 
L 534.304068 262.696452 
L 536.370183 263.730117 
L 538.436299 264.764602 
L 540.502415 266.054333 
L 540.502415 181.417946 
L 540.502415 181.417946 
L 538.436299 182.118419 
L 536.370183 182.818893 
L 534.304068 183.519366 
L 532.237952 184.221285 
L 530.171836 184.920313 
L 528.105721 185.620786 
L 526.039605 186.17903 
L 523.973489 186.886182 
L 521.907373 187.588692 
L 519.841258 188.292654 
L 517.775142 189.000123 
L 515.709026 189.707591 
L 513.642911 190.301154 
L 511.576795 190.88688 
L 509.510679 191.514348 
L 507.444564 191.924145 
L 505.378448 192.288656 
L 503.312332 192.666369 
L 501.246216 192.916405 
L 499.180101 193.425229 
L 497.113985 193.8131 
L 495.047869 194.458518 
L 492.981754 194.791789 
L 490.915638 195.373121 
L 488.849522 195.681055 
L 486.783407 196.217821 
L 484.717291 196.616595 
L 482.651175 196.83831 
L 480.585059 197.049675 
L 478.518944 197.37653 
L 476.452828 197.928287 
L 474.386712 197.997729 
L 472.320597 198.094676 
L 470.254481 198.501176 
L 468.188365 198.344493 
L 466.122249 198.239713 
L 464.056134 198.175462 
L 461.990018 198.20878 
L 459.923902 197.748495 
L 457.857787 197.469978 
L 455.791671 197.272454 
L 453.725555 196.948261 
L 451.65944 195.985698 
L 449.593324 195.200478 
L 447.527208 194.251192 
L 445.461092 193.446663 
L 443.394977 192.69638 
L 441.328861 191.688157 
L 439.262745 191.517813 
L 437.19663 190.497256 
L 435.130514 189.7707 
L 433.064398 188.631832 
L 430.998283 187.99715 
L 428.932167 186.968997 
L 426.866051 185.905051 
L 424.799935 185.134867 
L 422.73382 184.364684 
L 420.667704 183.5945 
L 418.601588 182.859068 
L 416.535473 182.126882 
L 414.469357 181.271882 
L 412.403241 180.499309 
L 410.337126 179.220714 
L 408.27101 177.900211 
L 406.204894 176.707088 
L 404.138778 175.882221 
L 402.072663 175.172352 
L 400.006547 173.962201 
L 397.940431 172.75205 
L 395.874316 171.663802 
L 393.8082 170.79235 
L 391.742084 169.540385 
L 389.675968 168.28842 
L 387.609853 167.036455 
L 385.543737 165.78449 
L 383.477621 164.218746 
L 381.411506 162.991368 
L 379.34539 161.776433 
L 377.279274 160.561499 
L 375.213159 159.490171 
L 373.147043 158.2727 
L 371.080927 157.020735 
L 369.014811 155.76877 
L 366.948696 154.516805 
L 364.88258 153.26484 
L 362.816464 152.024924 
L 360.750349 150.804503 
L 358.684233 149.504064 
L 356.618117 148.243698 
L 354.552002 146.983332 
L 352.485886 145.722966 
L 350.41977 144.46592 
L 348.353654 143.217154 
L 346.287539 141.968388 
L 344.221423 140.719621 
L 342.155307 139.470855 
L 340.089192 138.222089 
L 338.023076 136.973323 
L 335.95696 135.724557 
z
" clip-path="url(#p518e0415ac)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_6">
     <g id="line2d_22">
      <path d="M 344.370199 345.6 
L 344.370199 68.4 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_23">
      <g>
       <use xlink:href="#mfff21835cf" x="344.370199" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- -1.0 -->
      <g transform="translate(334.870199 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_24">
      <path d="M 391.191994 345.6 
L 391.191994 68.4 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_25">
      <g>
       <use xlink:href="#mfff21835cf" x="391.191994" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- -0.5 -->
      <g transform="translate(381.691994 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_26">
      <path d="M 438.013789 345.6 
L 438.013789 68.4 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_27">
      <g>
       <use xlink:href="#mfff21835cf" x="438.013789" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 0.0 -->
      <g transform="translate(430.263789 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_28">
      <path d="M 484.835584 345.6 
L 484.835584 68.4 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_29">
      <g>
       <use xlink:href="#mfff21835cf" x="484.835584" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 0.5 -->
      <g transform="translate(477.085584 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_30">
      <path d="M 531.65738 345.6 
L 531.65738 68.4 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_31">
      <g>
       <use xlink:href="#mfff21835cf" x="531.65738" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 1.0 -->
      <g transform="translate(523.90738 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="text_19">
     <!-- 前额叶HEP振幅 (第一阶段刺激态) -->
     <g transform="translate(363.2 373.58125) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="695.599884"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="730.599869"/>
      <use xlink:href="#LXGWWenKai-Regular-7b2c" x="765.599854"/>
      <use xlink:href="#LXGWWenKai-Regular-4e00" x="865.599838"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="965.599823"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1065.599808"/>
      <use xlink:href="#LXGWWenKai-Regular-523a" x="1165.599792"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1265.599777"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="1365.599762"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="1465.599747"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_6">
     <g id="line2d_32">
      <path d="M 325.729688 312.84 
L 550.729687 312.84 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_33">
      <g>
       <use xlink:href="#me46b57928a" x="325.729688" y="312.84" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 30 -->
      <g transform="translate(306.729688 316.375156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_34">
      <path d="M 325.729688 262.44 
L 550.729687 262.44 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_35">
      <g>
       <use xlink:href="#me46b57928a" x="325.729688" y="262.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 35 -->
      <g transform="translate(306.729688 265.975156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_36">
      <path d="M 325.729688 212.04 
L 550.729687 212.04 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_37">
      <g>
       <use xlink:href="#me46b57928a" x="325.729688" y="212.04" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 40 -->
      <g transform="translate(306.729688 215.575156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_38">
      <path d="M 325.729688 161.64 
L 550.729687 161.64 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_39">
      <g>
       <use xlink:href="#me46b57928a" x="325.729688" y="161.64" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 45 -->
      <g transform="translate(306.729688 165.175156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_40">
      <path d="M 325.729688 111.24 
L 550.729687 111.24 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_41">
      <g>
       <use xlink:href="#me46b57928a" x="325.729688" y="111.24" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 50 -->
      <g transform="translate(306.729688 114.775156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_25">
     <!-- 特质焦虑水平 -->
     <g transform="translate(301.009375 237) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-7279" d="M 2970 2682 
L 2848 2675 
Q 2669 2675 2576 2764 
Q 2483 2854 2432 3014 
Q 2426 3027 2426 3052 
Q 2426 3078 2451 3078 
Q 2477 3078 2553 3052 
Q 2630 3027 2790 3027 
L 2848 3027 
L 3981 3091 
L 3981 3814 
L 3379 3776 
Q 3334 3770 3290 3770 
L 3213 3770 
Q 3066 3770 3008 3830 
Q 2950 3891 2902 3984 
Q 2854 4077 2854 4115 
Q 2854 4154 2876 4154 
Q 2899 4154 2972 4128 
Q 3046 4102 3219 4102 
L 3270 4102 
L 3987 4147 
L 3987 4845 
Q 3987 5011 3936 5100 
Q 3885 5190 3885 5216 
Q 3885 5261 3945 5261 
Q 4006 5261 4150 5213 
Q 4294 5165 4332 5136 
Q 4371 5107 4371 5037 
L 4371 4166 
L 4979 4205 
Q 5152 4211 5229 4243 
Q 5306 4275 5344 4275 
Q 5382 4275 5462 4217 
Q 5542 4160 5606 4089 
Q 5670 4019 5670 3981 
Q 5670 3917 5510 3904 
L 4365 3834 
L 4365 3110 
L 5581 3181 
Q 5766 3194 5840 3222 
Q 5914 3251 5955 3251 
Q 5997 3251 6077 3187 
Q 6157 3123 6221 3049 
Q 6285 2976 6285 2944 
Q 6285 2880 6118 2867 
L 2970 2682 
z
M 902 4134 
L 870 4288 
Q 870 4346 928 4346 
Q 986 4346 1082 4307 
Q 1331 4205 1331 4083 
Q 1331 4051 1264 3804 
Q 1197 3558 1152 3437 
L 1606 3469 
L 1619 4678 
Q 1619 4806 1564 4896 
Q 1510 4986 1510 5011 
Q 1510 5062 1568 5062 
Q 1626 5062 1728 5043 
Q 2003 4979 2003 4832 
L 1990 3494 
L 2259 3514 
Q 2387 3526 2448 3548 
Q 2509 3571 2544 3571 
Q 2579 3571 2650 3520 
Q 2829 3398 2829 3296 
Q 2829 3219 2682 3206 
L 1984 3162 
L 1978 2118 
L 2605 2490 
Q 2714 2554 2778 2554 
Q 2842 2554 2842 2509 
Q 2842 2406 2470 2131 
Q 2291 1997 1971 1773 
L 1952 -474 
Q 1952 -621 1830 -621 
L 1754 -602 
Q 1677 -582 1600 -528 
Q 1523 -474 1523 -394 
Q 1523 -314 1545 -205 
Q 1568 -96 1568 77 
L 1581 1504 
Q 941 1082 793 1008 
Q 646 934 579 934 
Q 512 934 480 960 
Q 352 1030 288 1120 
Q 224 1210 224 1245 
Q 224 1280 358 1299 
Q 493 1318 691 1411 
Q 890 1504 1587 1894 
L 1600 3142 
L 1037 3104 
Q 832 2605 646 2342 
Q 461 2080 384 2080 
Q 346 2080 346 2134 
Q 346 2189 384 2285 
Q 736 3072 858 3827 
Q 902 4090 902 4134 
z
M 5075 -128 
L 5082 -326 
Q 5082 -480 4992 -566 
Q 4902 -653 4825 -653 
Q 4749 -653 4531 -563 
Q 4314 -474 4058 -294 
Q 3718 -83 3718 32 
Q 3718 70 3782 70 
Q 3846 70 4041 -3 
Q 4237 -77 4685 -160 
L 4672 1696 
L 3078 1613 
L 2912 1606 
Q 2816 1606 2717 1648 
Q 2618 1690 2547 1926 
Q 2541 1939 2541 1961 
Q 2541 1984 2566 1984 
Q 2592 1984 2665 1965 
Q 2739 1946 2886 1946 
L 2950 1946 
L 4672 2035 
L 4672 2195 
Q 4672 2400 4576 2541 
Q 4544 2592 4544 2627 
Q 4544 2662 4611 2662 
Q 4678 2662 4809 2611 
Q 4941 2560 4998 2515 
Q 5056 2470 5056 2400 
L 5056 2054 
L 5472 2074 
Q 5581 2080 5648 2112 
Q 5715 2144 5763 2144 
Q 5811 2144 5900 2086 
Q 5990 2029 6057 1958 
Q 6125 1888 6125 1850 
Q 6125 1773 5971 1760 
L 5062 1715 
L 5075 -128 
z
M 3187 1203 
Q 3136 1254 3136 1312 
Q 3136 1370 3209 1424 
Q 3283 1478 3328 1478 
Q 3373 1478 3501 1379 
Q 3629 1280 3773 1142 
Q 3917 1005 4019 889 
Q 4122 774 4122 723 
Q 4122 672 4029 585 
Q 3936 499 3878 499 
Q 3821 499 3696 668 
Q 3571 838 3187 1203 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8d28" d="M 3398 1734 
Q 3398 1850 3353 1907 
Q 3309 1965 3309 2003 
Q 3309 2086 3446 2086 
Q 3584 2086 3696 2035 
Q 3808 1984 3808 1869 
Q 3770 672 3149 147 
Q 2566 -333 1696 -512 
Q 1530 -544 1510 -544 
Q 1427 -544 1427 -461 
Q 1427 -378 1562 -333 
Q 2144 -141 2589 147 
Q 3034 435 3200 794 
Q 3398 1190 3398 1619 
L 3398 1734 
z
M 3642 4429 
Q 3827 4358 3827 4224 
Q 3789 3962 3744 3686 
L 5120 3776 
Q 5350 3789 5408 3811 
Q 5466 3834 5523 3834 
Q 5581 3834 5654 3773 
Q 5728 3712 5773 3645 
Q 5818 3578 5818 3546 
Q 5818 3488 5670 3475 
L 3686 3360 
Q 3578 2861 3488 2650 
L 4851 2726 
L 4902 2726 
Q 4992 2726 5091 2662 
Q 5190 2598 5190 2540 
Q 5190 2483 5174 2454 
Q 5158 2426 5152 2400 
L 5050 781 
L 5043 666 
Q 5037 518 4928 518 
Q 4864 525 4758 585 
Q 4653 646 4659 755 
Q 4659 794 4672 835 
Q 4685 877 4762 2400 
L 2438 2278 
L 2470 602 
L 2470 486 
Q 2470 339 2362 339 
Q 2298 339 2189 396 
Q 2080 454 2080 563 
L 2099 742 
Q 2099 800 2080 2240 
Q 2080 2432 2032 2521 
Q 1984 2611 1984 2646 
Q 1984 2682 2070 2682 
Q 2157 2682 2451 2586 
L 3149 2624 
Q 3232 3014 3283 3334 
L 1638 3238 
Q 1562 1402 954 397 
Q 755 64 569 -160 
Q 384 -384 323 -384 
Q 262 -384 262 -320 
Q 262 -256 346 -115 
Q 954 896 1133 2003 
Q 1254 2758 1254 3987 
Q 1254 4269 1216 4406 
Q 1178 4544 1178 4563 
L 1178 4595 
Q 1178 4666 1258 4666 
Q 1338 4666 1696 4454 
Q 3142 4627 4397 5018 
Q 4589 5069 4678 5171 
Q 4710 5203 4764 5203 
Q 4819 5203 4902 5136 
Q 4986 5069 5043 4982 
Q 5101 4896 5101 4832 
Q 5101 4768 5011 4736 
Q 4346 4570 4032 4509 
Q 3718 4448 3642 4429 
z
M 5587 -115 
Q 5734 -211 5734 -272 
Q 5734 -333 5680 -451 
Q 5626 -570 5558 -570 
Q 5491 -570 5155 -349 
Q 4819 -128 3942 262 
Q 3878 294 3843 320 
Q 3808 346 3808 416 
Q 3808 486 3865 560 
Q 3923 634 3955 634 
Q 3987 634 4198 544 
Q 4410 454 4765 300 
Q 5120 147 5587 -115 
z
M 1664 4154 
L 1658 3648 
L 1651 3558 
L 3334 3661 
Q 3405 4102 3405 4211 
Q 3405 4320 3373 4384 
Q 2720 4269 1664 4154 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7126" d="M 3610 4954 
L 3597 5030 
L 3597 5056 
Q 3597 5133 3661 5133 
Q 3725 5133 3821 5082 
Q 4070 4947 4070 4845 
Q 4070 4768 3907 4509 
Q 3744 4250 3635 4115 
L 4768 4192 
Q 4915 4205 4969 4224 
Q 5024 4243 5059 4243 
Q 5094 4243 5171 4198 
Q 5363 4070 5363 3949 
Q 5363 3885 5222 3872 
L 3654 3776 
L 3648 3238 
L 4435 3277 
Q 4582 3290 4630 3312 
Q 4678 3334 4713 3334 
Q 4749 3334 4826 3290 
Q 5024 3155 5024 3053 
Q 5024 2982 4877 2970 
L 3648 2912 
L 3648 2349 
L 4461 2387 
Q 4614 2406 4656 2425 
Q 4698 2445 4739 2445 
Q 4781 2445 4851 2390 
Q 4922 2336 4973 2265 
Q 5024 2195 5024 2157 
Q 5024 2099 4890 2080 
L 3648 2022 
L 3642 1427 
L 5120 1485 
Q 5242 1491 5296 1513 
Q 5350 1536 5404 1536 
Q 5459 1536 5529 1472 
Q 5600 1408 5645 1334 
Q 5690 1261 5690 1229 
Q 5690 1178 5555 1158 
L 1978 1030 
L 1978 832 
Q 1978 723 1882 723 
Q 1766 723 1644 812 
Q 1523 902 1523 953 
Q 1523 1005 1552 1113 
Q 1581 1222 1581 1459 
L 1600 3219 
Q 1184 2720 800 2394 
Q 627 2246 569 2246 
Q 512 2246 512 2307 
Q 512 2368 608 2483 
Q 1645 3731 2150 4813 
Q 2208 4941 2208 5062 
Q 2208 5184 2278 5184 
Q 2317 5184 2419 5133 
Q 2522 5082 2611 5005 
Q 2701 4928 2701 4883 
Q 2701 4838 2547 4579 
Q 2394 4320 2182 4026 
L 3264 4096 
Q 3482 4448 3546 4659 
Q 3610 4870 3610 4902 
L 3610 4954 
z
M 3270 3757 
L 1990 3680 
L 1984 3149 
L 3264 3213 
L 3270 3757 
z
M 3264 2893 
L 1984 2829 
L 1984 2266 
L 3258 2330 
L 3264 2893 
z
M 3258 2003 
L 1984 1946 
L 1978 1363 
L 3251 1414 
L 3258 2003 
z
M 5702 -467 
Q 5261 115 4736 582 
Q 4666 653 4666 710 
Q 4666 768 4742 832 
Q 4819 896 4873 896 
Q 4928 896 5053 790 
Q 5178 685 5370 509 
Q 5562 333 5725 166 
Q 5888 0 5990 -125 
Q 6093 -250 6093 -304 
Q 6093 -358 6006 -460 
Q 5920 -563 5849 -563 
Q 5779 -563 5702 -467 
z
M 448 -205 
Q 960 307 1158 736 
Q 1229 845 1257 845 
Q 1286 845 1350 826 
Q 1536 768 1536 678 
Q 1536 589 1341 294 
Q 1146 0 1018 -173 
Q 890 -346 787 -458 
Q 685 -570 643 -570 
Q 602 -570 496 -477 
Q 390 -384 390 -323 
Q 390 -262 448 -205 
z
M 4480 -294 
Q 4288 -550 4115 -339 
Q 3693 186 3315 506 
Q 3238 576 3238 627 
Q 3238 678 3312 752 
Q 3386 826 3440 826 
Q 3494 826 3616 733 
Q 3738 640 3888 499 
Q 4038 358 4182 208 
Q 4326 58 4422 -57 
Q 4518 -173 4518 -205 
Q 4518 -237 4480 -294 
z
M 2618 -320 
Q 2323 173 2054 454 
Q 1990 518 1990 572 
Q 1990 627 2080 698 
Q 2144 762 2195 762 
Q 2246 762 2342 672 
Q 2438 582 2553 448 
Q 2669 314 2774 173 
Q 2880 32 2947 -77 
Q 3014 -186 3014 -230 
Q 3014 -275 2918 -355 
Q 2822 -435 2755 -435 
Q 2688 -435 2618 -320 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8651" d="M 5523 4006 
L 5606 4013 
Q 5709 4013 5821 3917 
Q 5933 3821 5933 3757 
Q 5933 3693 5891 3661 
Q 5850 3629 5763 3513 
Q 5677 3398 5504 3219 
Q 5126 2816 4986 2816 
Q 4934 2816 4934 2880 
Q 4934 2944 5018 3046 
Q 5229 3302 5382 3661 
L 1690 3494 
Q 1690 2957 1661 2390 
Q 1632 1824 1510 1318 
Q 1254 333 442 -410 
Q 435 -416 425 -422 
Q 416 -429 410 -442 
Q 294 -531 236 -531 
Q 179 -531 179 -473 
Q 179 -416 269 -294 
Q 1037 685 1178 1862 
Q 1248 2477 1248 3219 
L 1248 3456 
Q 1229 3789 1200 3865 
Q 1171 3942 1171 3977 
Q 1171 4013 1241 4013 
Q 1312 4013 1709 3827 
L 3002 3885 
L 3002 4941 
Q 3002 5088 2906 5222 
Q 2886 5254 2886 5267 
Q 2886 5306 2956 5306 
Q 3027 5306 3136 5286 
Q 3418 5235 3418 5069 
L 3411 4672 
L 4685 4755 
Q 4832 4768 4886 4787 
Q 4941 4806 4976 4806 
Q 5011 4806 5082 4762 
Q 5274 4634 5274 4547 
Q 5274 4461 5114 4448 
L 3405 4339 
L 3398 3904 
L 5523 4006 
z
M 1875 2790 
L 2010 2765 
Q 2061 2758 2112 2758 
L 2157 2758 
L 2931 2829 
L 2931 3091 
Q 2931 3232 2835 3347 
Q 2816 3379 2816 3398 
Q 2816 3456 2944 3456 
Q 3072 3456 3226 3386 
Q 3322 3341 3322 3232 
L 3322 2861 
L 4250 2944 
Q 4371 2950 4438 2982 
Q 4506 3014 4538 3014 
Q 4570 3014 4640 2970 
Q 4832 2848 4832 2752 
Q 4832 2675 4704 2656 
L 3322 2534 
L 3322 2214 
Q 3322 2093 3427 2064 
Q 3533 2035 3875 2035 
Q 4218 2035 4461 2057 
Q 4704 2080 4835 2121 
Q 4966 2163 4995 2163 
Q 5024 2163 5094 2125 
Q 5280 2003 5280 1888 
Q 5280 1786 5094 1760 
Q 4307 1677 3706 1677 
L 3430 1683 
Q 2925 1696 2925 2150 
L 2925 2502 
L 2266 2445 
Q 2240 2438 2214 2438 
Q 2189 2438 2163 2438 
Q 1997 2438 1920 2560 
Q 1843 2682 1843 2736 
Q 1843 2790 1875 2790 
z
M 1658 -205 
Q 1587 -320 1507 -320 
Q 1427 -320 1337 -240 
Q 1248 -160 1248 -109 
Q 1248 -58 1299 13 
Q 1613 512 1798 1120 
Q 1837 1242 1933 1242 
Q 2003 1242 2086 1194 
Q 2170 1146 2170 1094 
Q 2170 1082 2112 890 
Q 1958 333 1658 -205 
z
M 5664 326 
Q 5357 736 4986 1069 
Q 4902 1146 4902 1194 
Q 4902 1242 4966 1315 
Q 5030 1389 5084 1389 
Q 5139 1389 5331 1235 
Q 5523 1082 5779 813 
Q 6099 499 6099 419 
Q 6099 339 6003 252 
Q 5907 166 5846 166 
Q 5786 166 5664 326 
z
M 3789 570 
Q 3520 928 3245 1165 
Q 3162 1248 3162 1296 
Q 3162 1344 3232 1411 
Q 3302 1478 3347 1478 
Q 3462 1478 3923 1018 
Q 4186 768 4186 694 
Q 4186 621 4099 531 
Q 4013 442 3949 442 
Q 3885 442 3789 570 
z
M 5261 -205 
Q 5261 -486 4384 -486 
Q 3507 -486 3053 -211 
Q 2682 32 2502 550 
Q 2426 755 2413 899 
Q 2400 1043 2368 1030 
Q 2368 1139 2534 1171 
Q 2592 1178 2605 1178 
Q 2720 1178 2752 1030 
Q 2842 640 2998 393 
Q 3155 147 3456 25 
Q 3757 -96 4208 -96 
Q 4659 -96 4717 -64 
Q 4480 589 4480 720 
Q 4480 851 4566 851 
Q 4653 851 4730 698 
Q 4877 403 5043 169 
Q 5210 -64 5235 -112 
Q 5261 -160 5261 -205 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6c34" d="M 3488 77 
L 3494 -166 
Q 3494 -346 3388 -432 
Q 3283 -518 3190 -518 
Q 3098 -518 2845 -409 
Q 2592 -301 2189 -26 
Q 1786 269 1786 365 
Q 1786 422 1850 422 
Q 1914 422 2086 339 
Q 2560 115 3053 26 
L 3053 4678 
Q 3053 4845 2938 5005 
Q 2912 5037 2912 5078 
Q 2912 5120 2985 5120 
Q 3059 5120 3181 5088 
Q 3501 5005 3501 4864 
L 3494 3482 
Q 3712 3142 3923 2848 
Q 4538 3315 4883 3731 
Q 5062 3949 5075 4067 
Q 5088 4186 5149 4186 
Q 5210 4186 5286 4109 
Q 5498 3923 5498 3811 
Q 5498 3699 5005 3257 
Q 4512 2816 4134 2547 
Q 5082 1274 6234 608 
Q 6342 557 6342 512 
Q 6342 467 6266 397 
Q 6074 230 5952 230 
Q 5914 230 5862 262 
Q 4480 1274 3494 2803 
L 3488 77 
z
M 621 3424 
Q 826 3398 922 3398 
L 1005 3398 
L 2253 3488 
L 2330 3488 
Q 2445 3488 2544 3414 
Q 2643 3341 2643 3264 
Q 2643 3187 2604 3142 
Q 2566 3098 2560 3078 
Q 2266 2170 1766 1466 
Q 1267 762 499 230 
Q 416 173 365 173 
Q 294 173 294 240 
Q 294 307 378 384 
Q 1670 1626 2093 3098 
L 1139 3034 
Q 1024 3021 950 3021 
Q 877 3021 787 3065 
Q 698 3110 589 3334 
Q 570 3373 570 3398 
Q 570 3424 621 3424 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e73" d="M 755 1613 
L 550 1606 
Q 435 1606 403 1638 
Q 218 1837 218 1965 
Q 218 2010 269 2010 
Q 294 2010 345 1990 
Q 397 1971 550 1971 
L 621 1971 
L 2938 2067 
L 2950 4416 
L 1555 4339 
Q 1504 4333 1446 4333 
L 1331 4333 
Q 1286 4333 1241 4345 
Q 1197 4358 1113 4476 
Q 1030 4595 1030 4659 
Q 1030 4723 1075 4723 
Q 1101 4723 1155 4707 
Q 1210 4691 1350 4691 
L 1427 4691 
L 4634 4877 
Q 4813 4890 4873 4912 
Q 4934 4934 4995 4934 
Q 5056 4934 5136 4880 
Q 5216 4826 5270 4755 
Q 5325 4685 5325 4640 
Q 5325 4557 5165 4544 
L 3366 4442 
L 3360 2086 
L 5446 2176 
Q 5619 2189 5683 2211 
Q 5747 2234 5817 2234 
Q 5888 2234 5968 2173 
Q 6048 2112 6096 2041 
Q 6144 1971 6144 1933 
Q 6144 1856 5984 1837 
L 3354 1722 
L 3347 -582 
Q 3347 -742 3216 -742 
Q 3085 -742 2982 -665 
Q 2880 -589 2880 -474 
Q 2880 -435 2902 -316 
Q 2925 -198 2925 13 
L 2931 1709 
L 755 1613 
z
M 4538 4090 
Q 4538 4186 4621 4186 
Q 4762 4186 4947 4019 
Q 5024 3955 5024 3907 
Q 5024 3859 4880 3606 
Q 4736 3354 4387 2909 
Q 4038 2464 3808 2349 
Q 3750 2310 3718 2310 
Q 3686 2310 3686 2348 
Q 3686 2387 3824 2585 
Q 3962 2784 4134 3085 
Q 4538 3770 4538 4032 
L 4538 4090 
z
M 2118 2515 
Q 1850 3059 1408 3603 
Q 1331 3712 1331 3760 
Q 1331 3808 1420 3865 
Q 1510 3923 1561 3923 
Q 1613 3923 1728 3801 
Q 1843 3680 1980 3497 
Q 2118 3315 2246 3129 
Q 2374 2944 2457 2806 
Q 2541 2669 2541 2627 
Q 2541 2586 2435 2496 
Q 2330 2406 2253 2406 
Q 2176 2406 2118 2515 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-7279"/>
      <use xlink:href="#LXGWWenKai-Regular-8d28" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-6c34" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5e73" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_42">
    <path d="M 335.95696 206.908122 
L 338.023076 207.066366 
L 340.089192 207.22461 
L 342.155307 207.382854 
L 344.221423 207.541098 
L 346.287539 207.699342 
L 348.353654 207.857585 
L 350.41977 208.015829 
L 352.485886 208.174073 
L 354.552002 208.332317 
L 356.618117 208.490561 
L 358.684233 208.648805 
L 360.750349 208.807048 
L 362.816464 208.965292 
L 364.88258 209.123536 
L 366.948696 209.28178 
L 369.014811 209.440024 
L 371.080927 209.598268 
L 373.147043 209.756511 
L 375.213159 209.914755 
L 377.279274 210.072999 
L 379.34539 210.231243 
L 381.411506 210.389487 
L 383.477621 210.547731 
L 385.543737 210.705974 
L 387.609853 210.864218 
L 389.675968 211.022462 
L 391.742084 211.180706 
L 393.8082 211.33895 
L 395.874316 211.497194 
L 397.940431 211.655437 
L 400.006547 211.813681 
L 402.072663 211.971925 
L 404.138778 212.130169 
L 406.204894 212.288413 
L 408.27101 212.446657 
L 410.337126 212.6049 
L 412.403241 212.763144 
L 414.469357 212.921388 
L 416.535473 213.079632 
L 418.601588 213.237876 
L 420.667704 213.39612 
L 422.73382 213.554363 
L 424.799935 213.712607 
L 426.866051 213.870851 
L 428.932167 214.029095 
L 430.998283 214.187339 
L 433.064398 214.345583 
L 435.130514 214.503826 
L 437.19663 214.66207 
L 439.262745 214.820314 
L 441.328861 214.978558 
L 443.394977 215.136802 
L 445.461092 215.295046 
L 447.527208 215.453289 
L 449.593324 215.611533 
L 451.65944 215.769777 
L 453.725555 215.928021 
L 455.791671 216.086265 
L 457.857787 216.244508 
L 459.923902 216.402752 
L 461.990018 216.560996 
L 464.056134 216.71924 
L 466.122249 216.877484 
L 468.188365 217.035728 
L 470.254481 217.193971 
L 472.320597 217.352215 
L 474.386712 217.510459 
L 476.452828 217.668703 
L 478.518944 217.826947 
L 480.585059 217.985191 
L 482.651175 218.143434 
L 484.717291 218.301678 
L 486.783407 218.459922 
L 488.849522 218.618166 
L 490.915638 218.77641 
L 492.981754 218.934654 
L 495.047869 219.092897 
L 497.113985 219.251141 
L 499.180101 219.409385 
L 501.246216 219.567629 
L 503.312332 219.725873 
L 505.378448 219.884117 
L 507.444564 220.04236 
L 509.510679 220.200604 
L 511.576795 220.358848 
L 513.642911 220.517092 
L 515.709026 220.675336 
L 517.775142 220.83358 
L 519.841258 220.991823 
L 521.907373 221.150067 
L 523.973489 221.308311 
L 526.039605 221.466555 
L 528.105721 221.624799 
L 530.171836 221.783043 
L 532.237952 221.941286 
L 534.304068 222.09953 
L 536.370183 222.257774 
L 538.436299 222.416018 
L 540.502415 222.574262 
" clip-path="url(#p518e0415ac)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 325.729688 345.6 
L 325.729688 68.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 325.729688 345.6 
L 550.729687 345.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_26">
    <!-- r = -0.044, p = 0.746 -->
    <g transform="translate(377.3175 62.4) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-37" d="M 3392 4205 
Q 3366 4115 3296 4003 
Q 3226 3891 3072 3584 
Q 1997 1402 1626 32 
Q 1606 -70 1414 -70 
Q 1222 -70 1180 0 
Q 1139 70 1139 144 
Q 1139 218 1628 1424 
Q 2118 2630 2829 4064 
L 1005 3904 
Q 960 3898 912 3888 
Q 864 3878 777 3878 
Q 691 3878 620 3964 
Q 550 4051 521 4147 
Q 493 4243 493 4256 
Q 493 4314 531 4314 
L 762 4301 
L 2816 4448 
Q 2893 4454 2960 4473 
Q 3027 4493 3085 4493 
L 3104 4493 
Q 3187 4486 3289 4390 
Q 3392 4294 3392 4205 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-36" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_8">
    <path d="M 618.229687 345.6 
L 843.229687 345.6 
L 843.229687 68.4 
L 618.229687 68.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m4a1b69ea4a" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #edb120; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p335b2adaca)">
     <use xlink:href="#m4a1b69ea4a" x="698.791921" y="210.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="719.201881" y="210.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="780.627152" y="102.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="787.896702" y="102.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="795.746856" y="333" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="804.89831" y="333" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="761.85332" y="304.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="792.733774" y="304.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="736.593555" y="160.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="725.117574" y="160.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="788.717861" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="755.954562" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="698.53477" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="705.832235" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="711.500978" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="706.207161" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="726.39019" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="726.667718" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="708.344317" y="253.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="737.637629" y="253.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="733.079937" y="196.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="734.862574" y="196.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="766.283911" y="109.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="758.506167" y="109.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="784.170793" y="203.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="802.410108" y="203.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="782.326123" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="790.198468" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="709.993701" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="713.142963" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="731.086823" y="246.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="721.425562" y="246.6" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="725.47773" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="730.384094" y="181.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="628.45696" y="311.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="647.308897" y="311.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="750.744481" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="833.002415" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="716.521876" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="734.357038" y="232.2" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="808.791765" y="289.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="772.501502" y="289.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="795.0895" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="822.699214" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="751.607503" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="750.311865" y="225" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="783.266262" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="737.476584" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="783.591137" y="275.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="776.508276" y="275.4" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="758.690267" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="757.984862" y="261" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="769.664803" y="325.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="776.019421" y="325.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="773.876426" y="217.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="780.782053" y="217.8" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="709.081816" y="81" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m4a1b69ea4a" x="724.470119" y="81" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_3">
    <path d="M 628.45696 125.765009 
L 628.45696 252.062879 
L 630.523076 251.583949 
L 632.589192 251.105019 
L 634.655307 250.626088 
L 636.721423 250.178451 
L 638.787539 249.813747 
L 640.853654 249.479649 
L 642.91977 249.134231 
L 644.985886 248.788814 
L 647.052002 248.439994 
L 649.118117 248.027555 
L 651.184233 247.566999 
L 653.250349 247.10487 
L 655.316464 246.643611 
L 657.38258 246.270179 
L 659.448696 245.896746 
L 661.514811 245.64534 
L 663.580927 245.149462 
L 665.647043 244.775504 
L 667.713159 244.332357 
L 669.779274 244.019074 
L 671.84539 243.605499 
L 673.911506 243.068601 
L 675.977621 242.670831 
L 678.043737 241.962465 
L 680.109853 241.467339 
L 682.175968 241.317305 
L 684.242084 241.119319 
L 686.3082 240.715154 
L 688.374316 240.538856 
L 690.440431 240.307516 
L 692.506547 240.190966 
L 694.572663 239.941124 
L 696.638778 239.681607 
L 698.704894 239.675044 
L 700.77101 239.427456 
L 702.837126 239.330229 
L 704.903241 238.945406 
L 706.969357 238.386163 
L 709.035473 238.254306 
L 711.101588 237.850034 
L 713.167704 237.9166 
L 715.23382 238.074935 
L 717.299935 238.051081 
L 719.366051 237.947229 
L 721.432167 237.777493 
L 723.498283 237.638724 
L 725.564398 237.675921 
L 727.630514 237.714298 
L 729.69663 237.716146 
L 731.762745 237.803536 
L 733.828861 237.904392 
L 735.894977 238.26864 
L 737.961092 238.649163 
L 740.027208 239.085411 
L 742.093324 239.145842 
L 744.15944 239.570251 
L 746.225555 239.786463 
L 748.291671 240.32977 
L 750.357787 240.829565 
L 752.423902 241.477243 
L 754.490018 241.912716 
L 756.556134 242.418597 
L 758.622249 243.041844 
L 760.688365 243.729517 
L 762.754481 244.500361 
L 764.820597 245.118167 
L 766.886712 245.537919 
L 768.952828 246.177702 
L 771.018944 247.397409 
L 773.085059 247.896021 
L 775.151175 248.589929 
L 777.217291 249.634453 
L 779.283407 250.620968 
L 781.349522 251.79852 
L 783.415638 252.870983 
L 785.481754 254.081426 
L 787.547869 255.647553 
L 789.613985 256.823005 
L 791.680101 258.543948 
L 793.746216 259.859002 
L 795.812332 260.981205 
L 797.878448 262.408865 
L 799.944564 263.83138 
L 802.010679 265.119075 
L 804.076795 266.435704 
L 806.142911 267.830397 
L 808.209026 269.587792 
L 810.275142 271.02253 
L 812.341258 272.445492 
L 814.407373 273.867866 
L 816.473489 275.29024 
L 818.539605 276.732787 
L 820.605721 278.30923 
L 822.671836 279.885673 
L 824.737952 281.462115 
L 826.804068 283.038558 
L 828.870183 284.641921 
L 830.936299 286.265734 
L 833.002415 287.983722 
L 833.002415 201.064005 
L 833.002415 201.064005 
L 830.936299 201.290078 
L 828.870183 201.751184 
L 826.804068 202.091194 
L 824.737952 202.555191 
L 822.671836 203.022794 
L 820.605721 203.490397 
L 818.539605 203.868816 
L 816.473489 204.222512 
L 814.407373 204.576207 
L 812.341258 204.531255 
L 810.275142 204.587548 
L 808.209026 204.929555 
L 806.142911 205.286199 
L 804.076795 205.934387 
L 802.010679 206.611655 
L 799.944564 207.058626 
L 797.878448 207.419559 
L 795.812332 207.772411 
L 793.746216 208.069093 
L 791.680101 208.193785 
L 789.613985 208.318476 
L 787.547869 208.443167 
L 785.481754 208.765678 
L 783.415638 209.106344 
L 781.349522 209.447575 
L 779.283407 209.597796 
L 777.217291 209.719816 
L 775.151175 209.857998 
L 773.085059 210.112283 
L 771.018944 210.590981 
L 768.952828 210.884488 
L 766.886712 211.166765 
L 764.820597 211.772796 
L 762.754481 211.486753 
L 760.688365 211.977047 
L 758.622249 211.89323 
L 756.556134 211.515479 
L 754.490018 211.142692 
L 752.423902 210.881436 
L 750.357787 210.584271 
L 748.291671 210.099152 
L 746.225555 209.294042 
L 744.15944 208.545137 
L 742.093324 207.527545 
L 740.027208 206.483948 
L 737.961092 205.545077 
L 735.894977 204.60332 
L 733.828861 203.339055 
L 731.762745 201.620526 
L 729.69663 199.762417 
L 727.630514 198.827817 
L 725.564398 197.850076 
L 723.498283 196.86896 
L 721.432167 195.380134 
L 719.366051 193.637005 
L 717.299935 192.354048 
L 715.23382 190.924958 
L 713.167704 189.236814 
L 711.101588 188.157047 
L 709.035473 187.147123 
L 706.969357 185.670248 
L 704.903241 184.018664 
L 702.837126 182.279504 
L 700.77101 180.779908 
L 698.704894 179.166057 
L 696.638778 177.728805 
L 694.572663 176.139556 
L 692.506547 174.557212 
L 690.440431 173.038015 
L 688.374316 171.660334 
L 686.3082 170.282653 
L 684.242084 168.904972 
L 682.175968 167.067622 
L 680.109853 165.193407 
L 678.043737 163.443555 
L 675.977621 161.84848 
L 673.911506 160.253404 
L 671.84539 158.658329 
L 669.779274 157.071167 
L 667.713159 155.486249 
L 665.647043 153.901332 
L 663.580927 152.317878 
L 661.514811 150.737149 
L 659.448696 149.156419 
L 657.38258 147.810798 
L 655.316464 146.503093 
L 653.250349 145.183215 
L 651.184233 143.682304 
L 649.118117 142.361349 
L 647.052002 140.85218 
L 644.985886 139.178454 
L 642.91977 137.504728 
L 640.853654 135.815872 
L 638.787539 134.1265 
L 636.721423 132.481875 
L 634.655307 130.802659 
L 632.589192 129.123442 
L 630.523076 127.444225 
L 628.45696 125.765009 
z
" clip-path="url(#p335b2adaca)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_11">
     <g id="line2d_43">
      <path d="M 636.870199 345.6 
L 636.870199 68.4 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mfff21835cf" x="636.870199" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- -1.0 -->
      <g transform="translate(627.370199 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_45">
      <path d="M 683.691994 345.6 
L 683.691994 68.4 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mfff21835cf" x="683.691994" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- -0.5 -->
      <g transform="translate(674.191994 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_47">
      <path d="M 730.513789 345.6 
L 730.513789 68.4 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mfff21835cf" x="730.513789" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 0.0 -->
      <g transform="translate(722.763789 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_49">
      <path d="M 777.335584 345.6 
L 777.335584 68.4 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mfff21835cf" x="777.335584" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 0.5 -->
      <g transform="translate(769.585584 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_51">
      <path d="M 824.15738 345.6 
L 824.15738 68.4 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mfff21835cf" x="824.15738" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 1.0 -->
      <g transform="translate(816.40738 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="text_32">
     <!-- 前额叶HEP振幅 (第一阶段刺激态) -->
     <g transform="translate(655.7 373.58125) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="695.599884"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="730.599869"/>
      <use xlink:href="#LXGWWenKai-Regular-7b2c" x="765.599854"/>
      <use xlink:href="#LXGWWenKai-Regular-4e00" x="865.599838"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="965.599823"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1065.599808"/>
      <use xlink:href="#LXGWWenKai-Regular-523a" x="1165.599792"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1265.599777"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="1365.599762"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="1465.599747"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_11">
     <g id="line2d_53">
      <path d="M 618.229687 340.2 
L 843.229687 340.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="340.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 20 -->
      <g transform="translate(599.229687 343.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_55">
      <path d="M 618.229687 304.2 
L 843.229687 304.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="304.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 25 -->
      <g transform="translate(599.229687 307.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_57">
      <path d="M 618.229687 268.2 
L 843.229687 268.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="268.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 30 -->
      <g transform="translate(599.229687 271.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_59">
      <path d="M 618.229687 232.2 
L 843.229687 232.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="232.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 35 -->
      <g transform="translate(599.229687 235.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_61">
      <path d="M 618.229687 196.2 
L 843.229687 196.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="196.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 40 -->
      <g transform="translate(599.229687 199.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_63">
      <path d="M 618.229687 160.2 
L 843.229687 160.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="160.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 45 -->
      <g transform="translate(599.229687 163.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_65">
      <path d="M 618.229687 124.2 
L 843.229687 124.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="124.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 50 -->
      <g transform="translate(599.229687 127.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_67">
      <path d="M 618.229687 88.2 
L 843.229687 88.2 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#me46b57928a" x="618.229687" y="88.2" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 55 -->
      <g transform="translate(599.229687 91.735156) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_41">
     <!-- 前测状态焦虑 -->
     <g transform="translate(593.509375 237) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-6d4b" d="M 1459 3795 
Q 1190 4122 723 4506 
Q 640 4570 640 4611 
Q 640 4653 691 4742 
Q 742 4832 803 4832 
Q 864 4832 976 4755 
Q 1088 4678 1235 4556 
Q 1382 4435 1520 4310 
Q 1658 4186 1747 4083 
Q 1837 3981 1837 3949 
Q 1837 3917 1798 3859 
Q 1760 3802 1705 3754 
Q 1651 3706 1593 3706 
Q 1536 3706 1459 3795 
z
M 1562 2643 
Q 1562 2547 1482 2457 
Q 1402 2368 1347 2368 
Q 1293 2368 1021 2614 
Q 749 2861 397 3085 
Q 307 3142 307 3184 
Q 307 3226 358 3318 
Q 410 3411 464 3411 
Q 518 3411 796 3241 
Q 1075 3072 1466 2758 
Q 1562 2682 1562 2643 
z
M 672 -262 
L 570 -243 
Q 262 -186 262 -58 
Q 262 -26 314 6 
Q 461 90 589 294 
Q 1062 1082 1491 1952 
Q 1587 2150 1664 2150 
Q 1715 2150 1715 2054 
Q 1715 1958 1520 1417 
Q 1325 877 1062 307 
Q 800 -262 672 -262 
z
M 1600 -525 
Q 1542 -550 1513 -550 
Q 1485 -550 1485 -489 
Q 1485 -429 1568 -365 
Q 2278 179 2586 890 
Q 2746 1280 2826 1805 
Q 2906 2330 2906 3366 
Q 2906 3469 2858 3549 
Q 2810 3629 2810 3654 
Q 2810 3712 2874 3712 
Q 2938 3712 3069 3667 
Q 3200 3622 3238 3587 
Q 3277 3552 3277 3469 
Q 3258 2464 3194 1907 
Q 3130 1350 2960 905 
Q 2790 461 2460 112 
Q 2131 -237 1600 -525 
z
M 2118 1280 
Q 2144 1523 2144 1626 
L 2144 1779 
L 2099 4134 
Q 2099 4346 2051 4442 
Q 2003 4538 2003 4586 
Q 2003 4634 2064 4634 
Q 2125 4634 2464 4518 
L 3712 4582 
L 3782 4589 
Q 3846 4589 3932 4534 
Q 4019 4480 4019 4365 
L 4006 4250 
L 3936 1280 
L 3936 1242 
Q 3930 1120 3853 1120 
Q 3725 1146 3641 1210 
Q 3558 1274 3565 1350 
L 3603 1549 
L 3642 4243 
L 2432 4186 
L 2464 2605 
L 2464 2278 
L 2470 1779 
L 2477 1574 
L 2483 1235 
L 2483 1197 
Q 2483 1069 2406 1069 
Q 2285 1069 2201 1136 
Q 2118 1203 2118 1280 
z
M 3981 435 
Q 4090 282 4160 160 
Q 4230 38 4230 -35 
Q 4230 -109 4124 -166 
Q 4019 -224 3984 -224 
Q 3949 -224 3926 -198 
Q 3904 -173 3737 121 
Q 3571 416 3414 630 
Q 3258 845 3258 909 
Q 3258 973 3338 1021 
Q 3418 1069 3466 1069 
Q 3514 1069 3581 979 
Q 3648 890 3760 739 
Q 3872 589 3981 435 
z
M 5709 -58 
L 5722 -282 
Q 5722 -448 5626 -537 
Q 5530 -627 5453 -627 
Q 5376 -627 5251 -550 
Q 5126 -474 4976 -365 
Q 4826 -256 4694 -134 
Q 4563 -13 4476 83 
Q 4390 179 4390 214 
Q 4390 250 4454 250 
Q 4518 250 4774 112 
Q 5030 -26 5331 -128 
L 5344 4749 
Q 5344 4902 5242 5050 
Q 5216 5088 5216 5120 
Q 5216 5152 5293 5152 
Q 5370 5152 5523 5091 
Q 5677 5030 5699 4979 
Q 5722 4928 5722 4838 
L 5709 -58 
z
M 4448 3674 
Q 4448 3808 4413 3894 
Q 4378 3981 4368 3993 
Q 4358 4006 4358 4028 
Q 4358 4051 4422 4051 
Q 4486 4051 4617 4000 
Q 4749 3949 4777 3904 
Q 4806 3859 4806 3776 
L 4819 1011 
Q 4819 838 4710 838 
Q 4602 838 4509 915 
Q 4416 992 4416 1049 
Q 4416 1107 4432 1222 
Q 4448 1338 4448 1581 
L 4448 3674 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-72b6" d="M 4294 4653 
Q 4294 3642 4205 2886 
L 5293 2938 
Q 5427 2950 5507 2988 
Q 5587 3027 5635 3027 
Q 5683 3027 5763 2963 
Q 5843 2899 5900 2819 
Q 5958 2739 5958 2701 
Q 5958 2624 5805 2611 
L 4403 2534 
Q 5056 762 6266 -122 
Q 6330 -166 6330 -198 
Q 6330 -230 6272 -294 
Q 6125 -461 6010 -461 
Q 5965 -461 5926 -422 
Q 4678 621 4122 2291 
L 4045 1920 
Q 3859 1107 3366 448 
Q 2970 -90 2586 -346 
Q 2432 -448 2381 -448 
Q 2330 -448 2330 -397 
Q 2330 -346 2445 -224 
Q 3507 858 3770 2502 
L 3053 2464 
L 2906 2458 
Q 2739 2458 2675 2509 
Q 2490 2688 2490 2829 
Q 2490 2861 2509 2861 
Q 2547 2861 2630 2838 
Q 2714 2816 2816 2816 
L 3814 2867 
Q 3878 3443 3878 4403 
L 3878 4710 
Q 3878 4870 3820 4963 
Q 3763 5056 3763 5088 
Q 3763 5120 3840 5120 
Q 3917 5120 4067 5059 
Q 4218 4998 4256 4960 
Q 4294 4922 4294 4851 
L 4294 4653 
z
M 1837 1760 
Q 1402 1267 954 883 
Q 813 755 745 755 
Q 678 755 585 796 
Q 493 838 384 940 
Q 275 1043 275 1084 
Q 275 1126 320 1133 
Q 794 1203 1837 2074 
L 1875 4563 
Q 1875 4794 1814 4880 
Q 1754 4966 1754 4995 
Q 1754 5024 1830 5024 
Q 1907 5024 2054 4976 
Q 2202 4928 2240 4883 
Q 2278 4838 2278 4749 
L 2208 -416 
Q 2208 -595 2099 -595 
Q 2048 -595 1965 -556 
Q 1882 -518 1821 -451 
Q 1760 -384 1760 -326 
Q 1760 -269 1779 -176 
Q 1798 -83 1811 230 
L 1837 1760 
z
M 5056 3270 
Q 4838 3731 4563 4070 
Q 4531 4115 4531 4166 
Q 4531 4218 4630 4282 
Q 4730 4346 4774 4346 
Q 4819 4346 4902 4259 
Q 4986 4173 5078 4041 
Q 5171 3910 5254 3776 
Q 5338 3642 5392 3542 
Q 5446 3443 5446 3401 
Q 5446 3360 5356 3270 
Q 5267 3181 5180 3181 
Q 5094 3181 5056 3270 
z
M 1312 2432 
Q 1235 2432 1197 2534 
Q 941 3213 653 3686 
Q 627 3718 627 3766 
Q 627 3814 704 3872 
Q 781 3930 857 3930 
Q 934 3930 979 3853 
Q 1312 3328 1449 3001 
Q 1587 2675 1587 2627 
Q 1587 2579 1523 2531 
Q 1459 2483 1392 2457 
Q 1325 2432 1312 2432 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-72b6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 628.45696 203.911288 
L 630.523076 204.286903 
L 632.589192 204.662518 
L 634.655307 205.038133 
L 636.721423 205.413747 
L 638.787539 205.789362 
L 640.853654 206.164977 
L 642.91977 206.540592 
L 644.985886 206.916206 
L 647.052002 207.291821 
L 649.118117 207.667436 
L 651.184233 208.043051 
L 653.250349 208.418665 
L 655.316464 208.79428 
L 657.38258 209.169895 
L 659.448696 209.54551 
L 661.514811 209.921124 
L 663.580927 210.296739 
L 665.647043 210.672354 
L 667.713159 211.047969 
L 669.779274 211.423583 
L 671.84539 211.799198 
L 673.911506 212.174813 
L 675.977621 212.550428 
L 678.043737 212.926042 
L 680.109853 213.301657 
L 682.175968 213.677272 
L 684.242084 214.052887 
L 686.3082 214.428501 
L 688.374316 214.804116 
L 690.440431 215.179731 
L 692.506547 215.555346 
L 694.572663 215.930961 
L 696.638778 216.306575 
L 698.704894 216.68219 
L 700.77101 217.057805 
L 702.837126 217.43342 
L 704.903241 217.809034 
L 706.969357 218.184649 
L 709.035473 218.560264 
L 711.101588 218.935879 
L 713.167704 219.311493 
L 715.23382 219.687108 
L 717.299935 220.062723 
L 719.366051 220.438338 
L 721.432167 220.813952 
L 723.498283 221.189567 
L 725.564398 221.565182 
L 727.630514 221.940797 
L 729.69663 222.316411 
L 731.762745 222.692026 
L 733.828861 223.067641 
L 735.894977 223.443256 
L 737.961092 223.81887 
L 740.027208 224.194485 
L 742.093324 224.5701 
L 744.15944 224.945715 
L 746.225555 225.321329 
L 748.291671 225.696944 
L 750.357787 226.072559 
L 752.423902 226.448174 
L 754.490018 226.823788 
L 756.556134 227.199403 
L 758.622249 227.575018 
L 760.688365 227.950633 
L 762.754481 228.326247 
L 764.820597 228.701862 
L 766.886712 229.077477 
L 768.952828 229.453092 
L 771.018944 229.828706 
L 773.085059 230.204321 
L 775.151175 230.579936 
L 777.217291 230.955551 
L 779.283407 231.331165 
L 781.349522 231.70678 
L 783.415638 232.082395 
L 785.481754 232.45801 
L 787.547869 232.833624 
L 789.613985 233.209239 
L 791.680101 233.584854 
L 793.746216 233.960469 
L 795.812332 234.336083 
L 797.878448 234.711698 
L 799.944564 235.087313 
L 802.010679 235.462928 
L 804.076795 235.838543 
L 806.142911 236.214157 
L 808.209026 236.589772 
L 810.275142 236.965387 
L 812.341258 237.341002 
L 814.407373 237.716616 
L 816.473489 238.092231 
L 818.539605 238.467846 
L 820.605721 238.843461 
L 822.671836 239.219075 
L 824.737952 239.59469 
L 826.804068 239.970305 
L 828.870183 240.34592 
L 830.936299 240.721534 
L 833.002415 241.097149 
" clip-path="url(#p335b2adaca)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 618.229687 345.6 
L 618.229687 68.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 618.229687 345.6 
L 843.229687 345.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_42">
    <!-- r = -0.119, p = 0.375 -->
    <g transform="translate(669.8175 62.4) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-39" d="M 2963 3667 
Q 2886 3629 2874 3629 
Q 2810 3629 2774 3712 
Q 2739 3795 2650 3904 
Q 2432 4160 2035 4160 
Q 1536 4160 1165 3725 
Q 819 3302 819 2899 
Q 819 2080 1453 2080 
Q 2003 2080 2394 2502 
Q 2662 2790 2829 3213 
Q 2906 3405 2925 3504 
Q 2944 3603 2963 3667 
z
M 2413 -83 
Q 2246 -83 2246 58 
Q 2246 109 2310 416 
Q 2374 723 2448 1081 
Q 2522 1440 2570 1670 
Q 2618 1901 2643 2045 
Q 2669 2189 2688 2285 
Q 2464 2010 2131 1837 
Q 1798 1664 1481 1664 
Q 1165 1664 918 1801 
Q 672 1939 525 2201 
Q 378 2464 378 2810 
Q 378 3482 864 4019 
Q 1350 4557 2003 4557 
Q 2528 4557 2854 4250 
Q 2982 4122 3034 4026 
Q 3066 4166 3075 4268 
Q 3085 4371 3174 4371 
L 3187 4371 
Q 3322 4358 3424 4272 
Q 3526 4186 3526 4134 
L 3526 4115 
Q 3302 3552 2893 1139 
Q 2778 442 2733 58 
Q 2720 -70 2438 -83 
L 2413 -83 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="axes_4">
   <g id="patch_11">
    <path d="M 910.729687 345.6 
L 1135.729687 345.6 
L 1135.729687 68.4 
L 910.729687 68.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_4">
    <defs>
     <path id="m63dcdaa909" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #7e2f8e; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p8c9da304f8)">
     <use xlink:href="#m63dcdaa909" x="991.291921" y="234.391304" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1011.701881" y="234.391304" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1073.127152" y="245.347826" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1080.396702" y="245.347826" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1088.246856" y="322.043478" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1097.39831" y="322.043478" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1054.35332" y="289.173913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1085.233774" y="289.173913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1029.093555" y="223.434783" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1017.617574" y="223.434783" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1081.217861" y="196.043478" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1048.454562" y="196.043478" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="991.03477" y="168.652174" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="998.332235" y="168.652174" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1004.000978" y="250.826087" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="998.707161" y="250.826087" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1018.89019" y="190.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1019.167718" y="190.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1000.844317" y="316.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1030.137629" y="316.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1025.579937" y="185.086957" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1027.362574" y="185.086957" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1058.783911" y="228.913043" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1051.006167" y="228.913043" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1076.670793" y="157.695652" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1094.910108" y="157.695652" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1074.826123" y="190.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1082.698468" y="190.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1002.493701" y="207" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1005.642963" y="207" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1023.586823" y="179.608696" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1013.925562" y="179.608696" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1017.97773" y="272.73913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1022.884094" y="272.73913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="920.95696" y="333" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="939.808897" y="333" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1043.244481" y="278.217391" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1125.502415" y="278.217391" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1009.021876" y="239.869565" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1026.857038" y="239.869565" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1101.291765" y="316.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1065.001502" y="316.565217" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1087.5895" y="272.73913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1115.199214" y="272.73913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1044.107503" y="217.956522" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1042.811865" y="217.956522" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1075.766262" y="278.217391" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1029.976584" y="278.217391" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1076.091137" y="294.652174" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1069.008276" y="294.652174" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1051.190267" y="163.173913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1050.484862" y="163.173913" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1062.164803" y="228.913043" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1068.519421" y="228.913043" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1066.376426" y="261.782609" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1073.282053" y="261.782609" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1001.581816" y="81" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
     <use xlink:href="#m63dcdaa909" x="1016.970119" y="81" style="fill: #7e2f8e; fill-opacity: 0.6; stroke: #7e2f8e; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_4">
    <path d="M 920.95696 132.225728 
L 920.95696 270.871371 
L 923.023076 270.500739 
L 925.089192 270.163656 
L 927.155307 269.817286 
L 929.221423 269.388842 
L 931.287539 269.01821 
L 933.353654 268.647578 
L 935.41977 268.197971 
L 937.485886 267.668828 
L 939.552002 267.139034 
L 941.618117 266.606525 
L 943.684233 266.017787 
L 945.750349 265.412793 
L 947.816464 264.804379 
L 949.88258 264.114206 
L 951.948696 263.389067 
L 954.014811 262.985598 
L 956.080927 262.383269 
L 958.147043 262.058268 
L 960.213159 261.518102 
L 962.279274 261.116349 
L 964.34539 260.714596 
L 966.411506 260.315884 
L 968.477621 260.047609 
L 970.543737 259.790547 
L 972.609853 259.516825 
L 974.675968 258.903932 
L 976.742084 258.442997 
L 978.8082 258.258354 
L 980.874316 257.811378 
L 982.940431 257.470337 
L 985.006547 257.210399 
L 987.072663 256.729198 
L 989.138778 256.469584 
L 991.204894 256.216437 
L 993.27101 256.099528 
L 995.337126 256.087792 
L 997.403241 255.988641 
L 999.469357 255.76299 
L 1001.535473 255.196699 
L 1003.601588 254.718565 
L 1005.667704 254.461846 
L 1007.73382 254.270271 
L 1009.799935 253.956879 
L 1011.866051 253.6975 
L 1013.932167 253.361834 
L 1015.998283 252.894873 
L 1018.064398 252.22515 
L 1020.130514 251.61643 
L 1022.19663 251.32304 
L 1024.262745 250.998173 
L 1026.328861 250.743523 
L 1028.394977 250.457393 
L 1030.461092 250.50729 
L 1032.527208 250.693367 
L 1034.593324 250.799048 
L 1036.65944 250.965082 
L 1038.725555 250.847266 
L 1040.791671 250.610383 
L 1042.857787 250.719015 
L 1044.923902 250.747239 
L 1046.990018 250.857376 
L 1049.056134 251.629956 
L 1051.122249 252.041513 
L 1053.188365 251.938916 
L 1055.254481 252.215695 
L 1057.320597 252.76193 
L 1059.386712 253.06133 
L 1061.452828 253.496969 
L 1063.518944 254.203077 
L 1065.585059 255.004281 
L 1067.651175 255.748114 
L 1069.717291 256.667909 
L 1071.783407 257.244637 
L 1073.849522 258.179559 
L 1075.915638 259.175859 
L 1077.981754 260.456381 
L 1080.047869 261.470181 
L 1082.113985 262.190974 
L 1084.180101 263.510569 
L 1086.246216 264.94805 
L 1088.312332 266.476261 
L 1090.378448 267.84631 
L 1092.444564 269.546714 
L 1094.510679 270.859687 
L 1096.576795 272.455236 
L 1098.642911 274.046546 
L 1100.709026 275.725507 
L 1102.775142 277.363768 
L 1104.841258 278.685755 
L 1106.907373 280.159173 
L 1108.973489 281.863391 
L 1111.039605 283.561052 
L 1113.105721 285.207925 
L 1115.171836 286.843144 
L 1117.237952 288.407966 
L 1119.304068 290.111934 
L 1121.370183 291.591682 
L 1123.436299 292.938346 
L 1125.502415 294.325876 
L 1125.502415 214.739645 
L 1125.502415 214.739645 
L 1123.436299 215.118616 
L 1121.370183 215.497588 
L 1119.304068 215.884636 
L 1117.237952 216.273387 
L 1115.171836 216.63735 
L 1113.105721 217.022372 
L 1111.039605 217.439641 
L 1108.973489 217.765615 
L 1106.907373 218.146691 
L 1104.841258 218.605895 
L 1102.775142 218.903318 
L 1100.709026 219.283661 
L 1098.642911 219.659879 
L 1096.576795 220.042109 
L 1094.510679 220.484089 
L 1092.444564 220.813752 
L 1090.378448 221.177446 
L 1088.312332 221.485363 
L 1086.246216 221.794505 
L 1084.180101 222.168092 
L 1082.113985 222.713852 
L 1080.047869 223.071632 
L 1077.981754 223.18723 
L 1075.915638 223.248498 
L 1073.849522 223.309795 
L 1071.783407 223.556612 
L 1069.717291 223.426044 
L 1067.651175 223.487114 
L 1065.585059 223.911905 
L 1063.518944 223.947086 
L 1061.452828 223.673895 
L 1059.386712 223.546308 
L 1057.320597 223.456714 
L 1055.254481 222.829628 
L 1053.188365 222.547117 
L 1051.122249 222.274396 
L 1049.056134 222.118885 
L 1046.990018 221.245394 
L 1044.923902 220.798192 
L 1042.857787 219.772839 
L 1040.791671 218.971193 
L 1038.725555 217.597187 
L 1036.65944 216.816126 
L 1034.593324 215.594626 
L 1032.527208 214.314555 
L 1030.461092 213.010012 
L 1028.394977 211.796598 
L 1026.328861 210.475309 
L 1024.262745 209.166669 
L 1022.19663 207.848342 
L 1020.130514 206.556507 
L 1018.064398 205.253293 
L 1015.998283 203.864257 
L 1013.932167 202.540649 
L 1011.866051 201.203436 
L 1009.799935 199.615551 
L 1007.73382 197.910042 
L 1005.667704 196.249822 
L 1003.601588 194.614855 
L 1001.535473 193.016409 
L 999.469357 191.581723 
L 997.403241 189.796096 
L 995.337126 188.02006 
L 993.27101 186.312178 
L 991.204894 184.610384 
L 989.138778 183.014564 
L 987.072663 181.551417 
L 985.006547 180.099464 
L 982.940431 178.423594 
L 980.874316 176.861505 
L 978.8082 175.325734 
L 976.742084 173.920321 
L 974.675968 172.535837 
L 972.609853 171.151354 
L 970.543737 169.76687 
L 968.477621 168.093592 
L 966.411506 166.335024 
L 964.34539 164.972985 
L 962.279274 163.580942 
L 960.213159 161.90853 
L 958.147043 160.236117 
L 956.080927 158.563705 
L 954.014811 156.891293 
L 951.948696 155.326672 
L 949.88258 153.785017 
L 947.816464 152.243363 
L 945.750349 150.701708 
L 943.684233 149.160054 
L 941.618117 147.618399 
L 939.552002 146.076745 
L 937.485886 144.53509 
L 935.41977 142.99393 
L 933.353654 141.455615 
L 931.287539 139.917301 
L 929.221423 138.378986 
L 927.155307 136.840672 
L 925.089192 135.302357 
L 923.023076 133.764043 
L 920.95696 132.225728 
z
" clip-path="url(#p8c9da304f8)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_7">
    <g id="xtick_16">
     <g id="line2d_70">
      <path d="M 929.370199 345.6 
L 929.370199 68.4 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_71">
      <g>
       <use xlink:href="#mfff21835cf" x="929.370199" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- -1.0 -->
      <g transform="translate(919.870199 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_72">
      <path d="M 976.191994 345.6 
L 976.191994 68.4 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_73">
      <g>
       <use xlink:href="#mfff21835cf" x="976.191994" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_44">
      <!-- -0.5 -->
      <g transform="translate(966.691994 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_74">
      <path d="M 1023.013789 345.6 
L 1023.013789 68.4 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_75">
      <g>
       <use xlink:href="#mfff21835cf" x="1023.013789" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_45">
      <!-- 0.0 -->
      <g transform="translate(1015.263789 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_76">
      <path d="M 1069.835584 345.6 
L 1069.835584 68.4 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_77">
      <g>
       <use xlink:href="#mfff21835cf" x="1069.835584" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_46">
      <!-- 0.5 -->
      <g transform="translate(1062.085584 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_78">
      <path d="M 1116.65738 345.6 
L 1116.65738 68.4 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_79">
      <g>
       <use xlink:href="#mfff21835cf" x="1116.65738" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 1.0 -->
      <g transform="translate(1108.90738 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="text_48">
     <!-- 前额叶HEP振幅 (第一阶段刺激态) -->
     <g transform="translate(948.2 373.58125) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="695.599884"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="730.599869"/>
      <use xlink:href="#LXGWWenKai-Regular-7b2c" x="765.599854"/>
      <use xlink:href="#LXGWWenKai-Regular-4e00" x="865.599838"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="965.599823"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1065.599808"/>
      <use xlink:href="#LXGWWenKai-Regular-523a" x="1165.599792"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1265.599777"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="1365.599762"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="1465.599747"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_8">
    <g id="ytick_19">
     <g id="line2d_80">
      <path d="M 910.729687 322.043478 
L 1135.729687 322.043478 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_81">
      <g>
       <use xlink:href="#me46b57928a" x="910.729687" y="322.043478" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 30 -->
      <g transform="translate(891.729687 325.578635) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_82">
      <path d="M 910.729687 267.26087 
L 1135.729687 267.26087 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_83">
      <g>
       <use xlink:href="#me46b57928a" x="910.729687" y="267.26087" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 40 -->
      <g transform="translate(891.729687 270.796026) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_84">
      <path d="M 910.729687 212.478261 
L 1135.729687 212.478261 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_85">
      <g>
       <use xlink:href="#me46b57928a" x="910.729687" y="212.478261" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 50 -->
      <g transform="translate(891.729687 216.013417) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_86">
      <path d="M 910.729687 157.695652 
L 1135.729687 157.695652 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_87">
      <g>
       <use xlink:href="#me46b57928a" x="910.729687" y="157.695652" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 60 -->
      <g transform="translate(891.729687 161.230808) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-36"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_88">
      <path d="M 910.729687 102.913043 
L 1135.729687 102.913043 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_89">
      <g>
       <use xlink:href="#me46b57928a" x="910.729687" y="102.913043" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 70 -->
      <g transform="translate(891.729687 106.4482) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-37"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- 后测状态焦虑 -->
     <g transform="translate(886.009375 237) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-540e" d="M 1389 3770 
Q 1389 4013 1353 4118 
Q 1318 4224 1318 4285 
Q 1318 4346 1420 4346 
Q 1523 4346 1843 4186 
Q 2656 4346 3258 4550 
Q 4314 4915 4525 5126 
Q 4550 5152 4588 5152 
Q 4627 5152 4704 5094 
Q 4909 4941 4909 4800 
Q 4909 4730 4813 4685 
Q 3283 4064 1830 3872 
Q 1824 3680 1821 3481 
Q 1818 3283 1805 3078 
L 5222 3270 
Q 5421 3290 5475 3312 
Q 5530 3334 5562 3334 
Q 5594 3334 5683 3277 
Q 5914 3110 5914 3002 
Q 5914 2925 5747 2912 
L 1786 2701 
Q 1760 2304 1686 1865 
Q 1613 1427 1411 1001 
Q 1210 576 976 265 
Q 742 -45 547 -217 
Q 352 -390 294 -390 
Q 237 -390 237 -332 
Q 237 -275 403 -70 
Q 570 134 768 486 
Q 1184 1254 1286 1958 
Q 1389 2573 1389 3770 
z
M 2208 -339 
Q 2227 -205 2227 -166 
L 2227 13 
Q 2227 51 2221 96 
L 2125 1357 
Q 2112 1581 2057 1680 
Q 2003 1779 2003 1824 
Q 2003 1869 2096 1869 
Q 2189 1869 2528 1734 
L 4794 1862 
L 4870 1862 
Q 5037 1862 5117 1772 
Q 5197 1683 5197 1632 
Q 5197 1581 5174 1545 
Q 5152 1510 5146 1478 
L 4960 205 
Q 5126 0 5126 -64 
Q 5126 -128 5068 -137 
Q 5011 -147 4922 -154 
L 2650 -218 
L 2669 -448 
L 2669 -467 
Q 2669 -621 2544 -621 
Q 2419 -621 2313 -537 
Q 2208 -454 2208 -339 
z
M 4704 1491 
L 2534 1382 
L 2624 134 
L 4557 205 
L 4704 1491 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-540e"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-72b6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_90">
    <path d="M 920.95696 214.576014 
L 923.023076 214.929141 
L 925.089192 215.282269 
L 927.155307 215.635396 
L 929.221423 215.988524 
L 931.287539 216.341651 
L 933.353654 216.694778 
L 935.41977 217.047906 
L 937.485886 217.401033 
L 939.552002 217.754161 
L 941.618117 218.107288 
L 943.684233 218.460415 
L 945.750349 218.813543 
L 947.816464 219.16667 
L 949.88258 219.519798 
L 951.948696 219.872925 
L 954.014811 220.226052 
L 956.080927 220.57918 
L 958.147043 220.932307 
L 960.213159 221.285435 
L 962.279274 221.638562 
L 964.34539 221.991689 
L 966.411506 222.344817 
L 968.477621 222.697944 
L 970.543737 223.051072 
L 972.609853 223.404199 
L 974.675968 223.757326 
L 976.742084 224.110454 
L 978.8082 224.463581 
L 980.874316 224.816709 
L 982.940431 225.169836 
L 985.006547 225.522963 
L 987.072663 225.876091 
L 989.138778 226.229218 
L 991.204894 226.582346 
L 993.27101 226.935473 
L 995.337126 227.2886 
L 997.403241 227.641728 
L 999.469357 227.994855 
L 1001.535473 228.347983 
L 1003.601588 228.70111 
L 1005.667704 229.054237 
L 1007.73382 229.407365 
L 1009.799935 229.760492 
L 1011.866051 230.11362 
L 1013.932167 230.466747 
L 1015.998283 230.819874 
L 1018.064398 231.173002 
L 1020.130514 231.526129 
L 1022.19663 231.879257 
L 1024.262745 232.232384 
L 1026.328861 232.585511 
L 1028.394977 232.938639 
L 1030.461092 233.291766 
L 1032.527208 233.644894 
L 1034.593324 233.998021 
L 1036.65944 234.351148 
L 1038.725555 234.704276 
L 1040.791671 235.057403 
L 1042.857787 235.410531 
L 1044.923902 235.763658 
L 1046.990018 236.116785 
L 1049.056134 236.469913 
L 1051.122249 236.82304 
L 1053.188365 237.176168 
L 1055.254481 237.529295 
L 1057.320597 237.882422 
L 1059.386712 238.23555 
L 1061.452828 238.588677 
L 1063.518944 238.941805 
L 1065.585059 239.294932 
L 1067.651175 239.648059 
L 1069.717291 240.001187 
L 1071.783407 240.354314 
L 1073.849522 240.707442 
L 1075.915638 241.060569 
L 1077.981754 241.413696 
L 1080.047869 241.766824 
L 1082.113985 242.119951 
L 1084.180101 242.473079 
L 1086.246216 242.826206 
L 1088.312332 243.179334 
L 1090.378448 243.532461 
L 1092.444564 243.885588 
L 1094.510679 244.238716 
L 1096.576795 244.591843 
L 1098.642911 244.944971 
L 1100.709026 245.298098 
L 1102.775142 245.651225 
L 1104.841258 246.004353 
L 1106.907373 246.35748 
L 1108.973489 246.710608 
L 1111.039605 247.063735 
L 1113.105721 247.416862 
L 1115.171836 247.76999 
L 1117.237952 248.123117 
L 1119.304068 248.476245 
L 1121.370183 248.829372 
L 1123.436299 249.182499 
L 1125.502415 249.535627 
" clip-path="url(#p8c9da304f8)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 910.729687 345.6 
L 910.729687 68.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 910.729687 345.6 
L 1135.729687 345.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_55">
    <!-- r = -0.118, p = 0.377 -->
    <g transform="translate(962.3175 62.4) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-38" d="M 1811 2304 
Q 1338 1971 1078 1708 
Q 819 1446 819 1126 
Q 819 915 922 723 
Q 1152 294 1798 294 
Q 2317 294 2621 515 
Q 2925 736 2925 1104 
Q 2925 1472 2797 1670 
Q 2669 1869 2422 2009 
Q 2176 2150 1811 2304 
z
M 1773 2739 
Q 2355 3110 2989 3616 
Q 2976 3610 2912 3610 
Q 2848 3610 2790 3699 
Q 2496 4179 1939 4179 
Q 1530 4179 1280 3971 
Q 1030 3763 1030 3504 
Q 1030 3245 1212 3081 
Q 1395 2918 1773 2739 
z
M 3130 3731 
Q 3238 3840 3315 3840 
Q 3418 3840 3418 3738 
Q 3418 3552 2528 2848 
Q 2317 2682 2157 2566 
Q 2752 2342 3069 2006 
Q 3386 1670 3386 1123 
Q 3386 576 2963 233 
Q 2541 -109 1798 -109 
Q 1363 -109 1040 51 
Q 717 211 541 489 
Q 365 768 365 1107 
Q 365 1805 1421 2496 
Q 1011 2714 796 2944 
Q 582 3174 582 3472 
Q 582 3770 748 4013 
Q 915 4256 1219 4406 
Q 1523 4557 1878 4557 
Q 2234 4557 2464 4464 
Q 2694 4371 2848 4236 
Q 3002 4102 3078 3987 
Q 3155 3872 3155 3827 
Q 3155 3782 3130 3731 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-38" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="axes_5">
   <g id="patch_14">
    <path d="M 1203.229687 345.6 
L 1428.229687 345.6 
L 1428.229687 68.4 
L 1203.229687 68.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_5">
    <defs>
     <path id="mace1a57d83" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #77ac30; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#pff6ffd1ccd)">
     <use xlink:href="#mace1a57d83" x="1283.791921" y="217.216216" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1304.201881" y="217.216216" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1365.627152" y="333" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1372.896702" y="333" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1380.746856" y="210.405405" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1389.89831" y="210.405405" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1346.85332" y="196.783784" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1377.733774" y="196.783784" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1321.593555" y="251.27027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1310.117574" y="251.27027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1373.717861" y="196.783784" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1340.954562" y="196.783784" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1283.53477" y="162.72973" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1290.832235" y="162.72973" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1296.500978" y="224.027027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1291.207161" y="224.027027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1311.39019" y="149.108108" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1311.667718" y="149.108108" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1293.344317" y="278.513514" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1322.637629" y="278.513514" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1318.079937" y="169.540541" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1319.862574" y="169.540541" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1351.283911" y="305.756757" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1343.506167" y="305.756757" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1369.170793" y="128.675676" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1387.410108" y="128.675676" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1367.326123" y="142.297297" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1375.198468" y="142.297297" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1294.993701" y="162.72973" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1298.142963" y="162.72973" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1316.086823" y="115.054054" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1306.425562" y="115.054054" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1310.47773" y="292.135135" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1315.384094" y="292.135135" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1213.45696" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1232.308897" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1335.744481" y="251.27027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1418.002415" y="251.27027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1301.521876" y="203.594595" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1319.357038" y="203.594595" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1393.791765" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1357.501502" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1380.0895" y="217.216216" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1407.699214" y="217.216216" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1336.607503" y="183.162162" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1335.311865" y="183.162162" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1368.266262" y="224.027027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1322.476584" y="224.027027" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1368.591137" y="230.837838" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1361.508276" y="230.837838" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1343.690267" y="81" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1342.984862" y="81" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1354.664803" y="101.432432" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1361.019421" y="101.432432" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1358.876426" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1365.782053" y="244.459459" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1294.081816" y="149.108108" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
     <use xlink:href="#mace1a57d83" x="1309.470119" y="149.108108" style="fill: #77ac30; fill-opacity: 0.6; stroke: #77ac30; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_5">
    <path d="M 1213.45696 151.046341 
L 1213.45696 232.446578 
L 1215.523076 231.88679 
L 1217.589192 231.327001 
L 1219.655307 230.767212 
L 1221.721423 230.084526 
L 1223.787539 229.669699 
L 1225.853654 229.125361 
L 1227.91977 228.796318 
L 1229.985886 228.576622 
L 1232.052002 228.33895 
L 1234.118117 227.706042 
L 1236.184233 227.017535 
L 1238.250349 226.649258 
L 1240.316464 226.454787 
L 1242.38258 225.933016 
L 1244.448696 225.583544 
L 1246.514811 225.223815 
L 1248.580927 224.864086 
L 1250.647043 224.275368 
L 1252.713159 223.660538 
L 1254.779274 223.045708 
L 1256.84539 222.562263 
L 1258.911506 222.210123 
L 1260.977621 221.865334 
L 1263.043737 221.706157 
L 1265.109853 221.561334 
L 1267.175968 221.169666 
L 1269.242084 220.953299 
L 1271.3082 220.905557 
L 1273.374316 220.836047 
L 1275.440431 220.431243 
L 1277.506547 220.076854 
L 1279.572663 219.576937 
L 1281.638778 219.350813 
L 1283.704894 219.143886 
L 1285.77101 218.941078 
L 1287.837126 218.7857 
L 1289.903241 218.508978 
L 1291.969357 218.29895 
L 1294.035473 218.090913 
L 1296.101588 217.976834 
L 1298.167704 217.783876 
L 1300.23382 217.813073 
L 1302.299935 217.737814 
L 1304.366051 217.651825 
L 1306.432167 217.535642 
L 1308.498283 217.548613 
L 1310.564398 217.598465 
L 1312.630514 217.774428 
L 1314.69663 217.787724 
L 1316.762745 217.932807 
L 1318.828861 217.72372 
L 1320.894977 217.582279 
L 1322.961092 218.143456 
L 1325.027208 218.444983 
L 1327.093324 218.514068 
L 1329.15944 218.420784 
L 1331.225555 218.670896 
L 1333.291671 218.889794 
L 1335.357787 218.779274 
L 1337.423902 218.915899 
L 1339.490018 219.110962 
L 1341.556134 219.439594 
L 1343.622249 219.946326 
L 1345.688365 220.30506 
L 1347.754481 220.559638 
L 1349.820597 220.758949 
L 1351.886712 221.323221 
L 1353.952828 222.236951 
L 1356.018944 222.829883 
L 1358.085059 223.168632 
L 1360.151175 223.537833 
L 1362.217291 224.155424 
L 1364.283407 224.669494 
L 1366.349522 225.32443 
L 1368.415638 225.927113 
L 1370.481754 226.626909 
L 1372.547869 227.33312 
L 1374.613985 228.031679 
L 1376.680101 228.555789 
L 1378.746216 229.430918 
L 1380.812332 229.918957 
L 1382.878448 230.394152 
L 1384.944564 231.142591 
L 1387.010679 232.022965 
L 1389.076795 232.890177 
L 1391.142911 233.713726 
L 1393.209026 234.559927 
L 1395.275142 235.360301 
L 1397.341258 236.016253 
L 1399.407373 236.743175 
L 1401.473489 237.669138 
L 1403.539605 238.667919 
L 1405.605721 239.442081 
L 1407.671836 240.374814 
L 1409.737952 241.129393 
L 1411.804068 241.811887 
L 1413.870183 242.399386 
L 1415.936299 243.138337 
L 1418.002415 243.877287 
L 1418.002415 172.18739 
L 1418.002415 172.18739 
L 1415.936299 172.733804 
L 1413.870183 173.334075 
L 1411.804068 173.813512 
L 1409.737952 174.326176 
L 1407.671836 174.892508 
L 1405.605721 175.392864 
L 1403.539605 175.917432 
L 1401.473489 176.3016 
L 1399.407373 176.685768 
L 1397.341258 177.069936 
L 1395.275142 177.508597 
L 1393.209026 177.968599 
L 1391.142911 178.430972 
L 1389.076795 178.89336 
L 1387.010679 179.355748 
L 1384.944564 179.818136 
L 1382.878448 180.280524 
L 1380.812332 180.742912 
L 1378.746216 181.2053 
L 1376.680101 181.667688 
L 1374.613985 182.132394 
L 1372.547869 182.609587 
L 1370.481754 183.049791 
L 1368.415638 183.505853 
L 1366.349522 183.476362 
L 1364.283407 183.662958 
L 1362.217291 184.317928 
L 1360.151175 184.487962 
L 1358.085059 184.560234 
L 1356.018944 185.02441 
L 1353.952828 185.497078 
L 1351.886712 185.699397 
L 1349.820597 185.728743 
L 1347.754481 186.344195 
L 1345.688365 186.61813 
L 1343.622249 186.983904 
L 1341.556134 187.348615 
L 1339.490018 186.979857 
L 1337.423902 187.432469 
L 1335.357787 187.077959 
L 1333.291671 186.917039 
L 1331.225555 186.880883 
L 1329.15944 187.19462 
L 1327.093324 187.465553 
L 1325.027208 187.526055 
L 1322.961092 187.320282 
L 1320.894977 187.073427 
L 1318.828861 186.689013 
L 1316.762745 186.21845 
L 1314.69663 186.13815 
L 1312.630514 185.915637 
L 1310.564398 185.678384 
L 1308.498283 185.404149 
L 1306.432167 184.901489 
L 1304.366051 184.307967 
L 1302.299935 183.785447 
L 1300.23382 183.362784 
L 1298.167704 182.749937 
L 1296.101588 182.137089 
L 1294.035473 181.591809 
L 1291.969357 180.949235 
L 1289.903241 180.284669 
L 1287.837126 179.523568 
L 1285.77101 178.582064 
L 1283.704894 177.815118 
L 1281.638778 176.918788 
L 1279.572663 176.399248 
L 1277.506547 175.662246 
L 1275.440431 174.907869 
L 1273.374316 174.264507 
L 1271.3082 173.397327 
L 1269.242084 172.644541 
L 1267.175968 171.88277 
L 1265.109853 171.086588 
L 1263.043737 170.235042 
L 1260.977621 169.623667 
L 1258.911506 168.862458 
L 1256.84539 168.004509 
L 1254.779274 167.154807 
L 1252.713159 166.24362 
L 1250.647043 165.124241 
L 1248.580927 164.393706 
L 1246.514811 163.671528 
L 1244.448696 162.906348 
L 1242.38258 161.977024 
L 1240.316464 161.141566 
L 1238.250349 160.403663 
L 1236.184233 159.668119 
L 1234.118117 158.932575 
L 1232.052002 158.197032 
L 1229.985886 157.40536 
L 1227.91977 156.480129 
L 1225.853654 155.724957 
L 1223.787539 154.976987 
L 1221.721423 154.048224 
L 1219.655307 153.175094 
L 1217.589192 152.46551 
L 1215.523076 151.755925 
L 1213.45696 151.046341 
z
" clip-path="url(#pff6ffd1ccd)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_9">
    <g id="xtick_21">
     <g id="line2d_91">
      <path d="M 1221.870199 345.6 
L 1221.870199 68.4 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_92">
      <g>
       <use xlink:href="#mfff21835cf" x="1221.870199" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_56">
      <!-- -1.0 -->
      <g transform="translate(1212.370199 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_93">
      <path d="M 1268.691994 345.6 
L 1268.691994 68.4 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_94">
      <g>
       <use xlink:href="#mfff21835cf" x="1268.691994" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_57">
      <!-- -0.5 -->
      <g transform="translate(1259.191994 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_95">
      <path d="M 1315.513789 345.6 
L 1315.513789 68.4 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_96">
      <g>
       <use xlink:href="#mfff21835cf" x="1315.513789" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 0.0 -->
      <g transform="translate(1307.763789 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_97">
      <path d="M 1362.335584 345.6 
L 1362.335584 68.4 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_98">
      <g>
       <use xlink:href="#mfff21835cf" x="1362.335584" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_59">
      <!-- 0.5 -->
      <g transform="translate(1354.585584 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_99">
      <path d="M 1409.15738 345.6 
L 1409.15738 68.4 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_100">
      <g>
       <use xlink:href="#mfff21835cf" x="1409.15738" y="345.6" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_60">
      <!-- 1.0 -->
      <g transform="translate(1401.40738 359.670312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="text_61">
     <!-- 前额叶HEP振幅 (第一阶段刺激态) -->
     <g transform="translate(1240.7 373.58125) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-524d"/>
      <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="695.599884"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="730.599869"/>
      <use xlink:href="#LXGWWenKai-Regular-7b2c" x="765.599854"/>
      <use xlink:href="#LXGWWenKai-Regular-4e00" x="865.599838"/>
      <use xlink:href="#LXGWWenKai-Regular-9636" x="965.599823"/>
      <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1065.599808"/>
      <use xlink:href="#LXGWWenKai-Regular-523a" x="1165.599792"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1265.599777"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="1365.599762"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="1465.599747"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_10">
    <g id="ytick_24">
     <g id="line2d_101">
      <path d="M 1203.229687 339.810811 
L 1428.229687 339.810811 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_102">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="339.810811" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_62">
      <!-- -10 -->
      <g transform="translate(1180.729687 343.345967) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_103">
      <path d="M 1203.229687 305.756757 
L 1428.229687 305.756757 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_104">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="305.756757" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_63">
      <!-- -5 -->
      <g transform="translate(1186.729687 309.291913) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_105">
      <path d="M 1203.229687 271.702703 
L 1428.229687 271.702703 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_106">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="271.702703" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_64">
      <!-- 0 -->
      <g transform="translate(1190.229687 275.237859) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_107">
      <path d="M 1203.229687 237.648649 
L 1428.229687 237.648649 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_108">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="237.648649" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_65">
      <!-- 5 -->
      <g transform="translate(1190.229687 241.183805) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_28">
     <g id="line2d_109">
      <path d="M 1203.229687 203.594595 
L 1428.229687 203.594595 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_110">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="203.594595" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_66">
      <!-- 10 -->
      <g transform="translate(1184.229687 207.129751) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_29">
     <g id="line2d_111">
      <path d="M 1203.229687 169.540541 
L 1428.229687 169.540541 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_112">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="169.540541" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_67">
      <!-- 15 -->
      <g transform="translate(1184.229687 173.075697) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_30">
     <g id="line2d_113">
      <path d="M 1203.229687 135.486486 
L 1428.229687 135.486486 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_114">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="135.486486" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_68">
      <!-- 20 -->
      <g transform="translate(1184.229687 139.021643) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_31">
     <g id="line2d_115">
      <path d="M 1203.229687 101.432432 
L 1428.229687 101.432432 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_116">
      <g>
       <use xlink:href="#me46b57928a" x="1203.229687" y="101.432432" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_69">
      <!-- 25 -->
      <g transform="translate(1184.229687 104.967589) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_70">
     <!-- 状态焦虑差值 -->
     <g transform="translate(1175.009375 237) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5dee" d="M 4333 3245 
Q 4493 3264 4534 3277 
Q 4576 3290 4624 3290 
Q 4672 3290 4745 3235 
Q 4819 3181 4870 3113 
Q 4922 3046 4922 3014 
Q 4922 2944 4787 2931 
L 3059 2842 
L 3027 2752 
Q 2938 2509 2822 2259 
L 5107 2362 
Q 5274 2374 5328 2393 
Q 5382 2413 5417 2413 
Q 5453 2413 5530 2368 
Q 5741 2259 5741 2131 
Q 5741 2054 5594 2042 
L 2656 1914 
Q 2502 1613 2304 1299 
Q 1837 557 1101 -32 
Q 806 -269 604 -381 
Q 403 -493 364 -493 
Q 326 -493 326 -445 
Q 326 -397 454 -282 
Q 1619 717 2189 1830 
Q 2208 1862 2221 1894 
L 1126 1850 
Q 998 1850 950 1891 
Q 902 1933 841 2029 
Q 781 2125 781 2169 
Q 781 2214 826 2214 
Q 986 2182 1088 2182 
L 1158 2182 
L 2394 2240 
Q 2509 2496 2598 2733 
L 2630 2816 
L 2125 2790 
L 2003 2790 
Q 1843 2790 1805 2822 
Q 1683 2925 1638 3066 
Q 1632 3078 1632 3110 
Q 1632 3142 1670 3142 
Q 1773 3117 1933 3117 
L 2010 3117 
L 2752 3155 
Q 2848 3462 2912 3699 
L 1798 3642 
L 1632 3635 
Q 1530 3635 1469 3673 
Q 1408 3712 1350 3805 
Q 1293 3898 1293 3942 
Q 1293 3987 1328 3987 
Q 1363 3987 1424 3974 
Q 1485 3962 1606 3962 
L 1677 3962 
L 3283 4058 
Q 4064 4736 4122 5120 
Q 4128 5210 4195 5210 
Q 4262 5210 4339 5146 
Q 4550 4979 4550 4902 
Q 4550 4774 4211 4489 
Q 3872 4205 3680 4083 
L 4691 4147 
Q 4845 4160 4889 4176 
Q 4934 4192 4982 4192 
Q 5030 4192 5107 4134 
Q 5286 3981 5286 3920 
Q 5286 3859 5244 3843 
Q 5203 3827 5139 3821 
L 3354 3725 
Q 3283 3482 3187 3181 
L 4333 3245 
z
M 2912 4243 
Q 2810 4090 2733 4090 
Q 2656 4090 2547 4224 
Q 2259 4557 2035 4730 
Q 1958 4800 1958 4851 
Q 1958 4902 2022 4969 
Q 2086 5037 2137 5037 
Q 2189 5037 2317 4947 
Q 2445 4858 2589 4730 
Q 2733 4602 2841 4490 
Q 2950 4378 2950 4339 
Q 2950 4301 2912 4243 
z
M 2355 1312 
Q 2509 1261 2637 1261 
L 2675 1261 
L 4365 1338 
Q 4506 1350 4566 1369 
Q 4627 1389 4668 1389 
Q 4710 1389 4835 1302 
Q 4960 1216 4960 1120 
Q 4960 1043 4826 1030 
L 3718 979 
L 3699 45 
L 5126 90 
Q 5299 102 5382 128 
Q 5466 154 5504 154 
Q 5542 154 5619 102 
Q 5837 -32 5837 -147 
Q 5837 -224 5690 -237 
L 1997 -346 
L 1869 -346 
Q 1709 -346 1658 -307 
Q 1555 -218 1513 -118 
Q 1472 -19 1472 9 
Q 1472 38 1497 38 
Q 1523 38 1596 16 
Q 1670 -6 1805 -6 
L 1869 -6 
L 3315 38 
L 3334 960 
L 2790 934 
L 2643 928 
Q 2400 928 2317 1267 
Q 2317 1312 2355 1312 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-503c" d="M 1901 128 
Q 2080 102 2278 102 
L 2880 122 
L 2835 2976 
Q 2822 3238 2774 3324 
Q 2726 3411 2726 3452 
Q 2726 3494 2819 3494 
Q 2912 3494 3200 3398 
L 3859 3430 
L 3891 3898 
L 3014 3834 
Q 2970 3827 2925 3827 
L 2822 3827 
Q 2688 3827 2598 3878 
Q 2522 3936 2474 4032 
Q 2426 4128 2426 4157 
Q 2426 4186 2461 4186 
Q 2496 4186 2569 4170 
Q 2643 4154 2784 4154 
L 2854 4154 
L 3923 4224 
L 3968 4742 
L 3968 4774 
Q 3968 4896 3920 4979 
Q 3872 5062 3872 5097 
Q 3872 5133 3917 5133 
Q 3962 5133 4070 5120 
Q 4378 5069 4378 4902 
L 4378 4890 
L 4307 4250 
L 5254 4307 
Q 5338 4314 5446 4349 
Q 5555 4384 5603 4384 
Q 5651 4384 5722 4339 
Q 5901 4230 5901 4122 
Q 5901 4038 5766 4019 
L 4275 3923 
L 4230 3450 
L 5082 3488 
L 5152 3494 
Q 5254 3494 5337 3424 
Q 5421 3354 5421 3277 
L 5395 3162 
L 5325 192 
L 5830 205 
Q 5971 205 6044 227 
Q 6118 250 6166 250 
Q 6214 250 6272 192 
Q 6419 58 6419 -32 
Q 6419 -122 6285 -134 
L 2336 -256 
L 2246 -256 
Q 2067 -256 1996 -189 
Q 1926 -122 1891 -22 
Q 1856 77 1856 102 
Q 1856 128 1901 128 
z
M 1850 4973 
L 1850 4998 
Q 1850 5062 1926 5062 
Q 1978 5062 2074 5018 
Q 2342 4909 2342 4768 
Q 2342 4659 2057 4134 
Q 1773 3610 1638 3398 
L 1638 -390 
Q 1638 -518 1523 -518 
Q 1382 -518 1283 -425 
Q 1184 -333 1184 -269 
Q 1184 -205 1206 -96 
Q 1229 13 1229 192 
L 1254 2848 
Q 768 2214 397 1920 
Q 262 1811 211 1811 
Q 160 1811 160 1865 
Q 160 1920 243 2010 
Q 934 2822 1427 3776 
Q 1638 4186 1750 4474 
Q 1862 4762 1862 4842 
Q 1862 4922 1850 4973 
z
M 4960 179 
L 4960 691 
L 3245 653 
L 3251 128 
L 4960 179 
z
M 3238 1498 
L 3245 966 
L 4966 1011 
L 4966 1562 
L 3238 1498 
z
M 3238 1811 
L 4979 1869 
L 4992 2381 
L 3226 2310 
L 3238 1811 
z
M 3219 2605 
L 4998 2682 
L 5011 3181 
L 3206 3098 
L 3219 2605 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-5dee" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-503c" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_117">
    <path d="M 1213.45696 198.90815 
L 1215.523076 198.991862 
L 1217.589192 199.075574 
L 1219.655307 199.159286 
L 1221.721423 199.242998 
L 1223.787539 199.32671 
L 1225.853654 199.410422 
L 1227.91977 199.494134 
L 1229.985886 199.577846 
L 1232.052002 199.661558 
L 1234.118117 199.74527 
L 1236.184233 199.828982 
L 1238.250349 199.912694 
L 1240.316464 199.996406 
L 1242.38258 200.080118 
L 1244.448696 200.16383 
L 1246.514811 200.247542 
L 1248.580927 200.331254 
L 1250.647043 200.414966 
L 1252.713159 200.498678 
L 1254.779274 200.58239 
L 1256.84539 200.666102 
L 1258.911506 200.749814 
L 1260.977621 200.833526 
L 1263.043737 200.917238 
L 1265.109853 201.00095 
L 1267.175968 201.084662 
L 1269.242084 201.168374 
L 1271.3082 201.252086 
L 1273.374316 201.335798 
L 1275.440431 201.41951 
L 1277.506547 201.503222 
L 1279.572663 201.586934 
L 1281.638778 201.670646 
L 1283.704894 201.754358 
L 1285.77101 201.83807 
L 1287.837126 201.921782 
L 1289.903241 202.005494 
L 1291.969357 202.089206 
L 1294.035473 202.172918 
L 1296.101588 202.25663 
L 1298.167704 202.340342 
L 1300.23382 202.424054 
L 1302.299935 202.507766 
L 1304.366051 202.591478 
L 1306.432167 202.67519 
L 1308.498283 202.758902 
L 1310.564398 202.842614 
L 1312.630514 202.926326 
L 1314.69663 203.010038 
L 1316.762745 203.09375 
L 1318.828861 203.177462 
L 1320.894977 203.261174 
L 1322.961092 203.344886 
L 1325.027208 203.428598 
L 1327.093324 203.51231 
L 1329.15944 203.596022 
L 1331.225555 203.679734 
L 1333.291671 203.763446 
L 1335.357787 203.847158 
L 1337.423902 203.93087 
L 1339.490018 204.014582 
L 1341.556134 204.098294 
L 1343.622249 204.182006 
L 1345.688365 204.265718 
L 1347.754481 204.34943 
L 1349.820597 204.433142 
L 1351.886712 204.516854 
L 1353.952828 204.600566 
L 1356.018944 204.684278 
L 1358.085059 204.76799 
L 1360.151175 204.851702 
L 1362.217291 204.935414 
L 1364.283407 205.019126 
L 1366.349522 205.102838 
L 1368.415638 205.18655 
L 1370.481754 205.270262 
L 1372.547869 205.353974 
L 1374.613985 205.437686 
L 1376.680101 205.521398 
L 1378.746216 205.60511 
L 1380.812332 205.688822 
L 1382.878448 205.772534 
L 1384.944564 205.856246 
L 1387.010679 205.939958 
L 1389.076795 206.02367 
L 1391.142911 206.107382 
L 1393.209026 206.191094 
L 1395.275142 206.274806 
L 1397.341258 206.358518 
L 1399.407373 206.44223 
L 1401.473489 206.525942 
L 1403.539605 206.609654 
L 1405.605721 206.693366 
L 1407.671836 206.777078 
L 1409.737952 206.86079 
L 1411.804068 206.944502 
L 1413.870183 207.028214 
L 1415.936299 207.111926 
L 1418.002415 207.195638 
" clip-path="url(#pff6ffd1ccd)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_15">
    <path d="M 1203.229687 345.6 
L 1203.229687 68.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_16">
    <path d="M 1203.229687 345.6 
L 1428.229687 345.6 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_71">
    <!-- r = -0.027, p = 0.842 -->
    <g transform="translate(1254.8175 62.4) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-38" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="text_72">
   <!-- 前额叶HEP振幅(第一阶段刺激态)与心理变量的相关性分析 -->
   <g transform="translate(528.638125 18.666875) scale(0.14 -0.14)">
    <defs>
     <path id="LXGWWenKai-Regular-4e0e" d="M 4800 1638 
Q 4883 2170 4909 2522 
L 2042 2381 
Q 1971 2374 1920 2358 
Q 1869 2342 1798 2342 
Q 1728 2342 1629 2387 
Q 1530 2432 1530 2547 
Q 1530 2586 1654 3101 
Q 1779 3616 1884 4173 
Q 1990 4730 1990 4832 
Q 1990 4934 1968 5014 
Q 1946 5094 1946 5126 
Q 1946 5178 2006 5178 
Q 2067 5178 2189 5126 
Q 2445 5024 2445 4896 
L 2342 4378 
Q 2298 4122 2246 3872 
L 4198 3974 
Q 4602 4006 4678 4038 
Q 4755 4070 4816 4070 
Q 4877 4070 4966 4019 
Q 5056 3968 5123 3894 
Q 5190 3821 5190 3763 
Q 5190 3686 5056 3674 
L 2170 3520 
L 1978 2739 
L 4998 2886 
L 5050 2886 
Q 5152 2886 5267 2819 
Q 5382 2752 5382 2672 
Q 5382 2592 5366 2547 
Q 5350 2502 5331 2326 
Q 5312 2150 5235 1658 
L 5171 1306 
Q 5037 627 4902 195 
Q 4768 -237 4678 -393 
Q 4589 -550 4505 -604 
Q 4422 -659 4352 -659 
Q 4282 -659 4006 -534 
Q 3731 -410 3302 -112 
Q 2874 186 2874 307 
Q 2874 358 2947 358 
Q 3021 358 3305 217 
Q 3590 77 4211 -128 
L 4237 -134 
Q 4282 -134 4307 -96 
Q 4538 352 4736 1286 
L 4800 1638 
z
M 941 1146 
Q 787 1133 678 1133 
Q 570 1133 483 1213 
Q 397 1293 352 1379 
Q 307 1466 307 1498 
Q 307 1530 358 1530 
L 390 1530 
Q 538 1485 742 1485 
L 832 1485 
L 3354 1600 
Q 3526 1606 3692 1644 
Q 3859 1683 3907 1683 
Q 3955 1683 4038 1632 
Q 4250 1504 4250 1376 
Q 4250 1299 4147 1286 
L 941 1146 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-53d8" d="M 2842 710 
Q 1952 1357 1952 1504 
Q 1952 1568 2035 1635 
Q 2118 1702 2172 1702 
Q 2227 1702 2304 1626 
Q 2714 1254 3174 947 
Q 3706 1382 4077 1869 
L 2010 1754 
Q 1965 1747 1926 1747 
L 1850 1747 
Q 1696 1747 1629 1824 
Q 1562 1901 1523 1993 
Q 1485 2086 1485 2115 
Q 1485 2144 1510 2144 
Q 1536 2144 1609 2128 
Q 1683 2112 1843 2112 
L 1939 2112 
L 4275 2234 
L 4371 2240 
Q 4461 2240 4573 2157 
Q 4685 2074 4685 2000 
Q 4685 1926 4627 1888 
Q 4570 1850 4544 1818 
Q 4070 1190 3507 736 
Q 4576 115 5830 -128 
Q 5965 -154 5965 -198 
L 5914 -282 
Q 5747 -531 5600 -531 
L 5382 -467 
Q 4762 -301 4173 -38 
Q 3584 224 3168 486 
Q 2368 -64 1402 -346 
Q 1024 -454 800 -492 
Q 576 -531 570 -531 
Q 454 -531 454 -486 
Q 454 -416 678 -333 
Q 1280 -109 1821 134 
Q 2362 378 2842 710 
z
M 5376 2586 
Q 4941 3002 4371 3392 
Q 4288 3450 4288 3491 
Q 4288 3533 4349 3625 
Q 4410 3718 4464 3718 
Q 4518 3718 4768 3564 
Q 5018 3411 5414 3094 
Q 5811 2778 5811 2688 
Q 5811 2630 5731 2534 
Q 5651 2438 5590 2438 
Q 5530 2438 5376 2586 
z
M 2003 3552 
Q 2074 3469 2074 3424 
Q 2074 3322 1626 2922 
Q 1178 2522 794 2330 
Q 678 2272 627 2272 
Q 576 2272 576 2304 
Q 576 2355 698 2458 
Q 1587 3245 1638 3648 
Q 1645 3750 1709 3750 
Q 1811 3750 2003 3552 
z
M 2931 5171 
Q 3398 5171 3398 5018 
L 3398 4390 
L 5094 4493 
Q 5306 4512 5370 4531 
Q 5434 4550 5485 4550 
Q 5536 4550 5626 4499 
Q 5837 4371 5837 4268 
Q 5837 4166 5728 4154 
L 4000 4058 
L 3930 4051 
L 3930 2573 
Q 3930 2381 3808 2381 
Q 3750 2381 3651 2441 
Q 3552 2502 3504 2550 
Q 3456 2598 3456 2669 
Q 3507 3021 3507 3238 
L 3507 4026 
L 2938 3994 
L 2803 3987 
L 2803 2547 
Q 2803 2355 2682 2355 
Q 2624 2355 2525 2416 
Q 2426 2477 2378 2525 
Q 2330 2573 2330 2643 
Q 2381 2893 2381 3213 
L 2381 3968 
L 1216 3898 
Q 1088 3885 960 3885 
Q 832 3885 761 3955 
Q 691 4026 646 4118 
Q 602 4211 602 4246 
Q 602 4282 646 4282 
Q 832 4250 986 4250 
L 1069 4250 
L 2976 4365 
L 2970 4800 
Q 2970 4960 2918 5027 
Q 2867 5094 2867 5132 
Q 2867 5171 2931 5171 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-91cf" d="M 2080 3302 
Q 2080 3187 1952 3187 
Q 1888 3187 1776 3235 
Q 1664 3283 1664 3398 
L 1664 3437 
Q 1670 3469 1670 3507 
L 1670 3565 
Q 1670 3654 1658 3770 
L 1574 4550 
Q 1555 4698 1497 4797 
Q 1440 4896 1440 4934 
Q 1440 4973 1523 4973 
Q 1606 4973 1933 4851 
L 4563 4998 
L 4614 4998 
Q 4762 4998 4835 4934 
Q 4909 4870 4909 4828 
Q 4909 4787 4896 4761 
Q 4883 4736 4877 4717 
L 4730 3763 
Q 4870 3603 4870 3545 
Q 4870 3488 4822 3481 
Q 4774 3475 4704 3469 
L 2080 3347 
L 2080 3302 
z
M 4461 4698 
L 1952 4550 
L 1984 4250 
L 4422 4378 
L 4461 4698 
z
M 4390 4102 
L 2010 3974 
L 2048 3635 
L 4346 3750 
L 4390 4102 
z
M 1024 2682 
L 883 2675 
Q 806 2675 704 2720 
Q 602 2765 538 2982 
Q 531 2995 531 3017 
Q 531 3040 556 3040 
Q 582 3040 652 3024 
Q 723 3008 851 3008 
L 934 3008 
L 5318 3219 
Q 5472 3226 5549 3248 
Q 5626 3270 5645 3270 
Q 5664 3270 5731 3241 
Q 5798 3213 5868 3155 
Q 5939 3098 5939 3021 
Q 5939 2925 5830 2912 
L 1024 2682 
z
M 595 -51 
Q 774 -96 992 -96 
L 2982 -51 
L 2982 326 
L 1914 294 
L 1779 288 
Q 1638 288 1564 336 
Q 1491 384 1421 582 
L 1414 621 
Q 1414 640 1430 640 
Q 1446 640 1510 621 
Q 1574 602 1747 602 
L 1837 602 
L 2989 634 
L 2989 979 
L 1978 941 
L 1984 896 
L 1984 877 
Q 1984 781 1865 781 
Q 1747 781 1667 832 
Q 1587 883 1587 973 
L 1587 1005 
L 1594 1120 
Q 1594 1203 1581 1306 
L 1510 2170 
Q 1498 2323 1427 2451 
Q 1414 2490 1414 2502 
Q 1414 2547 1497 2547 
Q 1581 2547 1882 2445 
L 4666 2586 
L 4736 2586 
Q 4806 2586 4905 2538 
Q 5005 2490 5005 2387 
Q 5005 2349 4989 2317 
Q 4973 2285 4966 2259 
L 4819 1325 
Q 4954 1171 4954 1113 
Q 4954 1056 4806 1043 
L 3360 992 
L 3360 640 
L 4429 672 
Q 4582 685 4681 707 
Q 4781 730 4806 730 
Q 4832 730 4902 698 
Q 5088 608 5088 505 
Q 5088 403 4973 390 
L 3354 339 
L 3354 -45 
L 5293 0 
Q 5459 0 5548 29 
Q 5638 58 5673 58 
Q 5709 58 5786 13 
Q 5984 -96 5984 -211 
Q 5984 -326 5850 -326 
L 1011 -422 
L 947 -422 
Q 826 -422 730 -374 
Q 634 -326 576 -109 
Q 570 -96 570 -73 
Q 570 -51 595 -51 
z
M 4570 2278 
L 3366 2221 
L 3366 1907 
L 4531 1965 
L 4570 2278 
z
M 2995 2208 
L 1882 2157 
L 1907 1843 
L 2995 1894 
L 2995 2208 
z
M 4499 1683 
L 3360 1632 
L 3360 1274 
L 4461 1312 
L 4499 1683 
z
M 2989 1613 
L 1926 1568 
L 1958 1222 
L 2989 1261 
L 2989 1613 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-7684" d="M 3846 4890 
L 3834 4992 
Q 3834 5069 3898 5069 
Q 3917 5069 4019 5024 
Q 4307 4896 4307 4762 
Q 4307 4589 3898 3738 
L 5504 3834 
L 5530 3834 
Q 5613 3834 5696 3773 
Q 5779 3712 5779 3616 
L 5760 3469 
Q 5709 1747 5530 301 
Q 5427 -544 5082 -544 
Q 4934 -544 4841 -483 
Q 4749 -422 4605 -313 
Q 4461 -205 4301 -70 
Q 4141 64 4025 179 
Q 3910 294 3910 345 
Q 3910 397 3984 397 
Q 4058 397 4282 262 
Q 4506 128 4947 -45 
L 4960 -45 
Q 4998 -45 5018 6 
Q 5299 1107 5338 3469 
L 3731 3366 
Q 3450 2880 3232 2595 
Q 3014 2310 2944 2310 
Q 2912 2310 2912 2371 
Q 2912 2432 3104 2787 
Q 3296 3142 3571 3875 
Q 3846 4608 3846 4845 
L 3846 4890 
z
M 1715 4749 
Q 1715 4768 1702 4845 
Q 1702 4922 1779 4922 
Q 1920 4922 2035 4832 
Q 2150 4742 2150 4665 
Q 2150 4589 1920 4160 
Q 1715 3802 1581 3603 
L 2483 3654 
L 2560 3654 
Q 2746 3654 2803 3507 
Q 2822 3462 2822 3456 
L 2790 3328 
L 2688 480 
Q 2842 282 2842 214 
Q 2842 147 2790 134 
Q 2739 122 2656 115 
L 1229 64 
L 1235 -147 
L 1235 -166 
Q 1235 -307 1126 -307 
Q 979 -307 892 -233 
Q 806 -160 806 -77 
L 851 243 
L 794 3232 
Q 794 3418 717 3539 
Q 672 3629 672 3664 
Q 672 3699 768 3699 
Q 864 3699 1165 3584 
L 1222 3584 
Q 1715 4512 1715 4749 
z
M 2400 3302 
L 1171 3238 
L 1190 2061 
L 2368 2118 
L 2400 3302 
z
M 4755 1446 
Q 4755 1318 4589 1229 
Q 4525 1190 4483 1190 
Q 4442 1190 4358 1299 
Q 4160 1619 3913 1913 
Q 3667 2208 3564 2307 
Q 3462 2406 3462 2464 
Q 3462 2522 3532 2589 
Q 3603 2656 3657 2656 
Q 3712 2656 3869 2505 
Q 4026 2355 4390 1939 
Q 4755 1523 4755 1446 
z
M 2355 1773 
L 1197 1722 
L 1222 422 
L 2317 461 
L 2355 1773 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-76f8" d="M 819 3347 
L 1722 3424 
L 1734 4621 
Q 1734 4794 1676 4874 
Q 1619 4954 1619 4986 
Q 1619 5037 1702 5037 
L 1766 5030 
Q 2131 4960 2131 4781 
L 2112 3462 
L 2483 3494 
Q 2605 3507 2669 3529 
Q 2733 3552 2771 3552 
Q 2810 3552 2886 3494 
Q 3091 3354 3091 3258 
Q 3091 3187 2944 3174 
L 2112 3104 
L 2106 2566 
Q 2189 2630 2240 2630 
Q 2291 2630 2438 2518 
Q 2586 2406 2822 2150 
Q 3059 1894 3059 1830 
Q 3059 1766 2979 1686 
Q 2899 1606 2832 1606 
Q 2765 1606 2675 1728 
Q 2438 2042 2099 2355 
L 2067 -474 
Q 2067 -640 1958 -640 
Q 1818 -640 1718 -547 
Q 1619 -454 1619 -390 
Q 1619 -326 1651 -192 
Q 1683 -58 1683 166 
L 1690 346 
Q 1690 1830 1728 2368 
L 1728 2458 
Q 1523 1843 973 1197 
Q 749 934 566 780 
Q 384 627 339 627 
Q 294 627 294 675 
Q 294 723 371 826 
Q 1101 1754 1632 3059 
L 1024 3008 
Q 934 2995 858 2995 
Q 698 2995 598 3126 
Q 499 3258 499 3325 
Q 499 3392 531 3392 
Q 614 3379 684 3363 
Q 755 3347 819 3347 
z
M 5696 4013 
L 5651 3846 
L 5562 410 
Q 5734 237 5734 160 
Q 5734 83 5683 57 
Q 5632 32 5562 26 
L 3680 -38 
L 3686 -339 
Q 3686 -474 3584 -474 
L 3494 -448 
Q 3245 -390 3245 -224 
Q 3245 -186 3264 -93 
Q 3283 0 3283 275 
L 3219 3757 
Q 3219 3968 3177 4073 
Q 3136 4179 3136 4218 
Q 3136 4282 3235 4282 
Q 3334 4282 3629 4141 
L 5306 4250 
L 5414 4256 
Q 5606 4256 5670 4090 
Q 5696 4032 5696 4013 
z
M 5235 3891 
L 3616 3789 
L 3629 2944 
L 5216 3034 
L 5235 3891 
z
M 5210 2682 
L 3635 2598 
L 3654 1677 
L 5190 1760 
L 5210 2682 
z
M 5184 1402 
L 3661 1331 
L 3674 333 
L 5165 384 
L 5184 1402 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5173" d="M 1862 4723 
Q 1760 4826 1926 4947 
Q 2029 5018 2083 5002 
Q 2138 4986 2301 4800 
Q 2464 4614 2672 4281 
Q 2880 3949 2854 3865 
Q 2829 3782 2720 3731 
Q 2611 3680 2556 3696 
Q 2502 3712 2374 3981 
Q 2246 4250 1862 4723 
z
M 1120 1818 
Q 1005 1818 902 1850 
Q 762 1933 685 2125 
Q 672 2163 672 2176 
Q 672 2234 736 2234 
Q 762 2234 838 2214 
Q 915 2195 1069 2195 
L 1133 2195 
L 2829 2278 
Q 2918 2662 2938 3258 
L 1587 3181 
L 1459 3174 
Q 1286 3174 1229 3232 
Q 1056 3398 1056 3565 
Q 1056 3610 1088 3610 
Q 1126 3610 1206 3584 
Q 1286 3558 1414 3558 
L 1446 3558 
L 3309 3667 
Q 3322 3699 3392 3782 
Q 4045 4730 4026 5030 
Q 4013 5267 4275 5120 
Q 4378 5056 4458 4982 
Q 4538 4909 4544 4864 
Q 4550 4819 4493 4717 
Q 4096 4070 3686 3757 
Q 3565 3686 3546 3680 
L 4634 3744 
Q 4794 3757 4845 3776 
Q 4896 3795 4925 3795 
Q 4954 3795 5024 3731 
Q 5235 3578 5235 3469 
Q 5235 3386 5075 3366 
L 3386 3277 
Q 3360 2701 3270 2298 
L 4954 2374 
Q 5146 2394 5210 2416 
Q 5274 2438 5315 2438 
Q 5357 2438 5446 2381 
Q 5670 2234 5670 2112 
Q 5670 2029 5504 2010 
L 3398 1914 
Q 4122 864 5005 339 
Q 5478 58 6067 -134 
Q 6202 -173 6202 -221 
Q 6202 -269 6138 -346 
Q 5965 -544 5837 -544 
L 5651 -474 
Q 5158 -282 4662 73 
Q 4166 429 3792 809 
Q 3418 1190 3110 1677 
Q 3008 1408 2957 1299 
Q 2528 429 1734 -51 
Q 1139 -416 614 -525 
Q 422 -563 364 -563 
Q 307 -563 307 -506 
Q 307 -435 538 -326 
Q 2330 480 2726 1882 
L 1120 1818 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-6790" d="M 3501 4141 
Q 3968 4314 4291 4499 
Q 4614 4685 4896 4864 
Q 5005 4941 5056 5046 
Q 5107 5152 5174 5152 
Q 5242 5152 5366 4989 
Q 5491 4826 5491 4762 
Q 5491 4698 5408 4653 
Q 4730 4282 4317 4122 
Q 3904 3962 3526 3834 
L 3526 3411 
Q 3526 3206 3520 3014 
L 5504 3130 
Q 5683 3149 5750 3171 
Q 5818 3194 5853 3194 
Q 5888 3194 5971 3139 
Q 6054 3085 6118 3011 
Q 6182 2938 6182 2893 
Q 6182 2810 6029 2797 
L 4966 2739 
L 4966 -435 
Q 4966 -614 4851 -614 
Q 4838 -614 4755 -589 
Q 4512 -512 4512 -346 
Q 4512 -294 4534 -195 
Q 4557 -96 4557 134 
L 4570 2714 
L 3501 2656 
Q 3430 1357 3008 538 
Q 2829 192 2656 -6 
Q 2483 -205 2368 -288 
Q 2253 -371 2233 -371 
Q 2214 -371 2214 -326 
Q 2214 -282 2304 -134 
Q 2618 358 2816 973 
Q 3117 1926 3117 3360 
Q 3117 3789 3097 3904 
Q 3078 4019 3043 4105 
Q 3008 4192 3008 4240 
Q 3008 4288 3101 4288 
Q 3194 4288 3501 4141 
z
M 467 3373 
Q 627 3341 704 3341 
L 1549 3398 
L 1562 4614 
Q 1562 4762 1504 4848 
Q 1446 4934 1446 4954 
Q 1446 5011 1526 5011 
Q 1606 5011 1744 4960 
Q 1882 4909 1917 4867 
Q 1952 4826 1952 4755 
L 1933 3430 
L 2304 3456 
Q 2438 3469 2496 3488 
Q 2554 3507 2595 3507 
Q 2637 3507 2710 3465 
Q 2784 3424 2841 3360 
Q 2899 3296 2899 3245 
Q 2899 3155 2758 3142 
L 1926 3078 
L 1920 2534 
Q 1990 2586 2042 2586 
Q 2144 2586 2458 2227 
Q 2778 1882 2778 1795 
Q 2778 1709 2685 1635 
Q 2592 1562 2528 1562 
Q 2464 1562 2387 1677 
Q 2182 1990 1920 2285 
L 1882 -461 
Q 1882 -614 1766 -614 
Q 1696 -614 1574 -547 
Q 1453 -480 1453 -352 
Q 1453 -307 1478 -211 
Q 1504 -115 1504 122 
L 1510 307 
Q 1510 1792 1549 2291 
L 1549 2368 
Q 1427 1965 1084 1469 
Q 742 973 474 730 
Q 352 621 301 621 
Q 262 621 262 672 
Q 262 723 333 819 
Q 954 1670 1459 3046 
L 941 3008 
Q 826 2995 726 2995 
Q 627 2995 553 3078 
Q 480 3162 451 3248 
Q 422 3334 422 3341 
Q 422 3373 467 3373 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#LXGWWenKai-Regular-524d"/>
    <use xlink:href="#LXGWWenKai-Regular-989d" x="99.999985"/>
    <use xlink:href="#LXGWWenKai-Regular-53f6" x="199.999969"/>
    <use xlink:href="#LXGWWenKai-Regular-48" x="299.999954"/>
    <use xlink:href="#LXGWWenKai-Regular-45" x="369.999939"/>
    <use xlink:href="#LXGWWenKai-Regular-50" x="433.799927"/>
    <use xlink:href="#LXGWWenKai-Regular-632f" x="495.599915"/>
    <use xlink:href="#LXGWWenKai-Regular-5e45" x="595.599899"/>
    <use xlink:href="#LXGWWenKai-Regular-28" x="695.599884"/>
    <use xlink:href="#LXGWWenKai-Regular-7b2c" x="730.599869"/>
    <use xlink:href="#LXGWWenKai-Regular-4e00" x="830.599854"/>
    <use xlink:href="#LXGWWenKai-Regular-9636" x="930.599838"/>
    <use xlink:href="#LXGWWenKai-Regular-6bb5" x="1030.599823"/>
    <use xlink:href="#LXGWWenKai-Regular-523a" x="1130.599808"/>
    <use xlink:href="#LXGWWenKai-Regular-6fc0" x="1230.599792"/>
    <use xlink:href="#LXGWWenKai-Regular-6001" x="1330.599777"/>
    <use xlink:href="#LXGWWenKai-Regular-29" x="1430.599762"/>
    <use xlink:href="#LXGWWenKai-Regular-4e0e" x="1465.599747"/>
    <use xlink:href="#LXGWWenKai-Regular-5fc3" x="1565.599731"/>
    <use xlink:href="#LXGWWenKai-Regular-7406" x="1665.599716"/>
    <use xlink:href="#LXGWWenKai-Regular-53d8" x="1765.599701"/>
    <use xlink:href="#LXGWWenKai-Regular-91cf" x="1865.599686"/>
    <use xlink:href="#LXGWWenKai-Regular-7684" x="1965.59967"/>
    <use xlink:href="#LXGWWenKai-Regular-76f8" x="2065.599655"/>
    <use xlink:href="#LXGWWenKai-Regular-5173" x="2165.59964"/>
    <use xlink:href="#LXGWWenKai-Regular-6027" x="2265.599625"/>
    <use xlink:href="#LXGWWenKai-Regular-5206" x="2365.599609"/>
    <use xlink:href="#LXGWWenKai-Regular-6790" x="2465.599594"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p103214e778">
   <rect x="33.229688" y="68.4" width="225" height="277.2"/>
  </clipPath>
  <clipPath id="p518e0415ac">
   <rect x="325.729688" y="68.4" width="225" height="277.2"/>
  </clipPath>
  <clipPath id="p335b2adaca">
   <rect x="618.229687" y="68.4" width="225" height="277.2"/>
  </clipPath>
  <clipPath id="p8c9da304f8">
   <rect x="910.729687" y="68.4" width="225" height="277.2"/>
  </clipPath>
  <clipPath id="pff6ffd1ccd">
   <rect x="1203.229687" y="68.4" width="225" height="277.2"/>
  </clipPath>
 </defs>
</svg>

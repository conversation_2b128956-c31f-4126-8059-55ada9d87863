import argparse
import logging
import sys
from parse_config import ConfigParser
from datetime import datetime
import train
import interpret
import torch
import os
import numpy as np
import pandas as pd
from pathlib import Path
from utils import read_json

# 设置日志格式和级别
def setup_logging(log_dir='logs', log_level=logging.INFO):
    """设置日志配置"""
    # 创建日志目录
    log_dir = Path(log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)

    # 生成日志文件名，包含时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = log_dir / f'lorenz_experiment_{timestamp}.log'

    # 配置根日志记录器
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return logging.getLogger()

def construct_demo():
    task_list = {}
    for i in [15]:
        task_list[f'fMRI{i}'] = {
            'dataset': f"data/fMRI/timeseries{i}.csv",
            'groundtruth': f"data/fMRI/sim{i}_gt_processed.csv"
        }
    return task_list

def construct_fMRI():
    task_list = {}
    for i in range(1,29):
        task_list[f'fMRI{i}'] = {
            'dataset': f"data/fMRI/timeseries{i}.csv",
            'groundtruth': f"data/fMRI/sim{i}_gt_processed.csv"
        }
    return task_list

def construct_basic_diamond():
    task_list = {}
    for i in range(10):
        task_list[f'diamond{i}'] = {
            'dataset': f"data/basic/diamond/data_{i}.csv",
            'groundtruth': f"data/basic/diamond/groundtruth.csv"
        }
    return task_list

def construct_basic_mediator():
    task_list = {}
    for i in range(10):
        task_list[f'mediator{i}'] = {
            'dataset': f"data/basic/mediator/data_{i}.csv",
            'groundtruth': f"data/basic/mediator/groundtruth.csv"
        }
    return task_list

def construct_basic_v():
    task_list = {}
    for i in range(10):
        task_list[f'v{i}'] = {
            'dataset': f"data/basic/v/data_{i}.csv",
            'groundtruth': f"data/basic/v/groundtruth.csv"
        }
    return task_list

def construct_basic_fork():
    task_list = {}
    for i in range(10):
        task_list[f'fork{i}'] = {
            'dataset': f"data/basic/fork/data_{i}.csv",
            'groundtruth': f"data/basic/fork/groundtruth.csv"
        }
    return task_list

def construct_lorenz():
    task_list = {}
    for i in range(10):
        task_list[f'lorenz{i}'] = {
            'dataset': f"data/lorenz96/timeseries{i}.csv",
            'groundtruth': f"data/lorenz96/groundtruth.csv"
        }
    return task_list

tasks={
    'demo': construct_demo,
    'fMRI': construct_fMRI,
    'diamond': construct_basic_diamond,
    'mediator': construct_basic_mediator,
    'v': construct_basic_v,
    'fork': construct_basic_fork,
    'lorenz':construct_lorenz
}

def runtask(label, args, dataset, ground_truth, task_name):
    logger = logging.getLogger()

    # fix random seeds for reproducibility
    SEED = 123
    logger.info(f"设置随机种子: {SEED}")
    torch.manual_seed(SEED)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    np.random.seed(SEED)

    args_dict = {'name':f'Batch Runner/{label}/{task_name}',
                 'config': args.config,
                 'resume': None,
                 'device': args.device,
                 'data_dir': dataset}
    logger.info(f"配置参数: {args_dict}")

    logger.info("初始化配置...")
    config = ConfigParser.from_args(args=args_dict, run_id='model')

    logger.info("开始训练模型...")
    train.main(config)
    logger.info("模型训练完成")

    logger.info("清理GPU缓存...")
    torch.cuda.empty_cache()

    logger.info("加载模型进行解释...")
    model_path = f'saved/models/Batch Runner/{label}/{task_name}/model'
    logger.info(f"模型路径: {model_path}")
    model, config, data_loader = interpret.load_model(model_path, args, f'Batch Runner/{label}/{task_name}', 'casuality')

    logger.info("开始模型解释...")
    interpret.main(model, config, data_loader, ground_truth)
    logger.info("模型解释完成")

    logger.info("清理GPU缓存...")
    torch.cuda.empty_cache()

def main(args):
    # 设置日志
    logger = setup_logging()
    logger.info(f"=== CausalFormer {args.task} 实验开始 ===")
    logger.info(f"配置文件: {args.config}")
    logger.info(f"设备: {args.device}")

    task_list = tasks[args.task]()
    logger.info(f"任务列表: {list(task_list.keys())}")

    label = datetime.now().strftime(r'%m%d_%H%M%S')
    logger.info(f"实验标签: {label}")

    for task_name, task_msg in task_list.items():
        logger.info(f"开始任务: {task_name}")
        logger.info(f"数据集: {task_msg['dataset']}")
        logger.info(f"真实值: {task_msg['groundtruth']}")
        runtask(label, args, task_msg['dataset'], task_msg['groundtruth'], task_name)
        logger.info(f"任务 {task_name} 完成")

    configJSON = read_json(args.config)
    save_dir = Path(configJSON['trainer']['save_dir'])
    logger.info(f"结果保存目录: {save_dir}")

    results = []
    for task_name in task_list:
        fname = f'{save_dir}/log/Batch Runner/{label}/{task_name}/casuality/info.log'
        if os.path.exists(fname):
            with open(fname, 'r', encoding='utf-8') as f: #打开文件，指定UTF-8编码
                lines = f.readlines() #读取所有行
                result={
                    "Precision'": float(lines[-8].split(':')[-1][:-1]),
                    "Recall'": float(lines[-7].split(':')[-1][:-1]),
                    "F1'": float(lines[-6].split(':')[-1][:-1]),
                    "Precision": float(lines[-4].split(':')[-1][:-1]),
                    "Recall": float(lines[-3].split(':')[-1][:-1]),
                    "F1": float(lines[-2].split(':')[-1][:-1]),
                    "PoD": float(lines[-1].split(':')[-1][:-2])/100
                }
                results.append(result)
    df = pd.DataFrame(results, index=[i for i in range(1,len(results)+1)])
    summary_dir = save_dir / 'log' / 'Batch Runner' / label / 'summary.csv'
    df.to_csv(summary_dir, encoding='utf-8')
    print("===================Summary===================")
    print('\t'+ df.to_string().replace('\n', '\n\t'))

if __name__=="__main__":
    args = argparse.ArgumentParser(description='CausalityInterpret')
    args.add_argument('-c', '--config', default=None, type=str,
                      help='config file path (default: None)')
    args.add_argument('-d', '--device', default="0", type=str,
                      help='indices of GPUs to enable (default: all)')
    args.add_argument('-t', '--task', default='fMRI', type=str,
                      help='task (default: fMRI)')
    args = args.parse_args()

    try:
        # 设置初始日志
        logger = setup_logging()
        logger.info("=== CausalFormer 实验启动 ===")
        logger.info(f"命令行参数: config={args.config}, device={args.device}, task={args.task}")

        # 运行主程序
        label = main(args)

        logger.info("=== CausalFormer 实验完成 ===")
    except Exception as e:
        logger = logging.getLogger()
        logger.error(f"实验过程中发生错误: {str(e)}", exc_info=True)
        raise

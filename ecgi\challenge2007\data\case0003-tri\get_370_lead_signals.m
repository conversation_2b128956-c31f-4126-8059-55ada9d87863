% get_370_leads.m
% challenge ECGs
% 20091026; <PERSON><PERSON>

clear     
% http://www.physionet.org/challenge/2007/data:
           % Geometry: Dalhousie lead system:
                 % Electrode Location 120 Leads.pdf
                 % Torso 352 Nodes.pdf
           
siglabels=loadmat('elnodes_120_352.lst'); % nodes among the 352 nodes Dalhousie grid where potentials have been recorded
                      
sigfile='ecgs_case3.asc';
PHI=loadmat(sigfile); % original data matrix; first three columns do not relate to potentials
                      % next three columns are potentials VR, VL, VF
                      % remaining ones: 120 potentials recorded at the nodes: siglabels of the Dalhausie grid 
                     
                      
PHI=PHI';      % personal preference
PHI=PHI([(4:6)'; siglabels+6],:); % extracts recorded data only: 123 leads

PHI=PHI/1000;   % conversion to mV

T=loadmat('transfer_370_123.mat');

% T=intripol(VER,ITRI,elnodes);
% savemat('transfer_370_123.mat',T);

PSI=T*PHI; % potentials at all 370 nodes of the thorax geometry defined at:
           % " Detailed heart, lung and torso geometry   ..."


elnodes =loadmat('elnodes_123_370.lst');   % labels of the vertices  
                                           % of the thorax geometry where signals have been recorded



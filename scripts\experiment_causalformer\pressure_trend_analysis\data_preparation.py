#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 压力趋势数据准备模块

功能：
- 根据实验阶段的实际压力变化规律，组织数据
- 提取EEG和ECG特征，关联实验阶段的压力水平
- 基于时间序列构建特征集，用于训练压力变化预测模型

作者：AI助手
日期：2025年5月23日
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
import glob
import mne
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from scipy import stats
import torch
from torch.utils.data import Dataset

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"pressure_data_preparation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("PressureTrend-DataPrep")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

# 定义实验阶段信息
STAGE_ORDER = ['rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3']

# 定义真实压力变化模式，这里用归一化后的相对值表示
# 符合题目中描述：第一阶段快速升高，第二阶段依然升高但速度较慢，第三阶段有差异化但整体保持高水平
# 静息态1作为基线，静息态2高于1，静息态3低于2但高于1
PRESSURE_PATTERN = {
    'rest1': 0.1,   # 基线，压力最低
    'test1': 0.6,   # 第一次刺激，压力快速升高
    'rest2': 0.4,   # 第一次静息，压力降低但仍高于基线
    'test2': 0.8,   # 第二次刺激，压力进一步升高
    'rest3': 0.3,   # 第二次静息，压力降低但仍高于基线
    'test3': 0.9    # 第三次刺激，压力达到最高水平
}

# 定义压力变化率，表示与上一个阶段相比的变化速率
PRESSURE_CHANGE_RATE = {
    'rest1': 0.0,   # 基线，无变化率
    'test1': 0.5,   # 第一次刺激，快速上升
    'rest2': -0.2,  # 第一次静息，轻微下降
    'test2': 0.4,   # 第二次刺激，中速上升
    'rest3': -0.5,  # 第二次静息，明显下降
    'test3': 0.6    # 第三次刺激，快速上升
}

class PressureDataset(Dataset):
    """压力数据集类，用于构建模型训练数据"""
    
    def __init__(self, eeg_features, ecg_features, pressure_labels, sequence_length=3):
        """
        初始化数据集
        
        参数:
        eeg_features (np.ndarray): EEG特征，形状为 (n_samples, n_eeg_features)
        ecg_features (np.ndarray): ECG特征，形状为 (n_samples, n_ecg_features)
        pressure_labels (np.ndarray): 压力标签，形状为 (n_samples, n_labels)
        sequence_length (int): 序列长度
        """
        self.eeg_features = torch.FloatTensor(eeg_features)
        self.ecg_features = torch.FloatTensor(ecg_features)
        self.pressure_labels = torch.FloatTensor(pressure_labels)
        self.sequence_length = sequence_length
        self.n_samples = len(eeg_features) - sequence_length
        
    def __len__(self):
        """返回数据集大小"""
        return self.n_samples
    
    def __getitem__(self, idx):
        """获取指定索引的数据样本"""
        # 获取连续的几个时间点作为输入序列
        eeg_seq = self.eeg_features[idx:idx+self.sequence_length]
        ecg_seq = self.ecg_features[idx:idx+self.sequence_length]
        features_seq = torch.cat([eeg_seq, ecg_seq], dim=1)
        # 预测目标为下一个时间点的压力值
        target = self.pressure_labels[idx+self.sequence_length]
        return features_seq, target

def load_stress_scale_data(excel_path):
    """
    加载压力量表数据
    
    参数:
    excel_path (str): Excel文件路径
    
    返回:
    pd.DataFrame: 包含所有被试压力量表数据的DataFrame
    """
    logger.info(f"加载压力量表数据: {excel_path}")
    try:
        df = pd.read_excel(excel_path)
        logger.info(f"成功加载压力量表数据: {len(df)}行, {df.shape[1]}列")
        return df
    except Exception as e:
        logger.error(f"加载压力量表数据失败: {str(e)}")
        return None

def load_fif_data(data_dir, subject_pattern=None):
    """
    加载fif格式的EEG和ECG数据
    
    参数:
    data_dir (str): 数据目录
    subject_pattern (str): 被试模式，如"01_01"，默认为None表示加载所有被试
    
    返回:
    dict: 按被试编号和阶段组织的数据
    """
    logger.info(f"加载fif数据: {data_dir}")
    
    # 查找所有fif文件
    fif_files = glob.glob(os.path.join(data_dir, "*.fif"))
    logger.info(f"找到 {len(fif_files)} 个fif文件")
    
    data_by_subject = {}
    
    for file_path in fif_files:
        file_name = os.path.basename(file_path)
        
        # 如果指定了被试模式，只加载匹配的文件
        if subject_pattern and not file_name.startswith(subject_pattern):
            continue
        
        # 提取被试编号
        parts = file_name.split('_')
        if len(parts) >= 2:
            subject_id = f"{parts[0]}_{parts[1]}"
            
            # 识别阶段 (rest, test, prac)
            stage = None
            if 'rest' in file_name:
                for i in range(1, 4):
                    if f'rest{i}' in file_name:
                        stage = f'rest{i}'
                        break
                if not stage:
                    stage = 'rest1'  # 默认为rest1
            elif 'test' in file_name:
                for i in range(1, 4):
                    if f'test{i}' in file_name:
                        stage = f'test{i}'
                        break
                if not stage:
                    stage = 'test1'  # 默认为test1
            elif 'prac' in file_name:
                stage = 'prac'
            else:
                logger.warning(f"无法识别文件 {file_name} 的阶段")
                continue
            
            try:
                # 加载原始数据
                raw = mne.io.read_raw_fif(file_path, preload=True, verbose=False)
                
                # 识别EEG和ECG通道
                ch_names = raw.ch_names
                eeg_channels = [ch for ch in ch_names if ch.lower() in ['fz', 'cz', 'pz', 'oz'] or 
                               any(ch.lower().startswith(prefix) for prefix in ['eeg', 'fp', 'f', 'c', 'p', 'o', 't'])]
                ecg_channels = [ch for ch in ch_names if 'ecg' in ch.lower()]
                
                if not eeg_channels or not ecg_channels:
                    logger.warning(f"文件 {file_name} 缺少EEG或ECG通道，跳过")
                    continue
                
                # 提取数据
                eeg_data = raw.get_data(picks=eeg_channels)
                ecg_data = raw.get_data(picks=ecg_channels)
                
                # 初始化被试数据字典
                if subject_id not in data_by_subject:
                    data_by_subject[subject_id] = {}
                
                # 保存阶段数据
                data_by_subject[subject_id][stage] = {
                    'eeg': eeg_data,
                    'ecg': ecg_data,
                    'sfreq': raw.info['sfreq'],
                    'eeg_ch_names': eeg_channels,
                    'ecg_ch_names': ecg_channels
                }
                
                logger.info(f"加载文件 {file_name}: 被试={subject_id}, 阶段={stage}, EEG通道={len(eeg_channels)}, ECG通道={len(ecg_channels)}")
                
            except Exception as e:
                logger.error(f"加载文件 {file_name} 失败: {str(e)}")
    
    logger.info(f"成功加载数据: {len(data_by_subject)}个被试")
    return data_by_subject

def load_h5_epochs_data(h5_dir, subject_pattern=None):
    """
    加载H5格式的epochs数据
    
    参数:
    h5_dir (str): H5文件目录
    subject_pattern (str): 被试模式，如"01"，默认为None表示加载所有被试
    
    返回:
    dict: 按被试编号和阶段组织的数据
    """
    logger.info(f"从H5文件加载epochs数据: {h5_dir}")
    
    # 所有阶段文件路径
    h5_files = {
        'prac': os.path.join(h5_dir, "prac_raw_epochs_20250519_015707.h5"),
        'rest1': os.path.join(h5_dir, "rest1_raw_epochs_20250519_015731.h5"),
        'test1': os.path.join(h5_dir, "test1_raw_epochs_20250519_015828.h5"),
        'rest2': os.path.join(h5_dir, "rest2_raw_epochs_20250519_020005.h5"),
        'test2': os.path.join(h5_dir, "test2_raw_epochs_20250519_020103.h5"),
        'rest3': os.path.join(h5_dir, "rest3_raw_epochs_20250519_020228.h5"),
        'test3': os.path.join(h5_dir, "test3_raw_epochs_20250519_020327.h5")
    }
    
    data_by_subject = {}
    
    try:
        import h5py
        from collections import defaultdict
        
        # 处理每个阶段的数据
        for stage, file_path in h5_files.items():
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                continue
                
            logger.info(f"加载文件: {file_path}")
            
            with h5py.File(file_path, 'r') as f:
                # 检查文件结构
                if 'subject_ids' not in f or 'data' not in f or 'ch_names' not in f:
                    logger.warning(f"文件 {file_path} 缺少必要的数据集")
                    continue
                
                # 获取被试ID、数据和通道名称
                subject_ids = f['subject_ids'][:]
                data = f['data'][:]
                ch_names = f['ch_names'][:]
                
                # 转换字节字符串为普通字符串
                decoded_subject_ids = []
                for sid in subject_ids:
                    if isinstance(sid, bytes):
                        decoded_subject_ids.append(sid.decode('utf-8'))
                    else:
                        decoded_subject_ids.append(str(sid))
                subject_ids = decoded_subject_ids
                
                decoded_ch_names = []
                for name in ch_names:
                    if isinstance(name, bytes):
                        decoded_ch_names.append(name.decode('utf-8'))
                    else:
                        decoded_ch_names.append(str(name))
                ch_names = decoded_ch_names
                
                # 找出EEG和ECG通道的索引
                eeg_indices = [i for i, name in enumerate(ch_names) if not name.startswith('ECG')]
                ecg_indices = [i for i, name in enumerate(ch_names) if name.startswith('ECG')]
                
                eeg_ch_names = [ch_names[i] for i in eeg_indices]
                ecg_ch_names = [ch_names[i] for i in ecg_indices]
                
                # 如果指定了被试模式，只处理匹配的被试
                unique_subject_ids = list(np.unique(subject_ids))
                if subject_pattern:
                    unique_subject_ids = [sid for sid in unique_subject_ids if subject_pattern in sid]
                
                if len(unique_subject_ids) == 0:
                    logger.warning(f"在文件 {file_path} 中未找到匹配的被试")
                    continue
                
                # 处理每个被试的数据
                for sid in unique_subject_ids:
                    # 找出该被试的所有epochs
                    subject_mask = np.array(subject_ids) == sid
                    subject_data = data[subject_mask]
                    
                    if len(subject_data) == 0:
                        logger.warning(f"被试 {sid} 在 {stage} 阶段没有数据")
                        continue
                    
                    # 分离EEG和ECG数据
                    eeg_data = subject_data[:, eeg_indices, :]
                    ecg_data = subject_data[:, ecg_indices, :]
                    
                    # 重塑数据形状为(通道, 时间点)，合并所有epochs
                    eeg_data = np.transpose(eeg_data, (1, 0, 2)).reshape(len(eeg_indices), -1)
                    ecg_data = np.transpose(ecg_data, (1, 0, 2)).reshape(len(ecg_indices), -1)
                    
                    # 存储数据
                    if sid not in data_by_subject:
                        data_by_subject[sid] = {}
                    
                    data_by_subject[sid][stage] = {
                        'eeg': eeg_data,
                        'ecg': ecg_data,
                        'sfreq': 500.0,  # 假设采样率为500Hz
                        'eeg_ch_names': eeg_ch_names,
                        'ecg_ch_names': ecg_ch_names
                    }
                    
                    logger.info(f"加载被试 {sid} 的 {stage} 阶段数据: EEG形状={eeg_data.shape}, ECG形状={ecg_data.shape}")
        
        logger.info(f"成功加载数据: {len(data_by_subject)}个被试")
        
    except Exception as e:
        logger.error(f"加载H5数据失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    
    return data_by_subject

def extract_features_from_raw(data, sfreq, feature_type='time'):
    """
    从原始数据中提取特征
    
    参数:
    data (np.ndarray): 原始数据，形状为 (n_channels, n_times)
    sfreq (float): 采样频率
    feature_type (str): 特征类型，'time' 或 'frequency'
    
    返回:
    np.ndarray: 特征矩阵，形状为 (n_windows, n_features)
    """
    n_channels, n_times = data.shape
    
    # 定义窗口大小和步长
    window_size = int(2 * sfreq)  # 2秒窗口
    step_size = int(1 * sfreq)    # 1秒步长
    
    # 计算窗口数量
    n_windows = (n_times - window_size) // step_size + 1
    
    if feature_type == 'time':
        # 时域特征：均值、标准差、最小值、最大值、范围、RMS
        n_features = 6
        features = np.zeros((n_windows, n_channels * n_features))
        
        for i in range(n_windows):
            start = i * step_size
            end = start + window_size
            window_data = data[:, start:end]
            
            # 对每个通道提取特征
            for j in range(n_channels):
                channel_data = window_data[j]
                feature_idx = j * n_features
                
                features[i, feature_idx] = np.mean(channel_data)
                features[i, feature_idx + 1] = np.std(channel_data)
                features[i, feature_idx + 2] = np.min(channel_data)
                features[i, feature_idx + 3] = np.max(channel_data)
                features[i, feature_idx + 4] = np.max(channel_data) - np.min(channel_data)
                features[i, feature_idx + 5] = np.sqrt(np.mean(channel_data**2))
        
    elif feature_type == 'frequency':
        # 频域特征：各频段能量（delta, theta, alpha, beta, gamma）
        bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, min(sfreq/2, 45))
        }
        n_features = len(bands)
        features = np.zeros((n_windows, n_channels * n_features))
        
        for i in range(n_windows):
            start = i * step_size
            end = start + window_size
            window_data = data[:, start:end]
            
            # 对每个通道计算频谱
            for j in range(n_channels):
                channel_data = window_data[j]
                
                # 应用窗函数
                windowed_data = channel_data * np.hanning(window_size)
                
                # 计算FFT
                fft_result = np.fft.rfft(windowed_data)
                fft_freqs = np.fft.rfftfreq(window_size, 1/sfreq)
                power = np.abs(fft_result)**2
                
                # 计算各频段能量
                for k, (band_name, (low_freq, high_freq)) in enumerate(bands.items()):
                    freq_mask = (fft_freqs >= low_freq) & (fft_freqs <= high_freq)
                    band_power = np.sum(power[freq_mask])
                    features[i, j * n_features + k] = band_power
    
    else:
        raise ValueError(f"未知的特征类型: {feature_type}")
    
    return features

def prepare_stage_data(data_by_subject, include_prac=False):
    """
    准备分阶段的数据
    
    参数:
    data_by_subject (dict): 按被试编号和阶段组织的数据
    include_prac (bool): 是否包含练习阶段数据
    
    返回:
    dict: 按阶段组织的特征数据
    """
    logger.info(f"准备分阶段数据: 被试数量={len(data_by_subject)}")
    
    # 定义要包含的阶段
    stages_to_include = STAGE_ORDER.copy()
    if include_prac:
        stages_to_include = ['prac'] + stages_to_include
    
    # 初始化特征数据字典
    feature_data = {stage: {'eeg_time': [], 'ecg_time': [], 'eeg_freq': [], 'ecg_freq': []} for stage in stages_to_include}
    
    # 处理每个被试的数据
    for subject_id, subject_data in data_by_subject.items():
        for stage in stages_to_include:
            if stage in subject_data:
                # 提取EEG和ECG原始数据
                eeg_data = subject_data[stage]['eeg']
                ecg_data = subject_data[stage]['ecg']
                sfreq = subject_data[stage]['sfreq']
                
                # 提取时域特征
                eeg_time_features = extract_features_from_raw(eeg_data, sfreq, 'time')
                ecg_time_features = extract_features_from_raw(ecg_data, sfreq, 'time')
                
                # 提取频域特征
                eeg_freq_features = extract_features_from_raw(eeg_data, sfreq, 'frequency')
                ecg_freq_features = extract_features_from_raw(ecg_data, sfreq, 'frequency')
                
                # 添加到特征数据字典
                feature_data[stage]['eeg_time'].append(eeg_time_features)
                feature_data[stage]['ecg_time'].append(ecg_time_features)
                feature_data[stage]['eeg_freq'].append(eeg_freq_features)
                feature_data[stage]['ecg_freq'].append(ecg_freq_features)
    
    # 合并所有被试的特征数据
    for stage in stages_to_include:
        for feature_type in ['eeg_time', 'ecg_time', 'eeg_freq', 'ecg_freq']:
            if feature_data[stage][feature_type]:
                # 使用最小长度合并，避免维度不匹配问题
                min_length = min(feat.shape[0] for feat in feature_data[stage][feature_type])
                feature_data[stage][feature_type] = np.vstack([feat[:min_length] for feat in feature_data[stage][feature_type]])
            else:
                logger.warning(f"阶段 {stage} 的 {feature_type} 特征为空")
                feature_data[stage][feature_type] = np.array([])
    
    return feature_data

def create_pressure_trend_dataset(feature_data, sequence_length=3):
    """
    创建压力趋势预测数据集
    
    参数:
    feature_data (dict): 按阶段组织的特征数据
    sequence_length (int): 序列长度
    
    返回:
    tuple: (eeg_combined, ecg_combined, pressure_labels)
    """
    logger.info(f"创建压力趋势预测数据集: 序列长度={sequence_length}")
    
    # 从feature_data中提取所有阶段的数据
    eeg_features = []
    ecg_features = []
    stage_labels = []
    sample_counts = []
    
    for stage in STAGE_ORDER:
        if stage in feature_data:
            # 合并时域和频域特征
            if len(feature_data[stage]['eeg_time']) > 0 and len(feature_data[stage]['eeg_freq']) > 0:
                eeg_time = feature_data[stage]['eeg_time']
                eeg_freq = feature_data[stage]['eeg_freq']
                # 截断到相同长度
                min_length = min(eeg_time.shape[0], eeg_freq.shape[0])
                eeg_combined = np.hstack([eeg_time[:min_length], eeg_freq[:min_length]])
                
                ecg_time = feature_data[stage]['ecg_time']
                ecg_freq = feature_data[stage]['ecg_freq']
                # 截断到相同长度
                min_length = min(ecg_time.shape[0], ecg_freq.shape[0], eeg_combined.shape[0])
                ecg_combined = np.hstack([ecg_time[:min_length], ecg_freq[:min_length]])
                eeg_combined = eeg_combined[:min_length]
                
                # 创建阶段标签
                stage_label = np.full(min_length, PRESSURE_PATTERN[stage])
                
                eeg_features.append(eeg_combined)
                ecg_features.append(ecg_combined)
                stage_labels.append(stage_label)
                sample_counts.append(min_length)
    
    # 标准化特征
    eeg_combined = np.vstack(eeg_features)
    ecg_combined = np.vstack(ecg_features)
    pressure_labels = np.hstack(stage_labels).reshape(-1, 1)
    
    eeg_scaler = StandardScaler()
    ecg_scaler = StandardScaler()
    pressure_scaler = MinMaxScaler()
    
    eeg_combined = eeg_scaler.fit_transform(eeg_combined)
    ecg_combined = ecg_scaler.fit_transform(ecg_combined)
    pressure_labels = pressure_scaler.fit_transform(pressure_labels)
    
    logger.info(f"数据集构建完成: EEG特征={eeg_combined.shape}, ECG特征={ecg_combined.shape}, 压力标签={pressure_labels.shape}")
    
    return eeg_combined, ecg_combined, pressure_labels, sample_counts

def visualize_pressure_pattern(output_dir=None):
    """
    可视化压力变化模式
    
    参数:
    output_dir (str): 输出目录
    
    返回:
    str: 图像保存路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\pressure_trend_analysis")
    
    os.makedirs(output_dir, exist_ok=True)
    
    stages = STAGE_ORDER
    pressure_values = [PRESSURE_PATTERN[stage] for stage in stages]
    change_rates = [PRESSURE_CHANGE_RATE[stage] for stage in stages]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
    
    # 压力水平
    ax1.plot(stages, pressure_values, 'bo-', linewidth=2, markersize=10)
    ax1.set_ylabel('压力水平 (归一化)', fontproperties=chinese_font, fontsize=10)
    ax1.set_title('实验阶段压力变化模式', fontproperties=chinese_font, fontsize=12)
    ax1.grid(True)
    
    # 添加标注
    for i, txt in enumerate(pressure_values):
        ax1.annotate(f"{txt:.1f}", (i, pressure_values[i]), 
                     textcoords="offset points", xytext=(0,10), ha='center',
                     fontproperties=chinese_font, fontsize=10)
    
    # 压力变化率
    ax2.bar(stages, change_rates, color='lightcoral')
    ax2.set_ylabel('压力变化率', fontproperties=chinese_font, fontsize=10)
    ax2.set_xlabel('实验阶段', fontproperties=chinese_font, fontsize=10)
    ax2.grid(True, axis='y')
    
    # 添加标注
    for i, txt in enumerate(change_rates):
        ax2.annotate(f"{txt:.1f}", (i, change_rates[i]), 
                     textcoords="offset points", xytext=(0,5 if change_rates[i] >= 0 else -15), ha='center',
                     fontproperties=chinese_font, fontsize=10)
    
    plt.tight_layout()
    
    output_file = os.path.join(output_dir, "pressure_pattern.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"压力变化模式可视化已保存到: {output_file}")
    
    return output_file

def generate_synthetic_data(data_by_subject, subject_id):
    """
    生成模拟数据，填充缺失的实验阶段
    
    参数:
    data_by_subject (dict): 按被试编号和阶段组织的数据
    subject_id (str): 被试ID
    
    返回:
    dict: 包含真实和模拟数据的字典
    """
    logger.info(f"为被试 {subject_id} 生成模拟数据以填充缺失阶段")
    
    if subject_id not in data_by_subject:
        logger.error(f"被试 {subject_id} 不存在于数据集中")
        return data_by_subject
    
    # 获取已有阶段
    existing_stages = list(data_by_subject[subject_id].keys())
    logger.info(f"被试 {subject_id} 已有阶段: {existing_stages}")
    
    # 检查缺失阶段
    missing_stages = [stage for stage in STAGE_ORDER if stage not in existing_stages]
    logger.info(f"被试 {subject_id} 缺失阶段: {missing_stages}")
    
    if not missing_stages:
        logger.info(f"被试 {subject_id} 没有缺失阶段")
        return data_by_subject
    
    # 获取模板阶段数据
    template_stage = existing_stages[0]
    template_data = data_by_subject[subject_id][template_stage]
    
    # 为缺失阶段生成模拟数据
    for stage in missing_stages:
        # 复制模板数据结构
        data_by_subject[subject_id][stage] = {
            'eeg': template_data['eeg'].copy(),
            'ecg': template_data['ecg'].copy(),
            'sfreq': template_data['sfreq'],
            'eeg_ch_names': template_data['eeg_ch_names'].copy(),
            'ecg_ch_names': template_data['ecg_ch_names'].copy()
        }
        
        # 根据压力模式调整数据
        # 这里使用简单的缩放方法，根据PRESSURE_PATTERN调整信号幅度
        scale_factor = PRESSURE_PATTERN[stage] / PRESSURE_PATTERN[template_stage]
        
        # 对EEG数据应用缩放和随机变化
        noise_level = 0.1  # 添加10%的随机噪声
        random_state = np.random.RandomState(hash(stage) % 2**32)  # 使用阶段名称作为随机种子
        noise = random_state.normal(0, noise_level, data_by_subject[subject_id][stage]['eeg'].shape)
        data_by_subject[subject_id][stage]['eeg'] = data_by_subject[subject_id][stage]['eeg'] * scale_factor + noise
        
        # 对ECG数据应用缩放和随机变化
        # ECG数据的变化应该更加符合生理特性，所以使用不同的缩放方法
        # 这里我们假设心率随压力增加而增加，通过改变R波间隔来模拟
        ecg_scale = 1.0 + (PRESSURE_PATTERN[stage] - PRESSURE_PATTERN[template_stage]) * 0.3
        ecg_noise = random_state.normal(0, noise_level * 0.5, data_by_subject[subject_id][stage]['ecg'].shape)
        data_by_subject[subject_id][stage]['ecg'] = data_by_subject[subject_id][stage]['ecg'] * ecg_scale + ecg_noise
        
        logger.info(f"已为阶段 {stage} 生成模拟数据，缩放因子: EEG={scale_factor:.2f}, ECG={ecg_scale:.2f}")
    
    return data_by_subject

def main():
    """主函数"""
    # 可视化压力变化模式
    visualize_pressure_pattern()
    
    # 数据目录
    data_dir = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
    
    # 加载数据
    data_by_subject = load_h5_epochs_data(data_dir)
    
    # 准备分阶段数据
    feature_data = prepare_stage_data(data_by_subject)
    
    # 创建压力趋势预测数据集
    eeg_combined, ecg_combined, pressure_labels, _ = create_pressure_trend_dataset(feature_data)
    
    logger.info("压力趋势数据准备完成!")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 特征提取模块

功能：
- 从分段后的EEG和ECG数据中提取时域和频域特征
- 支持多种特征提取方法
- 提供特征质量评估和可视化功能

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
from scipy import stats, signal
import seaborn as sns
from matplotlib.gridspec import GridSpec
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_feature_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-FeatureExtraction")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

class FeatureExtractionError(Exception):
    """特征提取错误类"""
    pass

# 时域特征提取函数
def extract_time_domain_features(data, sampling_rate=500):
    """
    提取时域特征
    
    参数:
    data (np.ndarray): 输入数据，形状为 (segments, channels, time_points)
    sampling_rate (int): 采样率，默认为500Hz
    
    返回:
    dict: 时域特征字典，每个特征的形状为 (segments, channels)
          - 'mean': 均值，反映信号的直流分量
          - 'std': 标准差，反映信号的变异性
          - 'min': 最小值，反映信号的下限
          - 'max': 最大值，反映信号的上限
          - 'range': 范围（最大值-最小值），反映信号的振幅
          - 'rms': 均方根值，反映信号的能量
          - 'skewness': 偏度，反映信号分布的不对称性
          - 'kurtosis': 峰度，反映信号分布的尖锐程度
          - 'energy': 能量，信号平方和
          - 'zero_crossings': 过零点数，反映信号的频率特性
          - 'hjorth_activity': Hjorth活动度，反映信号的方差
          - 'hjorth_mobility': Hjorth移动度，反映信号频谱的平均频率
          - 'hjorth_complexity': Hjorth复杂度，反映频谱的变化
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为 np.ndarray"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为 (segments, channels, time_points)"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    segments, channels, time_points = data.shape
    
    logger.info(f"提取时域特征: 数据形状 {data.shape}, 采样率 {sampling_rate}Hz")
    logger.info(f"将提取 {segments} 个片段, {channels} 个通道的13种时域特征")
    
    # 初始化特征字典
    features = {
        'mean': np.zeros((segments, channels)),          # 均值: (segments, channels)
        'std': np.zeros((segments, channels)),           # 标准差: (segments, channels)
        'min': np.zeros((segments, channels)),           # 最小值: (segments, channels)
        'max': np.zeros((segments, channels)),           # 最大值: (segments, channels)
        'range': np.zeros((segments, channels)),         # 范围: (segments, channels)
        'rms': np.zeros((segments, channels)),           # 均方根: (segments, channels)
        'skewness': np.zeros((segments, channels)),      # 偏度: (segments, channels)
        'kurtosis': np.zeros((segments, channels)),      # 峰度: (segments, channels)
        'energy': np.zeros((segments, channels)),        # 能量: (segments, channels)
        'zero_crossings': np.zeros((segments, channels)),# 过零点数: (segments, channels)
        'hjorth_activity': np.zeros((segments, channels)),    # Hjorth活动度: (segments, channels)
        'hjorth_mobility': np.zeros((segments, channels)),    # Hjorth移动度: (segments, channels)
        'hjorth_complexity': np.zeros((segments, channels))   # Hjorth复杂度: (segments, channels)
    }
    
    # 计算特征
    for seg in range(segments):
        for ch in range(channels):
            signal_data = data[seg, ch, :]
            
            # 基本统计特征
            features['mean'][seg, ch] = np.mean(signal_data)              # 均值：直流分量
            features['std'][seg, ch] = np.std(signal_data)                # 标准差：信号变异性
            features['min'][seg, ch] = np.min(signal_data)                # 最小值
            features['max'][seg, ch] = np.max(signal_data)                # 最大值
            features['range'][seg, ch] = np.max(signal_data) - np.min(signal_data)  # 范围：峰峰值
            features['rms'][seg, ch] = np.sqrt(np.mean(np.square(signal_data)))  # 均方根值：信号能量指标
            features['skewness'][seg, ch] = stats.skew(signal_data)       # 偏度：分布不对称性
            features['kurtosis'][seg, ch] = stats.kurtosis(signal_data)   # 峰度：分布尖锐度
            features['energy'][seg, ch] = np.sum(np.square(signal_data))  # 能量：信号平方和
            
            # 过零点数：信号穿越零点的次数，与频率相关
            zero_crossings = np.where(np.diff(np.signbit(signal_data)))[0]
            features['zero_crossings'][seg, ch] = len(zero_crossings)
            
            # Hjorth参数
            # 活动度 - 信号方差，反映信号功率
            activity = np.var(signal_data)
            features['hjorth_activity'][seg, ch] = activity
            
            # 移动度 - 信号一阶导数方差与信号方差的比值的平方根，反映平均频率
            diff1 = np.diff(signal_data)
            mobility = np.sqrt(np.var(diff1) / activity) if activity > 0 else 0
            features['hjorth_mobility'][seg, ch] = mobility
            
            # 复杂度 - 信号二阶导数的移动度与一阶导数移动度的比值，反映频谱带宽
            diff2 = np.diff(diff1)
            mobility_diff = np.sqrt(np.var(diff2) / np.var(diff1)) if np.var(diff1) > 0 else 0
            features['hjorth_complexity'][seg, ch] = mobility_diff / mobility if mobility > 0 else 0
    
    logger.info(f"时域特征提取完成: {len(features)}个特征，每个特征形状为({segments}, {channels})")
    return features

# 频域特征提取函数
def extract_frequency_domain_features(data, sampling_rate=500):
    """
    提取频域特征
    
    参数:
    data (np.ndarray): 输入数据，形状为 (segments, channels, time_points)
    sampling_rate (int): 采样率，默认为500Hz
    
    返回:
    dict: 频域特征字典
          - 'delta_power': delta频段(0.5-4 Hz)功率，形状为 (segments, channels)
          - 'theta_power': theta频段(4-8 Hz)功率，形状为 (segments, channels)
          - 'alpha_power': alpha频段(8-13 Hz)功率，形状为 (segments, channels)
          - 'beta_power': beta频段(13-30 Hz)功率，形状为 (segments, channels)
          - 'gamma_power': gamma频段(30-100 Hz)功率，形状为 (segments, channels)
          - 'total_power': 总功率，形状为 (segments, channels)
          - 'spectral_edge': 谱边缘频率，形状为 (segments, channels)
          - 'spectral_entropy': 谱熵，形状为 (segments, channels)
          - 'dominant_frequency': 主频，形状为 (segments, channels)
          - 'median_frequency': 中值频率，形状为 (segments, channels)
          - 'band_power_ratio': 各频段功率比，形状为 (segments, channels, 4)
                               [0]: delta/total, [1]: theta/total, [2]: alpha/total, [3]: beta/total
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为 np.ndarray"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为 (segments, channels, time_points)"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    segments, channels, time_points = data.shape
    
    logger.info(f"提取频域特征: 数据形状 {data.shape}, 采样率 {sampling_rate}Hz")
    logger.info(f"将提取 {segments} 个片段, {channels} 个通道的11种频域特征")
    
    # 初始化特征字典
    features = {
        'delta_power': np.zeros((segments, channels)),     # delta功率: (segments, channels)
        'theta_power': np.zeros((segments, channels)),     # theta功率: (segments, channels)
        'alpha_power': np.zeros((segments, channels)),     # alpha功率: (segments, channels)
        'beta_power': np.zeros((segments, channels)),      # beta功率: (segments, channels)
        'gamma_power': np.zeros((segments, channels)),     # gamma功率: (segments, channels)
        'total_power': np.zeros((segments, channels)),     # 总功率: (segments, channels)
        'spectral_edge': np.zeros((segments, channels)),   # 谱边缘频率: (segments, channels)
        'spectral_entropy': np.zeros((segments, channels)),# 谱熵: (segments, channels)
        'dominant_frequency': np.zeros((segments, channels)), # 主频: (segments, channels)
        'median_frequency': np.zeros((segments, channels)),   # 中值频率: (segments, channels)
        'band_power_ratio': np.zeros((segments, channels, 4)) # 频段功率比: (segments, channels, 4)
    }
    
    # 计算特征
    for seg in range(segments):
        for ch in range(channels):
            signal_data = data[seg, ch, :]
            
            # 计算功率谱密度
            freqs, psd = signal.welch(signal_data, fs=sampling_rate, nperseg=min(256, time_points))
            
            # 计算各频段功率
            delta_idx = np.logical_and(freqs >= 0.5, freqs < 4)     # delta: 0.5-4 Hz
            theta_idx = np.logical_and(freqs >= 4, freqs < 8)       # theta: 4-8 Hz
            alpha_idx = np.logical_and(freqs >= 8, freqs < 13)      # alpha: 8-13 Hz
            beta_idx = np.logical_and(freqs >= 13, freqs < 30)      # beta: 13-30 Hz
            gamma_idx = np.logical_and(freqs >= 30, freqs <= 100)   # gamma: 30-100 Hz
            
            # 计算各频段功率（频谱能量密度的积分）
            delta_power = np.sum(psd[delta_idx])   # delta频段功率
            theta_power = np.sum(psd[theta_idx])   # theta频段功率
            alpha_power = np.sum(psd[alpha_idx])   # alpha频段功率
            beta_power = np.sum(psd[beta_idx])     # beta频段功率
            gamma_power = np.sum(psd[gamma_idx])   # gamma频段功率
            total_power = np.sum(psd)              # 总功率
            
            features['delta_power'][seg, ch] = delta_power
            features['theta_power'][seg, ch] = theta_power
            features['alpha_power'][seg, ch] = alpha_power
            features['beta_power'][seg, ch] = beta_power
            features['gamma_power'][seg, ch] = gamma_power
            features['total_power'][seg, ch] = total_power
            
            # 计算频段功率比（各频段功率占总功率的比例）
            if total_power > 0:
                features['band_power_ratio'][seg, ch, 0] = delta_power / total_power  # delta功率比
                features['band_power_ratio'][seg, ch, 1] = theta_power / total_power  # theta功率比
                features['band_power_ratio'][seg, ch, 2] = alpha_power / total_power  # alpha功率比
                features['band_power_ratio'][seg, ch, 3] = beta_power / total_power   # beta功率比
            
            # 计算主频（功率谱最大值对应的频率）
            if len(psd) > 0:
                features['dominant_frequency'][seg, ch] = freqs[np.argmax(psd)]
            
            # 计算中值频率（累积功率达到总功率一半时的频率）
            if total_power > 0:
                cumsum_psd = np.cumsum(psd)
                median_idx = np.where(cumsum_psd >= total_power / 2)[0]
                if len(median_idx) > 0:
                    features['median_frequency'][seg, ch] = freqs[median_idx[0]]
            
            # 计算谱边缘频率 (95%)（累积功率达到总功率95%时的频率）
            if total_power > 0:
                cumsum_psd = np.cumsum(psd) / total_power
                edge_idx = np.where(cumsum_psd >= 0.95)[0]
                if len(edge_idx) > 0:
                    features['spectral_edge'][seg, ch] = freqs[edge_idx[0]]
            
            # 计算谱熵（频谱的不确定性度量，反映信号的复杂度）
            if total_power > 0:
                psd_norm = psd / total_power
                psd_norm = psd_norm[psd_norm > 0]  # 避免log(0)
                features['spectral_entropy'][seg, ch] = -np.sum(psd_norm * np.log2(psd_norm))
    
    logger.info(f"频域特征提取完成: {len(features)}个特征")
    logger.info(f"  - 大多数特征形状为({segments}, {channels})")
    logger.info(f"  - 频段功率比形状为({segments}, {channels}, 4)")
    return features

# 非线性特征提取函数
def extract_nonlinear_features(data, sampling_rate=500):
    """
    提取非线性特征
    
    参数:
    data (np.ndarray): 输入数据，形状为 (segments, channels, time_points)
    sampling_rate (int): 采样率，默认为500Hz
    
    返回:
    dict: 非线性特征字典
          - 'sample_entropy': 样本熵，反映时间序列的复杂度，形状为 (segments, channels)
          - 'approximate_entropy': 近似熵，衡量时间序列的规律性，形状为 (segments, channels)
          - 'perm_entropy': 排列熵，基于序列模式的熵测量，形状为 (segments, channels)
          - 'lzc': Lempel-Ziv复杂度，基于序列压缩的复杂度测量，形状为 (segments, channels)
          - 'hurst': Hurst指数，衡量时间序列的长期记忆性，形状为 (segments, channels)
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为 np.ndarray"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为 (segments, channels, time_points)"
        logger.error(error_msg)
        raise FeatureExtractionError(error_msg)
    
    segments, channels, time_points = data.shape
    
    logger.info(f"提取非线性特征: 数据形状 {data.shape}, 采样率 {sampling_rate}Hz")
    logger.info(f"将提取 {segments} 个片段, {channels} 个通道的5种非线性特征")
    
    # 初始化特征字典
    features = {
        'sample_entropy': np.zeros((segments, channels)),      # 样本熵: (segments, channels)
        'approximate_entropy': np.zeros((segments, channels)), # 近似熵: (segments, channels)
        'perm_entropy': np.zeros((segments, channels)),        # 排列熵: (segments, channels)
        'lzc': np.zeros((segments, channels)),                 # Lempel-Ziv复杂度: (segments, channels)
        'hurst': np.zeros((segments, channels))                # Hurst指数: (segments, channels)
    }
    
    # 计算特征
    for seg in range(segments):
        for ch in range(channels):
            signal_data = data[seg, ch, :]
            
            # 标准化信号以改进熵测量
            signal_norm = (signal_data - np.mean(signal_data)) / (np.std(signal_data) if np.std(signal_data) > 0 else 1)
            
            # 样本熵（Sample Entropy）- 衡量时间序列的复杂性
            try:
                # m=2, r=0.2*std
                r = 0.2 * np.std(signal_norm)
                count1 = 0
                count2 = 0
                
                # 计算m=2的匹配数
                for i in range(time_points - 2):
                    for j in range(i + 1, time_points - 2):
                        if abs(signal_norm[i] - signal_norm[j]) < r and abs(signal_norm[i+1] - signal_norm[j+1]) < r:
                            count1 += 1
                            if abs(signal_norm[i+2] - signal_norm[j+2]) < r:
                                count2 += 1
                
                if count1 > 0 and count2 > 0:
                    features['sample_entropy'][seg, ch] = -np.log(count2 / count1)
                else:
                    features['sample_entropy'][seg, ch] = 0
            except Exception as e:
                logger.warning(f"计算样本熵时出错: {str(e)}")
                features['sample_entropy'][seg, ch] = 0
            
            # 近似熵（Approximate Entropy）- 衡量时间序列的规律性
            try:
                # m=2, r=0.2*std
                r = 0.2 * np.std(signal_norm)
                count1 = np.zeros(time_points - 1)
                count2 = np.zeros(time_points - 2)
                
                # 计算m=1的phi
                for i in range(time_points - 1):
                    count = 0
                    for j in range(time_points - 1):
                        if abs(signal_norm[i] - signal_norm[j]) < r:
                            count += 1
                    count1[i] = count / (time_points - 1)
                phi1 = np.sum(np.log(count1[count1 > 0])) / (time_points - 1)
                
                # 计算m=2的phi
                for i in range(time_points - 2):
                    count = 0
                    for j in range(time_points - 2):
                        if (abs(signal_norm[i] - signal_norm[j]) < r and 
                            abs(signal_norm[i+1] - signal_norm[j+1]) < r):
                            count += 1
                    count2[i] = count / (time_points - 2)
                phi2 = np.sum(np.log(count2[count2 > 0])) / (time_points - 2)
                
                features['approximate_entropy'][seg, ch] = phi1 - phi2
            except Exception as e:
                logger.warning(f"计算近似熵时出错: {str(e)}")
                features['approximate_entropy'][seg, ch] = 0
            
            # 排列熵（Permutation Entropy）- 基于排列模式的熵
            try:
                order = 3  # 排列阶数
                delay = 1  # 延迟
                
                pattern_count = {}
                for i in range(time_points - (order - 1) * delay):
                    # 提取长度为order的模式
                    pattern = signal_norm[i:i + order * delay:delay].argsort()
                    pattern_str = ''.join(map(str, pattern))
                    
                    # 计数
                    if pattern_str in pattern_count:
                        pattern_count[pattern_str] += 1
                    else:
                        pattern_count[pattern_str] = 1
                
                # 计算每个模式的概率
                total_patterns = time_points - (order - 1) * delay
                probabilities = np.array(list(pattern_count.values())) / total_patterns
                
                # 计算熵
                features['perm_entropy'][seg, ch] = -np.sum(probabilities * np.log2(probabilities))
            except Exception as e:
                logger.warning(f"计算排列熵时出错: {str(e)}")
                features['perm_entropy'][seg, ch] = 0
            
            # Lempel-Ziv复杂度（Lempel-Ziv Complexity）- 基于序列压缩的复杂度
            try:
                # 将信号二值化
                binary_signal = np.zeros(time_points, dtype=int)
                binary_signal[signal_norm > np.median(signal_norm)] = 1
                
                # 压缩复杂度计算
                i, c = 0, 1
                complexity = 1
                
                while i + c <= time_points:
                    # 当前窗口
                    current = ''.join(map(str, binary_signal[i:i+c]))
                    
                    # 如果当前窗口已经在历史中找到
                    if i + c < time_points and current in ''.join(map(str, binary_signal[0:i])):
                        c += 1
                    else:
                        # 新的子序列
                        i += c
                        complexity += 1
                        c = 1
                
                # 归一化
                features['lzc'][seg, ch] = complexity * np.log(time_points) / (time_points)
            except Exception as e:
                logger.warning(f"计算Lempel-Ziv复杂度时出错: {str(e)}")
                features['lzc'][seg, ch] = 0
            
            # Hurst指数 - 衡量时间序列的长期记忆性
            try:
                # 使用R/S方法估计Hurst指数
                lags = [2, 4, 8, 16, 32]  # 延迟窗口
                rs_values = []
                
                for lag in lags:
                    if lag >= time_points:
                        continue
                    
                    # 分段
                    n_segments = time_points // lag
                    
                    if n_segments == 0:
                        continue
                    
                    rs_segment = []
                    
                    # 对每个分段计算R/S
                    for i in range(n_segments):
                        segment = signal_norm[i*lag:(i+1)*lag]
                        mean_segment = np.mean(segment)
                        # 计算累积离差序列
                        cum_dev = np.cumsum(segment - mean_segment)
                        # R: 最大累积离差 - 最小累积离差
                        r = np.max(cum_dev) - np.min(cum_dev)
                        # S: 标准差
                        s = np.std(segment)
                        
                        if s > 0:
                            rs_segment.append(r/s)
                    
                    if rs_segment:
                        rs_values.append(np.mean(rs_segment))
                
                if len(rs_values) > 1 and len(lags) > 1:
                    # 取对数后用线性回归估计Hurst指数
                    log_lags = np.log(lags[:len(rs_values)])
                    log_rs = np.log(rs_values)
                    
                    slope, _, _, _, _ = stats.linregress(log_lags, log_rs)
                    features['hurst'][seg, ch] = slope
                else:
                    features['hurst'][seg, ch] = 0.5  # 默认值，表示无相关性
            except Exception as e:
                logger.warning(f"计算Hurst指数时出错: {str(e)}")
                features['hurst'][seg, ch] = 0.5
    
    logger.info(f"非线性特征提取完成: {len(features)}个特征, 每个特征形状为({segments}, {channels})")
    return features

# 主要特征提取函数
def extract_features(segmented_data, feature_types=None, sampling_rate=500):
    """
    从分段数据中提取特征
    
    参数:
    segmented_data (dict): 分段后的数据字典
    feature_types (list): 要提取的特征类型列表，可选 'time', 'frequency', 'nonlinear', 'connectivity'
    sampling_rate (int): 采样率，默认为500Hz
    
    返回:
    dict: 特征数据字典
    """
    if feature_types is None:
        feature_types = ['time', 'frequency']
    
    logger.info(f"从分段数据中提取特征: 特征类型 {feature_types}")
    
    features_data = {}
    
    for stage, stage_data in segmented_data.items():
        logger.info(f"处理阶段 {stage} 的数据...")
        
        eeg_data = stage_data['eeg']
        ecg_data = stage_data['ecg']
        sr = stage_data.get('sampling_rate', sampling_rate)
        
        eeg_features = {}
        ecg_features = {}
        
        for feature_type in feature_types:
            logger.info(f"  - 提取 {feature_type} 特征...")
            
            if feature_type == 'time':
                eeg_features['time'] = extract_time_domain_features(eeg_data, sr)
                ecg_features['time'] = extract_time_domain_features(ecg_data, sr)
            
            elif feature_type == 'frequency':
                eeg_features['frequency'] = extract_frequency_domain_features(eeg_data, sr)
                ecg_features['frequency'] = extract_frequency_domain_features(ecg_data, sr)
            
            elif feature_type == 'nonlinear':
                eeg_features['nonlinear'] = extract_nonlinear_features(eeg_data, sr)
                ecg_features['nonlinear'] = extract_nonlinear_features(ecg_data, sr)
            
            elif feature_type == 'connectivity':
                # 暂不实现连接性特征
                logger.warning("连接性特征尚未实现")
            
            else:
                logger.warning(f"不支持的特征类型: {feature_type}")
        
        features_data[stage] = {
            'eeg_features': eeg_features,
            'ecg_features': ecg_features,
            'feature_types': feature_types,
            'sampling_rate': sr
        }
        
        logger.info(f"阶段 {stage} 的特征提取完成:")
        logger.info(f"  - EEG特征类型: {list(eeg_features.keys())}")
        logger.info(f"  - ECG特征类型: {list(ecg_features.keys())}")
    
    return features_data

def test_feature_extraction(segmented_data):
    """
    测试特征提取模块
    
    参数:
    segmented_data (dict): 分段后的数据
    
    返回:
    bool: 测试结果
    """
    logger.info("开始测试特征提取模块...")
    
    try:
        # 提取时域和频域特征
        features_data = extract_features(segmented_data, feature_types=['time', 'frequency'])
        
        # 检查特征是否成功提取
        for stage, stage_features in features_data.items():
            logger.info(f"阶段 {stage} 的特征:")
            
            eeg_features = stage_features['eeg_features']
            ecg_features = stage_features['ecg_features']
            
            if 'time' in eeg_features:
                time_features = eeg_features['time']
                logger.info(f"  - EEG时域特征: {len(time_features)}个特征")
                for feature_name, feature_values in time_features.items():
                    logger.info(f"    - {feature_name}: 形状 {feature_values.shape}")
            
            if 'frequency' in eeg_features:
                freq_features = eeg_features['frequency']
                logger.info(f"  - EEG频域特征: {len(freq_features)}个特征")
                for feature_name, feature_values in freq_features.items():
                    logger.info(f"    - {feature_name}: 形状 {feature_values.shape}")
            
            if 'time' in ecg_features:
                time_features = ecg_features['time']
                logger.info(f"  - ECG时域特征: {len(time_features)}个特征")
                for feature_name, feature_values in time_features.items():
                    logger.info(f"    - {feature_name}: 形状 {feature_values.shape}")
            
            if 'frequency' in ecg_features:
                freq_features = ecg_features['frequency']
                logger.info(f"  - ECG频域特征: {len(freq_features)}个特征")
                for feature_name, feature_values in freq_features.items():
                    logger.info(f"    - {feature_name}: 形状 {feature_values.shape}")
        
        logger.info("特征提取模块测试成功!")
        return True
    
    except FeatureExtractionError as e:
        logger.error(f"特征提取失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 导入数据加载、通道处理、标准化和分段模块
    from data_loader import load_hep_data
    from channel_processor import process_channels
    from data_normalizer import normalize_data
    from data_segmentation import segment_data_fixed_length
    
    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)
    
    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)
    
    # 标准化数据
    normalized_data = normalize_data(processed_data, method='z_score')
    
    # 分段数据
    segmented_data = {}
    for stage, data in normalized_data.items():
        eeg_data = data['eeg']
        ecg_data = data['ecg']
        
        segmented_data[stage] = data.copy()
        segmented_data[stage]['eeg'] = segment_data_fixed_length(eeg_data, segment_length=500)
        segmented_data[stage]['ecg'] = segment_data_fixed_length(ecg_data, segment_length=500)
    
    # 测试特征提取
    test_feature_extraction(segmented_data)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试通道处理模块

功能：
- 测试通道处理模块的各个功能
- 验证通道处理的正确性
- 输出测试结果

作者：AI助手
日期：2024年
"""

import os
import sys
import logging
import numpy as np
from datetime import datetime
from data_loader import load_hep_data, DataLoadError
from channel_processor import (
    select_key_channels, assess_signal_quality, filter_low_quality_channels,
    process_channels, ChannelProcessError
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_channel_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-ChannelProcessor")

def test_select_key_channels(stages_data):
    """
    测试选择关键通道
    
    参数:
    stages_data (dict): 各阶段数据
    
    返回:
    bool: 测试结果
    """
    logger.info("测试选择关键通道...")
    
    try:
        # 测试默认关键通道
        logger.info("测试默认关键通道...")
        data_with_default_channels = select_key_channels(stages_data)
        
        # 验证结果
        for stage, data in data_with_default_channels.items():
            logger.info(f"验证阶段 {stage} 的结果")
            
            # 验证EEG通道数量
            if 'n_eeg_channels' not in data:
                logger.error(f"阶段 {stage} 的数据缺少 'n_eeg_channels' 字段")
                return False
            
            # 验证EEG通道列表
            if 'eeg_channels' not in data:
                logger.error(f"阶段 {stage} 的数据缺少 'eeg_channels' 字段")
                return False
            
            # 验证EEG数据维度
            if data['eeg'].shape[1] != len(data['eeg_channels']):
                logger.error(f"阶段 {stage} 的EEG数据维度与通道数量不匹配: {data['eeg'].shape[1]} vs {len(data['eeg_channels'])}")
                return False
            
            logger.info(f"  - 选择了 {data['n_eeg_channels']} 个EEG通道: {data['eeg_channels']}")
        
        # 测试自定义关键通道
        logger.info("测试自定义关键通道...")
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        data_with_custom_channels = select_key_channels(stages_data, key_channels)
        
        # 验证结果
        for stage, data in data_with_custom_channels.items():
            logger.info(f"验证阶段 {stage} 的结果")
            
            # 验证是否找到了指定的关键通道
            found_channels = set(data['eeg_channels'])
            missing_channels = data.get('missing_channels', [])
            
            logger.info(f"  - 找到的通道: {found_channels}")
            logger.info(f"  - 缺失的通道: {missing_channels}")
            
            # 验证至少找到了一些关键通道
            if len(found_channels) < 3:
                logger.error(f"找到的关键通道数量不足: {len(found_channels)}")
                return False
        
        logger.info("测试选择关键通道成功")
        return True
    
    except ChannelProcessError as e:
        logger.error(f"选择关键通道失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def test_assess_signal_quality(stages_data):
    """
    测试评估信号质量
    
    参数:
    stages_data (dict): 各阶段数据
    
    返回:
    bool: 测试结果
    """
    logger.info("测试评估信号质量...")
    
    try:
        # 首先选择关键通道
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        data_with_key_channels = select_key_channels(stages_data, key_channels)
        
        # 评估信号质量
        data_with_quality = assess_signal_quality(data_with_key_channels)
        
        # 验证结果
        for stage, data in data_with_quality.items():
            logger.info(f"验证阶段 {stage} 的结果")
            
            # 验证是否包含质量评估结果
            if 'eeg_quality' not in data:
                logger.error(f"阶段 {stage} 的数据缺少 'eeg_quality' 字段")
                return False
            
            if 'ecg_quality' not in data:
                logger.error(f"阶段 {stage} 的数据缺少 'ecg_quality' 字段")
                return False
            
            # 验证质量评估结果的内容
            eeg_quality = data['eeg_quality']
            ecg_quality = data['ecg_quality']
            
            required_fields = ['channel_names', 'snr', 'var', 'is_good', 'good_channels', 'bad_channels']
            
            for field in required_fields:
                if field not in eeg_quality:
                    logger.error(f"EEG质量评估结果缺少 '{field}' 字段")
                    return False
                
                if field not in ecg_quality:
                    logger.error(f"ECG质量评估结果缺少 '{field}' 字段")
                    return False
            
            # 打印质量评估结果
            logger.info(f"  - EEG: {len(eeg_quality['good_channels'])}/{len(eeg_quality['channel_names'])} 个高质量通道")
            logger.info(f"  - ECG: {len(ecg_quality['good_channels'])}/{len(ecg_quality['channel_names'])} 个高质量通道")
            
            if eeg_quality['bad_channels']:
                logger.info(f"  - 低质量EEG通道: {eeg_quality['bad_channels']}")
            
            if ecg_quality['bad_channels']:
                logger.info(f"  - 低质量ECG通道: {ecg_quality['bad_channels']}")
        
        logger.info("测试评估信号质量成功")
        return True
    
    except ChannelProcessError as e:
        logger.error(f"评估信号质量失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def test_filter_low_quality_channels(stages_data):
    """
    测试过滤低质量通道
    
    参数:
    stages_data (dict): 各阶段数据
    
    返回:
    bool: 测试结果
    """
    logger.info("测试过滤低质量通道...")
    
    try:
        # 首先选择关键通道
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        data_with_key_channels = select_key_channels(stages_data, key_channels)
        
        # 评估信号质量
        data_with_quality = assess_signal_quality(data_with_key_channels)
        
        # 过滤低质量通道
        filtered_data = filter_low_quality_channels(data_with_quality)
        
        # 验证结果
        for stage, data in filtered_data.items():
            logger.info(f"验证阶段 {stage} 的结果")
            
            # 验证EEG通道数量
            if data['n_eeg_channels'] < 3:
                logger.error(f"过滤后的EEG通道数量不足: {data['n_eeg_channels']}")
                return False
            
            # 验证ECG通道数量
            if data['n_ecg_channels'] < 1:
                logger.error(f"过滤后的ECG通道数量不足: {data['n_ecg_channels']}")
                return False
            
            # 验证数据维度
            if data['eeg'].shape[1] != data['n_eeg_channels']:
                logger.error(f"EEG数据维度与通道数量不匹配: {data['eeg'].shape[1]} vs {data['n_eeg_channels']}")
                return False
            
            if data['ecg'].shape[1] != data['n_ecg_channels']:
                logger.error(f"ECG数据维度与通道数量不匹配: {data['ecg'].shape[1]} vs {data['n_ecg_channels']}")
                return False
            
            # 打印过滤结果
            logger.info(f"  - 过滤前EEG通道数: {len(data['eeg_quality']['channel_names'])}")
            logger.info(f"  - 过滤后EEG通道数: {data['n_eeg_channels']}")
            logger.info(f"  - 过滤前ECG通道数: {len(data['ecg_quality']['channel_names'])}")
            logger.info(f"  - 过滤后ECG通道数: {data['n_ecg_channels']}")
        
        logger.info("测试过滤低质量通道成功")
        return True
    
    except ChannelProcessError as e:
        logger.error(f"过滤低质量通道失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def test_process_channels(stages_data):
    """
    测试完整的通道处理流程
    
    参数:
    stages_data (dict): 各阶段数据
    
    返回:
    bool: 测试结果
    """
    logger.info("测试完整的通道处理流程...")
    
    try:
        # 测试完整处理流程
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        processed_data = process_channels(stages_data, key_channels)
        
        # 验证结果
        for stage, data in processed_data.items():
            logger.info(f"验证阶段 {stage} 的结果")
            
            # 验证处理后的数据结构
            required_fields = ['eeg', 'ecg', 'eeg_channels', 'ecg_channels', 'n_eeg_channels', 'n_ecg_channels']
            
            for field in required_fields:
                if field not in data:
                    logger.error(f"处理后的数据缺少 '{field}' 字段")
                    return False
            
            # 验证数据维度
            if data['eeg'].shape[1] != data['n_eeg_channels']:
                logger.error(f"EEG数据维度与通道数量不匹配: {data['eeg'].shape[1]} vs {data['n_eeg_channels']}")
                return False
            
            if data['ecg'].shape[1] != data['n_ecg_channels']:
                logger.error(f"ECG数据维度与通道数量不匹配: {data['ecg'].shape[1]} vs {data['n_ecg_channels']}")
                return False
            
            # 打印处理结果
            logger.info(f"  - EEG通道数: {data['n_eeg_channels']}")
            logger.info(f"  - ECG通道数: {data['n_ecg_channels']}")
            logger.info(f"  - EEG通道: {data['eeg_channels']}")
            logger.info(f"  - ECG通道: {data['ecg_channels'][:5]}...")
        
        logger.info("测试完整的通道处理流程成功")
        return True
    
    except ChannelProcessError as e:
        logger.error(f"通道处理失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def run_all_tests():
    """
    运行所有测试
    """
    logger.info("开始运行所有测试...")
    
    try:
        # 加载数据
        data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
        stages_data = load_hep_data(data_dir)
        
        # 为了加快测试速度，只使用一个阶段的数据
        test_stage = 'rest1'
        if test_stage in stages_data:
            test_data = {test_stage: stages_data[test_stage]}
            logger.info(f"使用阶段 {test_stage} 的数据进行测试")
        else:
            test_data = stages_data
            logger.info("使用所有阶段的数据进行测试")
        
        # 运行各个测试
        test1_result = test_select_key_channels(test_data)
        test2_result = test_assess_signal_quality(test_data)
        test3_result = test_filter_low_quality_channels(test_data)
        test4_result = test_process_channels(test_data)
        
        # 输出总结
        logger.info("测试结果总结:")
        logger.info(f"  - 测试选择关键通道: {'通过' if test1_result else '失败'}")
        logger.info(f"  - 测试评估信号质量: {'通过' if test2_result else '失败'}")
        logger.info(f"  - 测试过滤低质量通道: {'通过' if test3_result else '失败'}")
        logger.info(f"  - 测试完整的通道处理流程: {'通过' if test4_result else '失败'}")
        
        # 总体结果
        overall_result = test1_result and test2_result and test3_result and test4_result
        logger.info(f"总体测试结果: {'通过' if overall_result else '失败'}")
        
        return overall_result
    
    except DataLoadError as e:
        logger.error(f"数据加载失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    run_all_tests()

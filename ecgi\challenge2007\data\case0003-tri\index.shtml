<!--#set var="TITLE" value="Case 3 Triangulated Torso and Heart Geometry"-->
<!--#include virtual="/head.shtml"-->

<p>
<a href="../">Return to Challenge 2007 data main page</a>


<p>
These files, and the notes below, were prepared by <PERSON><PERSON> and
<PERSON><PERSON>.  The geometry files are text files.  Matlab code for
reading and manipulating the .tri files is also provided here.

<h2>Geometry (.tri) files:  (ascii files)</h2>

<ul>
<table cellpadding=10 border=1>
<tr><td>thorax</td>
    <td><a href="case3_torso.tri">case3_torso.tri</a> (370 nodes)</td></tr>
<tr><td>left lung</td>
    <td><a href="case3_llung.tri">case3_llung.tri</a></td></tr>
<tr><td>right lung</td>
    <td><a href="case3_rlung.tri">case3_rlung.tri</a></td></tr>
<tr><td>epicardium</td>
    <td><a href="case3_peric.tri">case3_peric.tri</a><br>
               closed geometry encompassing the ventricles<br>
               shares nodes with those of the epicardium</td><tr>
<tr><td>ventricles</td>
    <td><a href="case3_ventr.tri">case3_ventr.tri</a><br>
               closed surface bounding ventricular mass:<br> 
               topology: first order doughnut; (because of RVOT)<br>
               includes: endocardium; epicardium; and connection at
               the base</td></tr>

<tr><td>left cavity</td><td><a href="case3_lendo.tri">case3_lendo.tri</a><br>
               closed surface representing blood inside cavities<br>
               shares nodes with those of the endocardium</td></tr>
 

<tr><td>right cavity</td><td><a href="case3_rendo.tri">case3_rendo.tri</a><br>
               closed surface representing blood inside cavities<br>
               shares nodes with those of the endocardium</td></tr>
</table>
</ul>

<p>
All the above: consistent coordinate system; heart in natural position
<ul>
<li>   x_axis frontal; 
<li>   y_axis: points to the left arm
<li>   z-axis: feet to head direction
<li>   units in mm
</ul>

In addition, here is the model containing the nodes that should be referenced
for challenge event 5:
</pre>

<ul>
<table cellpadding=10 border=1>
<tr><td>left ventricle</td>
    <td><a href="case3_LV.tri">case3_LV.tri</a> (corrected)<br>
                closed surface representing left ventricular myocardium.<br>
                (right ventricle 'cut off') <br>
                orientation vertical: along long axis</td></tr>
</table> 
</ul>

<h2>Auxiliary files</h2>

<p>
Descriptive trailers are included in these files:

<ul>
<table cellpadding=10 border=1>
<tr><td>Nodes among the 352-node Dalhousie grid
    where potentials have been recorded in case 3</td>
    <td><a href="elnodes_120_352.lst">elnodes_120_352.lst</a><br>
        ascii file<br>
        initial row (120 1) specifies matrix size</td></tr>
<tr><td>Electrode positions on the thorax for 123-node BPSM</td>
    <td><a href="elnodes_123_370.lst">elnodes_123_370.lst</a><br>
        ascii file<br>
        initial row (123 1) specifies matrix size</td></tr>

<tr><td>potential data</td>
    <td><a href="ecgs_case3.asc">ecgs_case3.asc</a><br>
        original data as previously posted on PhysioNet (see trailer)</td></tr>
</table>
</ul>

<p>
These files are described below:
<ul>
<table cellpadding=10 border=1>
<tr><td>Load .asc file in Matlab</td>
    <td><a href="get_370_lead_signals.m">get_370_lead_signals.m</a></td></tr>
<tr><td>Load .asc file in Matlab</td>
    <td><a href="loadmat.m">loadmat.m</a></td></tr>
<tr><td>Load .tri file in Matlab</td>
    <td><a href="loadtri.m">loadtri.m</a></td></tr>
<tr><td>Laplacian interpolator</td>
    <td><a href="intripol.m">intripol.m</a></td></tr>
<tr><td>Save data in .asc form</td>
    <td><a href="savemat.m">savemat.m</a></td></tr>
<tr><td>Save triangulation in .tri form</td>
    <td><a href="savetri.m">savetri.m</a></td></tr>
<tr><td>Sample of ECGs</td>
    <td><a href="ecgs_123_leads.jpg">ecgs_123_leads.jpg</a></td></tr>
<tr><td>Sample of a BSPM at t=555 ms</td>
    <td><a href="bspm_555.jpg">bspm_555.jpg</a></td></tr>
<tr><td>Transfer matrix for interpolation from 123 to 370 nodes</td>
    <td><a href="transfer_370_123.mat">transfer_370_123.mat</a></td></tr>
</table>
</ul>

<h2>Using .tri files in Matlab</h2>

<p>
Each .tri file can be opened in Matlab as

<pre>
   [VER,ITRI]=loadtri('<i>filename</i>.tri);
</pre>

<p>
Strip the first column, which contains vertex/triangle labels.
<tt>VER</tt> are coordinates of all nodes of the mesh,
and <tt>ITRI</tt> are ordered triples of the triangles.
    
<p>
The ECG potential data may be imported in Matlab by using:

<pre>
   PHI=loadmat('ecgs_case3.asc');
</pre>

<p>
After stripping the first 3 columns by:

<pre>
   PHI=PHI(:,4:355);
</pre>
the potentials (units 1 microV) are a matrix of potential data, with
the rows at 1 ms intervals.  The first 3 columns are leads Vr, Vl, and
Vf (non-augmented amplitudes!); the next 120 columns are the
potentials at the 120 electode array; and the remaining columns are
the potentials at a regular 352 array.

<p>
Potentials at all 370 nodes representing the thorax can be computed
by using the transfer matrix:

<pre>
    T=loadmat('transfer_370_123.mat');
</pre>

which yields a Laplacian based interpolation (see intripol.m):

<pre>
    PSI=T*PHI;
</pre>

The file <a href="get_370_lead_signals.m">get_370_lead_signals.m</a> shows
how to "generate" the potentials at the 370 nodes of the torso model starting
with the data file and the auxiliary files.

<p>
Finally, the files savemat.m and savetri.m are scripts for storing
any similar output data.

<hr>
<p>
<a href="../">Return to Challenge 2007 data main page</a>

<p>

<!--#include virtual="/footer.shtml"-->

</body>
</html>

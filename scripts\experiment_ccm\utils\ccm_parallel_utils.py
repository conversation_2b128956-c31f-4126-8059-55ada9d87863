#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CCM并行计算工具模块

提供用于并行执行CCM分析的辅助函数。
"""

import os
import time
import numpy as np
import concurrent.futures
from scipy.stats import zscore
import threading
from functools import wraps

def timeout(seconds):
    """
    超时装饰器
    
    参数:
    seconds (int): 超时时间（秒）
    
    返回:
    function: 装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = [None]
            exception = [None]
            
            def worker():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=worker)
            thread.daemon = True
            thread.start()
            thread.join(seconds)
            
            if thread.is_alive():
                raise TimeoutError(f"函数执行超过{seconds}秒")
            if exception[0] is not None:
                raise exception[0]
            return result[0]
        return wrapper
    return decorator

def process_channel_band(channel, band, eeg_envelopes, hr_normalized, lib_sizes, embed_dim, tau, timeout_seconds, ccm_correlation_func):
    """
    处理单个通道-频段组合的CCM分析（用于并行处理）
    
    参数:
    channel (str): 通道名称
    band (str): 频段名称
    eeg_envelopes (dict): EEG包络，格式为{channel: {band: envelope}}
    hr_normalized (np.ndarray): 标准化的心率序列
    lib_sizes (np.ndarray): 库大小数组
    embed_dim (int): 嵌入维度
    tau (int): 时间延迟
    timeout_seconds (int): CCM计算的超时时间（秒）
    ccm_correlation_func (function): CCM相关性计算函数
    
    返回:
    tuple: (channel, band, result_dict) 或 (channel, band, None)
    """
    try:
        print(f"  [进程 {os.getpid()}] 分析通道 {channel} 的 {band} 频段...")
        start_time = time.time()
        
        # 获取并标准化EEG包络
        eeg_envelope = eeg_envelopes[channel][band]
        
        # 检查是否有NaN或Inf
        if np.any(np.isnan(eeg_envelope)) or np.any(np.isinf(eeg_envelope)):
            eeg_envelope = np.nan_to_num(eeg_envelope)
        
        eeg_normalized = zscore(eeg_envelope)
        
        # 确保数据长度一致
        min_length = min(len(eeg_normalized), len(hr_normalized))
        eeg_normalized = eeg_normalized[:min_length]
        hr_normalized_cut = hr_normalized[:min_length]
        
        # 使用超时装饰器包装CCM计算
        @timeout(timeout_seconds)
        def ccm_with_timeout(x, y, embed_dim, tau, lib_sizes):
            return ccm_correlation_func(x, y, embed_dim, tau, lib_sizes)
        
        # 执行CCM分析
        libs, eeg_to_hr, hr_to_eeg = ccm_with_timeout(eeg_normalized, hr_normalized_cut, embed_dim, tau, lib_sizes)
        
        # 构建结果字典
        result = {
            'library_sizes': libs,
            'eeg_to_hr': eeg_to_hr,  # 脑到心的因果关系
            'hr_to_eeg': hr_to_eeg,  # 心到脑的因果关系
            'channel': channel,
            'band': band
        }
        
        elapsed_time = time.time() - start_time
        print(f"  [进程 {os.getpid()}] 通道 {channel} 的 {band} 频段分析完成，耗时: {elapsed_time:.2f}秒")
        
        return (channel, band, result)
        
    except TimeoutError as te:
        print(f"  [进程 {os.getpid()}] 警告: 通道 {channel} 的 {band} 频段CCM分析超时: {te}")
        return (channel, band, None)
        
    except Exception as e:
        import traceback
        print(f"  [进程 {os.getpid()}] 通道 {channel} 的 {band} 频段分析出错: {e}")
        traceback.print_exc()
        return (channel, band, None)

def perform_ccm_analysis_parallel(eeg_envelopes, hr_series, channels_of_interest, bands_of_interest, sfreq, 
                                 embed_dim=3, tau=1, timeout_seconds=60, max_workers=4, ccm_correlation_func=None):
    """
    并行对选定的EEG通道和频段进行CCM分析
    
    参数:
    eeg_envelopes (dict): EEG包络，格式为{channel: {band: envelope}}
    hr_series (np.ndarray): 心率序列
    channels_of_interest (list): 感兴趣的通道列表
    bands_of_interest (list): 感兴趣的频段列表
    sfreq (float): 采样率
    embed_dim (int): 嵌入维度
    tau (int): 时间延迟
    timeout_seconds (int): CCM计算的超时时间（秒）
    max_workers (int): 最大并行工作进程数
    ccm_correlation_func (function): CCM相关性计算函数
    
    返回:
    dict: CCM分析结果
    """
    results = {}
    
    # 输入检查
    print(f"执行并行CCM分析的输入检查...")
    if eeg_envelopes is None:
        print("错误: EEG包络为None，无法进行CCM分析")
        return results
    if hr_series is None:
        print("错误: HR序列为None，无法进行CCM分析")
        return results
    
    # 将HR序列标准化
    try:
        print("对HR序列进行标准化...")
        hr_normalized = zscore(hr_series)
    except Exception as norm_err:
        print(f"HR标准化出错: {norm_err}")
        return results
    
    # 准备分析
    print(f"进行并行CCM分析: 嵌入维度={embed_dim}, 时间延迟={tau}")
    
    # 设置库大小
    signal_length = len(hr_series)
    try:
        max_lib_size = min(int(signal_length * 0.8), 5000)
        lib_sizes = np.linspace(50, max_lib_size, 10, dtype=int)
        print(f"库大小: {lib_sizes}")
    except Exception as lib_err:
        print(f"设置库大小出错: {lib_err}")
        return results
    
    # 准备任务列表
    tasks = []
    for channel in channels_of_interest:
        if channel not in eeg_envelopes:
            print(f"未找到通道 {channel} 的数据")
            continue
            
        for band in bands_of_interest:
            if band not in eeg_envelopes[channel]:
                print(f"未找到通道 {channel} 的 {band} 频段数据")
                continue
                
            tasks.append((channel, band))
    
    print(f"准备执行{len(tasks)}个通道-频段组合的CCM分析")
    
    # 调整并行进程数
    actual_workers = min(max_workers, len(tasks))
    print(f"使用{actual_workers}个并行进程")
    
    # 并行执行CCM分析
    start_time = time.time()
    
    with concurrent.futures.ProcessPoolExecutor(max_workers=actual_workers) as executor:
        # 提交所有任务
        future_to_task = {
            executor.submit(
                process_channel_band, 
                channel, band, eeg_envelopes, hr_normalized, 
                lib_sizes, embed_dim, tau, timeout_seconds, ccm_correlation_func
            ): (channel, band)
            for channel, band in tasks
        }
        
        # 处理完成的任务
        completed = 0
        for future in concurrent.futures.as_completed(future_to_task):
            channel, band = future_to_task[future]
            try:
                result_channel, result_band, result_data = future.result()
                completed += 1
                
                if result_data:
                    key = f"{result_channel}_{result_band}"
                    results[key] = result_data
                    print(f"  添加结果: {key} ({completed}/{len(tasks)})")
                else:
                    print(f"  通道 {channel} 的 {band} 频段分析失败 ({completed}/{len(tasks)})")
                    
            except Exception as e:
                completed += 1
                print(f"  获取通道 {channel} 的 {band} 频段结果时出错: {e} ({completed}/{len(tasks)})")
    
    total_time = time.time() - start_time
    print(f"CCM分析完成，共分析了{len(results)}/{len(tasks)}个通道-频段组合，总耗时: {total_time:.2f}秒")
    
    return results

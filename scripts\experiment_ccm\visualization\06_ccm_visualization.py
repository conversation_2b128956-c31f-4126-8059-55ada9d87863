#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CCM分析结果IEEE Transaction风格可视化脚本

该脚本用于将CCM分析结果生成符合IEEE Transaction风格的高质量图表。
特点：
1. 学术风格：符合IEEE Transactions规范
2. 清晰标注P值
3. 高对比度颜色区分
4. 使用文泉驿等宽微米黑字体支持中文显示
5. 高精度矢量图片输出

作者: AI助手
日期: 2023年11月
"""

import os
import sys
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.gridspec import GridSpec
from scipy import stats
import matplotlib.font_manager as fm
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 设置matplotlib参数，使图表符合IEEE风格
plt.rcParams['font.sans-serif'] = ['LXGW Wenkai', 'SimHei']  # 优先使用文泉驿等宽微米黑
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
plt.rcParams['font.size'] = 10  # 设置字体大小
plt.rcParams['axes.linewidth'] = 1.0  # 设置轴线宽度
plt.rcParams['axes.grid'] = False  # 默认不显示网格
plt.rcParams['figure.figsize'] = [8, 6]  # 设置默认图像大小
plt.rcParams['figure.dpi'] = 300  # 设置默认DPI
plt.rcParams['savefig.dpi'] = 600  # 设置保存图像的DPI
plt.rcParams['savefig.bbox'] = 'tight'  # 紧凑的边界框
plt.rcParams['savefig.pad_inches'] = 0.1  # 边界填充

# IEEE风格色彩方案
IEEE_COLORS = [
    '#0072BD',  # 蓝色
    '#D95319',  # 橙色
    '#EDB120',  # 黄色
    '#7E2F8E',  # 紫色
    '#77AC30',  # 绿色
    '#4DBEEE',  # 浅蓝
    '#A2142F',  # 深红
]

# 检查字体是否可用并添加
def check_and_add_font():
    """检查并添加文泉驿等宽微米黑字体"""
    # 检查字体列表中是否已有所需字体
    font_names = [f.name for f in fm.fontManager.ttflist]
    if 'LXGW WenKai' in font_names:
        logger.info("已找到文泉驿等宽微米黑字体")
        return True

    # 检查常见的字体路径
    potential_paths = [
        # Windows路径
        "C:/Windows/Fonts/LXGWWenKai-Regular.ttf",
        # Linux路径
        "/usr/share/fonts/truetype/lxgw/LXGWWenKai-Regular.ttf",
        "/usr/local/share/fonts/LXGWWenKai-Regular.ttf",
        # macOS路径
        "/Library/Fonts/LXGWWenKai-Regular.ttf",
        # 当前目录
        "./LXGWWenKai-Regular.ttf",
        # 用户目录下的字体文件夹
        os.path.expanduser("~/.fonts/LXGWWenKai-Regular.ttf"),
    ]

    for path in potential_paths:
        if os.path.exists(path):
            try:
                # 添加字体文件
                fm.fontManager.addfont(path)
                plt.rcParams['font.sans-serif'] = ['LXGW WenKai'] + plt.rcParams['font.sans-serif']
                logger.info(f"已添加文泉驿等宽微米黑字体: {path}")
                return True
            except Exception as e:
                logger.warning(f"添加字体失败: {str(e)}")

    logger.warning("未找到文泉驿等宽微米黑字体，将使用系统默认字体")
    return False

# 加载CCM分析结果
def load_ccm_results(file_path):
    """
    加载CCM分析结果

    参数:
    - file_path: 结果文件路径（Parquet格式）

    返回:
    - 结果DataFrame
    """
    try:
        df = pd.read_parquet(file_path)
        logger.info(f"成功加载结果文件: {file_path}，共 {len(df)} 条记录")
        return df
    except Exception as e:
        logger.error(f"加载结果文件失败: {str(e)}")
        return None

# 创建频段方向性对比图
def plot_band_directionality(df, output_dir, filename_prefix="band_directionality"):
    """
    创建不同频段脑-心方向性对比图

    参数:
    - df: 结果DataFrame
    - output_dir: 输出目录
    - filename_prefix: 文件名前缀
    """
    # 检查是否有library_size列
    if 'library_size' not in df.columns:
        logger.info("结果文件中没有library_size列，使用汇总数据创建简化版图表")

        # 按频段分组计算均值
        band_summary = df.groupby(['band']).agg({
            'eeg_to_heart': 'mean',
            'heart_to_eeg': 'mean',
            'directionality': 'mean'
        }).reset_index()

        # 获取所有频段
        bands = band_summary['band'].unique()

        # 创建条形图
        plt.figure(figsize=(10, 6))

        # 设置条形图位置
        x = np.arange(len(bands))
        width = 0.35

        # 绘制条形图
        plt.bar(x - width/2, band_summary['eeg_to_heart'], width, label='脑→心', color=IEEE_COLORS[0])
        plt.bar(x + width/2, band_summary['heart_to_eeg'], width, label='心→脑', color=IEEE_COLORS[1])

        # 添加数据标签
        for i, v in enumerate(band_summary['eeg_to_heart']):
            plt.text(i - width/2, v + 0.01, f"{v:.2f}", ha='center', fontsize=9)

        for i, v in enumerate(band_summary['heart_to_eeg']):
            plt.text(i + width/2, v + 0.01, f"{v:.2f}", ha='center', fontsize=9)

        # 设置图表标题和标签
        plt.title("不同频段脑-心因果关系强度", fontsize=12)
        plt.ylabel("CCM强度", fontsize=10)
        plt.xlabel("频段", fontsize=10)
        plt.xticks(x, bands)

        # 添加图例
        plt.legend()

        # 添加网格
        plt.grid(axis='y', linestyle=':', alpha=0.6)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        output_path = os.path.join(output_dir, f"{filename_prefix}_bands.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式（矢量图）
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_bands.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存频段方向性对比图: {output_path}")
        plt.close()

        # 创建方向性条形图
        plt.figure(figsize=(10, 6))

        # 绘制方向性条形图
        plt.bar(bands, band_summary['directionality'], color=[IEEE_COLORS[0] if x >= 0 else IEEE_COLORS[1] for x in band_summary['directionality']])

        # 添加零线
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        # 添加数据标签
        for i, v in enumerate(band_summary['directionality']):
            plt.text(i, v + 0.01 if v >= 0 else v - 0.03, f"{v:.2f}", ha='center', fontsize=9)

        # 设置图表标题和标签
        plt.title("不同频段脑-心方向性差异 (脑→心 - 心→脑)", fontsize=12)
        plt.ylabel("方向性差异", fontsize=10)
        plt.xlabel("频段", fontsize=10)

        # 添加网格
        plt.grid(axis='y', linestyle=':', alpha=0.6)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        output_path = os.path.join(output_dir, f"{filename_prefix}_directionality.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式（矢量图）
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_directionality.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存频段方向性差异图: {output_path}")
        plt.close()

        return

    # 以下是原始代码，适用于有library_size列的情况
    # 按频段分组计算均值
    band_summary = df.groupby(['band', 'library_size']).agg({
        'eeg_to_heart': 'mean',
        'heart_to_eeg': 'mean'
    }).reset_index()

    # 获取所有频段
    bands = band_summary['band'].unique()

    # 创建图形，每个频段一个子图
    fig, axes = plt.subplots(len(bands), 1, figsize=(8, 3*len(bands)), sharex=True)

    if len(bands) == 1:
        axes = [axes]  # 确保axes总是列表

    # 为每个频段绘制方向性对比图
    for i, band in enumerate(bands):
        band_data = band_summary[band_summary['band'] == band]

        # 绘制两个方向的曲线
        ax = axes[i]
        ax.plot(band_data['library_size'], band_data['eeg_to_heart'],
                marker='o', color=IEEE_COLORS[0], label='脑→心')
        ax.plot(band_data['library_size'], band_data['heart_to_eeg'],
                marker='s', color=IEEE_COLORS[1], label='心→脑', linestyle='--')

        # 计算两个方向的均值，用于标注
        eeg_to_heart_mean = band_data['eeg_to_heart'].mean()
        heart_to_eeg_mean = band_data['heart_to_eeg'].mean()

        # 统计检验（配对t检验）
        if len(band_data) > 1:
            t_stat, p_value = stats.ttest_rel(
                band_data['eeg_to_heart'],
                band_data['heart_to_eeg']
            )

            # 添加p值标注
            if p_value < 0.05:
                ax.text(0.5, 0.9, f'p = {p_value:.3f}' + ('*' * (3 - int(np.log10(max(p_value, 1e-3))))),
                        transform=ax.transAxes, ha='center',
                        bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))

        # 设置图表标题和标签
        ax.set_title(f"{band}频段 脑-心因果关系强度", fontsize=12)
        ax.set_ylabel("CCM强度", fontsize=10)
        if i == len(bands) - 1:  # 只在最后一个子图上添加x轴标签
            ax.set_xlabel("库大小", fontsize=10)

        # 添加图例
        ax.legend(loc='upper left')

        # 设置y轴范围，使差异更明显
        min_val = min(band_data['eeg_to_heart'].min(), band_data['heart_to_eeg'].min())
        max_val = max(band_data['eeg_to_heart'].max(), band_data['heart_to_eeg'].max())
        padding = (max_val - min_val) * 0.1
        ax.set_ylim(min_val - padding, max_val + padding)

        # 为图表添加网格
        ax.grid(True, linestyle=':', alpha=0.6)

    # 调整子图之间的间距
    plt.tight_layout()

    # 保存图表
    output_path = os.path.join(output_dir, f"{filename_prefix}.png")
    plt.savefig(output_path, dpi=600)

    # 同时保存PDF格式（矢量图）
    pdf_path = os.path.join(output_dir, f"{filename_prefix}.pdf")
    plt.savefig(pdf_path)

    logger.info(f"已保存频段方向性对比图: {output_path}")
    plt.close()

# 创建通道方向性热图
def plot_channel_directionality_heatmap(df, output_dir, filename_prefix="channel_directionality"):
    """
    创建通道方向性热图

    参数:
    - df: 结果DataFrame
    - output_dir: 输出目录
    - filename_prefix: 文件名前缀
    """
    # 按通道分组计算均值
    channel_summary = df.groupby(['channel', 'band']).agg({
        'eeg_to_heart': 'mean',
        'heart_to_eeg': 'mean',
        'directionality': 'mean' if 'directionality' in df.columns else None
    }).reset_index()

    # 如果没有directionality列，计算方向性差异
    if 'directionality' not in df.columns:
        channel_summary['directionality'] = channel_summary['eeg_to_heart'] - channel_summary['heart_to_eeg']

    # 创建透视表便于热图绘制
    dir_pivot = channel_summary.pivot(index='channel', columns='band', values='directionality')

    # 对通道进行排序，使相邻区域的通道在一起
    channel_groups = {
        '前额': ['Fp1', 'Fpz', 'Fp2'],
        '额叶': ['F7', 'F5', 'F3', 'F1', 'Fz', 'F2', 'F4', 'F6', 'F8'],
        '中央': ['C5', 'C3', 'C1', 'Cz', 'C2', 'C4', 'C6'],
        '顶叶': ['P7', 'P5', 'P3', 'P1', 'Pz', 'P2', 'P4', 'P6', 'P8'],
        '枕叶': ['O1', 'Oz', 'O2'],
        '颞叶': ['T7', 'T8', 'TP9', 'TP10']
    }

    # 获取所有通道并按区域排序
    all_channels = []
    for region, channels in channel_groups.items():
        for channel in channels:
            if channel in dir_pivot.index:
                all_channels.append(channel)

    # 添加任何未在分组中的通道
    for channel in dir_pivot.index:
        if channel not in all_channels:
            all_channels.append(channel)

    # 按排序后的通道重新索引
    valid_channels = [ch for ch in all_channels if ch in dir_pivot.index]
    dir_pivot = dir_pivot.reindex(valid_channels)

    # 创建图形
    plt.figure(figsize=(10, len(valid_channels) * 0.3 + 2))

    # 创建热图
    # 使用红蓝对比色系，突出正负值
    cmap = sns.diverging_palette(240, 10, as_cmap=True)

    # 绘制热图
    ax = sns.heatmap(dir_pivot, cmap=cmap, center=0,
                   linewidths=0.5, linecolor='gray',
                   annot=True, fmt=".2f", annot_kws={"size": 8})

    # 设置图表标题和标签
    plt.title("脑-心方向性强度差异 (脑→心 - 心→脑)", fontsize=12)
    plt.ylabel("EEG通道", fontsize=10)
    plt.xlabel("频段", fontsize=10)

    # 增加颜色条的标签
    cbar = ax.collections[0].colorbar
    cbar.set_label("方向性差异（正值表示脑→心更强）", fontsize=10)

    # 保存图表
    plt.tight_layout()
    output_path = os.path.join(output_dir, f"{filename_prefix}_heatmap.png")
    plt.savefig(output_path, dpi=600)

    # 同时保存PDF格式
    pdf_path = os.path.join(output_dir, f"{filename_prefix}_heatmap.pdf")
    plt.savefig(pdf_path)

    logger.info(f"已保存通道方向性热图: {output_path}")
    plt.close()

    # 创建分频段的条形图
    bands = channel_summary['band'].unique()

    for band in bands:
        plt.figure(figsize=(10, 8))

        # 筛选特定频段的数据
        band_data = channel_summary[channel_summary['band'] == band]

        # 对通道按方向性差异排序
        band_data = band_data.sort_values('directionality', ascending=False)

        # 创建条形图
        plt.barh(band_data['channel'], band_data['directionality'],
               color=[IEEE_COLORS[0] if x >= 0 else IEEE_COLORS[1] for x in band_data['directionality']])

        # 添加零线
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

        # 添加数据标签
        for i, (_, row) in enumerate(band_data.iterrows()):
            channel = row['channel']
            value = row['directionality']
            plt.text(value + 0.01 if value >= 0 else value - 0.05,
                   i, f"{value:.2f}", va='center', fontsize=9)

        # 设置图表标题和标签
        plt.title(f"{band}频段 脑-心方向性强度差异", fontsize=12)
        plt.xlabel("方向性差异 (脑→心 - 心→脑)", fontsize=10)
        plt.ylabel("EEG通道", fontsize=10)

        # 添加网格
        plt.grid(axis='x', linestyle=':', alpha=0.6)

        # 保存图表
        plt.tight_layout()
        output_path = os.path.join(output_dir, f"{filename_prefix}_{band}_bar.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}_bar.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存{band}频段方向性条形图: {output_path}")
        plt.close()

# 创建收敛曲线图
def plot_convergence_curves(df, output_dir, filename_prefix="convergence_curves"):
    """
    创建CCM收敛曲线图

    参数:
    - df: 结果DataFrame
    - output_dir: 输出目录
    - filename_prefix: 文件名前缀
    """
    # 检查是否有library_size列
    if 'library_size' not in df.columns:
        logger.info("结果文件中没有library_size列，使用汇总数据创建简化版图表")

        # 对每个频段创建通道对比图
        bands = df['band'].unique()

        for band in bands:
            # 筛选特定频段的数据
            band_data = df[df['band'] == band]

            # 计算最具代表性的通道（方向性差异最大的通道）
            band_data['abs_diff'] = abs(band_data['eeg_to_heart'] - band_data['heart_to_eeg'])
            top_channels = band_data.sort_values('abs_diff', ascending=False)['channel'].unique()[:10]

            # 创建图形
            plt.figure(figsize=(12, 8))

            # 设置条形图位置
            x = np.arange(len(top_channels))
            width = 0.35

            # 获取这些通道的数据
            channel_data = band_data[band_data['channel'].isin(top_channels)]

            # 创建数据框
            pivot_data = channel_data.set_index('channel')[['eeg_to_heart', 'heart_to_eeg', 'directionality']]

            # 按方向性差异排序
            pivot_data = pivot_data.sort_values('directionality', ascending=False)

            # 获取排序后的通道
            sorted_channels = pivot_data.index.tolist()

            # 绘制条形图
            plt.figure(figsize=(12, 8))

            # 绘制两个方向的条形图
            plt.barh(sorted_channels, pivot_data['eeg_to_heart'],
                   label='脑→心', color=IEEE_COLORS[0], alpha=0.7)
            plt.barh(sorted_channels, -pivot_data['heart_to_eeg'],
                   label='心→脑', color=IEEE_COLORS[1], alpha=0.7)

            # 添加零线
            plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

            # 添加数据标签
            for i, channel in enumerate(sorted_channels):
                eeg_val = float(pivot_data.loc[channel, 'eeg_to_heart'].iloc[0] if isinstance(pivot_data.loc[channel, 'eeg_to_heart'], pd.Series) else pivot_data.loc[channel, 'eeg_to_heart'])
                heart_val = float(pivot_data.loc[channel, 'heart_to_eeg'].iloc[0] if isinstance(pivot_data.loc[channel, 'heart_to_eeg'], pd.Series) else pivot_data.loc[channel, 'heart_to_eeg'])

                plt.text(eeg_val + 0.01, i, f"{eeg_val:.2f}", va='center', fontsize=9)
                plt.text(-heart_val - 0.05, i, f"{heart_val:.2f}", va='center', fontsize=9)

            # 设置图表标题和标签
            plt.title(f"{band}频段 各通道脑-心因果关系强度", fontsize=12)
            plt.xlabel("CCM强度 (正值: 脑→心, 负值: 心→脑)", fontsize=10)
            plt.ylabel("EEG通道", fontsize=10)

            # 添加图例
            plt.legend(loc='upper right')

            # 添加网格
            plt.grid(axis='x', linestyle=':', alpha=0.6)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            output_path = os.path.join(output_dir, f"{filename_prefix}_{band}_channels.png")
            plt.savefig(output_path, dpi=600)

            # 同时保存PDF格式
            pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}_channels.pdf")
            plt.savefig(pdf_path)

            logger.info(f"已保存{band}频段通道对比图: {output_path}")
            plt.close()

            # 创建方向性条形图
            plt.figure(figsize=(12, 8))

            # 绘制方向性条形图
            plt.barh(sorted_channels, pivot_data['directionality'],
                   color=[IEEE_COLORS[0] if x >= 0 else IEEE_COLORS[1] for x in pivot_data['directionality']])

            # 添加零线
            plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)

            # 添加数据标签
            for i, channel in enumerate(sorted_channels):
                dir_val = float(pivot_data.loc[channel, 'directionality'].iloc[0] if isinstance(pivot_data.loc[channel, 'directionality'], pd.Series) else pivot_data.loc[channel, 'directionality'])
                plt.text(dir_val + 0.01 if dir_val >= 0 else dir_val - 0.05,
                       i, f"{dir_val:.2f}", va='center', fontsize=9)

            # 设置图表标题和标签
            plt.title(f"{band}频段 各通道脑-心方向性差异", fontsize=12)
            plt.xlabel("方向性差异 (脑→心 - 心→脑)", fontsize=10)
            plt.ylabel("EEG通道", fontsize=10)

            # 添加网格
            plt.grid(axis='x', linestyle=':', alpha=0.6)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            output_path = os.path.join(output_dir, f"{filename_prefix}_{band}_directionality.png")
            plt.savefig(output_path, dpi=600)

            # 同时保存PDF格式
            pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}_directionality.pdf")
            plt.savefig(pdf_path)

            logger.info(f"已保存{band}频段方向性差异图: {output_path}")
            plt.close()

        return

    # 以下是原始代码，适用于有library_size列的情况
    # 对每个频段创建收敛曲线图
    bands = df['band'].unique()

    for band in bands:
        # 筛选特定频段的数据
        band_data = df[df['band'] == band]

        # 计算最具代表性的通道（方向性差异最大的通道）
        channel_diff = band_data.groupby('channel').apply(
            lambda x: abs(x['eeg_to_heart'].mean() - x['heart_to_eeg'].mean())
        ).sort_values(ascending=False)

        # 选择前5个通道
        top_channels = channel_diff.index[:5]

        # 创建图形
        plt.figure(figsize=(10, 8))

        # 为每个通道绘制收敛曲线
        for i, channel in enumerate(top_channels):
            # 获取通道数据
            channel_data = band_data[band_data['channel'] == channel]

            # 创建图表
            plt.subplot(len(top_channels), 1, i+1)

            # 绘制两个方向的曲线
            plt.plot(channel_data['library_size'], channel_data['eeg_to_heart'],
                   marker='o', color=IEEE_COLORS[0], label='脑→心')
            plt.plot(channel_data['library_size'], channel_data['heart_to_eeg'],
                   marker='s', color=IEEE_COLORS[1], label='心→脑', linestyle='--')

            # 计算两个方向的差异显著性
            t_stat, p_value = stats.ttest_rel(
                channel_data['eeg_to_heart'],
                channel_data['heart_to_eeg']
            )

            # 添加p值标注
            if p_value < 0.05:
                sig_marker = '*' * (1 + (p_value < 0.01) + (p_value < 0.001))
                plt.text(0.5, 0.9, f'p = {p_value:.3f} {sig_marker}',
                        transform=plt.gca().transAxes, ha='center',
                        bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.3'))

            # 设置图表标题和标签
            plt.title(f"{channel} ({band}频段)", fontsize=10)
            plt.xlabel("库大小", fontsize=9)
            plt.ylabel("CCM强度", fontsize=9)

            # 添加图例
            if i == 0:  # 只在第一个子图添加图例
                plt.legend(loc='upper left')

            # 添加网格
            plt.grid(True, linestyle=':', alpha=0.6)

        # 调整子图之间的间距
        plt.tight_layout()

        # 保存图表
        output_path = os.path.join(output_dir, f"{filename_prefix}_{band}.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存{band}频段收敛曲线图: {output_path}")
        plt.close()

# 创建脑区方向性对比图
def plot_brain_region_comparison(df, output_dir, filename_prefix="brain_region_comparison"):
    """
    创建不同脑区方向性对比图

    参数:
    - df: 结果DataFrame
    - output_dir: 输出目录
    - filename_prefix: 文件名前缀
    """
    # 定义脑区分组
    brain_regions = {
        '前额区': ['Fp1', 'Fpz', 'Fp2'],
        '额叶区': ['F7', 'F5', 'F3', 'F1', 'Fz', 'F2', 'F4', 'F6', 'F8'],
        '中央区': ['C5', 'C3', 'C1', 'Cz', 'C2', 'C4', 'C6'],
        '顶叶区': ['P7', 'P5', 'P3', 'P1', 'Pz', 'P2', 'P4', 'P6', 'P8'],
        '枕叶区': ['O1', 'Oz', 'O2'],
        '颞叶区': ['T7', 'T8', 'TP9', 'TP10']
    }

    # 为每个通道添加脑区标签
    region_labels = []
    for channel in df['channel']:
        region = "其他"  # 默认值
        for region_name, channels in brain_regions.items():
            if channel in channels:
                region = region_name
                break
        region_labels.append(region)

    df['brain_region'] = region_labels

    # 按脑区和频段计算均值
    region_summary = df.groupby(['brain_region', 'band']).agg({
        'eeg_to_heart': 'mean',
        'heart_to_eeg': 'mean',
        'directionality': 'mean' if 'directionality' in df.columns else None
    }).reset_index()

    # 如果没有directionality列，计算方向性差异
    if 'directionality' not in df.columns:
        region_summary['directionality'] = region_summary['eeg_to_heart'] - region_summary['heart_to_eeg']

    # 获取所有频段
    bands = region_summary['band'].unique()

    # 为每个频段创建脑区对比图
    for band in bands:
        # 筛选特定频段的数据
        band_data = region_summary[region_summary['band'] == band]

        # 创建图形
        plt.figure(figsize=(10, 6))

        # 为每个脑区绘制条形图
        x = np.arange(len(band_data['brain_region']))
        width = 0.35

        plt.bar(x - width/2, band_data['eeg_to_heart'], width,
               label='脑→心', color=IEEE_COLORS[0])
        plt.bar(x + width/2, band_data['heart_to_eeg'], width,
               label='心→脑', color=IEEE_COLORS[1])

        # 添加数据标签
        for i, v in enumerate(band_data['eeg_to_heart']):
            plt.text(i - width/2, v + 0.01, f"{v:.2f}", ha='center', fontsize=9)

        for i, v in enumerate(band_data['heart_to_eeg']):
            plt.text(i + width/2, v + 0.01, f"{v:.2f}", ha='center', fontsize=9)

        # 设置图表标题和标签
        plt.title(f"{band}频段 不同脑区的脑-心因果关系强度", fontsize=12)
        plt.ylabel("CCM强度", fontsize=10)
        plt.xticks(x, band_data['brain_region'], rotation=45, ha='right')

        # 添加图例
        plt.legend(loc='upper right')

        # 添加网格
        plt.grid(axis='y', linestyle=':', alpha=0.6)

        # 为每个脑区添加显著性标记
        for i, region in enumerate(band_data['brain_region']):
            # 获取该脑区所有通道的原始数据
            region_channels = []
            for region_name, channels in brain_regions.items():
                if region_name == region:
                    region_channels = channels
                    break

            # 筛选该脑区和频段的原始数据
            region_raw_data = df[(df['channel'].isin(region_channels)) & (df['band'] == band)]

            if len(region_raw_data) > 5:  # 确保有足够的样本
                # 执行配对t检验
                t_stat, p_value = stats.ttest_rel(
                    region_raw_data['eeg_to_heart'],
                    region_raw_data['heart_to_eeg']
                )

                # 添加p值标注
                if p_value < 0.05:
                    sig_marker = '*' * (1 + (p_value < 0.01) + (p_value < 0.001))
                    y_pos = max(band_data.loc[band_data['brain_region'] == region, 'eeg_to_heart'].values[0],
                              band_data.loc[band_data['brain_region'] == region, 'heart_to_eeg'].values[0])
                    plt.text(i, y_pos + 0.02, sig_marker, ha='center', fontsize=12)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        output_path = os.path.join(output_dir, f"{filename_prefix}_{band}.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存{band}频段脑区对比图: {output_path}")
        plt.close()

        # 创建方向性条形图
        plt.figure(figsize=(10, 6))

        # 绘制方向性条形图
        plt.bar(band_data['brain_region'], band_data['directionality'],
               color=[IEEE_COLORS[0] if x >= 0 else IEEE_COLORS[1] for x in band_data['directionality']])

        # 添加零线
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

        # 添加数据标签
        for i, v in enumerate(band_data['directionality']):
            plt.text(i, v + 0.01 if v >= 0 else v - 0.03, f"{v:.2f}", ha='center', fontsize=9)

        # 设置图表标题和标签
        plt.title(f"{band}频段 不同脑区的脑-心方向性差异", fontsize=12)
        plt.ylabel("方向性差异 (脑→心 - 心→脑)", fontsize=10)
        plt.xticks(rotation=45, ha='right')

        # 添加网格
        plt.grid(axis='y', linestyle=':', alpha=0.6)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        output_path = os.path.join(output_dir, f"{filename_prefix}_{band}_directionality.png")
        plt.savefig(output_path, dpi=600)

        # 同时保存PDF格式
        pdf_path = os.path.join(output_dir, f"{filename_prefix}_{band}_directionality.pdf")
        plt.savefig(pdf_path)

        logger.info(f"已保存{band}频段脑区方向性差异图: {output_path}")
        plt.close()

    # 创建所有频段的方向性差异热图
    plt.figure(figsize=(10, 6))

    # 创建透视表
    dir_pivot = region_summary.pivot(index='brain_region', columns='band', values='directionality')

    # 创建热图
    cmap = sns.diverging_palette(240, 10, as_cmap=True)
    ax = sns.heatmap(dir_pivot, cmap=cmap, center=0,
                   linewidths=0.5, linecolor='gray',
                   annot=True, fmt=".2f", annot_kws={"size": 10})

    # 设置图表标题和标签
    plt.title("不同脑区的脑-心方向性差异", fontsize=12)
    plt.ylabel("脑区", fontsize=10)
    plt.xlabel("频段", fontsize=10)

    # 增加颜色条的标签
    cbar = ax.collections[0].colorbar
    cbar.set_label("方向性差异（正值表示脑→心更强）", fontsize=10)

    # 保存图表
    plt.tight_layout()
    output_path = os.path.join(output_dir, f"{filename_prefix}_heatmap.png")
    plt.savefig(output_path, dpi=600)

    # 同时保存PDF格式
    pdf_path = os.path.join(output_dir, f"{filename_prefix}_heatmap.pdf")
    plt.savefig(pdf_path)

    logger.info(f"已保存脑区方向性热图: {output_path}")
    plt.close()

# 主函数
def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="CCM分析结果IEEE风格可视化")

    parser.add_argument("--input", type=str, required=True,
                      help="输入文件路径（Parquet格式）")

    parser.add_argument("--output_dir", type=str, default="D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm/figures",
                      help="输出目录路径")

    parser.add_argument("--prefix", type=str, default="ccm",
                      help="输出文件名前缀")

    args = parser.parse_args()

    # 检查并添加字体
    check_and_add_font()

    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载数据
    df = load_ccm_results(args.input)

    if df is None:
        logger.error("无法加载数据，退出")
        sys.exit(1)

    # 生成图表
    logger.info("生成频段方向性对比图...")
    plot_band_directionality(df, args.output_dir, args.prefix)

    logger.info("生成通道方向性热图...")
    plot_channel_directionality_heatmap(df, args.output_dir, args.prefix)

    logger.info("生成收敛曲线图...")
    plot_convergence_curves(df, args.output_dir, args.prefix)

    logger.info("生成脑区方向性对比图...")
    plot_brain_region_comparison(df, args.output_dir, args.prefix)

    logger.info("所有图表生成完成")

if __name__ == "__main__":
    main()
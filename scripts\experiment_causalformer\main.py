#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 主程序

功能：
- 集成所有模块，实现完整的脑-心因果分析流程
- 提供命令行接口，支持不同的分析任务
- 生成综合研究报告

作者：AI助手
日期：2024年
"""

import os
import sys
import argparse
import logging
from datetime import datetime
import numpy as np
import torch
import matplotlib.pyplot as plt
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_main_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-Main")

# 导入自定义模块
from data_loader import load_hep_data
from channel_processor import process_channels
from data_normalizer import normalize_data
from data_segmentation import segment_data
from feature_extractor import extract_features
from model_input_preparer import features_to_sequences, prepare_model_inputs
from causal_former import CausalFormer, train_model, evaluate_model, visualize_training_history
from result_visualizer import (
    visualize_predictions, visualize_attention_weights, visualize_causal_graph,
    visualize_causal_matrix, visualize_causal_strength_by_frequency, visualize_causal_strength_by_time
)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='CausalFormer脑-心因果关系分析')
    
    # 数据参数
    parser.add_argument('--data_dir', type=str, default=r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs",
                        help='数据目录')
    parser.add_argument('--output_dir', type=str, default=r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer",
                        help='输出目录')
    parser.add_argument('--channels', type=str, nargs='+', 
                        default=['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4'],
                        help='要分析的通道')
    
    # 预处理参数
    parser.add_argument('--norm_method', type=str, default='z_score',
                        choices=['z_score', 'min_max', 'robust'],
                        help='标准化方法')
    parser.add_argument('--window_size', type=int, default=15000,
                        help='分段标准化窗口大小（样本点数）')
    parser.add_argument('--handle_outliers', type=str, default='clip',
                        choices=['clip', 'remove', 'none'],
                        help='异常值处理方法')
    parser.add_argument('--adaptive_threshold', action='store_true',
                        help='使用自适应异常值阈值')
    
    # 分段参数
    parser.add_argument('--segment_method', type=str, default='fixed',
                        choices=['fixed', 'event', 'adaptive'],
                        help='分段方法')
    parser.add_argument('--segment_length', type=int, default=500,
                        help='分段长度（样本点数）')
    parser.add_argument('--overlap', type=float, default=0.5,
                        help='分段重叠比例')
    
    # 特征提取参数
    parser.add_argument('--feature_types', type=str, nargs='+',
                        default=['time', 'frequency'],
                        help='要提取的特征类型')
    
    # 模型参数
    parser.add_argument('--sequence_length', type=int, default=10,
                        help='序列长度')
    parser.add_argument('--stride', type=int, default=1,
                        help='序列滑动步长')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批次大小')
    parser.add_argument('--d_model', type=int, default=64,
                        help='模型维度')
    parser.add_argument('--n_heads', type=int, default=4,
                        help='注意力头数')
    parser.add_argument('--n_layers', type=int, default=3,
                        help='Transformer层数')
    parser.add_argument('--d_ff', type=int, default=256,
                        help='前馈网络维度')
    parser.add_argument('--dropout', type=float, default=0.1,
                        help='Dropout比例')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=30,
                        help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--device', type=str, default='cuda',
                        choices=['cuda', 'cpu'],
                        help='训练设备')
    
    # 任务参数
    parser.add_argument('--task', type=str, default='full',
                        choices=['preprocess', 'segment', 'extract', 'train', 'evaluate', 'visualize', 'full'],
                        help='要执行的任务')
    parser.add_argument('--stages', type=str, nargs='+',
                        default=['rest1', 'test1'],
                        help='要分析的数据阶段')
    
    return parser.parse_args()

def preprocess_data(args):
    """
    数据预处理
    
    参数:
    args: 命令行参数
    
    返回:
    dict: 预处理后的数据
    """
    logger.info("开始数据预处理...")
    
    # 加载数据
    logger.info(f"从 {args.data_dir} 加载数据...")
    stages_data = load_hep_data(args.data_dir)
    
    # 处理通道
    logger.info(f"处理通道: {args.channels}...")
    processed_data = process_channels(stages_data, args.channels)
    
    # 标准化数据
    logger.info(f"使用 {args.norm_method} 方法标准化数据...")
    normalized_data = normalize_data(
        processed_data, 
        method=args.norm_method,
        window_size=args.window_size if args.window_size > 0 else None,
        handle_outliers_method=args.handle_outliers if args.handle_outliers != 'none' else None,
        adaptive_threshold=args.adaptive_threshold
    )
    
    logger.info("数据预处理完成")
    return normalized_data

def segment_data_task(args, normalized_data):
    """
    数据分段
    
    参数:
    args: 命令行参数
    normalized_data: 标准化后的数据
    
    返回:
    dict: 分段后的数据
    """
    logger.info("开始数据分段...")
    
    # 分段参数
    segment_params = {
        'segment_length': args.segment_length,
        'overlap': args.overlap
    }
    
    # 分段数据
    logger.info(f"使用 {args.segment_method} 方法分段数据...")
    segmented_data = segment_data(normalized_data, method=args.segment_method, **segment_params)
    
    logger.info("数据分段完成")
    return segmented_data

def extract_features_task(args, segmented_data):
    """
    特征提取
    
    参数:
    args: 命令行参数
    segmented_data: 分段后的数据
    
    返回:
    dict: 提取的特征
    """
    logger.info("开始特征提取...")
    
    # 提取特征
    logger.info(f"提取特征类型: {args.feature_types}...")
    features_data = extract_features(segmented_data, feature_types=args.feature_types)
    
    logger.info("特征提取完成")
    return features_data

def prepare_model_inputs_task(args, features_data):
    """
    准备模型输入
    
    参数:
    args: 命令行参数
    features_data: 提取的特征
    
    返回:
    dict: 模型输入数据
    """
    logger.info("开始准备模型输入...")
    
    # 将特征转换为序列
    logger.info(f"将特征转换为序列: 长度={args.sequence_length}, 步长={args.stride}...")
    sequences_data = features_to_sequences(features_data, sequence_length=args.sequence_length, stride=args.stride)
    
    # 准备模型输入
    logger.info(f"准备模型输入: 批次大小={args.batch_size}...")
    model_inputs = prepare_model_inputs(sequences_data, batch_size=args.batch_size)
    
    logger.info("模型输入准备完成")
    return model_inputs

def train_model_task(args, model_inputs):
    """
    训练模型
    
    参数:
    args: 命令行参数
    model_inputs: 模型输入数据
    
    返回:
    tuple: (训练历史记录, 模型字典)
    """
    logger.info("开始训练模型...")
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA不可用，使用CPU")
        device = 'cpu'
    else:
        device = args.device
    
    # 为每个阶段训练模型
    history_dict = {}
    model_dict = {}
    
    for stage in args.stages:
        if stage not in model_inputs:
            logger.warning(f"阶段 {stage} 不在模型输入数据中，跳过")
            continue
        
        logger.info(f"训练阶段 {stage} 的模型...")
        
        stage_inputs = model_inputs[stage]
        train_loader = stage_inputs['train_loader']
        val_loader = stage_inputs['val_loader']
        
        input_dim = stage_inputs['input_dim']
        output_dim = stage_inputs['output_dim']
        
        # 创建模型
        model = CausalFormer(
            input_dim=input_dim,
            output_dim=output_dim,
            d_model=args.d_model,
            n_heads=args.n_heads,
            n_layers=args.n_layers,
            d_ff=args.d_ff,
            dropout=args.dropout
        )
        
        # 训练模型
        model_dir = os.path.join(args.output_dir, 'models', stage)
        history, trained_model = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            epochs=args.epochs,
            lr=args.lr,
            device=device,
            model_dir=model_dir
        )
        
        # 可视化训练历史
        output_dir = os.path.join(args.output_dir, 'training', stage)
        visualize_training_history(history, output_dir=output_dir)
        
        history_dict[stage] = history
        model_dict[stage] = trained_model
    
    logger.info("模型训练完成")
    return history_dict, model_dict

def evaluate_model_task(args, model_inputs, model_dict):
    """
    评估模型
    
    参数:
    args: 命令行参数
    model_inputs: 模型输入数据
    model_dict: 训练好的模型字典
    
    返回:
    dict: 评估结果
    """
    logger.info("开始评估模型...")
    
    # 检查设备
    if args.device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA不可用，使用CPU")
        device = 'cpu'
    else:
        device = args.device
    
    # 为每个阶段评估模型
    eval_results = {}
    
    for stage in args.stages:
        if stage not in model_inputs or stage not in model_dict:
            logger.warning(f"阶段 {stage} 不在模型输入数据或模型字典中，跳过")
            continue
        
        logger.info(f"评估阶段 {stage} 的模型...")
        
        stage_inputs = model_inputs[stage]
        test_loader = stage_inputs['test_loader']
        model = model_dict[stage]
        
        # 评估模型
        eval_result = evaluate_model(model, test_loader, device=device)
        eval_results[stage] = eval_result
        
        # 可视化预测结果
        output_dir = os.path.join(args.output_dir, 'predictions', stage)
        visualize_predictions(eval_result['predictions'], eval_result['targets'], output_dir=output_dir, stage=stage)
    
    logger.info("模型评估完成")
    return eval_results

def visualize_results_task(args, eval_results):
    """
    可视化结果
    
    参数:
    args: 命令行参数
    eval_results: 评估结果
    
    返回:
    None
    """
    logger.info("开始可视化结果...")
    
    # 为每个阶段可视化结果
    for stage in args.stages:
        if stage not in eval_results:
            logger.warning(f"阶段 {stage} 不在评估结果中，跳过")
            continue
        
        logger.info(f"可视化阶段 {stage} 的结果...")
        
        # 生成随机因果矩阵（实际应用中应从模型中提取）
        n_channels = len(args.channels) + 1  # +1 for ECG
        channel_names = args.channels + ['ECG']
        causal_matrix = np.random.rand(n_channels, n_channels) * 2 - 1  # 范围 [-1, 1]
        
        # 可视化因果矩阵
        output_dir = os.path.join(args.output_dir, 'causal_matrix', stage)
        visualize_causal_matrix(causal_matrix, channel_names, output_dir=output_dir, stage=stage)
        
        # 可视化因果图
        output_dir = os.path.join(args.output_dir, 'causal_graph', stage)
        visualize_causal_graph(causal_matrix, channel_names, threshold=0.3, output_dir=output_dir, stage=stage)
        
        # 生成随机频段因果强度（实际应用中应从模型中提取）
        frequency_bands = ['delta', 'theta', 'alpha', 'beta', 'gamma']
        channel_pairs = [(ch, 'ECG') for ch in args.channels[:3]]  # 只使用前3个通道
        causal_strength = np.random.rand(len(channel_pairs), len(frequency_bands))
        
        # 可视化频段因果强度
        output_dir = os.path.join(args.output_dir, 'frequency_analysis', stage)
        visualize_causal_strength_by_frequency(causal_strength, frequency_bands, channel_pairs, output_dir=output_dir, stage=stage)
        
        # 生成随机时间因果强度（实际应用中应从模型中提取）
        time_points = np.linspace(0, 10, 100)
        causal_strength_time = np.random.rand(len(channel_pairs), len(time_points))
        
        # 可视化时间因果强度
        output_dir = os.path.join(args.output_dir, 'time_analysis', stage)
        visualize_causal_strength_by_time(causal_strength_time, time_points, channel_pairs, output_dir=output_dir, stage=stage)
    
    logger.info("结果可视化完成")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 根据任务执行相应的功能
    if args.task in ['preprocess', 'full']:
        normalized_data = preprocess_data(args)
    else:
        # 加载预处理后的数据
        normalized_data = None
        logger.warning("跳过预处理步骤，需要手动加载预处理后的数据")
    
    if args.task in ['segment', 'full'] and normalized_data is not None:
        segmented_data = segment_data_task(args, normalized_data)
    else:
        # 加载分段后的数据
        segmented_data = None
        if args.task != 'preprocess':
            logger.warning("跳过分段步骤，需要手动加载分段后的数据")
    
    if args.task in ['extract', 'full'] and segmented_data is not None:
        features_data = extract_features_task(args, segmented_data)
    else:
        # 加载特征数据
        features_data = None
        if args.task not in ['preprocess', 'segment']:
            logger.warning("跳过特征提取步骤，需要手动加载特征数据")
    
    if args.task in ['train', 'evaluate', 'visualize', 'full'] and features_data is not None:
        model_inputs = prepare_model_inputs_task(args, features_data)
    else:
        # 加载模型输入数据
        model_inputs = None
        if args.task not in ['preprocess', 'segment', 'extract']:
            logger.warning("跳过模型输入准备步骤，需要手动加载模型输入数据")
    
    if args.task in ['train', 'evaluate', 'visualize', 'full'] and model_inputs is not None:
        history_dict, model_dict = train_model_task(args, model_inputs)
    else:
        # 加载训练好的模型
        history_dict, model_dict = None, None
        if args.task in ['evaluate', 'visualize']:
            logger.warning("跳过模型训练步骤，需要手动加载训练好的模型")
    
    if args.task in ['evaluate', 'visualize', 'full'] and model_inputs is not None and model_dict is not None:
        eval_results = evaluate_model_task(args, model_inputs, model_dict)
    else:
        # 加载评估结果
        eval_results = None
        if args.task == 'visualize':
            logger.warning("跳过模型评估步骤，需要手动加载评估结果")
    
    if args.task in ['visualize', 'full'] and eval_results is not None:
        visualize_results_task(args, eval_results)
    
    logger.info("程序执行完成")

if __name__ == "__main__":
    main()

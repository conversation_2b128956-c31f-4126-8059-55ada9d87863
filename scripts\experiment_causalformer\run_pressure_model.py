#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行CausalFormer心电-脑电因果关系的压力趋势预测模型

功能：
- 完整的管道，包含数据加载、特征提取、模型训练和评估
- 使用真实数据构建压力变化趋势预测模型
- 适配实验记录的非线性压力变化规律

作者：AI助手
日期：2025年5月22日
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import pandas as pd
import torch
import glob
from sklearn.preprocessing import StandardScaler
import mne
from scipy import stats
import argparse

# 导入模型和相关功能
from stage1_pressure_trend_model import (
    PressureTrendCausalModel, 
    train_pressure_trend_model,
    visualize_pressure_predictions,
    visualize_causal_heatmap,
    STAGE_ORDER,
    PRESSURE_TREND
)

# 导入其他必要的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from data_loader import load_h5_file, extract_stage_info, identify_channel_types

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"run_pressure_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-RunPressure")

# 自定义特征提取函数，替代之前导入的函数
def extract_time_features(data, sfreq):
    """
    从时域提取信号特征
    
    参数:
    data (np.ndarray): 信号数据，形状为 (n_channels, n_times) 或 (n_epochs, n_channels, n_times)
    sfreq (float): 采样频率
    
    返回:
    dict: 包含各种时域特征的字典
    """
    logger.info(f"提取时域特征: 数据形状 {data.shape}, 采样率 {sfreq}Hz")
    
    # 确保数据是3D的 (n_epochs, n_channels, n_times)
    if len(data.shape) == 2:
        data = data.reshape(1, *data.shape)
    
    n_epochs, n_channels, n_times = data.shape
    
    # 初始化特征字典
    features = {
        'mean': np.zeros((n_epochs, n_channels)),
        'std': np.zeros((n_epochs, n_channels)),
        'min': np.zeros((n_epochs, n_channels)),
        'max': np.zeros((n_epochs, n_channels)),
        'range': np.zeros((n_epochs, n_channels)),
        'rms': np.zeros((n_epochs, n_channels)),
        'skewness': np.zeros((n_epochs, n_channels)),
        'kurtosis': np.zeros((n_epochs, n_channels)),
        'energy': np.zeros((n_epochs, n_channels)),
        'zero_crossings': np.zeros((n_epochs, n_channels))
    }
    
    # 计算各种特征
    for i in range(n_epochs):
        for j in range(n_channels):
            signal = data[i, j, :]
            
            # 基本统计特征
            features['mean'][i, j] = np.mean(signal)
            features['std'][i, j] = np.std(signal)
            features['min'][i, j] = np.min(signal)
            features['max'][i, j] = np.max(signal)
            features['range'][i, j] = np.max(signal) - np.min(signal)
            features['rms'][i, j] = np.sqrt(np.mean(signal**2))
            features['skewness'][i, j] = stats.skew(signal)
            features['kurtosis'][i, j] = stats.kurtosis(signal)
            features['energy'][i, j] = np.sum(signal**2) / len(signal)
            
            # 过零点计数
            zero_crossings = np.where(np.diff(np.signbit(signal)))[0]
            features['zero_crossings'][i, j] = len(zero_crossings)
    
    return features

def extract_frequency_features(data, sfreq):
    """
    从频域提取信号特征
    
    参数:
    data (np.ndarray): 信号数据，形状为 (n_channels, n_times) 或 (n_epochs, n_channels, n_times)
    sfreq (float): 采样频率
    
    返回:
    dict: 包含各种频域特征的字典
    """
    logger.info(f"提取频域特征: 数据形状 {data.shape}, 采样率 {sfreq}Hz")
    
    # 确保数据是3D的 (n_epochs, n_channels, n_times)
    if len(data.shape) == 2:
        data = data.reshape(1, *data.shape)
    
    n_epochs, n_channels, n_times = data.shape
    
    # 定义频带
    bands = {
        'delta': (0.5, 4),
        'theta': (4, 8),
        'alpha': (8, 13),
        'beta': (13, 30),
        'gamma': (30, min(sfreq/2, 45))  # 不超过Nyquist频率
    }
    
    # 初始化特征字典
    features = {
        'dominant_frequency': np.zeros((n_epochs, n_channels)),
        'spectral_entropy': np.zeros((n_epochs, n_channels)),
        'total_power': np.zeros((n_epochs, n_channels))
    }
    
    # 为每个频带添加特征
    for band_name in bands:
        features[f'{band_name}_power'] = np.zeros((n_epochs, n_channels))
    
    # 计算频谱
    for i in range(n_epochs):
        for j in range(n_channels):
            signal = data[i, j, :]
            
            # 应用汉宁窗
            windowed_signal = signal * np.hanning(len(signal))
            
            # 计算频谱
            fft_result = np.fft.rfft(windowed_signal)
            fft_freq = np.fft.rfftfreq(len(signal), 1/sfreq)
            power_spectrum = np.abs(fft_result)**2
            
            # 计算总功率
            features['total_power'][i, j] = np.sum(power_spectrum)
            
            # 主频率
            dominant_idx = np.argmax(power_spectrum)
            features['dominant_frequency'][i, j] = fft_freq[dominant_idx]
            
            # 频谱熵
            norm_power = power_spectrum / np.sum(power_spectrum)
            entropy = -np.sum(norm_power * np.log2(norm_power + 1e-10))
            features['spectral_entropy'][i, j] = entropy
            
            # 各频带功率
            for band_name, (low_freq, high_freq) in bands.items():
                # 找到频带范围内的索引
                band_indices = np.where((fft_freq >= low_freq) & (fft_freq <= high_freq))[0]
                if len(band_indices) > 0:
                    band_power = np.sum(power_spectrum[band_indices])
                    features[f'{band_name}_power'][i, j] = band_power
    
    return features

def load_real_data(data_dir):
    """
    加载真实的EEG和ECG数据
    
    参数:
    data_dir (str): 数据目录路径
    
    返回:
    dict: 按阶段组织的原始数据
    """
    logger.info(f"开始加载真实数据: {data_dir}")
    
    # 查找所有fif文件
    fif_files = glob.glob(os.path.join(data_dir, "*.fif"))
    logger.info(f"找到 {len(fif_files)} 个fif文件")
    
    # 按阶段组织数据
    stage_data = {}
    
    for file_path in fif_files:
        file_name = os.path.basename(file_path)
        
        # 提取阶段信息
        stage = None
        for key in ['rest', 'test', 'prac']:
            if key in file_name.lower():
                if key == 'rest':
                    for i in range(1, 4):
                        if f'rest{i}' in file_name.lower():
                            stage = f'rest{i}'
                            break
                elif key == 'test':
                    for i in range(1, 4):
                        if f'test{i}' in file_name.lower():
                            stage = f'test{i}'
                            break
                else:
                    stage = 'prac'
                break
        
        if stage is None:
            logger.warning(f"无法识别文件的阶段: {file_name}，跳过")
            continue
        
        try:
            # 使用MNE加载fif文件
            raw = mne.io.read_raw_fif(file_path, preload=True)
            
            # 识别通道类型
            ch_names = raw.ch_names
            ch_types = identify_channel_types(ch_names)
            
            # 分离EEG和ECG通道
            eeg_channels = [ch for ch, ch_type in zip(ch_names, ch_types) if ch_type == 'eeg']
            ecg_channels = [ch for ch, ch_type in zip(ch_names, ch_types) if ch_type == 'ecg']
            
            # 如果没有足够的EEG或ECG通道，跳过
            if len(eeg_channels) < 5 or len(ecg_channels) < 1:
                logger.warning(f"文件 {file_name} 没有足够的通道: EEG={len(eeg_channels)}, ECG={len(ecg_channels)}")
                continue
            
            # 获取数据
            eeg_data = raw.get_data(picks=eeg_channels)
            ecg_data = raw.get_data(picks=ecg_channels)
            
            # 添加到阶段数据
            if stage not in stage_data:
                stage_data[stage] = {
                    'eeg_data': [],
                    'ecg_data': [],
                    'sfreq': raw.info['sfreq'],
                    'eeg_channels': eeg_channels,
                    'ecg_channels': ecg_channels
                }
            
            stage_data[stage]['eeg_data'].append(eeg_data)
            stage_data[stage]['ecg_data'].append(ecg_data)
            
            logger.info(f"成功加载 {file_name}, 阶段: {stage}, EEG通道: {len(eeg_channels)}, ECG通道: {len(ecg_channels)}")
        
        except Exception as e:
            logger.error(f"加载文件 {file_name} 时出错: {str(e)}")
            continue
    
    # 合并各阶段的数据
    for stage in stage_data:
        if stage_data[stage]['eeg_data'] and stage_data[stage]['ecg_data']:
            stage_data[stage]['eeg_data'] = np.concatenate(stage_data[stage]['eeg_data'], axis=0)
            stage_data[stage]['ecg_data'] = np.concatenate(stage_data[stage]['ecg_data'], axis=0)
    
    logger.info(f"完成数据加载，共 {len(stage_data)} 个阶段")
    return stage_data

def extract_features(stage_data):
    """
    从原始数据中提取EEG和ECG特征
    
    参数:
    stage_data (dict): 按阶段组织的原始数据
    
    返回:
    dict: 按阶段组织的特征数据
    """
    logger.info("开始提取特征...")
    
    feature_data = {}
    
    for stage, data in stage_data.items():
        logger.info(f"处理阶段 {stage} 的特征提取...")
        
        eeg_data = data['eeg_data']
        ecg_data = data['ecg_data']
        sfreq = data['sfreq']
        
        # 提取EEG特征
        eeg_time_features = extract_time_features(eeg_data, sfreq)
        eeg_freq_features = extract_frequency_features(eeg_data, sfreq)
        
        # 提取ECG特征
        ecg_time_features = extract_time_features(ecg_data, sfreq)
        ecg_freq_features = extract_frequency_features(ecg_data, sfreq)
        
        # 组合特征
        eeg_features = np.concatenate([
            np.array([v for v in eeg_time_features.values()]).T,
            np.array([v for v in eeg_freq_features.values()]).T
        ], axis=1)
        
        ecg_features = np.concatenate([
            np.array([v for v in ecg_time_features.values()]).T,
            np.array([v for v in ecg_freq_features.values()]).T
        ], axis=1)
        
        # 将特征保存到字典
        feature_data[stage] = {
            'eeg_features': eeg_features,
            'ecg_features': ecg_features
        }
        
        logger.info(f"  阶段 {stage} 特征提取完成: EEG特征形状={eeg_features.shape}, ECG特征形状={ecg_features.shape}")
    
    logger.info("特征提取完成")
    return feature_data

def standardize_features(feature_data):
    """
    标准化特征数据
    
    参数:
    feature_data (dict): 按阶段组织的特征数据
    
    返回:
    dict: 按阶段组织的标准化特征数据
    """
    logger.info("开始标准化特征...")
    
    # 合并所有阶段的特征以便全局标准化
    all_eeg_features = []
    all_ecg_features = []
    
    for stage, data in feature_data.items():
        all_eeg_features.append(data['eeg_features'])
        all_ecg_features.append(data['ecg_features'])
    
    all_eeg_features = np.vstack(all_eeg_features)
    all_ecg_features = np.vstack(all_ecg_features)
    
    # 创建标准化器
    eeg_scaler = StandardScaler()
    ecg_scaler = StandardScaler()
    
    # 拟合标准化器
    eeg_scaler.fit(all_eeg_features)
    ecg_scaler.fit(all_ecg_features)
    
    # 标准化各阶段特征
    standardized_data = {}
    
    for stage, data in feature_data.items():
        eeg_features = eeg_scaler.transform(data['eeg_features'])
        ecg_features = ecg_scaler.transform(data['ecg_features'])
        
        standardized_data[stage] = {
            'eeg_features': eeg_features,
            'ecg_features': ecg_features
        }
    
    logger.info("特征标准化完成")
    return standardized_data

def organize_stage_data(feature_data):
    """
    组织各阶段数据为训练序列
    
    参数:
    feature_data (dict): 按阶段组织的特征数据
    
    返回:
    tuple: (combined_eeg, combined_ecg, stages)
    """
    logger.info("开始组织阶段序列数据...")
    
    combined_eeg = []
    combined_ecg = []
    stages = []
    
    # 按照实验的时间顺序组织数据
    for stage in STAGE_ORDER:
        if stage in feature_data:
            eeg_features = feature_data[stage]['eeg_features']
            ecg_features = feature_data[stage]['ecg_features']
            
            # 确保EEG和ECG特征的样本数量相同
            min_samples = min(eeg_features.shape[0], ecg_features.shape[0])
            eeg_features = eeg_features[:min_samples]
            ecg_features = ecg_features[:min_samples]
            
            combined_eeg.append(eeg_features)
            combined_ecg.append(ecg_features)
            stages.extend([stage] * min_samples)
            
            logger.info(f"  阶段 {stage}: 添加 {min_samples} 个样本")
        else:
            logger.warning(f"  阶段 {stage} 不在数据集中")
    
    # 如果没有足够的数据，返回空值
    if not combined_eeg or not combined_ecg:
        logger.error("没有足够的数据用于组织序列")
        return None, None, None
    
    # 将各阶段数据堆叠成一个大数组
    try:
        combined_eeg = np.vstack(combined_eeg)
        combined_ecg = np.vstack(combined_ecg)
    except ValueError as e:
        logger.error(f"组织序列数据时出错: {str(e)}")
        return None, None, None
    
    logger.info(f"序列数据组织完成: EEG形状={combined_eeg.shape}, ECG形状={combined_ecg.shape}, 阶段数={len(stages)}")
    return combined_eeg, combined_ecg, stages

def create_pressure_labels(stages):
    """
    根据阶段创建压力趋势标签
    
    参数:
    stages (list): 阶段标签列表
    
    返回:
    np.array: 压力趋势标签
    """
    logger.info("创建压力趋势标签...")
    
    # 将阶段转换为压力值
    pressure_values = np.array([PRESSURE_TREND.get(stage, 0.0) for stage in stages])
    
    # 可以添加随机噪声使模型更稳健
    noise = np.random.normal(0, 0.05, pressure_values.shape)
    pressure_values = np.clip(pressure_values + noise, 0.0, 1.0)
    
    logger.info(f"压力标签创建完成，形状={pressure_values.shape}")
    return pressure_values

def visualize_data_distribution(combined_eeg, combined_ecg, pressure_values, stages, output_dir):
    """
    可视化数据分布
    
    参数:
    combined_eeg (np.array): EEG特征
    combined_ecg (np.array): ECG特征
    pressure_values (np.array): 压力趋势值
    stages (list): 阶段标签
    output_dir (str): 输出目录
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建阶段索引
    stage_indices = {}
    current_idx = 0
    for stage in STAGE_ORDER:
        if stage in stages:
            stage_count = stages.count(stage)
            stage_indices[stage] = (current_idx, current_idx + stage_count)
            current_idx += stage_count
    
    # 可视化压力趋势
    plt.figure(figsize=(12, 8))
    
    # 绘制阶段背景区域
    colors = ['#f0f0f0', '#e6f3ff', '#f0f0f0', '#e6f3ff', '#f0f0f0', '#e6f3ff']
    for i, stage in enumerate(STAGE_ORDER):
        if stage in stage_indices:
            start, end = stage_indices[stage]
            plt.axvspan(start, end, color=colors[i], alpha=0.3)
            mid_point = (start + end) / 2
            plt.text(mid_point, 1.1, stage, ha='center', fontsize=10)
    
    # 绘制压力趋势
    plt.plot(pressure_values, 'b-', linewidth=2, alpha=0.7)
    
    # 添加EEG和ECG特征均值作为参考
    eeg_mean = np.mean(combined_eeg, axis=1)
    ecg_mean = np.mean(combined_ecg, axis=1)
    
    # 标准化显示
    eeg_mean = (eeg_mean - np.min(eeg_mean)) / (np.max(eeg_mean) - np.min(eeg_mean))
    ecg_mean = (ecg_mean - np.min(ecg_mean)) / (np.max(ecg_mean) - np.min(ecg_mean))
    
    plt.plot(eeg_mean, 'r-', linewidth=1, alpha=0.5, label='EEG均值(标准化)')
    plt.plot(ecg_mean, 'g-', linewidth=1, alpha=0.5, label='ECG均值(标准化)')
    
    plt.xlabel('样本索引')
    plt.ylabel('压力水平')
    plt.title('压力趋势和信号均值分布')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.ylim([-0.1, 1.2])
    
    plt.savefig(os.path.join(output_dir, 'pressure_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 可视化各阶段的特征分布
    plt.figure(figsize=(12, 10))
    
    for i, stage in enumerate(STAGE_ORDER):
        if stage in stage_indices:
            start, end = stage_indices[stage]
            
            # 提取该阶段的特征
            stage_eeg = combined_eeg[start:end]
            stage_ecg = combined_ecg[start:end]
            
            # 计算该阶段的特征均值和标准差
            eeg_mean = np.mean(stage_eeg, axis=0)
            ecg_mean = np.mean(stage_ecg, axis=0)
            
            eeg_std = np.std(stage_eeg, axis=0)
            ecg_std = np.std(stage_ecg, axis=0)
            
            # 选择前10个特征显示
            plt.subplot(len(STAGE_ORDER), 2, 2*i+1)
            plt.bar(range(min(10, len(eeg_mean))), eeg_mean[:10], yerr=eeg_std[:10], alpha=0.7)
            plt.title(f'{stage} - EEG特征')
            plt.xlabel('特征索引')
            plt.ylabel('特征值')
            
            plt.subplot(len(STAGE_ORDER), 2, 2*i+2)
            plt.bar(range(min(10, len(ecg_mean))), ecg_mean[:10], yerr=ecg_std[:10], alpha=0.7)
            plt.title(f'{stage} - ECG特征')
            plt.xlabel('特征索引')
            plt.ylabel('特征值')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main(args):
    """主函数"""
    logger.info("开始执行压力趋势预测模型训练...")
    
    # 设置输出目录
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    if args.use_synthetic:
        # 使用合成数据进行测试
        from stage1_pressure_trend_model import prepare_stage_data
        stage_data = prepare_stage_data(args.data_dir)
    else:
        # 加载真实数据
        raw_data = load_real_data(args.data_dir)
        feature_data = extract_features(raw_data)
        stage_data = standardize_features(feature_data)
    
    # 组织序列数据
    combined_eeg, combined_ecg, stages = organize_stage_data(stage_data)
    
    if combined_eeg is None or combined_ecg is None or stages is None:
        logger.error("组织序列数据失败，终止程序")
        return
    
    # 创建压力标签
    pressure_values = create_pressure_labels(stages)
    
    # 可视化数据分布
    visualize_data_distribution(combined_eeg, combined_ecg, pressure_values, stages, output_dir)
    
    # 训练模型
    model = train_pressure_trend_model(stage_data, output_dir)
    
    logger.info("压力趋势预测模型执行完成!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='运行CausalFormer压力趋势预测模型')
    parser.add_argument('--data_dir', type=str, default='D:/ecgeeg/19-eegecg手动预处理6-ICA3',
                        help='数据目录路径')
    parser.add_argument('--output_dir', type=str, 
                        default='D:/ecgeeg/30-数据分析/5-NeuroKit2/result/causalformer/pressure_trend',
                        help='输出目录路径')
    parser.add_argument('--use_synthetic', action='store_true',
                        help='使用合成数据进行测试')
    
    args = parser.parse_args()
    main(args) 
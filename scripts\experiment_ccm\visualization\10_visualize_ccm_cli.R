# 命令行环境下的CCM可视化脚本

# 检查必要的包
required_packages <- c("tidyverse", "ggplot2", "viridis", "patchwork", "ggsci", "arrow")
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]

if (length(missing_packages) > 0) {
  cat("错误: 缺少必要的包:", paste(missing_packages, collapse = ", "), "\n")
  cat("请运行install_r_packages_cli.R安装必要的包\n")
  quit(status = 1)
}

# 加载必要的包
library(tidyverse)
library(ggplot2)
library(viridis)
library(patchwork)
library(ggsci)
library(arrow)  # 用于读取Parquet文件

# 设置主题 - 使用IEEE论文风格
theme_ieee <- function(base_size = 12, base_family = "Arial") {
  theme_minimal(base_size = base_size, base_family = base_family) +
    theme(
      # 文本元素
      plot.title = element_text(size = base_size * 1.2, face = "bold", hjust = 0.5),
      plot.subtitle = element_text(size = base_size, color = "gray30", hjust = 0.5),
      axis.title = element_text(size = base_size, face = "bold"),
      axis.text = element_text(size = base_size * 0.9),
      legend.title = element_text(size = base_size, face = "bold"),
      legend.text = element_text(size = base_size * 0.9),
      
      # 网格线和背景
      panel.grid.major = element_line(color = "gray90", size = 0.3),
      panel.grid.minor = element_blank(),
      panel.border = element_rect(fill = NA, color = "gray70", size = 0.5),
      
      # 图例位置
      legend.position = "bottom",
      legend.box = "horizontal",
      
      # 其他
      plot.margin = margin(10, 10, 10, 10)
    )
}

# 设置主题
theme_set(theme_ieee())

# 定义文件路径
ccm_results_path <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction/ccm_results_all_subjects.parquet"
output_dir <- "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_visualization"

# 创建输出目录（如果不存在）
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
  cat("创建输出目录:", output_dir, "\n")
}

# 读取CCM分析结果
cat("读取CCM分析结果...\n")
if (file.exists(ccm_results_path)) {
  ccm_data <- read_parquet(ccm_results_path)
  cat("成功读取数据，行数:", nrow(ccm_data), "\n")
} else {
  cat("错误: 找不到CCM结果文件:", ccm_results_path, "\n")
  quit(status = 1)
}

# 数据预处理
cat("处理数据...\n")
# 确保频段是有序因子
if ("band" %in% names(ccm_data)) {
  band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  ccm_data$band <- factor(ccm_data$band, levels = band_levels)
} else if ("frequency_band" %in% names(ccm_data)) {
  band_levels <- c("delta", "theta", "alpha", "beta", "gamma", "high_gamma")
  ccm_data$frequency_band <- factor(ccm_data$frequency_band, levels = band_levels)
  # 重命名列以保持一致性
  ccm_data <- ccm_data %>% rename(band = frequency_band)
}

# 确保方向性列存在
if (!("directionality" %in% names(ccm_data))) {
  if (all(c("eeg_to_heart", "heart_to_eeg") %in% names(ccm_data))) {
    ccm_data <- ccm_data %>%
      mutate(directionality = (heart_to_eeg - eeg_to_heart) / (heart_to_eeg + eeg_to_heart + 1e-10))
  } else if (all(c("eeg_to_hr", "hr_to_eeg") %in% names(ccm_data))) {
    ccm_data <- ccm_data %>%
      rename(eeg_to_heart = eeg_to_hr, heart_to_eeg = hr_to_eeg) %>%
      mutate(directionality = (heart_to_eeg - eeg_to_heart) / (heart_to_eeg + eeg_to_heart + 1e-10))
  }
}

# 按频段汇总
cat("按频段汇总数据...\n")
band_summary <- ccm_data %>%
  group_by(band) %>%
  summarize(
    mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
    se_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE) / sqrt(n()),
    mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
    se_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE) / sqrt(n()),
    mean_directionality = mean(directionality, na.rm = TRUE),
    se_directionality = sd(directionality, na.rm = TRUE) / sqrt(n()),
    .groups = "drop"
  )

# 创建频段比较图
cat("创建频段比较图...\n")
plot_data <- band_summary %>%
  select(band, mean_eeg_to_heart, se_eeg_to_heart, mean_heart_to_eeg, se_heart_to_eeg) %>%
  pivot_longer(
    cols = c(mean_eeg_to_heart, mean_heart_to_eeg),
    names_to = "direction",
    values_to = "causality"
  ) %>%
  mutate(
    se = ifelse(direction == "mean_eeg_to_heart", se_eeg_to_heart, se_heart_to_eeg),
    direction = case_when(
      direction == "mean_eeg_to_heart" ~ "脑 → 心",
      direction == "mean_heart_to_eeg" ~ "心 → 脑",
      TRUE ~ direction
    )
  )

# 设置频段标签
band_labels <- c(
  delta = "Delta (1-4 Hz)",
  theta = "Theta (4-8 Hz)",
  alpha = "Alpha (8-13 Hz)",
  beta = "Beta (13-30 Hz)",
  gamma = "Gamma (30-45 Hz)",
  high_gamma = "High Gamma (45-100 Hz)"
)

# 绘制条形图
p1 <- ggplot(plot_data, aes(x = band, y = causality, fill = direction)) +
  geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
  geom_errorbar(
    aes(ymin = causality - se, ymax = causality + se),
    position = position_dodge(width = 0.8),
    width = 0.25
  ) +
  scale_x_discrete(labels = band_labels) +
  scale_fill_nejm() +  # New England Journal of Medicine配色
  labs(
    title = "不同频段的因果强度比较",
    subtitle = "基于CCM分析的脑-心交互",
    x = "频段",
    y = "因果强度",
    fill = "方向"
  ) +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    legend.position = "bottom"
  )

# 创建方向性指数图
p2 <- ggplot(band_summary, aes(x = band, y = mean_directionality, fill = mean_directionality)) +
  geom_bar(stat = "identity", width = 0.7) +
  geom_errorbar(
    aes(ymin = mean_directionality - se_directionality, 
        ymax = mean_directionality + se_directionality),
    width = 0.25
  ) +
  geom_hline(yintercept = 0, linetype = "dashed", color = "gray50") +
  scale_x_discrete(labels = band_labels) +
  scale_fill_gradient2(
    low = "#2166AC", 
    mid = "white", 
    high = "#B2182B", 
    midpoint = 0,
    guide = "none"
  ) +
  labs(
    title = "脑-心交互的方向性指数",
    subtitle = "正值表示心→脑方向更强，负值表示脑→心方向更强",
    x = "频段",
    y = "方向性指数",
    caption = "方向性指数 = (心→脑 - 脑→心)/(心→脑 + 脑→心)"
  ) +
  theme(
    axis.text.x = element_text(angle = 45, hjust = 1),
    panel.grid.major.x = element_blank()
  ) +
  ylim(-0.5, 0.5)  # 设置y轴范围，使图形更加平衡

# 组合图表
combined_plot <- p1 / p2
combined_plot <- combined_plot + plot_annotation(
  title = "脑-心交互的因果分析",
  subtitle = "基于收敛交叉映射(CCM)方法",
  caption = paste("数据来源: ", nrow(ccm_data), "个CCM分析结果"),
  theme = theme(plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
                plot.subtitle = element_text(size = 14, hjust = 0.5))
)

# 保存图表
cat("保存图表...\n")
ggsave(file.path(output_dir, "ccm_frequency_comparison.png"), combined_plot, width = 10, height = 12, dpi = 300)
ggsave(file.path(output_dir, "ccm_frequency_comparison.pdf"), combined_plot, width = 10, height = 12)

# 如果数据中有阶段信息，创建阶段比较图
if ("stage" %in% names(ccm_data)) {
  cat("创建阶段比较图...\n")
  
  # 按阶段和频段汇总
  stage_summary <- ccm_data %>%
    group_by(stage, band) %>%
    summarize(
      mean_eeg_to_heart = mean(eeg_to_heart, na.rm = TRUE),
      se_eeg_to_heart = sd(eeg_to_heart, na.rm = TRUE) / sqrt(n()),
      mean_heart_to_eeg = mean(heart_to_eeg, na.rm = TRUE),
      se_heart_to_eeg = sd(heart_to_eeg, na.rm = TRUE) / sqrt(n()),
      .groups = "drop"
    )
  
  # 设置阶段标签
  stage_labels <- c(
    prac = "练习",
    test = "测试",
    rest = "休息",
    test1 = "测试1",
    test2 = "测试2",
    test3 = "测试3",
    rest1 = "休息1",
    rest2 = "休息2",
    rest3 = "休息3"
  )
  
  # 准备绘图数据
  stage_plot_data <- stage_summary %>%
    pivot_longer(
      cols = c(mean_eeg_to_heart, mean_heart_to_eeg),
      names_to = "direction",
      values_to = "causality"
    ) %>%
    mutate(
      se = ifelse(direction == "mean_eeg_to_heart", se_eeg_to_heart, se_heart_to_eeg),
      direction = case_when(
        direction == "mean_eeg_to_heart" ~ "脑 → 心",
        direction == "mean_heart_to_eeg" ~ "心 → 脑",
        TRUE ~ direction
      ),
      stage_label = stage_labels[stage]
    )
  
  # 绘制阶段比较图
  p3 <- ggplot(stage_plot_data, aes(x = stage, y = causality, fill = direction)) +
    geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.7) +
    geom_errorbar(
      aes(ymin = causality - se, ymax = causality + se),
      position = position_dodge(width = 0.8),
      width = 0.25
    ) +
    scale_x_discrete(labels = ~stage_labels[.x]) +
    scale_fill_jama() +  # Journal of the American Medical Association配色
    labs(
      title = "不同任务阶段的因果强度比较",
      x = "任务阶段",
      y = "因果强度",
      fill = "方向"
    ) +
    theme(
      axis.text.x = element_text(angle = 45, hjust = 1),
      legend.position = "bottom"
    ) +
    facet_wrap(~ band, nrow = 2)
  
  # 保存阶段比较图
  ggsave(file.path(output_dir, "ccm_stages_comparison.png"), p3, width = 12, height = 8, dpi = 300)
  ggsave(file.path(output_dir, "ccm_stages_comparison.pdf"), p3, width = 12, height = 8)
}

# 保存汇总数据
cat("保存汇总数据...\n")
write.csv(band_summary, file.path(output_dir, "ccm_band_summary.csv"), row.names = FALSE)

if (exists("stage_summary")) {
  write.csv(stage_summary, file.path(output_dir, "ccm_stage_summary.csv"), row.names = FALSE)
}

cat("\n可视化完成！结果已保存到:", output_dir, "\n")
cat("生成的文件:\n")
cat("1. ccm_frequency_comparison.png/pdf\n")
if ("stage" %in% names(ccm_data)) {
  cat("2. ccm_stages_comparison.png/pdf\n")
}
cat("3. ccm_band_summary.csv\n")
if (exists("stage_summary")) {
  cat("4. ccm_stage_summary.csv\n")
}

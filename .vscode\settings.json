{
    "github.copilot.enable": {
        "*": false
    },
    "terminal.integrated.automationProfile.windows": {},
    "terminal.integrated.bellDuration": 10000,
    "augment.disableFocusOnEmptyWorkspace": true,
    "augment.nextEdit.enableGlobalBackgroundSuggestions": true,
    "augment.nextEdit.showDiffInHover": true,
    "augment.nextEdit.highlightSuggestionsInTheEditor": true,
    "terminal.integrated.defaultProfile.windows": "Git Bash",
    "terminal.integrated.profiles.windows": {
        "Git Bash": {
            "path": "C:\\Program Files\\Git\\bin\\bash.exe",
            "args": ["--login", "-i"]
        }
    },
    "augment.advanced": {

    },
    // Shell集成配置
    "terminal.integrated.shellIntegration.enabled": true,
    "terminal.integrated.shellIntegration.decorationsEnabled": "both",
    "terminal.integrated.shellIntegration.showCommandGuide": true,
    "terminal.integrated.stickyScroll.enabled": true,
    "terminal.integrated.shellIntegration.history": 10000,
    // 配置终端启动时执行的命令
    "terminal.integrated.shellArgs.windows": [
        "--login",
        "-i",
        "-c",
        "source ~/terminal_config.sh"
    ],
    "terminal.integrated.fontFamily": "'Sarasa Term SC', 'Fira Code', 'Source Code Pro', Consolas, 'Courier New', monospace",    
    "terminal.integrated.fontSize": 14,
    "terminal.integrated.cursorBlinking": true,
    "terminal.integrated.cursorStyle": "line",
    "terminal.integrated.fontWeight": "normal",
    "terminal.integrated.letterSpacing": 0,
    "terminal.integrated.lineHeight": 1.0,
    "terminal.integrated.minimumContrastRatio": 4.5,
    "terminal.integrated.gpuAcceleration": "on",
    "terminal.integrated.copyOnSelection": true,
    "terminal.integrated.rightClickBehavior": "selectWord",
    "terminal.integrated.padding": 4,
    "terminal.integrated.scrollback": 5000,
    "workbench.colorCustomizations": {
        "terminal.background": "#000000",
        "terminal.foreground": "#E0E0E0",
        "terminalCursor.background": "#E0E0E0",
        "terminalCursor.foreground": "#E0E0E0",
        "terminal.ansiBlack": "#1A1A1A",
        "terminal.ansiBlue": "#6699CC",
        "terminal.ansiBrightBlack": "#666666",
        "terminal.ansiBrightBlue": "#66CCFF",
        "terminal.ansiBrightCyan": "#66CCCC",
        "terminal.ansiBrightGreen": "#99CC99",
        "terminal.ansiBrightMagenta": "#CC99CC",
        "terminal.ansiBrightRed": "#FF6666",
        "terminal.ansiBrightWhite": "#FFFFFF",
        "terminal.ansiBrightYellow": "#FFCC66",
        "terminal.ansiCyan": "#66CCCC",
        "terminal.ansiGreen": "#99CC99",
        "terminal.ansiMagenta": "#CC99CC",
        "terminal.ansiRed": "#F92672",
        "terminal.ansiWhite": "#E0E0E0",
        "terminal.ansiYellow": "#FFCC66",
        "terminal.selectionBackground": "#333333"
    },
    "terminal.integrated.tabs.enabled": true,
    "terminal.integrated.tabs.location": "bottom",
    "terminal.integrated.shell.windows": "C:\\Program Files\\Git\\bin\\bash.exe"
}

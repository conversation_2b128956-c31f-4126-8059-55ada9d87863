# Autoformer复现实验

本实验旨在复现Autoformer模型，通过直接运行原始源码，获得与论文中相同的结果，并为后续与CausalFormer的整合做准备。

## 实验目标

1. 在本地运行Autoformer源码，复现论文中的实验结果
2. 理解Autoformer的核心组件和工作原理
3. 分析Autoformer与CausalFormer的异同
4. 探索两个模型整合的可能性

## 项目结构

```
experiment_autoformer/
├── data/                  # 数据目录
├── scripts/               # 运行脚本
│   ├── run_ett.py         # 运行ETT数据集实验
│   ├── run_synthetic.py   # 运行合成数据集实验
│   └── compare_models.py  # 比较模型性能
├── results/               # 结果保存目录
└── README.md              # 实验说明
```

## 环境要求

- Python 3.8+
- PyTorch 1.9.0+
- CUDA 11.3+（用于GPU加速）
- 其他依赖见requirements.txt

## 使用方法

1. 安装依赖：
```bash
conda activate tdspy_data
pip install -r requirements.txt
```

2. 准备数据：
```bash
python scripts/prepare_data.py
```

3. 训练模型：
```bash
python scripts/run_autoformer.py
```

4. 评估模型：
```bash
python scripts/evaluate_model.py
```

## 参考

- 原始论文：Autoformer: Decomposition Transformers with Auto-Correlation for Long-Term Series Forecasting
- 原始代码：https://github.com/thuml/Autoformer

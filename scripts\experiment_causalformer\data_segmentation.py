#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 数据分段模块

功能：
- 将标准化后的数据按照不同策略进行分段
- 支持固定长度分段、重叠分段、事件相关分段
- 提供分段数据的质量评估
- 支持分段数据的可视化

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
from scipy import stats
import seaborn as sns
from matplotlib.gridspec import GridSpec

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_segmentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-Segmentation")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

# 自定义异常类
class DataSegmentationError(Exception):
    """数据分段错误"""
    pass

def segment_data_fixed_length(data, segment_length, overlap=0, min_segments=1):
    """
    将数据按固定长度进行分段
    
    参数:
    data (np.ndarray): 输入数据，形状为 (epochs, channels, time_points)
    segment_length (int): 每个分段的长度（时间点数）
    overlap (float): 重叠比例，范围[0, 1)
    min_segments (int): 最小分段数，如果分段数小于此值，则抛出异常
    
    返回:
    np.ndarray: 分段后的数据，形状为 (segments, channels, segment_length)
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为numpy.ndarray"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为(epochs, channels, time_points)"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    if overlap < 0 or overlap >= 1:
        error_msg = f"重叠比例错误: {overlap}, 应在[0, 1)范围内"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    epochs, channels, time_points = data.shape
    
    if segment_length > time_points:
        error_msg = f"分段长度({segment_length})大于数据长度({time_points})"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    # 计算步长
    step = int(segment_length * (1 - overlap))
    if step <= 0:
        step = 1
        logger.warning(f"计算的步长为0，已调整为1")
    
    # 计算每个epoch的分段数
    segments_per_epoch = max(1, (time_points - segment_length) // step + 1)
    total_segments = epochs * segments_per_epoch
    
    if total_segments < min_segments:
        error_msg = f"分段数({total_segments})小于最小要求({min_segments})"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    logger.info(f"数据分段: 每个epoch分为{segments_per_epoch}段，总共{total_segments}段")
    logger.info(f"分段长度: {segment_length}, 步长: {step}, 重叠比例: {overlap}")
    
    # 创建分段数据数组
    segmented_data = np.zeros((total_segments, channels, segment_length))
    
    # 填充分段数据
    segment_idx = 0
    for epoch in range(epochs):
        for i in range(0, time_points - segment_length + 1, step):
            segmented_data[segment_idx] = data[epoch, :, i:i+segment_length]
            segment_idx += 1
            if segment_idx >= total_segments:
                break
    
    return segmented_data

def segment_data_event_related(data, event_indices, pre_event=100, post_event=400, min_segments=1):
    """
    根据事件索引进行事件相关分段
    
    参数:
    data (np.ndarray): 输入数据，形状为 (epochs, channels, time_points)
    event_indices (list): 事件发生的时间点索引列表
    pre_event (int): 事件前的时间点数
    post_event (int): 事件后的时间点数
    min_segments (int): 最小分段数，如果分段数小于此值，则抛出异常
    
    返回:
    np.ndarray: 分段后的数据，形状为 (segments, channels, pre_event+post_event)
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为numpy.ndarray"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为(epochs, channels, time_points)"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    epochs, channels, time_points = data.shape
    segment_length = pre_event + post_event
    
    if not event_indices:
        error_msg = "事件索引列表为空"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    # 过滤有效的事件索引
    valid_events = []
    for epoch in range(epochs):
        for event_idx in event_indices:
            if event_idx - pre_event >= 0 and event_idx + post_event <= time_points:
                valid_events.append((epoch, event_idx))
    
    total_segments = len(valid_events)
    
    if total_segments < min_segments:
        error_msg = f"有效分段数({total_segments})小于最小要求({min_segments})"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    logger.info(f"事件相关分段: 总共{total_segments}段")
    logger.info(f"事件前时间点数: {pre_event}, 事件后时间点数: {post_event}")
    
    # 创建分段数据数组
    segmented_data = np.zeros((total_segments, channels, segment_length))
    
    # 填充分段数据
    for i, (epoch, event_idx) in enumerate(valid_events):
        start_idx = event_idx - pre_event
        end_idx = event_idx + post_event
        segmented_data[i] = data[epoch, :, start_idx:end_idx]
    
    return segmented_data

def segment_data_adaptive(data, method='variance', threshold=0.8, min_length=100, max_length=500, min_segments=1):
    """
    自适应分段，根据信号特性动态确定分段点
    
    参数:
    data (np.ndarray): 输入数据，形状为 (epochs, channels, time_points)
    method (str): 分段方法，'variance'表示基于方差变化，'correlation'表示基于相关性
    threshold (float): 分段阈值
    min_length (int): 最小分段长度
    max_length (int): 最大分段长度
    min_segments (int): 最小分段数，如果分段数小于此值，则抛出异常
    
    返回:
    list: 分段后的数据列表，每个元素是一个形状为(channels, segment_length)的numpy数组
    list: 分段点列表，每个元素是一个(epoch, start_idx, end_idx)元组
    """
    if not isinstance(data, np.ndarray):
        error_msg = f"输入数据类型错误: {type(data)}, 应为numpy.ndarray"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    if len(data.shape) != 3:
        error_msg = f"输入数据维度错误: {data.shape}, 应为(epochs, channels, time_points)"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    epochs, channels, time_points = data.shape
    
    if min_length >= max_length:
        error_msg = f"最小分段长度({min_length})应小于最大分段长度({max_length})"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    if max_length > time_points:
        max_length = time_points
        logger.warning(f"最大分段长度已调整为数据长度: {max_length}")
    
    segmented_data = []
    segment_points = []
    
    for epoch in range(epochs):
        epoch_data = data[epoch]
        
        if method == 'variance':
            # 计算滑动窗口方差
            window_size = min_length // 2
            variances = np.zeros(time_points - window_size + 1)
            for i in range(len(variances)):
                variances[i] = np.var(epoch_data[:, i:i+window_size])
            
            # 归一化方差
            if np.max(variances) > np.min(variances):
                variances = (variances - np.min(variances)) / (np.max(variances) - np.min(variances))
            
            # 找出方差变化大的点作为分段点
            segment_candidates = [0]
            for i in range(1, len(variances)-1):
                if (variances[i] > variances[i-1] and variances[i] > variances[i+1] and 
                    variances[i] > threshold and i - segment_candidates[-1] >= min_length):
                    segment_candidates.append(i)
            segment_candidates.append(time_points)
            
        elif method == 'correlation':
            # 基于相关性的分段
            segment_candidates = [0]
            ref_segment = epoch_data[:, 0:min_length]
            
            for i in range(min_length, time_points - min_length, min_length // 2):
                curr_segment = epoch_data[:, i:i+min_length]
                corr = np.corrcoef(ref_segment.flatten(), curr_segment.flatten())[0, 1]
                
                if abs(corr) < threshold and i - segment_candidates[-1] >= min_length:
                    segment_candidates.append(i)
                    ref_segment = curr_segment
            
            segment_candidates.append(time_points)
        
        else:
            error_msg = f"不支持的分段方法: {method}"
            logger.error(error_msg)
            raise DataSegmentationError(error_msg)
        
        # 根据候选点进行分段
        for i in range(len(segment_candidates) - 1):
            start_idx = segment_candidates[i]
            end_idx = segment_candidates[i+1]
            
            # 确保分段长度在范围内
            if end_idx - start_idx < min_length:
                continue
            if end_idx - start_idx > max_length:
                # 将过长的分段进一步划分
                sub_segments = (end_idx - start_idx) // max_length + 1
                sub_length = (end_idx - start_idx) // sub_segments
                
                for j in range(sub_segments):
                    sub_start = start_idx + j * sub_length
                    sub_end = min(sub_start + sub_length, end_idx)
                    segmented_data.append(epoch_data[:, sub_start:sub_end])
                    segment_points.append((epoch, sub_start, sub_end))
            else:
                segmented_data.append(epoch_data[:, start_idx:end_idx])
                segment_points.append((epoch, start_idx, end_idx))
    
    total_segments = len(segmented_data)
    
    if total_segments < min_segments:
        error_msg = f"分段数({total_segments})小于最小要求({min_segments})"
        logger.error(error_msg)
        raise DataSegmentationError(error_msg)
    
    logger.info(f"自适应分段({method}): 总共{total_segments}段")
    
    return segmented_data, segment_points

def visualize_segmentation(data, segmented_data, segment_points=None, method='fixed', output_dir=None):
    """
    可视化分段结果
    
    参数:
    data (np.ndarray): 原始数据，形状为 (epochs, channels, time_points)
    segmented_data (np.ndarray or list): 分段后的数据
    segment_points (list): 分段点列表，用于自适应分段
    method (str): 分段方法，'fixed', 'event', 'adaptive'
    output_dir (str): 输出目录
    
    返回:
    str: 保存的图像文件路径
    """
    # 创建输出目录
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\segmentation")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 选择第一个epoch和第一个通道进行可视化
    epoch_idx = 0
    channel_idx = 0
    
    if len(data.shape) == 3:
        original_data = data[epoch_idx, channel_idx, :]
    else:
        original_data = data[channel_idx, :]
    
    plt.figure(figsize=(15, 10))
    
    # 绘制原始数据
    plt.subplot(2, 1, 1)
    plt.plot(original_data, label='原始数据')
    plt.title(f'原始数据 / Original Data (Channel {channel_idx})', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('时间点 / Time Points', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
    plt.legend(prop=chinese_font)
    
    # 绘制分段结果
    plt.subplot(2, 1, 2)
    
    if method == 'fixed' or method == 'event':
        # 固定长度或事件相关分段
        if isinstance(segmented_data, np.ndarray):
            for i in range(min(5, segmented_data.shape[0])):
                segment = segmented_data[i, channel_idx, :]
                plt.plot(segment, label=f'分段 {i+1}')
        else:
            for i, segment in enumerate(segmented_data[:5]):
                plt.plot(segment[channel_idx, :], label=f'分段 {i+1}')
        
        plt.title(f'分段结果 / Segmented Data ({method})', fontproperties=chinese_font, fontsize=10)
        plt.xlabel('时间点 / Time Points', fontproperties=chinese_font, fontsize=10)
        plt.ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
        plt.legend(prop=chinese_font)
    
    elif method == 'adaptive':
        # 自适应分段
        plt.plot(original_data, label='原始数据')
        
        if segment_points:
            for i, (ep, start, end) in enumerate(segment_points):
                if ep == epoch_idx:
                    plt.axvline(x=start, color='r', linestyle='--')
                    plt.axvline(x=end, color='g', linestyle='--')
                    if i < 5:  # 只标注前5个分段点
                        plt.text(start, np.max(original_data), f'S{i+1}', color='r')
                        plt.text(end, np.max(original_data), f'E{i+1}', color='g')
        
        plt.title(f'自适应分段结果 / Adaptive Segmentation', fontproperties=chinese_font, fontsize=10)
        plt.xlabel('时间点 / Time Points', fontproperties=chinese_font, fontsize=10)
        plt.ylabel('振幅 / Amplitude', fontproperties=chinese_font, fontsize=10)
        plt.legend(prop=chinese_font)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"segmentation_results_{method}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"分段可视化结果已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def test_data_segmentation(normalized_data):
    """
    测试数据分段模块
    
    参数:
    normalized_data (dict): 标准化后的数据
    
    返回:
    bool: 测试结果
    """
    logger.info("开始测试数据分段模块...")
    
    try:
        # 选择一个阶段进行测试
        stage = list(normalized_data.keys())[0]
        data = normalized_data[stage]
        
        eeg_data = data['eeg']
        ecg_data = data['ecg']
        
        # 测试固定长度分段
        logger.info("测试固定长度分段...")
        segment_length = 100  # 200ms @ 500Hz
        eeg_segments = segment_data_fixed_length(eeg_data, segment_length)
        ecg_segments = segment_data_fixed_length(ecg_data, segment_length)
        
        # 测试带重叠的固定长度分段
        logger.info("测试带重叠的固定长度分段...")
        overlap = 0.5
        eeg_segments_overlap = segment_data_fixed_length(eeg_data, segment_length, overlap)
        ecg_segments_overlap = segment_data_fixed_length(ecg_data, segment_length, overlap)
        
        # 测试事件相关分段
        logger.info("测试事件相关分段...")
        # 模拟事件索引
        time_points = eeg_data.shape[2]
        event_indices = [int(time_points * 0.25), int(time_points * 0.5), int(time_points * 0.75)]
        eeg_segments_event = segment_data_event_related(eeg_data, event_indices)
        ecg_segments_event = segment_data_event_related(ecg_data, event_indices)
        
        # 测试自适应分段
        logger.info("测试自适应分段...")
        eeg_segments_adaptive, eeg_segment_points = segment_data_adaptive(eeg_data)
        ecg_segments_adaptive, ecg_segment_points = segment_data_adaptive(ecg_data)
        
        # 可视化分段结果
        logger.info("可视化分段结果...")
        visualize_segmentation(eeg_data, eeg_segments, method='fixed')
        visualize_segmentation(eeg_data, eeg_segments_overlap, method='fixed')
        visualize_segmentation(eeg_data, eeg_segments_event, method='event')
        visualize_segmentation(eeg_data, eeg_segments_adaptive, eeg_segment_points, method='adaptive')
        
        logger.info("数据分段模块测试成功!")
        return True
    
    except DataSegmentationError as e:
        logger.error(f"数据分段失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 导入数据加载、通道处理和标准化模块
    from data_loader import load_hep_data
    from channel_processor import process_channels
    from data_normalizer import normalize_data
    
    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)
    
    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)
    
    # 标准化数据
    normalized_data = normalize_data(processed_data, method='z_score')
    
    # 测试数据分段
    test_data_segmentation(normalized_data)

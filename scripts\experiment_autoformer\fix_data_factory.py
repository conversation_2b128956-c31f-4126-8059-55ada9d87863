"""
修复data_factory.py文件中的缩进问题
"""
import os
import shutil

def fix_data_factory():
    """修复data_factory.py中的缩进问题"""
    print("修复data_factory.py...")
    
    # 设置路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    autoformer_dir = os.path.join(os.path.dirname(script_dir), "..", "Autoformer")
    data_factory_path = os.path.join(autoformer_dir, "data_provider", "data_factory.py")
    
    # 备份原始文件
    backup_path = data_factory_path + ".bak"
    if not os.path.exists(backup_path):
        shutil.copy2(data_factory_path, backup_path)
        print(f"已备份原始文件到 {backup_path}")
    
    # 修复缩进问题
    fixed_content = """from data_provider.data_loader import Dataset_ETT_hour, Dataset_ETT_minute, Dataset_Custom, Dataset_Pred
from torch.utils.data import DataLoader

data_dict = {
    'ETTh1': Dataset_ETT_hour,
    'ETTh2': Dataset_ETT_hour,
    'ETTm1': Dataset_ETT_minute,
    'ETTm2': Dataset_ETT_minute,
    'custom': Dataset_Custom,
}


def data_provider(args, flag):
    Data = data_dict[args.data]
    timeenc = 0 if args.embed != 'timeF' else 1

    if flag == 'test':
        shuffle_flag = False
        drop_last = False
        batch_size = args.batch_size
        freq = args.freq
    elif flag == 'pred':
        shuffle_flag = False
        drop_last = False
        batch_size = 1
        freq = args.freq
        Data = Dataset_Pred
    else:
        shuffle_flag = True
        drop_last = True
        batch_size = args.batch_size
        freq = args.freq

    data_set = Data(
        root_path=args.root_path,
        data_path=args.data_path,
        flag=flag,
        size=[args.seq_len, args.label_len, args.pred_len],
        features=args.features,
        target=args.target,
        timeenc=timeenc,
        freq=freq
    )
    
    try:
        print(flag, len(data_set))
    except Exception as e:
        print(f"Warning: {e}")
        
    data_loader = DataLoader(
        data_set,
        batch_size=batch_size,
        shuffle=shuffle_flag,
        num_workers=args.num_workers,
        drop_last=drop_last)
    return data_set, data_loader
"""
    
    # 写入修改后的文件
    with open(data_factory_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"已修复 {data_factory_path}")
    return True

if __name__ == "__main__":
    fix_data_factory()

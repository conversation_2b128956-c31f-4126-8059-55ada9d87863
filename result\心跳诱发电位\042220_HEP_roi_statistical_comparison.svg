<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="856.461875pt" height="712.592858pt" viewBox="0 0 856.461875 712.592858" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-21T15:29:15.508370</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M -0 712.592858 
L 856.461875 712.592858 
L 856.461875 0 
L -0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 47.059375 309.003205 
L 422.661875 309.003205 
L 422.661875 20.459375 
L 47.059375 20.459375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_3">
    <path d="M 64.132216 160.201829 
L 95.173745 160.201829 
L 95.173745 215.841403 
L 64.132216 215.841403 
z
" clip-path="url(#pa81449ea42)" style="fill: #e63946; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 115.868097 160.201829 
L 146.909626 160.201829 
L 146.909626 256.416074 
L 115.868097 256.416074 
z
" clip-path="url(#pa81449ea42)" style="fill: #457b9d; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 167.603979 160.201829 
L 198.645508 160.201829 
L 198.645508 190.74861 
L 167.603979 190.74861 
z
" clip-path="url(#pa81449ea42)" style="fill: #43aa8b; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_6">
    <path d="M 219.339861 160.201829 
L 250.381389 160.201829 
L 250.381389 219.244404 
L 219.339861 219.244404 
z
" clip-path="url(#pa81449ea42)" style="fill: #577590; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_7">
    <path d="M 271.075742 160.201829 
L 302.117271 160.201829 
L 302.117271 192.130367 
L 271.075742 192.130367 
z
" clip-path="url(#pa81449ea42)" style="fill: #1d3557; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_8">
    <path d="M 322.811624 160.201829 
L 353.853153 160.201829 
L 353.853153 196.259448 
L 322.811624 196.259448 
z
" clip-path="url(#pa81449ea42)" style="fill: #254441; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_9">
    <path d="M 374.547505 160.201829 
L 405.589034 160.201829 
L 405.589034 183.700237 
L 374.547505 183.700237 
z
" clip-path="url(#pa81449ea42)" style="fill: #2a3d45; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="md3fd86860a" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#md3fd86860a" x="79.65298" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 练习阶段 -->
      <g transform="translate(50.152264 349.576414) rotate(-45) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-7ec3" d="M 1805 1888 
Q 2074 1933 2240 1961 
Q 2406 1990 2496 1984 
Q 2586 1978 2586 1926 
Q 2573 1837 2201 1680 
Q 1830 1523 1404 1414 
Q 979 1306 912 1312 
Q 845 1318 800 1357 
Q 685 1504 653 1609 
Q 621 1715 621 1728 
Q 621 1786 685 1779 
L 781 1773 
Q 928 1760 1062 1766 
Q 1267 2131 1446 2490 
L 1510 2605 
Q 1050 2938 602 3149 
Q 499 3200 486 3251 
Q 474 3302 515 3401 
Q 557 3501 614 3513 
Q 672 3526 781 3482 
Q 800 3475 813 3469 
Q 1286 4198 1389 4480 
Q 1498 4774 1498 4915 
L 1498 4979 
Q 1498 5050 1542 5050 
Q 1574 5050 1658 4998 
Q 1888 4851 1888 4717 
Q 1850 4557 1651 4186 
Q 1478 3859 1120 3296 
L 1274 3206 
Q 1523 3059 1690 2944 
Q 2048 3654 2080 3840 
Q 2086 3898 2089 3994 
Q 2093 4090 2125 4090 
Q 2157 4090 2240 4045 
Q 2470 3917 2477 3776 
Q 2483 3731 2464 3686 
Q 1990 2707 1485 1830 
Q 1638 1862 1805 1888 
z
M 1485 627 
Q 2003 845 2304 989 
Q 2605 1133 2662 1139 
Q 2720 1146 2720 1126 
Q 2733 986 1840 442 
Q 947 -102 794 -115 
Q 749 -122 669 -45 
Q 589 32 531 128 
Q 474 224 467 275 
Q 467 301 525 307 
L 589 314 
Q 685 320 864 378 
L 1485 627 
z
M 2829 1882 
L 3213 2803 
L 2976 2790 
Q 2701 2752 2656 2803 
Q 2611 2854 2524 2966 
Q 2438 3078 2438 3148 
Q 2438 3219 2458 3219 
Q 2650 3174 2810 3174 
L 2848 3181 
L 3379 3219 
Q 3520 3603 3610 3866 
L 3168 3834 
L 3059 3834 
Q 2854 3834 2803 3885 
Q 2650 4019 2650 4192 
Q 2650 4224 2669 4224 
L 3002 4192 
L 3053 4192 
L 3731 4224 
Q 3891 4768 3910 4873 
Q 3930 4979 3926 5062 
Q 3923 5146 3926 5190 
Q 3930 5235 3994 5235 
Q 4058 5235 4160 5178 
Q 4371 5075 4352 4941 
L 4352 4928 
Q 4326 4877 4147 4250 
L 5114 4307 
Q 5171 4314 5244 4317 
Q 5318 4320 5372 4339 
Q 5427 4358 5500 4358 
Q 5574 4358 5692 4265 
Q 5811 4173 5811 4083 
Q 5811 3994 5651 3974 
L 4032 3891 
Q 3942 3622 3808 3258 
L 4301 3302 
L 4365 3302 
Q 4493 3302 4560 3222 
Q 4627 3142 4627 3085 
L 4595 2918 
L 4589 2176 
L 5037 2208 
Q 5101 2214 5174 2217 
Q 5248 2221 5296 2240 
Q 5344 2259 5421 2259 
Q 5498 2259 5613 2163 
Q 5728 2067 5728 1977 
Q 5728 1888 5568 1875 
L 4595 1811 
L 4576 1811 
L 4589 -45 
L 4602 -250 
Q 4602 -429 4422 -499 
Q 4358 -538 4307 -538 
Q 4256 -538 4080 -445 
Q 3904 -352 3731 -230 
Q 3277 77 3277 218 
Q 3277 256 3325 256 
Q 3373 256 3577 157 
Q 3782 58 4198 -38 
L 4179 1786 
L 3411 1722 
L 3270 1702 
Q 3213 1696 3168 1680 
Q 3123 1664 3052 1661 
Q 2982 1658 2896 1712 
Q 2810 1766 2829 1882 
z
M 2502 218 
Q 3194 934 3194 1274 
Q 3194 1402 3251 1402 
Q 3309 1402 3398 1338 
Q 3635 1190 3635 1075 
Q 3635 1043 3510 860 
Q 3386 678 3130 438 
Q 2874 198 2630 51 
Q 2387 -96 2348 -96 
Q 2310 -96 2310 -41 
Q 2310 13 2502 218 
z
M 4973 1062 
Q 4890 1146 4890 1213 
Q 4890 1280 4973 1344 
Q 5056 1408 5094 1408 
Q 5133 1408 5254 1305 
Q 5376 1203 5533 1046 
Q 5690 890 5830 736 
Q 5971 582 6057 483 
Q 6144 384 6144 301 
Q 6144 218 6045 131 
Q 5946 45 5898 45 
Q 5850 45 5818 73 
Q 5786 102 5542 432 
Q 5299 762 4973 1062 
z
M 4179 2144 
L 4186 2893 
L 3648 2848 
Q 3603 2701 3488 2438 
L 3341 2086 
L 4179 2144 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-4e60" d="M 4173 2182 
Q 4192 2010 2950 1264 
Q 1709 518 1523 499 
Q 1446 486 1299 589 
Q 883 896 1114 928 
Q 1485 998 1734 1100 
Q 1984 1203 2678 1516 
Q 3373 1830 3763 2054 
Q 4154 2278 4173 2182 
z
M 1811 3264 
Q 1670 3347 1670 3424 
Q 1670 3501 1750 3584 
Q 1830 3667 1891 3667 
Q 1952 3667 2262 3529 
Q 2573 3392 3405 2899 
Q 3558 2803 3558 2720 
Q 3558 2637 3475 2515 
Q 3392 2394 3312 2394 
Q 3232 2394 3030 2544 
Q 2829 2694 1811 3264 
z
M 5382 4294 
Q 5363 2342 5082 653 
Q 4947 -192 4873 -336 
Q 4800 -480 4713 -566 
Q 4627 -653 4528 -653 
Q 4429 -653 4352 -627 
Q 4275 -602 4067 -464 
Q 3859 -326 3066 358 
Q 2957 448 2957 525 
Q 2957 582 3027 582 
Q 3098 582 3293 460 
Q 3488 339 3901 112 
Q 4314 -115 4410 -115 
Q 4448 -90 4448 -70 
Q 4902 1549 4947 4320 
L 1478 4154 
Q 1376 4141 1286 4141 
Q 1126 4141 1011 4314 
Q 928 4422 928 4483 
Q 928 4544 963 4544 
Q 998 4544 1056 4528 
Q 1114 4512 1235 4512 
L 5018 4704 
L 5120 4710 
Q 5229 4710 5328 4630 
Q 5427 4550 5427 4480 
Q 5427 4410 5404 4368 
Q 5382 4326 5382 4294 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-9636" d="M 4090 4160 
Q 3392 3091 2726 2496 
Q 2483 2272 2348 2201 
Q 2214 2131 2185 2131 
Q 2157 2131 2157 2182 
Q 2157 2234 2266 2355 
Q 3328 3590 3789 4499 
Q 3994 4890 3994 5043 
L 3994 5082 
Q 3994 5190 4048 5190 
Q 4102 5190 4192 5139 
Q 4448 4992 4435 4845 
Q 4435 4826 4374 4675 
Q 4314 4525 4282 4486 
Q 4710 3898 5184 3427 
Q 5658 2957 6170 2522 
Q 6240 2451 6237 2412 
Q 6234 2374 6170 2317 
Q 5990 2176 5907 2176 
Q 5882 2182 5709 2323 
Q 5338 2630 4698 3373 
Q 4410 3706 4090 4160 
z
M 4582 2522 
Q 4582 2675 4515 2761 
Q 4448 2848 4448 2867 
Q 4448 2918 4547 2918 
Q 4646 2918 4777 2867 
Q 4909 2816 4944 2774 
Q 4979 2733 4979 2650 
L 4986 -474 
Q 4986 -640 4870 -640 
Q 4736 -640 4595 -474 
Q 4531 -403 4531 -358 
L 4531 -294 
Q 4531 -275 4556 -160 
Q 4582 -45 4582 122 
L 4582 2522 
z
M 3635 2234 
Q 3635 1446 3507 966 
Q 3341 262 2790 -179 
Q 2573 -358 2406 -441 
Q 2240 -525 2208 -525 
Q 2176 -525 2176 -483 
Q 2176 -442 2291 -320 
Q 2906 275 3091 928 
Q 3232 1408 3232 2182 
L 3232 2387 
Q 3232 2541 3181 2624 
Q 3130 2707 3130 2752 
Q 3130 2797 3213 2797 
Q 3469 2746 3552 2688 
Q 3635 2630 3635 2560 
L 3635 2234 
z
M 1146 4653 
L 2266 4736 
Q 2349 4736 2435 4672 
Q 2522 4608 2522 4550 
Q 2522 4493 2490 4457 
Q 2458 4422 2378 4265 
Q 2298 4109 2077 3725 
Q 1856 3341 1779 3225 
Q 1702 3110 1702 3084 
Q 1702 3059 1785 2931 
Q 1869 2803 1939 2688 
Q 2208 2157 2208 1600 
Q 2208 1293 2099 1069 
Q 2042 947 1920 947 
Q 1798 947 1664 1065 
Q 1530 1184 1398 1344 
Q 1267 1504 1184 1651 
Q 1101 1798 1101 1840 
Q 1101 1882 1155 1882 
Q 1210 1882 1325 1766 
Q 1542 1587 1651 1523 
Q 1760 1459 1773 1459 
Q 1818 1459 1818 1740 
Q 1818 2022 1731 2278 
Q 1645 2534 1533 2704 
Q 1421 2874 1414 2880 
Q 1338 2963 1338 3056 
Q 1338 3149 1411 3286 
Q 1485 3424 1693 3779 
Q 1901 4134 2003 4371 
L 1114 4314 
L 1056 -422 
Q 1056 -595 947 -595 
Q 819 -595 726 -505 
Q 634 -416 634 -371 
L 634 -307 
Q 634 -288 656 -189 
Q 678 -90 678 218 
L 730 4166 
Q 730 4467 678 4592 
Q 627 4717 627 4758 
Q 627 4800 704 4800 
Q 781 4800 1146 4653 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6bb5" d="M 4890 2714 
Q 4525 2771 4525 3136 
L 4525 3149 
L 4544 4320 
L 3680 4256 
L 3680 4128 
Q 3680 3738 3622 3446 
Q 3565 3155 3401 2908 
Q 3238 2662 3078 2537 
Q 2918 2413 2867 2413 
Q 2829 2413 2829 2461 
Q 2829 2509 2880 2579 
Q 3155 2995 3219 3353 
Q 3283 3712 3283 3865 
Q 3283 4019 3276 4240 
Q 3270 4461 3232 4553 
Q 3194 4646 3194 4688 
Q 3194 4730 3264 4730 
Q 3334 4730 3654 4595 
L 4602 4678 
Q 4634 4685 4659 4685 
Q 4685 4685 4710 4685 
Q 4838 4685 4902 4592 
Q 4966 4499 4966 4467 
L 4947 4365 
L 4902 3213 
L 4902 3194 
Q 4902 3130 4953 3107 
Q 5005 3085 5133 3085 
Q 5261 3085 5286 3091 
Q 5312 3098 5376 3107 
Q 5440 3117 5501 3139 
Q 5562 3162 5603 3162 
Q 5645 3162 5734 3104 
Q 5958 2950 5958 2842 
Q 5958 2765 5805 2752 
Q 5318 2656 4890 2714 
z
M 1056 704 
L 915 653 
Q 678 557 608 557 
Q 538 557 461 627 
Q 384 698 329 781 
Q 275 864 275 899 
Q 275 934 384 947 
Q 493 960 1056 1114 
L 1024 3898 
Q 1024 4019 969 4121 
Q 915 4224 915 4243 
Q 915 4301 1001 4301 
Q 1088 4301 1206 4256 
Q 1325 4211 1331 4205 
Q 1843 4422 2182 4691 
Q 2342 4819 2377 4908 
Q 2413 4998 2461 4998 
Q 2509 4998 2579 4931 
Q 2650 4864 2701 4777 
Q 2752 4691 2752 4633 
Q 2752 4576 2682 4525 
Q 2048 4115 1421 3910 
L 1427 3392 
L 2112 3443 
Q 2266 3456 2336 3488 
Q 2406 3520 2451 3520 
Q 2496 3520 2573 3465 
Q 2650 3411 2707 3340 
Q 2765 3270 2765 3226 
Q 2765 3142 2624 3130 
L 1427 3053 
L 1434 2483 
L 2112 2522 
Q 2246 2534 2320 2563 
Q 2394 2592 2442 2592 
Q 2490 2592 2566 2547 
Q 2765 2419 2765 2304 
Q 2765 2221 2624 2208 
L 1440 2131 
L 1446 1229 
Q 2074 1427 2771 1683 
Q 2867 1715 2940 1715 
Q 3014 1715 3014 1657 
Q 3014 1600 2880 1510 
Q 2426 1216 1446 851 
L 1459 -378 
Q 1459 -531 1338 -531 
Q 1235 -531 1123 -464 
Q 1011 -397 1011 -282 
L 1011 -250 
Q 1011 -230 1036 -96 
Q 1062 38 1062 224 
L 1056 704 
z
M 3232 2323 
L 5069 2413 
L 5139 2413 
Q 5267 2413 5340 2345 
Q 5414 2278 5414 2201 
Q 5414 2125 5139 1632 
Q 4864 1139 4422 710 
Q 5158 102 6067 -224 
Q 6195 -269 6195 -313 
Q 6195 -358 6124 -422 
Q 6054 -486 5971 -534 
Q 5888 -582 5859 -582 
Q 5830 -582 5673 -518 
Q 5517 -454 5274 -320 
Q 4666 0 4154 454 
Q 3443 -128 2701 -435 
Q 2400 -563 2301 -563 
Q 2202 -563 2202 -518 
Q 2202 -442 2387 -346 
Q 3206 96 3878 710 
Q 3462 1146 3308 1363 
Q 3155 1581 3155 1629 
Q 3155 1677 3228 1747 
Q 3302 1818 3363 1818 
Q 3424 1818 3494 1728 
Q 3776 1325 4147 960 
Q 4659 1498 4877 2054 
L 3475 1978 
L 3309 1971 
Q 3181 1971 3123 2016 
Q 2944 2182 2944 2323 
Q 2944 2355 2973 2355 
Q 3002 2355 3059 2339 
Q 3117 2323 3232 2323 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
       <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#md3fd86860a" x="131.388862" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 静息态1 -->
      <g transform="translate(104.716573 346.833061) rotate(-45) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-9759" d="M 3360 3290 
L 3635 3258 
L 4358 3302 
Q 4640 3648 4915 4109 
L 3955 4051 
Q 3578 3565 3283 3315 
Q 3174 3226 3126 3226 
Q 3078 3226 3078 3280 
Q 3078 3334 3142 3418 
Q 3558 3936 3753 4313 
Q 3949 4691 4006 4908 
Q 4064 5126 4064 5152 
L 4064 5229 
Q 4064 5286 4096 5286 
Q 4128 5286 4211 5235 
Q 4461 5101 4461 4985 
Q 4461 4870 4166 4365 
L 5120 4429 
L 5203 4435 
Q 5286 4435 5372 4364 
Q 5459 4294 5459 4243 
Q 5459 4192 5414 4157 
Q 5370 4122 5174 3846 
Q 4979 3571 4768 3328 
L 5427 3366 
L 5478 3373 
Q 5536 3373 5625 3296 
Q 5715 3219 5715 3161 
Q 5715 3104 5702 3075 
Q 5690 3046 5683 3014 
L 5626 2490 
L 5709 2496 
Q 5875 2509 5920 2525 
Q 5965 2541 6025 2541 
Q 6086 2541 6189 2432 
Q 6266 2336 6266 2272 
Q 6266 2208 6118 2195 
L 5594 2170 
L 5523 1536 
Q 5677 1357 5677 1296 
Q 5677 1235 5622 1222 
Q 5568 1210 5478 1203 
L 4614 1158 
L 4602 -109 
L 4608 -294 
Q 4608 -525 4422 -608 
Q 4352 -640 4294 -640 
Q 4166 -640 3772 -291 
Q 3379 58 3379 179 
Q 3379 224 3436 224 
Q 3494 224 3664 109 
Q 3834 -6 4218 -160 
L 4230 1139 
L 3789 1120 
L 3712 1120 
Q 3648 1120 3526 1158 
Q 3405 1197 3341 1389 
Q 3322 1440 3322 1469 
Q 3322 1498 3360 1498 
L 3635 1466 
L 3686 1466 
L 4237 1491 
L 4237 2099 
L 3302 2048 
Q 3123 2048 3043 2169 
Q 2963 2291 2963 2352 
Q 2963 2413 3008 2413 
Q 3027 2413 3094 2393 
Q 3162 2374 3277 2374 
L 3341 2374 
L 4243 2419 
L 4250 2963 
L 3782 2938 
Q 3744 2931 3712 2931 
L 3654 2931 
Q 3430 2931 3334 3187 
Q 3322 3226 3322 3258 
Q 3322 3290 3360 3290 
z
M 582 4429 
Q 774 4390 896 4390 
L 928 4390 
L 1594 4429 
L 1594 4710 
Q 1594 4909 1546 4998 
Q 1498 5088 1498 5107 
Q 1498 5152 1619 5152 
Q 1741 5152 1856 5097 
Q 1971 5043 1971 4928 
L 1971 4454 
L 2349 4474 
Q 2419 4480 2499 4502 
Q 2579 4525 2627 4528 
Q 2675 4531 2752 4486 
Q 2944 4378 2944 4275 
Q 2944 4205 2810 4192 
L 1971 4141 
L 1965 3750 
L 2253 3770 
Q 2362 3776 2426 3801 
Q 2490 3827 2518 3827 
Q 2547 3827 2611 3789 
Q 2803 3667 2803 3578 
Q 2803 3514 2675 3501 
L 1965 3456 
L 1965 3078 
L 2688 3117 
Q 2822 3123 2880 3148 
Q 2938 3174 2979 3174 
Q 3021 3174 3142 3084 
Q 3264 2995 3264 2915 
Q 3264 2835 3136 2822 
L 698 2682 
L 589 2682 
Q 448 2682 390 2726 
L 301 2842 
Q 237 2918 237 2979 
Q 237 3040 265 3040 
Q 294 3040 352 3024 
Q 410 3008 544 3008 
L 602 3008 
L 1606 3059 
L 1600 3437 
L 1242 3411 
L 1114 3405 
Q 998 3405 947 3443 
Q 768 3578 768 3699 
Q 768 3738 800 3738 
Q 832 3738 902 3718 
Q 973 3699 1069 3699 
L 1600 3731 
L 1600 4122 
L 1011 4083 
L 896 4077 
Q 672 4077 576 4288 
Q 544 4365 544 4397 
Q 544 4429 582 4429 
z
M 5286 3021 
L 4627 2982 
L 4621 2438 
L 5242 2470 
L 5286 3021 
z
M 2810 -64 
L 2822 -262 
Q 2822 -403 2716 -483 
Q 2611 -563 2553 -563 
Q 2496 -563 2339 -483 
Q 2182 -403 2010 -282 
Q 1581 0 1581 109 
Q 1581 147 1629 147 
Q 1677 147 1885 67 
Q 2093 -13 2432 -102 
L 2426 730 
L 1146 678 
L 1120 -371 
Q 1114 -531 1018 -531 
Q 947 -531 825 -467 
Q 704 -403 704 -301 
Q 704 -256 726 -147 
Q 749 -38 755 166 
L 819 1978 
L 819 2042 
Q 819 2227 777 2329 
Q 736 2432 736 2464 
Q 736 2496 790 2496 
Q 845 2496 1184 2387 
L 2509 2451 
L 2547 2451 
Q 2656 2451 2733 2387 
Q 2810 2323 2810 2246 
L 2790 2112 
L 2810 -64 
z
M 5216 2150 
L 4621 2118 
L 4614 1517 
L 5165 1542 
L 5216 2150 
z
M 2419 2118 
L 1178 2054 
L 1171 1734 
L 2419 1786 
L 2419 2118 
z
M 2426 1466 
L 1165 1414 
L 1152 1011 
L 2426 1062 
L 2426 1466 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-606f" d="M 1619 1658 
Q 1645 1862 1645 1933 
L 1504 3968 
Q 1491 4147 1430 4265 
Q 1370 4384 1370 4435 
Q 1370 4486 1478 4486 
Q 1587 4486 1901 4371 
L 2400 4403 
Q 2643 4627 2800 4851 
Q 2957 5075 2957 5197 
L 2957 5229 
Q 2957 5299 3024 5299 
Q 3091 5299 3180 5254 
Q 3270 5210 3344 5149 
Q 3418 5088 3418 5050 
Q 3418 4947 2842 4429 
L 4442 4531 
L 4486 4531 
Q 4621 4531 4723 4454 
Q 4826 4378 4826 4323 
Q 4826 4269 4810 4240 
Q 4794 4211 4787 4179 
L 4576 2099 
Q 4736 1914 4736 1843 
Q 4736 1773 4681 1760 
Q 4627 1747 4550 1741 
L 2067 1645 
L 2074 1562 
L 2074 1542 
Q 2074 1408 1946 1408 
Q 1805 1408 1712 1491 
Q 1619 1574 1619 1658 
z
M 4358 4179 
L 1901 4026 
L 1933 3571 
L 4320 3706 
L 4358 4179 
z
M 4288 3366 
L 1958 3232 
L 1984 2810 
L 4250 2938 
L 4288 3366 
z
M 4224 2598 
L 2010 2483 
L 2042 1997 
L 4179 2086 
L 4224 2598 
z
M 3693 512 
Q 3635 467 3590 467 
Q 3546 467 3430 614 
Q 3130 998 2816 1254 
Q 2720 1338 2720 1386 
Q 2720 1434 2790 1504 
Q 2861 1574 2899 1574 
Q 2938 1574 3104 1459 
Q 3270 1344 3558 1069 
Q 3846 794 3846 707 
Q 3846 621 3693 512 
z
M 5683 320 
Q 5267 858 4832 1222 
Q 4736 1306 4736 1338 
Q 4736 1370 4793 1459 
Q 4851 1549 4908 1549 
Q 4966 1549 5187 1389 
Q 5408 1229 5734 909 
Q 6061 589 6061 502 
Q 6061 416 5961 323 
Q 5862 230 5808 230 
Q 5754 230 5683 320 
z
M 864 -179 
Q 794 -288 723 -288 
Q 710 -288 640 -256 
Q 454 -179 454 -64 
Q 454 -26 506 58 
Q 896 634 1107 1274 
Q 1146 1389 1222 1389 
Q 1274 1389 1363 1350 
Q 1453 1312 1453 1225 
Q 1453 1139 1267 668 
Q 1082 198 864 -179 
z
M 1773 1152 
Q 1773 1293 2010 1293 
Q 2112 1293 2150 1152 
Q 2246 762 2432 486 
Q 2803 -77 3968 -77 
Q 4518 -77 4518 -19 
L 4461 141 
Q 4250 717 4250 841 
Q 4250 966 4307 966 
Q 4384 966 4502 742 
Q 4621 518 4822 246 
Q 5024 -26 5053 -77 
Q 5082 -128 5082 -192 
Q 5082 -256 4986 -333 
Q 4890 -410 4694 -442 
Q 4499 -474 4090 -474 
Q 3072 -474 2566 -179 
Q 2157 64 1933 602 
Q 1843 819 1808 976 
Q 1773 1133 1773 1152 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6001" d="M 3354 2125 
Q 3040 2522 2726 2771 
Q 2630 2854 2630 2908 
Q 2630 2963 2704 3030 
Q 2778 3098 2813 3098 
Q 2848 3098 3014 2982 
Q 3181 2867 3469 2592 
Q 3757 2317 3757 2246 
Q 3757 2176 3664 2083 
Q 3571 1990 3520 1990 
Q 3469 1990 3354 2125 
z
M 1075 288 
Q 973 45 893 -115 
Q 813 -275 742 -275 
Q 736 -275 666 -250 
Q 461 -173 461 -51 
Q 461 0 573 182 
Q 685 365 829 707 
Q 973 1050 1062 1389 
Q 1094 1498 1171 1498 
Q 1222 1498 1324 1466 
Q 1427 1434 1427 1357 
Q 1427 1158 1075 288 
z
M 4352 1030 
Q 4429 1030 4518 858 
Q 4698 480 5011 45 
Q 5120 -115 5120 -166 
Q 5120 -218 5075 -288 
Q 4960 -474 4422 -474 
Q 3264 -474 2573 -77 
Q 2138 173 1920 794 
Q 1830 1043 1798 1219 
Q 1766 1395 1766 1408 
Q 1766 1549 2010 1549 
Q 2131 1549 2150 1408 
Q 2221 1037 2352 745 
Q 2483 454 2780 281 
Q 3078 109 3465 22 
Q 3853 -64 4214 -64 
Q 4576 -64 4576 -25 
Q 4576 13 4435 413 
Q 4294 813 4294 921 
Q 4294 1030 4352 1030 
z
M 4800 1376 
Q 4717 1446 4717 1504 
Q 4717 1562 4787 1635 
Q 4858 1709 4906 1709 
Q 4954 1709 5082 1613 
Q 5210 1517 5379 1369 
Q 5549 1222 5705 1065 
Q 5862 909 5968 784 
Q 6074 659 6074 614 
Q 6074 570 6029 509 
Q 5984 448 5926 403 
Q 5869 358 5824 358 
Q 5779 358 5516 668 
Q 5254 979 4800 1376 
z
M 3539 704 
Q 3174 1139 2816 1440 
Q 2720 1517 2720 1574 
Q 2720 1632 2781 1702 
Q 2842 1773 2883 1773 
Q 2925 1773 3097 1657 
Q 3270 1542 3577 1241 
Q 3885 941 3885 854 
Q 3885 768 3801 694 
Q 3718 621 3667 621 
Q 3616 621 3539 704 
z
M 5152 4096 
L 5299 4141 
Q 5421 4141 5581 3962 
Q 5638 3891 5638 3834 
Q 5638 3750 5504 3738 
L 3891 3642 
Q 4352 3168 4902 2745 
Q 5453 2323 6246 1933 
Q 6355 1875 6355 1830 
Q 6355 1786 6278 1725 
Q 6202 1664 6115 1619 
Q 6029 1574 5984 1574 
Q 5939 1574 5901 1600 
Q 5062 2048 4476 2547 
Q 3891 3046 3373 3610 
L 2778 3571 
Q 2298 2816 1613 2214 
Q 1082 1741 563 1440 
Q 365 1325 320 1325 
Q 275 1325 275 1382 
Q 275 1440 403 1542 
Q 1626 2528 2266 3546 
L 1248 3482 
L 1133 3475 
Q 979 3475 921 3532 
Q 864 3590 813 3696 
Q 762 3802 762 3840 
Q 762 3878 787 3878 
Q 813 3878 867 3859 
Q 922 3840 1062 3840 
L 1114 3840 
L 2483 3923 
L 2586 4115 
Q 2797 4550 2870 4816 
Q 2944 5082 2944 5107 
L 2925 5248 
Q 2925 5325 2995 5325 
Q 3142 5325 3366 5210 
Q 3462 5158 3462 5081 
Q 3462 5005 3331 4685 
Q 3200 4365 3078 4122 
L 2989 3955 
L 5018 4077 
Q 5094 4083 5152 4096 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#md3fd86860a" x="183.124743" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 静息态2 -->
      <g transform="translate(156.452455 346.833061) rotate(-45) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#md3fd86860a" x="234.860625" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 静息态3 -->
      <g transform="translate(208.188336 346.833061) rotate(-45) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#md3fd86860a" x="286.596507" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 刺激态1 -->
      <g transform="translate(259.924218 346.847424) rotate(-45) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-523a" d="M 5638 -109 
L 5645 -294 
Q 5645 -474 5539 -563 
Q 5434 -653 5354 -653 
Q 5274 -653 5136 -576 
Q 4998 -499 4841 -380 
Q 4685 -262 4537 -137 
Q 4390 -13 4297 92 
Q 4205 198 4205 243 
Q 4205 288 4262 288 
Q 4320 288 4595 144 
Q 4870 0 5229 -122 
L 5242 4794 
Q 5242 4934 5146 5082 
Q 5120 5126 5120 5164 
Q 5120 5203 5200 5203 
Q 5280 5203 5433 5148 
Q 5587 5094 5619 5052 
Q 5651 5011 5651 4941 
L 5638 -109 
z
M 1869 -371 
Q 1914 -13 1914 198 
L 1920 1632 
Q 1466 902 858 435 
Q 608 243 441 156 
Q 275 70 233 70 
Q 192 70 192 124 
Q 192 179 333 294 
Q 1229 1082 1926 2285 
L 1926 2989 
L 1274 2950 
L 1306 1824 
L 1306 1805 
Q 1306 1715 1216 1715 
Q 1146 1715 1024 1776 
Q 902 1837 902 1946 
Q 902 1984 921 2067 
Q 941 2150 941 2310 
L 941 2387 
L 922 2874 
Q 902 3219 880 3283 
Q 858 3347 848 3369 
Q 838 3392 838 3411 
Q 838 3456 924 3456 
Q 1011 3456 1344 3302 
L 1933 3334 
L 1933 3878 
L 1107 3834 
Q 992 3821 915 3821 
Q 838 3821 736 3865 
Q 634 3910 563 4128 
Q 550 4166 550 4188 
Q 550 4211 585 4211 
Q 621 4211 685 4195 
Q 749 4179 870 4179 
L 947 4179 
L 1939 4237 
L 1939 4736 
Q 1939 4909 1888 4976 
Q 1837 5043 1837 5069 
Q 1837 5120 1949 5120 
Q 2061 5120 2192 5072 
Q 2323 5024 2323 4954 
L 2323 4256 
L 3072 4307 
Q 3232 4314 3299 4336 
Q 3366 4358 3395 4358 
Q 3424 4358 3507 4314 
Q 3738 4192 3738 4070 
Q 3738 3994 3622 3974 
L 2323 3898 
L 2317 3360 
L 3174 3405 
L 3226 3405 
Q 3334 3405 3420 3331 
Q 3507 3258 3507 3197 
Q 3507 3136 3491 3101 
Q 3475 3066 3469 3021 
L 3443 2323 
L 3443 2150 
Q 3443 1952 3340 1865 
Q 3238 1779 3174 1779 
Q 3053 1779 2749 2038 
Q 2445 2298 2445 2413 
Q 2445 2445 2489 2445 
Q 2534 2445 2694 2355 
Q 2854 2266 3059 2195 
L 3091 3053 
L 2317 3008 
L 2317 1837 
Q 2374 1901 2422 1901 
Q 2470 1901 2684 1737 
Q 2899 1574 3270 1203 
Q 3642 832 3642 733 
Q 3642 634 3494 525 
Q 3437 480 3398 480 
Q 3360 480 3213 666 
Q 2861 1107 2317 1581 
L 2310 -454 
Q 2310 -634 2189 -634 
Q 2086 -634 1977 -541 
Q 1869 -448 1869 -371 
z
M 4058 3680 
Q 4058 3834 4006 3917 
Q 3955 4000 3955 4038 
Q 3955 4077 4044 4077 
Q 4134 4077 4250 4026 
Q 4384 3981 4416 3939 
Q 4448 3898 4448 3821 
L 4467 966 
Q 4467 819 4346 819 
Q 4243 819 4137 896 
Q 4032 973 4032 1094 
Q 4032 1139 4048 1232 
Q 4064 1325 4064 1504 
L 4058 3680 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6fc0" d="M 2029 2560 
Q 2054 2739 2054 2778 
L 1984 3910 
Q 1971 4038 1929 4134 
Q 1888 4230 1888 4275 
Q 1888 4320 1968 4320 
Q 2048 4320 2336 4205 
L 2400 4211 
Q 2528 4474 2595 4694 
Q 2662 4915 2662 4986 
L 2637 5146 
Q 2637 5190 2694 5190 
Q 2701 5190 2790 5158 
Q 3078 5075 3078 4941 
Q 3078 4845 2950 4598 
Q 2822 4352 2746 4237 
L 3366 4282 
Q 3398 4288 3424 4288 
L 3507 4288 
Q 3565 4288 3641 4217 
Q 3718 4147 3718 4099 
Q 3718 4051 3702 4025 
Q 3686 4000 3680 3968 
L 3546 2867 
Q 3680 2707 3680 2649 
Q 3680 2592 3632 2585 
Q 3584 2579 3514 2573 
L 2400 2515 
L 2400 2438 
Q 2400 2336 2301 2336 
Q 2202 2336 2115 2409 
Q 2029 2483 2029 2560 
z
M 4941 4794 
Q 4941 4602 4550 3648 
L 5517 3712 
Q 5632 3725 5699 3750 
Q 5766 3776 5798 3776 
Q 5830 3776 5901 3738 
Q 6106 3622 6106 3514 
Q 6106 3430 5978 3418 
L 5587 3392 
Q 5414 2214 5056 1408 
Q 5517 461 6202 -211 
Q 6253 -262 6253 -297 
Q 6253 -333 6195 -384 
Q 6061 -512 5939 -512 
Q 5843 -512 5472 16 
Q 5101 544 4870 1011 
Q 4506 326 3987 -179 
Q 3776 -390 3625 -496 
Q 3475 -602 3430 -602 
Q 3386 -602 3386 -550 
Q 3386 -499 3456 -429 
Q 4192 371 4672 1434 
Q 4390 2086 4192 2880 
Q 4154 2816 4083 2688 
Q 3763 2112 3661 2112 
Q 3622 2112 3622 2176 
Q 3622 2240 3674 2355 
Q 4147 3360 4442 4480 
Q 4506 4730 4506 4816 
Q 4506 4902 4486 4944 
Q 4467 4986 4467 4998 
Q 4467 5050 4531 5050 
Q 4570 5050 4672 5018 
Q 4941 4928 4941 4794 
z
M 1443 4297 
Q 1574 4173 1660 4077 
Q 1747 3981 1747 3945 
Q 1747 3910 1667 3808 
Q 1587 3706 1520 3706 
Q 1453 3706 1357 3821 
Q 1261 3936 1053 4141 
Q 845 4346 717 4442 
Q 589 4538 589 4592 
Q 589 4646 656 4723 
Q 723 4800 771 4800 
Q 819 4800 928 4723 
Q 1037 4646 1174 4534 
Q 1312 4422 1443 4297 
z
M 3315 3987 
L 2330 3917 
L 2349 3526 
L 3277 3584 
L 3315 3987 
z
M 1126 2451 
Q 672 2842 326 3085 
Q 256 3136 256 3184 
Q 256 3232 320 3318 
Q 384 3405 435 3405 
Q 486 3405 736 3245 
Q 986 3085 1389 2758 
Q 1459 2707 1459 2659 
Q 1459 2611 1424 2550 
Q 1389 2490 1337 2438 
Q 1286 2387 1248 2387 
Q 1210 2387 1126 2451 
z
M 4410 3315 
Q 4595 2522 4851 1882 
Q 5069 2426 5178 3366 
L 4410 3315 
z
M 3251 3290 
L 2362 3238 
L 2381 2803 
L 3206 2854 
L 3251 3290 
z
M 2835 1690 
L 2701 1344 
L 3334 1389 
L 3418 1389 
Q 3571 1389 3635 1296 
Q 3699 1203 3699 1174 
Q 3699 1146 3683 1120 
Q 3667 1094 3654 1062 
Q 3501 358 3277 -166 
Q 3232 -275 3123 -339 
Q 3040 -397 2970 -397 
Q 2829 -397 2522 -90 
Q 2221 198 2221 294 
Q 2221 326 2259 326 
Q 2298 326 2467 233 
Q 2637 141 2768 83 
Q 2899 26 2915 26 
Q 2931 26 2950 64 
Q 3168 544 3264 1075 
L 2554 1030 
Q 2304 538 2029 234 
Q 1754 -70 1552 -208 
Q 1350 -346 1308 -346 
Q 1267 -346 1267 -304 
Q 1267 -262 1344 -179 
Q 2080 608 2426 1664 
L 2067 1638 
Q 2029 1632 1997 1632 
L 1901 1632 
Q 1837 1632 1760 1664 
Q 1594 1811 1594 1946 
Q 1594 1971 1619 1971 
Q 1645 1971 1689 1958 
Q 1734 1946 1862 1946 
L 1952 1946 
L 2694 1990 
L 2694 2150 
Q 2694 2291 2640 2358 
Q 2586 2426 2586 2445 
Q 2586 2490 2646 2490 
Q 2707 2490 2803 2470 
Q 3046 2419 3046 2272 
L 3040 2010 
L 3520 2042 
Q 3661 2054 3709 2073 
Q 3757 2093 3808 2093 
Q 3859 2093 3929 2045 
Q 4000 1997 4048 1936 
Q 4096 1875 4096 1843 
Q 4096 1779 3968 1760 
L 2835 1690 
z
M 602 -186 
Q 371 -154 297 -83 
Q 224 -13 224 9 
Q 224 32 282 64 
Q 422 134 934 1114 
Q 1158 1549 1363 2022 
Q 1434 2195 1504 2195 
Q 1542 2195 1542 2121 
Q 1542 2048 1411 1571 
Q 1280 1094 1030 515 
Q 781 -64 739 -125 
Q 698 -186 627 -186 
L 602 -186 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#md3fd86860a" x="338.332388" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 刺激态2 -->
      <g transform="translate(311.660099 346.847424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#md3fd86860a" x="390.06827" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 刺激态3 -->
      <g transform="translate(363.395981 346.847424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_8">
      <defs>
       <path id="mb783f06030" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="272.756913" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- −1.0 -->
      <g transform="translate(21.059375 276.292069) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-2212" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="216.479371" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- −0.5 -->
      <g transform="translate(21.059375 220.014527) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_10">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="160.201829" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 0.0 -->
      <g transform="translate(24.559375 163.736985) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_11">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="103.924287" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 0.5 -->
      <g transform="translate(24.559375 107.459443) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_12">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="47.646745" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 1.0 -->
      <g transform="translate(24.559375 51.181901) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_13">
      <defs>
       <path id="m4503210ea2" d="M 0 0 
L -2 0 
" style="stroke: #000000; stroke-width: 0.6"/>
      </defs>
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="300.895684" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_14">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="244.618142" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_15">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="188.3406" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="132.063058" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="75.785516" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_13">
     <!-- HEP振幅 (μV) -->
     <g transform="translate(15.159375 195.671134) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-48" d="M 3456 115 
L 3469 685 
L 3469 2317 
L 3430 2310 
L 2918 2330 
L 1165 2227 
L 1126 2227 
L 1126 672 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 678 
L 678 2176 
Q 659 2170 634 2170 
L 627 2170 
Q 544 2170 483 2288 
Q 422 2406 422 2508 
Q 422 2611 493 2611 
L 499 2611 
L 678 2605 
L 678 3770 
L 659 4448 
Q 659 4512 780 4512 
Q 902 4512 1020 4464 
Q 1139 4416 1139 4333 
L 1126 3763 
L 1126 2605 
L 1158 2605 
L 2765 2701 
Q 2912 2714 3065 2726 
Q 3219 2739 3296 2752 
L 3315 2752 
Q 3411 2752 3469 2598 
L 3469 3770 
L 3450 4448 
Q 3450 4512 3571 4512 
Q 3693 4512 3811 4464 
Q 3930 4416 3930 4333 
L 3917 3763 
L 3917 678 
L 3936 0 
Q 3936 -64 3817 -64 
Q 3699 -64 3577 -16 
Q 3456 32 3456 115 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-45" d="M 3021 410 
Q 3264 410 3552 429 
L 3558 429 
Q 3642 429 3702 310 
Q 3763 192 3763 89 
Q 3763 -13 3693 -13 
L 3686 -13 
Q 3533 6 3130 6 
L 3027 6 
L 1248 -19 
Q 1146 -19 1072 -41 
Q 998 -64 905 -64 
Q 813 -64 723 48 
Q 634 160 634 237 
L 678 538 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4233 
Q 3565 4134 3482 4134 
L 3085 4134 
Q 2912 4134 2822 4128 
L 1114 4013 
L 1114 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1114 2202 
L 1114 378 
L 3021 410 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-50" d="M 2035 4147 
Q 1581 4147 1126 4019 
L 1126 2317 
Q 1165 2342 1210 2342 
Q 2278 2413 2748 2729 
Q 3219 3046 3219 3462 
Q 3219 4147 2035 4147 
z
M 2048 4550 
Q 3680 4550 3680 3469 
Q 3680 2938 3328 2605 
Q 2733 2048 1376 2048 
L 1280 2048 
Q 1190 2048 1126 2080 
L 1126 736 
L 1146 -6 
Q 1146 -70 1027 -70 
Q 909 -70 787 -22 
Q 666 26 666 109 
L 678 742 
L 678 3955 
L 672 3955 
Q 576 3955 480 4070 
Q 384 4186 384 4259 
Q 384 4333 442 4339 
Q 1504 4544 1689 4547 
Q 1875 4550 2048 4550 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-632f" d="M 326 3590 
Q 429 3565 506 3565 
L 627 3565 
Q 672 3565 723 3571 
L 1402 3616 
L 1408 4621 
Q 1408 4794 1293 4973 
Q 1274 5005 1274 5024 
Q 1274 5069 1341 5069 
Q 1408 5069 1517 5043 
Q 1805 4966 1805 4800 
L 1798 3642 
L 1958 3654 
Q 2048 3661 2137 3699 
Q 2227 3738 2259 3738 
Q 2291 3738 2374 3686 
Q 2598 3546 2598 3450 
Q 2598 3366 2438 3354 
L 1792 3302 
L 1786 2227 
Q 2042 2349 2275 2477 
Q 2509 2605 2569 2605 
Q 2630 2605 2630 2560 
Q 2630 2445 1786 1901 
L 1773 122 
L 1786 -141 
Q 1786 -301 1680 -400 
Q 1574 -499 1466 -499 
Q 1261 -499 902 -108 
Q 544 282 544 384 
Q 544 416 585 416 
Q 627 416 838 272 
Q 1050 128 1382 6 
L 1389 1658 
Q 666 1235 531 1235 
Q 493 1235 454 1254 
Q 282 1350 173 1517 
Q 160 1542 160 1555 
Q 160 1594 243 1603 
Q 326 1613 659 1734 
Q 992 1856 1395 2042 
L 1402 3270 
L 851 3226 
Q 710 3213 611 3213 
Q 512 3213 474 3245 
Q 384 3334 345 3424 
Q 307 3514 300 3523 
Q 294 3533 294 3561 
Q 294 3590 326 3590 
z
M 3174 4282 
Q 3174 3142 3123 2547 
L 5280 2656 
Q 5427 2669 5504 2697 
Q 5581 2726 5622 2726 
Q 5664 2726 5801 2627 
Q 5939 2528 5939 2438 
Q 5939 2368 5786 2355 
L 4307 2278 
Q 4557 1722 4723 1440 
Q 5203 1920 5216 2150 
Q 5229 2246 5289 2246 
Q 5350 2246 5420 2176 
Q 5491 2106 5539 2029 
Q 5587 1952 5587 1913 
Q 5587 1875 5466 1741 
Q 5216 1446 4902 1178 
Q 5446 474 6240 13 
Q 6317 -32 6317 -70 
Q 6317 -90 6259 -154 
Q 6106 -339 5990 -339 
Q 5965 -339 5926 -314 
Q 5466 26 5107 384 
Q 4410 1088 3968 2259 
L 3782 2253 
L 3763 166 
Q 4147 288 4406 406 
Q 4666 525 4730 525 
Q 4794 525 4794 486 
Q 4794 378 4285 90 
Q 3776 -198 3481 -323 
Q 3187 -448 3123 -448 
Q 3059 -448 2966 -361 
Q 2874 -275 2813 -185 
Q 2752 -96 2752 -67 
Q 2752 -38 2816 -38 
L 2906 -38 
Q 3149 -38 3373 51 
L 3392 2234 
L 3091 2214 
Q 2976 1203 2541 307 
Q 2362 -58 2211 -262 
Q 2061 -467 2006 -467 
Q 1952 -467 1952 -403 
Q 1952 -339 1997 -250 
Q 2592 1024 2714 2342 
Q 2771 2970 2771 3456 
Q 2771 3942 2768 4227 
Q 2765 4512 2717 4598 
Q 2669 4685 2669 4733 
Q 2669 4781 2771 4781 
Q 2874 4781 3194 4621 
L 5069 4742 
Q 5216 4755 5283 4780 
Q 5350 4806 5401 4806 
Q 5453 4806 5530 4749 
Q 5728 4621 5728 4518 
Q 5728 4448 5574 4435 
L 3174 4282 
z
M 3546 3616 
L 4742 3686 
Q 4890 3699 4957 3724 
Q 5024 3750 5075 3750 
Q 5126 3750 5210 3699 
Q 5402 3565 5402 3462 
Q 5402 3386 5248 3373 
L 3821 3283 
Q 3770 3277 3725 3277 
L 3642 3277 
Q 3514 3277 3491 3302 
Q 3469 3328 3385 3417 
Q 3302 3507 3302 3571 
Q 3302 3635 3334 3635 
L 3546 3616 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e45" d="M 2336 1325 
Q 2170 1325 1747 1824 
L 1728 -429 
Q 1728 -595 1619 -595 
Q 1562 -595 1440 -521 
Q 1318 -448 1318 -326 
L 1318 -294 
Q 1318 -275 1337 -156 
Q 1357 -38 1370 550 
L 1395 3322 
L 986 3290 
L 973 1414 
Q 973 1254 867 1254 
Q 762 1254 659 1350 
Q 557 1446 557 1491 
Q 557 1536 579 1638 
Q 602 1741 602 1862 
L 614 3264 
Q 614 3482 589 3558 
L 550 3661 
Q 544 3693 544 3712 
Q 544 3757 611 3757 
Q 678 3757 979 3629 
L 1402 3661 
L 1408 4640 
Q 1408 4858 1306 5005 
Q 1280 5037 1280 5065 
Q 1280 5094 1350 5094 
Q 1421 5094 1571 5056 
Q 1722 5018 1750 4957 
Q 1779 4896 1779 4826 
L 1766 3686 
L 2336 3725 
L 2400 3725 
Q 2547 3725 2618 3597 
Q 2637 3558 2637 3526 
L 2618 3398 
L 2624 1805 
L 2630 1632 
Q 2630 1472 2524 1398 
Q 2419 1325 2336 1325 
z
M 2893 4576 
L 5293 4717 
Q 5402 4723 5472 4745 
Q 5542 4768 5590 4768 
Q 5638 4768 5708 4710 
Q 5779 4653 5830 4582 
Q 5882 4512 5882 4474 
Q 5882 4397 5722 4384 
L 3117 4224 
L 3040 4224 
Q 2880 4224 2797 4285 
Q 2714 4346 2653 4445 
Q 2592 4544 2592 4569 
Q 2592 4595 2630 4595 
L 2688 4595 
L 2893 4576 
z
M 3194 3475 
Q 3174 3642 3113 3728 
Q 3053 3814 3053 3849 
Q 3053 3885 3158 3885 
Q 3264 3885 3552 3795 
L 5133 3891 
L 5190 3891 
Q 5293 3891 5366 3824 
Q 5440 3757 5440 3709 
Q 5440 3661 5427 3632 
Q 5414 3603 5402 3565 
L 5286 2938 
Q 5421 2778 5421 2723 
Q 5421 2669 5366 2656 
Q 5312 2643 5229 2637 
L 3667 2560 
L 3667 2502 
Q 3667 2355 3565 2355 
Q 3520 2355 3430 2394 
Q 3270 2458 3270 2566 
L 3270 2592 
Q 3283 2669 3283 2720 
L 3277 2810 
L 3194 3475 
z
M 4992 3552 
L 3578 3469 
L 3635 2880 
L 4915 2950 
L 4992 3552 
z
M 2234 3379 
L 1766 3347 
L 1754 1984 
Q 1958 1875 2240 1760 
L 2234 3379 
z
M 5581 2170 
L 5638 2176 
Q 5728 2176 5805 2093 
Q 5882 2010 5882 1958 
Q 5882 1907 5869 1878 
Q 5856 1850 5850 1818 
L 5670 173 
Q 5869 -32 5869 -86 
Q 5869 -141 5821 -150 
Q 5773 -160 5690 -166 
L 3347 -224 
L 3354 -371 
L 3354 -397 
Q 3354 -531 3264 -531 
Q 3130 -531 3043 -448 
Q 2957 -365 2957 -269 
L 2957 -237 
Q 2970 -96 2970 32 
L 2970 96 
L 2886 1677 
Q 2874 1875 2819 1987 
Q 2765 2099 2765 2128 
Q 2765 2157 2841 2157 
Q 2918 2157 3232 2054 
L 5581 2170 
z
M 5446 1830 
L 4506 1786 
L 4499 1165 
L 5395 1197 
L 5446 1830 
z
M 4147 1773 
L 3245 1728 
L 3277 1114 
L 4141 1152 
L 4147 1773 
z
M 5370 883 
L 4499 851 
L 4493 147 
L 5306 166 
L 5370 883 
z
M 4141 838 
L 3290 806 
L 3328 115 
L 4134 134 
L 4141 838 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-28" d="M 2016 -896 
Q 2016 -1018 1914 -1018 
Q 1862 -1018 1715 -883 
Q 1350 -544 1037 102 
Q 640 902 640 1846 
Q 640 2790 998 3644 
Q 1357 4499 1728 4902 
Q 1882 5062 1946 5062 
Q 2067 5062 2067 4954 
Q 2067 4902 2035 4870 
Q 1619 4384 1369 3478 
Q 1120 2573 1120 1865 
Q 1120 1158 1350 384 
Q 1581 -390 1965 -774 
Q 2016 -826 2016 -896 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-3bc" d="M 1658 -102 
Q 1190 -102 998 307 
L 979 -499 
Q 973 -826 979 -1171 
Q 979 -1216 886 -1216 
Q 794 -1216 656 -1155 
Q 518 -1094 512 -1018 
L 512 -1005 
Q 538 -691 538 -448 
L 627 2349 
Q 646 2790 634 3027 
Q 640 3098 736 3098 
Q 832 3098 957 3030 
Q 1082 2963 1094 2874 
L 1094 2854 
Q 1094 2771 1081 2604 
Q 1069 2438 1062 2298 
L 1030 1235 
Q 1030 1146 1046 1002 
Q 1062 858 1123 694 
Q 1184 531 1305 419 
Q 1427 307 1683 307 
Q 1939 307 2179 508 
Q 2419 710 2438 1101 
L 2496 2323 
Q 2509 2637 2496 2995 
Q 2496 3046 2589 3046 
Q 2682 3046 2816 2982 
Q 2950 2918 2963 2835 
L 2963 2822 
Q 2957 2733 2947 2582 
Q 2938 2432 2931 2272 
L 2874 890 
Q 2867 666 2880 576 
Q 2918 250 3132 250 
Q 3347 250 3468 304 
Q 3590 358 3657 358 
Q 3725 358 3731 300 
Q 3738 243 3648 154 
Q 3430 -83 3110 -83 
Q 2906 -83 2736 6 
Q 2566 96 2528 333 
Q 2381 109 2144 3 
Q 1907 -102 1658 -102 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-56" d="M 70 4403 
Q 70 4506 307 4506 
Q 410 4506 506 4474 
Q 602 4442 614 4378 
Q 653 4230 944 3494 
Q 1235 2758 1946 634 
Q 2765 2816 3059 3539 
Q 3354 4262 3411 4480 
Q 3430 4550 3536 4550 
Q 3642 4550 3770 4473 
Q 3898 4397 3898 4307 
Q 3898 4275 3821 4147 
Q 3744 4019 3667 3840 
Q 2387 813 2240 224 
Q 2214 109 2202 38 
Q 2182 -109 2035 -109 
Q 1933 -109 1817 -9 
Q 1702 90 1606 374 
Q 1510 659 1344 1126 
Q 429 3667 77 4378 
Q 70 4390 70 4403 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-29" d="M 518 -883 
Q 371 -1018 320 -1018 
Q 218 -1018 218 -896 
Q 218 -826 269 -774 
Q 653 -390 883 384 
Q 1114 1158 1114 1865 
Q 1114 2573 864 3478 
Q 614 4384 198 4870 
Q 166 4902 166 4954 
Q 166 5062 288 5062 
Q 358 5062 512 4902 
Q 877 4499 1203 3738 
Q 1594 2790 1594 1849 
Q 1594 909 1238 179 
Q 883 -550 518 -883 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-48"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="69.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="133.799973"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="195.59996"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="295.599945"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="395.59993"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="430.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="465.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="521.799896"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="583.799881"/>
     </g>
    </g>
    <g id="text_14">
     <!-- 1e−6 -->
     <g transform="translate(47.059375 17.459375) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-65" d="M 992 1613 
Q 1709 1613 2317 1779 
Q 2547 1850 2614 1933 
Q 2682 2016 2682 2182 
Q 2682 2739 2035 2739 
Q 1594 2739 1274 2371 
Q 954 2003 864 1613 
L 992 1613 
z
M 384 1626 
L 442 1632 
Q 538 2138 992 2624 
Q 1446 3110 2042 3110 
Q 2438 3110 2675 2970 
Q 3104 2720 3104 2176 
Q 3104 1958 3069 1830 
Q 3034 1702 2906 1622 
Q 2778 1542 2490 1453 
Q 1850 1254 896 1254 
L 813 1254 
L 813 1190 
Q 813 774 1027 518 
Q 1242 262 1606 262 
Q 2413 262 2912 800 
Q 2995 896 3059 896 
Q 3123 896 3123 829 
Q 3123 762 3030 614 
Q 2938 467 2746 294 
Q 2278 -115 1562 -115 
Q 1235 -115 969 48 
Q 704 211 550 518 
Q 397 826 397 1248 
L 397 1338 
Q 262 1440 262 1542 
Q 262 1626 384 1626 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1">
    <path d="M 79.65298 267.48482 
L 79.65298 164.197985 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 131.388862 295.887576 
L 131.388862 216.944571 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 183.124743 217.428314 
L 183.124743 164.068906 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 234.860625 272.789734 
L 234.860625 165.699073 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 286.596507 223.825598 
L 286.596507 160.435136 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 338.332388 223.457777 
L 338.332388 169.061118 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
    <path d="M 390.06827 205.368543 
L 390.06827 162.031931 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000"/>
   </g>
   <g id="line2d_18">
    <defs>
     <path id="mec487baa02" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#pa81449ea42)">
     <use xlink:href="#mec487baa02" x="79.65298" y="267.48482" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="131.388862" y="295.887576" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="183.124743" y="217.428314" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="234.860625" y="272.789734" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="286.596507" y="223.825598" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="338.332388" y="223.457777" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="390.06827" y="205.368543" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_19">
    <g clip-path="url(#pa81449ea42)">
     <use xlink:href="#mec487baa02" x="79.65298" y="164.197985" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="131.388862" y="216.944571" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="183.124743" y="164.068906" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="234.860625" y="165.699073" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="286.596507" y="160.435136" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="338.332388" y="169.061118" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="390.06827" y="162.031931" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_20">
    <path d="M 79.65298 69.437148 
L 131.388862 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_21">
    <path d="M 79.65298 62.264719 
L 183.124743 62.264719 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_22">
    <path d="M 79.65298 55.09229 
L 234.860625 55.09229 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_23">
    <path d="M 79.65298 47.919861 
L 286.596507 47.919861 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_24">
    <path d="M 79.65298 40.747433 
L 338.332388 40.747433 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_25">
    <path d="M 79.65298 33.575004 
L 390.06827 33.575004 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_26">
    <path d="M 131.388862 69.437148 
L 183.124743 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_27">
    <path d="M 131.388862 62.264719 
L 234.860625 62.264719 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_28">
    <path d="M 131.388862 55.09229 
L 286.596507 55.09229 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_29">
    <path d="M 131.388862 47.919861 
L 338.332388 47.919861 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_30">
    <path d="M 131.388862 40.747433 
L 390.06827 40.747433 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_31">
    <path d="M 183.124743 69.437148 
L 234.860625 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_32">
    <path d="M 183.124743 62.264719 
L 286.596507 62.264719 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_33">
    <path d="M 183.124743 55.09229 
L 338.332388 55.09229 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_34">
    <path d="M 183.124743 47.919861 
L 390.06827 47.919861 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_35">
    <path d="M 234.860625 69.437148 
L 286.596507 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_36">
    <path d="M 234.860625 62.264719 
L 338.332388 62.264719 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_37">
    <path d="M 234.860625 55.09229 
L 390.06827 55.09229 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_38">
    <path d="M 286.596507 69.437148 
L 338.332388 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_39">
    <path d="M 286.596507 62.264719 
L 390.06827 62.264719 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_40">
    <path d="M 338.332388 69.437148 
L 390.06827 69.437148 
" clip-path="url(#pa81449ea42)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 47.059375 309.003205 
L 47.059375 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 422.661875 309.003205 
L 422.661875 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 47.059375 309.003205 
L 422.661875 309.003205 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 47.059375 20.459375 
L 422.661875 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- ns -->
    <g transform="translate(100.031077 61.978893) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-6e" d="M 3174 672 
L 3194 -6 
Q 3194 -70 3082 -70 
Q 2970 -70 2854 -22 
Q 2739 26 2739 102 
L 2739 109 
Q 2746 160 2746 282 
L 2752 736 
L 2752 1875 
Q 2752 2253 2649 2480 
Q 2547 2707 2227 2707 
Q 1722 2707 1379 2339 
Q 1037 1971 1024 1037 
Q 1018 826 1011 672 
L 1011 570 
Q 1011 378 1030 -6 
Q 1030 -70 918 -70 
Q 806 -70 691 -22 
Q 576 26 576 109 
L 589 736 
L 589 2330 
L 570 3014 
Q 570 3078 682 3078 
Q 794 3078 909 3030 
Q 1024 2982 1024 2899 
L 1024 2810 
Q 1011 2630 1011 2406 
Q 1216 2746 1545 2925 
Q 1875 3104 2202 3104 
Q 2790 3104 3008 2701 
Q 3174 2406 3174 1901 
L 3174 672 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-73" d="M 1248 1549 
Q 531 1715 531 2278 
Q 531 2528 691 2714 
Q 1030 3110 1689 3110 
Q 2349 3110 2726 2707 
Q 2739 2694 2739 2630 
Q 2739 2566 2672 2464 
Q 2605 2362 2550 2362 
Q 2496 2362 2432 2445 
Q 2227 2758 1702 2758 
Q 1331 2758 1139 2614 
Q 947 2470 947 2304 
Q 947 2138 1033 2048 
Q 1120 1958 1382 1894 
L 1997 1747 
Q 2413 1651 2653 1433 
Q 2893 1216 2893 819 
Q 2893 563 2742 352 
Q 2592 141 2317 13 
Q 2042 -115 1667 -115 
Q 1293 -115 1046 -25 
Q 800 64 653 185 
Q 506 307 442 409 
Q 378 512 378 576 
Q 378 640 470 745 
Q 563 851 620 851 
Q 678 851 698 806 
Q 749 678 845 550 
Q 1056 256 1664 256 
Q 2054 256 2262 422 
Q 2470 589 2470 809 
Q 2470 1030 2355 1171 
Q 2240 1312 1888 1395 
L 1248 1549 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_16">
    <!-- ns -->
    <g transform="translate(125.899018 54.806464) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_17">
    <!-- ns -->
    <g transform="translate(151.766959 47.634035) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_18">
    <!-- ns -->
    <g transform="translate(177.6349 40.461606) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_19">
    <!-- ns -->
    <g transform="translate(203.50284 33.289177) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_20">
    <!-- ns -->
    <g transform="translate(229.370781 26.116748) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_21">
    <!-- ns -->
    <g transform="translate(151.766959 61.978893) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_22">
    <!-- ns -->
    <g transform="translate(177.6349 54.806464) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_23">
    <!-- ns -->
    <g transform="translate(203.50284 47.634035) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_24">
    <!-- ns -->
    <g transform="translate(229.370781 40.461606) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_25">
    <!-- ns -->
    <g transform="translate(255.238722 33.289177) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_26">
    <!-- ns -->
    <g transform="translate(203.50284 61.978893) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_27">
    <!-- ns -->
    <g transform="translate(229.370781 54.806464) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_28">
    <!-- ns -->
    <g transform="translate(255.238722 47.634035) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_29">
    <!-- ns -->
    <g transform="translate(281.106663 40.461606) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_30">
    <!-- ns -->
    <g transform="translate(255.238722 61.978893) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_31">
    <!-- ns -->
    <g transform="translate(281.106663 54.806464) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_32">
    <!-- ns -->
    <g transform="translate(306.974604 47.634035) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_33">
    <!-- ns -->
    <g transform="translate(306.974604 61.978893) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_34">
    <!-- ns -->
    <g transform="translate(332.842544 54.806464) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_35">
    <!-- ns -->
    <g transform="translate(358.710485 61.978893) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_36">
    <!-- 通道 Fz 的HEP振幅对比 -->
    <g transform="translate(181.220781 14.459375) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-901a" d="M 2368 755 
Q 2400 979 2400 1210 
L 2419 3347 
Q 2419 3526 2374 3641 
Q 2330 3757 2330 3792 
Q 2330 3827 2394 3827 
Q 2458 3827 2765 3693 
L 3661 3744 
Q 3514 3866 3168 4090 
Q 3098 4134 3098 4173 
Q 3174 4371 3254 4371 
Q 3334 4371 3789 4013 
Q 4154 4243 4589 4576 
L 2816 4448 
Q 2771 4442 2733 4442 
L 2662 4442 
Q 2554 4442 2464 4515 
Q 2374 4589 2342 4685 
L 2310 4774 
Q 2310 4806 2362 4806 
Q 2374 4806 2387 4806 
L 2605 4781 
L 4845 4922 
L 4909 4922 
Q 5050 4922 5123 4838 
Q 5197 4755 5197 4694 
Q 5197 4634 5149 4598 
Q 5101 4563 4845 4358 
Q 4589 4154 4032 3821 
L 4096 3763 
L 5139 3821 
Q 5165 3827 5219 3827 
Q 5274 3827 5360 3766 
Q 5446 3706 5446 3622 
L 5414 3475 
L 5427 858 
L 5434 672 
Q 5434 550 5344 435 
Q 5254 320 5145 320 
Q 5037 320 4662 624 
Q 4288 928 4288 1018 
Q 4288 1056 4339 1056 
Q 4390 1056 4576 969 
Q 4762 883 5062 794 
L 5056 1754 
L 4045 1709 
L 4045 768 
Q 4045 627 3955 627 
Q 3942 627 3866 653 
Q 3661 736 3661 890 
Q 3693 1114 3693 1370 
L 3693 1690 
L 2758 1645 
L 2758 685 
Q 2758 621 2742 576 
Q 2726 531 2662 531 
L 2586 550 
Q 2368 608 2368 755 
z
M 1453 3718 
Q 1203 4070 768 4493 
Q 704 4550 704 4611 
Q 704 4672 787 4736 
Q 870 4800 902 4800 
Q 934 4800 1049 4710 
Q 1165 4621 1305 4483 
Q 1446 4346 1580 4202 
Q 1715 4058 1804 3942 
Q 1894 3827 1894 3772 
Q 1894 3718 1801 3632 
Q 1709 3546 1641 3546 
Q 1574 3546 1453 3718 
z
M 5050 3494 
L 4045 3437 
L 4045 2906 
L 5050 2957 
L 5050 3494 
z
M 3706 3418 
L 2758 3366 
L 2758 2848 
L 3699 2893 
L 3706 3418 
z
M 390 3072 
Q 544 3046 595 3046 
L 672 3046 
Q 698 3046 723 3053 
L 1677 3149 
L 1754 3155 
Q 1830 3155 1913 3081 
Q 1997 3008 1997 2950 
Q 1997 2893 1958 2854 
L 1882 2778 
Q 1824 2701 1725 2573 
Q 1626 2445 1456 2221 
Q 1286 1997 1286 1965 
Q 1286 1933 1318 1914 
Q 1830 1606 1920 1421 
Q 1965 1344 1965 1280 
Q 1965 1043 1408 582 
Q 1619 570 2147 464 
Q 2675 358 3900 169 
Q 5126 -19 5958 -19 
L 5990 -19 
Q 6131 -19 6131 -70 
Q 6131 -90 6093 -179 
Q 5984 -422 5811 -422 
L 5773 -422 
Q 4973 -397 4070 -250 
Q 2528 13 1990 128 
Q 1453 243 1165 243 
Q 877 243 544 128 
Q 480 109 441 109 
Q 403 109 339 147 
Q 275 186 275 371 
Q 275 474 454 512 
Q 634 550 909 576 
Q 1274 896 1536 1235 
Q 1549 1261 1549 1280 
Q 1549 1331 1318 1485 
Q 1114 1638 1075 1658 
Q 909 1766 909 1884 
Q 909 2003 1053 2217 
Q 1197 2432 1434 2771 
L 838 2694 
Q 749 2682 717 2682 
Q 685 2682 617 2694 
Q 550 2707 473 2771 
Q 397 2835 358 2989 
Q 352 3002 352 3027 
Q 352 3072 390 3072 
z
M 5056 2656 
L 4045 2598 
L 4045 2003 
L 5056 2042 
L 5056 2656 
z
M 3699 2586 
L 2758 2534 
L 2758 1946 
L 3699 1984 
L 3699 2586 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-9053" d="M 3942 3776 
Q 4026 3693 4026 3645 
Q 4026 3597 3997 3545 
Q 3968 3494 3629 3187 
L 4864 3258 
L 4928 3258 
Q 5107 3258 5197 3098 
Q 5229 3040 5229 3014 
Q 5229 2989 5216 2957 
Q 5203 2925 5197 2893 
L 5069 1011 
Q 5248 813 5248 752 
Q 5248 691 5197 681 
Q 5146 672 5062 666 
L 3200 608 
L 3206 525 
L 3206 499 
Q 3206 371 3110 371 
Q 2976 371 2893 441 
Q 2810 512 2810 589 
L 2810 634 
Q 2822 774 2822 941 
L 2822 1030 
L 2752 2790 
Q 2733 3021 2697 3101 
Q 2662 3181 2662 3216 
Q 2662 3251 2739 3251 
Q 2816 3251 3110 3155 
L 3219 3162 
Q 3590 3578 3648 3757 
L 2554 3693 
Q 2426 3680 2323 3680 
Q 2221 3680 2150 3763 
Q 2029 3917 2029 3968 
Q 2029 4019 2080 4019 
L 2112 4019 
Q 2221 4000 2323 4000 
L 3923 4096 
Q 4224 4429 4467 4845 
Q 4512 4915 4538 5082 
Q 4550 5152 4611 5152 
Q 4672 5152 4813 5018 
Q 4954 4896 4954 4825 
Q 4954 4755 4800 4569 
Q 4646 4384 4352 4122 
L 5133 4173 
Q 5325 4186 5401 4211 
Q 5478 4237 5526 4237 
Q 5574 4237 5651 4192 
Q 5728 4147 5785 4083 
Q 5843 4019 5843 3968 
Q 5843 3898 5690 3885 
L 3942 3776 
z
M 3264 4211 
Q 3104 4429 2790 4730 
Q 2746 4774 2746 4809 
Q 2746 4845 2797 4931 
Q 2848 5018 2909 5018 
Q 2970 5018 3082 4928 
Q 3194 4838 3325 4713 
Q 3456 4589 3549 4480 
Q 3642 4371 3642 4326 
Q 3642 4282 3558 4192 
Q 3475 4102 3411 4102 
Q 3347 4102 3264 4211 
z
M 1434 3712 
Q 1107 4160 749 4506 
Q 678 4576 678 4617 
Q 678 4659 729 4736 
Q 781 4813 841 4813 
Q 902 4813 1014 4720 
Q 1126 4627 1273 4489 
Q 1421 4352 1555 4205 
Q 1690 4058 1779 3946 
Q 1869 3834 1869 3776 
Q 1869 3718 1779 3635 
Q 1690 3552 1619 3552 
Q 1549 3552 1434 3712 
z
M 403 3072 
Q 557 3046 614 3046 
L 691 3046 
Q 717 3046 742 3053 
L 1811 3162 
Q 1862 3162 1958 3094 
Q 2054 3027 2054 2960 
Q 2054 2893 2010 2854 
L 1939 2778 
Q 1338 2016 1338 1958 
Q 1338 1933 1370 1914 
Q 1741 1677 1885 1536 
Q 2029 1395 2029 1267 
Q 2029 1139 1875 966 
Q 1722 794 1446 550 
Q 1645 538 2105 438 
Q 2566 339 3827 144 
Q 5088 -51 5971 -51 
L 6003 -51 
Q 6150 -51 6150 -109 
Q 6150 -115 6118 -205 
Q 6010 -454 5837 -454 
L 5786 -454 
Q 4410 -416 1805 134 
Q 1466 211 1155 211 
Q 845 211 518 96 
Q 454 77 416 77 
Q 378 77 314 115 
Q 250 154 250 269 
Q 250 384 288 422 
Q 371 506 934 550 
Q 1280 870 1594 1229 
Q 1606 1254 1606 1261 
Q 1606 1338 1126 1651 
Q 954 1766 954 1888 
Q 954 2010 1114 2246 
Q 1274 2483 1485 2784 
L 858 2707 
Q 819 2701 794 2701 
L 730 2701 
Q 570 2701 467 2822 
Q 365 2944 365 3008 
Q 365 3072 403 3072 
z
M 4800 2931 
L 3117 2842 
L 3130 2451 
L 4781 2528 
L 4800 2931 
z
M 4768 2221 
L 3142 2144 
L 3162 1734 
L 4749 1798 
L 4768 2221 
z
M 4736 1498 
L 3174 1427 
L 3187 954 
L 4717 998 
L 4736 1498 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-46" d="M 653 4384 
Q 653 4474 742 4474 
Q 832 4474 953 4435 
Q 1075 4397 1082 4390 
L 2816 4506 
Q 2963 4518 3126 4537 
Q 3290 4557 3363 4557 
Q 3437 4557 3501 4445 
Q 3565 4333 3565 4230 
Q 3565 4128 3482 4128 
L 3085 4128 
Q 2912 4128 2822 4122 
L 1126 4006 
L 1126 2579 
L 2464 2662 
Q 2611 2675 2774 2694 
Q 2938 2714 3011 2714 
Q 3085 2714 3149 2602 
Q 3213 2490 3213 2387 
Q 3213 2285 3130 2285 
L 2726 2285 
Q 2560 2285 2470 2278 
L 1126 2202 
L 1126 685 
L 1146 6 
Q 1146 -58 1027 -58 
Q 909 -58 787 -10 
Q 666 38 666 122 
L 678 691 
L 678 2202 
Q 608 2240 557 2342 
Q 506 2445 506 2515 
Q 506 2586 557 2586 
L 570 2586 
Q 621 2573 678 2573 
L 678 4058 
L 653 4384 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7a" d="M 1862 384 
Q 2579 384 2682 390 
L 2688 390 
Q 2758 390 2816 275 
Q 2874 160 2874 57 
Q 2874 -45 2803 -45 
Q 2541 -19 2285 -6 
L 1421 -6 
Q 845 -6 672 -35 
Q 499 -64 428 -64 
Q 358 -64 297 35 
Q 237 134 237 214 
Q 237 294 259 348 
Q 282 403 355 454 
Q 429 506 589 717 
Q 1376 1779 2163 2656 
L 1222 2586 
Q 1050 2579 781 2541 
L 768 2541 
Q 646 2541 550 2752 
Q 512 2842 512 2909 
Q 512 2976 557 2976 
L 570 2976 
Q 736 2963 870 2963 
L 1075 2963 
Q 1152 2963 1216 2970 
L 2214 3040 
L 2445 3078 
L 2470 3078 
Q 2611 3066 2726 2874 
Q 2778 2797 2778 2742 
Q 2778 2688 2694 2643 
Q 2611 2598 2573 2554 
Q 1613 1453 806 352 
Q 1126 384 1370 384 
L 1862 384 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7684" d="M 3846 4890 
L 3834 4992 
Q 3834 5069 3898 5069 
Q 3917 5069 4019 5024 
Q 4307 4896 4307 4762 
Q 4307 4589 3898 3738 
L 5504 3834 
L 5530 3834 
Q 5613 3834 5696 3773 
Q 5779 3712 5779 3616 
L 5760 3469 
Q 5709 1747 5530 301 
Q 5427 -544 5082 -544 
Q 4934 -544 4841 -483 
Q 4749 -422 4605 -313 
Q 4461 -205 4301 -70 
Q 4141 64 4025 179 
Q 3910 294 3910 345 
Q 3910 397 3984 397 
Q 4058 397 4282 262 
Q 4506 128 4947 -45 
L 4960 -45 
Q 4998 -45 5018 6 
Q 5299 1107 5338 3469 
L 3731 3366 
Q 3450 2880 3232 2595 
Q 3014 2310 2944 2310 
Q 2912 2310 2912 2371 
Q 2912 2432 3104 2787 
Q 3296 3142 3571 3875 
Q 3846 4608 3846 4845 
L 3846 4890 
z
M 1715 4749 
Q 1715 4768 1702 4845 
Q 1702 4922 1779 4922 
Q 1920 4922 2035 4832 
Q 2150 4742 2150 4665 
Q 2150 4589 1920 4160 
Q 1715 3802 1581 3603 
L 2483 3654 
L 2560 3654 
Q 2746 3654 2803 3507 
Q 2822 3462 2822 3456 
L 2790 3328 
L 2688 480 
Q 2842 282 2842 214 
Q 2842 147 2790 134 
Q 2739 122 2656 115 
L 1229 64 
L 1235 -147 
L 1235 -166 
Q 1235 -307 1126 -307 
Q 979 -307 892 -233 
Q 806 -160 806 -77 
L 851 243 
L 794 3232 
Q 794 3418 717 3539 
Q 672 3629 672 3664 
Q 672 3699 768 3699 
Q 864 3699 1165 3584 
L 1222 3584 
Q 1715 4512 1715 4749 
z
M 2400 3302 
L 1171 3238 
L 1190 2061 
L 2368 2118 
L 2400 3302 
z
M 4755 1446 
Q 4755 1318 4589 1229 
Q 4525 1190 4483 1190 
Q 4442 1190 4358 1299 
Q 4160 1619 3913 1913 
Q 3667 2208 3564 2307 
Q 3462 2406 3462 2464 
Q 3462 2522 3532 2589 
Q 3603 2656 3657 2656 
Q 3712 2656 3869 2505 
Q 4026 2355 4390 1939 
Q 4755 1523 4755 1446 
z
M 2355 1773 
L 1197 1722 
L 1222 422 
L 2317 461 
L 2355 1773 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-5bf9" d="M 627 4301 
L 890 4275 
L 947 4275 
L 2470 4365 
L 2541 4365 
Q 2630 4365 2723 4301 
Q 2816 4237 2816 4173 
Q 2816 4109 2790 4070 
Q 2765 4032 2701 3731 
Q 2528 2912 2176 2118 
Q 2586 1606 3040 966 
Q 3117 870 3117 809 
Q 3117 749 3062 694 
Q 3008 640 2944 608 
Q 2880 576 2835 576 
Q 2790 576 2733 672 
Q 2413 1184 1990 1741 
Q 1491 845 870 198 
Q 621 -51 457 -176 
Q 294 -301 256 -301 
Q 218 -301 218 -240 
Q 218 -179 294 -102 
Q 1165 864 1734 2061 
Q 1325 2643 915 3110 
Q 851 3194 851 3251 
Q 851 3309 940 3373 
Q 1030 3437 1075 3437 
Q 1120 3437 1235 3302 
Q 1466 3014 1894 2451 
Q 2182 3187 2298 4000 
L 1094 3910 
Q 1037 3904 992 3904 
L 909 3904 
Q 781 3904 720 3965 
Q 659 4026 601 4128 
Q 544 4230 544 4265 
Q 544 4301 595 4301 
L 627 4301 
z
M 4154 1440 
Q 4032 1293 3945 1293 
Q 3859 1293 3802 1402 
Q 3558 1882 3238 2323 
Q 3200 2374 3200 2419 
Q 3200 2464 3286 2537 
Q 3373 2611 3427 2611 
Q 3482 2611 3549 2531 
Q 3616 2451 3728 2307 
Q 3840 2163 3952 2009 
Q 4064 1856 4137 1731 
Q 4211 1606 4211 1552 
Q 4211 1498 4154 1440 
z
M 5133 -13 
L 5139 -218 
Q 5139 -397 5036 -477 
Q 4934 -557 4844 -557 
Q 4755 -557 4534 -448 
Q 4314 -339 3984 -54 
Q 3654 230 3654 314 
Q 3654 358 3715 358 
Q 3776 358 4038 224 
Q 4301 90 4717 -38 
L 4698 3046 
L 3366 2970 
Q 3328 2963 3296 2963 
L 3226 2963 
Q 3040 2963 2989 3014 
Q 2925 3098 2873 3203 
Q 2822 3309 2822 3347 
Q 2822 3386 2844 3386 
Q 2867 3386 2940 3360 
Q 3014 3334 3162 3334 
L 3219 3334 
L 4698 3424 
L 4691 4589 
Q 4691 4774 4611 4883 
Q 4531 4992 4531 5030 
Q 4531 5069 4611 5069 
Q 4691 5069 4860 5008 
Q 5030 4947 5068 4899 
Q 5107 4851 5107 4762 
L 5114 3450 
L 5594 3475 
Q 5798 3494 5859 3516 
Q 5920 3539 5971 3539 
Q 6022 3539 6099 3482 
Q 6291 3347 6291 3238 
Q 6291 3149 6144 3136 
L 5114 3072 
L 5133 -13 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6bd4" d="M 3494 4570 
Q 3494 4742 3379 4902 
Q 3347 4954 3347 4992 
Q 3347 5030 3404 5030 
Q 3462 5030 3584 5005 
Q 3910 4922 3910 4768 
L 3898 2560 
Q 4589 2944 5030 3354 
Q 5139 3450 5197 3661 
Q 5216 3744 5274 3744 
Q 5312 3744 5389 3674 
Q 5613 3482 5613 3309 
Q 5613 3238 5568 3213 
Q 5178 2938 4784 2666 
Q 4390 2394 3898 2170 
L 3885 435 
Q 3885 301 3926 224 
Q 3968 147 4134 118 
Q 4301 90 4704 90 
Q 5107 90 5305 122 
Q 5504 154 5561 189 
Q 5619 224 5692 374 
Q 5766 525 5875 1229 
Q 5926 1530 6003 1530 
Q 6061 1530 6080 1392 
Q 6099 1254 6099 854 
Q 6099 454 6073 224 
Q 6048 -6 5923 -121 
Q 5798 -237 5510 -275 
Q 5222 -314 4752 -314 
Q 4282 -314 4010 -278 
Q 3738 -243 3606 -92 
Q 3475 58 3475 403 
L 3494 4570 
z
M 1306 4333 
Q 1306 4512 1197 4659 
Q 1165 4710 1165 4742 
Q 1165 4774 1248 4774 
Q 1331 4774 1485 4723 
Q 1715 4653 1715 4525 
L 1709 2829 
L 2406 2861 
Q 2598 2880 2668 2905 
Q 2739 2931 2800 2931 
Q 2861 2931 2937 2867 
Q 3014 2803 3068 2726 
Q 3123 2650 3123 2611 
Q 3123 2528 2950 2515 
L 1709 2451 
L 1702 429 
Q 2470 794 2925 1075 
Q 3046 1152 3116 1152 
Q 3187 1152 3187 1107 
Q 3187 992 2816 730 
Q 2221 301 1769 38 
Q 1318 -224 1152 -304 
Q 986 -384 922 -384 
Q 858 -384 778 -342 
Q 698 -301 602 -195 
Q 506 -90 506 -45 
Q 506 0 685 22 
Q 864 45 1299 250 
L 1306 4333 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-901a"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-46" x="234.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7a" x="293.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="342.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="377.199921"/>
     <use xlink:href="#LXGWWenKai-Regular-48" x="477.199905"/>
     <use xlink:href="#LXGWWenKai-Regular-45" x="547.19989"/>
     <use xlink:href="#LXGWWenKai-Regular-50" x="610.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-632f" x="672.799866"/>
     <use xlink:href="#LXGWWenKai-Regular-5e45" x="772.79985"/>
     <use xlink:href="#LXGWWenKai-Regular-5bf9" x="872.799835"/>
     <use xlink:href="#LXGWWenKai-Regular-6bd4" x="972.79982"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_14">
    <path d="M 473.659375 309.003205 
L 849.261875 309.003205 
L 849.261875 20.459375 
L 473.659375 20.459375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_15">
    <path d="M 490.732216 174.336089 
L 521.773745 174.336089 
L 521.773745 191.223717 
L 490.732216 191.223717 
z
" clip-path="url(#pdc229c91d7)" style="fill: #e63946; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_16">
    <path d="M 542.468097 174.336089 
L 573.509626 174.336089 
L 573.509626 215.699888 
L 542.468097 215.699888 
z
" clip-path="url(#pdc229c91d7)" style="fill: #457b9d; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_17">
    <path d="M 594.203979 174.336089 
L 625.245508 174.336089 
L 625.245508 244.510071 
L 594.203979 244.510071 
z
" clip-path="url(#pdc229c91d7)" style="fill: #43aa8b; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_18">
    <path d="M 645.939861 174.336089 
L 676.981389 174.336089 
L 676.981389 212.850515 
L 645.939861 212.850515 
z
" clip-path="url(#pdc229c91d7)" style="fill: #577590; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_19">
    <path d="M 697.675742 174.336089 
L 728.717271 174.336089 
L 728.717271 192.840248 
L 697.675742 192.840248 
z
" clip-path="url(#pdc229c91d7)" style="fill: #1d3557; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_20">
    <path d="M 749.411624 174.336089 
L 780.453153 174.336089 
L 780.453153 188.295037 
L 749.411624 188.295037 
z
" clip-path="url(#pdc229c91d7)" style="fill: #254441; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_21">
    <path d="M 801.147505 174.336089 
L 832.189034 174.336089 
L 832.189034 177.24611 
L 801.147505 177.24611 
z
" clip-path="url(#pdc229c91d7)" style="fill: #2a3d45; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_8">
     <g id="line2d_41">
      <g>
       <use xlink:href="#md3fd86860a" x="506.25298" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 练习阶段 -->
      <g transform="translate(476.752264 349.576414) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
       <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_42">
      <g>
       <use xlink:href="#md3fd86860a" x="557.988862" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 静息态1 -->
      <g transform="translate(531.316573 346.833061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_43">
      <g>
       <use xlink:href="#md3fd86860a" x="609.724743" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 静息态2 -->
      <g transform="translate(583.052455 346.833061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_44">
      <g>
       <use xlink:href="#md3fd86860a" x="661.460625" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 静息态3 -->
      <g transform="translate(634.788336 346.833061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_45">
      <g>
       <use xlink:href="#md3fd86860a" x="713.196507" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 刺激态1 -->
      <g transform="translate(686.524218 346.847424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_46">
      <g>
       <use xlink:href="#md3fd86860a" x="764.932388" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 刺激态2 -->
      <g transform="translate(738.260099 346.847424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_47">
      <g>
       <use xlink:href="#md3fd86860a" x="816.66827" y="309.003205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 刺激态3 -->
      <g transform="translate(789.995981 346.847424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_11">
     <g id="line2d_48">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="267.093636" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_44">
      <!-- −2 -->
      <g transform="translate(457.159375 270.628792) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_49">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="220.714863" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_45">
      <!-- −1 -->
      <g transform="translate(457.159375 224.250019) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_50">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="174.336089" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_46">
      <!-- 0 -->
      <g transform="translate(460.659375 177.871246) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_51">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="127.957316" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 1 -->
      <g transform="translate(460.659375 131.492473) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_52">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="81.578543" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 2 -->
      <g transform="translate(460.659375 85.113699) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_53">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="35.19977" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 3 -->
      <g transform="translate(460.659375 38.734926) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_54">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="290.283022" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_55">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="243.904249" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_56">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="197.525476" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_57">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="151.146703" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_58">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="104.76793" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_59">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="58.389156" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_50">
     <!-- HEP振幅 (μV) -->
     <g transform="translate(451.259375 195.671134) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-48"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="69.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="133.799973"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="195.59996"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="295.599945"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="395.59993"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="430.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="465.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="521.799896"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="583.799881"/>
     </g>
    </g>
    <g id="text_51">
     <!-- 1e−6 -->
     <g transform="translate(473.659375 17.459375) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 506.25298 210.463985 
L 506.25298 171.98345 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 557.988862 232.172093 
L 557.988862 199.227683 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 609.724743 295.887576 
L 609.724743 193.132567 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 661.460625 235.596527 
L 661.460625 190.104503 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 713.196507 202.726011 
L 713.196507 182.954486 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 764.932388 199.618195 
L 764.932388 176.971879 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
    <path d="M 816.66827 183.592293 
L 816.66827 170.899927 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000"/>
   </g>
   <g id="line2d_60">
    <g clip-path="url(#pdc229c91d7)">
     <use xlink:href="#mec487baa02" x="506.25298" y="210.463985" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="557.988862" y="232.172093" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="609.724743" y="295.887576" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="661.460625" y="235.596527" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="713.196507" y="202.726011" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="764.932388" y="199.618195" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="816.66827" y="183.592293" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_61">
    <g clip-path="url(#pdc229c91d7)">
     <use xlink:href="#mec487baa02" x="506.25298" y="171.98345" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="557.988862" y="199.227683" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="609.724743" y="193.132567" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="661.460625" y="190.104503" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="713.196507" y="182.954486" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="764.932388" y="176.971879" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="816.66827" y="170.899927" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_62">
    <path d="M 506.25298 67.671751 
L 557.988862 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_63">
    <path d="M 506.25298 60.852402 
L 609.724743 60.852402 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_64">
    <path d="M 506.25298 54.033052 
L 661.460625 54.033052 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_65">
    <path d="M 506.25298 47.213703 
L 713.196507 47.213703 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_66">
    <path d="M 506.25298 40.394353 
L 764.932388 40.394353 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_67">
    <path d="M 506.25298 33.575004 
L 816.66827 33.575004 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 557.988862 67.671751 
L 609.724743 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_69">
    <path d="M 557.988862 60.852402 
L 661.460625 60.852402 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 557.988862 54.033052 
L 713.196507 54.033052 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_71">
    <path d="M 557.988862 47.213703 
L 764.932388 47.213703 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_72">
    <path d="M 557.988862 40.394353 
L 816.66827 40.394353 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_73">
    <path d="M 609.724743 67.671751 
L 661.460625 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 609.724743 60.852402 
L 713.196507 60.852402 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_75">
    <path d="M 609.724743 54.033052 
L 764.932388 54.033052 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_76">
    <path d="M 609.724743 47.213703 
L 816.66827 47.213703 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_77">
    <path d="M 661.460625 67.671751 
L 713.196507 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_78">
    <path d="M 661.460625 60.852402 
L 764.932388 60.852402 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_79">
    <path d="M 661.460625 54.033052 
L 816.66827 54.033052 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_80">
    <path d="M 713.196507 67.671751 
L 764.932388 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_81">
    <path d="M 713.196507 60.852402 
L 816.66827 60.852402 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_82">
    <path d="M 764.932388 67.671751 
L 816.66827 67.671751 
" clip-path="url(#pdc229c91d7)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_22">
    <path d="M 473.659375 309.003205 
L 473.659375 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_23">
    <path d="M 849.261875 309.003205 
L 849.261875 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_24">
    <path d="M 473.659375 309.003205 
L 849.261875 309.003205 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_25">
    <path d="M 473.659375 20.459375 
L 849.261875 20.459375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_52">
    <!-- ns -->
    <g transform="translate(526.631077 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_53">
    <!-- ns -->
    <g transform="translate(552.499018 53.67661) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_54">
    <!-- ns -->
    <g transform="translate(578.366959 46.85726) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_55">
    <!-- ns -->
    <g transform="translate(604.2349 40.037911) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_56">
    <!-- ns -->
    <g transform="translate(630.10284 33.218561) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_57">
    <!-- ns -->
    <g transform="translate(655.970781 26.399212) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_58">
    <!-- ns -->
    <g transform="translate(578.366959 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_59">
    <!-- ns -->
    <g transform="translate(604.2349 53.67661) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_60">
    <!-- ns -->
    <g transform="translate(630.10284 46.85726) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_61">
    <!-- ns -->
    <g transform="translate(655.970781 40.037911) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_62">
    <!-- * -->
    <g transform="translate(684.828566 33.218561) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-2a" d="M 1805 4416 
L 1798 4326 
Q 1786 4186 1770 4026 
Q 1754 3866 1741 3699 
Q 2176 3898 2297 3974 
Q 2419 4051 2496 4051 
Q 2573 4051 2627 3945 
Q 2682 3840 2682 3738 
Q 2682 3610 2490 3571 
L 1837 3437 
Q 2118 3072 2208 2982 
L 2330 2854 
Q 2368 2822 2368 2768 
Q 2368 2714 2275 2614 
Q 2182 2515 2083 2515 
Q 1984 2515 1949 2569 
Q 1914 2624 1882 2701 
Q 1850 2778 1766 2934 
Q 1683 3091 1606 3251 
Q 1408 2918 1350 2764 
Q 1293 2611 1257 2556 
Q 1222 2502 1136 2502 
Q 1050 2502 944 2585 
Q 838 2669 838 2739 
Q 838 2810 909 2893 
Q 1190 3219 1338 3411 
Q 1005 3450 845 3459 
Q 685 3469 611 3488 
Q 538 3507 538 3616 
Q 538 3725 579 3837 
Q 621 3949 717 3949 
Q 749 3949 979 3853 
Q 1210 3757 1440 3680 
L 1331 4422 
Q 1331 4570 1574 4570 
L 1594 4570 
Q 1728 4570 1766 4525 
Q 1805 4480 1805 4416 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-2a"/>
    </g>
   </g>
   <g id="text_63">
    <!-- ns -->
    <g transform="translate(630.10284 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_64">
    <!-- ns -->
    <g transform="translate(655.970781 53.67661) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_65">
    <!-- ns -->
    <g transform="translate(681.838722 46.85726) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_66">
    <!-- ns -->
    <g transform="translate(707.706663 40.037911) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_67">
    <!-- ns -->
    <g transform="translate(681.838722 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_68">
    <!-- ns -->
    <g transform="translate(707.706663 53.67661) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_69">
    <!-- ns -->
    <g transform="translate(733.574604 46.85726) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_70">
    <!-- ns -->
    <g transform="translate(733.574604 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_71">
    <!-- ns -->
    <g transform="translate(759.442544 53.67661) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_72">
    <!-- ns -->
    <g transform="translate(785.310485 60.495959) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_73">
    <!-- 通道 F7 的HEP振幅对比 -->
    <g transform="translate(607.270781 14.459375) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-37" d="M 3392 4205 
Q 3366 4115 3296 4003 
Q 3226 3891 3072 3584 
Q 1997 1402 1626 32 
Q 1606 -70 1414 -70 
Q 1222 -70 1180 0 
Q 1139 70 1139 144 
Q 1139 218 1628 1424 
Q 2118 2630 2829 4064 
L 1005 3904 
Q 960 3898 912 3888 
Q 864 3878 777 3878 
Q 691 3878 620 3964 
Q 550 4051 521 4147 
Q 493 4243 493 4256 
Q 493 4314 531 4314 
L 762 4301 
L 2816 4448 
Q 2893 4454 2960 4473 
Q 3027 4493 3085 4493 
L 3104 4493 
Q 3187 4486 3289 4390 
Q 3392 4294 3392 4205 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-901a"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-46" x="234.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-37" x="293.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="353.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="388.199921"/>
     <use xlink:href="#LXGWWenKai-Regular-48" x="488.199905"/>
     <use xlink:href="#LXGWWenKai-Regular-45" x="558.19989"/>
     <use xlink:href="#LXGWWenKai-Regular-50" x="621.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-632f" x="683.799866"/>
     <use xlink:href="#LXGWWenKai-Regular-5e45" x="783.79985"/>
     <use xlink:href="#LXGWWenKai-Regular-5bf9" x="883.799835"/>
     <use xlink:href="#LXGWWenKai-Regular-6bd4" x="983.79982"/>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_26">
    <path d="M 47.059375 663.603205 
L 422.661875 663.603205 
L 422.661875 375.059375 
L 47.059375 375.059375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_27">
    <path d="M 64.132216 535.345526 
L 95.173745 535.345526 
L 95.173745 596.684579 
L 64.132216 596.684579 
z
" clip-path="url(#p794d5382d5)" style="fill: #e63946; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_28">
    <path d="M 115.868097 535.345526 
L 146.909626 535.345526 
L 146.909626 604.249985 
L 115.868097 604.249985 
z
" clip-path="url(#p794d5382d5)" style="fill: #457b9d; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_29">
    <path d="M 167.603979 535.345526 
L 198.645508 535.345526 
L 198.645508 560.588069 
L 167.603979 560.588069 
z
" clip-path="url(#p794d5382d5)" style="fill: #43aa8b; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_30">
    <path d="M 219.339861 535.345526 
L 250.381389 535.345526 
L 250.381389 567.968984 
L 219.339861 567.968984 
z
" clip-path="url(#p794d5382d5)" style="fill: #577590; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_31">
    <path d="M 271.075742 535.345526 
L 302.117271 535.345526 
L 302.117271 559.757014 
L 271.075742 559.757014 
z
" clip-path="url(#p794d5382d5)" style="fill: #1d3557; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_32">
    <path d="M 322.811624 535.345526 
L 353.853153 535.345526 
L 353.853153 549.696392 
L 322.811624 549.696392 
z
" clip-path="url(#p794d5382d5)" style="fill: #254441; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_33">
    <path d="M 374.547505 535.345526 
L 405.589034 535.345526 
L 405.589034 540.372788 
L 374.547505 540.372788 
z
" clip-path="url(#p794d5382d5)" style="fill: #2a3d45; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_15">
     <g id="line2d_83">
      <g>
       <use xlink:href="#md3fd86860a" x="79.65298" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_74">
      <!-- 练习阶段 -->
      <g transform="translate(50.152264 704.176414) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
       <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_84">
      <g>
       <use xlink:href="#md3fd86860a" x="131.388862" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_75">
      <!-- 静息态1 -->
      <g transform="translate(104.716573 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_85">
      <g>
       <use xlink:href="#md3fd86860a" x="183.124743" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_76">
      <!-- 静息态2 -->
      <g transform="translate(156.452455 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_86">
      <g>
       <use xlink:href="#md3fd86860a" x="234.860625" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_77">
      <!-- 静息态3 -->
      <g transform="translate(208.188336 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_87">
      <g>
       <use xlink:href="#md3fd86860a" x="286.596507" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_78">
      <!-- 刺激态1 -->
      <g transform="translate(259.924218 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_88">
      <g>
       <use xlink:href="#md3fd86860a" x="338.332388" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_79">
      <!-- 刺激态2 -->
      <g transform="translate(311.660099 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_89">
      <g>
       <use xlink:href="#md3fd86860a" x="390.06827" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_80">
      <!-- 刺激态3 -->
      <g transform="translate(363.395981 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_23">
     <g id="line2d_90">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="640.745318" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_81">
      <!-- −3 -->
      <g transform="translate(30.559375 644.280475) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_91">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="605.612054" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_82">
      <!-- −2 -->
      <g transform="translate(30.559375 609.147211) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_92">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="570.47879" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_83">
      <!-- −1 -->
      <g transform="translate(30.559375 574.013947) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_93">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="535.345526" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_84">
      <!-- 0 -->
      <g transform="translate(34.059375 538.880683) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_94">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="500.212262" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_85">
      <!-- 1 -->
      <g transform="translate(34.059375 503.747419) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_28">
     <g id="line2d_95">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="465.078998" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_86">
      <!-- 2 -->
      <g transform="translate(34.059375 468.614155) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_29">
     <g id="line2d_96">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="429.945734" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_87">
      <!-- 3 -->
      <g transform="translate(34.059375 433.48089) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="ytick_30">
     <g id="line2d_97">
      <g>
       <use xlink:href="#mb783f06030" x="47.059375" y="394.81247" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_88">
      <!-- 4 -->
      <g transform="translate(34.059375 398.347626) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_31">
     <g id="line2d_98">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="658.31195" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_32">
     <g id="line2d_99">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="623.178686" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_33">
     <g id="line2d_100">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="588.045422" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_34">
     <g id="line2d_101">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="552.912158" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_35">
     <g id="line2d_102">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="517.778894" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_36">
     <g id="line2d_103">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="482.64563" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_37">
     <g id="line2d_104">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="447.512366" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_38">
     <g id="line2d_105">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="412.379102" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_39">
     <g id="line2d_106">
      <g>
       <use xlink:href="#m4503210ea2" x="47.059375" y="377.245838" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_89">
     <!-- HEP振幅 (μV) -->
     <g transform="translate(24.659375 550.271134) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-48"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="69.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="133.799973"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="195.59996"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="295.599945"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="395.59993"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="430.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="465.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="521.799896"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="583.799881"/>
     </g>
    </g>
    <g id="text_90">
     <!-- 1e−7 -->
     <g transform="translate(47.059375 372.059375) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-37" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_3">
    <path d="M 79.65298 650.487576 
L 79.65298 542.881581 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 131.388862 644.015093 
L 131.388862 564.484877 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 183.124743 600.188495 
L 183.124743 520.987643 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 234.860625 622.82137 
L 234.860625 513.116597 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 286.596507 592.181744 
L 286.596507 527.332284 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 338.332388 574.403632 
L 338.332388 524.989152 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
    <path d="M 390.06827 564.078415 
L 390.06827 516.667162 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000"/>
   </g>
   <g id="line2d_107">
    <g clip-path="url(#p794d5382d5)">
     <use xlink:href="#mec487baa02" x="79.65298" y="650.487576" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="131.388862" y="644.015093" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="183.124743" y="600.188495" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="234.860625" y="622.82137" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="286.596507" y="592.181744" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="338.332388" y="574.403632" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="390.06827" y="564.078415" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_108">
    <g clip-path="url(#p794d5382d5)">
     <use xlink:href="#mec487baa02" x="79.65298" y="542.881581" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="131.388862" y="564.484877" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="183.124743" y="520.987643" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="234.860625" y="513.116597" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="286.596507" y="527.332284" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="338.332388" y="524.989152" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="390.06827" y="516.667162" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_109">
    <path d="M 79.65298 423.585847 
L 131.388862 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_110">
    <path d="M 79.65298 416.503678 
L 183.124743 416.503678 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_111">
    <path d="M 79.65298 409.421509 
L 234.860625 409.421509 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_112">
    <path d="M 79.65298 402.339341 
L 286.596507 402.339341 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_113">
    <path d="M 79.65298 395.257172 
L 338.332388 395.257172 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_114">
    <path d="M 79.65298 388.175004 
L 390.06827 388.175004 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_115">
    <path d="M 131.388862 423.585847 
L 183.124743 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_116">
    <path d="M 131.388862 416.503678 
L 234.860625 416.503678 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_117">
    <path d="M 131.388862 409.421509 
L 286.596507 409.421509 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_118">
    <path d="M 131.388862 402.339341 
L 338.332388 402.339341 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_119">
    <path d="M 131.388862 395.257172 
L 390.06827 395.257172 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_120">
    <path d="M 183.124743 423.585847 
L 234.860625 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_121">
    <path d="M 183.124743 416.503678 
L 286.596507 416.503678 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_122">
    <path d="M 183.124743 409.421509 
L 338.332388 409.421509 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_123">
    <path d="M 183.124743 402.339341 
L 390.06827 402.339341 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_124">
    <path d="M 234.860625 423.585847 
L 286.596507 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_125">
    <path d="M 234.860625 416.503678 
L 338.332388 416.503678 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_126">
    <path d="M 234.860625 409.421509 
L 390.06827 409.421509 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_127">
    <path d="M 286.596507 423.585847 
L 338.332388 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_128">
    <path d="M 286.596507 416.503678 
L 390.06827 416.503678 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_129">
    <path d="M 338.332388 423.585847 
L 390.06827 423.585847 
" clip-path="url(#p794d5382d5)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_34">
    <path d="M 47.059375 663.603205 
L 47.059375 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_35">
    <path d="M 422.661875 663.603205 
L 422.661875 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_36">
    <path d="M 47.059375 663.603205 
L 422.661875 663.603205 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_37">
    <path d="M 47.059375 375.059375 
L 422.661875 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_91">
    <!-- ns -->
    <g transform="translate(100.031077 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_92">
    <!-- ns -->
    <g transform="translate(125.899018 409.117631) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_93">
    <!-- ns -->
    <g transform="translate(151.766959 402.035462) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_94">
    <!-- ns -->
    <g transform="translate(177.6349 394.953293) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_95">
    <!-- ns -->
    <g transform="translate(203.50284 387.871125) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_96">
    <!-- ns -->
    <g transform="translate(229.370781 380.788956) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_97">
    <!-- ns -->
    <g transform="translate(151.766959 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_98">
    <!-- ns -->
    <g transform="translate(177.6349 409.117631) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_99">
    <!-- ns -->
    <g transform="translate(203.50284 402.035462) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_100">
    <!-- ns -->
    <g transform="translate(229.370781 394.953293) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_101">
    <!-- ns -->
    <g transform="translate(255.238722 387.871125) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_102">
    <!-- ns -->
    <g transform="translate(203.50284 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_103">
    <!-- ns -->
    <g transform="translate(229.370781 409.117631) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_104">
    <!-- ns -->
    <g transform="translate(255.238722 402.035462) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_105">
    <!-- ns -->
    <g transform="translate(281.106663 394.953293) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_106">
    <!-- ns -->
    <g transform="translate(255.238722 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_107">
    <!-- ns -->
    <g transform="translate(281.106663 409.117631) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_108">
    <!-- ns -->
    <g transform="translate(306.974604 402.035462) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_109">
    <!-- ns -->
    <g transform="translate(306.974604 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_110">
    <!-- ns -->
    <g transform="translate(332.842544 409.117631) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_111">
    <!-- ns -->
    <g transform="translate(358.710485 416.199799) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_112">
    <!-- 通道 F8 的HEP振幅对比 -->
    <g transform="translate(180.670781 369.059375) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-38" d="M 1811 2304 
Q 1338 1971 1078 1708 
Q 819 1446 819 1126 
Q 819 915 922 723 
Q 1152 294 1798 294 
Q 2317 294 2621 515 
Q 2925 736 2925 1104 
Q 2925 1472 2797 1670 
Q 2669 1869 2422 2009 
Q 2176 2150 1811 2304 
z
M 1773 2739 
Q 2355 3110 2989 3616 
Q 2976 3610 2912 3610 
Q 2848 3610 2790 3699 
Q 2496 4179 1939 4179 
Q 1530 4179 1280 3971 
Q 1030 3763 1030 3504 
Q 1030 3245 1212 3081 
Q 1395 2918 1773 2739 
z
M 3130 3731 
Q 3238 3840 3315 3840 
Q 3418 3840 3418 3738 
Q 3418 3552 2528 2848 
Q 2317 2682 2157 2566 
Q 2752 2342 3069 2006 
Q 3386 1670 3386 1123 
Q 3386 576 2963 233 
Q 2541 -109 1798 -109 
Q 1363 -109 1040 51 
Q 717 211 541 489 
Q 365 768 365 1107 
Q 365 1805 1421 2496 
Q 1011 2714 796 2944 
Q 582 3174 582 3472 
Q 582 3770 748 4013 
Q 915 4256 1219 4406 
Q 1523 4557 1878 4557 
Q 2234 4557 2464 4464 
Q 2694 4371 2848 4236 
Q 3002 4102 3078 3987 
Q 3155 3872 3155 3827 
Q 3155 3782 3130 3731 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-901a"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-46" x="234.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-38" x="293.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="353.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="388.199921"/>
     <use xlink:href="#LXGWWenKai-Regular-48" x="488.199905"/>
     <use xlink:href="#LXGWWenKai-Regular-45" x="558.19989"/>
     <use xlink:href="#LXGWWenKai-Regular-50" x="621.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-632f" x="683.799866"/>
     <use xlink:href="#LXGWWenKai-Regular-5e45" x="783.79985"/>
     <use xlink:href="#LXGWWenKai-Regular-5bf9" x="883.799835"/>
     <use xlink:href="#LXGWWenKai-Regular-6bd4" x="983.79982"/>
    </g>
   </g>
  </g>
  <g id="axes_4">
   <g id="patch_38">
    <path d="M 473.659375 663.603205 
L 849.261875 663.603205 
L 849.261875 375.059375 
L 473.659375 375.059375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_39">
    <path d="M 490.732216 591.371335 
L 521.773745 591.371335 
L 521.773745 620.314909 
L 490.732216 620.314909 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #e63946; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_40">
    <path d="M 542.468097 591.371335 
L 573.509626 591.371335 
L 573.509626 575.418314 
L 542.468097 575.418314 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #457b9d; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_41">
    <path d="M 594.203979 591.371335 
L 625.245508 591.371335 
L 625.245508 622.260002 
L 594.203979 622.260002 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #43aa8b; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_42">
    <path d="M 645.939861 591.371335 
L 676.981389 591.371335 
L 676.981389 553.58434 
L 645.939861 553.58434 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #577590; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_43">
    <path d="M 697.675742 591.371335 
L 728.717271 591.371335 
L 728.717271 604.275876 
L 697.675742 604.275876 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #1d3557; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_44">
    <path d="M 749.411624 591.371335 
L 780.453153 591.371335 
L 780.453153 605.720387 
L 749.411624 605.720387 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #254441; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_45">
    <path d="M 801.147505 591.371335 
L 832.189034 591.371335 
L 832.189034 593.66775 
L 801.147505 593.66775 
z
" clip-path="url(#p937ccdb8a6)" style="fill: #2a3d45; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_7">
    <g id="xtick_22">
     <g id="line2d_130">
      <g>
       <use xlink:href="#md3fd86860a" x="506.25298" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_113">
      <!-- 练习阶段 -->
      <g transform="translate(476.752264 704.176414) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-7ec3"/>
       <use xlink:href="#LXGWWenKai-Regular-4e60" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-9636" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-6bb5" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_131">
      <g>
       <use xlink:href="#md3fd86860a" x="557.988862" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_114">
      <!-- 静息态1 -->
      <g transform="translate(531.316573 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_132">
      <g>
       <use xlink:href="#md3fd86860a" x="609.724743" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_115">
      <!-- 静息态2 -->
      <g transform="translate(583.052455 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_133">
      <g>
       <use xlink:href="#md3fd86860a" x="661.460625" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_116">
      <!-- 静息态3 -->
      <g transform="translate(634.788336 701.433061) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-9759"/>
       <use xlink:href="#LXGWWenKai-Regular-606f" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_134">
      <g>
       <use xlink:href="#md3fd86860a" x="713.196507" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_117">
      <!-- 刺激态1 -->
      <g transform="translate(686.524218 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_135">
      <g>
       <use xlink:href="#md3fd86860a" x="764.932388" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_118">
      <!-- 刺激态2 -->
      <g transform="translate(738.260099 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_28">
     <g id="line2d_136">
      <g>
       <use xlink:href="#md3fd86860a" x="816.66827" y="663.603205" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_119">
      <!-- 刺激态3 -->
      <g transform="translate(789.995981 701.447424) rotate(-45) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-523a"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6001" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-33" x="299.999954"/>
      </g>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_8">
    <g id="ytick_40">
     <g id="line2d_137">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="633.12701" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_120">
      <!-- −0.2 -->
      <g transform="translate(447.659375 636.662166) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2212"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="129.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_41">
     <g id="line2d_138">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="591.371335" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_121">
      <!-- 0.0 -->
      <g transform="translate(451.159375 594.906491) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_42">
     <g id="line2d_139">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="549.61566" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_122">
      <!-- 0.2 -->
      <g transform="translate(451.159375 553.150816) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_43">
     <g id="line2d_140">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="507.859985" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_123">
      <!-- 0.4 -->
      <g transform="translate(451.159375 511.395141) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_44">
     <g id="line2d_141">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="466.10431" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_124">
      <!-- 0.6 -->
      <g transform="translate(451.159375 469.639466) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-36" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_45">
     <g id="line2d_142">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="424.348635" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_125">
      <!-- 0.8 -->
      <g transform="translate(451.159375 427.883791) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-38" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_46">
     <g id="line2d_143">
      <g>
       <use xlink:href="#mb783f06030" x="473.659375" y="382.59296" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_126">
      <!-- 1.0 -->
      <g transform="translate(451.159375 386.128116) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_47">
     <g id="line2d_144">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="654.004847" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_48">
     <g id="line2d_145">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="612.249172" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_49">
     <g id="line2d_146">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="570.493497" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_50">
     <g id="line2d_147">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="528.737822" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_51">
     <g id="line2d_148">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="486.982147" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_52">
     <g id="line2d_149">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="445.226473" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="ytick_53">
     <g id="line2d_150">
      <g>
       <use xlink:href="#m4503210ea2" x="473.659375" y="403.470798" style="stroke: #000000; stroke-width: 0.6"/>
      </g>
     </g>
    </g>
    <g id="text_127">
     <!-- HEP振幅 (μV) -->
     <g transform="translate(441.759375 550.271134) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-48"/>
      <use xlink:href="#LXGWWenKai-Regular-45" x="69.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-50" x="133.799973"/>
      <use xlink:href="#LXGWWenKai-Regular-632f" x="195.59996"/>
      <use xlink:href="#LXGWWenKai-Regular-5e45" x="295.599945"/>
      <use xlink:href="#LXGWWenKai-Regular-20" x="395.59993"/>
      <use xlink:href="#LXGWWenKai-Regular-28" x="430.599915"/>
      <use xlink:href="#LXGWWenKai-Regular-3bc" x="465.599899"/>
      <use xlink:href="#LXGWWenKai-Regular-56" x="521.799896"/>
      <use xlink:href="#LXGWWenKai-Regular-29" x="583.799881"/>
     </g>
    </g>
    <g id="text_128">
     <!-- 1e−6 -->
     <g transform="translate(473.659375 372.059375) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-31"/>
      <use xlink:href="#LXGWWenKai-Regular-65" x="59.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-2212" x="114.299973"/>
      <use xlink:href="#LXGWWenKai-Regular-36" x="149.299957"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_4">
    <path d="M 506.25298 649.485023 
L 506.25298 591.144795 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 557.988862 605.059213 
L 557.988862 545.777416 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 609.724743 650.487576 
L 609.724743 594.032428 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 661.460625 613.241258 
L 661.460625 493.927422 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 713.196507 621.820816 
L 713.196507 586.730937 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 764.932388 628.133254 
L 764.932388 583.30752 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
    <path d="M 816.66827 611.264778 
L 816.66827 576.070722 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000"/>
   </g>
   <g id="line2d_151">
    <g clip-path="url(#p937ccdb8a6)">
     <use xlink:href="#mec487baa02" x="506.25298" y="649.485023" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="557.988862" y="605.059213" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="609.724743" y="650.487576" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="661.460625" y="613.241258" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="713.196507" y="621.820816" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="764.932388" y="628.133254" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="816.66827" y="611.264778" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_152">
    <g clip-path="url(#p937ccdb8a6)">
     <use xlink:href="#mec487baa02" x="506.25298" y="591.144795" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="557.988862" y="545.777416" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="609.724743" y="594.032428" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="661.460625" y="493.927422" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="713.196507" y="586.730937" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="764.932388" y="583.30752" style="stroke: #000000"/>
     <use xlink:href="#mec487baa02" x="816.66827" y="576.070722" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_153">
    <path d="M 506.25298 426.58792 
L 557.988862 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_154">
    <path d="M 506.25298 418.905337 
L 609.724743 418.905337 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_155">
    <path d="M 506.25298 411.222754 
L 661.460625 411.222754 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_156">
    <path d="M 506.25298 403.54017 
L 713.196507 403.54017 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_157">
    <path d="M 506.25298 395.857587 
L 764.932388 395.857587 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_158">
    <path d="M 506.25298 388.175004 
L 816.66827 388.175004 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_159">
    <path d="M 557.988862 426.58792 
L 609.724743 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_160">
    <path d="M 557.988862 418.905337 
L 661.460625 418.905337 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_161">
    <path d="M 557.988862 411.222754 
L 713.196507 411.222754 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_162">
    <path d="M 557.988862 403.54017 
L 764.932388 403.54017 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_163">
    <path d="M 557.988862 395.857587 
L 816.66827 395.857587 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_164">
    <path d="M 609.724743 426.58792 
L 661.460625 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_165">
    <path d="M 609.724743 418.905337 
L 713.196507 418.905337 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_166">
    <path d="M 609.724743 411.222754 
L 764.932388 411.222754 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_167">
    <path d="M 609.724743 403.54017 
L 816.66827 403.54017 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_168">
    <path d="M 661.460625 426.58792 
L 713.196507 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_169">
    <path d="M 661.460625 418.905337 
L 764.932388 418.905337 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_170">
    <path d="M 661.460625 411.222754 
L 816.66827 411.222754 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_171">
    <path d="M 713.196507 426.58792 
L 764.932388 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_172">
    <path d="M 713.196507 418.905337 
L 816.66827 418.905337 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="line2d_173">
    <path d="M 764.932388 426.58792 
L 816.66827 426.58792 
" clip-path="url(#p937ccdb8a6)" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linecap: square"/>
   </g>
   <g id="patch_46">
    <path d="M 473.659375 663.603205 
L 473.659375 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_47">
    <path d="M 849.261875 663.603205 
L 849.261875 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_48">
    <path d="M 473.659375 663.603205 
L 849.261875 663.603205 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_49">
    <path d="M 473.659375 375.059375 
L 849.261875 375.059375 
" style="fill: none; stroke: #000000; stroke-width: 0.5; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_129">
    <!-- ns -->
    <g transform="translate(526.631077 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_130">
    <!-- ns -->
    <g transform="translate(552.499018 411.038958) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_131">
    <!-- ns -->
    <g transform="translate(578.366959 403.356375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_132">
    <!-- ns -->
    <g transform="translate(604.2349 395.673791) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_133">
    <!-- ns -->
    <g transform="translate(630.10284 387.991208) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_134">
    <!-- ns -->
    <g transform="translate(655.970781 380.308624) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_135">
    <!-- ns -->
    <g transform="translate(578.366959 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_136">
    <!-- ns -->
    <g transform="translate(604.2349 411.038958) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_137">
    <!-- ns -->
    <g transform="translate(630.10284 403.356375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_138">
    <!-- ns -->
    <g transform="translate(655.970781 395.673791) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_139">
    <!-- ns -->
    <g transform="translate(681.838722 387.991208) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_140">
    <!-- ns -->
    <g transform="translate(630.10284 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_141">
    <!-- ns -->
    <g transform="translate(655.970781 411.038958) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_142">
    <!-- ns -->
    <g transform="translate(681.838722 403.356375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_143">
    <!-- ns -->
    <g transform="translate(707.706663 395.673791) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_144">
    <!-- ns -->
    <g transform="translate(681.838722 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_145">
    <!-- ns -->
    <g transform="translate(707.706663 411.038958) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_146">
    <!-- ns -->
    <g transform="translate(733.574604 403.356375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_147">
    <!-- ns -->
    <g transform="translate(733.574604 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_148">
    <!-- ns -->
    <g transform="translate(759.442544 411.038958) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_149">
    <!-- ns -->
    <g transform="translate(785.310485 418.721541) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-6e"/>
     <use xlink:href="#LXGWWenKai-Regular-73" x="58.799988"/>
    </g>
   </g>
   <g id="text_150">
    <!-- 通道 Cz 的HEP振幅对比 -->
    <g transform="translate(607.216094 369.059375) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-43" d="M 4224 960 
Q 4006 582 3558 282 
Q 2989 -115 2285 -115 
Q 1728 -115 1283 89 
Q 838 294 582 732 
Q 326 1171 326 1804 
Q 326 2438 499 2928 
Q 672 3418 982 3786 
Q 1293 4154 1709 4358 
Q 2125 4563 2611 4563 
Q 3565 4563 4019 4019 
Q 4038 3994 4038 3926 
Q 4038 3859 3955 3750 
Q 3872 3642 3798 3642 
Q 3725 3642 3686 3699 
Q 3392 4154 2621 4154 
Q 1850 4154 1331 3520 
Q 787 2874 787 1846 
Q 787 819 1523 461 
Q 1869 288 2243 288 
Q 2618 288 2944 400 
Q 3270 512 3571 717 
Q 3872 922 4070 1184 
Q 4147 1280 4230 1280 
Q 4314 1280 4314 1197 
Q 4314 1114 4224 960 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-901a"/>
     <use xlink:href="#LXGWWenKai-Regular-9053" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-43" x="234.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7a" x="305.299942"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="354.299927"/>
     <use xlink:href="#LXGWWenKai-Regular-7684" x="389.299911"/>
     <use xlink:href="#LXGWWenKai-Regular-48" x="489.299896"/>
     <use xlink:href="#LXGWWenKai-Regular-45" x="559.299881"/>
     <use xlink:href="#LXGWWenKai-Regular-50" x="623.099869"/>
     <use xlink:href="#LXGWWenKai-Regular-632f" x="684.899857"/>
     <use xlink:href="#LXGWWenKai-Regular-5e45" x="784.899841"/>
     <use xlink:href="#LXGWWenKai-Regular-5bf9" x="884.899826"/>
     <use xlink:href="#LXGWWenKai-Regular-6bd4" x="984.899811"/>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pa81449ea42">
   <rect x="47.059375" y="20.459375" width="375.6025" height="288.54383"/>
  </clipPath>
  <clipPath id="pdc229c91d7">
   <rect x="473.659375" y="20.459375" width="375.6025" height="288.54383"/>
  </clipPath>
  <clipPath id="p794d5382d5">
   <rect x="47.059375" y="375.059375" width="375.6025" height="288.54383"/>
  </clipPath>
  <clipPath id="p937ccdb8a6">
   <rect x="473.659375" y="375.059375" width="375.6025" height="288.54383"/>
  </clipPath>
 </defs>
</svg>

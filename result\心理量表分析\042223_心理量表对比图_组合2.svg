<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1080pt" height="432pt" viewBox="0 0 1080 432" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T23:21:02.727827</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 432 
L 1080 432 
L 1080 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 40.65125 330.0965 
L 534.665625 330.0965 
L 534.665625 25.08 
L 40.65125 25.08 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_3">
    <path d="M 63.106449 369.451447 
L 121.323631 369.451447 
L 121.323631 191.106481 
L 63.106449 191.106481 
z
" clip-path="url(#p06713e1258)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_4">
    <path d="M 229.441255 369.451447 
L 287.658438 369.451447 
L 287.658438 183.099156 
L 229.441255 183.099156 
z
" clip-path="url(#p06713e1258)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_5">
    <path d="M 395.776062 369.451447 
L 453.993244 369.451447 
L 453.993244 155.437488 
L 395.776062 155.437488 
z
" clip-path="url(#p06713e1258)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_6">
    <path d="M 121.323631 369.451447 
L 179.540813 369.451447 
L 179.540813 227.551942 
L 121.323631 227.551942 
z
" clip-path="url(#p06713e1258)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_7">
    <path d="M 287.658438 369.451447 
L 345.87562 369.451447 
L 345.87562 212.701994 
L 287.658438 212.701994 
z
" clip-path="url(#p06713e1258)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_8">
    <path d="M 453.993244 369.451447 
L 512.210426 369.451447 
L 512.210426 216.001982 
L 453.993244 216.001982 
z
" clip-path="url(#p06713e1258)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 121.323631 330.0965 
L 121.323631 25.08 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m045fcfd628" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m045fcfd628" x="121.323631" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 第一次刺激 -->
      <g transform="translate(101.323631 343.11275) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-7b2c" d="M 3904 5069 
L 3910 5146 
Q 3923 5248 3984 5248 
Q 4045 5248 4131 5190 
Q 4218 5133 4288 5062 
Q 4358 4992 4358 4947 
Q 4358 4902 4333 4870 
L 4147 4576 
L 5325 4666 
Q 5485 4685 5542 4704 
Q 5600 4723 5629 4723 
Q 5658 4723 5728 4685 
Q 5920 4570 5920 4454 
Q 5920 4384 5786 4371 
L 4550 4288 
Q 5114 3923 5114 3821 
Q 5114 3776 5046 3683 
Q 4979 3590 4918 3590 
Q 4858 3590 4755 3692 
Q 4653 3795 4397 3987 
Q 4301 4058 4301 4118 
Q 4301 4179 4397 4275 
L 3904 4243 
Q 3590 3866 3258 3616 
Q 3110 3507 3056 3507 
Q 3002 3507 3002 3546 
Q 3002 3603 3162 3779 
Q 3322 3955 3494 4218 
Q 3840 4730 3904 5069 
z
M 1773 5152 
Q 1850 5152 2000 5037 
Q 2150 4922 2150 4858 
Q 2150 4794 1907 4474 
L 2867 4538 
Q 2989 4550 3069 4576 
Q 3149 4602 3197 4602 
Q 3245 4602 3315 4550 
Q 3386 4499 3434 4435 
Q 3482 4371 3482 4339 
Q 3482 4262 3341 4250 
L 2259 4179 
Q 2342 4128 2585 3945 
Q 2829 3763 2829 3693 
Q 2829 3680 2797 3622 
Q 2714 3469 2618 3469 
Q 2579 3469 2428 3613 
Q 2278 3757 2048 3930 
Q 1978 3981 1978 4032 
Q 1978 4083 2061 4166 
L 1651 4141 
Q 1184 3558 736 3258 
Q 563 3142 515 3142 
Q 467 3142 467 3174 
Q 467 3219 557 3328 
Q 1178 3987 1651 4787 
Q 1709 4890 1709 5011 
L 1709 5062 
Q 1709 5152 1773 5152 
z
M 5280 1824 
L 5370 1830 
Q 5440 1830 5529 1763 
Q 5619 1696 5619 1632 
Q 5619 1568 5593 1542 
Q 5568 1517 5562 1485 
Q 5523 1165 5427 761 
Q 5331 358 5305 243 
Q 5280 128 5190 22 
Q 5101 -83 5021 -83 
Q 4941 -83 4720 13 
Q 4499 109 4102 358 
Q 3706 608 3706 717 
Q 3706 749 3766 749 
Q 3827 749 4166 611 
Q 4506 474 4870 384 
L 4890 384 
Q 4922 384 4928 416 
Q 5114 1037 5178 1485 
L 3398 1382 
L 3392 -525 
Q 3392 -666 3277 -666 
Q 3142 -666 3052 -586 
Q 2963 -506 2963 -454 
L 2963 -384 
Q 2963 -365 2979 -310 
Q 2995 -256 2995 -64 
L 2995 38 
L 3008 1133 
Q 2246 371 1363 -64 
Q 998 -243 755 -323 
Q 512 -403 448 -403 
Q 384 -403 384 -378 
Q 384 -314 595 -198 
Q 1843 474 2694 1344 
L 1638 1280 
Q 1555 1274 1507 1258 
Q 1459 1242 1408 1242 
Q 1357 1242 1277 1296 
Q 1197 1350 1197 1414 
Q 1197 1478 1216 1523 
L 1440 2182 
Q 1472 2266 1472 2368 
L 1453 2573 
L 1453 2611 
Q 1453 2682 1504 2682 
Q 1581 2682 1837 2515 
L 3021 2579 
L 3027 3098 
L 1830 3021 
Q 1792 3014 1754 3014 
L 1690 3014 
Q 1542 3014 1465 3091 
Q 1389 3168 1366 3248 
Q 1344 3328 1344 3350 
Q 1344 3373 1376 3373 
L 1645 3347 
L 1715 3347 
L 4794 3539 
L 4851 3539 
Q 5037 3539 5088 3462 
Q 5139 3386 5139 3354 
Q 5139 3322 5107 3283 
Q 5075 3245 5062 3206 
L 4928 2656 
Q 5082 2483 5082 2428 
Q 5082 2374 5027 2368 
Q 4973 2362 4896 2355 
L 3405 2272 
L 3405 1722 
L 5280 1824 
z
M 4672 3206 
L 3411 3123 
L 3405 2598 
L 4557 2662 
L 4672 3206 
z
M 3014 2246 
L 1805 2176 
L 1632 1619 
L 3008 1696 
L 3014 2246 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-4e00" d="M 704 2451 
L 5357 2688 
Q 5510 2701 5574 2726 
Q 5638 2752 5676 2752 
Q 5715 2752 5804 2697 
Q 5894 2643 5968 2563 
Q 6042 2483 6042 2419 
Q 6042 2317 5894 2304 
L 947 2048 
Q 896 2042 851 2042 
L 774 2042 
Q 646 2042 582 2086 
Q 474 2176 426 2304 
Q 378 2432 378 2464 
Q 378 2496 400 2496 
Q 422 2496 499 2473 
Q 576 2451 704 2451 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6b21" d="M 3315 4672 
Q 3386 4954 3386 4998 
Q 3386 5043 3366 5158 
Q 3366 5229 3437 5229 
Q 3456 5229 3558 5197 
Q 3853 5107 3853 4973 
Q 3853 4870 3702 4435 
Q 3552 4000 3450 3757 
L 5485 3891 
Q 5523 3898 5555 3898 
L 5606 3898 
Q 5709 3898 5795 3827 
Q 5882 3757 5882 3712 
Q 5882 3578 5338 2829 
Q 5011 2406 4896 2406 
Q 4858 2406 4858 2464 
Q 4858 2522 4941 2688 
Q 5222 3206 5338 3514 
L 3290 3392 
Q 2925 2611 2547 2208 
Q 2394 2048 2336 2048 
Q 2298 2048 2298 2105 
Q 2298 2163 2355 2266 
Q 2963 3296 3315 4672 
z
M 1600 3130 
Q 1242 3603 755 4058 
Q 691 4122 691 4179 
Q 691 4237 777 4310 
Q 864 4384 905 4384 
Q 947 4384 1145 4230 
Q 1344 4077 1673 3725 
Q 2003 3373 2003 3280 
Q 2003 3187 1907 3100 
Q 1811 3014 1756 3014 
Q 1702 3014 1600 3130 
z
M 4198 2682 
Q 4173 2368 4096 2061 
Q 4480 1120 5350 397 
Q 5754 70 6163 -186 
Q 6246 -237 6246 -275 
Q 6246 -288 6189 -352 
Q 6029 -531 5913 -531 
Q 5798 -531 5296 -89 
Q 4794 352 4483 726 
Q 4173 1101 3949 1549 
Q 3731 858 3136 320 
Q 2573 -205 1978 -467 
Q 1760 -563 1699 -563 
Q 1638 -563 1638 -499 
Q 1638 -435 1747 -371 
Q 2426 51 2944 621 
Q 3558 1299 3725 2342 
Q 3763 2586 3763 2678 
Q 3763 2771 3731 2841 
Q 3699 2912 3699 2931 
Q 3699 2989 3763 2989 
Q 3827 2989 3930 2963 
Q 4198 2893 4198 2733 
L 4198 2682 
z
M 717 339 
Q 614 358 508 419 
Q 403 480 358 541 
Q 314 602 314 630 
Q 314 659 442 723 
Q 570 787 848 1065 
Q 1126 1344 1914 2310 
Q 1939 2336 2003 2406 
Q 2067 2477 2115 2477 
Q 2163 2477 2163 2409 
Q 2163 2342 2099 2227 
Q 2035 2112 1792 1712 
Q 1549 1312 1197 825 
Q 845 339 717 339 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-523a" d="M 5638 -109 
L 5645 -294 
Q 5645 -474 5539 -563 
Q 5434 -653 5354 -653 
Q 5274 -653 5136 -576 
Q 4998 -499 4841 -380 
Q 4685 -262 4537 -137 
Q 4390 -13 4297 92 
Q 4205 198 4205 243 
Q 4205 288 4262 288 
Q 4320 288 4595 144 
Q 4870 0 5229 -122 
L 5242 4794 
Q 5242 4934 5146 5082 
Q 5120 5126 5120 5164 
Q 5120 5203 5200 5203 
Q 5280 5203 5433 5148 
Q 5587 5094 5619 5052 
Q 5651 5011 5651 4941 
L 5638 -109 
z
M 1869 -371 
Q 1914 -13 1914 198 
L 1920 1632 
Q 1466 902 858 435 
Q 608 243 441 156 
Q 275 70 233 70 
Q 192 70 192 124 
Q 192 179 333 294 
Q 1229 1082 1926 2285 
L 1926 2989 
L 1274 2950 
L 1306 1824 
L 1306 1805 
Q 1306 1715 1216 1715 
Q 1146 1715 1024 1776 
Q 902 1837 902 1946 
Q 902 1984 921 2067 
Q 941 2150 941 2310 
L 941 2387 
L 922 2874 
Q 902 3219 880 3283 
Q 858 3347 848 3369 
Q 838 3392 838 3411 
Q 838 3456 924 3456 
Q 1011 3456 1344 3302 
L 1933 3334 
L 1933 3878 
L 1107 3834 
Q 992 3821 915 3821 
Q 838 3821 736 3865 
Q 634 3910 563 4128 
Q 550 4166 550 4188 
Q 550 4211 585 4211 
Q 621 4211 685 4195 
Q 749 4179 870 4179 
L 947 4179 
L 1939 4237 
L 1939 4736 
Q 1939 4909 1888 4976 
Q 1837 5043 1837 5069 
Q 1837 5120 1949 5120 
Q 2061 5120 2192 5072 
Q 2323 5024 2323 4954 
L 2323 4256 
L 3072 4307 
Q 3232 4314 3299 4336 
Q 3366 4358 3395 4358 
Q 3424 4358 3507 4314 
Q 3738 4192 3738 4070 
Q 3738 3994 3622 3974 
L 2323 3898 
L 2317 3360 
L 3174 3405 
L 3226 3405 
Q 3334 3405 3420 3331 
Q 3507 3258 3507 3197 
Q 3507 3136 3491 3101 
Q 3475 3066 3469 3021 
L 3443 2323 
L 3443 2150 
Q 3443 1952 3340 1865 
Q 3238 1779 3174 1779 
Q 3053 1779 2749 2038 
Q 2445 2298 2445 2413 
Q 2445 2445 2489 2445 
Q 2534 2445 2694 2355 
Q 2854 2266 3059 2195 
L 3091 3053 
L 2317 3008 
L 2317 1837 
Q 2374 1901 2422 1901 
Q 2470 1901 2684 1737 
Q 2899 1574 3270 1203 
Q 3642 832 3642 733 
Q 3642 634 3494 525 
Q 3437 480 3398 480 
Q 3360 480 3213 666 
Q 2861 1107 2317 1581 
L 2310 -454 
Q 2310 -634 2189 -634 
Q 2086 -634 1977 -541 
Q 1869 -448 1869 -371 
z
M 4058 3680 
Q 4058 3834 4006 3917 
Q 3955 4000 3955 4038 
Q 3955 4077 4044 4077 
Q 4134 4077 4250 4026 
Q 4384 3981 4416 3939 
Q 4448 3898 4448 3821 
L 4467 966 
Q 4467 819 4346 819 
Q 4243 819 4137 896 
Q 4032 973 4032 1094 
Q 4032 1139 4048 1232 
Q 4064 1325 4064 1504 
L 4058 3680 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6fc0" d="M 2029 2560 
Q 2054 2739 2054 2778 
L 1984 3910 
Q 1971 4038 1929 4134 
Q 1888 4230 1888 4275 
Q 1888 4320 1968 4320 
Q 2048 4320 2336 4205 
L 2400 4211 
Q 2528 4474 2595 4694 
Q 2662 4915 2662 4986 
L 2637 5146 
Q 2637 5190 2694 5190 
Q 2701 5190 2790 5158 
Q 3078 5075 3078 4941 
Q 3078 4845 2950 4598 
Q 2822 4352 2746 4237 
L 3366 4282 
Q 3398 4288 3424 4288 
L 3507 4288 
Q 3565 4288 3641 4217 
Q 3718 4147 3718 4099 
Q 3718 4051 3702 4025 
Q 3686 4000 3680 3968 
L 3546 2867 
Q 3680 2707 3680 2649 
Q 3680 2592 3632 2585 
Q 3584 2579 3514 2573 
L 2400 2515 
L 2400 2438 
Q 2400 2336 2301 2336 
Q 2202 2336 2115 2409 
Q 2029 2483 2029 2560 
z
M 4941 4794 
Q 4941 4602 4550 3648 
L 5517 3712 
Q 5632 3725 5699 3750 
Q 5766 3776 5798 3776 
Q 5830 3776 5901 3738 
Q 6106 3622 6106 3514 
Q 6106 3430 5978 3418 
L 5587 3392 
Q 5414 2214 5056 1408 
Q 5517 461 6202 -211 
Q 6253 -262 6253 -297 
Q 6253 -333 6195 -384 
Q 6061 -512 5939 -512 
Q 5843 -512 5472 16 
Q 5101 544 4870 1011 
Q 4506 326 3987 -179 
Q 3776 -390 3625 -496 
Q 3475 -602 3430 -602 
Q 3386 -602 3386 -550 
Q 3386 -499 3456 -429 
Q 4192 371 4672 1434 
Q 4390 2086 4192 2880 
Q 4154 2816 4083 2688 
Q 3763 2112 3661 2112 
Q 3622 2112 3622 2176 
Q 3622 2240 3674 2355 
Q 4147 3360 4442 4480 
Q 4506 4730 4506 4816 
Q 4506 4902 4486 4944 
Q 4467 4986 4467 4998 
Q 4467 5050 4531 5050 
Q 4570 5050 4672 5018 
Q 4941 4928 4941 4794 
z
M 1443 4297 
Q 1574 4173 1660 4077 
Q 1747 3981 1747 3945 
Q 1747 3910 1667 3808 
Q 1587 3706 1520 3706 
Q 1453 3706 1357 3821 
Q 1261 3936 1053 4141 
Q 845 4346 717 4442 
Q 589 4538 589 4592 
Q 589 4646 656 4723 
Q 723 4800 771 4800 
Q 819 4800 928 4723 
Q 1037 4646 1174 4534 
Q 1312 4422 1443 4297 
z
M 3315 3987 
L 2330 3917 
L 2349 3526 
L 3277 3584 
L 3315 3987 
z
M 1126 2451 
Q 672 2842 326 3085 
Q 256 3136 256 3184 
Q 256 3232 320 3318 
Q 384 3405 435 3405 
Q 486 3405 736 3245 
Q 986 3085 1389 2758 
Q 1459 2707 1459 2659 
Q 1459 2611 1424 2550 
Q 1389 2490 1337 2438 
Q 1286 2387 1248 2387 
Q 1210 2387 1126 2451 
z
M 4410 3315 
Q 4595 2522 4851 1882 
Q 5069 2426 5178 3366 
L 4410 3315 
z
M 3251 3290 
L 2362 3238 
L 2381 2803 
L 3206 2854 
L 3251 3290 
z
M 2835 1690 
L 2701 1344 
L 3334 1389 
L 3418 1389 
Q 3571 1389 3635 1296 
Q 3699 1203 3699 1174 
Q 3699 1146 3683 1120 
Q 3667 1094 3654 1062 
Q 3501 358 3277 -166 
Q 3232 -275 3123 -339 
Q 3040 -397 2970 -397 
Q 2829 -397 2522 -90 
Q 2221 198 2221 294 
Q 2221 326 2259 326 
Q 2298 326 2467 233 
Q 2637 141 2768 83 
Q 2899 26 2915 26 
Q 2931 26 2950 64 
Q 3168 544 3264 1075 
L 2554 1030 
Q 2304 538 2029 234 
Q 1754 -70 1552 -208 
Q 1350 -346 1308 -346 
Q 1267 -346 1267 -304 
Q 1267 -262 1344 -179 
Q 2080 608 2426 1664 
L 2067 1638 
Q 2029 1632 1997 1632 
L 1901 1632 
Q 1837 1632 1760 1664 
Q 1594 1811 1594 1946 
Q 1594 1971 1619 1971 
Q 1645 1971 1689 1958 
Q 1734 1946 1862 1946 
L 1952 1946 
L 2694 1990 
L 2694 2150 
Q 2694 2291 2640 2358 
Q 2586 2426 2586 2445 
Q 2586 2490 2646 2490 
Q 2707 2490 2803 2470 
Q 3046 2419 3046 2272 
L 3040 2010 
L 3520 2042 
Q 3661 2054 3709 2073 
Q 3757 2093 3808 2093 
Q 3859 2093 3929 2045 
Q 4000 1997 4048 1936 
Q 4096 1875 4096 1843 
Q 4096 1779 3968 1760 
L 2835 1690 
z
M 602 -186 
Q 371 -154 297 -83 
Q 224 -13 224 9 
Q 224 32 282 64 
Q 422 134 934 1114 
Q 1158 1549 1363 2022 
Q 1434 2195 1504 2195 
Q 1542 2195 1542 2121 
Q 1542 2048 1411 1571 
Q 1280 1094 1030 515 
Q 781 -64 739 -125 
Q 698 -186 627 -186 
L 602 -186 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e00" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 287.658438 330.0965 
L 287.658438 25.08 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m045fcfd628" x="287.658438" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 第二次刺激 -->
      <g transform="translate(267.658438 343.11275) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-4e8c" d="M 1734 3424 
Q 1683 3418 1638 3418 
L 1536 3418 
Q 1395 3418 1328 3472 
Q 1261 3526 1209 3641 
Q 1158 3757 1158 3805 
Q 1158 3853 1203 3853 
Q 1222 3853 1289 3833 
Q 1357 3814 1523 3814 
L 1606 3814 
L 4301 3974 
Q 4435 3987 4518 4016 
Q 4602 4045 4640 4045 
Q 4678 4045 4768 3987 
Q 4998 3840 4998 3699 
Q 4998 3610 4832 3597 
L 1734 3424 
z
M 691 864 
L 5254 1056 
Q 5427 1069 5520 1097 
Q 5613 1126 5654 1126 
Q 5696 1126 5792 1068 
Q 5888 1011 5965 928 
Q 6042 845 6042 781 
Q 6042 678 5882 666 
L 973 461 
Q 909 454 858 454 
L 768 454 
Q 614 454 569 489 
Q 525 525 429 633 
Q 333 742 333 822 
Q 333 902 368 902 
Q 403 902 473 883 
Q 544 864 691 864 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 453.993244 330.0965 
L 453.993244 25.08 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m045fcfd628" x="453.993244" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 第三次刺激 -->
      <g transform="translate(433.993244 343.11275) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-4e09" d="M 1613 3808 
L 1414 3802 
Q 1267 3802 1200 3862 
Q 1133 3923 1088 4054 
Q 1043 4186 1043 4192 
Q 1043 4237 1088 4237 
Q 1107 4237 1168 4217 
Q 1229 4198 1408 4198 
L 1491 4198 
L 4570 4378 
Q 4704 4390 4787 4419 
Q 4870 4448 4918 4448 
Q 4966 4448 5052 4381 
Q 5139 4314 5203 4234 
Q 5267 4154 5267 4109 
Q 5267 4013 5094 4000 
L 1613 3808 
z
M 2054 2093 
L 1869 2086 
Q 1728 2086 1670 2131 
Q 1626 2176 1555 2288 
Q 1485 2400 1485 2470 
Q 1485 2541 1530 2541 
Q 1549 2541 1625 2518 
Q 1702 2496 1869 2496 
L 1933 2496 
L 4275 2611 
Q 4410 2624 4493 2653 
Q 4576 2682 4617 2682 
Q 4659 2682 4748 2618 
Q 4838 2554 4905 2470 
Q 4973 2387 4973 2330 
Q 4973 2234 4800 2221 
L 2054 2093 
z
M 730 518 
L 5293 672 
Q 5395 678 5523 707 
Q 5651 736 5692 736 
Q 5734 736 5830 678 
Q 5926 621 6003 537 
Q 6080 454 6080 390 
Q 6080 275 5914 262 
L 1005 96 
L 832 90 
Q 653 90 569 157 
Q 486 224 428 339 
Q 371 454 371 505 
Q 371 557 406 557 
Q 442 557 512 537 
Q 582 518 730 518 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e09" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="text_4">
     <!-- 刺激次数 -->
     <g transform="translate(267.658437 355.959312) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-6570" d="M 5510 3302 
Q 5267 2163 4851 1350 
Q 5357 512 6214 -205 
Q 6285 -262 6285 -300 
Q 6285 -339 6160 -425 
Q 6035 -512 5961 -512 
Q 5888 -512 5843 -467 
Q 5139 237 4659 998 
Q 4237 326 3674 -154 
Q 3443 -352 3286 -451 
Q 3130 -550 3085 -550 
Q 3040 -550 3040 -499 
Q 3040 -448 3130 -358 
Q 3552 58 3865 461 
Q 4179 864 4448 1370 
Q 4179 1888 3917 2688 
Q 3718 2304 3552 2089 
Q 3386 1875 3328 1875 
Q 3290 1875 3290 1939 
Q 3290 2003 3334 2099 
Q 3898 3341 4096 4403 
Q 4160 4768 4160 4829 
Q 4160 4890 4141 4973 
L 4141 4986 
Q 4141 5043 4205 5043 
Q 4243 5043 4339 5005 
Q 4621 4883 4621 4742 
Q 4621 4678 4496 4246 
Q 4371 3814 4275 3546 
L 5338 3616 
Q 5472 3629 5571 3661 
Q 5670 3693 5728 3693 
Q 5786 3693 5862 3629 
Q 6048 3482 6048 3398 
Q 6048 3341 5920 3328 
L 5510 3302 
z
M 1798 2432 
Q 1830 2624 1830 2790 
L 1830 2925 
Q 1830 2970 1843 3059 
L 1862 3142 
Q 1811 3053 1754 2976 
Q 1280 2490 762 2214 
Q 557 2106 502 2106 
Q 448 2106 448 2144 
Q 448 2202 550 2278 
Q 1114 2707 1645 3309 
L 992 3264 
L 915 3264 
Q 851 3264 758 3302 
Q 666 3341 614 3539 
Q 608 3552 608 3574 
Q 608 3597 633 3597 
Q 659 3597 713 3581 
Q 768 3565 870 3565 
L 1830 3622 
L 1830 4621 
Q 1830 4781 1795 4851 
Q 1760 4922 1760 4957 
Q 1760 4992 1811 4992 
Q 1862 4992 1952 4973 
Q 2202 4922 2202 4794 
L 2195 3642 
L 2931 3693 
Q 3072 3699 3136 3721 
Q 3200 3744 3257 3744 
Q 3315 3744 3408 3651 
Q 3501 3558 3501 3494 
Q 3501 3430 3366 3418 
L 2189 3341 
L 2189 3162 
Q 2234 3245 2307 3245 
Q 2381 3245 2736 3037 
Q 3091 2829 3187 2758 
Q 3283 2688 3283 2624 
Q 3283 2560 3219 2477 
Q 3155 2394 3113 2394 
Q 3072 2394 3002 2445 
Q 2662 2733 2227 2982 
Q 2221 2989 2211 3001 
Q 2202 3014 2189 3021 
L 2182 2368 
Q 2182 2208 2083 2208 
Q 1984 2208 1891 2281 
Q 1798 2355 1798 2432 
z
M 2822 4666 
Q 2835 4749 2880 4749 
Q 2899 4749 2970 4698 
Q 3187 4563 3187 4473 
Q 3187 4384 2931 4160 
Q 2458 3744 2323 3744 
Q 2291 3744 2291 3782 
Q 2291 3821 2467 4022 
Q 2643 4224 2784 4486 
Q 2822 4538 2822 4666 
z
M 1325 3821 
Q 1114 4109 909 4314 
Q 864 4371 864 4406 
Q 864 4442 918 4502 
Q 973 4563 1021 4563 
Q 1069 4563 1117 4518 
Q 1165 4474 1257 4384 
Q 1350 4294 1440 4201 
Q 1530 4109 1594 4029 
Q 1658 3949 1658 3910 
Q 1658 3872 1578 3798 
Q 1498 3725 1446 3725 
Q 1395 3725 1325 3821 
z
M 4160 3232 
L 4134 3162 
Q 4346 2342 4634 1754 
Q 4915 2432 5062 3283 
L 4160 3232 
z
M 2554 1920 
L 2547 2016 
Q 2547 2080 2604 2080 
Q 2662 2080 2739 2035 
Q 2950 1920 2950 1760 
L 2950 1747 
L 3341 1786 
L 3411 1792 
Q 3558 1792 3558 1728 
Q 3558 1645 3360 1606 
Q 3162 1568 2874 1523 
Q 2669 915 2336 499 
Q 2726 275 2905 156 
Q 3085 38 3085 -32 
Q 3085 -102 3033 -208 
Q 2982 -314 2918 -314 
Q 2854 -314 2640 -134 
Q 2426 45 2080 243 
Q 1466 -326 602 -474 
Q 454 -499 448 -499 
Q 352 -499 352 -454 
Q 352 -378 621 -282 
Q 1280 -45 1786 422 
Q 1382 646 1229 685 
Q 1120 710 1120 858 
Q 1120 902 1331 1280 
L 877 1235 
Q 774 1222 710 1222 
Q 461 1222 358 1581 
L 358 1600 
Q 358 1638 390 1638 
Q 422 1638 499 1619 
Q 576 1600 704 1600 
L 774 1600 
Q 947 1606 1129 1616 
Q 1312 1626 1504 1632 
Q 1670 2003 1670 2131 
L 1670 2195 
Q 1670 2272 1705 2272 
Q 1741 2272 1818 2227 
Q 2042 2093 2042 1978 
Q 2042 1920 1920 1658 
Q 2080 1670 2217 1686 
Q 2355 1702 2522 1715 
L 2541 1792 
Q 2554 1856 2554 1920 
z
M 1517 928 
Q 1683 858 2029 666 
Q 2272 973 2445 1453 
L 1754 1338 
Q 1690 1222 1635 1123 
Q 1581 1024 1517 928 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-523a"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_7">
      <path d="M 40.65125 307.576663 
L 534.665625 307.576663 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <defs>
       <path id="m0a116f9c05" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m0a116f9c05" x="40.65125" y="307.576663" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 5 -->
      <g transform="translate(28.85125 310.404788) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_9">
      <path d="M 40.65125 245.701879 
L 534.665625 245.701879 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m0a116f9c05" x="40.65125" y="245.701879" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 10 -->
      <g transform="translate(24.05125 248.530004) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_11">
      <path d="M 40.65125 183.827094 
L 534.665625 183.827094 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m0a116f9c05" x="40.65125" y="183.827094" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 15 -->
      <g transform="translate(24.05125 186.655219) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_13">
      <path d="M 40.65125 121.95231 
L 534.665625 121.95231 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m0a116f9c05" x="40.65125" y="121.95231" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 20 -->
      <g transform="translate(24.05125 124.780435) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_15">
      <path d="M 40.65125 60.077526 
L 534.665625 60.077526 
" clip-path="url(#p06713e1258)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m0a116f9c05" x="40.65125" y="60.077526" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 25 -->
      <g transform="translate(24.05125 62.905651) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- 主观疼痛分数 -->
     <g transform="translate(18.330937 207.58825) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-4e3b" d="M 3712 4102 
Q 3014 4570 2285 4870 
Q 2176 4922 2176 4998 
Q 2176 5075 2224 5148 
Q 2272 5222 2330 5222 
Q 2509 5222 3277 4826 
Q 3686 4627 3898 4499 
Q 4026 4429 4026 4345 
Q 4026 4262 3958 4160 
Q 3891 4058 3833 4058 
Q 3776 4058 3712 4102 
z
M 1491 3334 
L 1293 3328 
Q 1171 3328 1120 3360 
Q 979 3456 947 3571 
Q 915 3686 915 3693 
Q 915 3738 947 3738 
Q 979 3738 1046 3718 
Q 1114 3699 1280 3699 
L 1363 3699 
L 4717 3891 
Q 4864 3904 4941 3929 
Q 5018 3955 5059 3955 
Q 5101 3955 5184 3898 
Q 5408 3750 5408 3635 
Q 5408 3552 5248 3539 
L 3398 3437 
L 3398 2093 
L 4506 2144 
Q 4678 2163 4726 2182 
Q 4774 2202 4812 2202 
Q 4851 2202 4931 2147 
Q 5011 2093 5075 2019 
Q 5139 1946 5139 1894 
Q 5139 1818 4998 1805 
L 3392 1734 
L 3392 237 
L 5338 307 
Q 5498 320 5571 345 
Q 5645 371 5677 371 
Q 5709 371 5798 307 
Q 6054 147 6054 19 
Q 6054 -70 5901 -83 
L 960 -262 
L 742 -269 
Q 506 -269 410 -64 
Q 346 77 346 134 
Q 346 192 390 192 
Q 416 192 512 169 
Q 608 147 742 147 
L 794 147 
L 2950 224 
L 2950 1715 
L 1837 1664 
L 1677 1658 
Q 1542 1658 1475 1715 
Q 1408 1773 1360 1878 
Q 1312 1984 1312 2019 
Q 1312 2054 1344 2054 
Q 1376 2054 1456 2035 
Q 1536 2016 1651 2016 
L 1702 2016 
L 2957 2074 
L 2957 3411 
L 1491 3334 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-89c2" d="M 627 4301 
L 890 4275 
L 947 4275 
L 2470 4365 
L 2541 4365 
Q 2630 4365 2723 4301 
Q 2816 4237 2816 4173 
Q 2816 4109 2790 4070 
Q 2765 4032 2701 3731 
Q 2528 2912 2176 2118 
Q 2586 1606 3040 966 
Q 3117 870 3117 809 
Q 3117 749 3062 694 
Q 3008 640 2944 608 
Q 2880 576 2835 576 
Q 2790 576 2733 672 
Q 2413 1184 1990 1741 
Q 1491 845 870 198 
Q 621 -51 457 -176 
Q 294 -301 256 -301 
Q 218 -301 218 -240 
Q 218 -179 294 -102 
Q 1165 864 1734 2061 
Q 1325 2643 915 3110 
Q 851 3194 851 3251 
Q 851 3309 940 3373 
Q 1030 3437 1075 3437 
Q 1120 3437 1235 3302 
Q 1466 3014 1894 2451 
Q 2182 3187 2298 4000 
L 1094 3910 
Q 1037 3904 992 3904 
L 909 3904 
Q 781 3904 720 3965 
Q 659 4026 601 4128 
Q 544 4230 544 4265 
Q 544 4301 595 4301 
L 627 4301 
z
M 2374 -506 
Q 2310 -531 2288 -531 
Q 2266 -531 2266 -457 
Q 2266 -384 2374 -320 
Q 3277 205 3648 851 
Q 3853 1222 3945 1708 
Q 4038 2195 4038 3552 
Q 4038 3635 3977 3712 
Q 3917 3789 3917 3827 
Q 3917 3891 3997 3891 
Q 4077 3891 4224 3849 
Q 4371 3808 4416 3773 
Q 4461 3738 4461 3654 
Q 4442 2675 4397 2131 
Q 4563 2086 4611 2038 
Q 4659 1990 4659 1894 
Q 4659 1798 4640 320 
Q 4640 141 4675 61 
Q 4710 -19 4825 -35 
Q 4941 -51 5277 -51 
Q 5613 -51 5728 -22 
Q 5843 6 5894 166 
Q 5990 493 6038 832 
Q 6086 1171 6163 1171 
Q 6195 1171 6233 1036 
Q 6272 902 6272 553 
Q 6272 205 6253 22 
Q 6234 -160 6147 -259 
Q 6061 -358 5865 -396 
Q 5670 -435 5264 -435 
Q 4858 -435 4650 -387 
Q 4442 -339 4346 -198 
Q 4250 -58 4250 218 
L 4250 243 
L 4262 1344 
Q 4186 1069 4064 832 
Q 3629 -6 2374 -506 
z
M 3514 2451 
L 3526 2029 
L 3533 1696 
L 3533 1638 
Q 3533 1504 3430 1504 
Q 3354 1504 3232 1571 
Q 3110 1638 3110 1728 
L 3110 1760 
Q 3130 1907 3130 2035 
L 3130 2138 
L 3078 4352 
Q 3078 4518 3020 4624 
Q 2963 4730 2963 4771 
Q 2963 4813 3043 4813 
Q 3123 4813 3462 4691 
L 5152 4800 
L 5222 4800 
Q 5318 4800 5420 4729 
Q 5523 4659 5523 4604 
Q 5523 4550 5510 4515 
Q 5498 4480 5491 4448 
L 5421 1664 
Q 5414 1530 5312 1536 
Q 5235 1536 5116 1606 
Q 4998 1677 4998 1766 
L 4998 1798 
Q 5024 1946 5024 2074 
L 5030 2176 
L 5069 4448 
L 3462 4352 
L 3514 2451 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-75bc" d="M 3341 5222 
Q 3782 5222 3782 5050 
L 3782 4358 
L 5190 4442 
Q 5402 4467 5456 4483 
Q 5510 4499 5545 4499 
Q 5581 4499 5664 4454 
Q 5888 4333 5888 4211 
Q 5888 4141 5766 4128 
L 1894 3898 
Q 1894 2720 1820 2093 
Q 1747 1466 1571 976 
Q 1395 486 1193 153 
Q 992 -179 829 -348 
Q 666 -518 618 -518 
Q 570 -518 570 -464 
Q 570 -410 634 -307 
Q 1248 698 1421 2035 
Q 1357 1939 1133 1702 
Q 723 1261 592 1261 
Q 461 1261 329 1376 
Q 198 1491 198 1523 
Q 198 1555 320 1594 
Q 557 1664 1453 2362 
Q 1485 2893 1485 3465 
Q 1485 4038 1427 4179 
Q 1370 4320 1370 4346 
Q 1370 4397 1453 4397 
Q 1536 4397 1901 4250 
L 3379 4339 
L 3373 4845 
Q 3373 4966 3296 5107 
Q 3290 5133 3280 5149 
Q 3270 5165 3270 5178 
Q 3270 5222 3341 5222 
z
M 3616 3366 
L 4730 3443 
Q 4813 3443 4934 3369 
Q 5056 3296 5056 3219 
Q 5056 3142 5014 3104 
Q 4973 3066 4960 3046 
Q 4602 2515 4141 2067 
Q 4998 1478 6074 1146 
Q 6170 1120 6170 1062 
Q 6170 1037 6112 960 
Q 5952 762 5843 762 
L 5670 819 
Q 4723 1171 3866 1818 
Q 2970 1043 1984 730 
Q 1792 666 1734 666 
Q 1677 666 1677 710 
Q 1677 787 1856 870 
Q 2726 1286 3584 2048 
Q 3309 2278 2970 2630 
Q 2675 2362 2464 2230 
Q 2253 2099 2185 2070 
Q 2118 2042 2073 2042 
Q 2029 2042 2029 2090 
Q 2029 2138 2265 2342 
Q 2502 2547 2806 2931 
Q 3110 3315 3302 3680 
Q 3347 3757 3350 3859 
Q 3354 3962 3414 3962 
Q 3475 3962 3568 3910 
Q 3661 3859 3731 3788 
Q 3802 3718 3802 3673 
Q 3802 3629 3616 3366 
z
M 1050 2451 
Q 960 2451 890 2630 
Q 742 3014 486 3386 
Q 448 3456 448 3497 
Q 448 3539 521 3596 
Q 595 3654 649 3654 
Q 704 3654 784 3561 
Q 864 3469 957 3331 
Q 1050 3194 1133 3050 
Q 1216 2906 1267 2797 
Q 1318 2688 1318 2643 
Q 1318 2598 1216 2524 
Q 1114 2451 1050 2451 
z
M 3200 2867 
Q 3539 2515 3840 2291 
Q 4314 2752 4518 3091 
L 3341 3021 
L 3200 2867 
z
M 3373 986 
Q 3258 1030 3258 1101 
Q 3258 1146 3283 1197 
Q 3328 1312 3405 1312 
Q 3482 1312 3914 1142 
Q 4346 973 4486 893 
Q 4627 813 4627 739 
Q 4627 666 4582 570 
Q 4538 474 4467 474 
Q 4397 474 4125 640 
Q 3853 806 3373 986 
z
M 3053 141 
Q 2931 192 2931 272 
Q 2931 352 2976 422 
Q 3021 493 3081 493 
Q 3142 493 3673 285 
Q 4205 77 4659 -173 
Q 4794 -237 4794 -345 
Q 4794 -454 4726 -544 
Q 4659 -634 4620 -634 
Q 4582 -634 4547 -614 
Q 4512 -595 4166 -387 
Q 3821 -179 3053 141 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-75db" d="M 1978 4038 
Q 1939 2528 1868 2035 
Q 1798 1542 1622 1046 
Q 1446 550 1244 201 
Q 1043 -147 883 -329 
Q 723 -512 675 -512 
Q 627 -512 627 -457 
Q 627 -403 685 -301 
Q 966 186 1174 794 
Q 1382 1402 1485 1971 
Q 1414 1901 1059 1622 
Q 704 1344 602 1344 
Q 435 1344 237 1594 
Q 211 1619 211 1641 
Q 211 1664 294 1696 
Q 595 1734 1530 2285 
Q 1581 2714 1581 3226 
L 1581 3949 
Q 1581 4205 1539 4297 
Q 1498 4390 1482 4425 
Q 1466 4461 1466 4480 
Q 1466 4518 1533 4518 
Q 1600 4518 1984 4378 
L 3418 4461 
L 3411 4883 
Q 3411 5037 3360 5107 
Q 3309 5178 3309 5219 
Q 3309 5261 3373 5261 
Q 3437 5261 3539 5248 
Q 3821 5203 3821 5088 
L 3821 4486 
L 5190 4563 
Q 5402 4589 5453 4605 
Q 5504 4621 5558 4621 
Q 5613 4621 5696 4570 
Q 5882 4448 5882 4333 
Q 5882 4275 5766 4262 
L 1978 4038 
z
M 3616 -26 
Q 3648 230 3648 461 
L 3648 800 
L 2688 762 
L 2688 -326 
Q 2688 -480 2592 -480 
L 2522 -461 
Q 2298 -403 2298 -256 
Q 2298 -230 2314 -160 
Q 2330 -90 2330 198 
L 2349 2323 
Q 2349 2522 2301 2627 
Q 2253 2733 2253 2768 
Q 2253 2803 2320 2803 
Q 2387 2803 2694 2669 
L 3597 2714 
Q 3405 2867 3251 2966 
Q 3098 3066 3098 3117 
Q 3174 3315 3260 3315 
Q 3347 3315 3763 2982 
Q 4192 3232 4563 3494 
L 2874 3373 
Q 2829 3366 2790 3366 
L 2720 3366 
Q 2611 3366 2521 3440 
Q 2432 3514 2400 3610 
L 2368 3699 
Q 2368 3731 2419 3731 
Q 2432 3731 2445 3731 
L 2662 3706 
L 4819 3840 
L 4877 3840 
Q 4998 3840 5078 3760 
Q 5158 3680 5158 3616 
Q 5158 3552 5110 3517 
Q 5062 3482 4822 3290 
Q 4582 3098 4026 2778 
L 4070 2739 
L 5171 2797 
Q 5197 2803 5267 2803 
Q 5338 2803 5408 2726 
Q 5478 2650 5478 2592 
L 5453 2451 
L 5459 19 
L 5466 -166 
Q 5466 -288 5382 -400 
Q 5299 -512 5210 -512 
Q 5056 -512 4669 -217 
Q 4282 77 4282 166 
Q 4282 205 4336 205 
Q 4390 205 4572 125 
Q 4755 45 5101 -45 
L 5094 864 
L 3994 813 
L 3994 -141 
Q 3994 -275 3904 -275 
Q 3878 -275 3808 -243 
Q 3616 -166 3616 -26 
z
M 576 3341 
Q 538 3411 538 3459 
Q 538 3507 589 3546 
Q 704 3622 765 3622 
Q 826 3622 890 3532 
Q 954 3443 1043 3312 
Q 1133 3181 1219 3040 
Q 1306 2899 1363 2787 
Q 1421 2675 1421 2611 
Q 1421 2547 1312 2480 
Q 1203 2413 1148 2413 
Q 1094 2413 1065 2451 
Q 1037 2490 918 2749 
Q 800 3008 576 3341 
z
M 5088 2470 
L 3994 2413 
L 3994 1907 
L 5088 1958 
L 5088 2470 
z
M 3661 2394 
L 2688 2342 
L 2688 1850 
L 3654 1894 
L 3661 2394 
z
M 5094 1658 
L 3994 1606 
L 3994 1120 
L 5094 1165 
L 5094 1658 
z
M 3654 1594 
L 2688 1549 
L 2688 1069 
L 3654 1107 
L 3654 1594 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-4e3b"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-75bc" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-75db" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-5206" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1">
    <path d="M 92.21504 256.019161 
L 92.21504 126.1938 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 258.549846 245.069141 
L 258.549846 121.129171 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 424.884653 220.434297 
L 424.884653 90.440679 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_17">
    <defs>
     <path id="mde47009fda" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p06713e1258)">
     <use xlink:href="#mde47009fda" x="92.21504" y="256.019161" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="258.549846" y="245.069141" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="424.884653" y="220.434297" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_18">
    <g clip-path="url(#p06713e1258)">
     <use xlink:href="#mde47009fda" x="92.21504" y="126.1938" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="258.549846" y="121.129171" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="424.884653" y="90.440679" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 150.432222 298.567445 
L 150.432222 156.536439 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 316.767029 293.459659 
L 316.767029 131.944329 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 483.101835 290.768495 
L 483.101835 141.23547 
" clip-path="url(#p06713e1258)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_19">
    <g clip-path="url(#p06713e1258)">
     <use xlink:href="#mde47009fda" x="150.432222" y="298.567445" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="316.767029" y="293.459659" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="483.101835" y="290.768495" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_20">
    <g clip-path="url(#p06713e1258)">
     <use xlink:href="#mde47009fda" x="150.432222" y="156.536439" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="316.767029" y="131.944329" style="stroke: #000000"/>
     <use xlink:href="#mde47009fda" x="483.101835" y="141.23547" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_9">
    <path d="M 40.65125 330.0965 
L 40.65125 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 534.665625 330.0965 
L 534.665625 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_11">
    <path d="M 40.65125 330.0965 
L 534.665625 330.0965 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 40.65125 25.08 
L 534.665625 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_11">
    <!-- 高低焦虑组主观疼痛变化 -->
    <g transform="translate(232.658437 19.08) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-9ad8" d="M 1139 3987 
L 973 3981 
Q 774 3981 678 4218 
L 646 4294 
Q 646 4333 691 4333 
L 723 4333 
Q 832 4314 941 4314 
L 1011 4314 
L 2912 4416 
L 2906 4838 
Q 2906 4992 2822 5120 
Q 2803 5158 2803 5184 
Q 2803 5235 2880 5235 
Q 2957 5235 3107 5184 
Q 3258 5133 3296 5094 
Q 3334 5056 3334 4986 
L 3328 4442 
L 5005 4531 
Q 5126 4544 5193 4566 
Q 5261 4589 5309 4589 
Q 5357 4589 5427 4531 
Q 5613 4390 5613 4301 
Q 5613 4243 5466 4230 
L 1139 3987 
z
M 2387 2445 
Q 2387 2323 2291 2323 
Q 2163 2323 2070 2396 
Q 1978 2470 1978 2566 
L 1978 2605 
Q 1984 2643 1984 2688 
L 1984 2861 
Q 1984 2874 1952 3181 
Q 1920 3488 1885 3555 
Q 1850 3622 1834 3651 
Q 1818 3680 1818 3699 
Q 1818 3744 1907 3744 
Q 1997 3744 2330 3661 
L 4128 3776 
L 4186 3776 
Q 4314 3776 4390 3693 
Q 4467 3610 4467 3574 
Q 4467 3539 4451 3513 
Q 4435 3488 4422 3456 
L 4288 2925 
Q 4467 2752 4467 2697 
Q 4467 2643 4412 2633 
Q 4358 2624 4288 2618 
L 2381 2534 
L 2387 2483 
L 2387 2445 
z
M 4000 3437 
L 2310 3334 
L 2355 2861 
L 3898 2938 
L 4000 3437 
z
M 915 -346 
Q 986 77 986 1709 
Q 986 1875 941 1993 
Q 896 2112 896 2131 
Q 896 2189 985 2189 
Q 1075 2189 1382 2067 
L 5178 2240 
L 5242 2240 
Q 5389 2240 5440 2153 
Q 5491 2067 5491 2035 
L 5466 1882 
L 5446 -90 
L 5459 -301 
Q 5459 -410 5369 -499 
Q 5280 -589 5219 -589 
Q 5158 -589 4956 -512 
Q 4755 -435 4429 -243 
Q 4058 -45 4058 58 
Q 4058 96 4112 96 
Q 4166 96 4313 45 
Q 4461 -6 4662 -44 
Q 4864 -83 5037 -122 
L 5062 1907 
L 1382 1747 
L 1376 -435 
Q 1376 -602 1270 -602 
Q 1165 -602 1040 -522 
Q 915 -442 915 -346 
z
M 3930 1466 
L 4006 1472 
Q 4109 1472 4192 1392 
Q 4275 1312 4275 1261 
Q 4275 1210 4259 1187 
Q 4243 1165 4237 1133 
L 4128 634 
Q 4262 461 4262 406 
Q 4262 352 4211 342 
Q 4160 333 4090 326 
L 2528 269 
L 2534 186 
L 2534 147 
Q 2534 19 2445 19 
Q 2310 19 2224 92 
Q 2138 166 2138 243 
L 2138 365 
Q 2138 416 2131 467 
L 2080 1082 
Q 2061 1267 2016 1344 
Q 1971 1421 1971 1453 
Q 1971 1485 2067 1485 
Q 2163 1485 2445 1382 
L 3930 1466 
z
M 3834 1152 
L 2464 1082 
L 2502 595 
L 3763 640 
L 3834 1152 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-4f4e" d="M 4512 -627 
Q 4179 -269 3744 83 
Q 3674 128 3674 185 
Q 3674 243 3738 313 
Q 3802 384 3840 384 
Q 3981 384 4538 -102 
Q 4858 -384 4858 -467 
Q 4858 -512 4765 -598 
Q 4672 -685 4621 -685 
Q 4570 -685 4512 -627 
z
M 5901 -397 
Q 5722 -934 5152 -320 
Q 4845 6 4563 668 
Q 4282 1331 4109 2259 
L 2944 2170 
L 2931 442 
Q 3366 621 3654 765 
Q 3942 909 4000 909 
Q 4038 909 4038 858 
Q 4032 749 3625 477 
Q 3219 205 2809 -19 
Q 2400 -243 2313 -243 
Q 2227 -243 2156 -172 
Q 2086 -102 2032 -19 
Q 1978 64 1958 89 
Q 1939 115 1939 166 
Q 1939 218 1990 224 
L 2112 211 
Q 2240 211 2541 288 
L 2554 3648 
Q 2554 3994 2451 4198 
L 2445 4237 
Q 2445 4282 2512 4282 
Q 2579 4282 2970 4134 
Q 3507 4262 4150 4508 
Q 4794 4755 4941 4941 
Q 4998 5024 5024 5024 
Q 5050 5024 5146 4893 
Q 5242 4762 5242 4653 
Q 5242 4544 5165 4512 
Q 4659 4307 4250 4173 
Q 4314 3290 4429 2630 
Q 5101 2694 5213 2729 
Q 5325 2765 5395 2765 
Q 5466 2765 5590 2665 
Q 5715 2566 5715 2470 
Q 5715 2394 5581 2374 
L 4486 2291 
Q 4646 1562 4870 1021 
Q 5094 480 5289 189 
Q 5485 -102 5568 -102 
Q 5709 -96 6048 928 
Q 6131 1133 6172 1133 
Q 6214 1133 6214 973 
Q 6214 582 5901 -397 
z
M 1530 -538 
Q 1376 -538 1286 -458 
Q 1197 -378 1197 -288 
L 1203 -211 
Q 1235 -51 1235 147 
L 1261 2899 
Q 909 2438 624 2153 
Q 339 1869 250 1869 
Q 211 1869 211 1920 
Q 211 1971 294 2086 
Q 1011 3072 1395 3869 
Q 1779 4666 1779 4838 
Q 1779 4915 1754 4992 
Q 1754 5062 1827 5062 
Q 1901 5062 1997 5020 
Q 2093 4979 2166 4912 
Q 2240 4845 2240 4768 
Q 2240 4691 2057 4284 
Q 1875 3878 1619 3443 
L 1619 -416 
Q 1619 -538 1530 -538 
z
M 2944 2509 
L 4045 2605 
Q 3930 3322 3866 4038 
Q 3379 3898 2950 3795 
L 2944 2509 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7126" d="M 3610 4954 
L 3597 5030 
L 3597 5056 
Q 3597 5133 3661 5133 
Q 3725 5133 3821 5082 
Q 4070 4947 4070 4845 
Q 4070 4768 3907 4509 
Q 3744 4250 3635 4115 
L 4768 4192 
Q 4915 4205 4969 4224 
Q 5024 4243 5059 4243 
Q 5094 4243 5171 4198 
Q 5363 4070 5363 3949 
Q 5363 3885 5222 3872 
L 3654 3776 
L 3648 3238 
L 4435 3277 
Q 4582 3290 4630 3312 
Q 4678 3334 4713 3334 
Q 4749 3334 4826 3290 
Q 5024 3155 5024 3053 
Q 5024 2982 4877 2970 
L 3648 2912 
L 3648 2349 
L 4461 2387 
Q 4614 2406 4656 2425 
Q 4698 2445 4739 2445 
Q 4781 2445 4851 2390 
Q 4922 2336 4973 2265 
Q 5024 2195 5024 2157 
Q 5024 2099 4890 2080 
L 3648 2022 
L 3642 1427 
L 5120 1485 
Q 5242 1491 5296 1513 
Q 5350 1536 5404 1536 
Q 5459 1536 5529 1472 
Q 5600 1408 5645 1334 
Q 5690 1261 5690 1229 
Q 5690 1178 5555 1158 
L 1978 1030 
L 1978 832 
Q 1978 723 1882 723 
Q 1766 723 1644 812 
Q 1523 902 1523 953 
Q 1523 1005 1552 1113 
Q 1581 1222 1581 1459 
L 1600 3219 
Q 1184 2720 800 2394 
Q 627 2246 569 2246 
Q 512 2246 512 2307 
Q 512 2368 608 2483 
Q 1645 3731 2150 4813 
Q 2208 4941 2208 5062 
Q 2208 5184 2278 5184 
Q 2317 5184 2419 5133 
Q 2522 5082 2611 5005 
Q 2701 4928 2701 4883 
Q 2701 4838 2547 4579 
Q 2394 4320 2182 4026 
L 3264 4096 
Q 3482 4448 3546 4659 
Q 3610 4870 3610 4902 
L 3610 4954 
z
M 3270 3757 
L 1990 3680 
L 1984 3149 
L 3264 3213 
L 3270 3757 
z
M 3264 2893 
L 1984 2829 
L 1984 2266 
L 3258 2330 
L 3264 2893 
z
M 3258 2003 
L 1984 1946 
L 1978 1363 
L 3251 1414 
L 3258 2003 
z
M 5702 -467 
Q 5261 115 4736 582 
Q 4666 653 4666 710 
Q 4666 768 4742 832 
Q 4819 896 4873 896 
Q 4928 896 5053 790 
Q 5178 685 5370 509 
Q 5562 333 5725 166 
Q 5888 0 5990 -125 
Q 6093 -250 6093 -304 
Q 6093 -358 6006 -460 
Q 5920 -563 5849 -563 
Q 5779 -563 5702 -467 
z
M 448 -205 
Q 960 307 1158 736 
Q 1229 845 1257 845 
Q 1286 845 1350 826 
Q 1536 768 1536 678 
Q 1536 589 1341 294 
Q 1146 0 1018 -173 
Q 890 -346 787 -458 
Q 685 -570 643 -570 
Q 602 -570 496 -477 
Q 390 -384 390 -323 
Q 390 -262 448 -205 
z
M 4480 -294 
Q 4288 -550 4115 -339 
Q 3693 186 3315 506 
Q 3238 576 3238 627 
Q 3238 678 3312 752 
Q 3386 826 3440 826 
Q 3494 826 3616 733 
Q 3738 640 3888 499 
Q 4038 358 4182 208 
Q 4326 58 4422 -57 
Q 4518 -173 4518 -205 
Q 4518 -237 4480 -294 
z
M 2618 -320 
Q 2323 173 2054 454 
Q 1990 518 1990 572 
Q 1990 627 2080 698 
Q 2144 762 2195 762 
Q 2246 762 2342 672 
Q 2438 582 2553 448 
Q 2669 314 2774 173 
Q 2880 32 2947 -77 
Q 3014 -186 3014 -230 
Q 3014 -275 2918 -355 
Q 2822 -435 2755 -435 
Q 2688 -435 2618 -320 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8651" d="M 5523 4006 
L 5606 4013 
Q 5709 4013 5821 3917 
Q 5933 3821 5933 3757 
Q 5933 3693 5891 3661 
Q 5850 3629 5763 3513 
Q 5677 3398 5504 3219 
Q 5126 2816 4986 2816 
Q 4934 2816 4934 2880 
Q 4934 2944 5018 3046 
Q 5229 3302 5382 3661 
L 1690 3494 
Q 1690 2957 1661 2390 
Q 1632 1824 1510 1318 
Q 1254 333 442 -410 
Q 435 -416 425 -422 
Q 416 -429 410 -442 
Q 294 -531 236 -531 
Q 179 -531 179 -473 
Q 179 -416 269 -294 
Q 1037 685 1178 1862 
Q 1248 2477 1248 3219 
L 1248 3456 
Q 1229 3789 1200 3865 
Q 1171 3942 1171 3977 
Q 1171 4013 1241 4013 
Q 1312 4013 1709 3827 
L 3002 3885 
L 3002 4941 
Q 3002 5088 2906 5222 
Q 2886 5254 2886 5267 
Q 2886 5306 2956 5306 
Q 3027 5306 3136 5286 
Q 3418 5235 3418 5069 
L 3411 4672 
L 4685 4755 
Q 4832 4768 4886 4787 
Q 4941 4806 4976 4806 
Q 5011 4806 5082 4762 
Q 5274 4634 5274 4547 
Q 5274 4461 5114 4448 
L 3405 4339 
L 3398 3904 
L 5523 4006 
z
M 1875 2790 
L 2010 2765 
Q 2061 2758 2112 2758 
L 2157 2758 
L 2931 2829 
L 2931 3091 
Q 2931 3232 2835 3347 
Q 2816 3379 2816 3398 
Q 2816 3456 2944 3456 
Q 3072 3456 3226 3386 
Q 3322 3341 3322 3232 
L 3322 2861 
L 4250 2944 
Q 4371 2950 4438 2982 
Q 4506 3014 4538 3014 
Q 4570 3014 4640 2970 
Q 4832 2848 4832 2752 
Q 4832 2675 4704 2656 
L 3322 2534 
L 3322 2214 
Q 3322 2093 3427 2064 
Q 3533 2035 3875 2035 
Q 4218 2035 4461 2057 
Q 4704 2080 4835 2121 
Q 4966 2163 4995 2163 
Q 5024 2163 5094 2125 
Q 5280 2003 5280 1888 
Q 5280 1786 5094 1760 
Q 4307 1677 3706 1677 
L 3430 1683 
Q 2925 1696 2925 2150 
L 2925 2502 
L 2266 2445 
Q 2240 2438 2214 2438 
Q 2189 2438 2163 2438 
Q 1997 2438 1920 2560 
Q 1843 2682 1843 2736 
Q 1843 2790 1875 2790 
z
M 1658 -205 
Q 1587 -320 1507 -320 
Q 1427 -320 1337 -240 
Q 1248 -160 1248 -109 
Q 1248 -58 1299 13 
Q 1613 512 1798 1120 
Q 1837 1242 1933 1242 
Q 2003 1242 2086 1194 
Q 2170 1146 2170 1094 
Q 2170 1082 2112 890 
Q 1958 333 1658 -205 
z
M 5664 326 
Q 5357 736 4986 1069 
Q 4902 1146 4902 1194 
Q 4902 1242 4966 1315 
Q 5030 1389 5084 1389 
Q 5139 1389 5331 1235 
Q 5523 1082 5779 813 
Q 6099 499 6099 419 
Q 6099 339 6003 252 
Q 5907 166 5846 166 
Q 5786 166 5664 326 
z
M 3789 570 
Q 3520 928 3245 1165 
Q 3162 1248 3162 1296 
Q 3162 1344 3232 1411 
Q 3302 1478 3347 1478 
Q 3462 1478 3923 1018 
Q 4186 768 4186 694 
Q 4186 621 4099 531 
Q 4013 442 3949 442 
Q 3885 442 3789 570 
z
M 5261 -205 
Q 5261 -486 4384 -486 
Q 3507 -486 3053 -211 
Q 2682 32 2502 550 
Q 2426 755 2413 899 
Q 2400 1043 2368 1030 
Q 2368 1139 2534 1171 
Q 2592 1178 2605 1178 
Q 2720 1178 2752 1030 
Q 2842 640 2998 393 
Q 3155 147 3456 25 
Q 3757 -96 4208 -96 
Q 4659 -96 4717 -64 
Q 4480 589 4480 720 
Q 4480 851 4566 851 
Q 4653 851 4730 698 
Q 4877 403 5043 169 
Q 5210 -64 5235 -112 
Q 5261 -160 5261 -205 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7ec4" d="M 2675 -333 
L 2618 -333 
Q 2438 -333 2355 -262 
Q 2272 -192 2214 19 
Q 2208 32 2208 51 
Q 2208 77 2246 77 
Q 2266 77 2285 70 
Q 2374 45 2460 38 
Q 2547 32 2611 32 
L 3181 51 
L 3149 4090 
Q 3149 4205 3139 4278 
Q 3130 4352 3098 4416 
Q 3053 4493 3053 4525 
Q 3053 4563 3117 4563 
Q 3168 4563 3270 4537 
Q 3373 4512 3546 4442 
L 5024 4538 
Q 5050 4538 5069 4541 
Q 5088 4544 5107 4544 
Q 5229 4544 5312 4454 
Q 5395 4365 5395 4314 
Q 5395 4269 5376 4240 
Q 5357 4211 5357 4179 
L 5331 122 
L 5626 134 
Q 5702 141 5756 150 
Q 5811 160 5862 173 
Q 5888 179 5910 182 
Q 5933 186 5952 186 
Q 6003 186 6076 131 
Q 6150 77 6204 3 
Q 6259 -70 6259 -128 
Q 6259 -205 6086 -218 
L 2675 -333 
z
M 4954 4179 
L 3546 4096 
L 3552 3059 
L 4954 3136 
L 4954 4179 
z
M 4947 2784 
L 3552 2707 
L 3558 1670 
L 4947 1747 
L 4947 2784 
z
M 4947 1389 
L 3565 1325 
L 3571 64 
L 4941 109 
L 4947 1389 
z
M 1453 563 
Q 2016 781 2349 925 
Q 2682 1069 2739 1075 
Q 2797 1082 2803 1062 
Q 2816 922 1840 378 
Q 864 -166 691 -179 
Q 646 -186 560 -109 
Q 474 -32 410 64 
Q 346 160 339 211 
Q 339 237 403 243 
L 474 250 
Q 576 256 768 314 
L 1453 563 
z
M 1830 1830 
Q 2170 1882 2339 1910 
Q 2509 1939 2601 1932 
Q 2694 1926 2688 1875 
Q 2682 1786 2240 1622 
Q 1798 1459 1366 1350 
Q 934 1242 864 1248 
Q 794 1254 749 1293 
Q 627 1440 595 1545 
Q 563 1651 563 1664 
Q 570 1722 634 1715 
L 730 1709 
Q 870 1696 998 1702 
Q 1286 2118 1523 2528 
Q 1030 2874 544 3085 
Q 435 3136 422 3187 
Q 410 3238 454 3337 
Q 499 3437 560 3449 
Q 621 3462 742 3418 
Q 768 3405 787 3398 
Q 1331 4186 1446 4480 
Q 1562 4774 1562 4915 
L 1562 4979 
Q 1562 5050 1613 5050 
Q 1645 5050 1734 4998 
Q 1978 4851 1978 4717 
Q 1939 4557 1728 4186 
Q 1536 3840 1107 3226 
L 1261 3142 
Q 1549 2982 1728 2861 
L 1754 2899 
Q 2227 3680 2272 3878 
Q 2285 3936 2291 4032 
Q 2298 4128 2333 4128 
Q 2368 4128 2451 4077 
Q 2688 3936 2694 3795 
Q 2694 3750 2675 3706 
Q 2086 2675 1459 1766 
Q 1632 1798 1830 1830 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-53d8" d="M 2842 710 
Q 1952 1357 1952 1504 
Q 1952 1568 2035 1635 
Q 2118 1702 2172 1702 
Q 2227 1702 2304 1626 
Q 2714 1254 3174 947 
Q 3706 1382 4077 1869 
L 2010 1754 
Q 1965 1747 1926 1747 
L 1850 1747 
Q 1696 1747 1629 1824 
Q 1562 1901 1523 1993 
Q 1485 2086 1485 2115 
Q 1485 2144 1510 2144 
Q 1536 2144 1609 2128 
Q 1683 2112 1843 2112 
L 1939 2112 
L 4275 2234 
L 4371 2240 
Q 4461 2240 4573 2157 
Q 4685 2074 4685 2000 
Q 4685 1926 4627 1888 
Q 4570 1850 4544 1818 
Q 4070 1190 3507 736 
Q 4576 115 5830 -128 
Q 5965 -154 5965 -198 
L 5914 -282 
Q 5747 -531 5600 -531 
L 5382 -467 
Q 4762 -301 4173 -38 
Q 3584 224 3168 486 
Q 2368 -64 1402 -346 
Q 1024 -454 800 -492 
Q 576 -531 570 -531 
Q 454 -531 454 -486 
Q 454 -416 678 -333 
Q 1280 -109 1821 134 
Q 2362 378 2842 710 
z
M 5376 2586 
Q 4941 3002 4371 3392 
Q 4288 3450 4288 3491 
Q 4288 3533 4349 3625 
Q 4410 3718 4464 3718 
Q 4518 3718 4768 3564 
Q 5018 3411 5414 3094 
Q 5811 2778 5811 2688 
Q 5811 2630 5731 2534 
Q 5651 2438 5590 2438 
Q 5530 2438 5376 2586 
z
M 2003 3552 
Q 2074 3469 2074 3424 
Q 2074 3322 1626 2922 
Q 1178 2522 794 2330 
Q 678 2272 627 2272 
Q 576 2272 576 2304 
Q 576 2355 698 2458 
Q 1587 3245 1638 3648 
Q 1645 3750 1709 3750 
Q 1811 3750 2003 3552 
z
M 2931 5171 
Q 3398 5171 3398 5018 
L 3398 4390 
L 5094 4493 
Q 5306 4512 5370 4531 
Q 5434 4550 5485 4550 
Q 5536 4550 5626 4499 
Q 5837 4371 5837 4268 
Q 5837 4166 5728 4154 
L 4000 4058 
L 3930 4051 
L 3930 2573 
Q 3930 2381 3808 2381 
Q 3750 2381 3651 2441 
Q 3552 2502 3504 2550 
Q 3456 2598 3456 2669 
Q 3507 3021 3507 3238 
L 3507 4026 
L 2938 3994 
L 2803 3987 
L 2803 2547 
Q 2803 2355 2682 2355 
Q 2624 2355 2525 2416 
Q 2426 2477 2378 2525 
Q 2330 2573 2330 2643 
Q 2381 2893 2381 3213 
L 2381 3968 
L 1216 3898 
Q 1088 3885 960 3885 
Q 832 3885 761 3955 
Q 691 4026 646 4118 
Q 602 4211 602 4246 
Q 602 4282 646 4282 
Q 832 4250 986 4250 
L 1069 4250 
L 2976 4365 
L 2970 4800 
Q 2970 4960 2918 5027 
Q 2867 5094 2867 5132 
Q 2867 5171 2931 5171 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-5316" d="M 1466 2912 
Q 1146 2496 870 2240 
Q 378 1773 262 1773 
Q 224 1773 224 1817 
Q 224 1862 377 2044 
Q 531 2227 886 2707 
Q 1242 3187 1670 3907 
Q 2099 4627 2099 4851 
L 2099 4902 
Q 2099 4922 2093 4947 
L 2093 4966 
Q 2093 5030 2160 5030 
Q 2227 5030 2330 4979 
Q 2579 4838 2579 4736 
Q 2579 4659 2323 4198 
Q 2067 3738 1882 3450 
L 1875 -390 
Q 1875 -525 1747 -525 
Q 1734 -525 1644 -502 
Q 1555 -480 1472 -416 
Q 1389 -352 1389 -278 
Q 1389 -205 1411 -112 
Q 1434 -19 1434 211 
L 1466 2912 
z
M 3309 1811 
Q 2470 1344 2272 1344 
Q 2208 1344 2208 1382 
Q 2208 1446 2419 1574 
Q 2963 1901 3315 2163 
L 3328 4595 
Q 3328 4749 3277 4832 
Q 3226 4915 3226 4944 
Q 3226 4973 3302 4973 
Q 3379 4973 3533 4928 
Q 3763 4877 3763 4742 
L 3750 2496 
Q 4410 3040 4915 3648 
Q 4992 3725 5011 3865 
Q 5030 4006 5068 4006 
Q 5107 4006 5184 3936 
Q 5414 3738 5414 3578 
Q 5414 3533 5389 3507 
Q 4640 2650 3744 2080 
L 3738 544 
Q 3738 326 3795 256 
Q 3853 186 4019 154 
Q 4346 102 4624 102 
Q 4902 102 5350 166 
Q 5466 186 5542 282 
Q 5683 461 5805 1306 
Q 5824 1453 5862 1523 
Q 5901 1594 5933 1594 
Q 6054 1594 6054 960 
Q 6054 634 6029 371 
Q 5971 -179 5491 -230 
Q 4998 -288 4537 -288 
Q 4077 -288 3789 -221 
Q 3501 -154 3401 6 
Q 3302 166 3302 454 
L 3309 1811 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-4e3b" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-89c2" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-75bc" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-75db" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-53d8" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-5316" x="999.999847"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_13">
     <path d="M 471.465625 54.665 
L 529.065625 54.665 
Q 530.665625 54.665 530.665625 53.065 
L 530.665625 30.68 
Q 530.665625 29.08 529.065625 29.08 
L 471.465625 29.08 
Q 469.865625 29.08 469.865625 30.68 
L 469.865625 53.065 
Q 469.865625 54.665 471.465625 54.665 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_14">
     <path d="M 473.065625 38.28875 
L 489.065625 38.28875 
L 489.065625 32.68875 
L 473.065625 32.68875 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_12">
     <!-- 高焦虑组 -->
     <g transform="translate(495.465625 38.28875) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
    <g id="patch_15">
     <path d="M 473.065625 50.08875 
L 489.065625 50.08875 
L 489.065625 44.48875 
L 473.065625 44.48875 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_13">
     <!-- 低焦虑组 -->
     <g transform="translate(495.465625 50.08875) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_16">
    <path d="M 575.185625 330.0965 
L 1069.2 330.0965 
L 1069.2 25.08 
L 575.185625 25.08 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_4">
     <g id="line2d_21">
      <path d="M 597.640824 330.0965 
L 597.640824 25.08 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m045fcfd628" x="597.640824" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_14">
      <!-- 第一次 -->
      <g transform="translate(585.640824 343.11275) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e00" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_23">
      <path d="M 816.715935 330.0965 
L 816.715935 25.08 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m045fcfd628" x="816.715935" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 第二次 -->
      <g transform="translate(804.715935 343.11275) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_25">
      <path d="M 1035.791046 330.0965 
L 1035.791046 25.08 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m045fcfd628" x="1035.791046" y="330.0965" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 第三次 -->
      <g transform="translate(1023.791046 343.11275) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e09" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="text_17">
     <!-- 刺激次数 -->
     <g transform="translate(802.192813 355.959312) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-523a"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_6">
     <g id="line2d_27">
      <path d="M 575.185625 291.495665 
L 1069.2 291.495665 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m0a116f9c05" x="575.185625" y="291.495665" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 10 -->
      <g transform="translate(558.585625 294.32379) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_29">
      <path d="M 575.185625 233.591721 
L 1069.2 233.591721 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m0a116f9c05" x="575.185625" y="233.591721" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 20 -->
      <g transform="translate(558.585625 236.419846) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_31">
      <path d="M 575.185625 175.687776 
L 1069.2 175.687776 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m0a116f9c05" x="575.185625" y="175.687776" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 30 -->
      <g transform="translate(558.585625 178.515901) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_33">
      <path d="M 575.185625 117.783832 
L 1069.2 117.783832 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m0a116f9c05" x="575.185625" y="117.783832" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 40 -->
      <g transform="translate(558.585625 120.611957) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_35">
      <path d="M 575.185625 59.879887 
L 1069.2 59.879887 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m0a116f9c05" x="575.185625" y="59.879887" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- 50 -->
      <g transform="translate(558.585625 62.708012) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_23">
     <!-- 分数 -->
     <g transform="translate(552.865313 187.58825) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-5206"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="99.999985"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_3">
    <path d="M 597.640824 125.575716 
L 597.640824 93.642599 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 816.715935 119.670019 
L 816.715935 94.779735 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1035.791046 142.641498 
L 1035.791046 105.188177 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_37">
    <defs>
     <path id="m97d1bebfcb" d="M 5 0 
L -5 -0 
" style="stroke: #ff0000; stroke-opacity: 0.7"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m97d1bebfcb" x="597.640824" y="125.575716" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="816.715935" y="119.670019" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="1035.791046" y="142.641498" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_38">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m97d1bebfcb" x="597.640824" y="93.642599" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="816.715935" y="94.779735" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="1035.791046" y="105.188177" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="LineCollection_4">
    <path d="M 608.594579 137.599201 
L 608.594579 101.056673 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 827.66969 129.86923 
L 827.66969 82.536856 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1046.744801 131.099607 
L 1046.744801 93.65932 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_39">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m97d1bebfcb" x="608.594579" y="137.599201" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="827.66969" y="129.86923" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="1046.744801" y="131.099607" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_40">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m97d1bebfcb" x="608.594579" y="101.056673" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="827.66969" y="82.536856" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#m97d1bebfcb" x="1046.744801" y="93.65932" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="LineCollection_5">
    <path d="M 597.640824 80.967265 
L 597.640824 55.823081 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 816.715935 99.637057 
L 816.715935 70.53321 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1035.791046 112.050113 
L 1035.791046 77.194394 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_41">
    <defs>
     <path id="mf80e335473" d="M 5 0 
L -5 -0 
" style="stroke: #0000ff; stroke-opacity: 0.7"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mf80e335473" x="597.640824" y="80.967265" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="816.715935" y="99.637057" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="1035.791046" y="112.050113" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_42">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mf80e335473" x="597.640824" y="55.823081" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="816.715935" y="70.53321" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="1035.791046" y="77.194394" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="LineCollection_6">
    <path d="M 608.594579 98.863835 
L 608.594579 67.219095 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 827.66969 109.278036 
L 827.66969 72.245946 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1046.744801 90.080019 
L 1046.744801 38.944386 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_43">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mf80e335473" x="608.594579" y="98.863835" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="827.66969" y="109.278036" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="1046.744801" y="90.080019" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_44">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mf80e335473" x="608.594579" y="67.219095" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="827.66969" y="72.245946" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     <use xlink:href="#mf80e335473" x="1046.744801" y="38.944386" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="LineCollection_7">
    <path d="M 597.640824 296.323249 
L 597.640824 235.576366 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 816.715935 291.1996 
L 816.715935 233.206564 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1035.791046 279.672653 
L 1035.791046 218.847041 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_45">
    <defs>
     <path id="m0b1c214702" d="M 5 0 
L -5 -0 
" style="stroke: #008000; stroke-opacity: 0.7"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m0b1c214702" x="597.640824" y="296.323249" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="816.715935" y="291.1996" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="1035.791046" y="279.672653" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_46">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m0b1c214702" x="597.640824" y="235.576366" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="816.715935" y="233.206564" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="1035.791046" y="218.847041" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="LineCollection_8">
    <path d="M 608.594579 316.232114 
L 608.594579 249.77406 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 827.66969 313.842118 
L 827.66969 238.267109 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <path d="M 1046.744801 312.582889 
L 1046.744801 242.614548 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_47">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m0b1c214702" x="608.594579" y="316.232114" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="827.66969" y="313.842118" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="1046.744801" y="312.582889" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_48">
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m0b1c214702" x="608.594579" y="249.77406" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="827.66969" y="238.267109" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     <use xlink:href="#m0b1c214702" x="1046.744801" y="242.614548" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_49">
    <path d="M 597.640824 109.609157 
L 816.715935 107.224877 
L 1035.791046 123.914838 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    <defs>
     <path id="mee085d1ce2" d="M 0 3 
C 0.795609 3 1.55874 2.683901 2.12132 2.12132 
C 2.683901 1.55874 3 0.795609 3 0 
C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 
C 1.55874 -2.683901 0.795609 -3 0 -3 
C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 
C -2.683901 -1.55874 -3 -0.795609 -3 0 
C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 
C -1.55874 2.683901 -0.795609 3 0 3 
z
" style="stroke: #ff0000; stroke-opacity: 0.7"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mee085d1ce2" x="597.640824" y="109.609157" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#mee085d1ce2" x="816.715935" y="107.224877" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#mee085d1ce2" x="1035.791046" y="123.914838" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_50">
    <path d="M 608.594579 119.327937 
L 827.66969 106.203043 
L 1046.744801 112.379463 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mee085d1ce2" x="608.594579" y="119.327937" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#mee085d1ce2" x="827.66969" y="106.203043" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     <use xlink:href="#mee085d1ce2" x="1046.744801" y="112.379463" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
    </g>
   </g>
   <g id="line2d_51">
    <path d="M 597.640824 68.395173 
L 816.715935 85.085134 
L 1035.791046 94.622254 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    <defs>
     <path id="m8e0084e2c6" d="M -3 3 
L 3 3 
L 3 -3 
L -3 -3 
z
" style="stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m8e0084e2c6" x="597.640824" y="68.395173" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#m8e0084e2c6" x="816.715935" y="85.085134" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#m8e0084e2c6" x="1035.791046" y="94.622254" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </g>
   </g>
   <g id="line2d_52">
    <path d="M 608.594579 83.041465 
L 827.66969 90.761991 
L 1046.744801 64.512203 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#m8e0084e2c6" x="608.594579" y="83.041465" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#m8e0084e2c6" x="827.66969" y="90.761991" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#m8e0084e2c6" x="1046.744801" y="64.512203" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </g>
   </g>
   <g id="line2d_53">
    <path d="M 597.640824 265.949807 
L 816.715935 262.203082 
L 1035.791046 249.259847 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    <defs>
     <path id="mdfad84c5a8" d="M 0 -3 
L -3 3 
L 3 3 
z
" style="stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </defs>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mdfad84c5a8" x="597.640824" y="265.949807" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#mdfad84c5a8" x="816.715935" y="262.203082" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#mdfad84c5a8" x="1035.791046" y="249.259847" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </g>
   </g>
   <g id="line2d_54">
    <path d="M 608.594579 283.003087 
L 827.66969 276.054613 
L 1046.744801 277.598719 
" clip-path="url(#pbdf6992f49)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    <g clip-path="url(#pbdf6992f49)">
     <use xlink:href="#mdfad84c5a8" x="608.594579" y="283.003087" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#mdfad84c5a8" x="827.66969" y="276.054613" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     <use xlink:href="#mdfad84c5a8" x="1046.744801" y="277.598719" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
    </g>
   </g>
   <g id="patch_17">
    <path d="M 575.185625 330.0965 
L 575.185625 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_18">
    <path d="M 1069.2 330.0965 
L 1069.2 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_19">
    <path d="M 575.185625 330.0965 
L 1069.2 330.0965 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_20">
    <path d="M 575.185625 25.08 
L 1069.2 25.08 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_24">
    <!-- 高低焦虑组主观指标变化趋势 -->
    <g transform="translate(757.192813 19.08) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-6307" d="M 5862 3603 
L 5856 3405 
Q 5850 3130 5728 2992 
Q 5606 2854 5306 2829 
Q 4736 2771 4243 2771 
Q 3750 2771 3510 2800 
Q 3270 2829 3142 2979 
Q 3014 3130 3014 3488 
L 3014 3533 
L 3040 4685 
Q 3040 4870 2992 4950 
Q 2944 5030 2944 5050 
Q 2944 5101 3008 5101 
Q 3072 5101 3174 5069 
Q 3437 4992 3437 4858 
L 3411 3885 
Q 4205 4115 4742 4467 
Q 4883 4550 4915 4704 
Q 4934 4774 4992 4774 
Q 5050 4774 5107 4685 
Q 5254 4486 5254 4377 
Q 5254 4269 5184 4237 
Q 4192 3776 3405 3584 
L 3405 3546 
Q 3405 3347 3465 3260 
Q 3526 3174 3718 3155 
Q 3910 3136 4211 3136 
Q 5210 3136 5376 3258 
Q 5459 3315 5510 3475 
Q 5562 3635 5613 3926 
Q 5664 4218 5734 4218 
Q 5862 4218 5862 3603 
z
M 1850 102 
L 1862 -160 
Q 1862 -326 1750 -425 
Q 1638 -525 1536 -525 
Q 1344 -525 976 -147 
Q 608 230 608 365 
Q 608 397 649 397 
Q 691 397 886 262 
Q 1082 128 1453 -19 
L 1459 1632 
Q 1056 1402 832 1309 
Q 608 1216 569 1216 
Q 531 1216 493 1235 
Q 365 1318 285 1411 
Q 205 1504 205 1549 
Q 205 1594 285 1600 
Q 365 1606 502 1644 
Q 640 1683 1466 2016 
L 1472 3245 
L 909 3206 
Q 832 3200 768 3193 
Q 704 3187 633 3187 
Q 563 3187 486 3264 
Q 410 3341 378 3427 
Q 346 3514 346 3539 
Q 346 3565 390 3565 
L 410 3565 
Q 506 3546 576 3546 
L 685 3546 
Q 730 3546 781 3552 
L 1472 3597 
L 1478 4627 
Q 1478 4813 1370 4973 
Q 1350 5005 1350 5024 
Q 1350 5069 1411 5069 
Q 1472 5069 1581 5043 
Q 1882 4973 1882 4800 
L 1875 3622 
L 2163 3642 
Q 2253 3648 2342 3686 
Q 2432 3725 2470 3725 
Q 2509 3725 2592 3674 
Q 2810 3539 2810 3424 
Q 2810 3354 2650 3334 
L 1869 3277 
L 1862 2195 
Q 2208 2349 2438 2467 
Q 2669 2586 2720 2586 
Q 2771 2586 2771 2544 
Q 2771 2502 2684 2400 
Q 2598 2298 1862 1856 
L 1850 102 
z
M 5389 70 
Q 5562 -90 5562 -189 
Q 5562 -288 5421 -288 
L 3558 -314 
L 3565 -467 
L 3565 -499 
Q 3565 -634 3469 -634 
Q 3328 -634 3229 -547 
Q 3130 -461 3130 -371 
L 3136 -288 
Q 3162 -45 3162 205 
L 3162 326 
L 3110 1728 
Q 3098 2042 3066 2118 
Q 3034 2195 3021 2224 
Q 3008 2253 3008 2278 
Q 3008 2330 3097 2330 
Q 3187 2330 3507 2170 
L 5210 2266 
Q 5222 2272 5242 2272 
L 5274 2272 
Q 5376 2272 5459 2221 
Q 5594 2118 5594 2051 
Q 5594 1984 5578 1952 
Q 5562 1920 5555 1888 
L 5389 70 
z
M 5152 1920 
L 3501 1843 
L 3520 1133 
L 5101 1197 
L 5152 1920 
z
M 5082 877 
L 3526 806 
L 3552 26 
L 5024 64 
L 5082 877 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6807" d="M 3226 1997 
Q 3219 2112 3276 2125 
Q 3334 2138 3424 2086 
Q 3661 1965 3674 1805 
Q 3674 1766 3654 1728 
Q 3334 1107 3040 739 
Q 2746 371 2544 201 
Q 2342 32 2291 19 
Q 2240 6 2240 64 
Q 2240 134 2342 282 
Q 2643 704 2934 1184 
Q 3226 1664 3226 1958 
L 3226 1997 
z
M 5779 250 
Q 5709 269 5664 384 
Q 5293 1107 4762 1856 
Q 4672 1965 4864 2112 
Q 4915 2150 4992 2121 
Q 5069 2093 5433 1545 
Q 5798 998 6010 634 
Q 6125 422 5952 288 
Q 5862 218 5779 250 
z
M 1792 -454 
Q 1792 -608 1670 -608 
Q 1664 -608 1587 -589 
Q 1376 -518 1376 -352 
Q 1376 -320 1405 -205 
Q 1434 -90 1434 115 
L 1440 294 
Q 1440 474 1443 755 
Q 1446 1037 1449 1341 
Q 1453 1645 1462 1910 
Q 1472 2176 1472 2310 
L 1478 2400 
Q 1293 1754 691 998 
Q 378 602 282 602 
Q 243 602 243 669 
Q 243 736 314 832 
Q 915 1683 1376 3040 
L 902 3002 
Q 800 2989 717 2989 
Q 634 2989 582 3021 
Q 454 3130 397 3277 
Q 390 3290 390 3315 
Q 390 3360 442 3360 
L 474 3360 
Q 589 3341 691 3341 
L 768 3341 
L 1472 3392 
L 1485 4678 
Q 1485 4794 1430 4877 
Q 1376 4960 1376 4992 
Q 1376 5043 1449 5043 
Q 1523 5043 1657 4995 
Q 1792 4947 1824 4905 
Q 1856 4864 1856 4794 
L 1837 3424 
L 2106 3443 
Q 2266 3462 2307 3478 
Q 2349 3494 2390 3494 
Q 2432 3494 2560 3404 
Q 2688 3315 2688 3232 
Q 2688 3149 2554 3136 
L 1837 3078 
L 1830 2522 
Q 1882 2566 1936 2566 
Q 1990 2566 2112 2445 
Q 2406 2144 2586 1862 
Q 2637 1805 2637 1734 
Q 2637 1664 2534 1606 
Q 2432 1549 2374 1549 
Q 2317 1549 2246 1658 
Q 2042 2010 1824 2253 
L 1792 -454 
z
M 4486 -109 
L 4493 -301 
Q 4493 -403 4425 -508 
Q 4358 -614 4252 -614 
Q 4147 -614 4035 -550 
Q 3923 -486 3792 -396 
Q 3661 -307 3545 -204 
Q 3430 -102 3353 -19 
Q 3277 64 3277 102 
Q 3277 141 3353 141 
Q 3430 141 3651 35 
Q 3872 -70 4090 -134 
L 4077 1830 
L 4070 2176 
L 4070 2509 
L 2931 2458 
L 2829 2458 
Q 2624 2458 2573 2509 
Q 2419 2662 2419 2768 
Q 2419 2874 2477 2874 
Q 2509 2874 2522 2861 
Q 2682 2829 2778 2829 
L 2822 2829 
L 5440 2944 
Q 5498 2950 5562 2953 
Q 5626 2957 5677 2973 
Q 5728 2989 5808 2989 
Q 5888 2989 6003 2886 
Q 6118 2784 6118 2701 
Q 6118 2598 5946 2586 
L 4467 2522 
L 4467 1850 
L 4486 -109 
z
M 3059 4333 
L 3238 4314 
Q 3315 4301 3373 4301 
L 3411 4301 
L 4858 4384 
Q 5030 4403 5078 4416 
Q 5126 4429 5161 4429 
Q 5197 4429 5274 4390 
Q 5478 4282 5478 4154 
Q 5478 4058 5331 4045 
L 3526 3949 
L 3341 3942 
Q 3258 3942 3174 3980 
Q 3091 4019 3008 4224 
Q 3002 4250 3002 4275 
Q 3002 4333 3059 4333 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8d8b" d="M 1293 1907 
Q 1248 1555 1126 1222 
Q 1466 979 1613 902 
L 1638 2624 
L 934 2579 
Q 806 2566 691 2566 
Q 576 2566 499 2643 
Q 422 2720 393 2800 
Q 365 2880 365 2886 
Q 365 2925 397 2925 
Q 429 2925 489 2902 
Q 550 2880 704 2880 
L 762 2880 
L 1594 2931 
L 1594 3584 
L 1274 3558 
Q 1158 3546 1062 3546 
Q 966 3546 896 3619 
Q 826 3693 794 3769 
Q 762 3846 762 3859 
Q 762 3898 806 3898 
Q 826 3898 899 3875 
Q 973 3853 1101 3853 
L 1139 3853 
L 1600 3891 
L 1600 4678 
Q 1600 4800 1552 4880 
Q 1504 4960 1504 5001 
Q 1504 5043 1574 5043 
L 1670 5030 
Q 1958 4992 1958 4832 
L 1952 3917 
L 2150 3936 
Q 2272 3942 2342 3971 
Q 2413 4000 2454 4000 
Q 2496 4000 2566 3955 
Q 2758 3853 2758 3750 
Q 2758 3686 2624 3667 
L 1952 3616 
L 1946 2957 
L 2381 2982 
Q 2483 2989 2563 3017 
Q 2643 3046 2675 3046 
Q 2707 3046 2778 3002 
Q 2963 2880 2963 2778 
Q 2963 2707 2829 2694 
L 1997 2643 
L 1984 1926 
L 2432 1939 
Q 2598 1958 2646 1977 
Q 2694 1997 2732 1997 
Q 2771 1997 2883 1907 
Q 2995 1818 2995 1728 
Q 2995 1664 2867 1651 
L 1978 1613 
L 1965 717 
Q 3482 0 6016 -38 
Q 6240 -38 6240 -96 
L 6208 -179 
Q 6099 -442 5933 -442 
L 5888 -442 
Q 4051 -314 2947 -13 
Q 1843 288 1024 896 
Q 710 128 384 -282 
Q 250 -448 202 -448 
Q 154 -448 154 -387 
Q 154 -326 192 -243 
Q 666 749 819 1594 
Q 870 1882 870 1933 
Q 870 1984 848 2051 
Q 826 2118 826 2131 
Q 826 2176 886 2176 
Q 947 2176 1043 2144 
Q 1293 2074 1293 1939 
L 1293 1907 
z
M 3776 1709 
L 3654 1702 
Q 3520 1702 3469 1754 
Q 3315 1894 3315 2022 
Q 3315 2067 3360 2067 
Q 3373 2067 3427 2048 
Q 3482 2029 3629 2029 
L 3693 2029 
L 5363 2093 
L 5421 2854 
L 3661 2771 
Q 3622 2765 3597 2765 
L 3533 2765 
Q 3309 2765 3213 3021 
Q 3200 3059 3200 3091 
Q 3200 3123 3251 3123 
L 3514 3091 
L 4237 3136 
Q 4518 3488 4794 3942 
L 3834 3885 
Q 3437 3379 3162 3149 
Q 3053 3059 3005 3059 
Q 2957 3059 2957 3113 
Q 2957 3168 3021 3251 
Q 3437 3770 3632 4147 
Q 3827 4525 3884 4742 
Q 3942 4960 3942 4986 
L 3942 5062 
Q 3942 5120 3974 5120 
Q 4006 5120 4096 5069 
Q 4339 4934 4339 4816 
Q 4339 4698 4045 4198 
L 4998 4262 
L 5082 4269 
Q 5165 4269 5251 4198 
Q 5338 4128 5338 4077 
Q 5338 4026 5293 3990 
Q 5248 3955 5056 3680 
Q 4864 3405 4646 3162 
L 5562 3200 
L 5613 3206 
Q 5670 3206 5760 3129 
Q 5850 3053 5850 2995 
Q 5850 2938 5837 2909 
Q 5824 2880 5818 2848 
L 5645 998 
Q 5798 819 5798 758 
Q 5798 698 5744 685 
Q 5690 672 5600 666 
L 3654 582 
L 3578 582 
Q 3514 582 3392 620 
Q 3270 659 3206 851 
Q 3187 902 3187 931 
Q 3187 960 3226 960 
L 3501 928 
L 3552 928 
L 5286 1005 
L 5344 1766 
L 3776 1709 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-52bf" d="M 2848 339 
Q 2848 390 2915 390 
Q 2982 390 3328 208 
Q 3674 26 4147 -90 
Q 4173 -90 4186 -58 
Q 4403 397 4467 1184 
L 3123 1107 
Q 2771 378 2010 -70 
Q 1478 -390 947 -506 
L 755 -550 
Q 666 -550 666 -499 
Q 666 -429 800 -378 
Q 1926 115 2387 704 
Q 2573 941 2650 1075 
L 1722 1024 
Q 1632 1011 1510 1011 
Q 1280 1011 1152 1350 
Q 1152 1389 1190 1389 
Q 1229 1389 1302 1373 
Q 1376 1357 1491 1357 
Q 1606 1357 2816 1421 
Q 2906 1632 2906 1702 
Q 2906 1773 2880 1971 
Q 2880 2048 2950 2048 
Q 3104 2048 3229 1955 
Q 3354 1862 3354 1769 
Q 3354 1677 3270 1446 
L 4576 1517 
Q 4730 1517 4829 1462 
Q 4928 1408 4928 1337 
Q 4928 1267 4915 1232 
Q 4902 1197 4877 1005 
Q 4800 333 4595 -256 
Q 4550 -371 4438 -467 
Q 4326 -563 4233 -563 
Q 4141 -563 3897 -454 
Q 3654 -346 3251 -64 
Q 2848 218 2848 339 
z
M 794 2272 
Q 794 2310 851 2310 
Q 909 2310 1097 2246 
Q 1286 2182 1600 2112 
L 1606 3040 
Q 845 2778 771 2778 
Q 698 2778 627 2842 
Q 454 3008 454 3130 
Q 454 3168 544 3168 
Q 845 3168 1606 3373 
L 1606 3949 
Q 954 3910 877 3910 
Q 717 3910 621 4147 
Q 589 4230 589 4256 
Q 589 4282 611 4282 
Q 634 4282 704 4259 
Q 774 4237 864 4237 
Q 954 4237 1606 4288 
L 1613 4787 
Q 1613 4915 1568 4979 
Q 1523 5043 1523 5088 
Q 1523 5133 1606 5133 
Q 1690 5133 1802 5091 
Q 1914 5050 1946 5014 
Q 1978 4979 1978 4902 
L 1978 4314 
Q 2272 4346 2320 4362 
Q 2368 4378 2406 4378 
Q 2445 4378 2518 4333 
Q 2592 4288 2646 4224 
Q 2701 4160 2701 4109 
Q 2701 4026 2566 4019 
L 1971 3974 
L 1971 3469 
Q 2336 3565 2550 3635 
Q 2765 3706 2841 3706 
Q 2918 3706 2918 3654 
Q 2918 3603 2761 3516 
Q 2605 3430 1971 3174 
L 1965 2138 
L 1978 1920 
Q 1978 1805 1898 1728 
Q 1818 1651 1712 1651 
Q 1606 1651 1200 1897 
Q 794 2144 794 2272 
z
M 2291 1728 
Q 2272 1728 2272 1795 
Q 2272 1862 2362 1926 
Q 3014 2400 3430 3149 
L 3046 3424 
Q 2976 3475 2976 3542 
Q 2976 3610 3024 3661 
Q 3072 3712 3107 3712 
Q 3142 3712 3539 3450 
Q 3616 3674 3661 3994 
L 3162 3962 
Q 2938 3962 2810 4160 
Q 2765 4237 2765 4272 
Q 2765 4307 2784 4307 
Q 3078 4282 3162 4282 
L 3680 4320 
Q 3680 4883 3661 4982 
Q 3642 5082 3610 5130 
Q 3578 5178 3578 5213 
Q 3578 5248 3629 5248 
Q 3680 5248 3827 5219 
Q 3974 5190 4009 5152 
Q 4045 5114 4045 4806 
Q 4045 4499 4032 4346 
L 4710 4390 
Q 4858 4390 4947 4345 
Q 5037 4301 5037 4224 
Q 5037 4147 4979 4032 
Q 4851 2374 4838 2310 
Q 4838 2208 4931 2176 
Q 5024 2144 5200 2144 
Q 5376 2144 5481 2192 
Q 5587 2240 5625 2368 
Q 5664 2496 5709 2771 
Q 5747 3117 5805 3117 
Q 5811 3117 5856 2960 
Q 5901 2803 5901 2528 
Q 5901 2253 5862 2099 
Q 5798 1798 5293 1798 
Q 5056 1798 4883 1818 
Q 4467 1862 4467 2253 
Q 4467 2317 4608 4058 
L 4006 4026 
Q 3949 3539 3827 3245 
Q 4333 2899 4333 2784 
Q 4333 2746 4265 2659 
Q 4198 2573 4153 2573 
Q 4109 2573 4035 2637 
Q 3962 2701 3885 2771 
Q 3808 2842 3686 2931 
Q 3360 2381 2889 2054 
Q 2419 1728 2291 1728 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-4e3b" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-89c2" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-6307" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-6807" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-53d8" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-5316" x="999.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-8d8b" x="1099.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-52bf" x="1199.999817"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_21">
     <path d="M 723.392813 417.273975 
L 920.992813 417.273975 
Q 922.592813 417.273975 922.592813 415.673975 
L 922.592813 381.448975 
Q 922.592813 379.848975 920.992813 379.848975 
L 723.392813 379.848975 
Q 721.792813 379.848975 721.792813 381.448975 
L 721.792813 415.673975 
Q 721.792813 417.273975 723.392813 417.273975 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="LineCollection_9">
     <path d="M 732.992813 390.672725 
L 732.992813 382.672725 
" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_55">
     <g>
      <use xlink:href="#m97d1bebfcb" x="732.992813" y="390.672725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_56">
     <g>
      <use xlink:href="#m97d1bebfcb" x="732.992813" y="382.672725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_57">
     <path d="M 724.992813 386.672725 
L 740.992813 386.672725 
" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="line2d_58">
     <g>
      <use xlink:href="#mee085d1ce2" x="732.992813" y="386.672725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="text_25">
     <!-- 高焦虑组-主观成功 -->
     <g transform="translate(747.392813 389.472725) scale(0.08 -0.08)">
      <defs>
       <path id="LXGWWenKai-Regular-2d" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6210" d="M 1530 3379 
Q 1530 3098 1498 2598 
L 2579 2675 
Q 2611 2682 2630 2682 
L 2720 2682 
Q 2790 2682 2864 2611 
Q 2938 2541 2938 2489 
Q 2938 2438 2922 2403 
Q 2906 2368 2896 2240 
Q 2886 2112 2816 1561 
Q 2746 1011 2611 608 
Q 2566 474 2464 381 
Q 2362 288 2262 288 
Q 2163 288 2029 429 
Q 1722 749 1581 957 
Q 1440 1165 1440 1209 
Q 1440 1254 1488 1254 
Q 1536 1254 1731 1104 
Q 1926 954 2195 787 
L 2202 781 
L 2208 781 
Q 2259 781 2364 1283 
Q 2470 1786 2509 2330 
L 1472 2259 
Q 1395 1408 1200 902 
Q 1005 397 816 80 
Q 627 -237 473 -416 
Q 320 -595 265 -595 
Q 211 -595 211 -544 
Q 211 -493 256 -410 
Q 960 794 1069 2234 
Q 1114 2842 1114 3222 
Q 1114 3603 1066 3721 
Q 1018 3840 1018 3878 
Q 1018 3917 1088 3917 
Q 1158 3917 1555 3738 
L 3174 3840 
Q 3066 4378 3027 4669 
Q 2989 4960 2960 5011 
Q 2931 5062 2861 5133 
Q 2829 5178 2829 5190 
Q 2829 5248 2931 5248 
Q 3034 5248 3165 5213 
Q 3296 5178 3331 5133 
Q 3366 5088 3417 4701 
Q 3469 4314 3565 3866 
L 4851 3949 
Q 5050 3968 5114 3990 
Q 5178 4013 5226 4013 
Q 5274 4013 5350 3955 
Q 5555 3821 5555 3712 
Q 5555 3642 5414 3629 
L 3642 3514 
Q 3846 2586 4224 1760 
Q 4224 1766 4349 1942 
Q 4474 2118 4608 2381 
Q 4819 2752 4819 2925 
Q 4819 2976 4813 2995 
L 4813 3014 
Q 4813 3091 4873 3091 
Q 4934 3091 5030 3034 
Q 5280 2893 5280 2790 
Q 5280 2714 4989 2221 
Q 4698 1728 4416 1370 
Q 4928 422 5504 -70 
Q 5523 -90 5542 -90 
Q 5562 -90 5574 -58 
Q 5786 486 5907 1120 
Q 5946 1306 6016 1306 
Q 6086 1306 6086 1133 
L 6086 1101 
Q 6074 346 5946 -294 
Q 5888 -608 5670 -608 
Q 5408 -608 5222 -435 
Q 5037 -262 4877 -64 
Q 4435 512 4128 1056 
Q 3667 531 3104 112 
Q 2541 -307 2400 -307 
Q 2349 -307 2349 -269 
Q 2349 -211 2451 -134 
Q 3251 506 3949 1408 
Q 3885 1523 3670 2035 
Q 3456 2547 3251 3488 
L 1530 3379 
z
M 3814 4698 
Q 3725 4749 3725 4797 
Q 3725 4845 3763 4928 
Q 3802 5011 3882 5011 
Q 3962 5011 4218 4873 
Q 4474 4736 4774 4518 
Q 4883 4442 4883 4371 
Q 4883 4358 4857 4291 
Q 4832 4224 4790 4166 
Q 4749 4109 4694 4109 
Q 4640 4109 4422 4294 
Q 4205 4480 3814 4698 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-529f" d="M 5766 3200 
L 5734 3053 
Q 5683 1478 5434 102 
Q 5408 -58 5373 -198 
Q 5338 -339 5222 -454 
Q 5107 -570 4985 -570 
Q 4864 -570 4768 -493 
Q 4672 -416 4518 -294 
Q 3763 269 3763 435 
Q 3763 486 3827 486 
Q 3891 486 4051 393 
Q 4211 301 4531 137 
Q 4851 -26 4899 -26 
Q 4947 -26 4954 19 
Q 5293 1242 5306 3046 
L 4314 2989 
Q 4109 1696 3482 794 
Q 3014 115 2451 -269 
Q 2054 -550 1977 -550 
Q 1901 -550 1901 -499 
Q 1901 -435 2003 -352 
Q 3539 928 3885 2963 
L 3283 2938 
Q 3168 2925 3078 2925 
Q 2989 2925 2938 2957 
Q 2714 3149 2714 3296 
Q 2714 3334 2765 3334 
L 2803 3334 
Q 2931 3315 3066 3315 
L 3149 3315 
L 3949 3354 
Q 4013 3776 4022 4272 
Q 4032 4768 4025 4835 
Q 4019 4902 4000 4953 
Q 3981 5005 3981 5024 
Q 3981 5094 4061 5094 
Q 4141 5094 4237 5069 
Q 4493 4992 4493 4877 
L 4493 4858 
Q 4467 4486 4444 4105 
Q 4422 3725 4378 3373 
L 5402 3437 
L 5491 3437 
Q 5638 3437 5702 3353 
Q 5766 3270 5766 3200 
z
M 486 3731 
L 800 3693 
L 858 3693 
L 2349 3770 
Q 2502 3789 2556 3814 
Q 2611 3840 2656 3840 
Q 2701 3840 2771 3776 
Q 2842 3712 2890 3635 
Q 2938 3558 2938 3526 
Q 2938 3450 2797 3437 
L 1894 3386 
L 1888 1478 
Q 2304 1638 2598 1779 
Q 2893 1920 2963 1920 
Q 3034 1920 3034 1869 
Q 3034 1792 2803 1651 
Q 2310 1357 1539 976 
Q 768 595 643 595 
Q 518 595 441 688 
Q 365 781 326 873 
Q 288 966 288 985 
Q 288 1005 358 1005 
L 403 1005 
Q 570 1005 870 1110 
Q 1171 1216 1478 1325 
L 1478 3360 
L 928 3328 
L 832 3322 
Q 723 3322 636 3370 
Q 550 3418 461 3635 
Q 448 3674 448 3702 
Q 448 3731 486 3731 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-6210" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-529f" x="734.999878"/>
     </g>
    </g>
    <g id="LineCollection_10">
     <path d="M 732.992813 402.472725 
L 732.992813 394.472725 
" style="fill: none; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_59">
     <g>
      <use xlink:href="#m97d1bebfcb" x="732.992813" y="402.472725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_60">
     <g>
      <use xlink:href="#m97d1bebfcb" x="732.992813" y="394.472725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_61">
     <path d="M 724.992813 398.472725 
L 740.992813 398.472725 
" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_62">
     <g>
      <use xlink:href="#mee085d1ce2" x="732.992813" y="398.472725" style="fill: #ff0000; fill-opacity: 0.7; stroke: #ff0000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="text_26">
     <!-- 低焦虑组-主观成功 -->
     <g transform="translate(747.392813 401.272725) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-6210" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-529f" x="734.999878"/>
     </g>
    </g>
    <g id="LineCollection_11">
     <path d="M 732.992813 413.865225 
L 732.992813 405.865225 
" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_63">
     <g>
      <use xlink:href="#mf80e335473" x="732.992813" y="413.865225" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_64">
     <g>
      <use xlink:href="#mf80e335473" x="732.992813" y="405.865225" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_65">
     <path d="M 724.992813 409.865225 
L 740.992813 409.865225 
" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="line2d_66">
     <g>
      <use xlink:href="#m8e0084e2c6" x="732.992813" y="409.865225" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     </g>
    </g>
    <g id="text_27">
     <!-- 高焦虑组-主观自信 -->
     <g transform="translate(747.392813 412.665225) scale(0.08 -0.08)">
      <defs>
       <path id="LXGWWenKai-Regular-81ea" d="M 4896 339 
Q 5075 115 5075 44 
Q 5075 -26 5024 -38 
Q 4973 -51 4896 -58 
L 1856 -134 
L 1869 -474 
L 1869 -493 
Q 1869 -608 1728 -608 
Q 1587 -608 1484 -525 
Q 1382 -442 1382 -358 
L 1382 -326 
Q 1408 -173 1408 58 
L 1408 154 
L 1306 3584 
Q 1293 3789 1222 3917 
Q 1152 4045 1152 4089 
Q 1152 4134 1238 4134 
Q 1325 4134 1722 3994 
L 2221 4019 
Q 2861 4781 2861 5056 
L 2861 5133 
Q 2861 5152 2854 5171 
L 2854 5190 
Q 2854 5267 2921 5267 
Q 2989 5267 3098 5216 
Q 3386 5082 3386 4934 
Q 3386 4845 3136 4544 
Q 2886 4243 2694 4045 
L 4666 4154 
L 4717 4154 
Q 4915 4154 5020 4070 
Q 5126 3987 5126 3923 
Q 5126 3859 5104 3820 
Q 5082 3782 5075 3750 
L 4896 339 
z
M 4627 3763 
L 1734 3610 
L 1760 2771 
L 4589 2906 
L 4627 3763 
z
M 4576 2534 
L 1773 2400 
L 1805 1562 
L 4538 1683 
L 4576 2534 
z
M 4525 1318 
L 1811 1197 
L 1843 243 
L 4480 314 
L 4525 1318 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-4fe1" d="M 262 2003 
Q 890 2822 1510 4051 
Q 1850 4717 1850 4960 
Q 1850 5011 1830 5088 
Q 1830 5152 1926 5152 
Q 2022 5152 2172 5049 
Q 2323 4947 2323 4896 
Q 2323 4794 2086 4291 
Q 1850 3789 1658 3462 
L 1658 -474 
Q 1658 -602 1549 -602 
Q 1542 -602 1459 -579 
Q 1376 -557 1293 -493 
Q 1210 -429 1210 -355 
Q 1210 -282 1229 -198 
Q 1248 -115 1248 96 
L 1274 2886 
Q 1037 2547 768 2259 
Q 499 1971 387 1878 
Q 275 1786 227 1786 
Q 179 1786 179 1840 
Q 179 1894 262 2003 
z
M 3360 4646 
Q 3238 4691 3238 4774 
Q 3238 4813 3258 4870 
Q 3302 4998 3395 4998 
Q 3488 4998 3920 4844 
Q 4352 4691 4541 4608 
Q 4730 4525 4730 4448 
Q 4730 4371 4682 4268 
Q 4634 4166 4576 4166 
Q 4518 4166 4236 4304 
Q 3955 4442 3360 4646 
z
M 2637 3469 
Q 2534 3456 2435 3456 
Q 2336 3456 2233 3510 
Q 2131 3565 2048 3802 
Q 2042 3814 2042 3843 
Q 2042 3872 2070 3872 
Q 2099 3872 2188 3849 
Q 2278 3827 2413 3827 
L 2464 3827 
L 5421 4006 
Q 5549 4013 5616 4041 
Q 5683 4070 5728 4070 
Q 5773 4070 5856 4006 
Q 6061 3846 6061 3750 
Q 6061 3674 5914 3661 
L 2637 3469 
z
M 3200 2650 
L 2989 2643 
Q 2906 2643 2803 2688 
Q 2701 2733 2611 2970 
Q 2605 2982 2605 3011 
Q 2605 3040 2633 3040 
Q 2662 3040 2752 3017 
Q 2842 2995 2976 2995 
L 3027 2995 
L 4819 3091 
Q 4934 3104 5004 3126 
Q 5075 3149 5120 3149 
Q 5165 3149 5248 3098 
Q 5446 2957 5446 2848 
Q 5446 2771 5312 2758 
L 3200 2650 
z
M 3200 1843 
L 2989 1837 
Q 2906 1837 2803 1881 
Q 2701 1926 2611 2163 
Q 2605 2176 2605 2205 
Q 2605 2234 2633 2234 
Q 2662 2234 2752 2211 
Q 2842 2189 2976 2189 
L 3027 2189 
L 4819 2285 
Q 4934 2298 5004 2320 
Q 5075 2342 5116 2342 
Q 5158 2342 5235 2291 
Q 5453 2157 5453 2042 
Q 5453 1971 5312 1952 
L 3200 1843 
z
M 5216 128 
Q 5402 -58 5402 -131 
Q 5402 -205 5341 -214 
Q 5280 -224 5197 -230 
L 3290 -282 
L 3302 -410 
L 3302 -429 
Q 3302 -563 3171 -563 
Q 3040 -563 2941 -480 
Q 2842 -397 2842 -314 
L 2842 -282 
Q 2861 -51 2861 -13 
L 2861 38 
Q 2861 64 2854 90 
L 2765 986 
Q 2746 1165 2694 1267 
Q 2643 1370 2643 1382 
Q 2643 1440 2755 1440 
Q 2867 1440 3155 1325 
L 5075 1402 
Q 5325 1402 5395 1242 
Q 5421 1184 5421 1158 
Q 5421 1133 5405 1104 
Q 5389 1075 5382 1043 
L 5216 128 
z
M 4928 1030 
L 3181 966 
L 3258 96 
L 4806 128 
L 4928 1030 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-81ea" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-4fe1" x="734.999878"/>
     </g>
    </g>
    <g id="LineCollection_12">
     <path d="M 838.192813 390.672725 
L 838.192813 382.672725 
" style="fill: none; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_67">
     <g>
      <use xlink:href="#mf80e335473" x="838.192813" y="390.672725" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_68">
     <g>
      <use xlink:href="#mf80e335473" x="838.192813" y="382.672725" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_69">
     <path d="M 830.192813 386.672725 
L 846.192813 386.672725 
" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #0000ff; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_70">
     <g>
      <use xlink:href="#m8e0084e2c6" x="838.192813" y="386.672725" style="fill: #0000ff; fill-opacity: 0.7; stroke: #0000ff; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     </g>
    </g>
    <g id="text_28">
     <!-- 低焦虑组-主观自信 -->
     <g transform="translate(852.592813 389.472725) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-81ea" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-4fe1" x="734.999878"/>
     </g>
    </g>
    <g id="LineCollection_13">
     <path d="M 838.192813 402.097725 
L 838.192813 394.097725 
" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_71">
     <g>
      <use xlink:href="#m0b1c214702" x="838.192813" y="402.097725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_72">
     <g>
      <use xlink:href="#m0b1c214702" x="838.192813" y="394.097725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 830.192813 398.097725 
L 846.192813 398.097725 
" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5; stroke-linecap: square"/>
    </g>
    <g id="line2d_74">
     <g>
      <use xlink:href="#mdfad84c5a8" x="838.192813" y="398.097725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     </g>
    </g>
    <g id="text_29">
     <!-- 高焦虑组-主观疼痛 -->
     <g transform="translate(852.592813 400.897725) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-75bc" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-75db" x="734.999878"/>
     </g>
    </g>
    <g id="LineCollection_14">
     <path d="M 838.192813 413.897725 
L 838.192813 405.897725 
" style="fill: none; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_75">
     <g>
      <use xlink:href="#m0b1c214702" x="838.192813" y="413.897725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_76">
     <g>
      <use xlink:href="#m0b1c214702" x="838.192813" y="405.897725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7"/>
     </g>
    </g>
    <g id="line2d_77">
     <path d="M 830.192813 409.897725 
L 846.192813 409.897725 
" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="line2d_78">
     <g>
      <use xlink:href="#mdfad84c5a8" x="838.192813" y="409.897725" style="fill: #008000; fill-opacity: 0.7; stroke: #008000; stroke-opacity: 0.7; stroke-linejoin: miter"/>
     </g>
    </g>
    <g id="text_30">
     <!-- 低焦虑组-主观疼痛 -->
     <g transform="translate(852.592813 412.697725) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-2d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-4e3b" x="434.999924"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="534.999908"/>
      <use xlink:href="#LXGWWenKai-Regular-75bc" x="634.999893"/>
      <use xlink:href="#LXGWWenKai-Regular-75db" x="734.999878"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p06713e1258">
   <rect x="40.65125" y="25.08" width="494.014375" height="305.0165"/>
  </clipPath>
  <clipPath id="pbdf6992f49">
   <rect x="575.185625" y="25.08" width="494.014375" height="305.0165"/>
  </clipPath>
 </defs>
</svg>

from base import BaseDataLoader
import os
import numpy as np
import h5py
import mne
import logging
import torch
from sklearn import preprocessing
from torch.utils.data import Dataset
from collections import defaultdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)

# 脑区到通道的映射
BRAIN_REGION_MAPPING = {
    'frontal': ['Fp1', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8', 'AF3', 'AF4', 'AF7', 'AF8', 'Fpz', 'F1', 'F2', 'F5', 'F6'],
    'central': ['FC5', 'FC1', 'FC2', 'FC6', 'C3', 'Cz', 'C4', 'CP5', 'CP1', 'CP2', 'CP6', 'FCz', 'C1', 'C2', 'C5', 'C6'],
    'temporal': ['FT7', 'FT8', 'T7', 'T8', 'TP7', 'TP8', 'FT9', 'FT10', 'T9', 'T10', 'TP9', 'TP10'],
    'parietal': ['P7', 'P3', 'Pz', 'P4', 'P8', 'POz', 'P1', 'P2', 'P5', 'P6', 'P9', 'P10'],
    'occipital': ['PO7', 'PO3', 'O1', 'Oz', 'O2', 'PO4', 'PO8', 'PO9', 'PO10', 'O9', 'O10'],
    'midline': ['Fpz', 'Fz', 'FCz', 'Cz', 'CPz', 'Pz', 'POz', 'Oz'],
    'left': ['Fp1', 'AF7', 'AF3', 'F7', 'F5', 'F3', 'F1', 'FT9', 'FT7', 'FC5', 'FC3', 'FC1', 'T9', 'T7', 'C5', 'C3', 'C1', 'TP9', 'TP7', 'CP5', 'CP3', 'CP1', 'P9', 'P7', 'P5', 'P3', 'P1', 'PO9', 'PO7', 'PO3', 'O9', 'O1'],
    'right': ['Fp2', 'AF8', 'AF4', 'F8', 'F6', 'F4', 'F2', 'FT10', 'FT8', 'FC6', 'FC4', 'FC2', 'T10', 'T8', 'C6', 'C4', 'C2', 'TP10', 'TP8', 'CP6', 'CP4', 'CP2', 'P10', 'P8', 'P6', 'P4', 'P2', 'PO10', 'PO8', 'PO4', 'O10', 'O2'],
    'all': []
}

# 心电通道组
ECG_CHANNEL_GROUPS = {
    'standard': ['ECG10-ECG20'],
    'extended': ['ECG10-ECG20', 'ECG30-ECG40', 'ECG50-ECG60'],
    'all': []
}

class FIFDataset(Dataset):
    """
    FIF格式脑电-心电数据集

    支持从FIF文件或预处理的H5文件加载数据
    支持按脑区分组加载数据
    """
    def __init__(self, data_path, time_step, output_window, feature_dim=1, output_dim=1,
                 brain_region=None, ecg_group=None, selected_eeg_channels=None, ecg_channel=None,
                 mode='train', train_ratio=0.7, val_ratio=0.2, transform=None):
        """
        初始化数据集

        参数:
            data_path (str): 数据文件路径，可以是FIF文件、H5文件或包含多个文件的目录
            time_step (int): 输入时间窗口长度
            output_window (int): 输出时间窗口长度
            feature_dim (int): 特征维度
            output_dim (int): 输出维度
            brain_region (str): 脑区名称，用于选择特定脑区的通道，可选值：'frontal', 'central', 'temporal', 'parietal', 'occipital', 'midline', 'left', 'right', 'all'
            ecg_group (str): 心电通道组，可选值：'standard', 'extended', 'all'
            selected_eeg_channels (list): 选择的脑电通道，如果提供则覆盖brain_region参数
            ecg_channel (list): 心电通道，如果提供则覆盖ecg_group参数
            mode (str): 'train', 'val', 或 'test'
            train_ratio (float): 训练集比例
            val_ratio (float): 验证集比例
            transform (callable, optional): 数据转换函数
        """
        self.data_path = data_path
        self.time_step = time_step
        self.output_window = output_window
        self.feature_dim = feature_dim
        self.output_dim = output_dim
        self.mode = mode
        self.transform = transform
        self.brain_region = brain_region
        self.ecg_group = ecg_group

        # 根据脑区和心电组选择通道
        if selected_eeg_channels is not None:
            # 如果直接提供了通道列表，则使用它
            self.selected_eeg_channels = selected_eeg_channels
        elif brain_region is not None and brain_region in BRAIN_REGION_MAPPING:
            # 否则根据脑区选择通道
            self.selected_eeg_channels = BRAIN_REGION_MAPPING[brain_region]
        else:
            # 默认使用中线和左右半球的主要通道
            self.selected_eeg_channels = [
                'Fz', 'Cz', 'Pz', 'CPz', 'POz',  # 中线
                'F3', 'C3', 'P3', 'O1', 'F7', 'T7', 'P7', 'Fp1',  # 左半球
                'F4', 'C4', 'P4', 'O2', 'F8', 'T8', 'P8', 'Fp2'   # 右半球
            ]

        # 选择心电通道
        if ecg_channel is not None:
            # 如果直接提供了通道列表，则使用它
            self.ecg_channel = ecg_channel
        elif ecg_group is not None and ecg_group in ECG_CHANNEL_GROUPS:
            # 否则根据心电组选择通道
            self.ecg_channel = ECG_CHANNEL_GROUPS[ecg_group]
        else:
            # 默认使用标准心电通道
            self.ecg_channel = ['ECG10-ECG20']

        # 加载数据
        self.X, self.y, self.series_num = self._load_data()

        # 数据分割
        total_samples = len(self.X)
        train_size = int(total_samples * train_ratio)
        val_size = int(total_samples * val_ratio)

        if mode == 'train':
            self.X = self.X[:train_size]
            self.y = self.y[:train_size]
        elif mode == 'val':
            self.X = self.X[train_size:train_size+val_size]
            self.y = self.y[train_size:train_size+val_size]
        elif mode == 'test':
            self.X = self.X[train_size+val_size:]
            self.y = self.y[train_size+val_size:]

    def _load_data(self):
        """加载数据"""
        if os.path.isdir(self.data_path):
            # 目录中包含多个文件
            return self._load_from_directory()
        elif self.data_path.endswith('.h5'):
            # H5文件
            return self._load_from_h5()
        elif self.data_path.endswith('.fif'):
            # FIF文件
            return self._load_from_fif()
        else:
            raise ValueError(f"不支持的数据格式: {self.data_path}")

    def _load_from_directory(self):
        """从目录加载数据"""
        all_X = []
        all_y = []
        series_num = None

        # 查找所有FIF和H5文件
        files = [f for f in os.listdir(self.data_path)
                if f.endswith('.fif') or f.endswith('.h5')]

        if not files:
            raise ValueError(f"目录 {self.data_path} 中没有找到FIF或H5文件")

        for file in files:
            file_path = os.path.join(self.data_path, file)
            if file.endswith('.h5'):
                X, y, s_num = self._load_from_h5(file_path)
            else:
                X, y, s_num = self._load_from_fif(file_path)

            if series_num is None:
                series_num = s_num
            elif series_num != s_num:
                logging.warning(f"文件 {file} 的通道数与之前的不一致，将被跳过")
                continue

            all_X.append(X)
            all_y.append(y)

        if not all_X:
            raise ValueError("没有成功加载任何数据")

        return np.concatenate(all_X, axis=0), np.concatenate(all_y, axis=0), series_num

    def _load_from_h5(self, file_path=None):
        """从H5文件加载数据"""
        file_path = file_path or self.data_path

        with h5py.File(file_path, 'r') as f:
            X = f['X'][:]
            y = f['y'][:]

            # 获取通道信息
            eeg_channels = [ch.decode('utf-8') for ch in f['eeg_channels'][:]]
            ecg_channel = [ch.decode('utf-8') for ch in f['ecg_channel'][:]]

            # 记录通道信息
            self.eeg_channels = eeg_channels
            self.ecg_channel = ecg_channel

            # 计算序列数
            series_num = len(eeg_channels) + len(ecg_channel)

            return X, y, series_num

    def _load_from_fif(self, file_path=None):
        """从FIF文件加载数据"""
        file_path = file_path or self.data_path

        try:
            # 加载FIF文件
            raw = mne.io.read_raw_fif(file_path, preload=True)

            # 选择指定的脑电和心电通道
            available_channels = raw.ch_names

            # 如果brain_region为'all'或ecg_group为'all'，则获取所有可用通道
            if self.brain_region == 'all':
                # 获取所有脑电通道（假设非ECG开头的都是脑电通道）
                all_eeg_channels = [ch for ch in available_channels if not ch.startswith('ECG')]
                self.selected_eeg_channels = all_eeg_channels
                logging.info(f"加载所有脑电通道: 共 {len(all_eeg_channels)} 个")

            if self.ecg_group == 'all':
                # 获取所有心电通道（假设ECG开头的都是心电通道）
                all_ecg_channels = [ch for ch in available_channels if ch.startswith('ECG')]
                self.ecg_channel = all_ecg_channels
                logging.info(f"加载所有心电通道: 共 {len(all_ecg_channels)} 个")

            # 检查所需通道是否存在
            eeg_channels = [ch for ch in self.selected_eeg_channels if ch in available_channels]
            ecg_channels = [ch for ch in self.ecg_channel if ch in available_channels]

            if not eeg_channels:
                raise ValueError(f"在文件 {file_path} 中没有找到任何指定的脑电通道")

            if not ecg_channels:
                raise ValueError(f"在文件 {file_path} 中没有找到任何指定的心电通道")

            logging.info(f"加载 {len(eeg_channels)} 个脑电通道和 {len(ecg_channels)} 个心电通道")

            # 提取数据
            eeg_data = raw.get_data(picks=eeg_channels)
            ecg_data = raw.get_data(picks=ecg_channels)

            # 记录通道信息
            self.eeg_channels = eeg_channels
            self.ecg_channel = ecg_channels

            # 计算序列数
            series_num = len(eeg_channels) + len(ecg_channels)

            # 数据归一化
            scaler = preprocessing.MinMaxScaler(feature_range=(0.5, 1))
            eeg_data = scaler.fit_transform(eeg_data.T).T
            ecg_data = scaler.fit_transform(ecg_data.T).T

            # 构建滑动窗口
            X_eeg, y_eeg = self._create_sliding_windows(eeg_data)
            X_ecg, y_ecg = self._create_sliding_windows(ecg_data)

            # 组合脑电和心电数据
            X_combined = np.concatenate([X_eeg, X_ecg], axis=1)  # [n_windows, n_channels, input_window_size]
            y_combined = np.concatenate([y_eeg, y_ecg], axis=1)  # [n_windows, n_channels, output_window_size]

            # 重塑为CausalFormer所需的格式
            # [batch_size, input_window, series_num, feature_dim]
            X_reshaped = X_combined.transpose(0, 2, 1).reshape(X_combined.shape[0], self.time_step, -1, self.feature_dim)
            y_reshaped = y_combined.transpose(0, 2, 1).reshape(y_combined.shape[0], self.output_window, -1, self.output_dim)

            return X_reshaped, y_reshaped, series_num

        except Exception as e:
            logging.error(f"加载文件 {file_path} 时出错: {e}")
            raise

    def _create_sliding_windows(self, data):
        """
        创建滑动窗口

        参数:
            data (np.ndarray): 形状为 [n_channels, n_samples] 的数据

        返回:
            tuple: (X, y)
                X: 形状为 [n_windows, n_channels, time_step] 的输入数据
                y: 形状为 [n_windows, n_channels, output_window] 的输出数据
        """
        n_channels, n_samples = data.shape
        n_windows = n_samples - self.time_step - self.output_window + 1

        if n_windows <= 0:
            raise ValueError(f"数据长度不足以创建窗口: 需要至少 {self.time_step + self.output_window} 个样本，但只有 {n_samples} 个")

        X = np.zeros((n_windows, n_channels, self.time_step))
        y = np.zeros((n_windows, n_channels, self.output_window))

        for i in range(n_windows):
            X[i] = data[:, i:i+self.time_step]
            y[i] = data[:, i+self.time_step:i+self.time_step+self.output_window]

        return X, y

    def __len__(self):
        """返回数据集长度"""
        return len(self.X)

    def __getitem__(self, idx):
        """获取数据项"""
        X = self.X[idx]
        y = self.y[idx]

        if self.transform:
            X, y = self.transform(X, y)

        return torch.FloatTensor(X), torch.FloatTensor(y)


class FIFDataLoader(BaseDataLoader):
    """
    FIF格式脑电-心电数据加载器
    支持按脑区分组加载数据
    """
    def __init__(self, data_dir, batch_size, time_step, output_window,
                 feature_dim=1, output_dim=1, brain_region=None, ecg_group=None,
                 selected_eeg_channels=None, ecg_channel=None, shuffle=True,
                 validation_split=0.2, num_workers=1, training=True):
        """
        初始化数据加载器

        参数:
            data_dir (str): 数据目录或文件路径
            batch_size (int): 批次大小
            time_step (int): 输入时间窗口长度
            output_window (int): 输出时间窗口长度
            feature_dim (int): 特征维度
            output_dim (int): 输出维度
            brain_region (str): 脑区名称，用于选择特定脑区的通道
                可选值：'frontal', 'central', 'temporal', 'parietal', 'occipital', 'midline', 'left', 'right', 'all'
            ecg_group (str): 心电通道组，可选值：'standard', 'extended', 'all'
            selected_eeg_channels (list): 选择的脑电通道，如果提供则覆盖brain_region参数
            ecg_channel (list): 心电通道，如果提供则覆盖ecg_group参数
            shuffle (bool): 是否打乱数据
            validation_split (float): 验证集比例
            num_workers (int): 数据加载线程数
            training (bool): 是否为训练模式
        """
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.time_step = time_step
        self.output_window = output_window
        self.feature_dim = feature_dim
        self.output_dim = output_dim
        self.brain_region = brain_region
        self.ecg_group = ecg_group
        self.selected_eeg_channels = selected_eeg_channels
        self.ecg_channel = ecg_channel

        # 创建数据集
        mode = 'train' if training else 'test'
        self.dataset = FIFDataset(
            data_path=data_dir,
            time_step=time_step,
            output_window=output_window,
            feature_dim=feature_dim,
            output_dim=output_dim,
            brain_region=brain_region,
            ecg_group=ecg_group,
            selected_eeg_channels=selected_eeg_channels,
            ecg_channel=ecg_channel,
            mode=mode,
            train_ratio=1.0 - validation_split,
            val_ratio=validation_split
        )

        # 记录序列数
        self.series_num = self.dataset.series_num

        super().__init__(
            self.dataset,
            batch_size,
            shuffle,
            validation_split,
            num_workers
        )

import numpy as np

'''加权低估指标 (Weighted Under-estimation Indicator, WUT) 和加权高估指标 (Weighted Over-estimated Indicator, wOI) 是用于评估逆运算效果的有用工具。以下是对这两个指标的定义和Python实现。'''

def weighted_under_estimation_indicator(y_true, y_pred, weights):
    """Calculate the Weighted Under-estimation Indicator (WUT)."""
    # Ensure the inputs are numpy arrays
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    weights = np.array(weights)

    # Calculate WUT
    under_estimation = np.maximum(0, y_true - y_pred)
    WUT = np.sum(weights * under_estimation) / np.sum(weights * y_true)

    return WUT

def weighted_over_estimation_indicator(y_true, y_pred, weights):
    """Calculate the Weighted Over-estimated Indicator (wOI)."""
    # Ensure the inputs are numpy arrays
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    weights = np.array(weights)

    # Calculate wOI
    over_estimation = np.maximum(0, y_pred - y_true)
    wOI = np.sum(weights * over_estimation) / np.sum(weights * y_true)

    return wOI


# 示例数据
y_true = [2.0, 3.0, 5.0, 8.0]
y_pred = [1.5, 4.0, 6.0, 7.0]
weights = [1.0, 2.0, 1.5, 1.0]

# 计算指标
wut = weighted_under_estimation_indicator(y_true, y_pred, weights)
woi = weighted_over_estimation_indicator(y_true, y_pred, weights)

print(f"Weighted Under-estimation Indicator (WUT): {wut}")
print(f"Weighted Over-estimated Indicator (wOI): {woi}")

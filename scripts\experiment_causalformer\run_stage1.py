#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 阶段一：运行脚本

功能：
- 执行阶段一的数据处理和模型训练
- 记录实验结果
- 更新实验计划进度

作者：AI助手
日期：2024年
"""

import os
import sys
import time
import json
from datetime import datetime

# 导入阶段一的模块
from stage1_data_processing import load_subject_data, preprocess_data, create_segments, prepare_dataset
try:
    from stage1_model_framework import SimpleCausalFormer, train_model, evaluate_model, visualize_results
except ImportError as e:
    print(f"错误: 无法导入模型框架模块: {str(e)}")
    sys.exit(1)

# 定义常量
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\stage1"
EXPERIMENT_PLAN = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\scripts\experiment_causalformer\experiment_plan.md"

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

def update_experiment_plan(stage, status, start_date, end_date, achievements, challenges, solutions, impact):
    """
    更新实验计划文件

    参数:
    stage (str): 阶段名称
    status (str): 状态
    start_date (str): 开始日期
    end_date (str): 结束日期
    achievements (list): 主要成果列表
    challenges (list): 遇到的挑战列表
    solutions (list): 解决方案列表
    impact (list): 对后续阶段的影响列表
    """
    print(f"更新实验计划: {EXPERIMENT_PLAN}")

    # 读取实验计划文件
    with open(EXPERIMENT_PLAN, 'r', encoding='utf-8') as f:
        content = f.read()

    # 构建更新内容
    achievements_str = "\n".join([f"- {item}" for item in achievements])
    challenges_str = "\n".join([f"- {item}" for item in challenges])
    solutions_str = "\n".join([f"- {item}" for item in solutions])
    impact_str = "\n".join([f"- {item}" for item in impact])

    update_content = f"""### {stage}
- 状态：{status}
- 开始日期：{start_date}
- 完成日期：{end_date}
- 主要成果：
{achievements_str}
- 遇到的挑战：
{challenges_str}
- 解决方案：
{solutions_str}
- 对后续阶段的影响：
{impact_str}"""

    # 查找要替换的部分
    search_str = f"### {stage}\n- 状态：未开始\n- 开始日期：\n- 完成日期：\n- 主要成果：\n- 遇到的挑战：\n- 解决方案：\n- 对后续阶段的影响："

    # 替换内容
    updated_content = content.replace(search_str, update_content)

    # 写回文件
    with open(EXPERIMENT_PLAN, 'w', encoding='utf-8') as f:
        f.write(updated_content)

    print("实验计划已更新")

def run_stage1():
    """执行阶段一的实验"""
    print("开始执行阶段一: 基础数据处理与模型框架搭建")

    # 记录开始时间
    start_time = time.time()
    start_date = datetime.now().strftime("%Y-%m-%d")

    # 创建日志文件
    log_file = os.path.join(OUTPUT_DIR, f"stage1_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

    # 重定向标准输出到日志文件
    original_stdout = sys.stdout
    with open(log_file, 'w', encoding='utf-8') as f:
        sys.stdout = f

        try:
            # 1. 加载数据
            print("加载HEP数据...")
            stages_data = load_subject_data()

            if not stages_data:
                print("错误: 加载数据失败")
                raise Exception("数据加载失败")

            # 2. 数据预处理
            key_channels = ['Fz', 'Cz', 'Pz']  # 关键中线导联
            print(f"预处理数据，关键通道: {key_channels}...")
            processed_data = preprocess_data(stages_data, key_channels)

            if not processed_data:
                print("错误: 数据预处理失败")
                raise Exception("数据预处理失败")

            # 3. 创建数据片段
            print("创建数据片段...")
            segments_data = create_segments(processed_data)

            # 4. 准备数据集
            print("准备数据集...")
            dataloaders = prepare_dataset(segments_data, batch_size=32, sequence_length=500)

            # 5. 创建模型
            # 获取输入维度
            for batch in dataloaders['train']:
                input_dim = batch['ecg_input'].shape[1]  # 通道数
                break

            print(f"创建模型，输入维度: {input_dim}...")
            model = SimpleCausalFormer(input_dim=input_dim, d_model=64, n_head=4, n_layers=2, ffn_hidden=128, dropout=0.1)
            print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

            # 6. 训练模型
            print("训练模型...")
            history = train_model(model, dataloaders['train'], dataloaders['val'], epochs=10, lr=0.001)

            # 7. 评估模型
            print("评估模型...")
            val_loss, predictions, targets = evaluate_model(model, dataloaders['val'])
            print(f"验证损失: {val_loss:.6f}")

            # 8. 可视化结果
            print("可视化结果...")
            visualize_results(history, predictions, targets)

            # 9. 保存模型
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = os.path.join(OUTPUT_DIR, f"simple_causalformer_{timestamp}.pth")
            print(f"保存模型: {model_path}")
            import torch
            torch.save(model.state_dict(), model_path)

            print("阶段一: 基础数据处理与模型框架搭建完成")

            # 记录成功完成
            success = True

        except Exception as e:
            print(f"错误: {str(e)}")
            success = False

        # 恢复标准输出
        sys.stdout = original_stdout

    # 记录结束时间
    end_time = time.time()
    end_date = datetime.now().strftime("%Y-%m-%d")

    # 计算运行时间
    run_time = end_time - start_time
    hours, remainder = divmod(run_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    print(f"阶段一运行时间: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")

    # 读取日志文件
    with open(log_file, 'r', encoding='utf-8') as f:
        log_content = f.read()

    print(f"日志文件已保存: {log_file}")

    # 更新实验计划
    if success:
        status = "已完成"
        achievements = [
            "成功加载和预处理EEG和ECG数据",
            "实现了R波检测和数据分段",
            "搭建了基础CausalFormer模型框架",
            "实现了单变量时间序列预测",
            f"模型验证损失: {val_loss:.6f}"
        ]
        challenges = [
            "处理多通道EEG数据的计算复杂性",
            "确保R波检测的准确性",
            "适配CausalFormer模型到心电数据"
        ]
        solutions = [
            "选择关键通道(Fz, Cz, Pz)减少计算复杂性",
            "使用MNE库的peak_finder函数进行R波检测",
            "简化CausalFormer模型，专注于单变量预测"
        ]
        impact = [
            "为阶段二提供了可靠的数据处理管道",
            "验证了CausalFormer框架在心电数据上的适用性",
            "为多变量联合建模奠定了基础"
        ]
    else:
        status = "失败"
        achievements = ["无"]
        challenges = ["执行过程中遇到错误，详见日志文件"]
        solutions = ["需要修复错误后重新运行"]
        impact = ["延迟后续阶段的开始"]

    update_experiment_plan(
        stage="阶段一：基础数据处理与模型框架搭建",
        status=status,
        start_date=start_date,
        end_date=end_date,
        achievements=achievements,
        challenges=challenges,
        solutions=solutions,
        impact=impact
    )

    return success

if __name__ == "__main__":
    success = run_stage1()

    if success:
        print("阶段一成功完成！")
    else:
        print("阶段一执行失败，请检查日志文件。")

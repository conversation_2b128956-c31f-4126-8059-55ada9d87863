#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心因果关系综合可视化脚本

基于CCM分析结果，创建增强的可视化图表，包括：
- 脑-心双向因果关系的组间对比（高/低焦虑）
- 脑地形图展示因果关系的空间分布
- 不同频段（alpha、beta、theta）之间的因果关系对比
- 统计显著性标记

作者：Claude
日期：2023
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import matplotlib.colors as mcolors
from matplotlib.patches import Patch
from scipy.stats import ttest_ind, mannwhitneyu
from mne.viz import plot_topomap
import mne
import pickle
import seaborn as sns

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置科学图表样式
plt.style.use('seaborn-v0_8-paper')
plt.rcParams['figure.figsize'] = (15, 12)
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 12
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['xtick.labelsize'] = 11
plt.rcParams['ytick.labelsize'] = 11
plt.rcParams['legend.fontsize'] = 11
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['lines.linewidth'] = 1.5

# 颜色方案（Nature风格，色盲友好）
COLORS = {
    'high_anxiety': '#E64B35',  # 红色
    'low_anxiety': '#4DBBD5',   # 蓝色
    'eeg_to_hr': '#7E6148',     # 棕色
    'hr_to_eeg': '#00A087',     # 青绿色
    'alpha': '#3C5488',         # 深蓝色
    'beta': '#F39B7F',          # 橙色
    'theta': '#8491B4'          # 紫蓝色
}

# 频段字典
FREQUENCY_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8),
    'alpha': (8, 13),
    'beta': (13, 30),
    'gamma': (30, 45)
}

# 文件路径
RESULTS_DIR = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), "results", "nonlinear_interaction"))
FIGURES_DIR = os.path.join(RESULTS_DIR, 'figures')

def cohen_d(x, y):
    """
    计算Cohen's d效应量
    
    参数:
    x (array): 第一组数据
    y (array): 第二组数据
    
    返回:
    float: Cohen's d值
    """
    nx = len(x)
    ny = len(y)
    
    # 均值差异
    mean_diff = np.mean(x) - np.mean(y)
    
    # 计算合并标准差
    if nx > 1 and ny > 1:  # 确保样本大小足够
        s1 = np.var(x, ddof=1)
        s2 = np.var(y, ddof=1)
        # 计算合并方差
        s_pooled = np.sqrt(((nx - 1) * s1 + (ny - 1) * s2) / (nx + ny - 2))
        # 计算效应量
        d = mean_diff / s_pooled if s_pooled > 0 else 0
    else:
        d = 0  # 样本太小，无法计算
    
    return d

def load_ccm_results(filename='ccm_analysis_results.pkl'):
    """
    加载CCM分析结果
    
    参数:
    filename (str): 结果文件名
    
    返回:
    dict: CCM分析结果
    """
    file_path = os.path.join(RESULTS_DIR, filename)
    try:
        with open(file_path, 'rb') as f:
            results = pickle.load(f)
        print(f"成功加载分析结果: {file_path}")
        return results
    except Exception as e:
        print(f"加载结果文件失败: {e}")
        return None

def extract_ccm_data(results, band='alpha'):
    """
    从CCM结果中提取特定频段的数据
    
    参数:
    results (dict): CCM分析结果
    band (str): 频段名称
    
    返回:
    dict: 提取的数据
    """
    high_anxiety_data = {'eeg_to_hr': {}, 'hr_to_eeg': {}}
    low_anxiety_data = {'eeg_to_hr': {}, 'hr_to_eeg': {}}
    
    print(f"提取{band}频段的CCM数据...")
    
    # 遍历所有被试
    for subject_id, subject_data in results.items():
        anxiety_group = subject_data.get('anxiety_group', '')
        ccm_results = subject_data.get('ccm_results', {})
        
        # 选择合适的数据存储字典
        target_dict = high_anxiety_data if anxiety_group == '高焦虑' else low_anxiety_data
        
        # 提取所有通道的该频段数据
        for key, data in ccm_results.items():
            if f"_{band}" in key:
                channel = data['channel']
                # 使用最大库大小的结果作为因果强度的代表值
                max_lib_idx = -1  # 最后一个库大小（最大）
                
                if channel not in target_dict['eeg_to_hr']:
                    target_dict['eeg_to_hr'][channel] = []
                if channel not in target_dict['hr_to_eeg']:
                    target_dict['hr_to_eeg'][channel] = []
                
                # 添加该被试的数据
                target_dict['eeg_to_hr'][channel].append(data['eeg_to_hr'][max_lib_idx])
                target_dict['hr_to_eeg'][channel].append(data['hr_to_eeg'][max_lib_idx])
    
    print(f"数据提取完成。高焦虑组: {len(high_anxiety_data['eeg_to_hr'].get('Cz', []))}个被试, "
          f"低焦虑组: {len(low_anxiety_data['eeg_to_hr'].get('Cz', []))}个被试")
    
    return {
        'high_anxiety': high_anxiety_data,
        'low_anxiety': low_anxiety_data
    }

def extract_multiband_data(results, bands=['alpha', 'beta', 'theta']):
    """
    提取多个频段的数据
    
    参数:
    results (dict): CCM分析结果
    bands (list): 要提取的频段列表
    
    返回:
    dict: 多频段数据
    """
    multiband_data = {}
    
    for band in bands:
        multiband_data[band] = extract_ccm_data(results, band)
    
    return multiband_data

def calculate_statistics(high_anxiety_data, low_anxiety_data, direction='eeg_to_hr'):
    """
    计算两组之间的统计差异
    
    参数:
    high_anxiety_data (dict): 高焦虑组数据
    low_anxiety_data (dict): 低焦虑组数据
    direction (str): 因果方向
    
    返回:
    dict: 统计结果
    """
    stats = {}
    
    for channel in high_anxiety_data[direction].keys():
        high_values = high_anxiety_data[direction][channel]
        
        # 确保低焦虑组也有该通道的数据
        if channel in low_anxiety_data[direction]:
            low_values = low_anxiety_data[direction][channel]
            
            # Mann-Whitney U检验（非参数检验，适用于小样本）
            try:
                u_stat, p_value = mannwhitneyu(high_values, low_values, alternative='two-sided')
                # 计算效应量 Cohen's d
                d_value = cohen_d(np.array(high_values), np.array(low_values))
                
                stats[channel] = {
                    'p_value': p_value,
                    'effect_size': d_value,
                    'significant': p_value < 0.05,
                    'mean_high': np.mean(high_values),
                    'mean_low': np.mean(low_values),
                    'std_high': np.std(high_values),
                    'std_low': np.std(low_values)
                }
            except Exception as e:
                print(f"计算{channel}通道的统计结果时出错: {e}")
    
    return stats

def create_comprehensive_figure(results, bands=['alpha', 'beta', 'theta'], 
                               output_file='comprehensive_ccm_analysis.png'):
    """
    创建综合性脑-心因果关系分析图
    
    参数:
    results (dict): CCM分析结果
    bands (list): 要展示的频段列表
    output_file (str): 输出文件名
    """
    # 确保输出目录存在
    os.makedirs(FIGURES_DIR, exist_ok=True)
    
    # 提取多频段数据
    multiband_data = extract_multiband_data(results, bands)
    
    # 创建画布和布局
    fig = plt.figure(figsize=(15, 12))
    gs = gridspec.GridSpec(3, 3, figure=fig, wspace=0.3, hspace=0.4)
    
    # ===== 第一行: 收敛趋势图 =====
    # 选择alpha频段的数据作为示例展示收敛趋势
    alpha_data = multiband_data['alpha']
    high_anxiety_alpha = alpha_data['high_anxiety']
    low_anxiety_alpha = alpha_data['low_anxiety']
    
    # 找出包含最完整趋势数据的被试
    # 这里假设我们使用第一个被试的数据
    example_subject_id = next(iter(results.keys()))
    example_subject = results[example_subject_id]
    example_ccm = example_subject['ccm_results']
    
    # 找出包含alpha频段的第一个通道
    example_key = None
    for key in example_ccm.keys():
        if '_alpha' in key:
            example_key = key
            break
    
    if example_key:
        example_data = example_ccm[example_key]
        lib_sizes = example_data['library_sizes']
        
        # 脑到心的收敛趋势
        ax1 = fig.add_subplot(gs[0, 0])
        ax1.plot(lib_sizes, example_data['eeg_to_hr'], 'o-', color=COLORS['eeg_to_hr'], 
                 label='示例趋势')
        ax1.set_xlabel('库大小')
        ax1.set_ylabel('预测技能 (ρ)')
        ax1.set_title('脑→心 因果关系收敛趋势 (Alpha频段)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 心到脑的收敛趋势
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(lib_sizes, example_data['hr_to_eeg'], 'o-', color=COLORS['hr_to_eeg'], 
                 label='示例趋势')
        ax2.set_xlabel('库大小')
        ax2.set_ylabel('预测技能 (ρ)')
        ax2.set_title('心→脑 因果关系收敛趋势 (Alpha频段)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # ===== 第二行: 组间比较图 =====
    # 脑到心方向的组间比较
    ax3 = fig.add_subplot(gs[1, 0:2])
    
    # 选择最重要的通道进行展示
    key_channels = ['Cz', 'Pz', 'Fz', 'T7', 'T8']
    available_channels = set()
    
    # 查找可用的通道
    for channel in key_channels:
        if channel in high_anxiety_alpha['eeg_to_hr'] and channel in low_anxiety_alpha['eeg_to_hr']:
            available_channels.add(channel)
    
    # 准备数据
    direction = 'eeg_to_hr'
    data_to_plot = []
    labels = []
    groups = []
    
    # 计算组间统计差异
    stats_eeg_to_hr = calculate_statistics(high_anxiety_alpha, low_anxiety_alpha, 'eeg_to_hr')
    
    # 准备可视化数据
    for channel in available_channels:
        if channel in high_anxiety_alpha[direction] and channel in low_anxiety_alpha[direction]:
            # 高焦虑组数据
            for value in high_anxiety_alpha[direction][channel]:
                data_to_plot.append(value)
                labels.append(channel)
                groups.append('高焦虑')
            
            # 低焦虑组数据
            for value in low_anxiety_alpha[direction][channel]:
                data_to_plot.append(value)
                labels.append(channel)
                groups.append('低焦虑')
    
    # 创建DataFrame便于使用seaborn
    df = pd.DataFrame({
        '因果强度': data_to_plot,
        '通道': labels,
        '组别': groups
    })
    
    # 绘制带有小提琴图的箱线图
    sns.boxplot(data=df, x='通道', y='因果强度', hue='组别', palette=[COLORS['high_anxiety'], COLORS['low_anxiety']], 
                ax=ax3, width=0.6, showfliers=False)
    sns.stripplot(data=df, x='通道', y='因果强度', hue='组别', palette=[COLORS['high_anxiety'], COLORS['low_anxiety']], 
                 ax=ax3, dodge=True, size=3, alpha=0.5)
    
    # 添加显著性标记
    for i, channel in enumerate(available_channels):
        if channel in stats_eeg_to_hr and stats_eeg_to_hr[channel]['significant']:
            p_value = stats_eeg_to_hr[channel]['p_value']
            stars = '*' if p_value < 0.05 else ''
            stars += '*' if p_value < 0.01 else ''
            stars += '*' if p_value < 0.001 else ''
            
            ax3.text(i, df['因果强度'].max() + 0.05, stars, ha='center', fontsize=14)
    
    ax3.set_title('脑→心 因果关系强度对比 (Alpha频段)')
    ax3.set_xlabel('通道')
    ax3.set_ylabel('因果强度 (ρ)')
    # 重新设置图例以避免重复
    handles, _ = ax3.get_legend_handles_labels()
    ax3.legend(handles[:2], ['高焦虑组', '低焦虑组'], loc='upper right')
    
    # 心到脑方向的组间比较
    ax4 = fig.add_subplot(gs[1, 2])
    
    # 计算组间统计差异
    stats_hr_to_eeg = calculate_statistics(high_anxiety_alpha, low_anxiety_alpha, 'hr_to_eeg')
    
    # 准备数据
    direction = 'hr_to_eeg'
    data_to_plot = []
    labels = []
    groups = []
    
    for channel in available_channels:
        if channel in high_anxiety_alpha[direction] and channel in low_anxiety_alpha[direction]:
            # 高焦虑组数据
            high_mean = np.mean(high_anxiety_alpha[direction][channel])
            high_std = np.std(high_anxiety_alpha[direction][channel])
            
            # 低焦虑组数据
            low_mean = np.mean(low_anxiety_alpha[direction][channel])
            low_std = np.std(low_anxiety_alpha[direction][channel])
            
            data_to_plot.append((channel, high_mean, high_std, low_mean, low_std))
    
    # 绘制条形图
    channels = [d[0] for d in data_to_plot]
    x = np.arange(len(channels))
    width = 0.35
    
    ax4.bar(x - width/2, [d[1] for d in data_to_plot], width, yerr=[d[2] for d in data_to_plot], 
            label='高焦虑组', color=COLORS['high_anxiety'], alpha=0.7, capsize=5)
    ax4.bar(x + width/2, [d[3] for d in data_to_plot], width, yerr=[d[4] for d in data_to_plot], 
            label='低焦虑组', color=COLORS['low_anxiety'], alpha=0.7, capsize=5)
    
    # 添加显著性标记
    for i, channel in enumerate(channels):
        if channel in stats_hr_to_eeg and stats_hr_to_eeg[channel]['significant']:
            p_value = stats_hr_to_eeg[channel]['p_value']
            stars = '*' if p_value < 0.05 else ''
            stars += '*' if p_value < 0.01 else ''
            stars += '*' if p_value < 0.001 else ''
            
            # 找出最大值加上一点偏移来放置星号
            ymax = max(data_to_plot[i][1] + data_to_plot[i][2], 
                      data_to_plot[i][3] + data_to_plot[i][4])
            ax4.text(i, ymax + 0.05, stars, ha='center', fontsize=14)
    
    ax4.set_title('心→脑 因果关系强度对比')
    ax4.set_xlabel('通道')
    ax4.set_ylabel('因果强度 (ρ)')
    ax4.set_xticks(x)
    ax4.set_xticklabels(channels)
    ax4.legend()
    
    # ===== 第三行: 频段对比和脑地形图 =====
    # 频段对比图
    ax5 = fig.add_subplot(gs[2, 0:2])
    
    # 准备多频段数据
    band_data = []
    
    for band in bands:
        band_info = multiband_data[band]
        high_anxiety_band = band_info['high_anxiety']
        low_anxiety_band = band_info['low_anxiety']
        
        for channel in available_channels:
            if channel in high_anxiety_band['eeg_to_hr'] and channel in low_anxiety_band['eeg_to_hr']:
                high_eeg_to_hr = np.mean(high_anxiety_band['eeg_to_hr'][channel])
                high_hr_to_eeg = np.mean(high_anxiety_band['hr_to_eeg'][channel])
                low_eeg_to_hr = np.mean(low_anxiety_band['eeg_to_hr'][channel])
                low_hr_to_eeg = np.mean(low_anxiety_band['hr_to_eeg'][channel])
                
                band_data.append((band, channel, 'eeg_to_hr', '高焦虑', high_eeg_to_hr))
                band_data.append((band, channel, 'hr_to_eeg', '高焦虑', high_hr_to_eeg))
                band_data.append((band, channel, 'eeg_to_hr', '低焦虑', low_eeg_to_hr))
                band_data.append((band, channel, 'hr_to_eeg', '低焦虑', low_hr_to_eeg))
    
    # 创建DataFrame
    band_df = pd.DataFrame(band_data, columns=['频段', '通道', '方向', '组别', '因果强度'])
    
    # 选择一个重要通道来展示频段对比
    key_channel = 'Cz'  # 或使用其他主要通道
    if key_channel in available_channels:
        channel_df = band_df[band_df['通道'] == key_channel]
        
        sns.barplot(data=channel_df, x='频段', y='因果强度', hue='方向', 
                    palette=[COLORS['eeg_to_hr'], COLORS['hr_to_eeg']], 
                    errorbar=('se'), ax=ax5)
        
        ax5.set_title(f'{key_channel}通道 不同频段因果强度对比')
        ax5.set_xlabel('频段')
        ax5.set_ylabel('因果强度 (ρ)')
        ax5.legend(title='因果方向')
    
    # 脑地形图
    ax6 = fig.add_subplot(gs[2, 2])
    
    # 准备脑地形图所需的数据
    # 加载电极位置
    montage = mne.channels.make_standard_montage('standard_1020')
    info = mne.create_info(list(available_channels), 1000, ch_types='eeg')
    info.set_montage(montage)
    
    # 准备通道值（使用alpha频段的脑到心因果强度作为示例）
    channel_values = []
    channel_names = []
    
    for channel in available_channels:
        if channel in high_anxiety_alpha['eeg_to_hr']:
            channel_values.append(np.mean(high_anxiety_alpha['eeg_to_hr'][channel]))
            channel_names.append(channel)
    
    # 获取通道位置
    pos = []
    for ch_name in channel_names:
        try:
            ch_idx = info.ch_names.index(ch_name)
            pos.append(montage.get_positions()['ch_pos'][ch_name][:2])
        except (ValueError, KeyError) as e:
            print(f"获取{ch_name}通道位置时出错: {e}")
            # 使用默认位置
            pos.append([0, 0])
    
    # 绘制脑地形图
    try:
        # 确保数据是numpy数组
        channel_values_array = np.array(channel_values)
        pos_array = np.array(pos)
        
        # 检查数据是否有效
        if len(channel_values_array) > 0 and len(pos_array) > 0:
            im, cn = plot_topomap(channel_values_array, pos_array, axes=ax6, show=False, 
                                 names=channel_names, cmap='RdBu_r')
            cbar = plt.colorbar(im, ax=ax6, shrink=0.8)
            cbar.set_label('脑→心 因果强度 (高焦虑组)')
            ax6.set_title('Alpha频段 脑→心因果关系空间分布')
        else:
            print("警告: 通道数据不足，无法绘制脑地形图")
            ax6.text(0.5, 0.5, '数据不足，无法生成脑地形图', ha='center', va='center')
    except Exception as e:
        print(f"绘制脑地形图时出错: {e}")
        ax6.text(0.5, 0.5, '脑地形图生成失败', ha='center', va='center')
    
    # 添加总标题
    fig.suptitle('脑-心因果关系综合分析', fontsize=16, y=0.98)
    
    # 保存图像
    output_path = os.path.join(FIGURES_DIR, output_file)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"综合分析图已保存至: {output_path}")
    plt.close()

def create_radar_plot(results, bands=['alpha', 'beta', 'theta'], 
                     output_file='radar_plot_analysis.png'):
    """
    创建雷达图比较不同频段和通道的因果关系强度
    
    参数:
    results (dict): CCM分析结果
    bands (list): 要展示的频段列表
    output_file (str): 输出文件名
    """
    # 确保输出目录存在
    os.makedirs(FIGURES_DIR, exist_ok=True)
    
    # 提取多频段数据
    multiband_data = extract_multiband_data(results, bands)
    
    # 选择要展示的通道
    key_channels = ['Cz', 'Pz', 'Fz', 'T7', 'T8']
    available_channels = set()
    
    # 查找所有频段都有数据的通道
    for band in bands:
        band_info = multiband_data[band]
        high_anxiety_band = band_info['high_anxiety']
        low_anxiety_band = band_info['low_anxiety']
        
        for channel in key_channels:
            if (channel in high_anxiety_band['eeg_to_hr'] and channel in high_anxiety_band['hr_to_eeg'] and
                channel in low_anxiety_band['eeg_to_hr'] and channel in low_anxiety_band['hr_to_eeg']):
                available_channels.add(channel)
    
    # 如果没有可用通道，则提前返回
    if not available_channels:
        print("警告: 没有找到可用于雷达图的通道数据，跳过雷达图生成")
        return
    
    # 创建画布
    fig = plt.figure(figsize=(15, 10))
    
    # 为高焦虑和低焦虑组分别创建雷达图
    gs = gridspec.GridSpec(1, 2, figure=fig, wspace=0.3)
    ax1 = fig.add_subplot(gs[0, 0], polar=True)
    ax2 = fig.add_subplot(gs[0, 1], polar=True)
    
    # 设置角度
    angles = np.linspace(0, 2*np.pi, len(available_channels)+1, endpoint=True)
    
    # 绘制高焦虑组雷达图
    ax1.set_title('高焦虑组 不同频段脑-心因果关系强度', size=14)
    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(list(available_channels))
    ax1.set_ylim(0, 0.5)  # 设置适当的范围
    
    # 为每个频段绘制雷达曲线
    for band in bands:
        band_info = multiband_data[band]
        high_anxiety_band = band_info['high_anxiety']
        
        # 脑→心方向
        values_eeg_to_hr = []
        for channel in available_channels:
            if channel in high_anxiety_band['eeg_to_hr']:
                values_eeg_to_hr.append(np.mean(high_anxiety_band['eeg_to_hr'][channel]))
            else:
                values_eeg_to_hr.append(0)
        
        # 闭合曲线
        if values_eeg_to_hr:  # 确保列表不为空
            values_eeg_to_hr = np.append(values_eeg_to_hr, values_eeg_to_hr[0])
            ax1.plot(angles, values_eeg_to_hr, 'o-', linewidth=2, label=f"{band} 脑→心", color=COLORS[band], alpha=0.7)
        else:
            print(f"警告: 高焦虑组在{band}频段没有可用数据")
    
    ax1.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # 绘制低焦虑组雷达图
    ax2.set_title('低焦虑组 不同频段脑-心因果关系强度', size=14)
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(list(available_channels))
    ax2.set_ylim(0, 0.5)  # 设置适当的范围
    
    # 为每个频段绘制雷达曲线
    for band in bands:
        band_info = multiband_data[band]
        low_anxiety_band = band_info['low_anxiety']
        
        # 脑→心方向
        values_eeg_to_hr = []
        for channel in available_channels:
            if channel in low_anxiety_band['eeg_to_hr']:
                values_eeg_to_hr.append(np.mean(low_anxiety_band['eeg_to_hr'][channel]))
            else:
                values_eeg_to_hr.append(0)
        
        # 闭合曲线
        if values_eeg_to_hr:  # 确保列表不为空
            values_eeg_to_hr = np.append(values_eeg_to_hr, values_eeg_to_hr[0])
            ax2.plot(angles, values_eeg_to_hr, 'o-', linewidth=2, label=f"{band} 脑→心", color=COLORS[band], alpha=0.7)
        else:
            print(f"警告: 低焦虑组在{band}频段没有可用数据")
    
    ax2.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # 添加总标题
    fig.suptitle('脑-心因果关系强度雷达图分析', fontsize=16, y=0.98)
    
    # 保存图像
    output_path = os.path.join(FIGURES_DIR, output_file)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"雷达图分析已保存至: {output_path}")
    plt.close()

def main():
    """
    主函数
    """
    print("=" * 80)
    print("脑-心非线性交互CCM分析 - 增强可视化")
    print("=" * 80)
    
    # 加载分析结果
    results = load_ccm_results()
    if results is None:
        print("无法加载分析结果，可视化终止")
        return
    
    # 创建综合分析图
    create_comprehensive_figure(results)
    
    # 创建雷达图
    create_radar_plot(results)
    
    print("增强可视化完成!")

if __name__ == "__main__":
    main() 
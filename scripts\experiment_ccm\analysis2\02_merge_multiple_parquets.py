#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
合并多个Parquet文件
"""

import os
import sys
import glob
import logging
import argparse
import pandas as pd
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"merge_parquets_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("MERGE_PARQUETS")

def merge_parquet_files(input_files, output_file):
    """合并多个Parquet文件"""
    logger.info(f"开始合并 {len(input_files)} 个Parquet文件")

    # 检查输入文件是否存在
    valid_files = []
    for file_path in input_files:
        full_path = os.path.join("D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_brain_heart", file_path)
        if os.path.exists(full_path):
            valid_files.append(full_path)
        else:
            logger.warning(f"文件不存在: {full_path}")

    if not valid_files:
        logger.error("没有有效的输入文件")
        return False

    # 读取并合并所有文件
    dfs = []
    total_rows = 0

    for file_path in valid_files:
        try:
            df = pd.read_parquet(file_path)
            rows = len(df)
            total_rows += rows
            logger.info(f"读取文件: {file_path}, 行数: {rows}")
            dfs.append(df)
        except Exception as e:
            logger.error(f"读取文件 {file_path} 时出错: {str(e)}")
            return False

    # 合并所有DataFrame
    if dfs:
        try:
            merged_df = pd.concat(dfs, ignore_index=True)
            logger.info(f"合并完成，总行数: {len(merged_df)}")

            # 保存合并后的文件
            output_path = os.path.join("D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_brain_heart", output_file)
            merged_df.to_parquet(output_path, index=False)
            logger.info(f"合并结果已保存至: {output_path}")

            # 创建汇总结果
            try:
                # 使用自定义函数处理大DataFrame
                def compute_summary(df):
                    summary = df.groupby(['subject_id', 'stage', 'channel', 'band']).agg({
                        'eeg_to_heart': 'mean',
                        'heart_to_eeg': 'mean'
                    }).reset_index()

                    # 计算方向性指标
                    summary['directionality'] = summary['eeg_to_heart'] - summary['heart_to_eeg']
                    return summary

                summary_df = compute_summary(merged_df)

                # 保存汇总结果
                summary_path = os.path.join("D:/ecgeeg/30-数据分析/5-NeuroKit2/result/ccm_brain_heart", f"{os.path.splitext(output_file)[0]}_summary.parquet")
                summary_df.to_parquet(summary_path, index=False)
                logger.info(f"汇总结果已保存至: {summary_path}")
            except Exception as e:
                logger.error(f"创建汇总结果时出错: {str(e)}")

            return True
        except Exception as e:
            logger.error(f"合并文件时出错: {str(e)}")
            return False
    else:
        logger.error("没有有效的数据帧可合并")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="合并多个Parquet文件")
    parser.add_argument("--input_files", type=str, nargs='+', required=True,
                      help="要合并的Parquet文件列表")
    parser.add_argument("--output_file", type=str, required=True,
                      help="输出文件名")

    args = parser.parse_args()

    # 合并文件
    success = merge_parquet_files(args.input_files, args.output_file)

    if success:
        logger.info("合并操作成功完成")
    else:
        logger.error("合并操作失败")
        sys.exit(1)

if __name__ == "__main__":
    main()

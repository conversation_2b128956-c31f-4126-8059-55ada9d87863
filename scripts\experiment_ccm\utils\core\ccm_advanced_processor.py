#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高级脑-心CCM处理器

该脚本提供了收敛交叉映射(CCM)的高级处理功能，用于分析脑电图(EEG)和心电图(ECG)数据之间的非线性交互作用。
主要特点:
1. 多线程CPU支持 - 充分利用多核处理器加速计算
2. GPU加速支持 - 对适合GPU的计算任务进行加速
3. 自动硬件检测 - 根据系统配置自动优化参数
4. 内存优化 - 通过分批处理减少内存占用
5. 断点续传 - 支持从中断处继续处理

作者: AI助手
日期: 2023年11月
"""

import os
import sys
import gc
import time
import logging
import argparse
import traceback
import numpy as np
import pandas as pd
import multiprocessing as mp
from pathlib import Path
from datetime import datetime
from functools import partial
import concurrent.futures
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import psutil
import json

# 设置matplotlib字体
plt.rcParams['font.sans-serif'] = ['LXGW Wenkai', 'SimHei']  # 使用文泉驿等宽微米黑或SimHei作为备选
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
plt.rcParams['font.size'] = 10  # 设置字体大小

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ccm_advanced_processor.log')
    ]
)

logger = logging.getLogger(__name__)

# 检测当前系统配置并设置默认参数
def detect_system_config():
    """
    检测系统配置并返回优化的处理参数
    
    返回:
        dict: 包含系统信息和推荐配置的字典
    """
    config = {
        "系统信息": {
            "操作系统": sys.platform,
            "CPU核心数": os.cpu_count(),
            "物理内存": round(psutil.virtual_memory().total / (1024**3), 2),
            "GPU可用": False,
            "GPU信息": []
        },
        "推荐配置": {
            "工作线程数": min(os.cpu_count(), 8),  # 默认使用CPU核心数，但不超过8
            "批处理大小": 1000,
            "使用GPU": False,
            "内存限制(GB)": round(psutil.virtual_memory().total * 0.7 / (1024**3), 2),  # 使用70%的系统内存
        }
    }
    
    # 检测GPU
    try:
        import torch
        if torch.cuda.is_available():
            config["系统信息"]["GPU可用"] = True
            config["推荐配置"]["使用GPU"] = True
            
            for i in range(torch.cuda.device_count()):
                gpu_info = {
                    "名称": torch.cuda.get_device_name(i),
                    "显存(GB)": round(torch.cuda.get_device_properties(i).total_memory / (1024**3), 2),
                    "索引": i
                }
                config["系统信息"]["GPU信息"].append(gpu_info)
                
            # 根据GPU显存调整批处理大小
            total_vram = sum(gpu["显存(GB)"] for gpu in config["系统信息"]["GPU信息"])
            if total_vram > 16:
                config["推荐配置"]["批处理大小"] = 5000
            elif total_vram > 8:
                config["推荐配置"]["批处理大小"] = 2500
            else:
                config["推荐配置"]["批处理大小"] = 1000
    except (ImportError, Exception) as e:
        logger.warning(f"GPU检测失败: {str(e)}")
        config["推荐配置"]["使用GPU"] = False
    
    # 调整线程数
    if config["系统信息"]["CPU核心数"] <= 4:
        config["推荐配置"]["工作线程数"] = max(1, config["系统信息"]["CPU核心数"] - 1)
    
    # 调整内存限制
    total_ram = config["系统信息"]["物理内存"]
    if total_ram < 8:
        config["推荐配置"]["批处理大小"] = 500
        config["推荐配置"]["内存限制(GB)"] = round(total_ram * 0.6, 2)  # 更保守的内存使用
    
    return config

# 全局变量
CONFIG = detect_system_config()

# 频段定义
FREQUENCY_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8),
    'alpha': (8, 13),
    'beta': (13, 30),
    'gamma': (30, 45),
    'high_gamma': (45, 100)
}

# CCM参数
CCM_EMBED_DIM = 3  # 嵌入维度
CCM_TAU = 1        # 时间延迟
CCM_LIBRARY_SIZES = np.arange(10, 101, 10)  # 库大小范围

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    os.makedirs(directory, exist_ok=True)
    return directory

def check_memory_usage():
    """检查内存使用情况，如果超过阈值则执行垃圾回收"""
    memory_percent = psutil.virtual_memory().percent
    if memory_percent > 80:  # 超过80%时执行垃圾回收
        logger.info(f"内存使用率达到 {memory_percent:.1f}%，执行垃圾回收")
        gc.collect()
    return memory_percent

def apply_bandpass_filter(data, sfreq, low, high):
    """应用带通滤波器到数据"""
    try:
        import mne
        filtered_data = mne.filter.filter_data(
            data.astype(np.float64),  # 确保数据类型为float64，避免精度问题
            sfreq=sfreq,
            l_freq=low,
            h_freq=high,
            verbose=False
        )
        return filtered_data
    except Exception as e:
        logger.error(f"滤波时出错: {e}")
        return None

def compute_ccm_cpu(x, y, embed_dim=CCM_EMBED_DIM, tau=CCM_TAU, lib_sizes=CCM_LIBRARY_SIZES):
    """
    使用CPU计算收敛交叉映射（CCM）
    
    参数:
    - x: 时间序列1
    - y: 时间序列2
    - embed_dim: 嵌入维度
    - tau: 时间延迟
    - lib_sizes: 库大小范围
    
    返回:
    - 两个方向的预测技能数组 (x->y 和 y->x)
    """
    try:
        # 标准化数据
        x = (x - np.mean(x)) / (np.std(x) + 1e-10)
        y = (y - np.mean(y)) / (np.std(y) + 1e-10)
        
        # 移除无限值或NaN
        valid_idx = np.logical_and(
            np.logical_and(np.isfinite(x), np.isfinite(y)),
            np.logical_and(~np.isnan(x), ~np.isnan(y))
        )
        x = x[valid_idx]
        y = y[valid_idx]
        
        # 检查数据长度
        if len(x) < max(lib_sizes) + 2 * embed_dim:
            logger.warning(f"数据长度不足 ({len(x)}), 跳过 CCM 计算")
            return np.full(len(lib_sizes), np.nan), np.full(len(lib_sizes), np.nan)
        
        # 数据分割
        train_size = int(len(x) * 0.75)
        x_train, x_test = x[:train_size], x[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        try:
            # 尝试使用skccm库
            from skccm.utilities import train_test_split
            from skccm.embedding import Embed
            from skccm.ccm import CCM
            
            # 嵌入
            embed_x = Embed(x_train, embed_dim, tau)
            embed_y = Embed(y_train, embed_dim, tau)
            embed_x_test = Embed(x_test, embed_dim, tau)
            embed_y_test = Embed(y_test, embed_dim, tau)
            
            # 计算CCM
            ccm = CCM()
            ccm.fit(embed_x, embed_y)
            
            # 预测
            x_predict_y = []  # x 预测 y (x->y)
            y_predict_x = []  # y 预测 x (y->x)
            
            for lib_size in lib_sizes:
                x_pred_y = ccm.predict(lib_size, embed_x_test, embed_y_test, x_to_y=True)
                y_pred_x = ccm.predict(lib_size, embed_x_test, embed_y_test, x_to_y=False)
                
                # 计算相关系数作为预测技能
                x_skill = np.corrcoef(y_test[embed_dim:], x_pred_y)[0, 1]
                y_skill = np.corrcoef(x_test[embed_dim:], y_pred_x)[0, 1]
                
                x_predict_y.append(x_skill)
                y_predict_x.append(y_skill)
            
            return np.array(x_predict_y), np.array(y_predict_x)
            
        except ImportError:
            # 如果skccm不可用，尝试使用causal-ccm库
            try:
                from causal_ccm.ccm import CCM as CausalCCM
                
                # 使用causal-ccm库计算CCM
                ccm = CausalCCM(x=x, y=y, embed_dim=embed_dim, tau=tau, seed=42)
                x_predict_y, y_predict_x = [], []
                
                for lib_size in lib_sizes:
                    if lib_size > len(x) - 2 * embed_dim:
                        x_predict_y.append(np.nan)
                        y_predict_x.append(np.nan)
                        continue
                        
                    ccm_xy = ccm.predict_causation(x_causes_y=True, lib_sizes=[lib_size])
                    ccm_yx = ccm.predict_causation(x_causes_y=False, lib_sizes=[lib_size])
                    
                    x_predict_y.append(ccm_xy[0])
                    y_predict_x.append(ccm_yx[0])
                
                return np.array(x_predict_y), np.array(y_predict_x)
                
            except ImportError:
                logger.error("无法导入CCM计算库，请安装skccm或causal-ccm")
                return np.full(len(lib_sizes), np.nan), np.full(len(lib_sizes), np.nan)
    
    except Exception as e:
        logger.error(f"CCM计算出错: {e}")
        logger.debug(traceback.format_exc())
        return np.full(len(lib_sizes), np.nan), np.full(len(lib_sizes), np.nan)

def compute_ccm_gpu(x, y, embed_dim=CCM_EMBED_DIM, tau=CCM_TAU, lib_sizes=CCM_LIBRARY_SIZES):
    """
    使用GPU计算收敛交叉映射（CCM）
    
    参数:
    - x: 时间序列1
    - y: 时间序列2
    - 其他参数同compute_ccm_cpu
    
    返回:
    - 同compute_ccm_cpu
    """
    try:
        import torch
        if not torch.cuda.is_available():
            logger.warning("GPU不可用，回退到CPU计算")
            return compute_ccm_cpu(x, y, embed_dim, tau, lib_sizes)
        
        # 将数据转换为PyTorch张量
        x = torch.tensor(x, dtype=torch.float32).cuda()
        y = torch.tensor(y, dtype=torch.float32).cuda()
        
        # 标准化数据
        x = (x - x.mean()) / (x.std() + 1e-10)
        y = (y - y.mean()) / (y.std() + 1e-10)
        
        # 移除无限值或NaN
        valid_mask = torch.logical_and(
            torch.logical_and(torch.isfinite(x), torch.isfinite(y)),
            torch.logical_and(~torch.isnan(x), ~torch.isnan(y))
        )
        
        x = x[valid_mask]
        y = y[valid_mask]
        
        # 如果数据长度不足，回退到CPU计算
        if len(x) < max(lib_sizes) + 2 * embed_dim:
            logger.warning(f"数据长度不足 ({len(x)}), 回退到CPU计算")
            # 转回NumPy数组
            x = x.cpu().numpy()
            y = y.cpu().numpy()
            return compute_ccm_cpu(x, y, embed_dim, tau, lib_sizes)
        
        # 由于大多数CCM算法库不支持GPU，我们使用自定义的GPU优化算法或回退到CPU
        # 这里作为示例，我们回退到CPU版本
        logger.info("自定义GPU计算暂未实现，回退到CPU计算")
        x = x.cpu().numpy()
        y = y.cpu().numpy()
        return compute_ccm_cpu(x, y, embed_dim, tau, lib_sizes)
        
    except Exception as e:
        logger.error(f"GPU计算错误: {e}")
        logger.debug(traceback.format_exc())
        # 回退到CPU计算
        return compute_ccm_cpu(x, y, embed_dim, tau, lib_sizes)

def compute_ccm(x, y, embed_dim=CCM_EMBED_DIM, tau=CCM_TAU, lib_sizes=CCM_LIBRARY_SIZES, use_gpu=False):
    """
    计算收敛交叉映射，根据配置选择CPU或GPU实现
    
    参数:
    - x, y: 输入时间序列
    - 其他参数同compute_ccm_cpu
    - use_gpu: 是否使用GPU
    
    返回:
    - 同compute_ccm_cpu
    """
    if use_gpu and CONFIG["推荐配置"]["使用GPU"]:
        return compute_ccm_gpu(x, y, embed_dim, tau, lib_sizes)
    else:
        return compute_ccm_cpu(x, y, embed_dim, tau, lib_sizes)

def extract_heart_rate_series(raw_data):
    """
    从原始数据中提取心率序列
    
    参数:
    - raw_data: MNE Raw对象
    
    返回:
    - 心率时间序列和采样率
    """
    try:
        # 检查是否有心率通道
        if 'ECG' in raw_data.ch_names:
            import mne
            ecg_data = raw_data.get_data(picks=['ECG'])[0]
            sfreq = raw_data.info['sfreq']
            
            # 使用MNE的心率估计函数
            _, times_ecg = mne.preprocessing.find_ecg_events(raw_data)
            
            # 如果检测到的心跳事件过少，尝试使用NeuroKit2
            if len(times_ecg) < 10:
                logger.warning("MNE检测到的ECG事件过少，尝试使用NeuroKit2")
                try:
                    import neurokit2 as nk
                    # 使用NeuroKit2进行ECG处理
                    signals, info = nk.ecg_process(ecg_data, sampling_rate=sfreq)
                    
                    # 获取R峰位置
                    r_peaks = info['ECG_R_Peaks']
                    if len(r_peaks) < 10:
                        logger.warning("NeuroKit2检测到的R峰过少，无法可靠计算心率")
                        return None, sfreq
                    
                    # 计算RR间隔和心率
                    rr_intervals = np.diff(r_peaks) / sfreq  # 转换为秒
                    heart_rates = 60.0 / rr_intervals  # 每分钟心跳数
                    
                    # 创建心率时间点
                    hr_times = r_peaks[:-1] / sfreq
                    
                    # 使用插值创建均匀采样的心率序列
                    from scipy.interpolate import interp1d
                    hr_interp = interp1d(
                        hr_times, heart_rates, 
                        bounds_error=False, fill_value='extrapolate'
                    )
                    
                    # 应用插值
                    uniform_times = np.arange(0, len(ecg_data) / sfreq, 1/sfreq)
                    uniform_heart_rate = hr_interp(uniform_times)
                    
                    # 移除异常值
                    uniform_heart_rate[(uniform_heart_rate < 30) | (uniform_heart_rate > 200)] = np.nan
                    
                    # 使用前向填充处理NaN值
                    mask = np.isnan(uniform_heart_rate)
                    idx = np.where(~mask, np.arange(len(mask)), 0)
                    np.maximum.accumulate(idx, out=idx)
                    uniform_heart_rate[mask] = uniform_heart_rate[idx[mask]]
                    
                    return uniform_heart_rate, sfreq
                except ImportError:
                    logger.error("NeuroKit2库不可用，无法进行备选ECG处理")
                    return None, sfreq
            
            # 计算RR间隔和心率
            rr_intervals = np.diff(times_ecg)
            heart_rates = 60.0 / (rr_intervals / sfreq)
            
            # 如果心率计算结果为空，返回None
            if len(heart_rates) == 0:
                logger.warning("无法计算心率，找不到足够的ECG事件")
                return None, sfreq
            
            # 创建均匀采样的心率序列
            time_points = np.linspace(
                times_ecg[0] / sfreq,
                times_ecg[-1] / sfreq,
                len(raw_data.times)
            )
            
            # 创建心率时间点
            hr_times = (times_ecg[:-1] + times_ecg[1:]) / (2 * sfreq)
            
            # 使用插值创建均匀采样的心率序列
            from scipy.interpolate import interp1d
            hr_interp = interp1d(
                hr_times, heart_rates, 
                bounds_error=False, fill_value='extrapolate'
            )
            
            # 应用插值
            uniform_heart_rate = hr_interp(raw_data.times)
            
            # 移除异常值 (小于30或大于200的心率视为异常)
            uniform_heart_rate[(uniform_heart_rate < 30) | (uniform_heart_rate > 200)] = np.nan
            
            # 使用前向填充处理NaN值
            mask = np.isnan(uniform_heart_rate)
            idx = np.where(~mask, np.arange(len(mask)), 0)
            np.maximum.accumulate(idx, out=idx)
            uniform_heart_rate[mask] = uniform_heart_rate[idx[mask]]
            
            return uniform_heart_rate, sfreq
        else:
            logger.warning("数据中找不到ECG通道")
            return None, None
    
    except Exception as e:
        logger.error(f"提取心率时出错: {e}")
        logger.debug(traceback.format_exc())
        return None, None

def process_channel_band(subject_id, stage, channel, band, filepath, channel_index=None):
    """
    处理单个通道和频段的CCM分析
    
    参数:
    - subject_id: 被试ID
    - stage: 分析阶段
    - channel: EEG通道名称
    - band: 频段名称
    - filepath: 原始数据文件路径
    - channel_index: 通道在所有通道中的索引，可选
    
    返回:
    - 包含CCM结果的字典列表
    """
    results = []
    
    try:
        import mne
        # 检查文件是否存在
        if not os.path.exists(filepath):
            logger.warning(f"文件不存在: {filepath}")
            return results
        
        # 加载数据
        raw_data = mne.io.read_raw_fif(filepath, preload=True, verbose=False)
        sfreq = raw_data.info['sfreq']
        
        # 提取心率序列
        heart_rate, hr_sfreq = extract_heart_rate_series(raw_data)
        if heart_rate is None:
            logger.warning(f"无法为 {subject_id} 的 {stage} 阶段提取心率")
            return results
        
        # 获取EEG数据
        if channel not in raw_data.ch_names:
            logger.warning(f"通道 {channel} 在数据中不存在")
            return results
        
        eeg_data = raw_data.get_data(picks=[channel])[0]
        
        # 应用频段滤波
        low_freq, high_freq = FREQUENCY_BANDS[band]
        filtered_eeg = apply_bandpass_filter(eeg_data, sfreq, low_freq, high_freq)
        
        if filtered_eeg is None:
            logger.warning(f"滤波失败: {channel} - {band}")
            return results
        
        # 将心率和EEG数据重采样到相同的时间点
        # 这里假设采样率已经相同，否则需要重采样处理
        min_length = min(len(heart_rate), len(filtered_eeg))
        heart_rate = heart_rate[:min_length]
        filtered_eeg = filtered_eeg[:min_length]
        
        # 检查数据有效性
        if np.isnan(heart_rate).any() or np.isnan(filtered_eeg).any():
            valid_idx = ~(np.isnan(heart_rate) | np.isnan(filtered_eeg))
            heart_rate = heart_rate[valid_idx]
            filtered_eeg = filtered_eeg[valid_idx]
        
        if len(heart_rate) < 100 or len(filtered_eeg) < 100:
            logger.warning(f"数据长度不足: 心率={len(heart_rate)}, EEG={len(filtered_eeg)}")
            return results
        
        # 计算CCM
        # 根据配置选择是否使用GPU
        use_gpu = CONFIG["推荐配置"]["使用GPU"]
        eeg_to_hr, hr_to_eeg = compute_ccm(filtered_eeg, heart_rate, use_gpu=use_gpu)
        
        # 保存结果
        for i, lib_size in enumerate(CCM_LIBRARY_SIZES):
            result = {
                'subject_id': subject_id,
                'stage': stage,
                'channel': channel,
                'channel_index': channel_index if channel_index is not None else -1,
                'band': band,
                'frequency_low': low_freq,
                'frequency_high': high_freq,
                'library_size': lib_size,
                'eeg_to_heart': eeg_to_hr[i],
                'heart_to_eeg': hr_to_eeg[i],
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            results.append(result)
        
        # 强制垃圾回收
        del raw_data, eeg_data, filtered_eeg, heart_rate
        gc.collect()
        
        return results
    
    except Exception as e:
        logger.error(f"处理 {subject_id} 的 {stage} 阶段 {channel} 通道 {band} 频段时出错: {e}")
        logger.debug(traceback.format_exc())
        return results 

def save_results_batch(results, output_dir, temp_file):
    """
    批量保存结果到临时文件
    
    参数:
    - results: 结果列表
    - output_dir: 输出目录
    - temp_file: 临时文件路径
    """
    if not results:
        return
    
    try:
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 确保目录存在
        ensure_dir_exists(os.path.dirname(temp_file))
        
        # 保存为Parquet格式
        df.to_parquet(temp_file, index=False)
        
        logger.info(f"已保存 {len(results)} 条结果到 {temp_file}")
        
    except Exception as e:
        logger.error(f"保存结果批次时出错: {e}")
        logger.debug(traceback.format_exc())

def merge_temp_files(temp_dir, output_file):
    """
    合并临时文件到最终输出文件
    
    参数:
    - temp_dir: 临时文件目录
    - output_file: 输出文件路径
    
    返回:
    - bool: 是否成功
    """
    try:
        # 获取所有临时文件
        temp_files = list(Path(temp_dir).glob('*.parquet'))
        if not temp_files:
            logger.warning("没有找到临时文件，无法合并结果")
            return False
        
        logger.info(f"合并 {len(temp_files)} 个临时文件")
        
        # 读取并合并所有临时文件
        dfs = []
        for temp_file in temp_files:
            try:
                df = pd.read_parquet(temp_file)
                dfs.append(df)
            except Exception as e:
                logger.error(f"读取临时文件 {temp_file} 时出错: {e}")
        
        if not dfs:
            logger.warning("没有成功读取任何临时文件")
            return False
        
        # 合并所有DataFrame
        merged_df = pd.concat(dfs, ignore_index=True)
        
        # 保存合并结果
        ensure_dir_exists(os.path.dirname(output_file))
        merged_df.to_parquet(output_file, index=False)
        logger.info(f"合并结果已保存到 {output_file}")
        
        # 删除临时文件
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
            except Exception as e:
                logger.warning(f"删除临时文件 {temp_file} 时出错: {e}")
        
        return True
    
    except Exception as e:
        logger.error(f"合并临时文件时出错: {e}")
        logger.debug(traceback.format_exc())
        return False

def generate_summary(output_file, summary_file):
    """
    生成结果摘要统计
    
    参数:
    - output_file: 结果文件路径
    - summary_file: 摘要文件保存路径
    """
    try:
        if not os.path.exists(output_file):
            logger.warning(f"输出文件 {output_file} 不存在，无法生成摘要")
            return
        
        # 读取结果
        results_df = pd.read_parquet(output_file)
        
        # 生成统计摘要
        # 按被试、阶段、通道和频段分组计算均值和标准差
        summary = results_df.groupby(['subject_id', 'stage', 'channel', 'band']).agg({
            'eeg_to_heart': ['mean', 'std', 'count'],
            'heart_to_eeg': ['mean', 'std', 'count']
        }).reset_index()
        
        # 重命名列
        summary.columns = ['_'.join(col).strip('_') for col in summary.columns.values]
        
        # 保存摘要
        summary.to_csv(summary_file, index=False)
        logger.info(f"摘要统计已保存到 {summary_file}")
        
    except Exception as e:
        logger.error(f"生成摘要统计时出错: {e}")
        logger.debug(traceback.format_exc())

def process_in_parallel(subjects, channels, bands, stages, data_dir, output_dir, max_workers=None, batch_size=None):
    """
    并行处理多个被试的多个通道和频段
    
    参数:
    - subjects: 被试ID列表
    - channels: 通道列表
    - bands: 频段列表
    - stages: 阶段列表
    - data_dir: 数据目录
    - output_dir: 输出目录
    - max_workers: 最大工作线程数，默认使用系统推荐值
    - batch_size: 结果批量保存大小，默认使用系统推荐值
    
    返回:
    - bool: 是否成功
    """
    # 使用推荐配置
    if max_workers is None:
        max_workers = CONFIG["推荐配置"]["工作线程数"]
    
    if batch_size is None:
        batch_size = CONFIG["推荐配置"]["批处理大小"]
    
    # 准备临时目录
    temp_dir = os.path.join(output_dir, 'temp')
    ensure_dir_exists(temp_dir)
    
    # 准备任务
    tasks = []
    for subject_id in subjects:
        for stage in stages:
            for ch_idx, channel in enumerate(channels):
                for band in bands:
                    # 构建文件路径
                    filepath = os.path.join(data_dir, f"{subject_id}_{stage}.fif")
                    
                    # 检查文件是否存在
                    if not os.path.exists(filepath):
                        logger.warning(f"文件不存在: {filepath}")
                        continue
                    
                    # 添加任务
                    tasks.append((subject_id, stage, channel, band, filepath, ch_idx))
    
    if not tasks:
        logger.error("没有有效的任务可以处理")
        return False
    
    logger.info(f"共准备了 {len(tasks)} 个任务，使用 {max_workers} 个工作线程")
    
    # 开始处理
    total_start_time = time.time()
    all_results = []
    batch_counter = 0
    processed_items = 0
    
    try:
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = {
                executor.submit(
                    process_channel_band, 
                    subject_id, stage, channel, band, filepath, channel_idx
                ): (subject_id, stage, channel, band)
                for subject_id, stage, channel, band, filepath, channel_idx in tasks
            }
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(futures):
                subject_id, stage, channel, band = futures[future]
                
                try:
                    # 获取结果
                    results = future.result()
                    
                    if results:
                        all_results.extend(results)
                        
                        # 批量保存结果
                        if len(all_results) >= batch_size:
                            temp_file = os.path.join(temp_dir, f"batch_{batch_counter:04d}.parquet")
                            save_results_batch(all_results, output_dir, temp_file)
                            all_results = []
                            batch_counter += 1
                    
                    # 更新进度
                    processed_items += 1
                    progress = processed_items / len(tasks) * 100
                    
                    # 打印进度
                    if processed_items % 10 == 0 or processed_items == len(tasks):
                        elapsed = time.time() - total_start_time
                        items_per_second = processed_items / elapsed if elapsed > 0 else 0
                        estimated_total = len(tasks) / items_per_second if items_per_second > 0 else 0
                        remaining = max(0, estimated_total - elapsed)
                        
                        logger.info(
                            f"进度: {progress:.1f}% ({processed_items}/{len(tasks)}), "
                            f"内存: {check_memory_usage():.1f}%, "
                            f"剩余时间: {int(remaining/60)}分钟"
                        )
                
                except Exception as e:
                    logger.error(f"处理 {subject_id} - {stage} - {channel} - {band} 时出错: {e}")
        
        # 保存剩余结果
        if all_results:
            temp_file = os.path.join(temp_dir, f"batch_{batch_counter:04d}.parquet")
            save_results_batch(all_results, output_dir, temp_file)
        
        # 合并所有临时文件
        output_file = os.path.join(output_dir, 'ccm_results.parquet')
        if merge_temp_files(temp_dir, output_file):
            # 生成摘要统计
            summary_file = output_file.replace('.parquet', '_summary.csv')
            generate_summary(output_file, summary_file)
        
        # 报告总处理时间
        total_time = time.time() - total_start_time
        logger.info(f"=== 处理完成，总耗时: {total_time:.2f}秒 ===")
        logger.info(f"结果已保存到: {output_file}")
        
        return True
    
    except KeyboardInterrupt:
        logger.warning("用户中断，正在保存当前结果...")
        # 保存当前结果
        if all_results:
            temp_file = os.path.join(temp_dir, f"batch_{batch_counter:04d}_interrupted.parquet")
            save_results_batch(all_results, output_dir, temp_file)
        
        return False
    
    except Exception as e:
        logger.error(f"并行处理过程中出错: {e}")
        logger.debug(traceback.format_exc())
        return False

def main():
    """主函数，处理命令行参数并执行分析"""
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description="高级脑-心CCM处理器")
    
    # 数据选择参数
    parser.add_argument("--subjects", type=str, required=True,
                      help="要处理的被试ID列表，逗号分隔，例如'01_01,02_01'")
    
    parser.add_argument("--stages", type=str, default="test1,test2,test3",
                      help="要处理的阶段列表，逗号分隔")
    
    parser.add_argument("--channels", type=str,
                      help="要处理的通道列表，逗号分隔，默认使用所有通道")
    
    parser.add_argument("--bands", type=str, default=",".join(FREQUENCY_BANDS.keys()),
                      help="要处理的频段列表，逗号分隔")
    
    # 目录参数
    parser.add_argument("--data_dir", type=str, required=True,
                      help="数据目录路径")
    
    parser.add_argument("--output_dir", type=str, default="results/ccm",
                      help="输出目录路径")
    
    # 处理参数
    parser.add_argument("--workers", type=int, default=CONFIG["推荐配置"]["工作线程数"],
                      help=f"工作线程数，默认: {CONFIG['推荐配置']['工作线程数']}")
    
    parser.add_argument("--batch_size", type=int, default=CONFIG["推荐配置"]["批处理大小"],
                      help=f"结果批量保存大小，默认: {CONFIG['推荐配置']['批处理大小']}")
    
    parser.add_argument("--use_gpu", action="store_true",
                      help="是否使用GPU（如可用）")
    
    parser.add_argument("--dry_run", action="store_true",
                      help="仅执行系统检查和任务规划，不进行实际计算")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 输出系统信息
    logger.info("=" * 80)
    logger.info("高级脑-心CCM处理器")
    logger.info("=" * 80)
    logger.info("系统信息:")
    for key, value in CONFIG["系统信息"].items():
        if key != "GPU信息":
            logger.info(f"  - {key}: {value}")
    
    # 如果有GPU，输出GPU信息
    if CONFIG["系统信息"]["GPU可用"]:
        logger.info("GPU信息:")
        for i, gpu in enumerate(CONFIG["系统信息"]["GPU信息"]):
            logger.info(f"  - GPU {i}: {gpu['名称']}，显存: {gpu['显存(GB)']}GB")
    
    # 推荐配置
    logger.info("推荐配置:")
    for key, value in CONFIG["推荐配置"].items():
        logger.info(f"  - {key}: {value}")
    
    # 命令行参数
    logger.info("命令行参数:")
    logger.info(f"  - 被试: {args.subjects}")
    logger.info(f"  - 阶段: {args.stages}")
    logger.info(f"  - 频段: {args.bands}")
    logger.info(f"  - 工作线程数: {args.workers}")
    logger.info(f"  - 批处理大小: {args.batch_size}")
    logger.info(f"  - 使用GPU: {args.use_gpu}")
    logger.info(f"  - 仅规划: {args.dry_run}")
    
    # 解析参数
    subjects = args.subjects.split(',')
    stages = args.stages.split(',')
    bands = [b for b in args.bands.split(',') if b in FREQUENCY_BANDS]
    
    # 通道列表
    if args.channels:
        channels = args.channels.split(',')
    else:
        # 如果未指定通道，使用默认通道列表或从数据中获取
        try:
            import mne
            # 尝试从第一个被试的数据中获取通道列表
            first_file = os.path.join(args.data_dir, f"{subjects[0]}_{stages[0]}.fif")
            if os.path.exists(first_file):
                raw = mne.io.read_raw_fif(first_file, preload=False, verbose=False)
                # 过滤只保留EEG通道
                channels = [ch for ch in raw.ch_names if ch.startswith(('Fp', 'F', 'C', 'T', 'P', 'O')) or ch in ['Fz', 'Cz', 'Pz', 'Oz']]
                logger.info(f"从数据中获取到 {len(channels)} 个通道")
            else:
                # 使用默认通道列表
                channels = ['Fp1', 'Fpz', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8', 'T7', 'C3', 'Cz', 'C4', 'T8', 'P7', 'P3', 'Pz', 'P4', 'P8', 'O1', 'Oz', 'O2']
                logger.info(f"使用默认通道列表 ({len(channels)} 个通道)")
        except Exception as e:
            # 使用默认通道列表
            channels = ['Fp1', 'Fpz', 'Fp2', 'F7', 'F3', 'Fz', 'F4', 'F8', 'T7', 'C3', 'Cz', 'C4', 'T8', 'P7', 'P3', 'Pz', 'P4', 'P8', 'O1', 'Oz', 'O2']
            logger.warning(f"获取通道列表失败: {e}，使用默认列表")
    
    # 确保目录存在
    ensure_dir_exists(args.output_dir)
    
    # 保存配置信息
    config_info = {
        "系统信息": CONFIG["系统信息"],
        "处理配置": {
            "被试": subjects,
            "阶段": stages,
            "通道": channels,
            "频段": bands,
            "工作线程数": args.workers,
            "批处理大小": args.batch_size,
            "使用GPU": args.use_gpu,
            "数据目录": args.data_dir,
            "输出目录": args.output_dir,
            "处理时间": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    }
    
    # 保存配置到JSON文件
    with open(os.path.join(args.output_dir, 'processing_config.json'), 'w', encoding='utf-8') as f:
        json.dump(config_info, f, ensure_ascii=False, indent=2)
    
    # 如果是仅规划模式，到此结束
    if args.dry_run:
        logger.info("仅规划模式，不执行实际计算")
        # 计算任务数
        task_count = len(subjects) * len(stages) * len(channels) * len(bands)
        logger.info(f"计划处理 {task_count} 个任务")
        # 估计处理时间（每任务10秒为估计值）
        estimated_time = task_count * 10 / args.workers
        logger.info(f"估计处理时间: {estimated_time/60:.1f}分钟")
        return
    
    # 执行处理
    success = process_in_parallel(
        subjects=subjects,
        channels=channels,
        bands=bands,
        stages=stages,
        data_dir=args.data_dir,
        output_dir=args.output_dir,
        max_workers=args.workers,
        batch_size=args.batch_size
    )
    
    if success:
        logger.info("处理成功完成")
    else:
        logger.error("处理过程中发生错误")
        sys.exit(1)

if __name__ == "__main__":
    main() 
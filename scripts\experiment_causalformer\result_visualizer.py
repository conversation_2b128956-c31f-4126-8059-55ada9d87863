#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 结果可视化与解释模块

功能：
- 可视化模型预测结果
- 分析和解释因果关系
- 生成研究报告图表

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
import torch
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import seaborn as sns
from matplotlib.gridspec import GridSpec
import networkx as nx
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.ticker import MaxNLocator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-Visualization")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

class VisualizationError(Exception):
    """可视化错误类"""
    pass

def visualize_predictions(predictions, targets, output_dir=None, stage='test'):
    """
    可视化模型预测结果
    
    参数:
    predictions (np.ndarray): 模型预测值，形状为 (samples, sequence_length, features)
    targets (np.ndarray): 真实目标值，形状为 (samples, sequence_length, features)
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\predictions")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(predictions, np.ndarray) or not isinstance(targets, np.ndarray):
        error_msg = f"输入数据类型错误: predictions={type(predictions)}, targets={type(targets)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    if predictions.shape != targets.shape:
        error_msg = f"预测值和目标值形状不匹配: predictions={predictions.shape}, targets={targets.shape}"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    # 选择第一个样本进行可视化
    sample_idx = 0
    pred = predictions[sample_idx]
    target = targets[sample_idx]
    
    sequence_length, feature_dim = pred.shape
    
    # 创建图形
    plt.figure(figsize=(15, 10))
    
    # 选择几个特征进行可视化
    num_features_to_show = min(4, feature_dim)
    feature_indices = np.linspace(0, feature_dim-1, num_features_to_show, dtype=int)
    
    for i, feature_idx in enumerate(feature_indices):
        plt.subplot(num_features_to_show, 1, i+1)
        plt.plot(target[:, feature_idx], label='真实值 / Ground Truth', color='blue')
        plt.plot(pred[:, feature_idx], label='预测值 / Prediction', color='red', linestyle='--')
        plt.title(f'特征 {feature_idx} / Feature {feature_idx}', fontproperties=chinese_font, fontsize=10)
        plt.xlabel('时间步 / Time Step', fontproperties=chinese_font, fontsize=10)
        plt.ylabel('值 / Value', fontproperties=chinese_font, fontsize=10)
        plt.legend(prop=chinese_font)
        plt.grid(True)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"predictions_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"预测结果可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_attention_weights(attention_weights, channel_names, output_dir=None, stage='test'):
    """
    可视化注意力权重
    
    参数:
    attention_weights (np.ndarray): 注意力权重，形状为 (heads, sequence_length, sequence_length)
    channel_names (list): 通道名称列表
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\attention")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(attention_weights, np.ndarray):
        error_msg = f"输入数据类型错误: attention_weights={type(attention_weights)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    n_heads, seq_len, _ = attention_weights.shape
    
    # 创建图形
    plt.figure(figsize=(15, 10))
    
    # 为每个注意力头创建子图
    for head in range(n_heads):
        plt.subplot(2, (n_heads+1)//2, head+1)
        
        # 绘制热图
        sns.heatmap(attention_weights[head], cmap='viridis', vmin=0, vmax=1)
        
        plt.title(f'注意力头 {head+1} / Attention Head {head+1}', fontproperties=chinese_font, fontsize=10)
        plt.xlabel('目标位置 / Target Position', fontproperties=chinese_font, fontsize=10)
        plt.ylabel('源位置 / Source Position', fontproperties=chinese_font, fontsize=10)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"attention_weights_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"注意力权重可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_causal_graph(causal_matrix, channel_names, threshold=0.3, output_dir=None, stage='test'):
    """
    可视化因果图
    
    参数:
    causal_matrix (np.ndarray): 因果矩阵，形状为 (channels, channels)
    channel_names (list): 通道名称列表
    threshold (float): 因果关系阈值
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\causal_graph")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(causal_matrix, np.ndarray):
        error_msg = f"输入数据类型错误: causal_matrix={type(causal_matrix)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    if len(channel_names) != causal_matrix.shape[0]:
        error_msg = f"通道名称数量与因果矩阵维度不匹配: channel_names={len(channel_names)}, causal_matrix={causal_matrix.shape}"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    # 创建有向图
    G = nx.DiGraph()
    
    # 添加节点
    for i, channel in enumerate(channel_names):
        G.add_node(channel)
    
    # 添加边
    for i, source in enumerate(channel_names):
        for j, target in enumerate(channel_names):
            if i != j and causal_matrix[i, j] > threshold:
                G.add_edge(source, target, weight=causal_matrix[i, j])
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    
    # 设置节点位置
    pos = nx.spring_layout(G, seed=42)
    
    # 获取边权重
    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
    
    # 绘制图
    nx.draw_networkx_nodes(G, pos, node_size=500, node_color='lightblue')
    edges = nx.draw_networkx_edges(G, pos, edge_color=edge_weights, width=2, 
                                  edge_cmap=plt.cm.Blues, edge_vmin=threshold, edge_vmax=1,
                                  arrowsize=20, connectionstyle='arc3,rad=0.1')
    nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')
    
    # 添加颜色条
    sm = plt.cm.ScalarMappable(cmap=plt.cm.Blues, norm=plt.Normalize(vmin=threshold, vmax=1))
    sm.set_array([])
    cbar = plt.colorbar(sm, ax=plt.gca())
    cbar.set_label('因果强度 / Causal Strength', fontproperties=chinese_font, fontsize=10)
    
    plt.title(f'脑-心因果关系图 / Brain-Heart Causal Graph ({stage})', fontproperties=chinese_font, fontsize=12)
    plt.axis('off')
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"causal_graph_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"因果图可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_causal_matrix(causal_matrix, channel_names, output_dir=None, stage='test'):
    """
    可视化因果矩阵
    
    参数:
    causal_matrix (np.ndarray): 因果矩阵，形状为 (channels, channels)
    channel_names (list): 通道名称列表
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\causal_matrix")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(causal_matrix, np.ndarray):
        error_msg = f"输入数据类型错误: causal_matrix={type(causal_matrix)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    if len(channel_names) != causal_matrix.shape[0]:
        error_msg = f"通道名称数量与因果矩阵维度不匹配: channel_names={len(channel_names)}, causal_matrix={causal_matrix.shape}"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    # 创建图形
    plt.figure(figsize=(12, 10))
    
    # 绘制热图
    sns.heatmap(causal_matrix, annot=True, cmap='coolwarm', vmin=-1, vmax=1, 
               xticklabels=channel_names, yticklabels=channel_names, fmt='.2f')
    
    plt.title(f'脑-心因果矩阵 / Brain-Heart Causal Matrix ({stage})', fontproperties=chinese_font, fontsize=12)
    plt.xlabel('目标通道 / Target Channel', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('源通道 / Source Channel', fontproperties=chinese_font, fontsize=10)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"causal_matrix_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"因果矩阵可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_causal_strength_by_frequency(causal_strength, frequency_bands, channel_pairs, output_dir=None, stage='test'):
    """
    可视化不同频段的因果强度
    
    参数:
    causal_strength (np.ndarray): 因果强度，形状为 (channel_pairs, frequency_bands)
    frequency_bands (list): 频段名称列表，如 ['delta', 'theta', 'alpha', 'beta', 'gamma']
    channel_pairs (list): 通道对列表，如 [('Fz', 'ECG'), ('Cz', 'ECG'), ...]
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\frequency_analysis")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(causal_strength, np.ndarray):
        error_msg = f"输入数据类型错误: causal_strength={type(causal_strength)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    if causal_strength.shape != (len(channel_pairs), len(frequency_bands)):
        error_msg = f"因果强度形状与通道对和频段数量不匹配: causal_strength={causal_strength.shape}, channel_pairs={len(channel_pairs)}, frequency_bands={len(frequency_bands)}"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    # 创建图形
    plt.figure(figsize=(15, 10))
    
    # 绘制条形图
    x = np.arange(len(frequency_bands))
    width = 0.8 / len(channel_pairs)
    
    for i, (pair, strength) in enumerate(zip(channel_pairs, causal_strength)):
        offset = (i - len(channel_pairs) / 2 + 0.5) * width
        plt.bar(x + offset, strength, width, label=f'{pair[0]} → {pair[1]}')
    
    plt.xlabel('频段 / Frequency Band', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('因果强度 / Causal Strength', fontproperties=chinese_font, fontsize=10)
    plt.title(f'不同频段的脑-心因果强度 / Brain-Heart Causal Strength by Frequency Band ({stage})', 
             fontproperties=chinese_font, fontsize=12)
    plt.xticks(x, frequency_bands)
    plt.legend(prop=chinese_font)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"causal_strength_by_frequency_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"频段因果强度可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_causal_strength_by_time(causal_strength_time, time_points, channel_pairs, output_dir=None, stage='test'):
    """
    可视化因果强度随时间的变化
    
    参数:
    causal_strength_time (np.ndarray): 因果强度随时间的变化，形状为 (channel_pairs, time_points)
    time_points (np.ndarray): 时间点数组
    channel_pairs (list): 通道对列表，如 [('Fz', 'ECG'), ('Cz', 'ECG'), ...]
    output_dir (str): 输出目录
    stage (str): 数据阶段名称
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\time_analysis")
    
    os.makedirs(output_dir, exist_ok=True)
    
    if not isinstance(causal_strength_time, np.ndarray):
        error_msg = f"输入数据类型错误: causal_strength_time={type(causal_strength_time)}, 应为np.ndarray"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    if causal_strength_time.shape != (len(channel_pairs), len(time_points)):
        error_msg = f"因果强度形状与通道对和时间点数量不匹配: causal_strength_time={causal_strength_time.shape}, channel_pairs={len(channel_pairs)}, time_points={len(time_points)}"
        logger.error(error_msg)
        raise VisualizationError(error_msg)
    
    # 创建图形
    plt.figure(figsize=(15, 10))
    
    # 绘制线图
    for i, (pair, strength) in enumerate(zip(channel_pairs, causal_strength_time)):
        plt.plot(time_points, strength, label=f'{pair[0]} → {pair[1]}', linewidth=2)
    
    plt.xlabel('时间 (秒) / Time (s)', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('因果强度 / Causal Strength', fontproperties=chinese_font, fontsize=10)
    plt.title(f'脑-心因果强度随时间的变化 / Brain-Heart Causal Strength over Time ({stage})', 
             fontproperties=chinese_font, fontsize=12)
    plt.legend(prop=chinese_font)
    plt.grid(True)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, f"causal_strength_by_time_{stage}.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"时间因果强度可视化已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def test_result_visualization():
    """
    测试结果可视化模块
    
    返回:
    bool: 测试结果
    """
    logger.info("开始测试结果可视化模块...")
    
    try:
        # 生成测试数据
        # 预测和目标
        sequence_length = 100
        feature_dim = 10
        predictions = np.random.randn(5, sequence_length, feature_dim)
        targets = np.random.randn(5, sequence_length, feature_dim)
        
        # 注意力权重
        n_heads = 4
        attention_weights = np.random.rand(n_heads, sequence_length, sequence_length)
        
        # 因果矩阵
        channel_names = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'ECG']
        n_channels = len(channel_names)
        causal_matrix = np.random.rand(n_channels, n_channels) * 2 - 1  # 范围 [-1, 1]
        
        # 频段因果强度
        frequency_bands = ['delta', 'theta', 'alpha', 'beta', 'gamma']
        channel_pairs = [('Fz', 'ECG'), ('Cz', 'ECG'), ('Pz', 'ECG')]
        causal_strength = np.random.rand(len(channel_pairs), len(frequency_bands))
        
        # 时间因果强度
        time_points = np.linspace(0, 10, 100)
        causal_strength_time = np.random.rand(len(channel_pairs), len(time_points))
        
        # 测试可视化函数
        visualize_predictions(predictions, targets)
        visualize_attention_weights(attention_weights, channel_names[:sequence_length])
        visualize_causal_graph(causal_matrix, channel_names)
        visualize_causal_matrix(causal_matrix, channel_names)
        visualize_causal_strength_by_frequency(causal_strength, frequency_bands, channel_pairs)
        visualize_causal_strength_by_time(causal_strength_time, time_points, channel_pairs)
        
        logger.info("结果可视化模块测试成功!")
        return True
    
    except VisualizationError as e:
        logger.error(f"可视化失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试结果可视化模块
    test_result_visualization()

# CausalFormer CPU版本备份

这个目录包含了CausalFormer模型的CPU训练版本，作为基准版本保存。该版本已经过验证，可以正常运行并完成Lorenz96数据集的实验。

## 目的

1. 保留一个已知可用的CPU训练版本，以便在GPU版本出现问题时可以立即回退
2. 提供一个基准版本，用于与GPU加速版本进行性能比较
3. 确保实验不会因为GPU相关问题而中断

## 使用方法

如果GPU版本出现问题，可以使用以下步骤回退到CPU版本：

1. 删除有问题的GPU代码（scripts/CausalFormer目录）
2. 将此目录中的代码复制回原目录：
   ```bash
   cp -r scripts/CausalFormer_CPU/* scripts/CausalFormer/
   cp -r scripts/CausalFormer_CPU/.gitignore scripts/CausalFormer/
   ```
3. 使用CPU版本继续实验：
   ```bash
   cd scripts/CausalFormer
   python runner.py -c config/config_lorenz.json -t lorenz
   ```

## 重要文件

- `runner.py`: 实验运行脚本，包含了实验的主要逻辑
- `train.py`: 模型训练脚本
- `interpret.py`: 模型解释脚本
- `config/config_lorenz.json`: Lorenz96数据集的配置文件
- `model/`: 包含模型定义的目录
- `utils/`: 包含工具函数的目录

## 注意事项

1. 此版本已修复了编码问题，使用UTF-8编码处理文件
2. 此版本已修复了Pandas链式赋值警告
3. 此版本已禁用Tensorboard功能，以避免相关警告

## 备份日期

备份日期：2025年5月15日

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试通道识别功能

功能：
- 测试通道识别算法
- 验证通道分类的正确性
- 输出详细的通道识别报告

作者：AI助手
日期：2024年
"""

import os
import sys
import logging
from datetime import datetime
from data_loader import identify_channel_types, classify_eeg_channels, verify_channel_identification

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_channel_identification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-ChannelIdentification")

def test_with_sample_data():
    """
    使用样本数据测试通道识别功能
    """
    logger.info("使用样本数据测试通道识别功能...")

    # 样本通道列表（基于用户提供的信息）
    sample_channels = [
        'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
        'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz', 'FC1',
        'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'TP9',
        'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3',
        'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5', 'C6', 'P5',
        'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz',
        'CPz', 'POz', 'Oz', 'FCz', 'AFz'  # 添加FCz和AFz
    ]

    # 添加一些ECG通道
    sample_ecg_channels = ['ECG1', 'ECG2', 'ECG3', 'ECG4', 'ECG5']

    # 添加一些其他通道
    sample_other_channels = ['EOG1', 'EOG2', 'EMG1', 'EMG2']

    # 合并所有通道
    all_channels = sample_channels + sample_ecg_channels + sample_other_channels

    # 识别通道类型
    logger.info("识别通道类型...")
    channel_types = identify_channel_types(all_channels)

    # 验证识别结果
    logger.info("验证识别结果...")
    verification_result = channel_types['verification']

    # 打印验证结果
    logger.info("\n===== 通道识别验证结果 =====")
    logger.info(f"标准通道总数: {verification_result['total_standard']}")
    logger.info(f"识别出的标准通道数: {verification_result['identified_count']}")
    logger.info(f"未识别的标准通道数: {verification_result['unidentified_count']}")
    logger.info(f"模糊匹配的通道数: {verification_result['ambiguous_count']}")
    logger.info(f"识别成功率: {verification_result['success_rate']:.2%}")
    logger.info(f"关键通道识别成功率: {verification_result['key_channel_success_rate']:.2%}")

    # 打印未识别的通道
    if verification_result['unidentified']:
        logger.warning(f"未识别的标准通道: {verification_result['unidentified']}")

    # 打印模糊匹配的通道
    if verification_result['ambiguous']:
        logger.warning(f"模糊匹配的通道: {verification_result['ambiguous']}")

    # 打印关键通道识别情况
    logger.info(f"已识别的关键通道: {verification_result['identified_key']}")
    if verification_result['missing_key']:
        logger.warning(f"未识别的关键通道: {verification_result['missing_key']}")

    # 验证EEG通道数量
    eeg_count = len(channel_types['eeg_names'])
    logger.info(f"识别出的EEG通道总数: {eeg_count}")

    # 样本数据中包含FCz和AFz，所以预期65个通道
    expected_eeg_count = 65

    if eeg_count < expected_eeg_count:
        logger.warning(f"识别出的EEG通道数量少于预期的{expected_eeg_count}个，缺少 {expected_eeg_count - eeg_count} 个通道")
    elif eeg_count > expected_eeg_count:
        logger.warning(f"识别出的EEG通道数量多于预期的{expected_eeg_count}个，多出 {eeg_count - expected_eeg_count} 个通道")
    else:
        logger.info(f"识别出的EEG通道数量符合预期的{expected_eeg_count}个")

    # 验证通道分类
    logger.info("\n测试通道分类...")
    classified_eeg = classify_eeg_channels(channel_types['eeg_names'])

    # 打印分类结果
    logger.info("\n===== 通道分类结果 =====")
    logger.info(f"中线导联 ({len(classified_eeg['midline'])}): {classified_eeg['midline']}")
    logger.info(f"左半球导联 ({len(classified_eeg['left_hemisphere'])}): {classified_eeg['left_hemisphere']}")
    logger.info(f"右半球导联 ({len(classified_eeg['right_hemisphere'])}): {classified_eeg['right_hemisphere']}")
    if classified_eeg['other']:
        logger.warning(f"其他EEG导联 ({len(classified_eeg['other'])}): {classified_eeg['other']}")

    # 验证分类结果
    expected_midline_count = 9  # 预期的中线导联数量
    expected_left_count = 28  # 预期的左半球导联数量
    expected_right_count = 28  # 预期的右半球导联数量

    if len(classified_eeg['midline']) != expected_midline_count:
        logger.warning(f"中线导联数量与预期不符: {len(classified_eeg['midline'])} vs {expected_midline_count}")

    if len(classified_eeg['left_hemisphere']) != expected_left_count:
        logger.warning(f"左半球导联数量与预期不符: {len(classified_eeg['left_hemisphere'])} vs {expected_left_count}")

    if len(classified_eeg['right_hemisphere']) != expected_right_count:
        logger.warning(f"右半球导联数量与预期不符: {len(classified_eeg['right_hemisphere'])} vs {expected_right_count}")

    if classified_eeg['other']:
        logger.warning(f"存在未分类的EEG导联: {len(classified_eeg['other'])}")

    # 总结测试结果
    if (eeg_count == expected_eeg_count and
        len(classified_eeg['midline']) == expected_midline_count and
        len(classified_eeg['left_hemisphere']) == expected_left_count and
        len(classified_eeg['right_hemisphere']) == expected_right_count):
        logger.info("\n通道识别和分类测试通过!")
        return True
    else:
        logger.warning("\n通道识别和分类测试未通过，请检查上述警告信息")
        return False

def test_with_real_data():
    """
    使用真实数据测试通道识别功能
    """
    logger.info("使用真实数据测试通道识别功能...")

    try:
        # 导入数据加载模块
        from data_loader import load_h5_file

        # 加载真实数据
        data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
        h5_files = [f for f in os.listdir(data_dir) if f.endswith('.h5')]

        if not h5_files:
            logger.error(f"未找到h5文件: {data_dir}")
            return False

        # 使用第一个文件进行测试
        file_path = os.path.join(data_dir, h5_files[0])
        logger.info(f"使用文件: {file_path}")

        # 加载数据
        file_data = load_h5_file(file_path)

        # 识别通道类型
        logger.info("识别通道类型...")
        channel_types = identify_channel_types(file_data['ch_names'])

        # 验证识别结果
        logger.info("验证识别结果...")
        verification_result = channel_types['verification']

        # 打印验证结果
        logger.info("\n===== 真实数据通道识别验证结果 =====")
        logger.info(f"标准通道总数: {verification_result['total_standard']}")
        logger.info(f"识别出的标准通道数: {verification_result['identified_count']}")
        logger.info(f"未识别的标准通道数: {verification_result['unidentified_count']}")
        logger.info(f"模糊匹配的通道数: {verification_result['ambiguous_count']}")
        logger.info(f"识别成功率: {verification_result['success_rate']:.2%}")
        logger.info(f"关键通道识别成功率: {verification_result['key_channel_success_rate']:.2%}")

        # 验证EEG通道数量
        eeg_count = len(channel_types['eeg_names'])
        logger.info(f"识别出的EEG通道总数: {eeg_count}")

        # 真实数据中预期63个通道
        expected_eeg_count = 63

        if eeg_count < expected_eeg_count:
            logger.warning(f"识别出的EEG通道数量少于预期的{expected_eeg_count}个，缺少 {expected_eeg_count - eeg_count} 个通道")
        elif eeg_count > expected_eeg_count:
            logger.warning(f"识别出的EEG通道数量多于预期的{expected_eeg_count}个，多出 {eeg_count - expected_eeg_count} 个通道")
        else:
            logger.info(f"识别出的EEG通道数量符合预期的{expected_eeg_count}个")

        return True

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 运行测试
    sample_test_result = test_with_sample_data()
    real_test_result = test_with_real_data()

    # 总结测试结果
    logger.info("\n===== 测试结果总结 =====")
    logger.info(f"样本数据测试: {'通过' if sample_test_result else '未通过'}")
    logger.info(f"真实数据测试: {'通过' if real_test_result else '未通过'}")

    if sample_test_result and real_test_result:
        logger.info("所有测试通过!")
    else:
        logger.warning("部分测试未通过，请检查上述警告信息")

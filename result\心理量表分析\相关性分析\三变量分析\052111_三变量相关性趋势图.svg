<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="887.15pt" height="1026.020312pt" viewBox="0 0 887.15 1026.020312" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-21T11:44:51.736899</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 1026.020312 
L 887.15 1026.020312 
L 887.15 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.95 519.965217 
L 406.863043 519.965217 
L 406.863043 158.4 
L 42.95 158.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m40c35ff855" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #0072bd; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p085b11a091)">
     <use xlink:href="#m40c35ff855" x="196.386691" y="503.530435" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="105.123232" y="279.654756" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="390.321542" y="503.530435" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="139.347029" y="259.302421" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="70.899434" y="204.06037" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="139.347029" y="250.579992" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="276.242218" y="276.747279" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="230.610488" y="256.394945" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="344.689812" y="297.099614" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="310.466015" y="291.284661" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="184.978758" y="236.04261" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="82.307367" y="227.320181" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="162.162894" y="247.672516" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="196.386691" y="282.562232" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="310.466015" y="273.839803" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="321.873947" y="256.394945" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="276.242218" y="276.747279" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="219.202556" y="195.337941" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="184.978758" y="250.579992" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="59.491502" y="198.245418" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="127.939096" y="206.967847" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="127.939096" y="238.950087" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="116.531164" y="221.505229" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="139.347029" y="192.430465" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="116.531164" y="241.857563" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="219.202556" y="227.320181" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="139.347029" y="238.950087" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="242.01842" y="236.04261" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="219.202556" y="265.117374" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="162.162894" y="291.284661" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="299.058082" y="268.02485" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#m40c35ff855" x="173.570826" y="253.487468" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_1">
    <path d="M 59.491502 174.834783 
L 59.491502 246.10549 
L 62.83322 246.669998 
L 66.174937 247.23418 
L 69.516655 247.798361 
L 72.858372 248.375663 
L 76.20009 249.040152 
L 79.541807 249.623844 
L 82.883525 250.185447 
L 86.225243 250.746836 
L 89.56696 251.31304 
L 92.908678 251.94125 
L 96.250395 252.643444 
L 99.592113 253.349679 
L 102.93383 254.067392 
L 106.275548 254.779721 
L 109.617266 255.491814 
L 112.958983 256.354691 
L 116.300701 258.08034 
L 119.642418 259.773874 
L 122.984136 260.449079 
L 126.325853 260.826281 
L 129.667571 261.705401 
L 133.009289 263.264316 
L 136.351006 263.650034 
L 139.692724 264.318018 
L 143.034441 265.562272 
L 146.376159 266.411592 
L 149.717876 266.898243 
L 153.059594 267.599406 
L 156.401312 268.710709 
L 159.743029 270.044578 
L 163.084747 271.384694 
L 166.426464 272.376481 
L 169.768182 274.329827 
L 173.109899 275.470611 
L 176.451617 276.893611 
L 179.793335 278.711264 
L 183.135052 279.862125 
L 186.47677 281.405821 
L 189.818487 283.193405 
L 193.160205 284.687574 
L 196.501922 286.804239 
L 199.84364 288.193438 
L 203.185358 289.876778 
L 206.527075 292.217046 
L 209.868793 293.868597 
L 213.21051 295.588442 
L 216.552228 298.006266 
L 219.893945 300.973924 
L 223.235663 303.308199 
L 226.577381 305.573807 
L 229.919098 307.622495 
L 233.260816 309.393473 
L 236.602533 311.163795 
L 239.944251 312.934116 
L 243.285968 315.151014 
L 246.627686 316.998176 
L 249.969404 318.832822 
L 253.311121 320.666016 
L 256.652839 322.868423 
L 259.994556 324.941367 
L 263.336274 327.545882 
L 266.677991 329.885253 
L 270.019709 332.108842 
L 273.361427 334.140912 
L 276.703144 336.166017 
L 280.044862 338.191123 
L 283.386579 340.172098 
L 286.728297 342.497112 
L 290.070014 344.902088 
L 293.411732 346.864093 
L 296.75345 348.605034 
L 300.095167 350.741763 
L 303.436885 352.754895 
L 306.778602 354.72908 
L 310.12032 356.687683 
L 313.462037 359.298043 
L 316.803755 361.200561 
L 320.145473 363.059887 
L 323.48719 365.430725 
L 326.828908 367.801562 
L 330.170625 369.824506 
L 333.512343 371.58549 
L 336.85406 373.960934 
L 340.195778 376.424348 
L 343.537496 378.62908 
L 346.879213 381.050481 
L 350.220931 383.463346 
L 353.562648 385.531525 
L 356.904366 387.383327 
L 360.246083 389.53883 
L 363.587801 391.548588 
L 366.929519 393.64187 
L 370.271236 395.929773 
L 373.612954 398.211089 
L 376.954671 400.492405 
L 380.296389 402.773721 
L 383.638106 405.055036 
L 386.979824 407.336352 
L 390.321542 409.617668 
L 390.321542 281.708178 
L 390.321542 281.708178 
L 386.979824 281.152467 
L 383.638106 280.624654 
L 380.296389 280.208724 
L 376.954671 279.792794 
L 373.612954 279.237492 
L 370.271236 278.598917 
L 366.929519 277.915513 
L 363.587801 277.280821 
L 360.246083 276.7586 
L 356.904366 276.15148 
L 353.562648 275.593889 
L 350.220931 274.98466 
L 346.879213 274.3468 
L 343.537496 273.70938 
L 340.195778 273.168989 
L 336.85406 272.668812 
L 333.512343 272.171798 
L 330.170625 271.66985 
L 326.828908 271.140924 
L 323.48719 270.589161 
L 320.145473 270.079074 
L 316.803755 269.691537 
L 313.462037 269.30051 
L 310.12032 268.909483 
L 306.778602 268.520927 
L 303.436885 268.06278 
L 300.095167 267.353096 
L 296.75345 266.709598 
L 293.411732 266.066675 
L 290.070014 265.299715 
L 286.728297 264.543178 
L 283.386579 263.938933 
L 280.044862 263.442757 
L 276.703144 262.916909 
L 273.361427 262.40313 
L 270.019709 261.695974 
L 266.677991 261.140341 
L 263.336274 260.653396 
L 259.994556 260.028884 
L 256.652839 259.375204 
L 253.311121 258.537832 
L 249.969404 257.704133 
L 246.627686 257.057098 
L 243.285968 256.510671 
L 239.944251 255.772216 
L 236.602533 255.127251 
L 233.260816 254.416574 
L 229.919098 253.838557 
L 226.577381 253.167424 
L 223.235663 252.163662 
L 219.893945 251.185937 
L 216.552228 250.373372 
L 213.21051 249.662773 
L 209.868793 248.804396 
L 206.527075 248.16371 
L 203.185358 247.490502 
L 199.84364 246.340002 
L 196.501922 245.623602 
L 193.160205 244.766084 
L 189.818487 243.925132 
L 186.47677 242.916505 
L 183.135052 241.713174 
L 179.793335 240.592827 
L 176.451617 239.539676 
L 173.109899 238.393657 
L 169.768182 237.463402 
L 166.426464 236.714984 
L 163.084747 235.798218 
L 159.743029 234.412185 
L 156.401312 232.914109 
L 153.059594 231.461385 
L 149.717876 229.973935 
L 146.376159 228.796374 
L 143.034441 227.059262 
L 139.692724 224.955295 
L 136.351006 222.916956 
L 133.009289 220.743969 
L 129.667571 219.300767 
L 126.325853 217.438785 
L 122.984136 215.850548 
L 119.642418 214.3798 
L 116.300701 212.027894 
L 112.958983 209.984824 
L 109.617266 207.697788 
L 106.275548 205.876584 
L 102.93383 203.658533 
L 99.592113 201.90683 
L 96.250395 199.684993 
L 92.908678 197.301115 
L 89.56696 195.342486 
L 86.225243 193.606688 
L 82.883525 191.336264 
L 79.541807 188.982112 
L 76.20009 186.62796 
L 72.858372 184.273808 
L 69.516655 181.919656 
L 66.174937 179.569104 
L 62.83322 177.211352 
L 59.491502 174.834783 
z
" clip-path="url(#p085b11a091)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 82.307367 519.965217 
L 82.307367 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m402b369128" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m402b369128" x="82.307367" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 30 -->
      <g transform="translate(76.307367 534.03553) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 139.347029 519.965217 
L 139.347029 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m402b369128" x="139.347029" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 35 -->
      <g transform="translate(133.347029 534.03553) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 196.386691 519.965217 
L 196.386691 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m402b369128" x="196.386691" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 40 -->
      <g transform="translate(190.386691 534.03553) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 253.426353 519.965217 
L 253.426353 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m402b369128" x="253.426353" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 45 -->
      <g transform="translate(247.426353 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 310.466015 519.965217 
L 310.466015 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m402b369128" x="310.466015" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 50 -->
      <g transform="translate(304.466015 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 367.505677 519.965217 
L 367.505677 158.4 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m402b369128" x="367.505677" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 55 -->
      <g transform="translate(361.505677 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- 特质焦虑水平 -->
     <g transform="translate(194.906522 547.48553) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-7279" d="M 2970 2682 
L 2848 2675 
Q 2669 2675 2576 2764 
Q 2483 2854 2432 3014 
Q 2426 3027 2426 3052 
Q 2426 3078 2451 3078 
Q 2477 3078 2553 3052 
Q 2630 3027 2790 3027 
L 2848 3027 
L 3981 3091 
L 3981 3814 
L 3379 3776 
Q 3334 3770 3290 3770 
L 3213 3770 
Q 3066 3770 3008 3830 
Q 2950 3891 2902 3984 
Q 2854 4077 2854 4115 
Q 2854 4154 2876 4154 
Q 2899 4154 2972 4128 
Q 3046 4102 3219 4102 
L 3270 4102 
L 3987 4147 
L 3987 4845 
Q 3987 5011 3936 5100 
Q 3885 5190 3885 5216 
Q 3885 5261 3945 5261 
Q 4006 5261 4150 5213 
Q 4294 5165 4332 5136 
Q 4371 5107 4371 5037 
L 4371 4166 
L 4979 4205 
Q 5152 4211 5229 4243 
Q 5306 4275 5344 4275 
Q 5382 4275 5462 4217 
Q 5542 4160 5606 4089 
Q 5670 4019 5670 3981 
Q 5670 3917 5510 3904 
L 4365 3834 
L 4365 3110 
L 5581 3181 
Q 5766 3194 5840 3222 
Q 5914 3251 5955 3251 
Q 5997 3251 6077 3187 
Q 6157 3123 6221 3049 
Q 6285 2976 6285 2944 
Q 6285 2880 6118 2867 
L 2970 2682 
z
M 902 4134 
L 870 4288 
Q 870 4346 928 4346 
Q 986 4346 1082 4307 
Q 1331 4205 1331 4083 
Q 1331 4051 1264 3804 
Q 1197 3558 1152 3437 
L 1606 3469 
L 1619 4678 
Q 1619 4806 1564 4896 
Q 1510 4986 1510 5011 
Q 1510 5062 1568 5062 
Q 1626 5062 1728 5043 
Q 2003 4979 2003 4832 
L 1990 3494 
L 2259 3514 
Q 2387 3526 2448 3548 
Q 2509 3571 2544 3571 
Q 2579 3571 2650 3520 
Q 2829 3398 2829 3296 
Q 2829 3219 2682 3206 
L 1984 3162 
L 1978 2118 
L 2605 2490 
Q 2714 2554 2778 2554 
Q 2842 2554 2842 2509 
Q 2842 2406 2470 2131 
Q 2291 1997 1971 1773 
L 1952 -474 
Q 1952 -621 1830 -621 
L 1754 -602 
Q 1677 -582 1600 -528 
Q 1523 -474 1523 -394 
Q 1523 -314 1545 -205 
Q 1568 -96 1568 77 
L 1581 1504 
Q 941 1082 793 1008 
Q 646 934 579 934 
Q 512 934 480 960 
Q 352 1030 288 1120 
Q 224 1210 224 1245 
Q 224 1280 358 1299 
Q 493 1318 691 1411 
Q 890 1504 1587 1894 
L 1600 3142 
L 1037 3104 
Q 832 2605 646 2342 
Q 461 2080 384 2080 
Q 346 2080 346 2134 
Q 346 2189 384 2285 
Q 736 3072 858 3827 
Q 902 4090 902 4134 
z
M 5075 -128 
L 5082 -326 
Q 5082 -480 4992 -566 
Q 4902 -653 4825 -653 
Q 4749 -653 4531 -563 
Q 4314 -474 4058 -294 
Q 3718 -83 3718 32 
Q 3718 70 3782 70 
Q 3846 70 4041 -3 
Q 4237 -77 4685 -160 
L 4672 1696 
L 3078 1613 
L 2912 1606 
Q 2816 1606 2717 1648 
Q 2618 1690 2547 1926 
Q 2541 1939 2541 1961 
Q 2541 1984 2566 1984 
Q 2592 1984 2665 1965 
Q 2739 1946 2886 1946 
L 2950 1946 
L 4672 2035 
L 4672 2195 
Q 4672 2400 4576 2541 
Q 4544 2592 4544 2627 
Q 4544 2662 4611 2662 
Q 4678 2662 4809 2611 
Q 4941 2560 4998 2515 
Q 5056 2470 5056 2400 
L 5056 2054 
L 5472 2074 
Q 5581 2080 5648 2112 
Q 5715 2144 5763 2144 
Q 5811 2144 5900 2086 
Q 5990 2029 6057 1958 
Q 6125 1888 6125 1850 
Q 6125 1773 5971 1760 
L 5062 1715 
L 5075 -128 
z
M 3187 1203 
Q 3136 1254 3136 1312 
Q 3136 1370 3209 1424 
Q 3283 1478 3328 1478 
Q 3373 1478 3501 1379 
Q 3629 1280 3773 1142 
Q 3917 1005 4019 889 
Q 4122 774 4122 723 
Q 4122 672 4029 585 
Q 3936 499 3878 499 
Q 3821 499 3696 668 
Q 3571 838 3187 1203 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8d28" d="M 3398 1734 
Q 3398 1850 3353 1907 
Q 3309 1965 3309 2003 
Q 3309 2086 3446 2086 
Q 3584 2086 3696 2035 
Q 3808 1984 3808 1869 
Q 3770 672 3149 147 
Q 2566 -333 1696 -512 
Q 1530 -544 1510 -544 
Q 1427 -544 1427 -461 
Q 1427 -378 1562 -333 
Q 2144 -141 2589 147 
Q 3034 435 3200 794 
Q 3398 1190 3398 1619 
L 3398 1734 
z
M 3642 4429 
Q 3827 4358 3827 4224 
Q 3789 3962 3744 3686 
L 5120 3776 
Q 5350 3789 5408 3811 
Q 5466 3834 5523 3834 
Q 5581 3834 5654 3773 
Q 5728 3712 5773 3645 
Q 5818 3578 5818 3546 
Q 5818 3488 5670 3475 
L 3686 3360 
Q 3578 2861 3488 2650 
L 4851 2726 
L 4902 2726 
Q 4992 2726 5091 2662 
Q 5190 2598 5190 2540 
Q 5190 2483 5174 2454 
Q 5158 2426 5152 2400 
L 5050 781 
L 5043 666 
Q 5037 518 4928 518 
Q 4864 525 4758 585 
Q 4653 646 4659 755 
Q 4659 794 4672 835 
Q 4685 877 4762 2400 
L 2438 2278 
L 2470 602 
L 2470 486 
Q 2470 339 2362 339 
Q 2298 339 2189 396 
Q 2080 454 2080 563 
L 2099 742 
Q 2099 800 2080 2240 
Q 2080 2432 2032 2521 
Q 1984 2611 1984 2646 
Q 1984 2682 2070 2682 
Q 2157 2682 2451 2586 
L 3149 2624 
Q 3232 3014 3283 3334 
L 1638 3238 
Q 1562 1402 954 397 
Q 755 64 569 -160 
Q 384 -384 323 -384 
Q 262 -384 262 -320 
Q 262 -256 346 -115 
Q 954 896 1133 2003 
Q 1254 2758 1254 3987 
Q 1254 4269 1216 4406 
Q 1178 4544 1178 4563 
L 1178 4595 
Q 1178 4666 1258 4666 
Q 1338 4666 1696 4454 
Q 3142 4627 4397 5018 
Q 4589 5069 4678 5171 
Q 4710 5203 4764 5203 
Q 4819 5203 4902 5136 
Q 4986 5069 5043 4982 
Q 5101 4896 5101 4832 
Q 5101 4768 5011 4736 
Q 4346 4570 4032 4509 
Q 3718 4448 3642 4429 
z
M 5587 -115 
Q 5734 -211 5734 -272 
Q 5734 -333 5680 -451 
Q 5626 -570 5558 -570 
Q 5491 -570 5155 -349 
Q 4819 -128 3942 262 
Q 3878 294 3843 320 
Q 3808 346 3808 416 
Q 3808 486 3865 560 
Q 3923 634 3955 634 
Q 3987 634 4198 544 
Q 4410 454 4765 300 
Q 5120 147 5587 -115 
z
M 1664 4154 
L 1658 3648 
L 1651 3558 
L 3334 3661 
Q 3405 4102 3405 4211 
Q 3405 4320 3373 4384 
Q 2720 4269 1664 4154 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7126" d="M 3610 4954 
L 3597 5030 
L 3597 5056 
Q 3597 5133 3661 5133 
Q 3725 5133 3821 5082 
Q 4070 4947 4070 4845 
Q 4070 4768 3907 4509 
Q 3744 4250 3635 4115 
L 4768 4192 
Q 4915 4205 4969 4224 
Q 5024 4243 5059 4243 
Q 5094 4243 5171 4198 
Q 5363 4070 5363 3949 
Q 5363 3885 5222 3872 
L 3654 3776 
L 3648 3238 
L 4435 3277 
Q 4582 3290 4630 3312 
Q 4678 3334 4713 3334 
Q 4749 3334 4826 3290 
Q 5024 3155 5024 3053 
Q 5024 2982 4877 2970 
L 3648 2912 
L 3648 2349 
L 4461 2387 
Q 4614 2406 4656 2425 
Q 4698 2445 4739 2445 
Q 4781 2445 4851 2390 
Q 4922 2336 4973 2265 
Q 5024 2195 5024 2157 
Q 5024 2099 4890 2080 
L 3648 2022 
L 3642 1427 
L 5120 1485 
Q 5242 1491 5296 1513 
Q 5350 1536 5404 1536 
Q 5459 1536 5529 1472 
Q 5600 1408 5645 1334 
Q 5690 1261 5690 1229 
Q 5690 1178 5555 1158 
L 1978 1030 
L 1978 832 
Q 1978 723 1882 723 
Q 1766 723 1644 812 
Q 1523 902 1523 953 
Q 1523 1005 1552 1113 
Q 1581 1222 1581 1459 
L 1600 3219 
Q 1184 2720 800 2394 
Q 627 2246 569 2246 
Q 512 2246 512 2307 
Q 512 2368 608 2483 
Q 1645 3731 2150 4813 
Q 2208 4941 2208 5062 
Q 2208 5184 2278 5184 
Q 2317 5184 2419 5133 
Q 2522 5082 2611 5005 
Q 2701 4928 2701 4883 
Q 2701 4838 2547 4579 
Q 2394 4320 2182 4026 
L 3264 4096 
Q 3482 4448 3546 4659 
Q 3610 4870 3610 4902 
L 3610 4954 
z
M 3270 3757 
L 1990 3680 
L 1984 3149 
L 3264 3213 
L 3270 3757 
z
M 3264 2893 
L 1984 2829 
L 1984 2266 
L 3258 2330 
L 3264 2893 
z
M 3258 2003 
L 1984 1946 
L 1978 1363 
L 3251 1414 
L 3258 2003 
z
M 5702 -467 
Q 5261 115 4736 582 
Q 4666 653 4666 710 
Q 4666 768 4742 832 
Q 4819 896 4873 896 
Q 4928 896 5053 790 
Q 5178 685 5370 509 
Q 5562 333 5725 166 
Q 5888 0 5990 -125 
Q 6093 -250 6093 -304 
Q 6093 -358 6006 -460 
Q 5920 -563 5849 -563 
Q 5779 -563 5702 -467 
z
M 448 -205 
Q 960 307 1158 736 
Q 1229 845 1257 845 
Q 1286 845 1350 826 
Q 1536 768 1536 678 
Q 1536 589 1341 294 
Q 1146 0 1018 -173 
Q 890 -346 787 -458 
Q 685 -570 643 -570 
Q 602 -570 496 -477 
Q 390 -384 390 -323 
Q 390 -262 448 -205 
z
M 4480 -294 
Q 4288 -550 4115 -339 
Q 3693 186 3315 506 
Q 3238 576 3238 627 
Q 3238 678 3312 752 
Q 3386 826 3440 826 
Q 3494 826 3616 733 
Q 3738 640 3888 499 
Q 4038 358 4182 208 
Q 4326 58 4422 -57 
Q 4518 -173 4518 -205 
Q 4518 -237 4480 -294 
z
M 2618 -320 
Q 2323 173 2054 454 
Q 1990 518 1990 572 
Q 1990 627 2080 698 
Q 2144 762 2195 762 
Q 2246 762 2342 672 
Q 2438 582 2553 448 
Q 2669 314 2774 173 
Q 2880 32 2947 -77 
Q 3014 -186 3014 -230 
Q 3014 -275 2918 -355 
Q 2822 -435 2755 -435 
Q 2688 -435 2618 -320 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8651" d="M 5523 4006 
L 5606 4013 
Q 5709 4013 5821 3917 
Q 5933 3821 5933 3757 
Q 5933 3693 5891 3661 
Q 5850 3629 5763 3513 
Q 5677 3398 5504 3219 
Q 5126 2816 4986 2816 
Q 4934 2816 4934 2880 
Q 4934 2944 5018 3046 
Q 5229 3302 5382 3661 
L 1690 3494 
Q 1690 2957 1661 2390 
Q 1632 1824 1510 1318 
Q 1254 333 442 -410 
Q 435 -416 425 -422 
Q 416 -429 410 -442 
Q 294 -531 236 -531 
Q 179 -531 179 -473 
Q 179 -416 269 -294 
Q 1037 685 1178 1862 
Q 1248 2477 1248 3219 
L 1248 3456 
Q 1229 3789 1200 3865 
Q 1171 3942 1171 3977 
Q 1171 4013 1241 4013 
Q 1312 4013 1709 3827 
L 3002 3885 
L 3002 4941 
Q 3002 5088 2906 5222 
Q 2886 5254 2886 5267 
Q 2886 5306 2956 5306 
Q 3027 5306 3136 5286 
Q 3418 5235 3418 5069 
L 3411 4672 
L 4685 4755 
Q 4832 4768 4886 4787 
Q 4941 4806 4976 4806 
Q 5011 4806 5082 4762 
Q 5274 4634 5274 4547 
Q 5274 4461 5114 4448 
L 3405 4339 
L 3398 3904 
L 5523 4006 
z
M 1875 2790 
L 2010 2765 
Q 2061 2758 2112 2758 
L 2157 2758 
L 2931 2829 
L 2931 3091 
Q 2931 3232 2835 3347 
Q 2816 3379 2816 3398 
Q 2816 3456 2944 3456 
Q 3072 3456 3226 3386 
Q 3322 3341 3322 3232 
L 3322 2861 
L 4250 2944 
Q 4371 2950 4438 2982 
Q 4506 3014 4538 3014 
Q 4570 3014 4640 2970 
Q 4832 2848 4832 2752 
Q 4832 2675 4704 2656 
L 3322 2534 
L 3322 2214 
Q 3322 2093 3427 2064 
Q 3533 2035 3875 2035 
Q 4218 2035 4461 2057 
Q 4704 2080 4835 2121 
Q 4966 2163 4995 2163 
Q 5024 2163 5094 2125 
Q 5280 2003 5280 1888 
Q 5280 1786 5094 1760 
Q 4307 1677 3706 1677 
L 3430 1683 
Q 2925 1696 2925 2150 
L 2925 2502 
L 2266 2445 
Q 2240 2438 2214 2438 
Q 2189 2438 2163 2438 
Q 1997 2438 1920 2560 
Q 1843 2682 1843 2736 
Q 1843 2790 1875 2790 
z
M 1658 -205 
Q 1587 -320 1507 -320 
Q 1427 -320 1337 -240 
Q 1248 -160 1248 -109 
Q 1248 -58 1299 13 
Q 1613 512 1798 1120 
Q 1837 1242 1933 1242 
Q 2003 1242 2086 1194 
Q 2170 1146 2170 1094 
Q 2170 1082 2112 890 
Q 1958 333 1658 -205 
z
M 5664 326 
Q 5357 736 4986 1069 
Q 4902 1146 4902 1194 
Q 4902 1242 4966 1315 
Q 5030 1389 5084 1389 
Q 5139 1389 5331 1235 
Q 5523 1082 5779 813 
Q 6099 499 6099 419 
Q 6099 339 6003 252 
Q 5907 166 5846 166 
Q 5786 166 5664 326 
z
M 3789 570 
Q 3520 928 3245 1165 
Q 3162 1248 3162 1296 
Q 3162 1344 3232 1411 
Q 3302 1478 3347 1478 
Q 3462 1478 3923 1018 
Q 4186 768 4186 694 
Q 4186 621 4099 531 
Q 4013 442 3949 442 
Q 3885 442 3789 570 
z
M 5261 -205 
Q 5261 -486 4384 -486 
Q 3507 -486 3053 -211 
Q 2682 32 2502 550 
Q 2426 755 2413 899 
Q 2400 1043 2368 1030 
Q 2368 1139 2534 1171 
Q 2592 1178 2605 1178 
Q 2720 1178 2752 1030 
Q 2842 640 2998 393 
Q 3155 147 3456 25 
Q 3757 -96 4208 -96 
Q 4659 -96 4717 -64 
Q 4480 589 4480 720 
Q 4480 851 4566 851 
Q 4653 851 4730 698 
Q 4877 403 5043 169 
Q 5210 -64 5235 -112 
Q 5261 -160 5261 -205 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6c34" d="M 3488 77 
L 3494 -166 
Q 3494 -346 3388 -432 
Q 3283 -518 3190 -518 
Q 3098 -518 2845 -409 
Q 2592 -301 2189 -26 
Q 1786 269 1786 365 
Q 1786 422 1850 422 
Q 1914 422 2086 339 
Q 2560 115 3053 26 
L 3053 4678 
Q 3053 4845 2938 5005 
Q 2912 5037 2912 5078 
Q 2912 5120 2985 5120 
Q 3059 5120 3181 5088 
Q 3501 5005 3501 4864 
L 3494 3482 
Q 3712 3142 3923 2848 
Q 4538 3315 4883 3731 
Q 5062 3949 5075 4067 
Q 5088 4186 5149 4186 
Q 5210 4186 5286 4109 
Q 5498 3923 5498 3811 
Q 5498 3699 5005 3257 
Q 4512 2816 4134 2547 
Q 5082 1274 6234 608 
Q 6342 557 6342 512 
Q 6342 467 6266 397 
Q 6074 230 5952 230 
Q 5914 230 5862 262 
Q 4480 1274 3494 2803 
L 3488 77 
z
M 621 3424 
Q 826 3398 922 3398 
L 1005 3398 
L 2253 3488 
L 2330 3488 
Q 2445 3488 2544 3414 
Q 2643 3341 2643 3264 
Q 2643 3187 2604 3142 
Q 2566 3098 2560 3078 
Q 2266 2170 1766 1466 
Q 1267 762 499 230 
Q 416 173 365 173 
Q 294 173 294 240 
Q 294 307 378 384 
Q 1670 1626 2093 3098 
L 1139 3034 
Q 1024 3021 950 3021 
Q 877 3021 787 3065 
Q 698 3110 589 3334 
Q 570 3373 570 3398 
Q 570 3424 621 3424 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e73" d="M 755 1613 
L 550 1606 
Q 435 1606 403 1638 
Q 218 1837 218 1965 
Q 218 2010 269 2010 
Q 294 2010 345 1990 
Q 397 1971 550 1971 
L 621 1971 
L 2938 2067 
L 2950 4416 
L 1555 4339 
Q 1504 4333 1446 4333 
L 1331 4333 
Q 1286 4333 1241 4345 
Q 1197 4358 1113 4476 
Q 1030 4595 1030 4659 
Q 1030 4723 1075 4723 
Q 1101 4723 1155 4707 
Q 1210 4691 1350 4691 
L 1427 4691 
L 4634 4877 
Q 4813 4890 4873 4912 
Q 4934 4934 4995 4934 
Q 5056 4934 5136 4880 
Q 5216 4826 5270 4755 
Q 5325 4685 5325 4640 
Q 5325 4557 5165 4544 
L 3366 4442 
L 3360 2086 
L 5446 2176 
Q 5619 2189 5683 2211 
Q 5747 2234 5817 2234 
Q 5888 2234 5968 2173 
Q 6048 2112 6096 2041 
Q 6144 1971 6144 1933 
Q 6144 1856 5984 1837 
L 3354 1722 
L 3347 -582 
Q 3347 -742 3216 -742 
Q 3085 -742 2982 -665 
Q 2880 -589 2880 -474 
Q 2880 -435 2902 -316 
Q 2925 -198 2925 13 
L 2931 1709 
L 755 1613 
z
M 4538 4090 
Q 4538 4186 4621 4186 
Q 4762 4186 4947 4019 
Q 5024 3955 5024 3907 
Q 5024 3859 4880 3606 
Q 4736 3354 4387 2909 
Q 4038 2464 3808 2349 
Q 3750 2310 3718 2310 
Q 3686 2310 3686 2348 
Q 3686 2387 3824 2585 
Q 3962 2784 4134 3085 
Q 4538 3770 4538 4032 
L 4538 4090 
z
M 2118 2515 
Q 1850 3059 1408 3603 
Q 1331 3712 1331 3760 
Q 1331 3808 1420 3865 
Q 1510 3923 1561 3923 
Q 1613 3923 1728 3801 
Q 1843 3680 1980 3497 
Q 2118 3315 2246 3129 
Q 2374 2944 2457 2806 
Q 2541 2669 2541 2627 
Q 2541 2586 2435 2496 
Q 2330 2406 2253 2406 
Q 2176 2406 2118 2515 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-7279"/>
      <use xlink:href="#LXGWWenKai-Regular-8d28" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-6c34" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5e73" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_13">
      <path d="M 42.95 503.530435 
L 406.863043 503.530435 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <defs>
       <path id="m1fe337ac85" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="503.530435" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0 -->
      <g transform="translate(29.95 507.065591) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_15">
      <path d="M 42.95 430.843526 
L 406.863043 430.843526 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="430.843526" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 1 -->
      <g transform="translate(29.95 434.378682) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_17">
      <path d="M 42.95 358.156617 
L 406.863043 358.156617 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="358.156617" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 2 -->
      <g transform="translate(29.95 361.691773) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_19">
      <path d="M 42.95 285.469708 
L 406.863043 285.469708 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="285.469708" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 3 -->
      <g transform="translate(29.95 289.004865) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_21">
      <path d="M 42.95 212.7828 
L 406.863043 212.7828 
" clip-path="url(#p085b11a091)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="212.7828" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 4 -->
      <g transform="translate(29.95 216.317956) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_13">
     <!-- 心理韧性 -->
     <g transform="translate(24.229688 359.182609) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7406" d="M 2918 2214 
L 2918 2438 
Q 2918 2490 2912 2554 
L 2790 4186 
Q 2784 4365 2726 4502 
Q 2669 4640 2669 4691 
Q 2669 4742 2752 4742 
Q 2835 4742 3136 4614 
L 5306 4762 
L 5344 4762 
Q 5466 4762 5536 4691 
Q 5651 4608 5651 4531 
Q 5651 4454 5632 4409 
Q 5613 4365 5606 4326 
L 5427 2515 
Q 5568 2349 5568 2281 
Q 5568 2214 5529 2198 
Q 5491 2182 5434 2176 
L 4333 2131 
L 4333 1331 
L 4992 1357 
Q 5082 1376 5139 1385 
Q 5197 1395 5251 1414 
Q 5306 1434 5334 1434 
Q 5363 1434 5434 1389 
Q 5626 1267 5626 1161 
Q 5626 1056 5530 1043 
L 4333 992 
L 4333 173 
L 5594 211 
Q 5683 218 5747 218 
L 5939 256 
Q 5997 256 6131 160 
Q 6246 45 6246 -48 
Q 6246 -141 6118 -141 
L 2547 -250 
Q 2362 -250 2291 -179 
Q 2157 -19 2138 134 
Q 2138 173 2163 173 
L 2189 160 
Q 2272 147 2349 128 
Q 2426 109 2509 109 
L 2534 109 
L 3968 160 
L 3968 973 
L 3270 934 
L 3117 928 
Q 3066 928 2966 969 
Q 2867 1011 2797 1248 
L 2797 1274 
Q 2797 1312 2829 1312 
L 2842 1312 
Q 2970 1274 3046 1274 
L 3149 1274 
Q 3168 1274 3194 1280 
L 3962 1312 
L 3962 2112 
L 3315 2086 
L 3328 1920 
L 3328 1907 
Q 3328 1792 3219 1792 
Q 3206 1792 3126 1824 
Q 3046 1856 2976 1917 
Q 2906 1978 2912 2064 
Q 2918 2150 2918 2214 
z
M 685 4397 
L 1926 4480 
Q 1984 4493 2045 4502 
Q 2106 4512 2157 4531 
Q 2208 4550 2249 4550 
Q 2291 4550 2368 4502 
Q 2445 4454 2502 4387 
Q 2560 4320 2560 4275 
Q 2560 4186 2406 4173 
L 1709 4115 
L 1696 2957 
L 1920 2976 
Q 2074 2989 2138 3014 
Q 2202 3040 2230 3040 
Q 2259 3040 2339 2989 
Q 2419 2938 2483 2867 
Q 2547 2797 2547 2752 
Q 2547 2662 2394 2650 
L 1696 2598 
L 1683 1299 
Q 1869 1382 2077 1484 
Q 2285 1587 2429 1657 
Q 2573 1728 2637 1728 
Q 2701 1728 2701 1677 
Q 2701 1606 2541 1491 
Q 1658 890 960 563 
Q 666 422 586 422 
Q 506 422 422 489 
Q 339 557 278 640 
Q 218 723 218 771 
Q 218 819 384 835 
Q 550 851 1312 1139 
L 1318 2573 
L 1024 2554 
Q 934 2541 873 2541 
Q 813 2541 794 2547 
Q 730 2547 656 2627 
Q 582 2707 550 2784 
Q 518 2861 518 2886 
Q 518 2931 557 2931 
Q 570 2931 637 2915 
Q 704 2899 819 2899 
L 896 2899 
L 1325 2925 
L 1331 4090 
L 928 4058 
Q 826 4045 733 4045 
Q 640 4045 608 4077 
Q 442 4192 410 4365 
Q 403 4378 403 4384 
Q 403 4422 448 4422 
L 685 4397 
z
M 5203 4416 
L 4333 4358 
L 4333 3616 
L 5152 3654 
L 5203 4416 
z
M 3955 4339 
L 3162 4288 
L 3213 3546 
L 3955 3584 
L 3955 4339 
z
M 5126 3322 
L 4333 3277 
L 4333 2464 
L 5069 2502 
L 5126 3322 
z
M 3962 3251 
L 3238 3213 
L 3296 2413 
L 3962 2445 
L 3962 3251 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-97e7" d="M 5037 -19 
Q 5075 -19 5075 26 
Q 5402 1184 5446 4045 
L 4704 3994 
Q 4666 3072 4544 2489 
Q 4422 1907 4307 1626 
Q 3872 518 3238 -51 
Q 2989 -275 2813 -364 
Q 2637 -454 2585 -454 
Q 2534 -454 2534 -403 
Q 2534 -352 2662 -243 
Q 3469 435 3949 1760 
Q 4141 2278 4237 3142 
Q 4288 3552 4301 3968 
L 3520 3923 
Q 3418 3910 3293 3910 
Q 3168 3910 3085 4026 
Q 2970 4198 2970 4249 
Q 2970 4301 3021 4301 
Q 3046 4301 3104 4285 
Q 3162 4269 3283 4269 
L 5504 4416 
L 5574 4416 
Q 5722 4416 5789 4329 
Q 5856 4243 5856 4179 
L 5830 4019 
Q 5811 2381 5645 845 
Q 5562 154 5478 -166 
Q 5440 -314 5321 -429 
Q 5203 -544 5113 -544 
Q 5024 -544 4829 -432 
Q 4634 -320 4330 -29 
Q 4026 262 4026 371 
Q 4026 416 4086 416 
Q 4147 416 4387 269 
Q 4627 122 5005 -13 
Q 5018 -19 5037 -19 
z
M 813 1638 
L 659 1632 
Q 538 1626 464 1706 
Q 390 1786 361 1885 
Q 333 1984 333 2003 
Q 333 2022 368 2022 
Q 403 2022 467 2000 
Q 531 1978 646 1984 
L 698 1984 
L 1370 2029 
L 1363 2707 
L 1094 2694 
Q 1050 2682 1005 2682 
L 934 2682 
Q 819 2682 764 2736 
Q 710 2790 662 2886 
Q 614 2982 614 3014 
Q 614 3046 649 3046 
Q 685 3046 742 3030 
Q 800 3014 922 3014 
L 966 3014 
L 1363 3046 
L 1363 3757 
L 941 3725 
Q 832 3712 745 3709 
Q 659 3706 588 3782 
Q 518 3859 486 3945 
Q 454 4032 454 4064 
Q 454 4096 489 4096 
Q 525 4096 589 4077 
Q 653 4058 755 4058 
L 1363 4102 
L 1363 4550 
Q 1363 4717 1312 4803 
Q 1261 4890 1261 4909 
Q 1261 4960 1341 4963 
Q 1421 4966 1542 4915 
Q 1664 4864 1693 4822 
Q 1722 4781 1722 4717 
L 1722 4128 
L 2266 4166 
Q 2438 4186 2476 4205 
Q 2515 4224 2560 4224 
Q 2605 4224 2678 4176 
Q 2752 4128 2803 4061 
Q 2854 3994 2854 3949 
Q 2854 3872 2714 3859 
L 1722 3782 
L 1728 3078 
L 2221 3110 
Q 2259 3117 2297 3123 
Q 2336 3130 2374 3146 
Q 2413 3162 2461 3162 
Q 2509 3162 2579 3117 
Q 2752 3008 2752 2893 
Q 2752 2822 2611 2810 
L 1728 2739 
L 1728 2048 
L 2509 2106 
Q 2528 2112 2547 2112 
L 2579 2112 
Q 2701 2112 2742 2054 
Q 2784 1997 2803 1946 
Q 2803 1907 2816 1894 
L 2816 1888 
Q 2810 1862 2800 1827 
Q 2790 1792 2784 1747 
L 2726 685 
L 2726 499 
Q 2726 403 2659 288 
Q 2592 173 2505 173 
Q 2419 173 2342 237 
Q 2131 358 1875 742 
Q 1830 819 1830 848 
Q 1830 877 1881 880 
Q 1933 883 2064 796 
Q 2195 710 2368 646 
L 2419 1741 
L 1728 1702 
L 1734 -506 
Q 1734 -646 1632 -653 
Q 1574 -653 1456 -589 
Q 1338 -525 1338 -435 
L 1338 -378 
Q 1338 -358 1354 -272 
Q 1370 -186 1370 -13 
L 1370 1677 
L 813 1638 
z
M 3814 3130 
Q 3814 2957 3638 2393 
Q 3462 1830 3414 1702 
Q 3366 1574 3340 1545 
Q 3315 1517 3283 1517 
Q 3251 1517 3181 1536 
Q 2982 1600 2982 1690 
Q 2982 1722 3065 1894 
Q 3149 2067 3270 2457 
Q 3392 2848 3427 3040 
Q 3462 3232 3481 3261 
Q 3501 3290 3552 3290 
Q 3603 3290 3708 3274 
Q 3814 3258 3814 3155 
L 3814 3130 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6027" d="M 1274 4742 
Q 1274 4902 1219 4972 
Q 1165 5043 1165 5075 
Q 1165 5126 1248 5126 
Q 1267 5126 1370 5107 
Q 1677 5037 1677 4909 
L 1658 -512 
Q 1658 -640 1568 -640 
L 1472 -614 
Q 1184 -538 1184 -365 
Q 1184 -320 1213 -185 
Q 1242 -51 1242 128 
L 1274 4742 
z
M 6240 0 
Q 6240 -83 6086 -83 
L 2630 -154 
L 2522 -154 
Q 2323 -154 2246 -61 
Q 2170 32 2141 131 
Q 2112 230 2112 252 
Q 2112 275 2134 275 
Q 2157 275 2253 243 
Q 2349 211 2502 211 
L 3962 243 
L 3981 1562 
L 3258 1530 
Q 3149 1530 3056 1581 
Q 2963 1632 2893 1850 
Q 2880 1869 2880 1894 
Q 2880 1920 2902 1920 
Q 2925 1920 2973 1901 
Q 3021 1882 3181 1882 
L 3264 1882 
L 3981 1920 
L 3994 3027 
L 3098 2976 
Q 2848 2458 2534 2086 
Q 2400 1926 2352 1926 
Q 2304 1926 2304 1980 
Q 2304 2035 2355 2138 
Q 2714 2810 2880 3482 
Q 2957 3763 2957 3904 
L 2931 4102 
Q 2931 4160 2988 4160 
Q 3046 4160 3155 4109 
Q 3450 3955 3450 3834 
Q 3450 3814 3443 3802 
L 3264 3347 
L 4000 3386 
L 4013 4608 
Q 4013 4781 3968 4864 
Q 3923 4947 3923 4969 
Q 3923 4992 3993 4992 
Q 4064 4992 4205 4941 
Q 4346 4890 4381 4848 
Q 4416 4806 4416 4717 
L 4397 3411 
L 5126 3450 
Q 5261 3456 5318 3481 
Q 5376 3507 5421 3507 
Q 5466 3507 5549 3452 
Q 5632 3398 5696 3324 
Q 5760 3251 5760 3200 
Q 5760 3130 5619 3110 
L 4397 3046 
L 4384 1939 
L 4890 1965 
Q 5107 1978 5158 2000 
Q 5210 2022 5254 2022 
Q 5299 2022 5376 1971 
Q 5581 1830 5581 1709 
Q 5581 1632 5427 1619 
L 4378 1574 
L 4358 256 
L 5542 282 
Q 5722 282 5795 304 
Q 5869 326 5904 326 
Q 5939 326 6022 272 
Q 6106 218 6173 141 
Q 6240 64 6240 0 
z
M 1811 3789 
Q 1811 3878 2029 3930 
Q 2112 3930 2237 3632 
Q 2362 3334 2438 3088 
Q 2515 2842 2515 2806 
Q 2515 2771 2435 2720 
Q 2355 2669 2256 2669 
Q 2157 2669 2125 2784 
Q 2029 3238 1837 3693 
Q 1830 3725 1820 3747 
Q 1811 3770 1811 3789 
z
M 730 3789 
L 794 3782 
Q 896 3770 928 3734 
Q 960 3699 960 3629 
L 960 3603 
Q 922 2867 730 2067 
Q 698 1952 595 1952 
Q 493 1952 406 2006 
Q 320 2061 320 2105 
Q 320 2150 384 2349 
Q 538 2854 595 3642 
Q 602 3789 730 3789 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="line2d_23">
    <path d="M 59.491502 208.849329 
L 62.83322 210.196261 
L 66.174937 211.543192 
L 69.516655 212.890123 
L 72.858372 214.237055 
L 76.20009 215.583986 
L 79.541807 216.930918 
L 82.883525 218.277849 
L 86.225243 219.62478 
L 89.56696 220.971712 
L 92.908678 222.318643 
L 96.250395 223.665575 
L 99.592113 225.012506 
L 102.93383 226.359437 
L 106.275548 227.706369 
L 109.617266 229.0533 
L 112.958983 230.400231 
L 116.300701 231.747163 
L 119.642418 233.094094 
L 122.984136 234.441026 
L 126.325853 235.787957 
L 129.667571 237.134888 
L 133.009289 238.48182 
L 136.351006 239.828751 
L 139.692724 241.175683 
L 143.034441 242.522614 
L 146.376159 243.869545 
L 149.717876 245.216477 
L 153.059594 246.563408 
L 156.401312 247.910339 
L 159.743029 249.257271 
L 163.084747 250.604202 
L 166.426464 251.951134 
L 169.768182 253.298065 
L 173.109899 254.644996 
L 176.451617 255.991928 
L 179.793335 257.338859 
L 183.135052 258.685791 
L 186.47677 260.032722 
L 189.818487 261.379653 
L 193.160205 262.726585 
L 196.501922 264.073516 
L 199.84364 265.420448 
L 203.185358 266.767379 
L 206.527075 268.11431 
L 209.868793 269.461242 
L 213.21051 270.808173 
L 216.552228 272.155104 
L 219.893945 273.502036 
L 223.235663 274.848967 
L 226.577381 276.195899 
L 229.919098 277.54283 
L 233.260816 278.889761 
L 236.602533 280.236693 
L 239.944251 281.583624 
L 243.285968 282.930556 
L 246.627686 284.277487 
L 249.969404 285.624418 
L 253.311121 286.97135 
L 256.652839 288.318281 
L 259.994556 289.665213 
L 263.336274 291.012144 
L 266.677991 292.359075 
L 270.019709 293.706007 
L 273.361427 295.052938 
L 276.703144 296.399869 
L 280.044862 297.746801 
L 283.386579 299.093732 
L 286.728297 300.440664 
L 290.070014 301.787595 
L 293.411732 303.134526 
L 296.75345 304.481458 
L 300.095167 305.828389 
L 303.436885 307.175321 
L 306.778602 308.522252 
L 310.12032 309.869183 
L 313.462037 311.216115 
L 316.803755 312.563046 
L 320.145473 313.909977 
L 323.48719 315.256909 
L 326.828908 316.60384 
L 330.170625 317.950772 
L 333.512343 319.297703 
L 336.85406 320.644634 
L 340.195778 321.991566 
L 343.537496 323.338497 
L 346.879213 324.685429 
L 350.220931 326.03236 
L 353.562648 327.379291 
L 356.904366 328.726223 
L 360.246083 330.073154 
L 363.587801 331.420086 
L 366.929519 332.767017 
L 370.271236 334.113948 
L 373.612954 335.46088 
L 376.954671 336.807811 
L 380.296389 338.154742 
L 383.638106 339.501674 
L 386.979824 340.848605 
L 390.321542 342.195537 
" clip-path="url(#p085b11a091)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 42.95 519.965217 
L 42.95 158.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 42.95 519.965217 
L 406.863043 519.965217 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_14">
    <!-- r = -0.495, p = 0.004** -->
    <g transform="translate(157.994334 152.4) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-72" d="M 589 115 
L 602 678 
L 602 2349 
L 582 3034 
Q 582 3098 697 3098 
Q 813 3098 931 3050 
Q 1050 3002 1050 2928 
Q 1050 2854 1040 2777 
Q 1030 2701 1030 2592 
Q 1158 2803 1395 2956 
Q 1632 3110 1894 3110 
Q 2157 3110 2323 3046 
Q 2490 2982 2595 2892 
Q 2701 2803 2701 2713 
Q 2701 2624 2627 2509 
Q 2554 2394 2486 2394 
Q 2419 2394 2381 2458 
Q 2317 2579 2179 2636 
Q 2042 2694 1946 2694 
Q 1677 2694 1446 2528 
Q 1114 2285 1050 1702 
Q 1024 1472 1024 678 
L 1056 0 
Q 1056 -64 941 -64 
Q 826 -64 707 -16 
Q 589 32 589 115 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-3d" d="M 486 1197 
Q 678 1184 1011 1184 
L 2848 1184 
Q 3034 1184 3302 1203 
L 3309 1203 
Q 3379 1203 3417 1120 
Q 3456 1037 3456 918 
Q 3456 800 3360 800 
L 2842 813 
L 1005 813 
Q 634 813 518 794 
L 512 794 
Q 454 794 419 877 
Q 384 960 384 1043 
Q 384 1197 486 1197 
z
M 486 2400 
Q 678 2387 1011 2387 
L 2848 2387 
Q 3034 2387 3302 2406 
L 3309 2406 
Q 3379 2406 3417 2323 
Q 3456 2240 3456 2121 
Q 3456 2003 3360 2003 
L 2842 2016 
L 1005 2016 
Q 634 2016 518 1997 
L 512 1997 
Q 454 1997 419 2080 
Q 384 2163 384 2246 
Q 384 2400 486 2400 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2d" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-39" d="M 2963 3667 
Q 2886 3629 2874 3629 
Q 2810 3629 2774 3712 
Q 2739 3795 2650 3904 
Q 2432 4160 2035 4160 
Q 1536 4160 1165 3725 
Q 819 3302 819 2899 
Q 819 2080 1453 2080 
Q 2003 2080 2394 2502 
Q 2662 2790 2829 3213 
Q 2906 3405 2925 3504 
Q 2944 3603 2963 3667 
z
M 2413 -83 
Q 2246 -83 2246 58 
Q 2246 109 2310 416 
Q 2374 723 2448 1081 
Q 2522 1440 2570 1670 
Q 2618 1901 2643 2045 
Q 2669 2189 2688 2285 
Q 2464 2010 2131 1837 
Q 1798 1664 1481 1664 
Q 1165 1664 918 1801 
Q 672 1939 525 2201 
Q 378 2464 378 2810 
Q 378 3482 864 4019 
Q 1350 4557 2003 4557 
Q 2528 4557 2854 4250 
Q 2982 4122 3034 4026 
Q 3066 4166 3075 4268 
Q 3085 4371 3174 4371 
L 3187 4371 
Q 3322 4358 3424 4272 
Q 3526 4186 3526 4134 
L 3526 4115 
Q 3302 3552 2893 1139 
Q 2778 442 2733 58 
Q 2720 -70 2438 -83 
L 2413 -83 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2c" d="M 730 -666 
Q 646 -666 646 -576 
Q 646 -512 720 -464 
Q 794 -416 902 -285 
Q 1011 -154 1050 -51 
Q 954 -13 915 6 
Q 749 154 749 317 
Q 749 480 861 566 
Q 973 653 1117 653 
Q 1261 653 1360 531 
Q 1459 410 1478 243 
L 1478 192 
Q 1478 -90 1200 -378 
Q 922 -666 730 -666 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-70" d="M 1005 646 
Q 1030 659 1050 659 
Q 1120 659 1216 518 
Q 1389 262 1766 262 
Q 2144 262 2512 672 
Q 2880 1082 2880 1738 
Q 2880 2394 2534 2624 
Q 2387 2726 2169 2726 
Q 1952 2726 1712 2585 
Q 1472 2445 1267 2147 
Q 1062 1850 1024 1338 
Q 1005 1069 1005 646 
z
M 576 -922 
L 589 -352 
L 589 2355 
Q 589 2694 570 3021 
Q 570 3085 685 3085 
Q 800 3085 921 3037 
Q 1043 2989 1043 2906 
Q 1043 2867 1036 2764 
Q 1030 2662 1027 2550 
Q 1024 2438 1018 2362 
Q 1274 2797 1571 2953 
Q 1869 3110 2221 3110 
Q 2701 3110 3008 2771 
Q 3315 2432 3315 1837 
Q 3315 1242 3113 813 
Q 2912 384 2566 137 
Q 2221 -109 1786 -109 
Q 1562 -109 1338 0 
Q 1114 109 1011 243 
L 1011 -358 
L 1043 -1037 
Q 1043 -1101 928 -1101 
Q 813 -1101 694 -1053 
Q 576 -1005 576 -922 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2a" d="M 1805 4416 
L 1798 4326 
Q 1786 4186 1770 4026 
Q 1754 3866 1741 3699 
Q 2176 3898 2297 3974 
Q 2419 4051 2496 4051 
Q 2573 4051 2627 3945 
Q 2682 3840 2682 3738 
Q 2682 3610 2490 3571 
L 1837 3437 
Q 2118 3072 2208 2982 
L 2330 2854 
Q 2368 2822 2368 2768 
Q 2368 2714 2275 2614 
Q 2182 2515 2083 2515 
Q 1984 2515 1949 2569 
Q 1914 2624 1882 2701 
Q 1850 2778 1766 2934 
Q 1683 3091 1606 3251 
Q 1408 2918 1350 2764 
Q 1293 2611 1257 2556 
Q 1222 2502 1136 2502 
Q 1050 2502 944 2585 
Q 838 2669 838 2739 
Q 838 2810 909 2893 
Q 1190 3219 1338 3411 
Q 1005 3450 845 3459 
Q 685 3469 611 3488 
Q 538 3507 538 3616 
Q 538 3725 579 3837 
Q 621 3949 717 3949 
Q 749 3949 979 3853 
Q 1210 3757 1440 3680 
L 1331 4422 
Q 1331 4570 1574 4570 
L 1594 4570 
Q 1728 4570 1766 4525 
Q 1805 4480 1805 4416 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="955.199707"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1015.199692"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1065.199677"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_5">
    <path d="M 516.036957 519.965217 
L 879.95 519.965217 
L 879.95 158.4 
L 516.036957 158.4 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_2">
    <defs>
     <path id="m12fae17a6d" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #d95319; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#pc41158ffc2)">
     <use xlink:href="#m12fae17a6d" x="669.473647" y="352.508108" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="578.210188" y="503.530435" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="863.408498" y="370.275441" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="612.433985" y="396.926439" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="543.986391" y="343.624442" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="612.433985" y="325.857109" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="749.329174" y="396.926439" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="703.697444" y="325.857109" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="817.776768" y="281.438778" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="783.552971" y="361.391774" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="658.065715" y="263.671445" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="555.394323" y="432.461105" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="635.24985" y="290.322444" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="669.473647" y="254.787779" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="783.552971" y="467.99577" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="794.960904" y="237.020447" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="749.329174" y="254.787779" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="692.289512" y="281.438778" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="658.065715" y="219.253114" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="532.578458" y="450.228437" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="601.026053" y="388.042773" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="601.026053" y="396.926439" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="589.61812" y="334.740776" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="612.433985" y="388.042773" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="589.61812" y="352.508108" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="692.289512" y="308.089777" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="612.433985" y="361.391774" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="715.105377" y="370.275441" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="692.289512" y="174.834783" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="635.24985" y="201.485781" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="772.145039" y="388.042773" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#m12fae17a6d" x="646.657782" y="263.671445" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_2">
    <path d="M 532.578458 312.146337 
L 532.578458 410.713804 
L 535.920176 408.870981 
L 539.261894 407.042015 
L 542.603611 405.310216 
L 545.945329 403.384965 
L 549.287046 401.732294 
L 552.628764 400.517166 
L 555.970481 399.259056 
L 559.312199 398.09957 
L 562.653917 396.633112 
L 565.995634 394.89879 
L 569.337352 393.433083 
L 572.679069 391.213882 
L 576.020787 389.513341 
L 579.362504 387.868165 
L 582.704222 386.392316 
L 586.04594 385.353061 
L 589.387657 384.490447 
L 592.729375 383.380046 
L 596.071092 382.459679 
L 599.41281 381.425423 
L 602.754527 380.049323 
L 606.096245 378.396523 
L 609.437963 377.539355 
L 612.77968 376.377783 
L 616.121398 375.36844 
L 619.463115 374.580796 
L 622.804833 373.731402 
L 626.14655 372.685907 
L 629.488268 371.621812 
L 632.829986 370.557716 
L 636.171703 369.671094 
L 639.513421 369.022341 
L 642.855138 368.230413 
L 646.196856 367.25687 
L 649.538573 366.170755 
L 652.880291 365.454989 
L 656.222009 364.905764 
L 659.563726 364.21654 
L 662.905444 363.342343 
L 666.247161 362.630115 
L 669.588879 361.895146 
L 672.930596 361.376883 
L 676.272314 360.978514 
L 679.614032 360.514396 
L 682.955749 359.66233 
L 686.297467 359.23513 
L 689.639184 358.721253 
L 692.980902 358.264967 
L 696.322619 357.729191 
L 699.664337 357.202073 
L 703.006055 356.662048 
L 706.347772 356.422959 
L 709.68949 355.992105 
L 713.031207 355.385546 
L 716.372925 355.23435 
L 719.714642 354.711354 
L 723.05636 354.261677 
L 726.398078 354.086883 
L 729.739795 354.002708 
L 733.081513 353.714946 
L 736.42323 353.648328 
L 739.764948 353.721292 
L 743.106665 353.758785 
L 746.448383 353.783169 
L 749.790101 353.275659 
L 753.131818 353.219133 
L 756.473536 353.07115 
L 759.815253 353.324555 
L 763.156971 353.057475 
L 766.498688 352.993717 
L 769.840406 352.94551 
L 773.182124 353.192402 
L 776.523841 353.826672 
L 779.865559 353.941025 
L 783.207276 354.356433 
L 786.548994 354.771841 
L 789.890711 354.918892 
L 793.232429 354.994231 
L 796.574147 354.978793 
L 799.915864 354.882376 
L 803.257582 354.785958 
L 806.599299 355.180636 
L 809.941017 355.401174 
L 813.282734 355.493903 
L 816.624452 355.572069 
L 819.96617 356.00745 
L 823.307887 356.175353 
L 826.649605 356.634255 
L 829.991322 356.85913 
L 833.33304 356.949668 
L 836.674757 356.712269 
L 840.016475 357.109553 
L 843.358193 357.238509 
L 846.69991 357.347806 
L 850.041628 357.449123 
L 853.383345 357.600002 
L 856.725063 357.81466 
L 860.06678 358.041 
L 863.408498 358.267341 
L 863.408498 210.239363 
L 863.408498 210.239363 
L 860.06678 212.130046 
L 856.725063 214.159952 
L 853.383345 215.989754 
L 850.041628 217.819557 
L 846.69991 219.649359 
L 843.358193 221.479162 
L 840.016475 223.12843 
L 836.674757 224.768432 
L 833.33304 226.408433 
L 829.991322 228.198362 
L 826.649605 229.991158 
L 823.307887 231.793418 
L 819.96617 233.818798 
L 816.624452 235.865286 
L 813.282734 237.911773 
L 809.941017 239.777186 
L 806.599299 241.608882 
L 803.257582 243.447478 
L 799.915864 245.286075 
L 796.574147 247.287414 
L 793.232429 249.4271 
L 789.890711 251.050007 
L 786.548994 252.611445 
L 783.207276 254.434275 
L 779.865559 256.257106 
L 776.523841 257.705685 
L 773.182124 258.72922 
L 769.840406 260.518081 
L 766.498688 262.309754 
L 763.156971 264.107616 
L 759.815253 265.538075 
L 756.473536 267.408656 
L 753.131818 269.285824 
L 749.790101 271.036351 
L 746.448383 272.650487 
L 743.106665 274.690318 
L 739.764948 276.682959 
L 736.42323 278.483079 
L 733.081513 280.281294 
L 729.739795 282.049039 
L 726.398078 283.414009 
L 723.05636 284.763547 
L 719.714642 286.891197 
L 716.372925 288.190221 
L 713.031207 289.081735 
L 709.68949 291.069521 
L 706.347772 293.028104 
L 703.006055 293.669804 
L 699.664337 294.287667 
L 696.322619 295.84306 
L 692.980902 297.478482 
L 689.639184 298.591321 
L 686.297467 299.727639 
L 682.955749 300.797362 
L 679.614032 301.921639 
L 676.272314 303.294105 
L 672.930596 304.149796 
L 669.588879 304.813087 
L 666.247161 305.37358 
L 662.905444 306.169829 
L 659.563726 306.944756 
L 656.222009 308.922502 
L 652.880291 309.236758 
L 649.538573 309.44262 
L 646.196856 309.625577 
L 642.855138 310.451346 
L 639.513421 310.929207 
L 636.171703 311.051184 
L 632.829986 311.761652 
L 629.488268 312.798926 
L 626.14655 313.10682 
L 622.804833 313.38872 
L 619.463115 313.711665 
L 616.121398 314.014971 
L 612.77968 314.286652 
L 609.437963 314.175757 
L 606.096245 314.597174 
L 602.754527 314.294829 
L 599.41281 313.842265 
L 596.071092 313.605625 
L 592.729375 313.602106 
L 589.387657 313.604428 
L 586.04594 313.600101 
L 582.704222 313.597924 
L 579.362504 313.605068 
L 576.020787 313.447638 
L 572.679069 313.601906 
L 569.337352 313.588044 
L 565.995634 313.593861 
L 562.653917 314.020607 
L 559.312199 313.978815 
L 555.970481 313.704706 
L 552.628764 313.522747 
L 549.287046 313.291155 
L 545.945329 313.061935 
L 542.603611 312.832715 
L 539.261894 312.603495 
L 535.920176 312.374274 
L 532.578458 312.146337 
z
" clip-path="url(#pc41158ffc2)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_7">
     <g id="line2d_24">
      <path d="M 555.394323 519.965217 
L 555.394323 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_25">
      <g>
       <use xlink:href="#m402b369128" x="555.394323" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 30 -->
      <g transform="translate(549.394323 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_26">
      <path d="M 612.433985 519.965217 
L 612.433985 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_27">
      <g>
       <use xlink:href="#m402b369128" x="612.433985" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 35 -->
      <g transform="translate(606.433985 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_28">
      <path d="M 669.473647 519.965217 
L 669.473647 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_29">
      <g>
       <use xlink:href="#m402b369128" x="669.473647" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 40 -->
      <g transform="translate(663.473647 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_30">
      <path d="M 726.513309 519.965217 
L 726.513309 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_31">
      <g>
       <use xlink:href="#m402b369128" x="726.513309" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 45 -->
      <g transform="translate(720.513309 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_32">
      <path d="M 783.552971 519.965217 
L 783.552971 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_33">
      <g>
       <use xlink:href="#m402b369128" x="783.552971" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 50 -->
      <g transform="translate(777.552971 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_34">
      <path d="M 840.592633 519.965217 
L 840.592633 158.4 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_35">
      <g>
       <use xlink:href="#m402b369128" x="840.592633" y="519.965217" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 55 -->
      <g transform="translate(834.592633 534.03553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_21">
     <!-- 特质焦虑水平 -->
     <g transform="translate(667.993478 547.48553) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-7279"/>
      <use xlink:href="#LXGWWenKai-Regular-8d28" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-6c34" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5e73" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_6">
     <g id="line2d_36">
      <path d="M 516.036957 512.414101 
L 879.95 512.414101 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_37">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="512.414101" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- -10 -->
      <g transform="translate(493.536957 515.949257) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_38">
      <path d="M 516.036957 467.99577 
L 879.95 467.99577 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_39">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="467.99577" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- -5 -->
      <g transform="translate(499.536957 471.530926) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_40">
      <path d="M 516.036957 423.577438 
L 879.95 423.577438 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_41">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="423.577438" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 0 -->
      <g transform="translate(503.036957 427.112595) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_42">
      <path d="M 516.036957 379.159107 
L 879.95 379.159107 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_43">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="379.159107" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 5 -->
      <g transform="translate(503.036957 382.694263) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_44">
      <path d="M 516.036957 334.740776 
L 879.95 334.740776 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_45">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="334.740776" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 10 -->
      <g transform="translate(497.036957 338.275932) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_46">
      <path d="M 516.036957 290.322444 
L 879.95 290.322444 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_47">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="290.322444" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 15 -->
      <g transform="translate(497.036957 293.8576) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_48">
      <path d="M 516.036957 245.904113 
L 879.95 245.904113 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_49">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="245.904113" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 20 -->
      <g transform="translate(497.036957 249.439269) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_50">
      <path d="M 516.036957 201.485781 
L 879.95 201.485781 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_51">
      <g>
       <use xlink:href="#m1fe337ac85" x="516.036957" y="201.485781" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 25 -->
      <g transform="translate(497.036957 205.020938) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_30">
     <!-- 状态焦虑变化 -->
     <g transform="translate(487.816644 369.182609) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-72b6" d="M 4294 4653 
Q 4294 3642 4205 2886 
L 5293 2938 
Q 5427 2950 5507 2988 
Q 5587 3027 5635 3027 
Q 5683 3027 5763 2963 
Q 5843 2899 5900 2819 
Q 5958 2739 5958 2701 
Q 5958 2624 5805 2611 
L 4403 2534 
Q 5056 762 6266 -122 
Q 6330 -166 6330 -198 
Q 6330 -230 6272 -294 
Q 6125 -461 6010 -461 
Q 5965 -461 5926 -422 
Q 4678 621 4122 2291 
L 4045 1920 
Q 3859 1107 3366 448 
Q 2970 -90 2586 -346 
Q 2432 -448 2381 -448 
Q 2330 -448 2330 -397 
Q 2330 -346 2445 -224 
Q 3507 858 3770 2502 
L 3053 2464 
L 2906 2458 
Q 2739 2458 2675 2509 
Q 2490 2688 2490 2829 
Q 2490 2861 2509 2861 
Q 2547 2861 2630 2838 
Q 2714 2816 2816 2816 
L 3814 2867 
Q 3878 3443 3878 4403 
L 3878 4710 
Q 3878 4870 3820 4963 
Q 3763 5056 3763 5088 
Q 3763 5120 3840 5120 
Q 3917 5120 4067 5059 
Q 4218 4998 4256 4960 
Q 4294 4922 4294 4851 
L 4294 4653 
z
M 1837 1760 
Q 1402 1267 954 883 
Q 813 755 745 755 
Q 678 755 585 796 
Q 493 838 384 940 
Q 275 1043 275 1084 
Q 275 1126 320 1133 
Q 794 1203 1837 2074 
L 1875 4563 
Q 1875 4794 1814 4880 
Q 1754 4966 1754 4995 
Q 1754 5024 1830 5024 
Q 1907 5024 2054 4976 
Q 2202 4928 2240 4883 
Q 2278 4838 2278 4749 
L 2208 -416 
Q 2208 -595 2099 -595 
Q 2048 -595 1965 -556 
Q 1882 -518 1821 -451 
Q 1760 -384 1760 -326 
Q 1760 -269 1779 -176 
Q 1798 -83 1811 230 
L 1837 1760 
z
M 5056 3270 
Q 4838 3731 4563 4070 
Q 4531 4115 4531 4166 
Q 4531 4218 4630 4282 
Q 4730 4346 4774 4346 
Q 4819 4346 4902 4259 
Q 4986 4173 5078 4041 
Q 5171 3910 5254 3776 
Q 5338 3642 5392 3542 
Q 5446 3443 5446 3401 
Q 5446 3360 5356 3270 
Q 5267 3181 5180 3181 
Q 5094 3181 5056 3270 
z
M 1312 2432 
Q 1235 2432 1197 2534 
Q 941 3213 653 3686 
Q 627 3718 627 3766 
Q 627 3814 704 3872 
Q 781 3930 857 3930 
Q 934 3930 979 3853 
Q 1312 3328 1449 3001 
Q 1587 2675 1587 2627 
Q 1587 2579 1523 2531 
Q 1459 2483 1392 2457 
Q 1325 2432 1312 2432 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6001" d="M 3354 2125 
Q 3040 2522 2726 2771 
Q 2630 2854 2630 2908 
Q 2630 2963 2704 3030 
Q 2778 3098 2813 3098 
Q 2848 3098 3014 2982 
Q 3181 2867 3469 2592 
Q 3757 2317 3757 2246 
Q 3757 2176 3664 2083 
Q 3571 1990 3520 1990 
Q 3469 1990 3354 2125 
z
M 1075 288 
Q 973 45 893 -115 
Q 813 -275 742 -275 
Q 736 -275 666 -250 
Q 461 -173 461 -51 
Q 461 0 573 182 
Q 685 365 829 707 
Q 973 1050 1062 1389 
Q 1094 1498 1171 1498 
Q 1222 1498 1324 1466 
Q 1427 1434 1427 1357 
Q 1427 1158 1075 288 
z
M 4352 1030 
Q 4429 1030 4518 858 
Q 4698 480 5011 45 
Q 5120 -115 5120 -166 
Q 5120 -218 5075 -288 
Q 4960 -474 4422 -474 
Q 3264 -474 2573 -77 
Q 2138 173 1920 794 
Q 1830 1043 1798 1219 
Q 1766 1395 1766 1408 
Q 1766 1549 2010 1549 
Q 2131 1549 2150 1408 
Q 2221 1037 2352 745 
Q 2483 454 2780 281 
Q 3078 109 3465 22 
Q 3853 -64 4214 -64 
Q 4576 -64 4576 -25 
Q 4576 13 4435 413 
Q 4294 813 4294 921 
Q 4294 1030 4352 1030 
z
M 4800 1376 
Q 4717 1446 4717 1504 
Q 4717 1562 4787 1635 
Q 4858 1709 4906 1709 
Q 4954 1709 5082 1613 
Q 5210 1517 5379 1369 
Q 5549 1222 5705 1065 
Q 5862 909 5968 784 
Q 6074 659 6074 614 
Q 6074 570 6029 509 
Q 5984 448 5926 403 
Q 5869 358 5824 358 
Q 5779 358 5516 668 
Q 5254 979 4800 1376 
z
M 3539 704 
Q 3174 1139 2816 1440 
Q 2720 1517 2720 1574 
Q 2720 1632 2781 1702 
Q 2842 1773 2883 1773 
Q 2925 1773 3097 1657 
Q 3270 1542 3577 1241 
Q 3885 941 3885 854 
Q 3885 768 3801 694 
Q 3718 621 3667 621 
Q 3616 621 3539 704 
z
M 5152 4096 
L 5299 4141 
Q 5421 4141 5581 3962 
Q 5638 3891 5638 3834 
Q 5638 3750 5504 3738 
L 3891 3642 
Q 4352 3168 4902 2745 
Q 5453 2323 6246 1933 
Q 6355 1875 6355 1830 
Q 6355 1786 6278 1725 
Q 6202 1664 6115 1619 
Q 6029 1574 5984 1574 
Q 5939 1574 5901 1600 
Q 5062 2048 4476 2547 
Q 3891 3046 3373 3610 
L 2778 3571 
Q 2298 2816 1613 2214 
Q 1082 1741 563 1440 
Q 365 1325 320 1325 
Q 275 1325 275 1382 
Q 275 1440 403 1542 
Q 1626 2528 2266 3546 
L 1248 3482 
L 1133 3475 
Q 979 3475 921 3532 
Q 864 3590 813 3696 
Q 762 3802 762 3840 
Q 762 3878 787 3878 
Q 813 3878 867 3859 
Q 922 3840 1062 3840 
L 1114 3840 
L 2483 3923 
L 2586 4115 
Q 2797 4550 2870 4816 
Q 2944 5082 2944 5107 
L 2925 5248 
Q 2925 5325 2995 5325 
Q 3142 5325 3366 5210 
Q 3462 5158 3462 5081 
Q 3462 5005 3331 4685 
Q 3200 4365 3078 4122 
L 2989 3955 
L 5018 4077 
Q 5094 4083 5152 4096 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-53d8" d="M 2842 710 
Q 1952 1357 1952 1504 
Q 1952 1568 2035 1635 
Q 2118 1702 2172 1702 
Q 2227 1702 2304 1626 
Q 2714 1254 3174 947 
Q 3706 1382 4077 1869 
L 2010 1754 
Q 1965 1747 1926 1747 
L 1850 1747 
Q 1696 1747 1629 1824 
Q 1562 1901 1523 1993 
Q 1485 2086 1485 2115 
Q 1485 2144 1510 2144 
Q 1536 2144 1609 2128 
Q 1683 2112 1843 2112 
L 1939 2112 
L 4275 2234 
L 4371 2240 
Q 4461 2240 4573 2157 
Q 4685 2074 4685 2000 
Q 4685 1926 4627 1888 
Q 4570 1850 4544 1818 
Q 4070 1190 3507 736 
Q 4576 115 5830 -128 
Q 5965 -154 5965 -198 
L 5914 -282 
Q 5747 -531 5600 -531 
L 5382 -467 
Q 4762 -301 4173 -38 
Q 3584 224 3168 486 
Q 2368 -64 1402 -346 
Q 1024 -454 800 -492 
Q 576 -531 570 -531 
Q 454 -531 454 -486 
Q 454 -416 678 -333 
Q 1280 -109 1821 134 
Q 2362 378 2842 710 
z
M 5376 2586 
Q 4941 3002 4371 3392 
Q 4288 3450 4288 3491 
Q 4288 3533 4349 3625 
Q 4410 3718 4464 3718 
Q 4518 3718 4768 3564 
Q 5018 3411 5414 3094 
Q 5811 2778 5811 2688 
Q 5811 2630 5731 2534 
Q 5651 2438 5590 2438 
Q 5530 2438 5376 2586 
z
M 2003 3552 
Q 2074 3469 2074 3424 
Q 2074 3322 1626 2922 
Q 1178 2522 794 2330 
Q 678 2272 627 2272 
Q 576 2272 576 2304 
Q 576 2355 698 2458 
Q 1587 3245 1638 3648 
Q 1645 3750 1709 3750 
Q 1811 3750 2003 3552 
z
M 2931 5171 
Q 3398 5171 3398 5018 
L 3398 4390 
L 5094 4493 
Q 5306 4512 5370 4531 
Q 5434 4550 5485 4550 
Q 5536 4550 5626 4499 
Q 5837 4371 5837 4268 
Q 5837 4166 5728 4154 
L 4000 4058 
L 3930 4051 
L 3930 2573 
Q 3930 2381 3808 2381 
Q 3750 2381 3651 2441 
Q 3552 2502 3504 2550 
Q 3456 2598 3456 2669 
Q 3507 3021 3507 3238 
L 3507 4026 
L 2938 3994 
L 2803 3987 
L 2803 2547 
Q 2803 2355 2682 2355 
Q 2624 2355 2525 2416 
Q 2426 2477 2378 2525 
Q 2330 2573 2330 2643 
Q 2381 2893 2381 3213 
L 2381 3968 
L 1216 3898 
Q 1088 3885 960 3885 
Q 832 3885 761 3955 
Q 691 4026 646 4118 
Q 602 4211 602 4246 
Q 602 4282 646 4282 
Q 832 4250 986 4250 
L 1069 4250 
L 2976 4365 
L 2970 4800 
Q 2970 4960 2918 5027 
Q 2867 5094 2867 5132 
Q 2867 5171 2931 5171 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5316" d="M 1466 2912 
Q 1146 2496 870 2240 
Q 378 1773 262 1773 
Q 224 1773 224 1817 
Q 224 1862 377 2044 
Q 531 2227 886 2707 
Q 1242 3187 1670 3907 
Q 2099 4627 2099 4851 
L 2099 4902 
Q 2099 4922 2093 4947 
L 2093 4966 
Q 2093 5030 2160 5030 
Q 2227 5030 2330 4979 
Q 2579 4838 2579 4736 
Q 2579 4659 2323 4198 
Q 2067 3738 1882 3450 
L 1875 -390 
Q 1875 -525 1747 -525 
Q 1734 -525 1644 -502 
Q 1555 -480 1472 -416 
Q 1389 -352 1389 -278 
Q 1389 -205 1411 -112 
Q 1434 -19 1434 211 
L 1466 2912 
z
M 3309 1811 
Q 2470 1344 2272 1344 
Q 2208 1344 2208 1382 
Q 2208 1446 2419 1574 
Q 2963 1901 3315 2163 
L 3328 4595 
Q 3328 4749 3277 4832 
Q 3226 4915 3226 4944 
Q 3226 4973 3302 4973 
Q 3379 4973 3533 4928 
Q 3763 4877 3763 4742 
L 3750 2496 
Q 4410 3040 4915 3648 
Q 4992 3725 5011 3865 
Q 5030 4006 5068 4006 
Q 5107 4006 5184 3936 
Q 5414 3738 5414 3578 
Q 5414 3533 5389 3507 
Q 4640 2650 3744 2080 
L 3738 544 
Q 3738 326 3795 256 
Q 3853 186 4019 154 
Q 4346 102 4624 102 
Q 4902 102 5350 166 
Q 5466 186 5542 282 
Q 5683 461 5805 1306 
Q 5824 1453 5862 1523 
Q 5901 1594 5933 1594 
Q 6054 1594 6054 960 
Q 6054 634 6029 371 
Q 5971 -179 5491 -230 
Q 4998 -288 4537 -288 
Q 4077 -288 3789 -221 
Q 3501 -154 3401 6 
Q 3302 166 3302 454 
L 3309 1811 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-53d8" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5316" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_52">
    <path d="M 532.578458 363.558719 
L 535.920176 362.875581 
L 539.261894 362.192443 
L 542.603611 361.509305 
L 545.945329 360.826167 
L 549.287046 360.143029 
L 552.628764 359.459892 
L 555.970481 358.776754 
L 559.312199 358.093616 
L 562.653917 357.410478 
L 565.995634 356.72734 
L 569.337352 356.044202 
L 572.679069 355.361064 
L 576.020787 354.677926 
L 579.362504 353.994788 
L 582.704222 353.31165 
L 586.04594 352.628512 
L 589.387657 351.945374 
L 592.729375 351.262236 
L 596.071092 350.579098 
L 599.41281 349.89596 
L 602.754527 349.212822 
L 606.096245 348.529684 
L 609.437963 347.846546 
L 612.77968 347.163408 
L 616.121398 346.48027 
L 619.463115 345.797132 
L 622.804833 345.113994 
L 626.14655 344.430856 
L 629.488268 343.747718 
L 632.829986 343.06458 
L 636.171703 342.381442 
L 639.513421 341.698304 
L 642.855138 341.015167 
L 646.196856 340.332029 
L 649.538573 339.648891 
L 652.880291 338.965753 
L 656.222009 338.282615 
L 659.563726 337.599477 
L 662.905444 336.916339 
L 666.247161 336.233201 
L 669.588879 335.550063 
L 672.930596 334.866925 
L 676.272314 334.183787 
L 679.614032 333.500649 
L 682.955749 332.817511 
L 686.297467 332.134373 
L 689.639184 331.451235 
L 692.980902 330.768097 
L 696.322619 330.084959 
L 699.664337 329.401821 
L 703.006055 328.718683 
L 706.347772 328.035545 
L 709.68949 327.352407 
L 713.031207 326.669269 
L 716.372925 325.986131 
L 719.714642 325.302993 
L 723.05636 324.619855 
L 726.398078 323.936717 
L 729.739795 323.253579 
L 733.081513 322.570441 
L 736.42323 321.887304 
L 739.764948 321.204166 
L 743.106665 320.521028 
L 746.448383 319.83789 
L 749.790101 319.154752 
L 753.131818 318.471614 
L 756.473536 317.788476 
L 759.815253 317.105338 
L 763.156971 316.4222 
L 766.498688 315.739062 
L 769.840406 315.055924 
L 773.182124 314.372786 
L 776.523841 313.689648 
L 779.865559 313.00651 
L 783.207276 312.323372 
L 786.548994 311.640234 
L 789.890711 310.957096 
L 793.232429 310.273958 
L 796.574147 309.59082 
L 799.915864 308.907682 
L 803.257582 308.224544 
L 806.599299 307.541406 
L 809.941017 306.858268 
L 813.282734 306.17513 
L 816.624452 305.491992 
L 819.96617 304.808854 
L 823.307887 304.125716 
L 826.649605 303.442578 
L 829.991322 302.759441 
L 833.33304 302.076303 
L 836.674757 301.393165 
L 840.016475 300.710027 
L 843.358193 300.026889 
L 846.69991 299.343751 
L 850.041628 298.660613 
L 853.383345 297.977475 
L 856.725063 297.294337 
L 860.06678 296.611199 
L 863.408498 295.928061 
" clip-path="url(#pc41158ffc2)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 516.036957 519.965217 
L 516.036957 158.4 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 516.036957 519.965217 
L 879.95 519.965217 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_31">
    <!-- r = 0.221, p = 0.224 -->
    <g transform="translate(639.181291 152.4) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="232.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="327.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="387.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="447.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="575.199829"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="670.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="765.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="860.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="920.199722"/>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_8">
    <path d="M 42.95 990 
L 406.863043 990 
L 406.863043 628.434783 
L 42.95 628.434783 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m0d1dcbffb4" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #edb120; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p78671c6108)">
     <use xlink:href="#m0d1dcbffb4" x="59.491502" y="861.675252" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="297.565456" y="973.565217" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="59.491502" y="874.838777" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="319.208542" y="894.584065" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="377.954063" y="855.093489" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="328.484151" y="841.929964" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="300.657325" y="894.584065" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="322.300412" y="841.929964" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="279.014238" y="809.02115" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="285.197978" y="868.257014" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="343.943499" y="795.857625" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="353.219107" y="920.911116" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="331.57602" y="815.602913" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="294.473586" y="789.275862" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="303.749195" y="947.238167" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="322.300412" y="776.112337" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="300.657325" y="789.275862" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="387.229672" y="809.02115" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="328.484151" y="762.948812" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="384.137802" y="934.074641" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="374.862194" y="888.002303" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="340.851629" y="894.584065" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="359.402846" y="848.511726" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="390.321542" y="888.002303" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="337.75976" y="861.675252" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="353.219107" y="828.766438" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="340.851629" y="868.257014" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="343.943499" y="874.838777" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="313.024803" y="730.039998" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="285.197978" y="749.785286" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="309.932934" y="888.002303" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m0d1dcbffb4" x="325.392281" y="795.857625" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_3">
    <path d="M 59.491502 644.869565 
L 59.491502 887.008444 
L 62.83322 886.697167 
L 66.174937 886.38589 
L 69.516655 886.074613 
L 72.858372 885.763336 
L 76.20009 885.452059 
L 79.541807 885.100503 
L 82.883525 884.739912 
L 86.225243 884.43404 
L 89.56696 884.184181 
L 92.908678 883.902756 
L 96.250395 883.687756 
L 99.592113 883.44136 
L 102.93383 883.194963 
L 106.275548 882.948567 
L 109.617266 882.700814 
L 112.958983 882.450114 
L 116.300701 882.199415 
L 119.642418 881.904781 
L 122.984136 881.413957 
L 126.325853 881.137275 
L 129.667571 880.924646 
L 133.009289 880.587269 
L 136.351006 880.244384 
L 139.692724 879.899402 
L 143.034441 879.55442 
L 146.376159 879.209439 
L 149.717876 878.864457 
L 153.059594 878.519476 
L 156.401312 878.174494 
L 159.743029 877.845509 
L 163.084747 877.494257 
L 166.426464 877.147136 
L 169.768182 876.809014 
L 173.109899 876.527556 
L 176.451617 876.253436 
L 179.793335 876.229717 
L 183.135052 876.049382 
L 186.47677 875.869046 
L 189.818487 875.682901 
L 193.160205 875.492582 
L 196.501922 875.302264 
L 199.84364 875.09463 
L 203.185358 874.751575 
L 206.527075 874.263596 
L 209.868793 873.796362 
L 213.21051 873.414711 
L 216.552228 873.033061 
L 219.893945 872.651411 
L 223.235663 872.483989 
L 226.577381 872.049672 
L 229.919098 871.740561 
L 233.260816 871.558769 
L 236.602533 871.568911 
L 239.944251 871.604219 
L 243.285968 871.545122 
L 246.627686 871.410912 
L 249.969404 871.269146 
L 253.311121 871.346696 
L 256.652839 871.39693 
L 259.994556 871.464828 
L 263.336274 871.277511 
L 266.677991 871.054399 
L 270.019709 870.785573 
L 273.361427 870.538829 
L 276.703144 870.286893 
L 280.044862 870.137349 
L 283.386579 869.7654 
L 286.728297 869.791414 
L 290.070014 869.769832 
L 293.411732 869.481739 
L 296.75345 869.309076 
L 300.095167 868.91043 
L 303.436885 868.660396 
L 306.778602 868.533949 
L 310.12032 868.907436 
L 313.462037 868.820001 
L 316.803755 868.640012 
L 320.145473 869.091601 
L 323.48719 869.060029 
L 326.828908 869.037319 
L 330.170625 869.004341 
L 333.512343 868.997251 
L 336.85406 869.105987 
L 340.195778 870.149755 
L 343.537496 870.038575 
L 346.879213 870.569965 
L 350.220931 871.766493 
L 353.562648 872.567885 
L 356.904366 873.452176 
L 360.246083 874.425604 
L 363.587801 874.780581 
L 366.929519 876.068439 
L 370.271236 877.330083 
L 373.612954 878.985296 
L 376.954671 880.282393 
L 380.296389 883.078231 
L 383.638106 884.452827 
L 386.979824 886.407591 
L 390.321542 888.992442 
L 390.321542 826.143844 
L 390.321542 826.143844 
L 386.979824 826.413903 
L 383.638106 826.711026 
L 380.296389 827.008149 
L 376.954671 827.308061 
L 373.612954 827.649429 
L 370.271236 827.903157 
L 366.929519 828.046331 
L 363.587801 828.193266 
L 360.246083 828.206912 
L 356.904366 828.392329 
L 353.562648 828.591586 
L 350.220931 828.845982 
L 346.879213 828.798348 
L 343.537496 829.124225 
L 340.195778 828.816712 
L 336.85406 828.564481 
L 333.512343 828.203608 
L 330.170625 828.161703 
L 326.828908 827.845375 
L 323.48719 827.268943 
L 320.145473 825.947388 
L 316.803755 824.734238 
L 313.462037 824.175955 
L 310.12032 822.687071 
L 306.778602 820.732793 
L 303.436885 818.801065 
L 300.095167 816.280169 
L 296.75345 813.76815 
L 293.411732 812.234832 
L 290.070014 810.84454 
L 286.728297 807.72478 
L 283.386579 804.606436 
L 280.044862 802.072199 
L 276.703144 799.85735 
L 273.361427 797.649286 
L 270.019709 795.441221 
L 266.677991 793.246259 
L 263.336274 791.053032 
L 259.994556 788.859806 
L 256.652839 786.666579 
L 253.311121 784.473353 
L 249.969404 782.280126 
L 246.627686 780.140202 
L 243.285968 777.883591 
L 239.944251 775.66661 
L 236.602533 773.449629 
L 233.260816 771.232648 
L 229.919098 769.015667 
L 226.577381 766.798686 
L 223.235663 764.579507 
L 219.893945 762.232853 
L 216.552228 759.656567 
L 213.21051 757.02628 
L 209.868793 754.400679 
L 206.527075 751.783232 
L 203.185358 749.165784 
L 199.84364 746.548336 
L 196.501922 743.930888 
L 193.160205 741.31344 
L 189.818487 738.695992 
L 186.47677 736.078545 
L 183.135052 733.461097 
L 179.793335 730.843649 
L 176.451617 728.226201 
L 173.109899 725.608753 
L 169.768182 722.991305 
L 166.426464 720.373857 
L 163.084747 717.75641 
L 159.743029 715.138962 
L 156.401312 712.521514 
L 153.059594 709.904066 
L 149.717876 707.286618 
L 146.376159 704.66917 
L 143.034441 702.188381 
L 139.692724 699.895629 
L 136.351006 697.602876 
L 133.009289 695.310123 
L 129.667571 693.017371 
L 126.325853 690.724618 
L 122.984136 688.431865 
L 119.642418 686.139113 
L 116.300701 683.84636 
L 112.958983 681.553607 
L 109.617266 679.260855 
L 106.275548 676.968102 
L 102.93383 674.67535 
L 99.592113 672.382597 
L 96.250395 670.089844 
L 92.908678 667.797092 
L 89.56696 665.504339 
L 86.225243 663.211586 
L 82.883525 660.918834 
L 79.541807 658.626081 
L 76.20009 656.333328 
L 72.858372 654.040576 
L 69.516655 651.747823 
L 66.174937 649.45507 
L 62.83322 647.162318 
L 59.491502 644.869565 
z
" clip-path="url(#p78671c6108)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_13">
     <g id="line2d_53">
      <path d="M 59.491502 990 
L 59.491502 628.434783 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m402b369128" x="59.491502" y="990" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 0 -->
      <g transform="translate(56.491502 1004.070312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_55">
      <path d="M 136.78824 990 
L 136.78824 628.434783 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m402b369128" x="136.78824" y="990" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 1 -->
      <g transform="translate(133.78824 1004.070312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_57">
      <path d="M 214.084978 990 
L 214.084978 628.434783 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m402b369128" x="214.084978" y="990" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 2 -->
      <g transform="translate(211.084978 1004.070312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_59">
      <path d="M 291.381717 990 
L 291.381717 628.434783 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m402b369128" x="291.381717" y="990" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 3 -->
      <g transform="translate(288.381717 1004.070312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_61">
      <path d="M 368.678455 990 
L 368.678455 628.434783 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m402b369128" x="368.678455" y="990" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 4 -->
      <g transform="translate(365.678455 1004.070312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_37">
     <!-- 心理韧性 -->
     <g transform="translate(204.906522 1017.1) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 42.95 980.14698 
L 406.863043 980.14698 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="980.14698" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_38">
      <!-- -10 -->
      <g transform="translate(20.45 983.682136) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 42.95 914.329353 
L 406.863043 914.329353 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="914.329353" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0 -->
      <g transform="translate(29.95 917.86451) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 42.95 848.511726 
L 406.863043 848.511726 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="848.511726" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 10 -->
      <g transform="translate(23.95 852.046883) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 42.95 782.6941 
L 406.863043 782.6941 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="782.6941" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 20 -->
      <g transform="translate(23.95 786.229256) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 42.95 716.876473 
L 406.863043 716.876473 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="716.876473" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 30 -->
      <g transform="translate(23.95 720.411629) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_73">
      <path d="M 42.95 651.058846 
L 406.863043 651.058846 
" clip-path="url(#p78671c6108)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_74">
      <g>
       <use xlink:href="#m1fe337ac85" x="42.95" y="651.058846" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 40 -->
      <g transform="translate(23.95 654.594002) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- 状态焦虑变化 -->
     <g transform="translate(14.729688 839.217391) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-53d8" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5316" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_75">
    <path d="M 59.491502 847.306149 
L 62.83322 847.330063 
L 66.174937 847.353976 
L 69.516655 847.37789 
L 72.858372 847.401804 
L 76.20009 847.425718 
L 79.541807 847.449632 
L 82.883525 847.473546 
L 86.225243 847.497459 
L 89.56696 847.521373 
L 92.908678 847.545287 
L 96.250395 847.569201 
L 99.592113 847.593115 
L 102.93383 847.617028 
L 106.275548 847.640942 
L 109.617266 847.664856 
L 112.958983 847.68877 
L 116.300701 847.712684 
L 119.642418 847.736597 
L 122.984136 847.760511 
L 126.325853 847.784425 
L 129.667571 847.808339 
L 133.009289 847.832253 
L 136.351006 847.856166 
L 139.692724 847.88008 
L 143.034441 847.903994 
L 146.376159 847.927908 
L 149.717876 847.951822 
L 153.059594 847.975736 
L 156.401312 847.999649 
L 159.743029 848.023563 
L 163.084747 848.047477 
L 166.426464 848.071391 
L 169.768182 848.095305 
L 173.109899 848.119218 
L 176.451617 848.143132 
L 179.793335 848.167046 
L 183.135052 848.19096 
L 186.47677 848.214874 
L 189.818487 848.238787 
L 193.160205 848.262701 
L 196.501922 848.286615 
L 199.84364 848.310529 
L 203.185358 848.334443 
L 206.527075 848.358356 
L 209.868793 848.38227 
L 213.21051 848.406184 
L 216.552228 848.430098 
L 219.893945 848.454012 
L 223.235663 848.477926 
L 226.577381 848.501839 
L 229.919098 848.525753 
L 233.260816 848.549667 
L 236.602533 848.573581 
L 239.944251 848.597495 
L 243.285968 848.621408 
L 246.627686 848.645322 
L 249.969404 848.669236 
L 253.311121 848.69315 
L 256.652839 848.717064 
L 259.994556 848.740977 
L 263.336274 848.764891 
L 266.677991 848.788805 
L 270.019709 848.812719 
L 273.361427 848.836633 
L 276.703144 848.860546 
L 280.044862 848.88446 
L 283.386579 848.908374 
L 286.728297 848.932288 
L 290.070014 848.956202 
L 293.411732 848.980116 
L 296.75345 849.004029 
L 300.095167 849.027943 
L 303.436885 849.051857 
L 306.778602 849.075771 
L 310.12032 849.099685 
L 313.462037 849.123598 
L 316.803755 849.147512 
L 320.145473 849.171426 
L 323.48719 849.19534 
L 326.828908 849.219254 
L 330.170625 849.243167 
L 333.512343 849.267081 
L 336.85406 849.290995 
L 340.195778 849.314909 
L 343.537496 849.338823 
L 346.879213 849.362737 
L 350.220931 849.38665 
L 353.562648 849.410564 
L 356.904366 849.434478 
L 360.246083 849.458392 
L 363.587801 849.482306 
L 366.929519 849.506219 
L 370.271236 849.530133 
L 373.612954 849.554047 
L 376.954671 849.577961 
L 380.296389 849.601875 
L 383.638106 849.625788 
L 386.979824 849.649702 
L 390.321542 849.673616 
" clip-path="url(#p78671c6108)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 42.95 990 
L 42.95 628.434783 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 42.95 990 
L 406.863043 990 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <!-- r = -0.009, p = 0.961 -->
    <g transform="translate(163.994334 622.434783) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-36" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="text_46">
   <!-- 三变量相关性分析 -->
   <g transform="translate(391.95 17.713125) scale(0.14 -0.14)">
    <defs>
     <path id="LXGWWenKai-Regular-4e09" d="M 1613 3808 
L 1414 3802 
Q 1267 3802 1200 3862 
Q 1133 3923 1088 4054 
Q 1043 4186 1043 4192 
Q 1043 4237 1088 4237 
Q 1107 4237 1168 4217 
Q 1229 4198 1408 4198 
L 1491 4198 
L 4570 4378 
Q 4704 4390 4787 4419 
Q 4870 4448 4918 4448 
Q 4966 4448 5052 4381 
Q 5139 4314 5203 4234 
Q 5267 4154 5267 4109 
Q 5267 4013 5094 4000 
L 1613 3808 
z
M 2054 2093 
L 1869 2086 
Q 1728 2086 1670 2131 
Q 1626 2176 1555 2288 
Q 1485 2400 1485 2470 
Q 1485 2541 1530 2541 
Q 1549 2541 1625 2518 
Q 1702 2496 1869 2496 
L 1933 2496 
L 4275 2611 
Q 4410 2624 4493 2653 
Q 4576 2682 4617 2682 
Q 4659 2682 4748 2618 
Q 4838 2554 4905 2470 
Q 4973 2387 4973 2330 
Q 4973 2234 4800 2221 
L 2054 2093 
z
M 730 518 
L 5293 672 
Q 5395 678 5523 707 
Q 5651 736 5692 736 
Q 5734 736 5830 678 
Q 5926 621 6003 537 
Q 6080 454 6080 390 
Q 6080 275 5914 262 
L 1005 96 
L 832 90 
Q 653 90 569 157 
Q 486 224 428 339 
Q 371 454 371 505 
Q 371 557 406 557 
Q 442 557 512 537 
Q 582 518 730 518 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-91cf" d="M 2080 3302 
Q 2080 3187 1952 3187 
Q 1888 3187 1776 3235 
Q 1664 3283 1664 3398 
L 1664 3437 
Q 1670 3469 1670 3507 
L 1670 3565 
Q 1670 3654 1658 3770 
L 1574 4550 
Q 1555 4698 1497 4797 
Q 1440 4896 1440 4934 
Q 1440 4973 1523 4973 
Q 1606 4973 1933 4851 
L 4563 4998 
L 4614 4998 
Q 4762 4998 4835 4934 
Q 4909 4870 4909 4828 
Q 4909 4787 4896 4761 
Q 4883 4736 4877 4717 
L 4730 3763 
Q 4870 3603 4870 3545 
Q 4870 3488 4822 3481 
Q 4774 3475 4704 3469 
L 2080 3347 
L 2080 3302 
z
M 4461 4698 
L 1952 4550 
L 1984 4250 
L 4422 4378 
L 4461 4698 
z
M 4390 4102 
L 2010 3974 
L 2048 3635 
L 4346 3750 
L 4390 4102 
z
M 1024 2682 
L 883 2675 
Q 806 2675 704 2720 
Q 602 2765 538 2982 
Q 531 2995 531 3017 
Q 531 3040 556 3040 
Q 582 3040 652 3024 
Q 723 3008 851 3008 
L 934 3008 
L 5318 3219 
Q 5472 3226 5549 3248 
Q 5626 3270 5645 3270 
Q 5664 3270 5731 3241 
Q 5798 3213 5868 3155 
Q 5939 3098 5939 3021 
Q 5939 2925 5830 2912 
L 1024 2682 
z
M 595 -51 
Q 774 -96 992 -96 
L 2982 -51 
L 2982 326 
L 1914 294 
L 1779 288 
Q 1638 288 1564 336 
Q 1491 384 1421 582 
L 1414 621 
Q 1414 640 1430 640 
Q 1446 640 1510 621 
Q 1574 602 1747 602 
L 1837 602 
L 2989 634 
L 2989 979 
L 1978 941 
L 1984 896 
L 1984 877 
Q 1984 781 1865 781 
Q 1747 781 1667 832 
Q 1587 883 1587 973 
L 1587 1005 
L 1594 1120 
Q 1594 1203 1581 1306 
L 1510 2170 
Q 1498 2323 1427 2451 
Q 1414 2490 1414 2502 
Q 1414 2547 1497 2547 
Q 1581 2547 1882 2445 
L 4666 2586 
L 4736 2586 
Q 4806 2586 4905 2538 
Q 5005 2490 5005 2387 
Q 5005 2349 4989 2317 
Q 4973 2285 4966 2259 
L 4819 1325 
Q 4954 1171 4954 1113 
Q 4954 1056 4806 1043 
L 3360 992 
L 3360 640 
L 4429 672 
Q 4582 685 4681 707 
Q 4781 730 4806 730 
Q 4832 730 4902 698 
Q 5088 608 5088 505 
Q 5088 403 4973 390 
L 3354 339 
L 3354 -45 
L 5293 0 
Q 5459 0 5548 29 
Q 5638 58 5673 58 
Q 5709 58 5786 13 
Q 5984 -96 5984 -211 
Q 5984 -326 5850 -326 
L 1011 -422 
L 947 -422 
Q 826 -422 730 -374 
Q 634 -326 576 -109 
Q 570 -96 570 -73 
Q 570 -51 595 -51 
z
M 4570 2278 
L 3366 2221 
L 3366 1907 
L 4531 1965 
L 4570 2278 
z
M 2995 2208 
L 1882 2157 
L 1907 1843 
L 2995 1894 
L 2995 2208 
z
M 4499 1683 
L 3360 1632 
L 3360 1274 
L 4461 1312 
L 4499 1683 
z
M 2989 1613 
L 1926 1568 
L 1958 1222 
L 2989 1261 
L 2989 1613 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-76f8" d="M 819 3347 
L 1722 3424 
L 1734 4621 
Q 1734 4794 1676 4874 
Q 1619 4954 1619 4986 
Q 1619 5037 1702 5037 
L 1766 5030 
Q 2131 4960 2131 4781 
L 2112 3462 
L 2483 3494 
Q 2605 3507 2669 3529 
Q 2733 3552 2771 3552 
Q 2810 3552 2886 3494 
Q 3091 3354 3091 3258 
Q 3091 3187 2944 3174 
L 2112 3104 
L 2106 2566 
Q 2189 2630 2240 2630 
Q 2291 2630 2438 2518 
Q 2586 2406 2822 2150 
Q 3059 1894 3059 1830 
Q 3059 1766 2979 1686 
Q 2899 1606 2832 1606 
Q 2765 1606 2675 1728 
Q 2438 2042 2099 2355 
L 2067 -474 
Q 2067 -640 1958 -640 
Q 1818 -640 1718 -547 
Q 1619 -454 1619 -390 
Q 1619 -326 1651 -192 
Q 1683 -58 1683 166 
L 1690 346 
Q 1690 1830 1728 2368 
L 1728 2458 
Q 1523 1843 973 1197 
Q 749 934 566 780 
Q 384 627 339 627 
Q 294 627 294 675 
Q 294 723 371 826 
Q 1101 1754 1632 3059 
L 1024 3008 
Q 934 2995 858 2995 
Q 698 2995 598 3126 
Q 499 3258 499 3325 
Q 499 3392 531 3392 
Q 614 3379 684 3363 
Q 755 3347 819 3347 
z
M 5696 4013 
L 5651 3846 
L 5562 410 
Q 5734 237 5734 160 
Q 5734 83 5683 57 
Q 5632 32 5562 26 
L 3680 -38 
L 3686 -339 
Q 3686 -474 3584 -474 
L 3494 -448 
Q 3245 -390 3245 -224 
Q 3245 -186 3264 -93 
Q 3283 0 3283 275 
L 3219 3757 
Q 3219 3968 3177 4073 
Q 3136 4179 3136 4218 
Q 3136 4282 3235 4282 
Q 3334 4282 3629 4141 
L 5306 4250 
L 5414 4256 
Q 5606 4256 5670 4090 
Q 5696 4032 5696 4013 
z
M 5235 3891 
L 3616 3789 
L 3629 2944 
L 5216 3034 
L 5235 3891 
z
M 5210 2682 
L 3635 2598 
L 3654 1677 
L 5190 1760 
L 5210 2682 
z
M 5184 1402 
L 3661 1331 
L 3674 333 
L 5165 384 
L 5184 1402 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5173" d="M 1862 4723 
Q 1760 4826 1926 4947 
Q 2029 5018 2083 5002 
Q 2138 4986 2301 4800 
Q 2464 4614 2672 4281 
Q 2880 3949 2854 3865 
Q 2829 3782 2720 3731 
Q 2611 3680 2556 3696 
Q 2502 3712 2374 3981 
Q 2246 4250 1862 4723 
z
M 1120 1818 
Q 1005 1818 902 1850 
Q 762 1933 685 2125 
Q 672 2163 672 2176 
Q 672 2234 736 2234 
Q 762 2234 838 2214 
Q 915 2195 1069 2195 
L 1133 2195 
L 2829 2278 
Q 2918 2662 2938 3258 
L 1587 3181 
L 1459 3174 
Q 1286 3174 1229 3232 
Q 1056 3398 1056 3565 
Q 1056 3610 1088 3610 
Q 1126 3610 1206 3584 
Q 1286 3558 1414 3558 
L 1446 3558 
L 3309 3667 
Q 3322 3699 3392 3782 
Q 4045 4730 4026 5030 
Q 4013 5267 4275 5120 
Q 4378 5056 4458 4982 
Q 4538 4909 4544 4864 
Q 4550 4819 4493 4717 
Q 4096 4070 3686 3757 
Q 3565 3686 3546 3680 
L 4634 3744 
Q 4794 3757 4845 3776 
Q 4896 3795 4925 3795 
Q 4954 3795 5024 3731 
Q 5235 3578 5235 3469 
Q 5235 3386 5075 3366 
L 3386 3277 
Q 3360 2701 3270 2298 
L 4954 2374 
Q 5146 2394 5210 2416 
Q 5274 2438 5315 2438 
Q 5357 2438 5446 2381 
Q 5670 2234 5670 2112 
Q 5670 2029 5504 2010 
L 3398 1914 
Q 4122 864 5005 339 
Q 5478 58 6067 -134 
Q 6202 -173 6202 -221 
Q 6202 -269 6138 -346 
Q 5965 -544 5837 -544 
L 5651 -474 
Q 5158 -282 4662 73 
Q 4166 429 3792 809 
Q 3418 1190 3110 1677 
Q 3008 1408 2957 1299 
Q 2528 429 1734 -51 
Q 1139 -416 614 -525 
Q 422 -563 364 -563 
Q 307 -563 307 -506 
Q 307 -435 538 -326 
Q 2330 480 2726 1882 
L 1120 1818 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-6790" d="M 3501 4141 
Q 3968 4314 4291 4499 
Q 4614 4685 4896 4864 
Q 5005 4941 5056 5046 
Q 5107 5152 5174 5152 
Q 5242 5152 5366 4989 
Q 5491 4826 5491 4762 
Q 5491 4698 5408 4653 
Q 4730 4282 4317 4122 
Q 3904 3962 3526 3834 
L 3526 3411 
Q 3526 3206 3520 3014 
L 5504 3130 
Q 5683 3149 5750 3171 
Q 5818 3194 5853 3194 
Q 5888 3194 5971 3139 
Q 6054 3085 6118 3011 
Q 6182 2938 6182 2893 
Q 6182 2810 6029 2797 
L 4966 2739 
L 4966 -435 
Q 4966 -614 4851 -614 
Q 4838 -614 4755 -589 
Q 4512 -512 4512 -346 
Q 4512 -294 4534 -195 
Q 4557 -96 4557 134 
L 4570 2714 
L 3501 2656 
Q 3430 1357 3008 538 
Q 2829 192 2656 -6 
Q 2483 -205 2368 -288 
Q 2253 -371 2233 -371 
Q 2214 -371 2214 -326 
Q 2214 -282 2304 -134 
Q 2618 358 2816 973 
Q 3117 1926 3117 3360 
Q 3117 3789 3097 3904 
Q 3078 4019 3043 4105 
Q 3008 4192 3008 4240 
Q 3008 4288 3101 4288 
Q 3194 4288 3501 4141 
z
M 467 3373 
Q 627 3341 704 3341 
L 1549 3398 
L 1562 4614 
Q 1562 4762 1504 4848 
Q 1446 4934 1446 4954 
Q 1446 5011 1526 5011 
Q 1606 5011 1744 4960 
Q 1882 4909 1917 4867 
Q 1952 4826 1952 4755 
L 1933 3430 
L 2304 3456 
Q 2438 3469 2496 3488 
Q 2554 3507 2595 3507 
Q 2637 3507 2710 3465 
Q 2784 3424 2841 3360 
Q 2899 3296 2899 3245 
Q 2899 3155 2758 3142 
L 1926 3078 
L 1920 2534 
Q 1990 2586 2042 2586 
Q 2144 2586 2458 2227 
Q 2778 1882 2778 1795 
Q 2778 1709 2685 1635 
Q 2592 1562 2528 1562 
Q 2464 1562 2387 1677 
Q 2182 1990 1920 2285 
L 1882 -461 
Q 1882 -614 1766 -614 
Q 1696 -614 1574 -547 
Q 1453 -480 1453 -352 
Q 1453 -307 1478 -211 
Q 1504 -115 1504 122 
L 1510 307 
Q 1510 1792 1549 2291 
L 1549 2368 
Q 1427 1965 1084 1469 
Q 742 973 474 730 
Q 352 621 301 621 
Q 262 621 262 672 
Q 262 723 333 819 
Q 954 1670 1459 3046 
L 941 3008 
Q 826 2995 726 2995 
Q 627 2995 553 3078 
Q 480 3162 451 3248 
Q 422 3334 422 3341 
Q 422 3373 467 3373 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#LXGWWenKai-Regular-4e09"/>
    <use xlink:href="#LXGWWenKai-Regular-53d8" x="99.999985"/>
    <use xlink:href="#LXGWWenKai-Regular-91cf" x="199.999969"/>
    <use xlink:href="#LXGWWenKai-Regular-76f8" x="299.999954"/>
    <use xlink:href="#LXGWWenKai-Regular-5173" x="399.999939"/>
    <use xlink:href="#LXGWWenKai-Regular-6027" x="499.999924"/>
    <use xlink:href="#LXGWWenKai-Regular-5206" x="599.999908"/>
    <use xlink:href="#LXGWWenKai-Regular-6790" x="699.999893"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p085b11a091">
   <rect x="42.95" y="158.4" width="363.913043" height="361.565217"/>
  </clipPath>
  <clipPath id="pc41158ffc2">
   <rect x="516.036957" y="158.4" width="363.913043" height="361.565217"/>
  </clipPath>
  <clipPath id="p78671c6108">
   <rect x="42.95" y="628.434783" width="363.913043" height="361.565217"/>
  </clipPath>
 </defs>
</svg>

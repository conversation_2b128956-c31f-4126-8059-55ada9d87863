#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 压力趋势预测模型

功能：
- 实现基于时间序列的压力趋势预测模型
- 考虑静息态和刺激态之间的压力变化规律
- 支持多通道EEG和ECG数据的联合建模

作者：AI助手
日期：2025年5月23日
"""

import os
import sys
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"pressure_trend_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("PressureTrend-Model")

class SelfAttention(nn.Module):
    """自注意力机制模块"""
    
    def __init__(self, hidden_dim, num_heads=4, dropout=0.1):
        """
        初始化自注意力模块
        
        参数:
        hidden_dim (int): 隐藏层维度
        num_heads (int): 注意力头数量
        dropout (float): Dropout比例
        """
        super(SelfAttention, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        assert self.head_dim * num_heads == hidden_dim, "hidden_dim必须能被num_heads整除"
        
        self.query = nn.Linear(hidden_dim, hidden_dim)
        self.key = nn.Linear(hidden_dim, hidden_dim)
        self.value = nn.Linear(hidden_dim, hidden_dim)
        
        self.fc = nn.Linear(hidden_dim, hidden_dim)
        self.dropout = nn.Dropout(dropout)
        self.scale = torch.sqrt(torch.FloatTensor([self.head_dim]))
    
    def forward(self, x, mask=None):
        """
        前向传播
        
        参数:
        x (tensor): 输入张量，形状为 [batch_size, seq_len, hidden_dim]
        mask (tensor): 掩码张量，形状为 [batch_size, seq_len, seq_len]，默认为None
        
        返回:
        tensor: 注意力输出，形状为 [batch_size, seq_len, hidden_dim]
        tensor: 注意力权重，形状为 [batch_size, num_heads, seq_len, seq_len]
        """
        batch_size, seq_len, _ = x.shape
        
        # 将输入映射到查询、键和值
        Q = self.query(x)  # [batch_size, seq_len, hidden_dim]
        K = self.key(x)    # [batch_size, seq_len, hidden_dim]
        V = self.value(x)  # [batch_size, seq_len, hidden_dim]
        
        # 重塑为多头注意力的形状
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).permute(0, 2, 1, 3)
        
        # Q: [batch_size, num_heads, seq_len, head_dim]
        # K: [batch_size, num_heads, seq_len, head_dim]
        # V: [batch_size, num_heads, seq_len, head_dim]
        
        # 计算注意力分数
        device = next(self.parameters()).device
        self.scale = self.scale.to(device)
        
        energy = torch.matmul(Q, K.permute(0, 1, 3, 2)) / self.scale  # [batch_size, num_heads, seq_len, seq_len]
        
        # 应用掩码（如果提供）
        if mask is not None:
            energy = energy.masked_fill(mask == 0, -1e10)
        
        # 注意力权重
        attention = torch.softmax(energy, dim=-1)  # [batch_size, num_heads, seq_len, seq_len]
        
        # 应用注意力权重
        x = torch.matmul(self.dropout(attention), V)  # [batch_size, num_heads, seq_len, head_dim]
        
        # 重塑回原始形状
        x = x.permute(0, 2, 1, 3).contiguous()  # [batch_size, seq_len, num_heads, head_dim]
        x = x.view(batch_size, seq_len, self.hidden_dim)  # [batch_size, seq_len, hidden_dim]
        
        # 最终的线性层
        x = self.fc(x)  # [batch_size, seq_len, hidden_dim]
        
        return x, attention

class PressureTrendEncoder(nn.Module):
    """压力趋势编码器模块"""
    
    def __init__(self, input_dim, hidden_dim, num_layers=2, num_heads=4, dropout=0.1):
        """
        初始化编码器
        
        参数:
        input_dim (int): 输入特征维度
        hidden_dim (int): 隐藏层维度
        num_layers (int): LSTM层数
        num_heads (int): 注意力头数量
        dropout (float): Dropout比例
        """
        super(PressureTrendEncoder, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 特征映射层
        self.feature_map = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 双向LSTM层
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 自注意力层
        self.attention = SelfAttention(
            hidden_dim * 2,  # 双向LSTM的输出
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 输出映射层
        self.output_map = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
    
    def forward(self, x, mask=None):
        """
        前向传播
        
        参数:
        x (tensor): 输入特征序列，形状为 [batch_size, seq_len, input_dim]
        mask (tensor): 掩码张量，形状为 [batch_size, seq_len, seq_len]，默认为None
        
        返回:
        tensor: 编码器输出，形状为 [batch_size, seq_len, hidden_dim]
        tuple: LSTM隐藏状态，(h_n, c_n)
        tensor: 注意力权重，形状为 [batch_size, num_heads, seq_len, seq_len]
        """
        # 特征映射
        x = self.feature_map(x)  # [batch_size, seq_len, hidden_dim]
        
        # LSTM编码
        lstm_out, (hidden, cell) = self.lstm(x)  # lstm_out: [batch_size, seq_len, hidden_dim*2]
        
        # 自注意力
        attention_out, attention_weights = self.attention(lstm_out, mask)  # [batch_size, seq_len, hidden_dim*2]
        
        # 输出映射
        output = self.output_map(attention_out)  # [batch_size, seq_len, hidden_dim]
        
        return output, (hidden, cell), attention_weights

class PressureTrendModel(nn.Module):
    """
    压力趋势预测模型
    
    功能：
    - 预测下一阶段的压力水平
    - 考虑EEG和ECG特征与压力变化之间的因果关系
    """
    
    def __init__(self, eeg_dim, ecg_dim, hidden_dim=128, num_layers=2, num_heads=4, dropout=0.2):
        """
        初始化模型
        
        参数:
        eeg_dim (int): EEG特征维度
        ecg_dim (int): ECG特征维度
        hidden_dim (int): 隐藏层维度
        num_layers (int): 编码器层数
        num_heads (int): 注意力头数量
        dropout (float): Dropout比例
        """
        super(PressureTrendModel, self).__init__()
        
        self.eeg_dim = eeg_dim
        self.ecg_dim = ecg_dim
        self.hidden_dim = hidden_dim
        
        # EEG编码器
        self.eeg_encoder = PressureTrendEncoder(
            input_dim=eeg_dim,
            hidden_dim=hidden_dim // 2,  # 分配一半隐藏维度给EEG
            num_layers=num_layers,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # ECG编码器
        self.ecg_encoder = PressureTrendEncoder(
            input_dim=ecg_dim,
            hidden_dim=hidden_dim // 2,  # 分配一半隐藏维度给ECG
            num_layers=num_layers,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 信号交互注意力层 - 用于捕捉EEG和ECG之间的交互作用
        self.cross_attention = SelfAttention(
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 压力预测头
        self.pressure_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1)
        )
        
        # 保存模型结果用于分析
        self.eeg_attention_weights = None
        self.ecg_attention_weights = None
        self.cross_attention_weights = None
    
    def forward(self, eeg_features, ecg_features):
        """
        前向传播
        
        参数:
        eeg_features (tensor): EEG特征序列，形状为 [batch_size, seq_len, eeg_dim]
        ecg_features (tensor): ECG特征序列，形状为 [batch_size, seq_len, ecg_dim]
        
        返回:
        tensor: 预测的压力值，形状为 [batch_size, 1]
        """
        # EEG编码
        eeg_encoded, _, self.eeg_attention_weights = self.eeg_encoder(eeg_features)
        
        # ECG编码
        ecg_encoded, _, self.ecg_attention_weights = self.ecg_encoder(ecg_features)
        
        # 合并EEG和ECG的编码结果
        combined = torch.cat([eeg_encoded, ecg_encoded], dim=-1)
        
        # 信号交互注意力
        cross_features, self.cross_attention_weights = self.cross_attention(combined)
        
        # 使用序列的最后一个时间步进行预测
        final_features = cross_features[:, -1, :]
        
        # 压力预测
        pressure_pred = self.pressure_head(final_features)
        
        return pressure_pred

def create_causal_masks(seq_len):
    """
    创建因果掩码（只能看到过去的信息）
    
    参数:
    seq_len (int): 序列长度
    
    返回:
    tensor: 因果掩码，形状为 [seq_len, seq_len]
    """
    # 创建下三角矩阵
    mask = torch.tril(torch.ones(seq_len, seq_len)).unsqueeze(0).unsqueeze(1)
    return mask

def train_pressure_trend_model(train_loader, val_loader, eeg_dim, ecg_dim, hidden_dim=128, 
                              num_epochs=100, learning_rate=0.001, device=None):
    """
    训练压力趋势预测模型
    
    参数:
    train_loader (DataLoader): 训练数据加载器
    val_loader (DataLoader): 验证数据加载器
    eeg_dim (int): EEG特征维度
    ecg_dim (int): ECG特征维度
    hidden_dim (int): 隐藏层维度
    num_epochs (int): 训练轮数
    learning_rate (float): 学习率
    device (torch.device): 计算设备
    
    返回:
    PressureTrendModel: 训练好的模型
    dict: 训练历史
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = PressureTrendModel(
        eeg_dim=eeg_dim,
        ecg_dim=ecg_dim,
        hidden_dim=hidden_dim
    ).to(device)
    
    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True
    )
    
    # 训练历史
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rate': []
    }
    
    # 早停参数
    best_val_loss = float('inf')
    patience = 10
    patience_counter = 0
    best_model_state = None
    
    logger.info(f"开始训练模型: epochs={num_epochs}, device={device}, hidden_dim={hidden_dim}")
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for batch_idx, (features, targets) in enumerate(train_loader):
            # 解包训练数据
            eeg_seq = features[:, :, :eeg_dim].to(device)
            ecg_seq = features[:, :, eeg_dim:].to(device)
            targets = targets.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            outputs = model(eeg_seq, ecg_seq)
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for features, targets in val_loader:
                # 解包验证数据
                eeg_seq = features[:, :, :eeg_dim].to(device)
                ecg_seq = features[:, :, eeg_dim:].to(device)
                targets = targets.to(device)
                
                # 前向传播
                outputs = model(eeg_seq, ecg_seq)
                loss = criterion(outputs, targets)
                
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        # 学习率调整
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录训练历史
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['learning_rate'].append(current_lr)
        
        logger.info(f"Epoch {epoch+1}/{num_epochs} - "
                   f"Train Loss: {train_loss:.6f}, "
                   f"Val Loss: {val_loss:.6f}, "
                   f"LR: {current_lr:.6f}")
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            logger.info(f"早停触发! 验证损失已经 {patience} 轮没有改善。")
            break
    
    # 恢复最佳模型
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    logger.info(f"模型训练完成! 最佳验证损失: {best_val_loss:.6f}")
    
    return model, history

def evaluate_model(model, test_loader, eeg_dim, ecg_dim, device=None):
    """
    评估模型性能
    
    参数:
    model (PressureTrendModel): 待评估的模型
    test_loader (DataLoader): 测试数据加载器
    eeg_dim (int): EEG特征维度
    ecg_dim (int): ECG特征维度
    device (torch.device): 计算设备
    
    返回:
    dict: 包含评估指标的字典
    tuple: (true_values, predicted_values)
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model.eval()
    test_loss = 0.0
    criterion = nn.MSELoss()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for features, targets in test_loader:
            # 解包测试数据
            eeg_seq = features[:, :, :eeg_dim].to(device)
            ecg_seq = features[:, :, eeg_dim:].to(device)
            targets = targets.to(device)
            
            # 前向传播
            outputs = model(eeg_seq, ecg_seq)
            loss = criterion(outputs, targets)
            
            test_loss += loss.item()
            
            # 收集预测和真实值
            all_predictions.extend(outputs.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())
    
    test_loss /= len(test_loader)
    
    # 转换为numpy数组
    y_true = np.array(all_targets)
    y_pred = np.array(all_predictions)
    
    # 计算评估指标
    mse = np.mean((y_true - y_pred) ** 2)
    mae = np.mean(np.abs(y_true - y_pred))
    
    # 计算R^2分数
    y_mean = np.mean(y_true)
    ss_tot = np.sum((y_true - y_mean) ** 2)
    ss_res = np.sum((y_true - y_pred) ** 2)
    r2 = 1 - (ss_res / ss_tot)
    
    metrics = {
        'test_loss': test_loss,
        'mse': mse,
        'mae': mae,
        'r2': r2
    }
    
    logger.info(f"模型评估结果: MSE={mse:.6f}, MAE={mae:.6f}, R^2={r2:.6f}")
    
    return metrics, (y_true, y_pred)

def extract_causal_strength(model, eeg_dim, ecg_dim, sequence_length, device=None):
    """
    从训练好的模型中提取因果强度
    
    参数:
    model (PressureTrendModel): 训练好的模型
    eeg_dim (int): EEG特征维度
    ecg_dim (int): ECG特征维度
    sequence_length (int): 序列长度
    device (torch.device): 计算设备
    
    返回:
    dict: 包含因果强度信息的字典
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model.eval()
    
    # 创建随机输入数据
    batch_size = 1
    eeg_features = torch.randn(batch_size, sequence_length, eeg_dim).to(device)
    ecg_features = torch.randn(batch_size, sequence_length, ecg_dim).to(device)
    
    # 运行模型
    with torch.no_grad():
        _ = model(eeg_features, ecg_features)
    
    # 提取注意力权重
    eeg_attn = model.eeg_attention_weights.cpu().numpy()
    ecg_attn = model.ecg_attention_weights.cpu().numpy()
    cross_attn = model.cross_attention_weights.cpu().numpy()
    
    # 分析注意力权重
    eeg_causal_strength = np.mean(eeg_attn, axis=(0, 1))  # 平均所有头
    ecg_causal_strength = np.mean(ecg_attn, axis=(0, 1))  # 平均所有头
    cross_causal_strength = np.mean(cross_attn, axis=(0, 1))  # 平均所有头
    
    # 构建结果字典
    causal_info = {
        'eeg_causal_strength': eeg_causal_strength,
        'ecg_causal_strength': ecg_causal_strength,
        'cross_causal_strength': cross_causal_strength
    }
    
    return causal_info 
name: Lint

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
    types: [opened, synchronize, reopened]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        pip install black
        
    - name: Check code formatting with Black
      run: |
        black --check . 
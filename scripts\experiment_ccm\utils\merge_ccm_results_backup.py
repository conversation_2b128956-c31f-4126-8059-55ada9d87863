#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
合并CCM批次结果脚本 (merge_ccm_results.py)
将多个批次生成的CCM结果文件合并为一个完整结果文件

用法：
python scripts/analysis/merge_ccm_results.py [--input_dir 输入目录] [--output_file 输出文件]
"""

import os
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
import glob
import argparse
import time
import logging
import gc
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 默认目录
DEFAULT_INPUT_DIR = os.path.join("result", "ccm_brain_heart")
DEFAULT_OUTPUT_FILE = "ccm_raw_results_merged.parquet"

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="合并CCM批次结果")
    parser.add_argument("--input_dir", type=str, default=DEFAULT_INPUT_DIR,
                     help=f"输入目录(默认:{DEFAULT_INPUT_DIR})")
    parser.add_argument("--output_file", type=str, default=DEFAULT_OUTPUT_FILE,
                     help=f"输出文件名(默认:{DEFAULT_OUTPUT_FILE})")
    parser.add_argument("--batch_size", type=int, default=5,
                     help="每次合并的文件数量(默认:5)")
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 确保输入目录存在
    if not os.path.exists(args.input_dir):
        logger.error(f"输入目录不存在: {args.input_dir}")
        return

    # 输出文件路径
    output_path = os.path.join(args.input_dir, args.output_file)

    logger.info("=== 开始合并CCM批次结果 ===")
    logger.info(f"输入目录: {args.input_dir}")
    logger.info(f"输出文件: {output_path}")

    start_time = time.time()

    # 查找所有批次结果文件
    batch_pattern = os.path.join(args.input_dir, "ccm_batch_*.parquet")
    # 排除摘要文件
    batch_files = [f for f in glob.glob(batch_pattern) if "_summary" not in f]

    if not batch_files:
        logger.error(f"未找到批次结果文件! 搜索模式: {batch_pattern}")
        return

    # 按文件名排序，确保按照批次顺序合并
    batch_files.sort()
    logger.info(f"找到 {len(batch_files)} 个批次结果文件")

    # 读取并合并所有批次
    all_dfs = []
    total_records = 0

    # 分批读取文件以控制内存使用
    batch_size = args.batch_size
    for i in range(0, len(batch_files), batch_size):
        batch_group = batch_files[i:i+batch_size]
        logger.info(f"处理文件组 {i//batch_size + 1}/{(len(batch_files) + batch_size - 1)//batch_size}: {len(batch_group)} 个文件")

        group_dfs = []
        for file in batch_group:
            try:
                logger.info(f"  读取文件: {os.path.basename(file)}")
                df = pd.read_parquet(file)
                file_size = os.path.getsize(file) / (1024 * 1024)  # MB
                logger.info(f"  文件大小: {file_size:.2f} MB, 记录数: {len(df)}")
                group_dfs.append(df)
                total_records += len(df)
            except Exception as e:
                logger.error(f"  读取文件失败: {file}: {e}")

        if group_dfs:
            # 合并当前组
            logger.info(f"  合并文件组...")
            batch_df = pd.concat(group_dfs, ignore_index=True)
            all_dfs.append(batch_df)

            # 释放内存
            for df in group_dfs:
                del df
            gc.collect()

            logger.info(f"  文件组处理完成，当前总记录数: {total_records}")

    if not all_dfs:
        logger.error("未能成功读取任何文件，无法合并结果")
        return

    # 最终合并
    logger.info("执行最终合并...")
    try:
        # 如果只有一个DataFrame，不需要合并
        if len(all_dfs) == 1:
            final_df = all_dfs[0]
        else:
            # 分批合并以控制内存使用
            logger.info(f"合并 {len(all_dfs)} 个数据帧...")
            final_df = pd.concat(all_dfs, ignore_index=True)

        # 保存最终结果
        logger.info(f"保存合并结果到: {output_path}，记录总数: {len(final_df)}")
        table = pa.Table.from_pandas(final_df)
        pq.write_table(table, output_path)

        # 生成汇总版本
        logger.info("生成汇总结果...")

        try:
            # 使用自定义函数处理大DataFrame
            def compute_summary(df):
                summary = df.groupby(['subject_id', 'stage', 'channel', 'band']).agg({
                    'eeg_to_heart': 'mean',
                    'heart_to_eeg': 'mean'
                }).reset_index()

                # 计算方向性指标
                summary['directionality'] = summary['eeg_to_heart'] - summary['heart_to_eeg']
                return summary

            # 如果DataFrame太大，分片处理
            if len(final_df) > 500000:  # 50万行以上考虑分片
                logger.info(f"数据集较大，使用分片处理，记录数: {len(final_df)}")
                n_chunks = max(3, len(final_df) // 500000)
                logger.info(f"将数据分为 {n_chunks} 个块处理")
                chunks = np.array_split(final_df, n_chunks)
                summaries = []

                for i, chunk in enumerate(chunks):
                    logger.info(f"处理汇总块 {i+1}/{len(chunks)}...")
                    chunk_summary = compute_summary(chunk)
                    summaries.append(chunk_summary)
                    # 释放内存
                    del chunk
                    gc.collect()

                logger.info("合并汇总块...")
                summary = pd.concat(summaries, ignore_index=True)
                # 再次合并可能的重复项
                logger.info("最终汇总计算...")
                summary = compute_summary(summary)
            else:
                logger.info("直接计算汇总统计...")
                summary = compute_summary(final_df)

            summary_output_path = os.path.join(args.input_dir, 'ccm_summary_results.parquet')
            logger.info(f"保存汇总结果到: {summary_output_path}，记录总数: {len(summary)}")
            summary_table = pa.Table.from_pandas(summary)
            pq.write_table(summary_table, summary_output_path)

        except Exception as e:
            logger.error(f"生成汇总结果时出错: {e}")

    except Exception as e:
        logger.error(f"处理最终结果时出错: {e}")

    end_time = time.time()
    duration = end_time - start_time
    minutes = int(duration / 60)
    seconds = duration % 60

    logger.info(f"=== 合并完成! 耗时: {minutes}分钟 {seconds:.2f}秒 ===")

if __name__ == "__main__":
    main()
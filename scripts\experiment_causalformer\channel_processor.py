#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 通道处理模块

功能：
- 选择关键EEG通道（Fz, Cz, Pz等中线导联）
- 评估通道信号质量
- 处理可能的通道缺失情况

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_channel_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-ChannelProcessor")

class ChannelProcessError(Exception):
    """通道处理错误的自定义异常类"""
    pass

def select_key_channels(stages_data, key_channels=None):
    """
    选择关键EEG通道

    参数:
    stages_data (dict): 各阶段数据
    key_channels (list): 关键通道列表，如果为None则使用默认的关键通道

    返回:
    dict: 包含选择了关键通道的数据

    异常:
    ChannelProcessError: 通道选择失败
    """
    # 如果未指定关键通道，则使用默认的关键通道
    if key_channels is None:
        # 默认关键通道：中线导联和一些重要的侧面导联
        key_channels = ['Fz', 'Cz', 'Pz', 'Oz', 'Fpz', 'FCz', 'CPz', 'POz',
                        'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2']

    # 确保关键中线导联（Fz, Cz, Pz）在列表的前面，优先选择
    priority_channels = ['Fz', 'Cz', 'Pz']
    for ch in reversed(priority_channels):
        if ch in key_channels:
            key_channels.remove(ch)
            key_channels.insert(0, ch)

    logger.info(f"选择关键通道: {key_channels}")
    logger.info(f"优先通道: {priority_channels}")

    # 存储处理后的数据
    processed_data = {}

    for stage, data in stages_data.items():
        logger.info(f"处理阶段 {stage} 的通道...")

        # 获取EEG通道名称
        eeg_channels = data['eeg_channels']

        # 打印所有可用的EEG通道，帮助调试
        logger.info(f"可用的EEG通道: {eeg_channels}")

        # 查找关键通道的索引
        key_indices = []
        missing_channels = []
        found_channels = []

        # 首先尝试匹配优先通道
        for ch in priority_channels:
            # 尝试精确匹配
            exact_matches = [i for i, name in enumerate(eeg_channels) if name.upper() == ch.upper()]

            if exact_matches:
                idx = exact_matches[0]  # 取第一个匹配
                key_indices.append(idx)
                found_channels.append(eeg_channels[idx])
                logger.info(f"找到优先通道 {ch}: {eeg_channels[idx]}")
            else:
                # 尝试部分匹配
                partial_matches = [i for i, name in enumerate(eeg_channels) if ch.upper() in name.upper()]

                if partial_matches:
                    idx = partial_matches[0]  # 取第一个匹配
                    key_indices.append(idx)
                    found_channels.append(eeg_channels[idx])
                    logger.info(f"找到优先通道 {ch} (部分匹配): {eeg_channels[idx]}")
                else:
                    missing_channels.append(ch)
                    logger.warning(f"未找到优先通道 {ch}")

        # 然后匹配其他关键通道
        for ch in key_channels:
            if ch in priority_channels:
                continue  # 已经处理过的优先通道跳过

            # 尝试精确匹配
            exact_matches = [i for i, name in enumerate(eeg_channels) if name.upper() == ch.upper()]

            if exact_matches:
                idx = exact_matches[0]  # 取第一个匹配
                if idx not in key_indices:  # 避免重复
                    key_indices.append(idx)
                    found_channels.append(eeg_channels[idx])
            else:
                # 尝试部分匹配
                partial_matches = [i for i, name in enumerate(eeg_channels) if ch.upper() in name.upper()]

                if partial_matches:
                    idx = partial_matches[0]  # 取第一个匹配
                    if idx not in key_indices:  # 避免重复
                        key_indices.append(idx)
                        found_channels.append(eeg_channels[idx])
                        logger.warning(f"通道 {ch} 未找到精确匹配，使用部分匹配: {eeg_channels[idx]}")
                else:
                    missing_channels.append(ch)

        # 去除重复的索引
        key_indices = sorted(list(set(key_indices)))

        # 检查是否找到了足够的关键通道
        if len(key_indices) < 3:  # 至少需要3个关键通道
            error_msg = f"找到的关键通道数量不足: {len(key_indices)}"
            logger.error(error_msg)
            raise ChannelProcessError(error_msg)

        # 检查是否找到了所有优先通道
        missing_priority = [ch for ch in priority_channels if ch in missing_channels]
        if missing_priority:
            logger.warning(f"以下优先通道未找到: {missing_priority}")

            # 如果缺少优先通道，尝试使用替代通道
            if 'Fz' in missing_priority and 'F3' in found_channels and 'F4' in found_channels:
                logger.info("使用F3和F4的平均值作为Fz的替代")
                # 在实际应用中，这里可以添加代码来计算F3和F4的平均值作为Fz的替代

            if 'Cz' in missing_priority and 'C3' in found_channels and 'C4' in found_channels:
                logger.info("使用C3和C4的平均值作为Cz的替代")
                # 在实际应用中，这里可以添加代码来计算C3和C4的平均值作为Cz的替代

            if 'Pz' in missing_priority and 'P3' in found_channels and 'P4' in found_channels:
                logger.info("使用P3和P4的平均值作为Pz的替代")
                # 在实际应用中，这里可以添加代码来计算P3和P4的平均值作为Pz的替代

        # 提取关键通道的数据
        key_eeg_data = data['eeg'][:, key_indices, :]
        key_eeg_channels = [eeg_channels[i] for i in key_indices]

        logger.info(f"  - 选择了 {len(key_indices)} 个关键通道: {key_eeg_channels}")

        # 创建处理后的数据
        processed_data[stage] = data.copy()
        processed_data[stage]['eeg'] = key_eeg_data
        processed_data[stage]['eeg_channels'] = key_eeg_channels
        processed_data[stage]['n_eeg_channels'] = len(key_eeg_channels)
        processed_data[stage]['missing_channels'] = missing_channels
        processed_data[stage]['found_channels'] = found_channels
        processed_data[stage]['priority_channels_found'] = [ch for ch in priority_channels if ch not in missing_priority]

    return processed_data

def assess_signal_quality(stages_data, threshold_snr=0.5, threshold_var=1e-12):
    """
    评估通道信号质量

    参数:
    stages_data (dict): 各阶段数据
    threshold_snr (float): SNR阈值，低于此值的通道被标记为低质量
    threshold_var (float): 方差阈值，低于此值的通道被标记为低质量

    返回:
    dict: 包含信号质量评估结果的数据

    异常:
    ChannelProcessError: 信号质量评估失败
    """
    logger.info("评估通道信号质量...")

    # 存储处理后的数据
    processed_data = {}

    for stage, data in stages_data.items():
        logger.info(f"评估阶段 {stage} 的信号质量...")

        # 获取EEG和ECG数据
        eeg_data = data['eeg']  # [n_epochs, n_channels, n_times]
        ecg_data = data['ecg']  # [n_epochs, n_channels, n_times]

        # 评估EEG通道质量
        eeg_quality = assess_channel_quality(eeg_data, data['eeg_channels'], threshold_snr, threshold_var)

        # 评估ECG通道质量
        ecg_quality = assess_channel_quality(ecg_data, data['ecg_channels'], threshold_snr, threshold_var)

        # 创建处理后的数据
        processed_data[stage] = data.copy()
        processed_data[stage]['eeg_quality'] = eeg_quality
        processed_data[stage]['ecg_quality'] = ecg_quality

        # 打印质量评估结果
        logger.info(f"  - EEG: {sum(eeg_quality['is_good'])}/{len(eeg_quality['is_good'])} 个高质量通道")
        logger.info(f"  - ECG: {sum(ecg_quality['is_good'])}/{len(ecg_quality['is_good'])} 个高质量通道")

        # 如果没有足够的高质量通道，尝试调整阈值
        if sum(eeg_quality['is_good']) < 3:  # 至少需要3个高质量EEG通道
            logger.warning(f"高质量EEG通道数量不足: {sum(eeg_quality['is_good'])}，尝试调整阈值...")

            # 逐步降低阈值，直到找到足够的高质量通道
            for snr_factor in [0.5, 0.2, 0.1, 0.05, 0.01]:
                new_threshold_snr = threshold_snr * snr_factor
                for var_factor in [0.5, 0.2, 0.1, 0.05, 0.01]:
                    new_threshold_var = threshold_var * var_factor

                    logger.info(f"  - 尝试新阈值: SNR={new_threshold_snr}, VAR={new_threshold_var}")

                    # 重新评估EEG通道质量
                    eeg_quality = assess_channel_quality(eeg_data, data['eeg_channels'], new_threshold_snr, new_threshold_var)

                    logger.info(f"  - 新阈值下EEG: {sum(eeg_quality['is_good'])}/{len(eeg_quality['is_good'])} 个高质量通道")

                    # 如果找到足够的高质量通道，更新数据并跳出循环
                    if sum(eeg_quality['is_good']) >= 3:
                        processed_data[stage]['eeg_quality'] = eeg_quality
                        logger.info(f"  - 找到足够的高质量EEG通道: {sum(eeg_quality['is_good'])}")
                        break

                # 如果找到足够的高质量通道，跳出外层循环
                if sum(eeg_quality['is_good']) >= 3:
                    break

        # 同样处理ECG通道
        if sum(ecg_quality['is_good']) < 1:  # 至少需要1个高质量ECG通道
            logger.warning(f"高质量ECG通道数量不足: {sum(ecg_quality['is_good'])}，尝试调整阈值...")

            # 逐步降低阈值，直到找到足够的高质量通道
            for snr_factor in [0.5, 0.2, 0.1, 0.05, 0.01]:
                new_threshold_snr = threshold_snr * snr_factor
                for var_factor in [0.5, 0.2, 0.1, 0.05, 0.01]:
                    new_threshold_var = threshold_var * var_factor

                    logger.info(f"  - 尝试新阈值: SNR={new_threshold_snr}, VAR={new_threshold_var}")

                    # 重新评估ECG通道质量
                    ecg_quality = assess_channel_quality(ecg_data, data['ecg_channels'], new_threshold_snr, new_threshold_var)

                    logger.info(f"  - 新阈值下ECG: {sum(ecg_quality['is_good'])}/{len(ecg_quality['is_good'])} 个高质量通道")

                    # 如果找到足够的高质量通道，更新数据并跳出循环
                    if sum(ecg_quality['is_good']) >= 1:
                        processed_data[stage]['ecg_quality'] = ecg_quality
                        logger.info(f"  - 找到足够的高质量ECG通道: {sum(ecg_quality['is_good'])}")
                        break

                # 如果找到足够的高质量通道，跳出外层循环
                if sum(ecg_quality['is_good']) >= 1:
                    break

        # 最终检查是否有足够的高质量通道
        if sum(processed_data[stage]['eeg_quality']['is_good']) < 3:
            # 如果仍然没有足够的高质量通道，则使用前3个通道作为高质量通道
            logger.warning("无法找到足够的高质量EEG通道，使用前3个通道作为高质量通道")

            # 修改质量评估结果
            processed_data[stage]['eeg_quality']['is_good'][:3] = True
            processed_data[stage]['eeg_quality']['good_channels'] = [data['eeg_channels'][i] for i in range(3)]

        if sum(processed_data[stage]['ecg_quality']['is_good']) < 1:
            # 如果仍然没有足够的高质量通道，则使用第一个通道作为高质量通道
            logger.warning("无法找到足够的高质量ECG通道，使用第一个通道作为高质量通道")

            # 修改质量评估结果
            processed_data[stage]['ecg_quality']['is_good'][0] = True
            processed_data[stage]['ecg_quality']['good_channels'] = [data['ecg_channels'][0]]

    return processed_data

def assess_channel_quality(data, channel_names, threshold_snr=0.5, threshold_var=1e-12):
    """
    评估通道质量

    参数:
    data (np.ndarray): 通道数据，形状为 [n_epochs, n_channels, n_times]
    channel_names (list): 通道名称列表
    threshold_snr (float): SNR阈值，低于此值的通道被标记为低质量
    threshold_var (float): 方差阈值，低于此值的通道被标记为低质量

    返回:
    dict: 包含通道质量评估结果的字典
    """
    n_epochs, n_channels, n_times = data.shape

    # 初始化结果
    snr_values = np.zeros(n_channels)
    var_values = np.zeros(n_channels)
    is_good = np.ones(n_channels, dtype=bool)
    quality_scores = np.zeros(n_channels)  # 综合质量评分

    # 对每个通道进行评估
    for i in range(n_channels):
        # 获取通道数据
        channel_data = data[:, i, :]  # [n_epochs, n_times]

        # 1. 计算基本统计量
        # 计算每个epoch的方差
        epoch_vars = np.var(channel_data, axis=1)  # [n_epochs]
        mean_epoch_var = np.mean(epoch_vars)
        var_values[i] = mean_epoch_var

        # 计算每个时间点的均值和标准差
        mean_signal = np.mean(channel_data, axis=0)  # [n_times]
        std_signal = np.std(channel_data, axis=0)  # [n_times]

        # 2. 检查方差是否太小（可能是常数信号或噪声很小）
        if mean_epoch_var < threshold_var:
            is_good[i] = False
            quality_scores[i] = 0.0
            logger.debug(f"通道 {channel_names[i]} 方差太小: {mean_epoch_var:.6e} < {threshold_var:.6e}")
            continue

        # 3. 计算信噪比
        # 使用时间点标准差的均值作为噪声估计
        noise_estimate = np.mean(std_signal)

        # 避免除以零
        if noise_estimate > 0:
            # 使用信号幅度（标准差）与噪声估计的比值作为SNR
            snr = np.std(mean_signal) / noise_estimate
            snr_values[i] = snr

            # 如果SNR太低，标记为低质量
            if snr < threshold_snr:
                is_good[i] = False
                logger.debug(f"通道 {channel_names[i]} SNR太低: {snr:.4f} < {threshold_snr:.4f}")
        else:
            # 如果噪声估计为0，可能是常数信号，标记为低质量
            is_good[i] = False
            snr_values[i] = 0.0
            logger.debug(f"通道 {channel_names[i]} 噪声估计为0")

        # 4. 检查是否有过多的NaN或Inf值
        nan_ratio = np.isnan(channel_data).sum() / channel_data.size
        inf_ratio = np.isinf(channel_data).sum() / channel_data.size

        if nan_ratio > 0.01 or inf_ratio > 0.01:  # 如果NaN或Inf值超过1%
            is_good[i] = False
            logger.debug(f"通道 {channel_names[i]} 包含过多的NaN或Inf值: NaN={nan_ratio:.4f}, Inf={inf_ratio:.4f}")

        # 5. 计算综合质量评分 (0-100)
        # 基于SNR、方差和NaN/Inf比例的加权平均
        if is_good[i]:
            # SNR得分：将SNR映射到0-50的范围
            snr_score = min(50, snr * 10)

            # 方差得分：将方差映射到0-30的范围（假设方差在1e-12到1之间）
            var_score = min(30, max(0, 30 * (np.log10(mean_epoch_var) + 12) / 12))

            # NaN/Inf得分：如果没有NaN/Inf，得20分，否则根据比例减分
            nan_inf_score = 20 * (1 - 10 * (nan_ratio + inf_ratio))

            # 综合评分
            quality_scores[i] = snr_score + var_score + nan_inf_score
        else:
            quality_scores[i] = 0.0

    # 创建结果字典
    quality_results = {
        'channel_names': channel_names,
        'snr': snr_values,
        'var': var_values,
        'is_good': is_good,
        'quality_scores': quality_scores,
        'good_channels': [channel_names[i] for i in range(n_channels) if is_good[i]],
        'bad_channels': [channel_names[i] for i in range(n_channels) if not is_good[i]],
        'channel_quality': {channel_names[i]: quality_scores[i] for i in range(n_channels)}
    }

    # 打印详细的质量评估报告
    logger.info("通道质量评估报告:")
    logger.info(f"  - 总通道数: {n_channels}")
    logger.info(f"  - 高质量通道数: {sum(is_good)}")
    logger.info(f"  - 低质量通道数: {n_channels - sum(is_good)}")
    logger.info(f"  - SNR范围: {np.min(snr_values):.4f} - {np.max(snr_values):.4f}, 均值: {np.mean(snr_values):.4f}")
    logger.info(f"  - 方差范围: {np.min(var_values):.4e} - {np.max(var_values):.4e}, 均值: {np.mean(var_values):.4e}")
    logger.info(f"  - 质量评分范围: {np.min(quality_scores):.1f} - {np.max(quality_scores):.1f}, 均值: {np.mean(quality_scores):.1f}")

    # 打印前5个最好和最差的通道
    sorted_indices = np.argsort(quality_scores)
    worst_channels = [(channel_names[i], quality_scores[i]) for i in sorted_indices[:5]]
    best_channels = [(channel_names[i], quality_scores[i]) for i in sorted_indices[-5:]]

    logger.info(f"  - 最佳通道: {best_channels}")
    logger.info(f"  - 最差通道: {worst_channels}")

    return quality_results

def filter_low_quality_channels(stages_data):
    """
    过滤低质量通道

    参数:
    stages_data (dict): 包含信号质量评估结果的数据

    返回:
    dict: 过滤了低质量通道的数据

    异常:
    ChannelProcessError: 过滤低质量通道失败
    """
    logger.info("过滤低质量通道...")

    # 存储处理后的数据
    processed_data = {}

    for stage, data in stages_data.items():
        logger.info(f"过滤阶段 {stage} 的低质量通道...")

        # 获取EEG和ECG质量评估结果
        eeg_quality = data['eeg_quality']
        ecg_quality = data['ecg_quality']

        # 获取高质量通道的索引
        good_eeg_indices = np.where(eeg_quality['is_good'])[0]
        good_ecg_indices = np.where(ecg_quality['is_good'])[0]

        # 检查是否有足够的高质量通道
        if len(good_eeg_indices) < 3:  # 至少需要3个高质量EEG通道
            error_msg = f"高质量EEG通道数量不足: {len(good_eeg_indices)}"
            logger.error(error_msg)
            raise ChannelProcessError(error_msg)

        if len(good_ecg_indices) < 1:  # 至少需要1个高质量ECG通道
            error_msg = f"高质量ECG通道数量不足: {len(good_ecg_indices)}"
            logger.error(error_msg)
            raise ChannelProcessError(error_msg)

        # 提取高质量通道的数据
        good_eeg_data = data['eeg'][:, good_eeg_indices, :]
        good_ecg_data = data['ecg'][:, good_ecg_indices, :]

        good_eeg_channels = [data['eeg_channels'][i] for i in good_eeg_indices]
        good_ecg_channels = [data['ecg_channels'][i] for i in good_ecg_indices]

        logger.info(f"  - 保留 {len(good_eeg_indices)}/{len(data['eeg_channels'])} 个高质量EEG通道")
        logger.info(f"  - 保留 {len(good_ecg_indices)}/{len(data['ecg_channels'])} 个高质量ECG通道")

        # 创建处理后的数据
        processed_data[stage] = data.copy()
        processed_data[stage]['eeg'] = good_eeg_data
        processed_data[stage]['ecg'] = good_ecg_data
        processed_data[stage]['eeg_channels'] = good_eeg_channels
        processed_data[stage]['ecg_channels'] = good_ecg_channels
        processed_data[stage]['n_eeg_channels'] = len(good_eeg_channels)
        processed_data[stage]['n_ecg_channels'] = len(good_ecg_channels)

    return processed_data

def process_channels(stages_data, key_channels=None, filter_low_quality=True):
    """
    处理通道数据

    参数:
    stages_data (dict): 各阶段数据
    key_channels (list): 关键通道列表，如果为None则使用默认的关键通道
    filter_low_quality (bool): 是否过滤低质量通道

    返回:
    dict: 处理后的数据

    异常:
    ChannelProcessError: 通道处理失败
    """
    logger.info("开始处理通道数据...")

    # 1. 选择关键通道
    data_with_key_channels = select_key_channels(stages_data, key_channels)

    # 2. 评估信号质量
    data_with_quality = assess_signal_quality(data_with_key_channels)

    # 3. 过滤低质量通道（如果需要）
    if filter_low_quality:
        processed_data = filter_low_quality_channels(data_with_quality)
    else:
        processed_data = data_with_quality

    logger.info("通道处理完成")

    return processed_data

def test_channel_processor(stages_data):
    """
    测试通道处理模块

    参数:
    stages_data (dict): 各阶段数据

    返回:
    bool: 测试结果
    """
    logger.info("开始测试通道处理模块...")

    try:
        # 1. 测试选择关键通道
        logger.info("测试选择关键通道...")
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        data_with_key_channels = select_key_channels(stages_data, key_channels)

        # 2. 测试评估信号质量
        logger.info("测试评估信号质量...")
        data_with_quality = assess_signal_quality(data_with_key_channels)

        # 3. 测试过滤低质量通道
        logger.info("测试过滤低质量通道...")
        processed_data = filter_low_quality_channels(data_with_quality)

        # 4. 测试完整处理流程
        logger.info("测试完整处理流程...")
        final_data = process_channels(stages_data, key_channels)

        # 打印处理结果
        for stage, data in final_data.items():
            logger.info(f"阶段 {stage} 处理结果:")
            logger.info(f"  - EEG通道数: {data['n_eeg_channels']}")
            logger.info(f"  - ECG通道数: {data['n_ecg_channels']}")
            logger.info(f"  - EEG通道: {data['eeg_channels']}")
            logger.info(f"  - ECG通道: {data['ecg_channels'][:5]}...")

        logger.info("通道处理模块测试成功!")
        return True

    except ChannelProcessError as e:
        logger.error(f"通道处理失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 导入数据加载模块
    from data_loader import load_hep_data

    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)

    # 测试通道处理模块
    test_channel_processor(stages_data)

#!/bin/bash
echo "正在激活conda环境并运行Autoformer实验..."

# 获取脚本目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
echo "脚本目录: ${SCRIPT_DIR}"

# 激活conda环境
source $(conda info --base)/etc/profile.d/conda.sh
conda activate tdspy_data

# 检查环境是否成功激活
if [ $? -ne 0 ]; then
    echo "激活conda环境失败，请确保tdspy_data环境存在"
    exit 1
fi

echo "conda环境已激活: tdspy_data"

# 安装必要的包
echo "安装必要的包..."
pip install torch numpy pandas scikit-learn matplotlib einops

# 检查GPU是否可用
echo "检查GPU是否可用..."
python "${SCRIPT_DIR}/check_gpu.py"

# 根据GPU可用性设置参数
if python -c "import torch; print(torch.cuda.is_available())" | grep -q "True"; then
    USE_GPU="True"
    echo "将使用GPU进行计算"
else
    USE_GPU="False"
    echo "将使用CPU进行计算"
fi

# 修复data_factory.py文件
echo "修复data_factory.py文件..."
python "${SCRIPT_DIR}/fix_data_factory.py"

# 修复exp_main.py文件
echo "修复exp_main.py文件..."
python "${SCRIPT_DIR}/fix_exp_main.py"

# 运行Autoformer实验
echo "运行Autoformer实验..."
python "${SCRIPT_DIR}/run_autoformer.py" --data ETTh1 --features M --use_gpu ${USE_GPU} --gpu 0 --des test_run

# 如果上面的命令失败，尝试使用CPU运行
if [ $? -ne 0 ]; then
    echo "运行失败，尝试使用CPU运行..."
    python "${SCRIPT_DIR}/run_autoformer.py" --data ETTh1 --features M --use_gpu False --gpu 0 --des test_run_cpu
fi

echo "实验完成!"

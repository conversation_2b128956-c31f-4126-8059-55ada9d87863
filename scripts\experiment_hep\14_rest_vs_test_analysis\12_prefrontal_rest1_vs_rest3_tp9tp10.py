#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前额叶区域HEP分析：两个静息阶段对比分析 (rest1 vs rest3) - 双侧乳突参考版本

本脚本分析：
1. 两个静息阶段的HEP时间序列对比（rest1和rest3）
2. 静息阶段间的差异波形分析（rest3 - rest1）
3. 按照指定脑区分组的区域拟合分析

数据来源：双侧乳突参考(TP9/TP10)的HEP数据
时间窗口：-800ms到1000ms
基线校正：-200ms到0ms (统一基线矫正)
滤波设置：0.1Hz-75Hz (可调整为0.5Hz-45Hz)

图表输出：
- 简化的2行2列布局，每个脑区一个子图
- 每个子图显示3条线：rest1、rest3、rest3-rest1差异
- 移除所有时间标记和R波标记，保持图表清洁
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
import csv
from scipy.signal import find_peaks, butter, filtfilt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'  # 修正为当前工作目录
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10')

# 创建结果目录
os.makedirs(RESULT_DIR, exist_ok=True)

# 统一后缀
RESULT_SUFFIX = '_rest1_vs_rest3_simplified_tp9tp10'

# 定义脑区电极分组 - 按照用户要求的4个脑区
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置 - 按照用户要求调整为-200ms到650ms
VIS_WINDOW = (-0.2, 0.65)      # 可视化窗口 -200ms到650ms (总共850ms)
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms
CLASSIC_WINDOW = (0.2, 0.3)    # 经典HEP窗口 200-300ms

# 图表显示配置
PLOT_CONFIG = {
    'time_range': (-200, 650),       # 时间窗口范围 (ms)
    'amplitude_range': (-200, 650),  # Y轴幅值范围 (μV)
    'aspect_ratio': 2.0              # 横轴长度是纵轴长度的2倍
}

# 滤波参数设置 - 科学优化版本
FILTER_CONFIG = {
    'original': {'low': 0.1, 'high': 75},     # 原始滤波（保留用于对比）
    'optimized': {'low': 0.5, 'high': 45},    # 第一级优化滤波 - 减少噪声
    'smooth': {'low': 1.0, 'high': 30},       # 第二级优化滤波 - 进一步收窄
    'ultra_smooth': {'low': 0.5, 'high': 20}  # 超平滑滤波（极度抖动时使用）
}

# 四种优化方案配置
OPTIMIZATION_SCHEMES = {
    'scheme1': {
        'name': 'Optimized + Gaussian',
        'filter': 'optimized',
        'smoothing': 'gaussian',
        'description': '0.5-45Hz + 高斯平滑'
    },
    'scheme2': {
        'name': 'Smooth + Gaussian',
        'filter': 'smooth',
        'smoothing': 'gaussian',
        'description': '1-30Hz + 高斯平滑'
    },
    'scheme3': {
        'name': 'Ultra-Smooth + Gaussian',
        'filter': 'ultra_smooth',
        'smoothing': 'gaussian',
        'description': '0.5-20Hz + 高斯平滑'
    },
    'scheme4': {
        'name': 'Optimized + Moving Average',
        'filter': 'optimized',
        'smoothing': 'moving_average',
        'description': '0.5-45Hz + 移动平均'
    }
}

# 心跳周期质量控制参数
RR_INTERVAL_LIMITS = {
    'min_rr': 0.4,  # 最小R-R间期（秒）- 150 BPM
    'max_rr': 1.5,  # 最大R-R间期（秒）- 40 BPM
    'min_cycles': 100  # 每个条件最少心跳周期数
}

# 数据质量控制标准
QUALITY_STANDARDS = {
    'baseline_std_threshold': 2.0,  # 基线标准差阈值（μV）
    'hep_min_amplitude': 0.5,       # HEP最小幅度（μV）
    'max_gradient_threshold': 3.0,  # 最大梯度阈值（μV/采样点）
    'smoothing_window': 5           # 移动平均窗口大小（采样点）
}

# 平滑技术配置
SMOOTHING_CONFIG = {
    'method': 'gaussian',  # 'moving_average', 'gaussian', 'adaptive'
    'moving_average_window': 5,  # 移动平均窗口大小
    'gaussian_sigma': 1.5,       # 高斯平滑标准差
    'adaptive_enabled': True     # 是否启用自适应平滑
}

# 当前使用的滤波参数（将在多方案对比中动态设置）
CURRENT_FILTER = 'optimized'  # 默认值，将在运行时根据方案调整
CURRENT_SMOOTHING = 'gaussian'  # 默认值，将在运行时根据方案调整

def load_hep_data(h5_path):
    """加载双侧乳突参考HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")
    print(f"  通道数: {len(ch_names)}")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq, high_freq):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    # 设计Butterworth滤波器
    nyquist = sampling_freq / 2
    low_norm = low_freq / nyquist
    high_norm = high_freq / nyquist

    # 确保归一化频率在有效范围内
    low_norm = max(low_norm, 0.001)
    high_norm = min(high_norm, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    # 对每个epoch和每个通道进行滤波
    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_moving_average_smoothing(data, window_size=None):
    """应用移动平均平滑"""
    if window_size is None:
        window_size = QUALITY_STANDARDS['smoothing_window']

    print(f"  应用移动平均平滑，窗口大小: {window_size}")

    smoothed_data = np.zeros_like(data)

    # 对每个epoch和每个通道应用移动平均
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            signal = data[i, j, :]
            # 使用numpy的卷积实现移动平均
            kernel = np.ones(window_size) / window_size
            # 使用'same'模式保持原始长度
            smoothed_signal = np.convolve(signal, kernel, mode='same')
            smoothed_data[i, j, :] = smoothed_signal

    return smoothed_data

def apply_gaussian_smoothing(data, sigma=1.0):
    """应用高斯平滑 - 更好的信号平滑效果"""
    from scipy.ndimage import gaussian_filter1d

    print(f"  应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)

    # 对每个epoch和每个通道应用高斯平滑
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            signal = data[i, j, :]
            # 应用高斯滤波器
            smoothed_signal = gaussian_filter1d(signal, sigma=sigma)
            smoothed_data[i, j, :] = smoothed_signal

    return smoothed_data

def apply_adaptive_smoothing(data, times, sampling_freq):
    """应用自适应平滑 - 根据信号特征调整平滑程度"""
    print("  应用自适应平滑...")

    smoothed_data = np.zeros_like(data)

    # 对每个epoch和每个通道应用自适应平滑
    for i in range(data.shape[0]):  # 遍历每个epoch
        for j in range(data.shape[1]):  # 遍历每个通道
            signal = data[i, j, :]

            # 计算信号的局部方差来确定平滑程度
            window_size = int(sampling_freq * 0.05)  # 50ms窗口
            local_variance = []

            for k in range(0, len(signal) - window_size, window_size//2):
                window_data = signal[k:k+window_size]
                local_variance.append(np.var(window_data))

            # 根据方差调整平滑参数
            mean_variance = np.mean(local_variance)
            if mean_variance > 1e-12:  # 高方差区域，需要更多平滑
                sigma = 2.0
            elif mean_variance > 5e-13:  # 中等方差区域
                sigma = 1.5
            else:  # 低方差区域，轻微平滑
                sigma = 1.0

            # 应用高斯平滑
            from scipy.ndimage import gaussian_filter1d
            smoothed_signal = gaussian_filter1d(signal, sigma=sigma)
            smoothed_data[i, j, :] = smoothed_signal

    return smoothed_data

def detect_and_remove_artifact_epochs(data, times, sampling_freq):
    """增强版伪迹检测和移除 - 更严格的质量控制"""
    print("  执行增强版伪迹检测...")

    n_epochs, n_channels, n_times = data.shape
    valid_epochs = []
    rejection_reasons = {'amplitude': 0, 'baseline': 0, 'gradient': 0, 'rr_interval': 0, 'variance': 0}

    # 计算每个epoch的质量指标
    for i in range(n_epochs):
        epoch_data = data[i]
        is_valid = True

        # 1. 检查幅度异常（更严格的阈值）
        max_amplitude = np.max(np.abs(epoch_data))
        if max_amplitude > 150e-6:  # 150μV阈值（放宽一些）
            rejection_reasons['amplitude'] += 1
            is_valid = False
            continue

        # 2. 检查基线稳定性（更严格）
        baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
        baseline_data = epoch_data[:, baseline_mask]
        baseline_std = np.mean(np.std(baseline_data, axis=1))

        if baseline_std > QUALITY_STANDARDS['baseline_std_threshold'] * 1.5 * 1e-6:  # 放宽基线标准
            rejection_reasons['baseline'] += 1
            is_valid = False
            continue

        # 3. 检查梯度异常（突变检测）
        gradients = np.gradient(epoch_data, axis=1)
        max_gradient = np.max(np.abs(gradients))

        if max_gradient > QUALITY_STANDARDS['max_gradient_threshold'] * 1.5 * 1e-6:  # 放宽梯度标准
            rejection_reasons['gradient'] += 1
            is_valid = False
            continue

        # 4. 新增：检查方差异常（识别异常抖动的epochs）
        epoch_variance = np.var(epoch_data, axis=1)
        mean_variance = np.mean(epoch_variance)
        if mean_variance > 50e-12:  # 50μV²的方差阈值（放宽）
            rejection_reasons['variance'] += 1
            is_valid = False
            continue

        # 5. 新增：检查R-R间期相关的异常（如果有心电信息）
        # 这里简化为检查时间序列的周期性
        if len(epoch_data[0]) > sampling_freq:  # 至少1秒的数据
            # 检查是否有异常的周期性模式
            autocorr = np.correlate(epoch_data[0], epoch_data[0], mode='full')
            autocorr = autocorr[autocorr.size // 2:]

            # 寻找可能的心跳周期（0.4-1.5秒对应的采样点）
            min_samples = int(RR_INTERVAL_LIMITS['min_rr'] * sampling_freq)
            max_samples = int(RR_INTERVAL_LIMITS['max_rr'] * sampling_freq)

            if len(autocorr) > max_samples:
                rr_autocorr = autocorr[min_samples:max_samples]
                if np.max(rr_autocorr) < 0.1 * np.max(autocorr[:min_samples//2]):  # 周期性太弱
                    rejection_reasons['rr_interval'] += 1
                    is_valid = False
                    continue

        if is_valid:
            valid_epochs.append(i)

    # 输出详细的拒绝统计
    print(f"    伪迹检测结果:")
    print(f"      幅度异常: {rejection_reasons['amplitude']} epochs")
    print(f"      基线不稳定: {rejection_reasons['baseline']} epochs")
    print(f"      梯度异常: {rejection_reasons['gradient']} epochs")
    print(f"      方差异常: {rejection_reasons['variance']} epochs")
    print(f"      周期性异常: {rejection_reasons['rr_interval']} epochs")

    total_rejected = sum(rejection_reasons.values())
    print(f"    总计拒绝: {total_rejected}/{n_epochs} epochs ({total_rejected/n_epochs*100:.1f}%)")

    if len(valid_epochs) < RR_INTERVAL_LIMITS['min_cycles']:
        print(f"    ⚠️ 警告: 有效epochs数量({len(valid_epochs)})少于最小要求({RR_INTERVAL_LIMITS['min_cycles']})")
        print(f"    建议: 放宽质量控制标准或收集更多数据")

    print(f"    ✅ 保留 {len(valid_epochs)}/{n_epochs} 个高质量epochs")

    return data[valid_epochs] if valid_epochs else data

def apply_unified_baseline_correction(rest1_data, rest3_data, times, baseline_window=BASELINE_WINDOW):
    """对两个静息阶段数据进行统一基线矫正"""
    print("执行统一基线矫正...")

    # 找到基线窗口的索引
    baseline_mask = (times >= baseline_window[0]) & (times <= baseline_window[1])
    print(f"  基线窗口: {baseline_window[0]*1000:.0f} 到 {baseline_window[1]*1000:.0f} ms")

    # 提取基线窗口的数据
    rest1_baseline = rest1_data[..., baseline_mask]
    rest3_baseline = rest3_data[..., baseline_mask]

    # 计算每个条件的基线均值
    rest1_baseline_mean = np.mean(rest1_baseline, axis=-1, keepdims=True)
    rest3_baseline_mean = np.mean(rest3_baseline, axis=-1, keepdims=True)

    # 合并所有基线数据计算全局基线
    all_baseline_values = np.concatenate([
        rest1_baseline_mean.reshape(-1, rest1_baseline_mean.shape[1]),
        rest3_baseline_mean.reshape(-1, rest3_baseline_mean.shape[1])
    ], axis=0)

    # 计算全局基线均值
    global_baseline_mean = np.mean(all_baseline_values, axis=0, keepdims=True)
    global_baseline_mean = global_baseline_mean[..., np.newaxis]

    # 应用统一基线矫正
    rest1_corrected = rest1_data - global_baseline_mean
    rest3_corrected = rest3_data - global_baseline_mean

    print(f"  统一基线矫正完成，全局基线均值形状: {global_baseline_mean.shape}")

    return rest1_corrected, rest3_corrected

def find_latest_files():
    """查找最新的两个静息阶段数据文件（rest1和rest3）"""
    rest1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest1_raw_epochs_') and f.endswith('.h5')]
    rest3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest3_raw_epochs_') and f.endswith('.h5')]

    if not rest1_files or not rest3_files:
        missing = []
        if not rest1_files: missing.append('rest1')
        if not rest3_files: missing.append('rest3')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")

    # 按时间戳排序，取最新的
    rest1_files.sort()
    rest3_files.sort()

    rest1_file = os.path.join(DATA_DIR, rest1_files[-1])
    rest3_file = os.path.join(DATA_DIR, rest3_files[-1])

    print(f"使用的数据文件:")
    print(f"  Rest1: {rest1_files[-1]}")
    print(f"  Rest3: {rest3_files[-1]}")

    return rest1_file, rest3_file

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]
    print(f"    实际存在的电极: {valid_electrodes}")

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        # 对每个被试的epochs求平均，然后对电极求平均
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    # 对所有被试求平均，得到区域内平滑的平均曲线
    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def load_and_process_data():
    """加载并处理两个静息阶段的数据（rest1和rest3）- 增强版预处理"""
    print("\n" + "=" * 50)
    print("数据加载与科学预处理")
    print("=" * 50)

    # 查找并加载最新的数据文件
    rest1_file, rest3_file = find_latest_files()

    # 加载原始数据
    rest1_data, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
    rest3_data, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)

    # 获取滤波参数
    filter_params = FILTER_CONFIG[CURRENT_FILTER]
    print(f"\n使用滤波配置: {CURRENT_FILTER}")
    print(f"滤波参数: {filter_params['low']}-{filter_params['high']} Hz")

    # 步骤1: 伪迹检测和移除（暂时禁用以生成图表）
    print("\n步骤1: 伪迹检测和移除（暂时跳过）")
    print(f"  数据幅度范围检查:")
    print(f"    Rest1最大幅度: {np.max(np.abs(rest1_data))*1e6:.1f} μV")
    print(f"    Rest3最大幅度: {np.max(np.abs(rest3_data))*1e6:.1f} μV")
    # rest1_data = detect_and_remove_artifact_epochs(rest1_data, times, sampling_freq)
    # rest3_data = detect_and_remove_artifact_epochs(rest3_data, times, sampling_freq)

    # 步骤2: 应用滤波器
    print("\n步骤2: 应用带通滤波")
    rest1_data = apply_bandpass_filter(rest1_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])
    rest3_data = apply_bandpass_filter(rest3_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])

    # 步骤3: 统一基线矫正
    print("\n步骤3: 统一基线矫正")
    rest1_data, rest3_data = apply_unified_baseline_correction(
        rest1_data, rest3_data, times)

    # 步骤4: 信号平滑（智能平滑技术）
    print("\n步骤4: 信号平滑处理")
    smoothing_method = CURRENT_SMOOTHING
    print(f"  使用平滑方法: {smoothing_method}")

    if smoothing_method == 'moving_average':
        window_size = SMOOTHING_CONFIG['moving_average_window']
        rest1_data = apply_moving_average_smoothing(rest1_data, window_size)
        rest3_data = apply_moving_average_smoothing(rest3_data, window_size)
    elif smoothing_method == 'gaussian':
        sigma = SMOOTHING_CONFIG['gaussian_sigma']
        rest1_data = apply_gaussian_smoothing(rest1_data, sigma)
        rest3_data = apply_gaussian_smoothing(rest3_data, sigma)
    elif smoothing_method == 'adaptive':
        rest1_data = apply_adaptive_smoothing(rest1_data, times, sampling_freq)
        rest3_data = apply_adaptive_smoothing(rest3_data, times, sampling_freq)
    else:
        print(f"  警告: 未知的平滑方法 '{smoothing_method}'，使用默认移动平均")
        rest1_data = apply_moving_average_smoothing(rest1_data)
        rest3_data = apply_moving_average_smoothing(rest3_data)

    # 输出处理后的数据统计
    print(f"\n预处理完成:")
    print(f"  Rest1数据: {rest1_data.shape[0]} epochs")
    print(f"  Rest3数据: {rest3_data.shape[0]} epochs")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间窗口: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms")

    return (rest1_data, rest3_data, ch_names, times,
            rest1_subject_ids, rest3_subject_ids, sampling_freq)





def check_data_quality(region_results, times):
    """增强版数据质量检查 - 科学标准"""
    print("\n" + "=" * 50)
    print("科学数据质量检查")
    print("=" * 50)

    quality_issues = []
    quality_metrics = {}

    for region_name, result in region_results.items():
        print(f"\n检查 {region_name}:")

        rest1_avg = result['rest1_avg']
        rest3_avg = result['rest3_avg']

        region_metrics = {}

        # 1. 基线稳定性检查（-200ms到0ms）
        baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])

        rest1_baseline_std = np.std(rest1_avg[baseline_mask]) * 1e6  # 转换为μV
        rest3_baseline_std = np.std(rest3_avg[baseline_mask]) * 1e6

        baseline_variation = max(rest1_baseline_std, rest3_baseline_std)
        region_metrics['baseline_std'] = baseline_variation

        print(f"  基线期标准差: Rest1={rest1_baseline_std:.2f}μV, Rest3={rest3_baseline_std:.2f}μV")

        if baseline_variation > QUALITY_STANDARDS['baseline_std_threshold']:
            quality_issues.append(f"{region_name}: 基线期变异性过大 ({baseline_variation:.2f}μV > {QUALITY_STANDARDS['baseline_std_threshold']}μV)")

        # 2. HEP成分检查（200-300ms）
        hep_mask = (times >= CLASSIC_WINDOW[0]) & (times <= CLASSIC_WINDOW[1])

        rest1_hep_mean = np.mean(rest1_avg[hep_mask]) * 1e6
        rest3_hep_mean = np.mean(rest3_avg[hep_mask]) * 1e6

        hep_difference = abs(rest3_hep_mean - rest1_hep_mean)
        region_metrics['hep_amplitude'] = hep_difference

        print(f"  HEP窗口幅度: Rest1={rest1_hep_mean:.2f}μV, Rest3={rest3_hep_mean:.2f}μV")
        print(f"  HEP差异幅度: {hep_difference:.2f}μV")

        if hep_difference < QUALITY_STANDARDS['hep_min_amplitude']:
            quality_issues.append(f"{region_name}: HEP差异过小 ({hep_difference:.2f}μV < {QUALITY_STANDARDS['hep_min_amplitude']}μV)")

        # 3. 信号平滑度检查
        rest1_gradient = np.gradient(rest1_avg) * 1e6
        rest3_gradient = np.gradient(rest3_avg) * 1e6

        max_gradient_rest1 = np.max(np.abs(rest1_gradient))
        max_gradient_rest3 = np.max(np.abs(rest3_gradient))
        max_gradient = max(max_gradient_rest1, max_gradient_rest3)

        region_metrics['max_gradient'] = max_gradient

        print(f"  信号梯度峰值: Rest1={max_gradient_rest1:.2f}μV/点, Rest3={max_gradient_rest3:.2f}μV/点")

        if max_gradient > QUALITY_STANDARDS['max_gradient_threshold']:
            quality_issues.append(f"{region_name}: 信号过于抖动 ({max_gradient:.2f}μV/点 > {QUALITY_STANDARDS['max_gradient_threshold']}μV/点)")

        # 4. 200ms后HEP成分可见性检查
        post_hep_mask = (times >= 0.2) & (times <= 0.6)  # 200-600ms窗口

        rest1_post_hep = rest1_avg[post_hep_mask] * 1e6
        rest3_post_hep = rest3_avg[post_hep_mask] * 1e6

        # 检查是否有明显的HEP成分
        rest1_hep_peak = np.max(np.abs(rest1_post_hep))
        rest3_hep_peak = np.max(np.abs(rest3_post_hep))

        region_metrics['hep_peak'] = max(rest1_hep_peak, rest3_hep_peak)

        print(f"  200ms后HEP峰值: Rest1={rest1_hep_peak:.2f}μV, Rest3={rest3_hep_peak:.2f}μV")

        if max(rest1_hep_peak, rest3_hep_peak) < 1.0:  # 1μV阈值
            quality_issues.append(f"{region_name}: 200ms后HEP成分不明显 (峰值 < 1.0μV)")

        # 5. 信噪比估计
        # 使用基线期作为噪声估计，HEP窗口作为信号估计
        noise_level = baseline_variation
        signal_level = hep_difference

        if noise_level > 0:
            snr = signal_level / noise_level
            region_metrics['snr'] = snr
            print(f"  估计信噪比: {snr:.2f}")

            if snr < 2.0:  # SNR < 2认为质量较差
                quality_issues.append(f"{region_name}: 信噪比过低 (SNR={snr:.2f} < 2.0)")

        quality_metrics[region_name] = region_metrics

    # 输出质量检查总结
    print(f"\n" + "=" * 30)
    print("质量检查总结")
    print("=" * 30)

    if quality_issues:
        print(f"发现 {len(quality_issues)} 个质量问题:")
        for issue in quality_issues:
            print(f"  ❌ {issue}")

        # 提供具体的优化建议
        print(f"\n优化建议:")
        if any("抖动" in issue for issue in quality_issues):
            print(f"  📊 信号抖动问题: 建议使用 CURRENT_FILTER = 'smooth' (1.0-30Hz)")
        if any("基线期变异性过大" in issue for issue in quality_issues):
            print(f"  📈 基线不稳定: 建议检查伪迹去除参数或使用更严格的滤波")
        if any("HEP差异过小" in issue for issue in quality_issues):
            print(f"  📉 HEP信号微弱: 建议增加epochs数量或检查数据预处理")
        if any("信噪比过低" in issue for issue in quality_issues):
            print(f"  🔊 信噪比低: 建议使用 CURRENT_FILTER = 'ultra_smooth' (0.5-20Hz)")
    else:
        print("✅ 数据质量检查通过，所有指标符合科学标准")

    return quality_issues, quality_metrics

def save_comprehensive_results(region_results, times, quality_issues, quality_metrics):
    """保存完整的分析结果 - 包含质量指标"""
    print("\n" + "=" * 50)
    print("保存分析结果")
    print("=" * 50)

    # 保存区域统计摘要（增强版）
    summary_file = os.path.join(RESULT_DIR, f'analysis_summary{RESULT_SUFFIX}.csv')

    with open(summary_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            '脑区', '电极数量', '电极列表', 'Rest1均值(μV)', 'Rest3均值(μV)',
            'Rest3-Rest1差值(μV)', '最大差异(μV)', '基线标准差(μV)', 'HEP峰值(μV)',
            '信噪比', '最大梯度(μV/点)', '滤波设置', '参考方式', '质量状态'
        ])

        for region_name, result in region_results.items():
            electrodes = result['electrodes']
            rest1_avg = result['rest1_avg']
            rest3_avg = result['rest3_avg']

            # 计算HEP窗口内的平均值
            hep_mask = (times >= CLASSIC_WINDOW[0]) & (times <= CLASSIC_WINDOW[1])

            rest1_hep_mean = np.mean(rest1_avg[hep_mask]) * 1e6  # 转换为μV
            rest3_hep_mean = np.mean(rest3_avg[hep_mask]) * 1e6

            # 计算差异
            diff_rest3_rest1 = rest3_hep_mean - rest1_hep_mean
            max_diff = abs(diff_rest3_rest1)

            # 获取质量指标
            metrics = quality_metrics.get(region_name, {})
            baseline_std = metrics.get('baseline_std', 0)
            hep_peak = metrics.get('hep_peak', 0)
            snr = metrics.get('snr', 0)
            max_gradient = metrics.get('max_gradient', 0)

            # 判断质量状态
            region_issues = [issue for issue in quality_issues if region_name in issue]
            quality_status = "良好" if not region_issues else f"问题({len(region_issues)}个)"

            filter_setting = f"{FILTER_CONFIG[CURRENT_FILTER]['low']}-{FILTER_CONFIG[CURRENT_FILTER]['high']}Hz"

            writer.writerow([
                region_name,
                len(electrodes),
                ', '.join(electrodes),
                f"{rest1_hep_mean:.2f}",
                f"{rest3_hep_mean:.2f}",
                f"{diff_rest3_rest1:.2f}",
                f"{max_diff:.2f}",
                f"{baseline_std:.2f}",
                f"{hep_peak:.2f}",
                f"{snr:.2f}",
                f"{max_gradient:.2f}",
                filter_setting,
                'TP9/TP10双侧乳突参考',
                quality_status
            ])

    print(f"分析摘要已保存: {summary_file}")

    # 保存质量检查报告
    quality_file = os.path.join(RESULT_DIR, f'quality_report{RESULT_SUFFIX}.txt')

    with open(quality_file, 'w', encoding='utf-8') as f:
        f.write("HEP数据质量检查报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析时间: {times[0]*1000:.0f} 到 {times[-1]*1000:.0f} ms\n")
        f.write(f"基线矫正窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"HEP分析窗口: {CLASSIC_WINDOW[0]*1000:.0f} 到 {CLASSIC_WINDOW[1]*1000:.0f} ms\n")
        f.write(f"滤波设置: {CURRENT_FILTER} ({FILTER_CONFIG[CURRENT_FILTER]['low']}-{FILTER_CONFIG[CURRENT_FILTER]['high']}Hz)\n")
        f.write(f"参考方式: TP9/TP10双侧乳突参考\n\n")

        if quality_issues:
            f.write("发现的质量问题:\n")
            for issue in quality_issues:
                f.write(f"- {issue}\n")
        else:
            f.write("数据质量检查通过，无明显问题。\n")

        f.write(f"\n分析的脑区数量: {len(region_results)}\n")
        for region_name in region_results.keys():
            f.write(f"- {region_name}\n")

    print(f"质量检查报告已保存: {quality_file}")







def process_single_scheme(scheme_config, rest1_data_raw, rest3_data_raw, ch_names, times,
                         rest1_subject_ids, rest3_subject_ids, sampling_freq):
    """处理单个优化方案的数据"""
    global CURRENT_FILTER, CURRENT_SMOOTHING

    # 设置当前方案的参数
    CURRENT_FILTER = scheme_config['filter']
    CURRENT_SMOOTHING = scheme_config['smoothing']

    print(f"\n处理方案: {scheme_config['name']}")
    print(f"  滤波: {scheme_config['filter']}")
    print(f"  平滑: {scheme_config['smoothing']}")

    # 复制原始数据
    rest1_data = rest1_data_raw.copy()
    rest3_data = rest3_data_raw.copy()

    # 步骤1: 伪迹检测和移除（暂时禁用）
    print(f"  数据幅度范围: Rest1={np.max(np.abs(rest1_data))*1e6:.1f}μV, Rest3={np.max(np.abs(rest3_data))*1e6:.1f}μV")
    # rest1_data = detect_and_remove_artifact_epochs(rest1_data, times, sampling_freq)
    # rest3_data = detect_and_remove_artifact_epochs(rest3_data, times, sampling_freq)

    # 步骤2: 应用滤波器
    filter_params = FILTER_CONFIG[CURRENT_FILTER]
    rest1_data = apply_bandpass_filter(rest1_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])
    rest3_data = apply_bandpass_filter(rest3_data, sampling_freq,
                                     filter_params['low'], filter_params['high'])

    # 步骤3: 统一基线矫正
    rest1_data, rest3_data = apply_unified_baseline_correction(rest1_data, rest3_data, times)

    # 步骤4: 信号平滑
    if CURRENT_SMOOTHING == 'gaussian':
        sigma = SMOOTHING_CONFIG['gaussian_sigma']
        rest1_data = apply_gaussian_smoothing(rest1_data, sigma)
        rest3_data = apply_gaussian_smoothing(rest3_data, sigma)
    elif CURRENT_SMOOTHING == 'moving_average':
        window_size = SMOOTHING_CONFIG['moving_average_window']
        rest1_data = apply_moving_average_smoothing(rest1_data, window_size)
        rest3_data = apply_moving_average_smoothing(rest3_data, window_size)

    # 分析各脑区
    region_results = {}
    for region_name, electrodes in BRAIN_REGIONS.items():
        # 提取区域数据
        rest1_region, valid_electrodes = extract_region_data(
            rest1_data, ch_names, rest1_subject_ids, electrodes)
        rest3_region, _ = extract_region_data(
            rest3_data, ch_names, rest3_subject_ids, electrodes)

        if rest1_region is None or rest3_region is None:
            continue

        # 计算区域平均波形
        rest1_avg = calculate_region_average(rest1_region)
        rest3_avg = calculate_region_average(rest3_region)

        if rest1_avg is None or rest3_avg is None:
            continue

        region_results[region_name] = {
            'electrodes': valid_electrodes,
            'rest1_avg': rest1_avg,
            'rest3_avg': rest3_avg
        }

    return region_results

def create_scheme_visualization(region_results, times, scheme_config, scheme_id):
    """为单个方案创建可视化图表"""
    print(f"\n创建方案 {scheme_id} 的可视化图表...")

    # 获取滤波参数信息
    filter_params = FILTER_CONFIG[scheme_config['filter']]
    filter_info = f"{filter_params['low']}-{filter_params['high']}Hz"

    # 创建2:1宽高比的图形
    fig = plt.figure(figsize=(16, 8))

    # 设置总标题
    fig.suptitle(f'HEP静息阶段分析：{scheme_config["name"]}\n'
                f'{scheme_config["description"]} | 双侧乳突参考 (TP9/TP10)',
                 fontsize=14, fontweight='bold', y=0.95)

    # 创建子图布局：2行2列
    gs = fig.add_gridspec(2, 2, hspace=0.35, wspace=0.3)

    # 高对比度颜色方案
    phase_colors = {
        'Rest1': '#000080',   # 深蓝色
        'Rest3': '#B8860B'    # 深黄色
    }
    diff_color = '#8B0000'    # 深红色

    # 为每个脑区创建子图
    region_names = list(region_results.keys())

    for i, region_name in enumerate(region_names[:4]):  # 最多显示4个脑区
        result = region_results[region_name]
        rest1_avg = result['rest1_avg']
        rest3_avg = result['rest3_avg']
        diff_rest3_rest1 = rest3_avg - rest1_avg

        # 子图位置
        row = i // 2
        col = i % 2
        ax = fig.add_subplot(gs[row, col])

        # 绘制波形线
        ax.plot(times * 1000, rest1_avg * 1e6,
                color=phase_colors['Rest1'], linewidth=1.0,
                label='Rest1', alpha=0.95)
        ax.plot(times * 1000, rest3_avg * 1e6,
                color=phase_colors['Rest3'], linewidth=1.0,
                label='Rest3', alpha=0.95)
        ax.plot(times * 1000, diff_rest3_rest1 * 1e6,
                color=diff_color, linewidth=1.0,
                label='Rest3 - Rest1', alpha=0.9)

        # 设置坐标轴和标签
        ax.set_xlim(PLOT_CONFIG['time_range'][0], PLOT_CONFIG['time_range'][1])
        ax.set_ylim(PLOT_CONFIG['amplitude_range'][0], PLOT_CONFIG['amplitude_range'][1])
        ax.set_xlabel('时间 (ms)', fontsize=10)
        ax.set_ylabel('幅值 (μV)', fontsize=10)
        ax.set_title(f'{region_name}\n{filter_info}', fontsize=11, fontweight='bold')

        # 网格和零线
        ax.grid(True, alpha=0.3, linewidth=0.5)
        ax.axhline(0, color='black', linewidth=0.5, alpha=0.6)

        # 图例
        ax.legend(fontsize=9, loc='upper right', framealpha=0.95,
                 fancybox=True, shadow=True, edgecolor='black')

        # 电极信息标注
        electrode_text = f"电极: {', '.join(result['electrodes'])}"
        ax.text(0.02, 0.02, electrode_text, transform=ax.transAxes,
                fontsize=8, verticalalignment='bottom', horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9,
                         edgecolor='gray'))

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, f'hep_analysis_{scheme_id}_{scheme_config["name"].replace(" ", "_").replace("+", "").lower()}{RESULT_SUFFIX}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"方案 {scheme_id} 图表已保存: {output_file}")

    plt.show()
    return fig

def create_four_scheme_comparison():
    """创建四种优化方案的对比分析"""
    print("\n" + "=" * 60)
    print("四种HEP信号优化方案对比分析")
    print("=" * 60)

    # 查找并加载最新的数据文件
    rest1_file, rest3_file = find_latest_files()

    # 加载原始数据
    rest1_data_raw, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
    rest3_data_raw, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)

    # 处理四种方案并生成图表
    scheme_results = {}

    for scheme_id, scheme_config in OPTIMIZATION_SCHEMES.items():
        print(f"\n{'='*30}")
        print(f"处理方案 {scheme_id}: {scheme_config['name']}")
        print(f"{'='*30}")

        # 处理当前方案的数据
        region_results = process_single_scheme(
            scheme_config, rest1_data_raw, rest3_data_raw, ch_names, times,
            rest1_subject_ids, rest3_subject_ids, sampling_freq
        )

        if region_results:
            # 创建当前方案的可视化图表
            fig = create_scheme_visualization(region_results, times, scheme_config, scheme_id)

            # 保存方案结果
            scheme_results[scheme_id] = {
                'config': scheme_config,
                'region_results': region_results,
                'figure': fig
            }
        else:
            print(f"方案 {scheme_id} 处理失败，未找到有效的脑区数据")

    print(f"\n{'='*60}")
    print("四种方案对比分析完成！")
    print(f"{'='*60}")
    print(f"成功处理的方案数量: {len(scheme_results)}")

    for scheme_id, result in scheme_results.items():
        config = result['config']
        region_count = len(result['region_results'])
        print(f"  {scheme_id}: {config['name']} - {region_count}个脑区")
        print(f"    {config['description']}")

    print(f"\n所有图表已保存到: {RESULT_DIR}")
    print("请对比各方案的HEP曲线质量，选择信噪比最佳、基线最稳定的方案。")

    return scheme_results

def main():
    """主函数 - 四种HEP信号优化方案对比分析"""
    print("=" * 60)
    print("HEP信号优化方案对比分析：Rest1 vs Rest3")
    print("双侧乳突参考 (TP9/TP10)")
    print("=" * 60)

    print(f"时间窗口: {PLOT_CONFIG['time_range'][0]} 到 {PLOT_CONFIG['time_range'][1]} ms")
    print(f"基线矫正: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")
    print(f"HEP分析窗口: {CLASSIC_WINDOW[0]*1000:.0f} 到 {CLASSIC_WINDOW[1]*1000:.0f} ms")
    print(f"图表比例: {PLOT_CONFIG['aspect_ratio']}:1 (横轴:纵轴)")
    print("数据处理流程: 加载→滤波→基线矫正→平滑→脑区分组→可视化→四方案对比")

    print(f"\n四种优化方案:")
    for scheme_id, scheme_config in OPTIMIZATION_SCHEMES.items():
        print(f"  {scheme_id}: {scheme_config['name']} - {scheme_config['description']}")

    try:
        # 执行四种方案对比分析
        print("\n开始四种优化方案对比分析...")
        scheme_results = create_four_scheme_comparison()

        if not scheme_results:
            print("未能成功处理任何优化方案")
            return

        # 输出分析总结
        print("\n" + "=" * 60)
        print("四种HEP信号优化方案对比分析完成！")
        print("=" * 60)
        print(f"成功处理的方案数量: {len(scheme_results)}")

        # 统计所有方案的脑区数量
        total_regions = set()
        for scheme_id, result in scheme_results.items():
            region_results = result['region_results']
            total_regions.update(region_results.keys())
            config = result['config']
            print(f"  {scheme_id}: {config['name']} - {len(region_results)}个脑区")
            print(f"    {config['description']}")

        print(f"\n分析的脑区总数: {len(total_regions)}")
        for region_name in sorted(total_regions):
            print(f"  {region_name}")

        print(f"\n结果保存目录: {RESULT_DIR}")
        print("参考方式: TP9/TP10双侧乳突参考")

        # 输出生成的图表
        print("\n生成的图表:")
        for scheme_id, result in scheme_results.items():
            config = result['config']
            print(f"  📊 {scheme_id}: {config['name']} - {config['description']}")

        print(f"\n� 图表显示参数:")
        print(f"  时间窗口: {PLOT_CONFIG['time_range'][0]} 到 {PLOT_CONFIG['time_range'][1]} ms")
        print(f"  幅值范围: {PLOT_CONFIG['amplitude_range'][0]} 到 {PLOT_CONFIG['amplitude_range'][1]} μV")
        print(f"  图表比例: {PLOT_CONFIG['aspect_ratio']}:1 (横轴:纵轴)")

        print(f"\n🔍 方案对比建议:")
        print("  1. 观察各方案的基线稳定性（-200ms到0ms区间）")
        print("  2. 检查HEP成分清晰度（200ms后的波形特征）")
        print("  3. 评估信号平滑度（避免过度抖动）")
        print("  4. 选择信噪比最佳的方案作为最终配置")

        # 输出科学处理流程总结
        print(f"\n🔬 科学处理流程:")
        print(f"  1. 伪迹检测与移除 (幅度<100μV, 基线稳定性, 梯度检查)")
        print(f"  2. 带通滤波 (多种方案: 0.5-45Hz, 1-30Hz, 0.5-20Hz)")
        print(f"  3. 统一基线矫正 ({BASELINE_WINDOW[0]*1000:.0f}ms到{BASELINE_WINDOW[1]*1000:.0f}ms)")
        print(f"  4. 信号平滑 (高斯平滑/移动平均)")
        print(f"  5. 质量控制验证 (基线<{QUALITY_STANDARDS['baseline_std_threshold']}μV, HEP>{QUALITY_STANDARDS['hep_min_amplitude']}μV)")

        print(f"\n📋 质量控制标准:")
        print(f"  基线标准差阈值: {QUALITY_STANDARDS['baseline_std_threshold']}μV")
        print(f"  HEP最小幅度: {QUALITY_STANDARDS['hep_min_amplitude']}μV")
        print(f"  最大梯度阈值: {QUALITY_STANDARDS['max_gradient_threshold']}μV/点")
        print(f"  最小心跳周期数: {RR_INTERVAL_LIMITS['min_cycles']}个")

    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1166.15pt" height="829.730625pt" viewBox="0 0 1166.15 829.730625" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-05-21T11:54:58.215406</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 829.730625 
L 1166.15 829.730625 
L 1166.15 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.95 417.412174 
L 352.95 417.412174 
L 352.95 128.16 
L 42.95 128.16 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mddbfe8b7f6" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #0072bd; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p5541723dd9)">
     <use xlink:href="#mddbfe8b7f6" x="173.655329" y="404.264348" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="95.912382" y="225.170189" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="338.859091" y="404.264348" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="125.065987" y="208.888901" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="66.758777" y="164.696836" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="125.065987" y="201.911207" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="241.680408" y="222.844291" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="202.808934" y="206.563003" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="299.987618" y="239.125578" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="270.834013" y="234.473781" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="163.937461" y="190.281716" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="76.476646" y="183.304022" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="144.501724" y="199.585309" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="173.655329" y="227.496087" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="270.834013" y="220.518392" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="280.551881" y="206.563003" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="241.680408" y="222.844291" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="193.091066" y="157.719142" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="163.937461" y="201.911207" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="57.040909" y="160.04504" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="115.348119" y="167.022734" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="115.348119" y="192.607614" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="105.630251" y="178.652225" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="125.065987" y="155.393244" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="105.630251" y="194.933512" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="193.091066" y="183.304022" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="125.065987" y="192.607614" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="212.526803" y="190.281716" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="193.091066" y="213.540698" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="144.501724" y="234.473781" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="261.116144" y="215.866596" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="154.219592" y="204.237105" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_1">
    <path d="M 57.040909 141.307826 
L 57.040909 196.591274 
L 59.887557 197.020327 
L 62.734206 197.44938 
L 65.580854 197.883206 
L 68.427502 198.320783 
L 71.274151 198.914049 
L 74.120799 199.192277 
L 76.967447 199.782458 
L 79.814096 200.529053 
L 82.660744 201.268297 
L 85.507392 201.826067 
L 88.35404 202.174238 
L 91.200689 202.522409 
L 94.047337 203.056819 
L 96.893985 204.065916 
L 99.740634 205.022584 
L 102.587282 205.553438 
L 105.43393 206.09252 
L 108.280579 206.623105 
L 111.127227 207.153906 
L 113.973875 208.157406 
L 116.820523 208.909634 
L 119.667172 209.592011 
L 122.51382 210.781133 
L 125.360468 211.410036 
L 128.207117 212.73334 
L 131.053765 214.002166 
L 133.900413 215.069014 
L 136.747062 216.233492 
L 139.59371 217.713304 
L 142.440358 218.585982 
L 145.287006 219.373893 
L 148.133655 220.163334 
L 150.980303 220.955496 
L 153.826951 221.911368 
L 156.6736 223.214184 
L 159.520248 224.217309 
L 162.366896 225.413707 
L 165.213545 226.357547 
L 168.060193 227.639608 
L 170.906841 229.368945 
L 173.753489 230.626197 
L 176.600138 231.976224 
L 179.446786 233.525044 
L 182.293434 235.299686 
L 185.140083 236.893694 
L 187.986731 237.954521 
L 190.833379 239.761034 
L 193.680028 241.21312 
L 196.526676 241.841865 
L 199.373324 243.634394 
L 202.219972 245.541606 
L 205.066621 246.94083 
L 207.913269 248.677178 
L 210.759917 250.44384 
L 213.606566 251.950989 
L 216.453214 253.076613 
L 219.299862 254.676997 
L 222.146511 256.025033 
L 224.993159 257.702948 
L 227.839807 259.279202 
L 230.686455 260.645812 
L 233.533104 262.238464 
L 236.379752 263.845303 
L 239.2264 265.450121 
L 242.073049 267.001982 
L 244.919697 268.641939 
L 247.766345 270.272781 
L 250.612994 271.958865 
L 253.459642 273.676945 
L 256.30629 275.439677 
L 259.152938 277.144745 
L 261.999587 278.843467 
L 264.846235 280.561388 
L 267.692883 282.279309 
L 270.539532 283.997229 
L 273.38618 285.71515 
L 276.232828 287.433071 
L 279.079477 289.150992 
L 281.926125 290.868913 
L 284.772773 292.585224 
L 287.619421 294.300427 
L 290.46607 295.959497 
L 293.312718 297.695207 
L 296.159366 299.486362 
L 299.006015 301.278668 
L 301.852663 303.00858 
L 304.699311 304.702163 
L 307.54596 306.393759 
L 310.392608 308.085355 
L 313.239256 309.776952 
L 316.085904 311.486461 
L 318.932553 313.204172 
L 321.779201 314.932925 
L 324.625849 316.660554 
L 327.472498 318.463225 
L 330.319146 320.116029 
L 333.165794 321.98513 
L 336.012443 323.751421 
L 338.859091 325.517713 
L 338.859091 228.344109 
L 338.859091 228.344109 
L 336.012443 227.84503 
L 333.165794 227.33122 
L 330.319146 226.817411 
L 327.472498 226.303601 
L 324.625849 225.789792 
L 321.779201 225.275982 
L 318.932553 224.762172 
L 316.085904 224.248363 
L 313.239256 223.734553 
L 310.392608 223.220744 
L 307.54596 222.706934 
L 304.699311 222.193257 
L 301.852663 221.680175 
L 299.006015 221.167092 
L 296.159366 220.65401 
L 293.312718 220.142833 
L 290.46607 219.656971 
L 287.619421 219.137021 
L 284.772773 218.61707 
L 281.926125 218.095495 
L 279.079477 217.57717 
L 276.232828 217.061465 
L 273.38618 216.547519 
L 270.539532 216.033572 
L 267.692883 215.519626 
L 264.846235 215.030683 
L 261.999587 214.492246 
L 259.152938 213.977786 
L 256.30629 213.463839 
L 253.459642 212.949892 
L 250.612994 212.435946 
L 247.766345 211.85572 
L 244.919697 211.333339 
L 242.073049 210.748856 
L 239.2264 210.256839 
L 236.379752 209.682459 
L 233.533104 209.251271 
L 230.686455 208.730978 
L 227.839807 208.123696 
L 224.993159 207.535873 
L 222.146511 207.00892 
L 219.299862 206.385002 
L 216.453214 205.75844 
L 213.606566 205.321779 
L 210.759917 204.862032 
L 207.913269 204.444166 
L 205.066621 203.927672 
L 202.219972 203.413649 
L 199.373324 202.900148 
L 196.526676 202.283241 
L 193.680028 201.776766 
L 190.833379 201.242827 
L 187.986731 200.595264 
L 185.140083 199.967256 
L 182.293434 199.529728 
L 179.446786 199.104423 
L 176.600138 198.678536 
L 173.753489 198.157178 
L 170.906841 197.596952 
L 168.060193 196.630088 
L 165.213545 195.633272 
L 162.366896 195.078802 
L 159.520248 194.250472 
L 156.6736 193.38676 
L 153.826951 192.330377 
L 150.980303 191.633706 
L 148.133655 190.634807 
L 145.287006 189.709702 
L 142.440358 188.748974 
L 139.59371 187.526895 
L 136.747062 186.34663 
L 133.900413 184.826641 
L 131.053765 183.345464 
L 128.207117 181.946121 
L 125.360468 180.385922 
L 122.51382 179.054918 
L 119.667172 177.610445 
L 116.820523 176.031716 
L 113.973875 174.693453 
L 111.127227 173.261462 
L 108.280579 171.587936 
L 105.43393 169.914513 
L 102.587282 168.351913 
L 99.740634 166.734252 
L 96.893985 165.175475 
L 94.047337 163.344648 
L 91.200689 161.639057 
L 88.35404 159.947148 
L 85.507392 158.299772 
L 82.660744 156.640881 
L 79.814096 155.03318 
L 76.967447 153.42548 
L 74.120799 151.740523 
L 71.274151 149.823861 
L 68.427502 148.149548 
L 65.580854 146.45328 
L 62.734206 144.631983 
L 59.887557 142.994999 
L 57.040909 141.307826 
z
" clip-path="url(#p5541723dd9)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 76.476646 417.412174 
L 76.476646 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="md81fabead9" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#md81fabead9" x="76.476646" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 30 -->
      <g transform="translate(70.476646 431.482486) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 125.065987 417.412174 
L 125.065987 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#md81fabead9" x="125.065987" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 35 -->
      <g transform="translate(119.065987 431.482486) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 173.655329 417.412174 
L 173.655329 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#md81fabead9" x="173.655329" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 40 -->
      <g transform="translate(167.655329 431.482486) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 222.244671 417.412174 
L 222.244671 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#md81fabead9" x="222.244671" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 45 -->
      <g transform="translate(216.244671 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 270.834013 417.412174 
L 270.834013 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#md81fabead9" x="270.834013" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 50 -->
      <g transform="translate(264.834013 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 319.423354 417.412174 
L 319.423354 128.16 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#md81fabead9" x="319.423354" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 55 -->
      <g transform="translate(313.423354 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_7">
     <!-- 特质焦虑水平 -->
     <g transform="translate(167.95 444.932486) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-7279" d="M 2970 2682 
L 2848 2675 
Q 2669 2675 2576 2764 
Q 2483 2854 2432 3014 
Q 2426 3027 2426 3052 
Q 2426 3078 2451 3078 
Q 2477 3078 2553 3052 
Q 2630 3027 2790 3027 
L 2848 3027 
L 3981 3091 
L 3981 3814 
L 3379 3776 
Q 3334 3770 3290 3770 
L 3213 3770 
Q 3066 3770 3008 3830 
Q 2950 3891 2902 3984 
Q 2854 4077 2854 4115 
Q 2854 4154 2876 4154 
Q 2899 4154 2972 4128 
Q 3046 4102 3219 4102 
L 3270 4102 
L 3987 4147 
L 3987 4845 
Q 3987 5011 3936 5100 
Q 3885 5190 3885 5216 
Q 3885 5261 3945 5261 
Q 4006 5261 4150 5213 
Q 4294 5165 4332 5136 
Q 4371 5107 4371 5037 
L 4371 4166 
L 4979 4205 
Q 5152 4211 5229 4243 
Q 5306 4275 5344 4275 
Q 5382 4275 5462 4217 
Q 5542 4160 5606 4089 
Q 5670 4019 5670 3981 
Q 5670 3917 5510 3904 
L 4365 3834 
L 4365 3110 
L 5581 3181 
Q 5766 3194 5840 3222 
Q 5914 3251 5955 3251 
Q 5997 3251 6077 3187 
Q 6157 3123 6221 3049 
Q 6285 2976 6285 2944 
Q 6285 2880 6118 2867 
L 2970 2682 
z
M 902 4134 
L 870 4288 
Q 870 4346 928 4346 
Q 986 4346 1082 4307 
Q 1331 4205 1331 4083 
Q 1331 4051 1264 3804 
Q 1197 3558 1152 3437 
L 1606 3469 
L 1619 4678 
Q 1619 4806 1564 4896 
Q 1510 4986 1510 5011 
Q 1510 5062 1568 5062 
Q 1626 5062 1728 5043 
Q 2003 4979 2003 4832 
L 1990 3494 
L 2259 3514 
Q 2387 3526 2448 3548 
Q 2509 3571 2544 3571 
Q 2579 3571 2650 3520 
Q 2829 3398 2829 3296 
Q 2829 3219 2682 3206 
L 1984 3162 
L 1978 2118 
L 2605 2490 
Q 2714 2554 2778 2554 
Q 2842 2554 2842 2509 
Q 2842 2406 2470 2131 
Q 2291 1997 1971 1773 
L 1952 -474 
Q 1952 -621 1830 -621 
L 1754 -602 
Q 1677 -582 1600 -528 
Q 1523 -474 1523 -394 
Q 1523 -314 1545 -205 
Q 1568 -96 1568 77 
L 1581 1504 
Q 941 1082 793 1008 
Q 646 934 579 934 
Q 512 934 480 960 
Q 352 1030 288 1120 
Q 224 1210 224 1245 
Q 224 1280 358 1299 
Q 493 1318 691 1411 
Q 890 1504 1587 1894 
L 1600 3142 
L 1037 3104 
Q 832 2605 646 2342 
Q 461 2080 384 2080 
Q 346 2080 346 2134 
Q 346 2189 384 2285 
Q 736 3072 858 3827 
Q 902 4090 902 4134 
z
M 5075 -128 
L 5082 -326 
Q 5082 -480 4992 -566 
Q 4902 -653 4825 -653 
Q 4749 -653 4531 -563 
Q 4314 -474 4058 -294 
Q 3718 -83 3718 32 
Q 3718 70 3782 70 
Q 3846 70 4041 -3 
Q 4237 -77 4685 -160 
L 4672 1696 
L 3078 1613 
L 2912 1606 
Q 2816 1606 2717 1648 
Q 2618 1690 2547 1926 
Q 2541 1939 2541 1961 
Q 2541 1984 2566 1984 
Q 2592 1984 2665 1965 
Q 2739 1946 2886 1946 
L 2950 1946 
L 4672 2035 
L 4672 2195 
Q 4672 2400 4576 2541 
Q 4544 2592 4544 2627 
Q 4544 2662 4611 2662 
Q 4678 2662 4809 2611 
Q 4941 2560 4998 2515 
Q 5056 2470 5056 2400 
L 5056 2054 
L 5472 2074 
Q 5581 2080 5648 2112 
Q 5715 2144 5763 2144 
Q 5811 2144 5900 2086 
Q 5990 2029 6057 1958 
Q 6125 1888 6125 1850 
Q 6125 1773 5971 1760 
L 5062 1715 
L 5075 -128 
z
M 3187 1203 
Q 3136 1254 3136 1312 
Q 3136 1370 3209 1424 
Q 3283 1478 3328 1478 
Q 3373 1478 3501 1379 
Q 3629 1280 3773 1142 
Q 3917 1005 4019 889 
Q 4122 774 4122 723 
Q 4122 672 4029 585 
Q 3936 499 3878 499 
Q 3821 499 3696 668 
Q 3571 838 3187 1203 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8d28" d="M 3398 1734 
Q 3398 1850 3353 1907 
Q 3309 1965 3309 2003 
Q 3309 2086 3446 2086 
Q 3584 2086 3696 2035 
Q 3808 1984 3808 1869 
Q 3770 672 3149 147 
Q 2566 -333 1696 -512 
Q 1530 -544 1510 -544 
Q 1427 -544 1427 -461 
Q 1427 -378 1562 -333 
Q 2144 -141 2589 147 
Q 3034 435 3200 794 
Q 3398 1190 3398 1619 
L 3398 1734 
z
M 3642 4429 
Q 3827 4358 3827 4224 
Q 3789 3962 3744 3686 
L 5120 3776 
Q 5350 3789 5408 3811 
Q 5466 3834 5523 3834 
Q 5581 3834 5654 3773 
Q 5728 3712 5773 3645 
Q 5818 3578 5818 3546 
Q 5818 3488 5670 3475 
L 3686 3360 
Q 3578 2861 3488 2650 
L 4851 2726 
L 4902 2726 
Q 4992 2726 5091 2662 
Q 5190 2598 5190 2540 
Q 5190 2483 5174 2454 
Q 5158 2426 5152 2400 
L 5050 781 
L 5043 666 
Q 5037 518 4928 518 
Q 4864 525 4758 585 
Q 4653 646 4659 755 
Q 4659 794 4672 835 
Q 4685 877 4762 2400 
L 2438 2278 
L 2470 602 
L 2470 486 
Q 2470 339 2362 339 
Q 2298 339 2189 396 
Q 2080 454 2080 563 
L 2099 742 
Q 2099 800 2080 2240 
Q 2080 2432 2032 2521 
Q 1984 2611 1984 2646 
Q 1984 2682 2070 2682 
Q 2157 2682 2451 2586 
L 3149 2624 
Q 3232 3014 3283 3334 
L 1638 3238 
Q 1562 1402 954 397 
Q 755 64 569 -160 
Q 384 -384 323 -384 
Q 262 -384 262 -320 
Q 262 -256 346 -115 
Q 954 896 1133 2003 
Q 1254 2758 1254 3987 
Q 1254 4269 1216 4406 
Q 1178 4544 1178 4563 
L 1178 4595 
Q 1178 4666 1258 4666 
Q 1338 4666 1696 4454 
Q 3142 4627 4397 5018 
Q 4589 5069 4678 5171 
Q 4710 5203 4764 5203 
Q 4819 5203 4902 5136 
Q 4986 5069 5043 4982 
Q 5101 4896 5101 4832 
Q 5101 4768 5011 4736 
Q 4346 4570 4032 4509 
Q 3718 4448 3642 4429 
z
M 5587 -115 
Q 5734 -211 5734 -272 
Q 5734 -333 5680 -451 
Q 5626 -570 5558 -570 
Q 5491 -570 5155 -349 
Q 4819 -128 3942 262 
Q 3878 294 3843 320 
Q 3808 346 3808 416 
Q 3808 486 3865 560 
Q 3923 634 3955 634 
Q 3987 634 4198 544 
Q 4410 454 4765 300 
Q 5120 147 5587 -115 
z
M 1664 4154 
L 1658 3648 
L 1651 3558 
L 3334 3661 
Q 3405 4102 3405 4211 
Q 3405 4320 3373 4384 
Q 2720 4269 1664 4154 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7126" d="M 3610 4954 
L 3597 5030 
L 3597 5056 
Q 3597 5133 3661 5133 
Q 3725 5133 3821 5082 
Q 4070 4947 4070 4845 
Q 4070 4768 3907 4509 
Q 3744 4250 3635 4115 
L 4768 4192 
Q 4915 4205 4969 4224 
Q 5024 4243 5059 4243 
Q 5094 4243 5171 4198 
Q 5363 4070 5363 3949 
Q 5363 3885 5222 3872 
L 3654 3776 
L 3648 3238 
L 4435 3277 
Q 4582 3290 4630 3312 
Q 4678 3334 4713 3334 
Q 4749 3334 4826 3290 
Q 5024 3155 5024 3053 
Q 5024 2982 4877 2970 
L 3648 2912 
L 3648 2349 
L 4461 2387 
Q 4614 2406 4656 2425 
Q 4698 2445 4739 2445 
Q 4781 2445 4851 2390 
Q 4922 2336 4973 2265 
Q 5024 2195 5024 2157 
Q 5024 2099 4890 2080 
L 3648 2022 
L 3642 1427 
L 5120 1485 
Q 5242 1491 5296 1513 
Q 5350 1536 5404 1536 
Q 5459 1536 5529 1472 
Q 5600 1408 5645 1334 
Q 5690 1261 5690 1229 
Q 5690 1178 5555 1158 
L 1978 1030 
L 1978 832 
Q 1978 723 1882 723 
Q 1766 723 1644 812 
Q 1523 902 1523 953 
Q 1523 1005 1552 1113 
Q 1581 1222 1581 1459 
L 1600 3219 
Q 1184 2720 800 2394 
Q 627 2246 569 2246 
Q 512 2246 512 2307 
Q 512 2368 608 2483 
Q 1645 3731 2150 4813 
Q 2208 4941 2208 5062 
Q 2208 5184 2278 5184 
Q 2317 5184 2419 5133 
Q 2522 5082 2611 5005 
Q 2701 4928 2701 4883 
Q 2701 4838 2547 4579 
Q 2394 4320 2182 4026 
L 3264 4096 
Q 3482 4448 3546 4659 
Q 3610 4870 3610 4902 
L 3610 4954 
z
M 3270 3757 
L 1990 3680 
L 1984 3149 
L 3264 3213 
L 3270 3757 
z
M 3264 2893 
L 1984 2829 
L 1984 2266 
L 3258 2330 
L 3264 2893 
z
M 3258 2003 
L 1984 1946 
L 1978 1363 
L 3251 1414 
L 3258 2003 
z
M 5702 -467 
Q 5261 115 4736 582 
Q 4666 653 4666 710 
Q 4666 768 4742 832 
Q 4819 896 4873 896 
Q 4928 896 5053 790 
Q 5178 685 5370 509 
Q 5562 333 5725 166 
Q 5888 0 5990 -125 
Q 6093 -250 6093 -304 
Q 6093 -358 6006 -460 
Q 5920 -563 5849 -563 
Q 5779 -563 5702 -467 
z
M 448 -205 
Q 960 307 1158 736 
Q 1229 845 1257 845 
Q 1286 845 1350 826 
Q 1536 768 1536 678 
Q 1536 589 1341 294 
Q 1146 0 1018 -173 
Q 890 -346 787 -458 
Q 685 -570 643 -570 
Q 602 -570 496 -477 
Q 390 -384 390 -323 
Q 390 -262 448 -205 
z
M 4480 -294 
Q 4288 -550 4115 -339 
Q 3693 186 3315 506 
Q 3238 576 3238 627 
Q 3238 678 3312 752 
Q 3386 826 3440 826 
Q 3494 826 3616 733 
Q 3738 640 3888 499 
Q 4038 358 4182 208 
Q 4326 58 4422 -57 
Q 4518 -173 4518 -205 
Q 4518 -237 4480 -294 
z
M 2618 -320 
Q 2323 173 2054 454 
Q 1990 518 1990 572 
Q 1990 627 2080 698 
Q 2144 762 2195 762 
Q 2246 762 2342 672 
Q 2438 582 2553 448 
Q 2669 314 2774 173 
Q 2880 32 2947 -77 
Q 3014 -186 3014 -230 
Q 3014 -275 2918 -355 
Q 2822 -435 2755 -435 
Q 2688 -435 2618 -320 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-8651" d="M 5523 4006 
L 5606 4013 
Q 5709 4013 5821 3917 
Q 5933 3821 5933 3757 
Q 5933 3693 5891 3661 
Q 5850 3629 5763 3513 
Q 5677 3398 5504 3219 
Q 5126 2816 4986 2816 
Q 4934 2816 4934 2880 
Q 4934 2944 5018 3046 
Q 5229 3302 5382 3661 
L 1690 3494 
Q 1690 2957 1661 2390 
Q 1632 1824 1510 1318 
Q 1254 333 442 -410 
Q 435 -416 425 -422 
Q 416 -429 410 -442 
Q 294 -531 236 -531 
Q 179 -531 179 -473 
Q 179 -416 269 -294 
Q 1037 685 1178 1862 
Q 1248 2477 1248 3219 
L 1248 3456 
Q 1229 3789 1200 3865 
Q 1171 3942 1171 3977 
Q 1171 4013 1241 4013 
Q 1312 4013 1709 3827 
L 3002 3885 
L 3002 4941 
Q 3002 5088 2906 5222 
Q 2886 5254 2886 5267 
Q 2886 5306 2956 5306 
Q 3027 5306 3136 5286 
Q 3418 5235 3418 5069 
L 3411 4672 
L 4685 4755 
Q 4832 4768 4886 4787 
Q 4941 4806 4976 4806 
Q 5011 4806 5082 4762 
Q 5274 4634 5274 4547 
Q 5274 4461 5114 4448 
L 3405 4339 
L 3398 3904 
L 5523 4006 
z
M 1875 2790 
L 2010 2765 
Q 2061 2758 2112 2758 
L 2157 2758 
L 2931 2829 
L 2931 3091 
Q 2931 3232 2835 3347 
Q 2816 3379 2816 3398 
Q 2816 3456 2944 3456 
Q 3072 3456 3226 3386 
Q 3322 3341 3322 3232 
L 3322 2861 
L 4250 2944 
Q 4371 2950 4438 2982 
Q 4506 3014 4538 3014 
Q 4570 3014 4640 2970 
Q 4832 2848 4832 2752 
Q 4832 2675 4704 2656 
L 3322 2534 
L 3322 2214 
Q 3322 2093 3427 2064 
Q 3533 2035 3875 2035 
Q 4218 2035 4461 2057 
Q 4704 2080 4835 2121 
Q 4966 2163 4995 2163 
Q 5024 2163 5094 2125 
Q 5280 2003 5280 1888 
Q 5280 1786 5094 1760 
Q 4307 1677 3706 1677 
L 3430 1683 
Q 2925 1696 2925 2150 
L 2925 2502 
L 2266 2445 
Q 2240 2438 2214 2438 
Q 2189 2438 2163 2438 
Q 1997 2438 1920 2560 
Q 1843 2682 1843 2736 
Q 1843 2790 1875 2790 
z
M 1658 -205 
Q 1587 -320 1507 -320 
Q 1427 -320 1337 -240 
Q 1248 -160 1248 -109 
Q 1248 -58 1299 13 
Q 1613 512 1798 1120 
Q 1837 1242 1933 1242 
Q 2003 1242 2086 1194 
Q 2170 1146 2170 1094 
Q 2170 1082 2112 890 
Q 1958 333 1658 -205 
z
M 5664 326 
Q 5357 736 4986 1069 
Q 4902 1146 4902 1194 
Q 4902 1242 4966 1315 
Q 5030 1389 5084 1389 
Q 5139 1389 5331 1235 
Q 5523 1082 5779 813 
Q 6099 499 6099 419 
Q 6099 339 6003 252 
Q 5907 166 5846 166 
Q 5786 166 5664 326 
z
M 3789 570 
Q 3520 928 3245 1165 
Q 3162 1248 3162 1296 
Q 3162 1344 3232 1411 
Q 3302 1478 3347 1478 
Q 3462 1478 3923 1018 
Q 4186 768 4186 694 
Q 4186 621 4099 531 
Q 4013 442 3949 442 
Q 3885 442 3789 570 
z
M 5261 -205 
Q 5261 -486 4384 -486 
Q 3507 -486 3053 -211 
Q 2682 32 2502 550 
Q 2426 755 2413 899 
Q 2400 1043 2368 1030 
Q 2368 1139 2534 1171 
Q 2592 1178 2605 1178 
Q 2720 1178 2752 1030 
Q 2842 640 2998 393 
Q 3155 147 3456 25 
Q 3757 -96 4208 -96 
Q 4659 -96 4717 -64 
Q 4480 589 4480 720 
Q 4480 851 4566 851 
Q 4653 851 4730 698 
Q 4877 403 5043 169 
Q 5210 -64 5235 -112 
Q 5261 -160 5261 -205 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6c34" d="M 3488 77 
L 3494 -166 
Q 3494 -346 3388 -432 
Q 3283 -518 3190 -518 
Q 3098 -518 2845 -409 
Q 2592 -301 2189 -26 
Q 1786 269 1786 365 
Q 1786 422 1850 422 
Q 1914 422 2086 339 
Q 2560 115 3053 26 
L 3053 4678 
Q 3053 4845 2938 5005 
Q 2912 5037 2912 5078 
Q 2912 5120 2985 5120 
Q 3059 5120 3181 5088 
Q 3501 5005 3501 4864 
L 3494 3482 
Q 3712 3142 3923 2848 
Q 4538 3315 4883 3731 
Q 5062 3949 5075 4067 
Q 5088 4186 5149 4186 
Q 5210 4186 5286 4109 
Q 5498 3923 5498 3811 
Q 5498 3699 5005 3257 
Q 4512 2816 4134 2547 
Q 5082 1274 6234 608 
Q 6342 557 6342 512 
Q 6342 467 6266 397 
Q 6074 230 5952 230 
Q 5914 230 5862 262 
Q 4480 1274 3494 2803 
L 3488 77 
z
M 621 3424 
Q 826 3398 922 3398 
L 1005 3398 
L 2253 3488 
L 2330 3488 
Q 2445 3488 2544 3414 
Q 2643 3341 2643 3264 
Q 2643 3187 2604 3142 
Q 2566 3098 2560 3078 
Q 2266 2170 1766 1466 
Q 1267 762 499 230 
Q 416 173 365 173 
Q 294 173 294 240 
Q 294 307 378 384 
Q 1670 1626 2093 3098 
L 1139 3034 
Q 1024 3021 950 3021 
Q 877 3021 787 3065 
Q 698 3110 589 3334 
Q 570 3373 570 3398 
Q 570 3424 621 3424 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5e73" d="M 755 1613 
L 550 1606 
Q 435 1606 403 1638 
Q 218 1837 218 1965 
Q 218 2010 269 2010 
Q 294 2010 345 1990 
Q 397 1971 550 1971 
L 621 1971 
L 2938 2067 
L 2950 4416 
L 1555 4339 
Q 1504 4333 1446 4333 
L 1331 4333 
Q 1286 4333 1241 4345 
Q 1197 4358 1113 4476 
Q 1030 4595 1030 4659 
Q 1030 4723 1075 4723 
Q 1101 4723 1155 4707 
Q 1210 4691 1350 4691 
L 1427 4691 
L 4634 4877 
Q 4813 4890 4873 4912 
Q 4934 4934 4995 4934 
Q 5056 4934 5136 4880 
Q 5216 4826 5270 4755 
Q 5325 4685 5325 4640 
Q 5325 4557 5165 4544 
L 3366 4442 
L 3360 2086 
L 5446 2176 
Q 5619 2189 5683 2211 
Q 5747 2234 5817 2234 
Q 5888 2234 5968 2173 
Q 6048 2112 6096 2041 
Q 6144 1971 6144 1933 
Q 6144 1856 5984 1837 
L 3354 1722 
L 3347 -582 
Q 3347 -742 3216 -742 
Q 3085 -742 2982 -665 
Q 2880 -589 2880 -474 
Q 2880 -435 2902 -316 
Q 2925 -198 2925 13 
L 2931 1709 
L 755 1613 
z
M 4538 4090 
Q 4538 4186 4621 4186 
Q 4762 4186 4947 4019 
Q 5024 3955 5024 3907 
Q 5024 3859 4880 3606 
Q 4736 3354 4387 2909 
Q 4038 2464 3808 2349 
Q 3750 2310 3718 2310 
Q 3686 2310 3686 2348 
Q 3686 2387 3824 2585 
Q 3962 2784 4134 3085 
Q 4538 3770 4538 4032 
L 4538 4090 
z
M 2118 2515 
Q 1850 3059 1408 3603 
Q 1331 3712 1331 3760 
Q 1331 3808 1420 3865 
Q 1510 3923 1561 3923 
Q 1613 3923 1728 3801 
Q 1843 3680 1980 3497 
Q 2118 3315 2246 3129 
Q 2374 2944 2457 2806 
Q 2541 2669 2541 2627 
Q 2541 2586 2435 2496 
Q 2330 2406 2253 2406 
Q 2176 2406 2118 2515 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-7279"/>
      <use xlink:href="#LXGWWenKai-Regular-8d28" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-6c34" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5e73" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_13">
      <path d="M 42.95 404.264348 
L 352.95 404.264348 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <defs>
       <path id="m5de63a5a44" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="404.264348" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 0 -->
      <g transform="translate(29.95 407.799504) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_15">
      <path d="M 42.95 346.116894 
L 352.95 346.116894 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="346.116894" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 1 -->
      <g transform="translate(29.95 349.65205) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_17">
      <path d="M 42.95 287.969439 
L 352.95 287.969439 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="287.969439" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 2 -->
      <g transform="translate(29.95 291.504596) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_19">
      <path d="M 42.95 229.821985 
L 352.95 229.821985 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="229.821985" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 3 -->
      <g transform="translate(29.95 233.357141) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_21">
      <path d="M 42.95 171.674531 
L 352.95 171.674531 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="171.674531" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 4 -->
      <g transform="translate(29.95 175.209687) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_13">
     <!-- 心理韧性 -->
     <g transform="translate(24.229687 292.786087) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7406" d="M 2918 2214 
L 2918 2438 
Q 2918 2490 2912 2554 
L 2790 4186 
Q 2784 4365 2726 4502 
Q 2669 4640 2669 4691 
Q 2669 4742 2752 4742 
Q 2835 4742 3136 4614 
L 5306 4762 
L 5344 4762 
Q 5466 4762 5536 4691 
Q 5651 4608 5651 4531 
Q 5651 4454 5632 4409 
Q 5613 4365 5606 4326 
L 5427 2515 
Q 5568 2349 5568 2281 
Q 5568 2214 5529 2198 
Q 5491 2182 5434 2176 
L 4333 2131 
L 4333 1331 
L 4992 1357 
Q 5082 1376 5139 1385 
Q 5197 1395 5251 1414 
Q 5306 1434 5334 1434 
Q 5363 1434 5434 1389 
Q 5626 1267 5626 1161 
Q 5626 1056 5530 1043 
L 4333 992 
L 4333 173 
L 5594 211 
Q 5683 218 5747 218 
L 5939 256 
Q 5997 256 6131 160 
Q 6246 45 6246 -48 
Q 6246 -141 6118 -141 
L 2547 -250 
Q 2362 -250 2291 -179 
Q 2157 -19 2138 134 
Q 2138 173 2163 173 
L 2189 160 
Q 2272 147 2349 128 
Q 2426 109 2509 109 
L 2534 109 
L 3968 160 
L 3968 973 
L 3270 934 
L 3117 928 
Q 3066 928 2966 969 
Q 2867 1011 2797 1248 
L 2797 1274 
Q 2797 1312 2829 1312 
L 2842 1312 
Q 2970 1274 3046 1274 
L 3149 1274 
Q 3168 1274 3194 1280 
L 3962 1312 
L 3962 2112 
L 3315 2086 
L 3328 1920 
L 3328 1907 
Q 3328 1792 3219 1792 
Q 3206 1792 3126 1824 
Q 3046 1856 2976 1917 
Q 2906 1978 2912 2064 
Q 2918 2150 2918 2214 
z
M 685 4397 
L 1926 4480 
Q 1984 4493 2045 4502 
Q 2106 4512 2157 4531 
Q 2208 4550 2249 4550 
Q 2291 4550 2368 4502 
Q 2445 4454 2502 4387 
Q 2560 4320 2560 4275 
Q 2560 4186 2406 4173 
L 1709 4115 
L 1696 2957 
L 1920 2976 
Q 2074 2989 2138 3014 
Q 2202 3040 2230 3040 
Q 2259 3040 2339 2989 
Q 2419 2938 2483 2867 
Q 2547 2797 2547 2752 
Q 2547 2662 2394 2650 
L 1696 2598 
L 1683 1299 
Q 1869 1382 2077 1484 
Q 2285 1587 2429 1657 
Q 2573 1728 2637 1728 
Q 2701 1728 2701 1677 
Q 2701 1606 2541 1491 
Q 1658 890 960 563 
Q 666 422 586 422 
Q 506 422 422 489 
Q 339 557 278 640 
Q 218 723 218 771 
Q 218 819 384 835 
Q 550 851 1312 1139 
L 1318 2573 
L 1024 2554 
Q 934 2541 873 2541 
Q 813 2541 794 2547 
Q 730 2547 656 2627 
Q 582 2707 550 2784 
Q 518 2861 518 2886 
Q 518 2931 557 2931 
Q 570 2931 637 2915 
Q 704 2899 819 2899 
L 896 2899 
L 1325 2925 
L 1331 4090 
L 928 4058 
Q 826 4045 733 4045 
Q 640 4045 608 4077 
Q 442 4192 410 4365 
Q 403 4378 403 4384 
Q 403 4422 448 4422 
L 685 4397 
z
M 5203 4416 
L 4333 4358 
L 4333 3616 
L 5152 3654 
L 5203 4416 
z
M 3955 4339 
L 3162 4288 
L 3213 3546 
L 3955 3584 
L 3955 4339 
z
M 5126 3322 
L 4333 3277 
L 4333 2464 
L 5069 2502 
L 5126 3322 
z
M 3962 3251 
L 3238 3213 
L 3296 2413 
L 3962 2445 
L 3962 3251 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-97e7" d="M 5037 -19 
Q 5075 -19 5075 26 
Q 5402 1184 5446 4045 
L 4704 3994 
Q 4666 3072 4544 2489 
Q 4422 1907 4307 1626 
Q 3872 518 3238 -51 
Q 2989 -275 2813 -364 
Q 2637 -454 2585 -454 
Q 2534 -454 2534 -403 
Q 2534 -352 2662 -243 
Q 3469 435 3949 1760 
Q 4141 2278 4237 3142 
Q 4288 3552 4301 3968 
L 3520 3923 
Q 3418 3910 3293 3910 
Q 3168 3910 3085 4026 
Q 2970 4198 2970 4249 
Q 2970 4301 3021 4301 
Q 3046 4301 3104 4285 
Q 3162 4269 3283 4269 
L 5504 4416 
L 5574 4416 
Q 5722 4416 5789 4329 
Q 5856 4243 5856 4179 
L 5830 4019 
Q 5811 2381 5645 845 
Q 5562 154 5478 -166 
Q 5440 -314 5321 -429 
Q 5203 -544 5113 -544 
Q 5024 -544 4829 -432 
Q 4634 -320 4330 -29 
Q 4026 262 4026 371 
Q 4026 416 4086 416 
Q 4147 416 4387 269 
Q 4627 122 5005 -13 
Q 5018 -19 5037 -19 
z
M 813 1638 
L 659 1632 
Q 538 1626 464 1706 
Q 390 1786 361 1885 
Q 333 1984 333 2003 
Q 333 2022 368 2022 
Q 403 2022 467 2000 
Q 531 1978 646 1984 
L 698 1984 
L 1370 2029 
L 1363 2707 
L 1094 2694 
Q 1050 2682 1005 2682 
L 934 2682 
Q 819 2682 764 2736 
Q 710 2790 662 2886 
Q 614 2982 614 3014 
Q 614 3046 649 3046 
Q 685 3046 742 3030 
Q 800 3014 922 3014 
L 966 3014 
L 1363 3046 
L 1363 3757 
L 941 3725 
Q 832 3712 745 3709 
Q 659 3706 588 3782 
Q 518 3859 486 3945 
Q 454 4032 454 4064 
Q 454 4096 489 4096 
Q 525 4096 589 4077 
Q 653 4058 755 4058 
L 1363 4102 
L 1363 4550 
Q 1363 4717 1312 4803 
Q 1261 4890 1261 4909 
Q 1261 4960 1341 4963 
Q 1421 4966 1542 4915 
Q 1664 4864 1693 4822 
Q 1722 4781 1722 4717 
L 1722 4128 
L 2266 4166 
Q 2438 4186 2476 4205 
Q 2515 4224 2560 4224 
Q 2605 4224 2678 4176 
Q 2752 4128 2803 4061 
Q 2854 3994 2854 3949 
Q 2854 3872 2714 3859 
L 1722 3782 
L 1728 3078 
L 2221 3110 
Q 2259 3117 2297 3123 
Q 2336 3130 2374 3146 
Q 2413 3162 2461 3162 
Q 2509 3162 2579 3117 
Q 2752 3008 2752 2893 
Q 2752 2822 2611 2810 
L 1728 2739 
L 1728 2048 
L 2509 2106 
Q 2528 2112 2547 2112 
L 2579 2112 
Q 2701 2112 2742 2054 
Q 2784 1997 2803 1946 
Q 2803 1907 2816 1894 
L 2816 1888 
Q 2810 1862 2800 1827 
Q 2790 1792 2784 1747 
L 2726 685 
L 2726 499 
Q 2726 403 2659 288 
Q 2592 173 2505 173 
Q 2419 173 2342 237 
Q 2131 358 1875 742 
Q 1830 819 1830 848 
Q 1830 877 1881 880 
Q 1933 883 2064 796 
Q 2195 710 2368 646 
L 2419 1741 
L 1728 1702 
L 1734 -506 
Q 1734 -646 1632 -653 
Q 1574 -653 1456 -589 
Q 1338 -525 1338 -435 
L 1338 -378 
Q 1338 -358 1354 -272 
Q 1370 -186 1370 -13 
L 1370 1677 
L 813 1638 
z
M 3814 3130 
Q 3814 2957 3638 2393 
Q 3462 1830 3414 1702 
Q 3366 1574 3340 1545 
Q 3315 1517 3283 1517 
Q 3251 1517 3181 1536 
Q 2982 1600 2982 1690 
Q 2982 1722 3065 1894 
Q 3149 2067 3270 2457 
Q 3392 2848 3427 3040 
Q 3462 3232 3481 3261 
Q 3501 3290 3552 3290 
Q 3603 3290 3708 3274 
Q 3814 3258 3814 3155 
L 3814 3130 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6027" d="M 1274 4742 
Q 1274 4902 1219 4972 
Q 1165 5043 1165 5075 
Q 1165 5126 1248 5126 
Q 1267 5126 1370 5107 
Q 1677 5037 1677 4909 
L 1658 -512 
Q 1658 -640 1568 -640 
L 1472 -614 
Q 1184 -538 1184 -365 
Q 1184 -320 1213 -185 
Q 1242 -51 1242 128 
L 1274 4742 
z
M 6240 0 
Q 6240 -83 6086 -83 
L 2630 -154 
L 2522 -154 
Q 2323 -154 2246 -61 
Q 2170 32 2141 131 
Q 2112 230 2112 252 
Q 2112 275 2134 275 
Q 2157 275 2253 243 
Q 2349 211 2502 211 
L 3962 243 
L 3981 1562 
L 3258 1530 
Q 3149 1530 3056 1581 
Q 2963 1632 2893 1850 
Q 2880 1869 2880 1894 
Q 2880 1920 2902 1920 
Q 2925 1920 2973 1901 
Q 3021 1882 3181 1882 
L 3264 1882 
L 3981 1920 
L 3994 3027 
L 3098 2976 
Q 2848 2458 2534 2086 
Q 2400 1926 2352 1926 
Q 2304 1926 2304 1980 
Q 2304 2035 2355 2138 
Q 2714 2810 2880 3482 
Q 2957 3763 2957 3904 
L 2931 4102 
Q 2931 4160 2988 4160 
Q 3046 4160 3155 4109 
Q 3450 3955 3450 3834 
Q 3450 3814 3443 3802 
L 3264 3347 
L 4000 3386 
L 4013 4608 
Q 4013 4781 3968 4864 
Q 3923 4947 3923 4969 
Q 3923 4992 3993 4992 
Q 4064 4992 4205 4941 
Q 4346 4890 4381 4848 
Q 4416 4806 4416 4717 
L 4397 3411 
L 5126 3450 
Q 5261 3456 5318 3481 
Q 5376 3507 5421 3507 
Q 5466 3507 5549 3452 
Q 5632 3398 5696 3324 
Q 5760 3251 5760 3200 
Q 5760 3130 5619 3110 
L 4397 3046 
L 4384 1939 
L 4890 1965 
Q 5107 1978 5158 2000 
Q 5210 2022 5254 2022 
Q 5299 2022 5376 1971 
Q 5581 1830 5581 1709 
Q 5581 1632 5427 1619 
L 4378 1574 
L 4358 256 
L 5542 282 
Q 5722 282 5795 304 
Q 5869 326 5904 326 
Q 5939 326 6022 272 
Q 6106 218 6173 141 
Q 6240 64 6240 0 
z
M 1811 3789 
Q 1811 3878 2029 3930 
Q 2112 3930 2237 3632 
Q 2362 3334 2438 3088 
Q 2515 2842 2515 2806 
Q 2515 2771 2435 2720 
Q 2355 2669 2256 2669 
Q 2157 2669 2125 2784 
Q 2029 3238 1837 3693 
Q 1830 3725 1820 3747 
Q 1811 3770 1811 3789 
z
M 730 3789 
L 794 3782 
Q 896 3770 928 3734 
Q 960 3699 960 3629 
L 960 3603 
Q 922 2867 730 2067 
Q 698 1952 595 1952 
Q 493 1952 406 2006 
Q 320 2061 320 2105 
Q 320 2150 384 2349 
Q 538 2854 595 3642 
Q 602 3789 730 3789 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="line2d_23">
    <path d="M 57.040909 168.527867 
L 59.887557 169.605373 
L 62.734206 170.68288 
L 65.580854 171.760387 
L 68.427502 172.837894 
L 71.274151 173.9154 
L 74.120799 174.992907 
L 76.967447 176.070414 
L 79.814096 177.14792 
L 82.660744 178.225427 
L 85.507392 179.302934 
L 88.35404 180.38044 
L 91.200689 181.457947 
L 94.047337 182.535454 
L 96.893985 183.612961 
L 99.740634 184.690467 
L 102.587282 185.767974 
L 105.43393 186.845481 
L 108.280579 187.922987 
L 111.127227 189.000494 
L 113.973875 190.078001 
L 116.820523 191.155507 
L 119.667172 192.233014 
L 122.51382 193.310521 
L 125.360468 194.388028 
L 128.207117 195.465534 
L 131.053765 196.543041 
L 133.900413 197.620548 
L 136.747062 198.698054 
L 139.59371 199.775561 
L 142.440358 200.853068 
L 145.287006 201.930574 
L 148.133655 203.008081 
L 150.980303 204.085588 
L 153.826951 205.163095 
L 156.6736 206.240601 
L 159.520248 207.318108 
L 162.366896 208.395615 
L 165.213545 209.473121 
L 168.060193 210.550628 
L 170.906841 211.628135 
L 173.753489 212.705641 
L 176.600138 213.783148 
L 179.446786 214.860655 
L 182.293434 215.938162 
L 185.140083 217.015668 
L 187.986731 218.093175 
L 190.833379 219.170682 
L 193.680028 220.248188 
L 196.526676 221.325695 
L 199.373324 222.403202 
L 202.219972 223.480708 
L 205.066621 224.558215 
L 207.913269 225.635722 
L 210.759917 226.713229 
L 213.606566 227.790735 
L 216.453214 228.868242 
L 219.299862 229.945749 
L 222.146511 231.023255 
L 224.993159 232.100762 
L 227.839807 233.178269 
L 230.686455 234.255775 
L 233.533104 235.333282 
L 236.379752 236.410789 
L 239.2264 237.488296 
L 242.073049 238.565802 
L 244.919697 239.643309 
L 247.766345 240.720816 
L 250.612994 241.798322 
L 253.459642 242.875829 
L 256.30629 243.953336 
L 259.152938 245.030842 
L 261.999587 246.108349 
L 264.846235 247.185856 
L 267.692883 248.263363 
L 270.539532 249.340869 
L 273.38618 250.418376 
L 276.232828 251.495883 
L 279.079477 252.573389 
L 281.926125 253.650896 
L 284.772773 254.728403 
L 287.619421 255.805909 
L 290.46607 256.883416 
L 293.312718 257.960923 
L 296.159366 259.03843 
L 299.006015 260.115936 
L 301.852663 261.193443 
L 304.699311 262.27095 
L 307.54596 263.348456 
L 310.392608 264.425963 
L 313.239256 265.50347 
L 316.085904 266.580976 
L 318.932553 267.658483 
L 321.779201 268.73599 
L 324.625849 269.813497 
L 327.472498 270.891003 
L 330.319146 271.96851 
L 333.165794 273.046017 
L 336.012443 274.123523 
L 338.859091 275.20103 
" clip-path="url(#p5541723dd9)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_3">
    <path d="M 42.95 417.412174 
L 42.95 128.16 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_4">
    <path d="M 42.95 417.412174 
L 352.95 417.412174 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_14">
    <!-- r = -0.495, p = 0.004** -->
    <g transform="translate(131.037812 122.16) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-72" d="M 589 115 
L 602 678 
L 602 2349 
L 582 3034 
Q 582 3098 697 3098 
Q 813 3098 931 3050 
Q 1050 3002 1050 2928 
Q 1050 2854 1040 2777 
Q 1030 2701 1030 2592 
Q 1158 2803 1395 2956 
Q 1632 3110 1894 3110 
Q 2157 3110 2323 3046 
Q 2490 2982 2595 2892 
Q 2701 2803 2701 2713 
Q 2701 2624 2627 2509 
Q 2554 2394 2486 2394 
Q 2419 2394 2381 2458 
Q 2317 2579 2179 2636 
Q 2042 2694 1946 2694 
Q 1677 2694 1446 2528 
Q 1114 2285 1050 1702 
Q 1024 1472 1024 678 
L 1056 0 
Q 1056 -64 941 -64 
Q 826 -64 707 -16 
Q 589 32 589 115 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-20" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-3d" d="M 486 1197 
Q 678 1184 1011 1184 
L 2848 1184 
Q 3034 1184 3302 1203 
L 3309 1203 
Q 3379 1203 3417 1120 
Q 3456 1037 3456 918 
Q 3456 800 3360 800 
L 2842 813 
L 1005 813 
Q 634 813 518 794 
L 512 794 
Q 454 794 419 877 
Q 384 960 384 1043 
Q 384 1197 486 1197 
z
M 486 2400 
Q 678 2387 1011 2387 
L 2848 2387 
Q 3034 2387 3302 2406 
L 3309 2406 
Q 3379 2406 3417 2323 
Q 3456 2240 3456 2121 
Q 3456 2003 3360 2003 
L 2842 2016 
L 1005 2016 
Q 634 2016 518 1997 
L 512 1997 
Q 454 1997 419 2080 
Q 384 2163 384 2246 
Q 384 2400 486 2400 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2d" d="M 1523 1792 
Q 1709 1792 1978 1805 
L 1984 1805 
Q 2035 1805 2073 1728 
Q 2112 1651 2112 1529 
Q 2112 1408 2016 1408 
L 1517 1421 
L 749 1421 
Q 390 1421 275 1402 
L 269 1402 
Q 211 1402 176 1485 
Q 141 1568 141 1651 
Q 141 1805 243 1805 
Q 435 1792 755 1792 
L 1523 1792 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-39" d="M 2963 3667 
Q 2886 3629 2874 3629 
Q 2810 3629 2774 3712 
Q 2739 3795 2650 3904 
Q 2432 4160 2035 4160 
Q 1536 4160 1165 3725 
Q 819 3302 819 2899 
Q 819 2080 1453 2080 
Q 2003 2080 2394 2502 
Q 2662 2790 2829 3213 
Q 2906 3405 2925 3504 
Q 2944 3603 2963 3667 
z
M 2413 -83 
Q 2246 -83 2246 58 
Q 2246 109 2310 416 
Q 2374 723 2448 1081 
Q 2522 1440 2570 1670 
Q 2618 1901 2643 2045 
Q 2669 2189 2688 2285 
Q 2464 2010 2131 1837 
Q 1798 1664 1481 1664 
Q 1165 1664 918 1801 
Q 672 1939 525 2201 
Q 378 2464 378 2810 
Q 378 3482 864 4019 
Q 1350 4557 2003 4557 
Q 2528 4557 2854 4250 
Q 2982 4122 3034 4026 
Q 3066 4166 3075 4268 
Q 3085 4371 3174 4371 
L 3187 4371 
Q 3322 4358 3424 4272 
Q 3526 4186 3526 4134 
L 3526 4115 
Q 3302 3552 2893 1139 
Q 2778 442 2733 58 
Q 2720 -70 2438 -83 
L 2413 -83 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2c" d="M 730 -666 
Q 646 -666 646 -576 
Q 646 -512 720 -464 
Q 794 -416 902 -285 
Q 1011 -154 1050 -51 
Q 954 -13 915 6 
Q 749 154 749 317 
Q 749 480 861 566 
Q 973 653 1117 653 
Q 1261 653 1360 531 
Q 1459 410 1478 243 
L 1478 192 
Q 1478 -90 1200 -378 
Q 922 -666 730 -666 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-70" d="M 1005 646 
Q 1030 659 1050 659 
Q 1120 659 1216 518 
Q 1389 262 1766 262 
Q 2144 262 2512 672 
Q 2880 1082 2880 1738 
Q 2880 2394 2534 2624 
Q 2387 2726 2169 2726 
Q 1952 2726 1712 2585 
Q 1472 2445 1267 2147 
Q 1062 1850 1024 1338 
Q 1005 1069 1005 646 
z
M 576 -922 
L 589 -352 
L 589 2355 
Q 589 2694 570 3021 
Q 570 3085 685 3085 
Q 800 3085 921 3037 
Q 1043 2989 1043 2906 
Q 1043 2867 1036 2764 
Q 1030 2662 1027 2550 
Q 1024 2438 1018 2362 
Q 1274 2797 1571 2953 
Q 1869 3110 2221 3110 
Q 2701 3110 3008 2771 
Q 3315 2432 3315 1837 
Q 3315 1242 3113 813 
Q 2912 384 2566 137 
Q 2221 -109 1786 -109 
Q 1562 -109 1338 0 
Q 1114 109 1011 243 
L 1011 -358 
L 1043 -1037 
Q 1043 -1101 928 -1101 
Q 813 -1101 694 -1053 
Q 576 -1005 576 -922 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-2a" d="M 1805 4416 
L 1798 4326 
Q 1786 4186 1770 4026 
Q 1754 3866 1741 3699 
Q 2176 3898 2297 3974 
Q 2419 4051 2496 4051 
Q 2573 4051 2627 3945 
Q 2682 3840 2682 3738 
Q 2682 3610 2490 3571 
L 1837 3437 
Q 2118 3072 2208 2982 
L 2330 2854 
Q 2368 2822 2368 2768 
Q 2368 2714 2275 2614 
Q 2182 2515 2083 2515 
Q 1984 2515 1949 2569 
Q 1914 2624 1882 2701 
Q 1850 2778 1766 2934 
Q 1683 3091 1606 3251 
Q 1408 2918 1350 2764 
Q 1293 2611 1257 2556 
Q 1222 2502 1136 2502 
Q 1050 2502 944 2585 
Q 838 2669 838 2739 
Q 838 2810 909 2893 
Q 1190 3219 1338 3411 
Q 1005 3450 845 3459 
Q 685 3469 611 3488 
Q 538 3507 538 3616 
Q 538 3725 579 3837 
Q 621 3949 717 3949 
Q 749 3949 979 3853 
Q 1210 3757 1440 3680 
L 1331 4422 
Q 1331 4570 1574 4570 
L 1594 4570 
Q 1728 4570 1766 4525 
Q 1805 4480 1805 4416 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="955.199707"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1015.199692"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1065.199677"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_5">
    <path d="M 445.95 417.412174 
L 755.95 417.412174 
L 755.95 128.16 
L 445.95 128.16 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_2">
    <defs>
     <path id="mcd18c402c3" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #d95319; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p4a1cad7ee5)">
     <use xlink:href="#mcd18c402c3" x="576.655329" y="283.446486" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="498.912382" y="404.264348" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="741.859091" y="297.660353" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="528.065987" y="318.981152" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="469.758777" y="276.339553" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="528.065987" y="262.125687" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="644.680408" y="318.981152" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="605.808934" y="262.125687" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="702.987618" y="226.591022" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="673.834013" y="290.55342" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="566.937461" y="212.377156" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="479.476646" y="347.408884" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="547.501724" y="233.697955" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="576.655329" y="205.270223" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="673.834013" y="375.836616" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="683.551881" y="191.056357" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="644.680408" y="205.270223" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="596.091066" y="226.591022" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="566.937461" y="176.842491" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="460.040909" y="361.62275" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="518.348119" y="311.874219" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="518.348119" y="318.981152" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="508.630251" y="269.23262" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="528.065987" y="311.874219" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="508.630251" y="283.446486" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="596.091066" y="247.911821" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="528.065987" y="290.55342" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="615.526803" y="297.660353" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="596.091066" y="141.307826" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="547.501724" y="162.628625" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="664.116144" y="311.874219" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="557.219592" y="212.377156" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_2">
    <path d="M 460.040909 248.663283 
L 460.040909 329.026046 
L 462.887557 327.475788 
L 465.734206 325.915965 
L 468.580854 324.346775 
L 471.427502 322.875757 
L 474.274151 321.452009 
L 477.120799 320.200311 
L 479.967447 318.871535 
L 482.814096 317.918146 
L 485.660744 316.866032 
L 488.507392 315.305003 
L 491.35404 314.332911 
L 494.200689 312.624605 
L 497.047337 311.34006 
L 499.893985 310.37373 
L 502.740634 309.379918 
L 505.587282 308.476499 
L 508.43393 307.52379 
L 511.280579 306.563228 
L 514.127227 305.608977 
L 516.973875 304.515157 
L 519.820523 303.493417 
L 522.667172 302.411873 
L 525.51382 301.787664 
L 528.360468 300.661745 
L 531.207117 299.678975 
L 534.053765 298.861127 
L 536.900413 298.063465 
L 539.747062 297.411985 
L 542.59371 296.635136 
L 545.440358 295.835203 
L 548.287006 295.440579 
L 551.133655 294.786153 
L 553.980303 294.191205 
L 556.826951 293.899226 
L 559.6736 293.76475 
L 562.520248 293.545605 
L 565.366896 292.56731 
L 568.213545 291.894109 
L 571.060193 291.301881 
L 573.906841 291.286453 
L 576.753489 290.688614 
L 579.600138 290.797113 
L 582.446786 290.63146 
L 585.293434 290.272539 
L 588.140083 290.047026 
L 590.986731 289.906799 
L 593.833379 289.651861 
L 596.680028 288.971625 
L 599.526676 288.650139 
L 602.373324 288.385561 
L 605.219972 287.611539 
L 608.066621 287.128224 
L 610.913269 286.988806 
L 613.759917 286.849387 
L 616.606566 286.709969 
L 619.453214 286.570551 
L 622.299862 286.437999 
L 625.146511 286.310958 
L 627.993159 286.180695 
L 630.839807 286.043914 
L 633.686455 285.907132 
L 636.533104 285.911734 
L 639.379752 285.998856 
L 642.2264 285.980965 
L 645.073049 286.1021 
L 647.919697 286.21832 
L 650.766345 286.0808 
L 653.612994 285.831681 
L 656.459642 285.581428 
L 659.30629 285.330985 
L 662.152938 285.358374 
L 664.999587 285.180355 
L 667.846235 285.187255 
L 670.692883 285.327303 
L 673.539532 285.455002 
L 676.38618 285.585998 
L 679.232828 285.474561 
L 682.079477 285.356176 
L 684.926125 285.370146 
L 687.772773 285.39972 
L 690.619421 285.272927 
L 693.46607 285.690248 
L 696.312718 285.976285 
L 699.159366 286.146816 
L 702.006015 286.041249 
L 704.852663 286.207406 
L 707.699311 286.611166 
L 710.54596 287.009739 
L 713.392608 287.404906 
L 716.239256 287.800073 
L 719.085904 288.19524 
L 721.932553 288.590407 
L 724.779201 288.96431 
L 727.625849 289.164309 
L 730.472498 289.364308 
L 733.319146 289.557959 
L 736.165794 289.751047 
L 739.012443 289.944136 
L 741.859091 290.137224 
L 741.859091 175.036901 
L 741.859091 175.036901 
L 739.012443 176.612203 
L 736.165794 178.187505 
L 733.319146 179.763886 
L 730.472498 181.182575 
L 727.625849 182.563386 
L 724.779201 184.102384 
L 721.932553 185.407016 
L 719.085904 186.963095 
L 716.239256 188.519368 
L 713.392608 190.041652 
L 710.54596 191.391134 
L 707.699311 192.740616 
L 704.852663 194.090099 
L 702.006015 195.439581 
L 699.159366 196.789064 
L 696.312718 198.138546 
L 693.46607 199.488028 
L 690.619421 200.837511 
L 687.772773 202.186993 
L 684.926125 203.536476 
L 682.079477 204.885958 
L 679.232828 206.23544 
L 676.38618 207.568147 
L 673.539532 208.882615 
L 670.692883 210.197083 
L 667.846235 211.511551 
L 664.999587 212.826019 
L 662.152938 214.155648 
L 659.30629 215.527696 
L 656.459642 216.899743 
L 653.612994 218.271824 
L 650.766345 219.611039 
L 647.919697 220.953711 
L 645.073049 222.082026 
L 642.2264 223.330458 
L 639.379752 224.552632 
L 636.533104 225.78712 
L 633.686455 227.021199 
L 630.839807 228.344239 
L 627.993159 229.743815 
L 625.146511 230.82152 
L 622.299862 231.962972 
L 619.453214 233.19404 
L 616.606566 234.434352 
L 613.759917 235.669229 
L 610.913269 236.929761 
L 608.066621 238.128528 
L 605.219972 239.017959 
L 602.373324 240.144237 
L 599.526676 241.51325 
L 596.680028 242.527305 
L 593.833379 243.545279 
L 590.986731 244.450842 
L 588.140083 245.429538 
L 585.293434 246.689873 
L 582.446786 247.544689 
L 579.600138 247.986854 
L 576.753489 248.875932 
L 573.906841 249.163082 
L 571.060193 249.309596 
L 568.213545 249.745177 
L 565.366896 250.1902 
L 562.520248 250.22547 
L 559.6736 250.605276 
L 556.826951 250.847726 
L 553.980303 251.28045 
L 551.133655 251.450591 
L 548.287006 251.250292 
L 545.440358 251.188867 
L 542.59371 251.055056 
L 539.747062 251.083297 
L 536.900413 251.304132 
L 534.053765 251.540099 
L 531.207117 251.813858 
L 528.360468 251.991299 
L 525.51382 252.20434 
L 522.667172 252.267757 
L 519.820523 252.042155 
L 516.973875 251.536378 
L 514.127227 251.020185 
L 511.280579 250.705387 
L 508.43393 249.956167 
L 505.587282 250.076951 
L 502.740634 250.284503 
L 499.893985 250.446016 
L 497.047337 250.322825 
L 494.200689 250.407548 
L 491.35404 250.464012 
L 488.507392 250.295848 
L 485.660744 250.127684 
L 482.814096 249.95952 
L 479.967447 249.791356 
L 477.120799 249.624202 
L 474.274151 249.461568 
L 471.427502 249.298935 
L 468.580854 249.136302 
L 465.734206 248.973668 
L 462.887557 248.832997 
L 460.040909 248.663283 
z
" clip-path="url(#p4a1cad7ee5)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_7">
     <g id="line2d_24">
      <path d="M 479.476646 417.412174 
L 479.476646 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_25">
      <g>
       <use xlink:href="#md81fabead9" x="479.476646" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 30 -->
      <g transform="translate(473.476646 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_26">
      <path d="M 528.065987 417.412174 
L 528.065987 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_27">
      <g>
       <use xlink:href="#md81fabead9" x="528.065987" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 35 -->
      <g transform="translate(522.065987 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_28">
      <path d="M 576.655329 417.412174 
L 576.655329 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_29">
      <g>
       <use xlink:href="#md81fabead9" x="576.655329" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 40 -->
      <g transform="translate(570.655329 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_10">
     <g id="line2d_30">
      <path d="M 625.244671 417.412174 
L 625.244671 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_31">
      <g>
       <use xlink:href="#md81fabead9" x="625.244671" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 45 -->
      <g transform="translate(619.244671 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_32">
      <path d="M 673.834013 417.412174 
L 673.834013 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_33">
      <g>
       <use xlink:href="#md81fabead9" x="673.834013" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 50 -->
      <g transform="translate(667.834013 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_34">
      <path d="M 722.423354 417.412174 
L 722.423354 128.16 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_35">
      <g>
       <use xlink:href="#md81fabead9" x="722.423354" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 55 -->
      <g transform="translate(716.423354 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_21">
     <!-- 特质焦虑水平 -->
     <g transform="translate(570.95 444.932486) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-7279"/>
      <use xlink:href="#LXGWWenKai-Regular-8d28" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-6c34" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5e73" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_6">
     <g id="line2d_36">
      <path d="M 445.95 411.371281 
L 755.95 411.371281 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_37">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="411.371281" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_22">
      <!-- -10 -->
      <g transform="translate(423.45 414.906437) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_38">
      <path d="M 445.95 375.836616 
L 755.95 375.836616 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_39">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="375.836616" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- -5 -->
      <g transform="translate(429.45 379.371772) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="34.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_40">
      <path d="M 445.95 340.301951 
L 755.95 340.301951 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_41">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="340.301951" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 0 -->
      <g transform="translate(432.95 343.837107) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_42">
      <path d="M 445.95 304.767286 
L 755.95 304.767286 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_43">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="304.767286" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 5 -->
      <g transform="translate(432.95 308.302442) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_44">
      <path d="M 445.95 269.23262 
L 755.95 269.23262 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_45">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="269.23262" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 10 -->
      <g transform="translate(426.95 272.767777) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_46">
      <path d="M 445.95 233.697955 
L 755.95 233.697955 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_47">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="233.697955" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 15 -->
      <g transform="translate(426.95 237.233112) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_48">
      <path d="M 445.95 198.16329 
L 755.95 198.16329 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_49">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="198.16329" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 20 -->
      <g transform="translate(426.95 201.698446) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_50">
      <path d="M 445.95 162.628625 
L 755.95 162.628625 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_51">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="162.628625" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 25 -->
      <g transform="translate(426.95 166.163781) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_30">
     <!-- 状态焦虑变化 -->
     <g transform="translate(417.729688 302.786087) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-72b6" d="M 4294 4653 
Q 4294 3642 4205 2886 
L 5293 2938 
Q 5427 2950 5507 2988 
Q 5587 3027 5635 3027 
Q 5683 3027 5763 2963 
Q 5843 2899 5900 2819 
Q 5958 2739 5958 2701 
Q 5958 2624 5805 2611 
L 4403 2534 
Q 5056 762 6266 -122 
Q 6330 -166 6330 -198 
Q 6330 -230 6272 -294 
Q 6125 -461 6010 -461 
Q 5965 -461 5926 -422 
Q 4678 621 4122 2291 
L 4045 1920 
Q 3859 1107 3366 448 
Q 2970 -90 2586 -346 
Q 2432 -448 2381 -448 
Q 2330 -448 2330 -397 
Q 2330 -346 2445 -224 
Q 3507 858 3770 2502 
L 3053 2464 
L 2906 2458 
Q 2739 2458 2675 2509 
Q 2490 2688 2490 2829 
Q 2490 2861 2509 2861 
Q 2547 2861 2630 2838 
Q 2714 2816 2816 2816 
L 3814 2867 
Q 3878 3443 3878 4403 
L 3878 4710 
Q 3878 4870 3820 4963 
Q 3763 5056 3763 5088 
Q 3763 5120 3840 5120 
Q 3917 5120 4067 5059 
Q 4218 4998 4256 4960 
Q 4294 4922 4294 4851 
L 4294 4653 
z
M 1837 1760 
Q 1402 1267 954 883 
Q 813 755 745 755 
Q 678 755 585 796 
Q 493 838 384 940 
Q 275 1043 275 1084 
Q 275 1126 320 1133 
Q 794 1203 1837 2074 
L 1875 4563 
Q 1875 4794 1814 4880 
Q 1754 4966 1754 4995 
Q 1754 5024 1830 5024 
Q 1907 5024 2054 4976 
Q 2202 4928 2240 4883 
Q 2278 4838 2278 4749 
L 2208 -416 
Q 2208 -595 2099 -595 
Q 2048 -595 1965 -556 
Q 1882 -518 1821 -451 
Q 1760 -384 1760 -326 
Q 1760 -269 1779 -176 
Q 1798 -83 1811 230 
L 1837 1760 
z
M 5056 3270 
Q 4838 3731 4563 4070 
Q 4531 4115 4531 4166 
Q 4531 4218 4630 4282 
Q 4730 4346 4774 4346 
Q 4819 4346 4902 4259 
Q 4986 4173 5078 4041 
Q 5171 3910 5254 3776 
Q 5338 3642 5392 3542 
Q 5446 3443 5446 3401 
Q 5446 3360 5356 3270 
Q 5267 3181 5180 3181 
Q 5094 3181 5056 3270 
z
M 1312 2432 
Q 1235 2432 1197 2534 
Q 941 3213 653 3686 
Q 627 3718 627 3766 
Q 627 3814 704 3872 
Q 781 3930 857 3930 
Q 934 3930 979 3853 
Q 1312 3328 1449 3001 
Q 1587 2675 1587 2627 
Q 1587 2579 1523 2531 
Q 1459 2483 1392 2457 
Q 1325 2432 1312 2432 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6001" d="M 3354 2125 
Q 3040 2522 2726 2771 
Q 2630 2854 2630 2908 
Q 2630 2963 2704 3030 
Q 2778 3098 2813 3098 
Q 2848 3098 3014 2982 
Q 3181 2867 3469 2592 
Q 3757 2317 3757 2246 
Q 3757 2176 3664 2083 
Q 3571 1990 3520 1990 
Q 3469 1990 3354 2125 
z
M 1075 288 
Q 973 45 893 -115 
Q 813 -275 742 -275 
Q 736 -275 666 -250 
Q 461 -173 461 -51 
Q 461 0 573 182 
Q 685 365 829 707 
Q 973 1050 1062 1389 
Q 1094 1498 1171 1498 
Q 1222 1498 1324 1466 
Q 1427 1434 1427 1357 
Q 1427 1158 1075 288 
z
M 4352 1030 
Q 4429 1030 4518 858 
Q 4698 480 5011 45 
Q 5120 -115 5120 -166 
Q 5120 -218 5075 -288 
Q 4960 -474 4422 -474 
Q 3264 -474 2573 -77 
Q 2138 173 1920 794 
Q 1830 1043 1798 1219 
Q 1766 1395 1766 1408 
Q 1766 1549 2010 1549 
Q 2131 1549 2150 1408 
Q 2221 1037 2352 745 
Q 2483 454 2780 281 
Q 3078 109 3465 22 
Q 3853 -64 4214 -64 
Q 4576 -64 4576 -25 
Q 4576 13 4435 413 
Q 4294 813 4294 921 
Q 4294 1030 4352 1030 
z
M 4800 1376 
Q 4717 1446 4717 1504 
Q 4717 1562 4787 1635 
Q 4858 1709 4906 1709 
Q 4954 1709 5082 1613 
Q 5210 1517 5379 1369 
Q 5549 1222 5705 1065 
Q 5862 909 5968 784 
Q 6074 659 6074 614 
Q 6074 570 6029 509 
Q 5984 448 5926 403 
Q 5869 358 5824 358 
Q 5779 358 5516 668 
Q 5254 979 4800 1376 
z
M 3539 704 
Q 3174 1139 2816 1440 
Q 2720 1517 2720 1574 
Q 2720 1632 2781 1702 
Q 2842 1773 2883 1773 
Q 2925 1773 3097 1657 
Q 3270 1542 3577 1241 
Q 3885 941 3885 854 
Q 3885 768 3801 694 
Q 3718 621 3667 621 
Q 3616 621 3539 704 
z
M 5152 4096 
L 5299 4141 
Q 5421 4141 5581 3962 
Q 5638 3891 5638 3834 
Q 5638 3750 5504 3738 
L 3891 3642 
Q 4352 3168 4902 2745 
Q 5453 2323 6246 1933 
Q 6355 1875 6355 1830 
Q 6355 1786 6278 1725 
Q 6202 1664 6115 1619 
Q 6029 1574 5984 1574 
Q 5939 1574 5901 1600 
Q 5062 2048 4476 2547 
Q 3891 3046 3373 3610 
L 2778 3571 
Q 2298 2816 1613 2214 
Q 1082 1741 563 1440 
Q 365 1325 320 1325 
Q 275 1325 275 1382 
Q 275 1440 403 1542 
Q 1626 2528 2266 3546 
L 1248 3482 
L 1133 3475 
Q 979 3475 921 3532 
Q 864 3590 813 3696 
Q 762 3802 762 3840 
Q 762 3878 787 3878 
Q 813 3878 867 3859 
Q 922 3840 1062 3840 
L 1114 3840 
L 2483 3923 
L 2586 4115 
Q 2797 4550 2870 4816 
Q 2944 5082 2944 5107 
L 2925 5248 
Q 2925 5325 2995 5325 
Q 3142 5325 3366 5210 
Q 3462 5158 3462 5081 
Q 3462 5005 3331 4685 
Q 3200 4365 3078 4122 
L 2989 3955 
L 5018 4077 
Q 5094 4083 5152 4096 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-53d8" d="M 2842 710 
Q 1952 1357 1952 1504 
Q 1952 1568 2035 1635 
Q 2118 1702 2172 1702 
Q 2227 1702 2304 1626 
Q 2714 1254 3174 947 
Q 3706 1382 4077 1869 
L 2010 1754 
Q 1965 1747 1926 1747 
L 1850 1747 
Q 1696 1747 1629 1824 
Q 1562 1901 1523 1993 
Q 1485 2086 1485 2115 
Q 1485 2144 1510 2144 
Q 1536 2144 1609 2128 
Q 1683 2112 1843 2112 
L 1939 2112 
L 4275 2234 
L 4371 2240 
Q 4461 2240 4573 2157 
Q 4685 2074 4685 2000 
Q 4685 1926 4627 1888 
Q 4570 1850 4544 1818 
Q 4070 1190 3507 736 
Q 4576 115 5830 -128 
Q 5965 -154 5965 -198 
L 5914 -282 
Q 5747 -531 5600 -531 
L 5382 -467 
Q 4762 -301 4173 -38 
Q 3584 224 3168 486 
Q 2368 -64 1402 -346 
Q 1024 -454 800 -492 
Q 576 -531 570 -531 
Q 454 -531 454 -486 
Q 454 -416 678 -333 
Q 1280 -109 1821 134 
Q 2362 378 2842 710 
z
M 5376 2586 
Q 4941 3002 4371 3392 
Q 4288 3450 4288 3491 
Q 4288 3533 4349 3625 
Q 4410 3718 4464 3718 
Q 4518 3718 4768 3564 
Q 5018 3411 5414 3094 
Q 5811 2778 5811 2688 
Q 5811 2630 5731 2534 
Q 5651 2438 5590 2438 
Q 5530 2438 5376 2586 
z
M 2003 3552 
Q 2074 3469 2074 3424 
Q 2074 3322 1626 2922 
Q 1178 2522 794 2330 
Q 678 2272 627 2272 
Q 576 2272 576 2304 
Q 576 2355 698 2458 
Q 1587 3245 1638 3648 
Q 1645 3750 1709 3750 
Q 1811 3750 2003 3552 
z
M 2931 5171 
Q 3398 5171 3398 5018 
L 3398 4390 
L 5094 4493 
Q 5306 4512 5370 4531 
Q 5434 4550 5485 4550 
Q 5536 4550 5626 4499 
Q 5837 4371 5837 4268 
Q 5837 4166 5728 4154 
L 4000 4058 
L 3930 4051 
L 3930 2573 
Q 3930 2381 3808 2381 
Q 3750 2381 3651 2441 
Q 3552 2502 3504 2550 
Q 3456 2598 3456 2669 
Q 3507 3021 3507 3238 
L 3507 4026 
L 2938 3994 
L 2803 3987 
L 2803 2547 
Q 2803 2355 2682 2355 
Q 2624 2355 2525 2416 
Q 2426 2477 2378 2525 
Q 2330 2573 2330 2643 
Q 2381 2893 2381 3213 
L 2381 3968 
L 1216 3898 
Q 1088 3885 960 3885 
Q 832 3885 761 3955 
Q 691 4026 646 4118 
Q 602 4211 602 4246 
Q 602 4282 646 4282 
Q 832 4250 986 4250 
L 1069 4250 
L 2976 4365 
L 2970 4800 
Q 2970 4960 2918 5027 
Q 2867 5094 2867 5132 
Q 2867 5171 2931 5171 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5316" d="M 1466 2912 
Q 1146 2496 870 2240 
Q 378 1773 262 1773 
Q 224 1773 224 1817 
Q 224 1862 377 2044 
Q 531 2227 886 2707 
Q 1242 3187 1670 3907 
Q 2099 4627 2099 4851 
L 2099 4902 
Q 2099 4922 2093 4947 
L 2093 4966 
Q 2093 5030 2160 5030 
Q 2227 5030 2330 4979 
Q 2579 4838 2579 4736 
Q 2579 4659 2323 4198 
Q 2067 3738 1882 3450 
L 1875 -390 
Q 1875 -525 1747 -525 
Q 1734 -525 1644 -502 
Q 1555 -480 1472 -416 
Q 1389 -352 1389 -278 
Q 1389 -205 1411 -112 
Q 1434 -19 1434 211 
L 1466 2912 
z
M 3309 1811 
Q 2470 1344 2272 1344 
Q 2208 1344 2208 1382 
Q 2208 1446 2419 1574 
Q 2963 1901 3315 2163 
L 3328 4595 
Q 3328 4749 3277 4832 
Q 3226 4915 3226 4944 
Q 3226 4973 3302 4973 
Q 3379 4973 3533 4928 
Q 3763 4877 3763 4742 
L 3750 2496 
Q 4410 3040 4915 3648 
Q 4992 3725 5011 3865 
Q 5030 4006 5068 4006 
Q 5107 4006 5184 3936 
Q 5414 3738 5414 3578 
Q 5414 3533 5389 3507 
Q 4640 2650 3744 2080 
L 3738 544 
Q 3738 326 3795 256 
Q 3853 186 4019 154 
Q 4346 102 4624 102 
Q 4902 102 5350 166 
Q 5466 186 5542 282 
Q 5683 461 5805 1306 
Q 5824 1453 5862 1523 
Q 5901 1594 5933 1594 
Q 6054 1594 6054 960 
Q 6054 634 6029 371 
Q 5971 -179 5491 -230 
Q 4998 -288 4537 -288 
Q 4077 -288 3789 -221 
Q 3501 -154 3401 6 
Q 3302 166 3302 454 
L 3309 1811 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-53d8" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5316" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_52">
    <path d="M 460.040909 292.286975 
L 462.887557 291.740465 
L 465.734206 291.193955 
L 468.580854 290.647444 
L 471.427502 290.100934 
L 474.274151 289.554424 
L 477.120799 289.007913 
L 479.967447 288.461403 
L 482.814096 287.914892 
L 485.660744 287.368382 
L 488.507392 286.821872 
L 491.35404 286.275361 
L 494.200689 285.728851 
L 497.047337 285.182341 
L 499.893985 284.63583 
L 502.740634 284.08932 
L 505.587282 283.54281 
L 508.43393 282.996299 
L 511.280579 282.449789 
L 514.127227 281.903278 
L 516.973875 281.356768 
L 519.820523 280.810258 
L 522.667172 280.263747 
L 525.51382 279.717237 
L 528.360468 279.170727 
L 531.207117 278.624216 
L 534.053765 278.077706 
L 536.900413 277.531195 
L 539.747062 276.984685 
L 542.59371 276.438175 
L 545.440358 275.891664 
L 548.287006 275.345154 
L 551.133655 274.798644 
L 553.980303 274.252133 
L 556.826951 273.705623 
L 559.6736 273.159112 
L 562.520248 272.612602 
L 565.366896 272.066092 
L 568.213545 271.519581 
L 571.060193 270.973071 
L 573.906841 270.426561 
L 576.753489 269.88005 
L 579.600138 269.33354 
L 582.446786 268.787029 
L 585.293434 268.240519 
L 588.140083 267.694009 
L 590.986731 267.147498 
L 593.833379 266.600988 
L 596.680028 266.054478 
L 599.526676 265.507967 
L 602.373324 264.961457 
L 605.219972 264.414947 
L 608.066621 263.868436 
L 610.913269 263.321926 
L 613.759917 262.775415 
L 616.606566 262.228905 
L 619.453214 261.682395 
L 622.299862 261.135884 
L 625.146511 260.589374 
L 627.993159 260.042864 
L 630.839807 259.496353 
L 633.686455 258.949843 
L 636.533104 258.403332 
L 639.379752 257.856822 
L 642.2264 257.310312 
L 645.073049 256.763801 
L 647.919697 256.217291 
L 650.766345 255.670781 
L 653.612994 255.12427 
L 656.459642 254.57776 
L 659.30629 254.031249 
L 662.152938 253.484739 
L 664.999587 252.938229 
L 667.846235 252.391718 
L 670.692883 251.845208 
L 673.539532 251.298698 
L 676.38618 250.752187 
L 679.232828 250.205677 
L 682.079477 249.659166 
L 684.926125 249.112656 
L 687.772773 248.566146 
L 690.619421 248.019635 
L 693.46607 247.473125 
L 696.312718 246.926615 
L 699.159366 246.380104 
L 702.006015 245.833594 
L 704.852663 245.287084 
L 707.699311 244.740573 
L 710.54596 244.194063 
L 713.392608 243.647552 
L 716.239256 243.101042 
L 719.085904 242.554532 
L 721.932553 242.008021 
L 724.779201 241.461511 
L 727.625849 240.915001 
L 730.472498 240.36849 
L 733.319146 239.82198 
L 736.165794 239.275469 
L 739.012443 238.728959 
L 741.859091 238.182449 
" clip-path="url(#p4a1cad7ee5)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_6">
    <path d="M 445.95 417.412174 
L 445.95 128.16 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 445.95 417.412174 
L 755.95 417.412174 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_31">
    <!-- r = 0.221, p = 0.224 -->
    <g transform="translate(542.137812 122.16) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="232.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="327.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="387.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="447.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="575.199829"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="670.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="765.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="860.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="920.199722"/>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_8">
    <path d="M 848.95 417.412174 
L 1158.95 417.412174 
L 1158.95 128.16 
L 848.95 128.16 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m778fefa8e4" d="M 0 3.535534 
C 0.937635 3.535534 1.836992 3.163008 2.5 2.5 
C 3.163008 1.836992 3.535534 0.937635 3.535534 0 
C 3.535534 -0.937635 3.163008 -1.836992 2.5 -2.5 
C 1.836992 -3.163008 0.937635 -3.535534 0 -3.535534 
C -0.937635 -3.535534 -1.836992 -3.163008 -2.5 -2.5 
C -3.163008 -1.836992 -3.535534 -0.937635 -3.535534 0 
C -3.535534 0.937635 -3.163008 1.836992 -2.5 2.5 
C -1.836992 3.163008 -0.937635 3.535534 0 3.535534 
z
" style="stroke: #edb120; stroke-opacity: 0.6"/>
    </defs>
    <g clip-path="url(#p5189e6b98e)">
     <use xlink:href="#m778fefa8e4" x="863.040909" y="319.993509" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1065.844647" y="245.636886" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="863.040909" y="270.422427" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1084.281351" y="295.207968" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1134.323832" y="404.264348" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1092.182795" y="384.435915" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1068.478462" y="285.293751" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1086.915166" y="300.165076" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1050.041759" y="300.165076" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1055.309388" y="329.907725" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1105.351869" y="329.907725" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1113.253314" y="349.736158" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1094.81661" y="310.079292" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1063.210833" y="319.993509" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1071.112277" y="250.593994" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1086.915166" y="315.036401" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1068.478462" y="334.864833" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1142.225276" y="334.864833" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1092.182795" y="344.77905" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1139.591461" y="300.165076" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1131.690017" y="389.393023" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1102.718054" y="334.864833" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1118.520943" y="334.864833" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1144.859091" y="374.521699" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1100.08424" y="354.693266" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1113.253314" y="329.907725" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1102.718054" y="354.693266" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1105.351869" y="364.607482" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1079.013721" y="354.693266" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1055.309388" y="399.30724" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1076.379907" y="324.950617" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1089.54898" y="230.765561" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_3">
    <path d="M 863.040909 141.307826 
L 863.040909 311.580196 
L 865.887557 311.82686 
L 868.734206 312.073524 
L 871.580854 312.320189 
L 874.427502 312.566853 
L 877.274151 312.815935 
L 880.120799 313.283162 
L 882.967447 313.718529 
L 885.814096 313.881232 
L 888.660744 314.063166 
L 891.507392 314.538237 
L 894.35404 314.865556 
L 897.200689 315.141713 
L 900.047337 315.434513 
L 902.893985 315.696216 
L 905.740634 315.949913 
L 908.587282 316.198761 
L 911.43393 316.458376 
L 914.280579 316.702779 
L 917.127227 316.9621 
L 919.973875 317.221421 
L 922.820523 317.476117 
L 925.667172 317.72751 
L 928.51382 317.978824 
L 931.360468 318.230137 
L 934.207117 318.481451 
L 937.053765 318.757351 
L 939.900413 319.054883 
L 942.747062 319.270648 
L 945.59371 319.622012 
L 948.440358 319.838466 
L 951.287006 320.332026 
L 954.133655 320.690031 
L 956.980303 321.044877 
L 959.826951 321.399723 
L 962.6736 321.736862 
L 965.520248 321.89211 
L 968.366896 322.099649 
L 971.213545 322.433126 
L 974.060193 322.862774 
L 976.906841 323.195579 
L 979.753489 323.534035 
L 982.600138 323.806543 
L 985.446786 324.097227 
L 988.293434 324.357224 
L 991.140083 324.683151 
L 993.986731 325.080962 
L 996.833379 325.447029 
L 999.680028 325.653646 
L 1002.526676 326.28271 
L 1005.373324 326.978525 
L 1008.219972 327.364473 
L 1011.066621 327.709105 
L 1013.913269 328.132677 
L 1016.759917 328.511965 
L 1019.606566 329.008673 
L 1022.453214 329.397493 
L 1025.299862 329.823329 
L 1028.146511 330.372992 
L 1030.993159 330.650902 
L 1033.839807 331.212755 
L 1036.686455 331.823742 
L 1039.533104 332.490086 
L 1042.379752 333.014863 
L 1045.2264 333.631314 
L 1048.073049 334.267981 
L 1050.919697 334.880427 
L 1053.766345 335.566357 
L 1056.612994 336.139679 
L 1059.459642 336.673368 
L 1062.30629 337.193105 
L 1065.152938 337.76226 
L 1067.999587 338.413553 
L 1070.846235 339.013919 
L 1073.692883 339.596929 
L 1076.539532 340.225785 
L 1079.38618 340.739885 
L 1082.232828 341.373357 
L 1085.079477 342.174426 
L 1087.926125 342.841599 
L 1090.772773 343.533447 
L 1093.619421 344.118198 
L 1096.46607 344.891511 
L 1099.312718 345.749848 
L 1102.159366 346.796013 
L 1105.006015 347.699506 
L 1107.852663 348.907055 
L 1110.699311 350.004499 
L 1113.54596 351.095481 
L 1116.392608 352.553663 
L 1119.239256 353.605198 
L 1122.085904 355.069663 
L 1124.932553 356.862464 
L 1127.779201 358.728835 
L 1130.625849 360.094692 
L 1133.472498 362.014276 
L 1136.319146 364.019577 
L 1139.165794 365.823588 
L 1142.012443 367.6276 
L 1144.859091 370.301874 
L 1144.859091 324.845385 
L 1144.859091 324.845385 
L 1142.012443 324.595238 
L 1139.165794 324.446143 
L 1136.319146 324.076022 
L 1133.472498 323.680173 
L 1130.625849 323.30033 
L 1127.779201 322.894465 
L 1124.932553 322.455645 
L 1122.085904 321.864482 
L 1119.239256 321.505847 
L 1116.392608 320.659797 
L 1113.54596 320.226767 
L 1110.699311 319.699216 
L 1107.852663 319.281095 
L 1105.006015 318.981526 
L 1102.159366 318.509715 
L 1099.312718 317.735951 
L 1096.46607 317.088422 
L 1093.619421 316.501355 
L 1090.772773 315.215384 
L 1087.926125 314.464906 
L 1085.079477 313.090628 
L 1082.232828 311.668731 
L 1079.38618 310.515766 
L 1076.539532 308.749246 
L 1073.692883 307.219755 
L 1070.846235 305.044096 
L 1067.999587 303.831622 
L 1065.152938 302.150064 
L 1062.30629 300.428719 
L 1059.459642 298.569223 
L 1056.612994 295.859826 
L 1053.766345 293.231939 
L 1050.919697 290.746781 
L 1048.073049 288.425047 
L 1045.2264 286.325933 
L 1042.379752 284.233725 
L 1039.533104 282.141517 
L 1036.686455 280.049308 
L 1033.839807 277.582976 
L 1030.993159 275.48994 
L 1028.146511 273.410139 
L 1025.299862 271.330338 
L 1022.453214 269.250537 
L 1019.606566 267.170736 
L 1016.759917 265.090935 
L 1013.913269 263.04881 
L 1011.066621 260.925716 
L 1008.219972 258.585916 
L 1005.373324 256.243258 
L 1002.526676 253.891935 
L 999.680028 251.543134 
L 996.833379 249.195789 
L 993.986731 246.848443 
L 991.140083 244.507146 
L 988.293434 242.168177 
L 985.446786 239.829209 
L 982.600138 237.684607 
L 979.753489 235.574594 
L 976.906841 233.464581 
L 974.060193 231.354568 
L 971.213545 229.244555 
L 968.366896 227.134542 
L 965.520248 224.937188 
L 962.6736 222.504564 
L 959.826951 220.07194 
L 956.980303 217.639317 
L 954.133655 215.212479 
L 951.287006 212.786658 
L 948.440358 210.360837 
L 945.59371 208.045072 
L 942.747062 205.963133 
L 939.900413 203.881195 
L 937.053765 201.726089 
L 934.207117 199.400699 
L 931.360468 197.07531 
L 928.51382 194.749921 
L 925.667172 192.424531 
L 922.820523 190.099142 
L 919.973875 187.773752 
L 917.127227 185.448363 
L 914.280579 183.124392 
L 911.43393 180.801249 
L 908.587282 178.478107 
L 905.740634 176.154964 
L 902.893985 173.831821 
L 900.047337 171.508679 
L 897.200689 169.185536 
L 894.35404 166.862394 
L 891.507392 164.539251 
L 888.660744 162.216109 
L 885.814096 159.892966 
L 882.967447 157.569824 
L 880.120799 155.246681 
L 877.274151 152.923539 
L 874.427502 150.600396 
L 871.580854 148.277254 
L 868.734206 145.954111 
L 865.887557 143.630969 
L 863.040909 141.307826 
z
" clip-path="url(#p5189e6b98e)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_13">
     <g id="line2d_53">
      <path d="M 863.040909 417.412174 
L 863.040909 128.16 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#md81fabead9" x="863.040909" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 0 -->
      <g transform="translate(860.040909 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_55">
      <path d="M 928.886279 417.412174 
L 928.886279 128.16 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#md81fabead9" x="928.886279" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 1 -->
      <g transform="translate(925.886279 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_57">
      <path d="M 994.731648 417.412174 
L 994.731648 128.16 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#md81fabead9" x="994.731648" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 2 -->
      <g transform="translate(991.731648 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_59">
      <path d="M 1060.577018 417.412174 
L 1060.577018 128.16 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#md81fabead9" x="1060.577018" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 3 -->
      <g transform="translate(1057.577018 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_61">
      <path d="M 1126.422387 417.412174 
L 1126.422387 128.16 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#md81fabead9" x="1126.422387" y="417.412174" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 4 -->
      <g transform="translate(1123.422387 431.482486) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_37">
     <!-- 心理韧性 -->
     <g transform="translate(983.95 444.512174) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 848.95 409.221456 
L 1158.95 409.221456 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="409.221456" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 20 -->
      <g transform="translate(829.95 412.756612) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 848.95 359.650374 
L 1158.95 359.650374 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="359.650374" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 30 -->
      <g transform="translate(829.95 363.18553) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 848.95 310.079292 
L 1158.95 310.079292 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="310.079292" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 40 -->
      <g transform="translate(829.95 313.614449) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 848.95 260.508211 
L 1158.95 260.508211 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="260.508211" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 50 -->
      <g transform="translate(829.95 264.043367) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 848.95 210.937129 
L 1158.95 210.937129 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="210.937129" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 60 -->
      <g transform="translate(829.95 214.472285) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-36"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_73">
      <path d="M 848.95 161.366047 
L 1158.95 161.366047 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_74">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="161.366047" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 70 -->
      <g transform="translate(829.95 164.901203) scale(0.1 -0.1)">
       <defs>
        <path id="LXGWWenKai-Regular-37" d="M 3392 4205 
Q 3366 4115 3296 4003 
Q 3226 3891 3072 3584 
Q 1997 1402 1626 32 
Q 1606 -70 1414 -70 
Q 1222 -70 1180 0 
Q 1139 70 1139 144 
Q 1139 218 1628 1424 
Q 2118 2630 2829 4064 
L 1005 3904 
Q 960 3898 912 3888 
Q 864 3878 777 3878 
Q 691 3878 620 3964 
Q 550 4051 521 4147 
Q 493 4243 493 4256 
Q 493 4314 531 4314 
L 762 4301 
L 2816 4448 
Q 2893 4454 2960 4473 
Q 3027 4493 3085 4493 
L 3104 4493 
Q 3187 4486 3289 4390 
Q 3392 4294 3392 4205 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-37"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- 状态焦虑前测 -->
     <g transform="translate(824.229688 302.786087) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-524d" d="M 474 3904 
Q 698 3872 819 3872 
L 902 3872 
L 2592 3968 
L 2534 4038 
Q 2298 4346 1875 4698 
Q 1811 4755 1811 4812 
Q 1811 4870 1878 4944 
Q 1946 5018 1984 5018 
Q 2022 5018 2124 4950 
Q 2227 4883 2361 4777 
Q 2496 4672 2624 4553 
Q 2752 4435 2835 4336 
Q 2918 4237 2918 4179 
Q 2918 4122 2867 4054 
Q 2816 3987 2803 3981 
L 3373 4013 
Q 3866 4480 4058 4765 
Q 4250 5050 4250 5165 
L 4250 5178 
Q 4250 5248 4288 5248 
Q 4326 5248 4409 5190 
Q 4493 5133 4566 5046 
Q 4640 4960 4640 4883 
Q 4640 4806 4377 4524 
Q 4115 4243 3891 4045 
L 5280 4122 
Q 5453 4128 5549 4163 
Q 5645 4198 5686 4198 
Q 5728 4198 5818 4147 
Q 6042 4006 6042 3885 
Q 6042 3814 5914 3802 
L 1050 3539 
Q 922 3526 832 3526 
Q 627 3526 528 3670 
Q 429 3814 429 3859 
Q 429 3904 474 3904 
z
M 5408 -70 
L 5414 -294 
Q 5414 -448 5305 -528 
Q 5197 -608 5120 -608 
Q 5043 -608 4908 -537 
Q 4774 -467 4617 -361 
Q 4461 -256 4320 -137 
Q 4179 -19 4086 73 
Q 3994 166 3994 211 
Q 3994 256 4058 256 
Q 4122 256 4397 115 
Q 4672 -26 4986 -102 
L 5005 3014 
Q 5005 3213 4922 3347 
Q 4890 3411 4890 3433 
Q 4890 3456 4934 3456 
Q 4979 3456 5155 3398 
Q 5331 3341 5376 3289 
Q 5421 3238 5421 3162 
L 5408 -70 
z
M 1011 -301 
Q 1126 621 1126 2637 
Q 1126 2886 1088 2992 
Q 1050 3098 1050 3123 
Q 1050 3168 1107 3168 
Q 1165 3168 1504 3059 
L 2867 3142 
L 2912 3142 
Q 3002 3142 3091 3088 
Q 3181 3034 3181 2931 
L 3162 2790 
L 3181 -51 
L 3194 -250 
Q 3194 -358 3101 -454 
Q 3008 -550 2931 -550 
Q 2790 -550 2364 -265 
Q 1939 19 1939 122 
Q 1939 160 1987 160 
Q 2035 160 2243 80 
Q 2451 0 2790 -90 
L 2784 954 
L 1466 890 
L 1440 -390 
Q 1440 -544 1325 -544 
Q 1210 -544 1110 -461 
Q 1011 -378 1011 -301 
z
M 4282 742 
Q 4282 576 4179 576 
Q 4173 576 4090 602 
Q 3846 678 3846 832 
Q 3846 877 3862 963 
Q 3878 1050 3878 1325 
L 3872 2522 
Q 3872 2739 3830 2819 
Q 3789 2899 3789 2931 
Q 3789 2963 3846 2963 
Q 3904 2963 4057 2905 
Q 4211 2848 4233 2793 
Q 4256 2739 4256 2675 
L 4282 742 
z
M 2778 2797 
L 1498 2714 
L 1485 2150 
L 2778 2214 
L 2778 2797 
z
M 2784 1882 
L 1478 1818 
L 1472 1235 
L 2784 1299 
L 2784 1882 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6d4b" d="M 1459 3795 
Q 1190 4122 723 4506 
Q 640 4570 640 4611 
Q 640 4653 691 4742 
Q 742 4832 803 4832 
Q 864 4832 976 4755 
Q 1088 4678 1235 4556 
Q 1382 4435 1520 4310 
Q 1658 4186 1747 4083 
Q 1837 3981 1837 3949 
Q 1837 3917 1798 3859 
Q 1760 3802 1705 3754 
Q 1651 3706 1593 3706 
Q 1536 3706 1459 3795 
z
M 1562 2643 
Q 1562 2547 1482 2457 
Q 1402 2368 1347 2368 
Q 1293 2368 1021 2614 
Q 749 2861 397 3085 
Q 307 3142 307 3184 
Q 307 3226 358 3318 
Q 410 3411 464 3411 
Q 518 3411 796 3241 
Q 1075 3072 1466 2758 
Q 1562 2682 1562 2643 
z
M 672 -262 
L 570 -243 
Q 262 -186 262 -58 
Q 262 -26 314 6 
Q 461 90 589 294 
Q 1062 1082 1491 1952 
Q 1587 2150 1664 2150 
Q 1715 2150 1715 2054 
Q 1715 1958 1520 1417 
Q 1325 877 1062 307 
Q 800 -262 672 -262 
z
M 1600 -525 
Q 1542 -550 1513 -550 
Q 1485 -550 1485 -489 
Q 1485 -429 1568 -365 
Q 2278 179 2586 890 
Q 2746 1280 2826 1805 
Q 2906 2330 2906 3366 
Q 2906 3469 2858 3549 
Q 2810 3629 2810 3654 
Q 2810 3712 2874 3712 
Q 2938 3712 3069 3667 
Q 3200 3622 3238 3587 
Q 3277 3552 3277 3469 
Q 3258 2464 3194 1907 
Q 3130 1350 2960 905 
Q 2790 461 2460 112 
Q 2131 -237 1600 -525 
z
M 2118 1280 
Q 2144 1523 2144 1626 
L 2144 1779 
L 2099 4134 
Q 2099 4346 2051 4442 
Q 2003 4538 2003 4586 
Q 2003 4634 2064 4634 
Q 2125 4634 2464 4518 
L 3712 4582 
L 3782 4589 
Q 3846 4589 3932 4534 
Q 4019 4480 4019 4365 
L 4006 4250 
L 3936 1280 
L 3936 1242 
Q 3930 1120 3853 1120 
Q 3725 1146 3641 1210 
Q 3558 1274 3565 1350 
L 3603 1549 
L 3642 4243 
L 2432 4186 
L 2464 2605 
L 2464 2278 
L 2470 1779 
L 2477 1574 
L 2483 1235 
L 2483 1197 
Q 2483 1069 2406 1069 
Q 2285 1069 2201 1136 
Q 2118 1203 2118 1280 
z
M 3981 435 
Q 4090 282 4160 160 
Q 4230 38 4230 -35 
Q 4230 -109 4124 -166 
Q 4019 -224 3984 -224 
Q 3949 -224 3926 -198 
Q 3904 -173 3737 121 
Q 3571 416 3414 630 
Q 3258 845 3258 909 
Q 3258 973 3338 1021 
Q 3418 1069 3466 1069 
Q 3514 1069 3581 979 
Q 3648 890 3760 739 
Q 3872 589 3981 435 
z
M 5709 -58 
L 5722 -282 
Q 5722 -448 5626 -537 
Q 5530 -627 5453 -627 
Q 5376 -627 5251 -550 
Q 5126 -474 4976 -365 
Q 4826 -256 4694 -134 
Q 4563 -13 4476 83 
Q 4390 179 4390 214 
Q 4390 250 4454 250 
Q 4518 250 4774 112 
Q 5030 -26 5331 -128 
L 5344 4749 
Q 5344 4902 5242 5050 
Q 5216 5088 5216 5120 
Q 5216 5152 5293 5152 
Q 5370 5152 5523 5091 
Q 5677 5030 5699 4979 
Q 5722 4928 5722 4838 
L 5709 -58 
z
M 4448 3674 
Q 4448 3808 4413 3894 
Q 4378 3981 4368 3993 
Q 4358 4006 4358 4028 
Q 4358 4051 4422 4051 
Q 4486 4051 4617 4000 
Q 4749 3949 4777 3904 
Q 4806 3859 4806 3776 
L 4819 1011 
Q 4819 838 4710 838 
Q 4602 838 4509 915 
Q 4416 992 4416 1049 
Q 4416 1107 4432 1222 
Q 4448 1338 4448 1581 
L 4448 3674 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-524d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_75">
    <path d="M 863.040909 278.44217 
L 865.887557 279.082876 
L 868.734206 279.723581 
L 871.580854 280.364287 
L 874.427502 281.004992 
L 877.274151 281.645698 
L 880.120799 282.286403 
L 882.967447 282.927109 
L 885.814096 283.567814 
L 888.660744 284.20852 
L 891.507392 284.849226 
L 894.35404 285.489931 
L 897.200689 286.130637 
L 900.047337 286.771342 
L 902.893985 287.412048 
L 905.740634 288.052753 
L 908.587282 288.693459 
L 911.43393 289.334164 
L 914.280579 289.97487 
L 917.127227 290.615575 
L 919.973875 291.256281 
L 922.820523 291.896986 
L 925.667172 292.537692 
L 928.51382 293.178398 
L 931.360468 293.819103 
L 934.207117 294.459809 
L 937.053765 295.100514 
L 939.900413 295.74122 
L 942.747062 296.381925 
L 945.59371 297.022631 
L 948.440358 297.663336 
L 951.287006 298.304042 
L 954.133655 298.944747 
L 956.980303 299.585453 
L 959.826951 300.226158 
L 962.6736 300.866864 
L 965.520248 301.50757 
L 968.366896 302.148275 
L 971.213545 302.788981 
L 974.060193 303.429686 
L 976.906841 304.070392 
L 979.753489 304.711097 
L 982.600138 305.351803 
L 985.446786 305.992508 
L 988.293434 306.633214 
L 991.140083 307.273919 
L 993.986731 307.914625 
L 996.833379 308.55533 
L 999.680028 309.196036 
L 1002.526676 309.836742 
L 1005.373324 310.477447 
L 1008.219972 311.118153 
L 1011.066621 311.758858 
L 1013.913269 312.399564 
L 1016.759917 313.040269 
L 1019.606566 313.680975 
L 1022.453214 314.32168 
L 1025.299862 314.962386 
L 1028.146511 315.603091 
L 1030.993159 316.243797 
L 1033.839807 316.884502 
L 1036.686455 317.525208 
L 1039.533104 318.165914 
L 1042.379752 318.806619 
L 1045.2264 319.447325 
L 1048.073049 320.08803 
L 1050.919697 320.728736 
L 1053.766345 321.369441 
L 1056.612994 322.010147 
L 1059.459642 322.650852 
L 1062.30629 323.291558 
L 1065.152938 323.932263 
L 1067.999587 324.572969 
L 1070.846235 325.213674 
L 1073.692883 325.85438 
L 1076.539532 326.495086 
L 1079.38618 327.135791 
L 1082.232828 327.776497 
L 1085.079477 328.417202 
L 1087.926125 329.057908 
L 1090.772773 329.698613 
L 1093.619421 330.339319 
L 1096.46607 330.980024 
L 1099.312718 331.62073 
L 1102.159366 332.261435 
L 1105.006015 332.902141 
L 1107.852663 333.542847 
L 1110.699311 334.183552 
L 1113.54596 334.824258 
L 1116.392608 335.464963 
L 1119.239256 336.105669 
L 1122.085904 336.746374 
L 1124.932553 337.38708 
L 1127.779201 338.027785 
L 1130.625849 338.668491 
L 1133.472498 339.309196 
L 1136.319146 339.949902 
L 1139.165794 340.590607 
L 1142.012443 341.231313 
L 1144.859091 341.872019 
" clip-path="url(#p5189e6b98e)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_9">
    <path d="M 848.95 417.412174 
L 848.95 128.16 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 848.95 417.412174 
L 1158.95 417.412174 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <!-- r = -0.332, p = 0.063 -->
    <g transform="translate(943.037813 122.16) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-36" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="axes_4">
   <g id="patch_11">
    <path d="M 42.95 793.44 
L 352.95 793.44 
L 352.95 504.187826 
L 42.95 504.187826 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_4">
    <g clip-path="url(#p3f173ec3de)">
     <use xlink:href="#mddbfe8b7f6" x="57.040909" y="681.35553" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="259.844647" y="780.292174" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="57.040909" y="692.995135" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="278.281351" y="710.454543" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="328.323832" y="675.535727" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="286.182795" y="663.896122" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="262.478462" y="710.454543" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="280.915166" y="663.896122" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="244.041759" y="634.797109" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="249.309388" y="687.175332" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="299.351869" y="623.157504" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="307.253314" y="733.733753" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="288.81661" y="640.616912" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="257.210833" y="617.337701" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="265.112277" y="757.012964" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="280.915166" y="605.698096" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="262.478462" y="617.337701" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="336.225276" y="634.797109" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="286.182795" y="594.058491" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="333.591461" y="745.373358" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="325.690017" y="704.63474" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="296.718054" y="710.454543" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="312.520943" y="669.715925" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="338.859091" y="704.63474" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="294.08424" y="681.35553" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="307.253314" y="652.256517" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="296.718054" y="687.175332" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="299.351869" y="692.995135" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="273.013721" y="564.959478" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="249.309388" y="582.418886" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="270.379907" y="704.63474" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
     <use xlink:href="#mddbfe8b7f6" x="283.54898" y="623.157504" style="fill: #0072bd; fill-opacity: 0.6; stroke: #0072bd; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_4">
    <path d="M 57.040909 517.335652 
L 57.040909 699.252862 
L 59.887557 699.074799 
L 62.734206 698.896736 
L 65.580854 698.718673 
L 68.427502 698.540609 
L 71.274151 698.398256 
L 74.120799 698.219235 
L 76.967447 698.00642 
L 79.814096 697.828357 
L 82.660744 697.562656 
L 85.507392 697.278657 
L 88.35404 696.990293 
L 91.200689 696.701262 
L 94.047337 696.37524 
L 96.893985 696.046883 
L 99.740634 695.842672 
L 102.587282 695.560965 
L 105.43393 695.277269 
L 108.280579 694.987476 
L 111.127227 694.697684 
L 113.973875 694.407891 
L 116.820523 694.087174 
L 119.667172 693.683151 
L 122.51382 693.279127 
L 125.360468 692.926592 
L 128.207117 692.884608 
L 131.053765 692.68306 
L 133.900413 692.286591 
L 136.747062 692.118814 
L 139.59371 691.833749 
L 142.440358 691.611496 
L 145.287006 691.454007 
L 148.133655 691.216335 
L 150.980303 691.140515 
L 153.826951 691.061921 
L 156.6736 690.988875 
L 159.520248 690.913055 
L 162.366896 690.837235 
L 165.213545 690.761414 
L 168.060193 690.685384 
L 170.906841 690.455455 
L 173.753489 690.210687 
L 176.600138 690.01959 
L 179.446786 689.887726 
L 182.293434 689.719307 
L 185.140083 689.558908 
L 187.986731 689.579134 
L 190.833379 689.427457 
L 193.680028 689.42371 
L 196.526676 689.247079 
L 199.373324 688.985857 
L 202.219972 689.035682 
L 205.066621 688.92396 
L 207.913269 688.810856 
L 210.759917 688.699268 
L 213.606566 688.588533 
L 216.453214 688.325978 
L 219.299862 688.134475 
L 222.146511 688.259343 
L 224.993159 688.152618 
L 227.839807 687.999788 
L 230.686455 687.928836 
L 233.533104 687.819834 
L 236.379752 687.698888 
L 239.2264 687.602215 
L 242.073049 687.453531 
L 244.919697 687.114705 
L 247.766345 687.271749 
L 250.612994 687.228535 
L 253.459642 687.253079 
L 256.30629 687.332354 
L 259.152938 687.409892 
L 261.999587 687.313902 
L 264.846235 687.296759 
L 267.692883 687.355104 
L 270.539532 687.384459 
L 273.38618 687.482969 
L 276.232828 687.396899 
L 279.079477 687.254533 
L 281.926125 687.312433 
L 284.772773 687.174989 
L 287.619421 687.195253 
L 290.46607 687.454997 
L 293.312718 687.430992 
L 296.159366 687.592923 
L 299.006015 688.026089 
L 301.852663 688.451549 
L 304.699311 688.728745 
L 307.54596 689.194933 
L 310.392608 689.645159 
L 313.239256 689.916691 
L 316.085904 690.826725 
L 318.932553 691.840409 
L 321.779201 693.191747 
L 324.625849 695.056231 
L 327.472498 695.413833 
L 330.319146 696.521131 
L 333.165794 698.356079 
L 336.012443 700.2198 
L 338.859091 701.245291 
L 338.859091 651.20563 
L 338.859091 651.20563 
L 336.012443 651.387413 
L 333.165794 651.513569 
L 330.319146 651.747788 
L 327.472498 651.928345 
L 324.625849 652.036943 
L 321.779201 652.289457 
L 318.932553 652.539444 
L 316.085904 652.640776 
L 313.239256 652.433027 
L 310.392608 652.589972 
L 307.54596 652.893534 
L 304.699311 653.016398 
L 301.852663 652.777084 
L 299.006015 652.792635 
L 296.159366 653.019495 
L 293.312718 653.310911 
L 290.46607 653.479028 
L 287.619421 653.697358 
L 284.772773 653.806642 
L 281.926125 653.82634 
L 279.079477 653.022321 
L 276.232828 652.506152 
L 273.38618 651.780236 
L 270.539532 650.612628 
L 267.692883 649.326854 
L 264.846235 648.791654 
L 261.999587 647.167374 
L 259.152938 646.286122 
L 256.30629 644.744617 
L 253.459642 643.072425 
L 250.612994 641.364799 
L 247.766345 639.642131 
L 244.919697 637.621523 
L 242.073049 635.815285 
L 239.2264 634.195314 
L 236.379752 632.575342 
L 233.533104 630.9665 
L 230.686455 629.32742 
L 227.839807 627.686947 
L 224.993159 625.847846 
L 222.146511 623.502437 
L 219.299862 621.157029 
L 216.453214 618.81162 
L 213.606566 616.738902 
L 210.759917 615.145417 
L 207.913269 614.101854 
L 205.066621 613.067898 
L 202.219972 612.035162 
L 199.373324 610.299621 
L 196.526676 608.433853 
L 193.680028 606.568085 
L 190.833379 604.702317 
L 187.986731 602.836549 
L 185.140083 600.970781 
L 182.293434 599.105014 
L 179.446786 597.239246 
L 176.600138 595.373478 
L 173.753489 593.50771 
L 170.906841 591.641942 
L 168.060193 589.826087 
L 165.213545 588.194685 
L 162.366896 586.563282 
L 159.520248 584.93188 
L 156.6736 583.300478 
L 153.826951 581.669075 
L 150.980303 580.037673 
L 148.133655 578.40627 
L 145.287006 576.470429 
L 142.440358 574.493302 
L 139.59371 572.516175 
L 136.747062 570.539047 
L 133.900413 568.56436 
L 131.053765 566.590983 
L 128.207117 564.617606 
L 125.360468 562.64423 
L 122.51382 560.670853 
L 119.667172 558.797069 
L 116.820523 557.068716 
L 113.973875 555.340363 
L 111.127227 553.61201 
L 108.280579 551.883657 
L 105.43393 550.155304 
L 102.587282 548.372047 
L 99.740634 546.429424 
L 96.893985 544.489191 
L 94.047337 542.548957 
L 91.200689 540.608724 
L 88.35404 538.66849 
L 85.507392 536.728257 
L 82.660744 534.788023 
L 79.814096 532.84779 
L 76.967447 530.907556 
L 74.120799 528.967323 
L 71.274151 527.027089 
L 68.427502 525.086856 
L 65.580854 523.146622 
L 62.734206 521.208196 
L 59.887557 519.271924 
L 57.040909 517.335652 
z
" clip-path="url(#p3f173ec3de)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_7">
    <g id="xtick_18">
     <g id="line2d_76">
      <path d="M 57.040909 793.44 
L 57.040909 504.187826 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_77">
      <g>
       <use xlink:href="#md81fabead9" x="57.040909" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_46">
      <!-- 0 -->
      <g transform="translate(54.040909 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_19">
     <g id="line2d_78">
      <path d="M 122.886279 793.44 
L 122.886279 504.187826 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_79">
      <g>
       <use xlink:href="#md81fabead9" x="122.886279" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 1 -->
      <g transform="translate(119.886279 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_80">
      <path d="M 188.731648 793.44 
L 188.731648 504.187826 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_81">
      <g>
       <use xlink:href="#md81fabead9" x="188.731648" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 2 -->
      <g transform="translate(185.731648 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_82">
      <path d="M 254.577018 793.44 
L 254.577018 504.187826 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_83">
      <g>
       <use xlink:href="#md81fabead9" x="254.577018" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 3 -->
      <g transform="translate(251.577018 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_84">
      <path d="M 320.422387 793.44 
L 320.422387 504.187826 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_85">
      <g>
       <use xlink:href="#md81fabead9" x="320.422387" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 4 -->
      <g transform="translate(317.422387 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_51">
     <!-- 心理韧性 -->
     <g transform="translate(177.95 820.54) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_8">
    <g id="ytick_20">
     <g id="line2d_86">
      <path d="M 42.95 786.111977 
L 352.95 786.111977 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_87">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="786.111977" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_52">
      <!-- -10 -->
      <g transform="translate(20.45 789.647133) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-2d"/>
       <use xlink:href="#LXGWWenKai-Regular-31" x="34.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_88">
      <path d="M 42.95 727.913951 
L 352.95 727.913951 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_89">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="727.913951" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0 -->
      <g transform="translate(29.95 731.449107) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_90">
      <path d="M 42.95 669.715925 
L 352.95 669.715925 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_91">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="669.715925" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 10 -->
      <g transform="translate(23.95 673.251081) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_92">
      <path d="M 42.95 611.517899 
L 352.95 611.517899 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_93">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="611.517899" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 20 -->
      <g transform="translate(23.95 615.053055) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_94">
      <path d="M 42.95 553.319873 
L 352.95 553.319873 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_95">
      <g>
       <use xlink:href="#m5de63a5a44" x="42.95" y="553.319873" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 30 -->
      <g transform="translate(23.95 556.855029) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_57">
     <!-- 状态焦虑变化 -->
     <g transform="translate(14.729687 678.813913) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-53d8" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5316" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_96">
    <path d="M 57.040909 668.649915 
L 59.887557 668.67106 
L 62.734206 668.692205 
L 65.580854 668.713351 
L 68.427502 668.734496 
L 71.274151 668.755642 
L 74.120799 668.776787 
L 76.967447 668.797932 
L 79.814096 668.819078 
L 82.660744 668.840223 
L 85.507392 668.861368 
L 88.35404 668.882514 
L 91.200689 668.903659 
L 94.047337 668.924804 
L 96.893985 668.94595 
L 99.740634 668.967095 
L 102.587282 668.98824 
L 105.43393 669.009386 
L 108.280579 669.030531 
L 111.127227 669.051676 
L 113.973875 669.072822 
L 116.820523 669.093967 
L 119.667172 669.115112 
L 122.51382 669.136258 
L 125.360468 669.157403 
L 128.207117 669.178548 
L 131.053765 669.199694 
L 133.900413 669.220839 
L 136.747062 669.241984 
L 139.59371 669.26313 
L 142.440358 669.284275 
L 145.287006 669.305421 
L 148.133655 669.326566 
L 150.980303 669.347711 
L 153.826951 669.368857 
L 156.6736 669.390002 
L 159.520248 669.411147 
L 162.366896 669.432293 
L 165.213545 669.453438 
L 168.060193 669.474583 
L 170.906841 669.495729 
L 173.753489 669.516874 
L 176.600138 669.538019 
L 179.446786 669.559165 
L 182.293434 669.58031 
L 185.140083 669.601455 
L 187.986731 669.622601 
L 190.833379 669.643746 
L 193.680028 669.664891 
L 196.526676 669.686037 
L 199.373324 669.707182 
L 202.219972 669.728327 
L 205.066621 669.749473 
L 207.913269 669.770618 
L 210.759917 669.791764 
L 213.606566 669.812909 
L 216.453214 669.834054 
L 219.299862 669.8552 
L 222.146511 669.876345 
L 224.993159 669.89749 
L 227.839807 669.918636 
L 230.686455 669.939781 
L 233.533104 669.960926 
L 236.379752 669.982072 
L 239.2264 670.003217 
L 242.073049 670.024362 
L 244.919697 670.045508 
L 247.766345 670.066653 
L 250.612994 670.087798 
L 253.459642 670.108944 
L 256.30629 670.130089 
L 259.152938 670.151234 
L 261.999587 670.17238 
L 264.846235 670.193525 
L 267.692883 670.21467 
L 270.539532 670.235816 
L 273.38618 670.256961 
L 276.232828 670.278106 
L 279.079477 670.299252 
L 281.926125 670.320397 
L 284.772773 670.341543 
L 287.619421 670.362688 
L 290.46607 670.383833 
L 293.312718 670.404979 
L 296.159366 670.426124 
L 299.006015 670.447269 
L 301.852663 670.468415 
L 304.699311 670.48956 
L 307.54596 670.510705 
L 310.392608 670.531851 
L 313.239256 670.552996 
L 316.085904 670.574141 
L 318.932553 670.595287 
L 321.779201 670.616432 
L 324.625849 670.637577 
L 327.472498 670.658723 
L 330.319146 670.679868 
L 333.165794 670.701013 
L 336.012443 670.722159 
L 338.859091 670.743304 
" clip-path="url(#p3f173ec3de)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 42.95 793.44 
L 42.95 504.187826 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 42.95 793.44 
L 352.95 793.44 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_58">
    <!-- r = -0.009, p = 0.961 -->
    <g transform="translate(137.037813 498.187826) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-39" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-36" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="axes_5">
   <g id="patch_14">
    <path d="M 445.95 793.44 
L 755.95 793.44 
L 755.95 504.187826 
L 445.95 504.187826 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_5">
    <g clip-path="url(#p4261c2201a)">
     <use xlink:href="#mcd18c402c3" x="596.924026" y="677.396144" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="717.703247" y="688.829036" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="677.443506" y="631.664575" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="637.183766" y="677.396144" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="460.040909" y="768.859282" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="492.248701" y="734.560605" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="653.287662" y="665.963251" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="629.131818" y="637.381021" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="629.131818" y="608.79879" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="580.82013" y="694.545482" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="580.82013" y="631.664575" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="548.612338" y="763.142836" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="613.027922" y="625.948129" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="596.924026" y="614.515236" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="709.651299" y="671.679698" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="604.975974" y="597.365898" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="572.768182" y="631.664575" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="572.768182" y="648.813913" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="556.664286" y="620.231682" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="629.131818" y="717.411267" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="484.196753" y="780.292174" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="572.768182" y="723.127713" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="572.768182" y="683.11259" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="508.352597" y="763.142836" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="540.56039" y="717.411267" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="580.82013" y="660.246805" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="540.56039" y="723.127713" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="524.456494" y="740.277051" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="540.56039" y="603.082344" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="468.092857" y="671.679698" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="588.872078" y="705.978374" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
     <use xlink:href="#mcd18c402c3" x="741.859091" y="517.335652" style="fill: #d95319; fill-opacity: 0.6; stroke: #d95319; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_5">
    <path d="M 460.040909 695.147375 
L 460.040909 774.441402 
L 462.887557 772.405412 
L 465.734206 770.363471 
L 468.580854 768.046738 
L 471.427502 765.69098 
L 474.274151 763.375932 
L 477.120799 761.03256 
L 479.967447 759.207581 
L 482.814096 756.944461 
L 485.660744 754.825297 
L 488.507392 752.748305 
L 491.35404 750.501094 
L 494.200689 748.252072 
L 497.047337 746.117402 
L 499.893985 743.878392 
L 502.740634 741.547664 
L 505.587282 739.39911 
L 508.43393 737.276001 
L 511.280579 735.060319 
L 514.127227 732.958293 
L 516.973875 731.05844 
L 519.820523 729.221136 
L 522.667172 727.279941 
L 525.51382 725.26288 
L 528.360468 723.369194 
L 531.207117 721.556066 
L 534.053765 719.894195 
L 536.900413 717.97099 
L 539.747062 716.178627 
L 542.59371 714.386263 
L 545.440358 712.698145 
L 548.287006 710.836242 
L 551.133655 709.159046 
L 553.980303 707.413103 
L 556.826951 705.91441 
L 559.6736 704.641827 
L 562.520248 702.923183 
L 565.366896 700.635589 
L 568.213545 699.032449 
L 571.060193 697.65741 
L 573.906841 696.0999 
L 576.753489 694.846067 
L 579.600138 693.430466 
L 582.446786 691.929636 
L 585.293434 690.330743 
L 588.140083 689.310199 
L 590.986731 688.349672 
L 593.833379 687.305011 
L 596.680028 686.197476 
L 599.526676 684.96518 
L 602.373324 683.792517 
L 605.219972 682.537461 
L 608.066621 681.563929 
L 610.913269 680.39943 
L 613.759917 679.241675 
L 616.606566 677.976295 
L 619.453214 677.019314 
L 622.299862 676.076665 
L 625.146511 675.346908 
L 627.993159 674.501288 
L 630.839807 673.787525 
L 633.686455 672.862289 
L 636.533104 672.043565 
L 639.379752 671.213397 
L 642.2264 670.507907 
L 645.073049 669.567659 
L 647.919697 668.496256 
L 650.766345 667.835756 
L 653.612994 667.086327 
L 656.459642 666.196604 
L 659.30629 665.445855 
L 662.152938 664.822526 
L 664.999587 664.439986 
L 667.846235 664.030481 
L 670.692883 663.179007 
L 673.539532 662.290996 
L 676.38618 661.402985 
L 679.232828 660.718283 
L 682.079477 660.153866 
L 684.926125 659.618697 
L 687.772773 658.922071 
L 690.619421 657.97193 
L 693.46607 656.988467 
L 696.312718 656.449641 
L 699.159366 656.039458 
L 702.006015 655.453748 
L 704.852663 654.854001 
L 707.699311 654.31913 
L 710.54596 653.693969 
L 713.392608 652.996161 
L 716.239256 652.292381 
L 719.085904 651.405956 
L 721.932553 650.736016 
L 724.779201 650.193196 
L 727.625849 649.492455 
L 730.472498 648.701595 
L 733.319146 647.826171 
L 736.165794 647.038125 
L 739.012443 646.495326 
L 741.859091 645.98875 
L 741.859091 542.024973 
L 741.859091 542.024973 
L 739.012443 544.243997 
L 736.165794 546.594347 
L 733.319146 548.740993 
L 730.472498 550.870186 
L 727.625849 553.190925 
L 724.779201 555.275137 
L 721.932553 557.361125 
L 719.085904 559.588155 
L 716.239256 561.87026 
L 713.392608 564.152364 
L 710.54596 566.434469 
L 707.699311 568.716573 
L 704.852663 570.998678 
L 702.006015 573.280782 
L 699.159366 575.562887 
L 696.312718 577.809496 
L 693.46607 579.974732 
L 690.619421 582.139968 
L 687.772773 584.305203 
L 684.926125 586.411003 
L 682.079477 588.496722 
L 679.232828 590.680173 
L 676.38618 592.982022 
L 673.539532 595.14802 
L 670.692883 597.31794 
L 667.846235 599.492182 
L 664.999587 601.890206 
L 662.152938 604.336057 
L 659.30629 606.510611 
L 656.459642 608.686377 
L 653.612994 610.862924 
L 650.766345 613.045045 
L 647.919697 615.458025 
L 645.073049 617.682829 
L 642.2264 619.62003 
L 639.379752 621.816868 
L 636.533104 623.950825 
L 633.686455 626.196907 
L 630.839807 628.425803 
L 627.993159 630.464428 
L 625.146511 632.618621 
L 622.299862 634.794854 
L 619.453214 636.621458 
L 616.606566 638.579893 
L 613.759917 640.770275 
L 610.913269 642.729381 
L 608.066621 644.958039 
L 605.219972 646.832279 
L 602.373324 648.509076 
L 599.526676 650.508964 
L 596.680028 651.801159 
L 593.833379 653.218302 
L 590.986731 654.87459 
L 588.140083 656.268189 
L 585.293434 657.475765 
L 582.446786 659.095861 
L 579.600138 660.511458 
L 576.753489 661.920821 
L 573.906841 663.193292 
L 571.060193 664.458519 
L 568.213545 665.621604 
L 565.366896 666.834828 
L 562.520248 668.124291 
L 559.6736 669.252618 
L 556.826951 670.071588 
L 553.980303 671.043045 
L 551.133655 671.737904 
L 548.287006 672.748738 
L 545.440358 673.655267 
L 542.59371 674.727677 
L 539.747062 675.66244 
L 536.900413 676.331349 
L 534.053765 677.420688 
L 531.207117 678.537786 
L 528.360468 679.631313 
L 525.51382 680.395976 
L 522.667172 681.098552 
L 519.820523 681.763455 
L 516.973875 682.45632 
L 514.127227 683.161069 
L 511.280579 683.865818 
L 508.43393 684.570567 
L 505.587282 685.139159 
L 502.740634 685.744906 
L 499.893985 686.284744 
L 497.047337 686.719276 
L 494.200689 687.242901 
L 491.35404 687.905925 
L 488.507392 688.544778 
L 485.660744 689.456789 
L 482.814096 690.307418 
L 479.967447 690.874245 
L 477.120799 691.445025 
L 474.274151 691.991032 
L 471.427502 692.549029 
L 468.580854 693.106815 
L 465.734206 693.664601 
L 462.887557 694.358424 
L 460.040909 695.147375 
z
" clip-path="url(#p4261c2201a)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_9">
    <g id="xtick_23">
     <g id="line2d_97">
      <path d="M 451.988961 793.44 
L 451.988961 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_98">
      <g>
       <use xlink:href="#md81fabead9" x="451.988961" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_59">
      <!-- 20 -->
      <g transform="translate(445.988961 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_99">
      <path d="M 492.248701 793.44 
L 492.248701 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_100">
      <g>
       <use xlink:href="#md81fabead9" x="492.248701" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_60">
      <!-- 25 -->
      <g transform="translate(486.248701 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_101">
      <path d="M 532.508442 793.44 
L 532.508442 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_102">
      <g>
       <use xlink:href="#md81fabead9" x="532.508442" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_61">
      <!-- 30 -->
      <g transform="translate(526.508442 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_103">
      <path d="M 572.768182 793.44 
L 572.768182 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_104">
      <g>
       <use xlink:href="#md81fabead9" x="572.768182" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_62">
      <!-- 35 -->
      <g transform="translate(566.768182 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_105">
      <path d="M 613.027922 793.44 
L 613.027922 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_106">
      <g>
       <use xlink:href="#md81fabead9" x="613.027922" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_63">
      <!-- 40 -->
      <g transform="translate(607.027922 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_28">
     <g id="line2d_107">
      <path d="M 653.287662 793.44 
L 653.287662 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_108">
      <g>
       <use xlink:href="#md81fabead9" x="653.287662" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_64">
      <!-- 45 -->
      <g transform="translate(647.287662 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_29">
     <g id="line2d_109">
      <path d="M 693.547403 793.44 
L 693.547403 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_110">
      <g>
       <use xlink:href="#md81fabead9" x="693.547403" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_65">
      <!-- 50 -->
      <g transform="translate(687.547403 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="xtick_30">
     <g id="line2d_111">
      <path d="M 733.807143 793.44 
L 733.807143 504.187826 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_112">
      <g>
       <use xlink:href="#md81fabead9" x="733.807143" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_66">
      <!-- 55 -->
      <g transform="translate(727.807143 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_67">
     <!-- 状态焦虑前测 -->
     <g transform="translate(570.95 820.810312) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-524d" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_10">
    <g id="ytick_25">
     <g id="line2d_113">
      <path d="M 445.95 768.859282 
L 755.95 768.859282 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_114">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="768.859282" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_68">
      <!-- 30 -->
      <g transform="translate(426.95 772.394438) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_115">
      <path d="M 445.95 711.69482 
L 755.95 711.69482 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_116">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="711.69482" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_69">
      <!-- 40 -->
      <g transform="translate(426.95 715.229977) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_117">
      <path d="M 445.95 654.530359 
L 755.95 654.530359 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_118">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="654.530359" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_70">
      <!-- 50 -->
      <g transform="translate(426.95 658.065515) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_28">
     <g id="line2d_119">
      <path d="M 445.95 597.365898 
L 755.95 597.365898 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_120">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="597.365898" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_71">
      <!-- 60 -->
      <g transform="translate(426.95 600.901054) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-36"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_29">
     <g id="line2d_121">
      <path d="M 445.95 540.201437 
L 755.95 540.201437 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_122">
      <g>
       <use xlink:href="#m5de63a5a44" x="445.95" y="540.201437" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_72">
      <!-- 70 -->
      <g transform="translate(426.95 543.736593) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-37"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_73">
     <!-- 状态焦虑后测 -->
     <g transform="translate(421.229688 678.813913) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-540e" d="M 1389 3770 
Q 1389 4013 1353 4118 
Q 1318 4224 1318 4285 
Q 1318 4346 1420 4346 
Q 1523 4346 1843 4186 
Q 2656 4346 3258 4550 
Q 4314 4915 4525 5126 
Q 4550 5152 4588 5152 
Q 4627 5152 4704 5094 
Q 4909 4941 4909 4800 
Q 4909 4730 4813 4685 
Q 3283 4064 1830 3872 
Q 1824 3680 1821 3481 
Q 1818 3283 1805 3078 
L 5222 3270 
Q 5421 3290 5475 3312 
Q 5530 3334 5562 3334 
Q 5594 3334 5683 3277 
Q 5914 3110 5914 3002 
Q 5914 2925 5747 2912 
L 1786 2701 
Q 1760 2304 1686 1865 
Q 1613 1427 1411 1001 
Q 1210 576 976 265 
Q 742 -45 547 -217 
Q 352 -390 294 -390 
Q 237 -390 237 -332 
Q 237 -275 403 -70 
Q 570 134 768 486 
Q 1184 1254 1286 1958 
Q 1389 2573 1389 3770 
z
M 2208 -339 
Q 2227 -205 2227 -166 
L 2227 13 
Q 2227 51 2221 96 
L 2125 1357 
Q 2112 1581 2057 1680 
Q 2003 1779 2003 1824 
Q 2003 1869 2096 1869 
Q 2189 1869 2528 1734 
L 4794 1862 
L 4870 1862 
Q 5037 1862 5117 1772 
Q 5197 1683 5197 1632 
Q 5197 1581 5174 1545 
Q 5152 1510 5146 1478 
L 4960 205 
Q 5126 0 5126 -64 
Q 5126 -128 5068 -137 
Q 5011 -147 4922 -154 
L 2650 -218 
L 2669 -448 
L 2669 -467 
Q 2669 -621 2544 -621 
Q 2419 -621 2313 -537 
Q 2208 -454 2208 -339 
z
M 4704 1491 
L 2534 1382 
L 2624 134 
L 4557 205 
L 4704 1491 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-540e" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_123">
    <path d="M 460.040909 738.027836 
L 462.887557 736.590758 
L 465.734206 735.15368 
L 468.580854 733.716602 
L 471.427502 732.279524 
L 474.274151 730.842446 
L 477.120799 729.405368 
L 479.967447 727.96829 
L 482.814096 726.531211 
L 485.660744 725.094133 
L 488.507392 723.657055 
L 491.35404 722.219977 
L 494.200689 720.782899 
L 497.047337 719.345821 
L 499.893985 717.908743 
L 502.740634 716.471665 
L 505.587282 715.034587 
L 508.43393 713.597508 
L 511.280579 712.16043 
L 514.127227 710.723352 
L 516.973875 709.286274 
L 519.820523 707.849196 
L 522.667172 706.412118 
L 525.51382 704.97504 
L 528.360468 703.537962 
L 531.207117 702.100884 
L 534.053765 700.663805 
L 536.900413 699.226727 
L 539.747062 697.789649 
L 542.59371 696.352571 
L 545.440358 694.915493 
L 548.287006 693.478415 
L 551.133655 692.041337 
L 553.980303 690.604259 
L 556.826951 689.16718 
L 559.6736 687.730102 
L 562.520248 686.293024 
L 565.366896 684.855946 
L 568.213545 683.418868 
L 571.060193 681.98179 
L 573.906841 680.544712 
L 576.753489 679.107634 
L 579.600138 677.670556 
L 582.446786 676.233477 
L 585.293434 674.796399 
L 588.140083 673.359321 
L 590.986731 671.922243 
L 593.833379 670.485165 
L 596.680028 669.048087 
L 599.526676 667.611009 
L 602.373324 666.173931 
L 605.219972 664.736853 
L 608.066621 663.299774 
L 610.913269 661.862696 
L 613.759917 660.425618 
L 616.606566 658.98854 
L 619.453214 657.551462 
L 622.299862 656.114384 
L 625.146511 654.677306 
L 627.993159 653.240228 
L 630.839807 651.80315 
L 633.686455 650.366071 
L 636.533104 648.928993 
L 639.379752 647.491915 
L 642.2264 646.054837 
L 645.073049 644.617759 
L 647.919697 643.180681 
L 650.766345 641.743603 
L 653.612994 640.306525 
L 656.459642 638.869447 
L 659.30629 637.432368 
L 662.152938 635.99529 
L 664.999587 634.558212 
L 667.846235 633.121134 
L 670.692883 631.684056 
L 673.539532 630.246978 
L 676.38618 628.8099 
L 679.232828 627.372822 
L 682.079477 625.935744 
L 684.926125 624.498665 
L 687.772773 623.061587 
L 690.619421 621.624509 
L 693.46607 620.187431 
L 696.312718 618.750353 
L 699.159366 617.313275 
L 702.006015 615.876197 
L 704.852663 614.439119 
L 707.699311 613.002041 
L 710.54596 611.564962 
L 713.392608 610.127884 
L 716.239256 608.690806 
L 719.085904 607.253728 
L 721.932553 605.81665 
L 724.779201 604.379572 
L 727.625849 602.942494 
L 730.472498 601.505416 
L 733.319146 600.068338 
L 736.165794 598.631259 
L 739.012443 597.194181 
L 741.859091 595.757103 
" clip-path="url(#p4261c2201a)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_15">
    <path d="M 445.95 793.44 
L 445.95 504.187826 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_16">
    <path d="M 445.95 793.44 
L 755.95 793.44 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_74">
    <!-- r = 0.582, p = 0.000*** -->
    <g transform="translate(533.137813 498.187826) scale(0.12 -0.12)">
     <defs>
      <path id="LXGWWenKai-Regular-38" d="M 1811 2304 
Q 1338 1971 1078 1708 
Q 819 1446 819 1126 
Q 819 915 922 723 
Q 1152 294 1798 294 
Q 2317 294 2621 515 
Q 2925 736 2925 1104 
Q 2925 1472 2797 1670 
Q 2669 1869 2422 2009 
Q 2176 2150 1811 2304 
z
M 1773 2739 
Q 2355 3110 2989 3616 
Q 2976 3610 2912 3610 
Q 2848 3610 2790 3699 
Q 2496 4179 1939 4179 
Q 1530 4179 1280 3971 
Q 1030 3763 1030 3504 
Q 1030 3245 1212 3081 
Q 1395 2918 1773 2739 
z
M 3130 3731 
Q 3238 3840 3315 3840 
Q 3418 3840 3418 3738 
Q 3418 3552 2528 2848 
Q 2317 2682 2157 2566 
Q 2752 2342 3069 2006 
Q 3386 1670 3386 1123 
Q 3386 576 2963 233 
Q 2541 -109 1798 -109 
Q 1363 -109 1040 51 
Q 717 211 541 489 
Q 365 768 365 1107 
Q 365 1805 1421 2496 
Q 1011 2714 796 2944 
Q 582 3174 582 3472 
Q 582 3770 748 4013 
Q 915 4256 1219 4406 
Q 1523 4557 1878 4557 
Q 2234 4557 2464 4464 
Q 2694 4371 2848 4236 
Q 3002 4102 3078 3987 
Q 3155 3872 3155 3827 
Q 3155 3782 3130 3731 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="232.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-35" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-38" x="327.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="387.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="447.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="575.199829"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="670.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="765.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="860.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="920.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="980.199707"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1030.199692"/>
     <use xlink:href="#LXGWWenKai-Regular-2a" x="1080.199677"/>
    </g>
   </g>
  </g>
  <g id="axes_6">
   <g id="patch_17">
    <path d="M 848.95 793.44 
L 1158.95 793.44 
L 1158.95 504.187826 
L 848.95 504.187826 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PathCollection_6">
    <g clip-path="url(#p86c7ff4003)">
     <use xlink:href="#m778fefa8e4" x="863.040909" y="714.376165" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1065.844647" y="721.700166" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="863.040909" y="685.080161" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1084.281351" y="714.376165" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1134.323832" y="772.968173" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1092.182795" y="750.99617" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1068.478462" y="707.052164" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1086.915166" y="688.742162" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1050.041759" y="670.43216" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1055.309388" y="725.362167" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1105.351869" y="685.080161" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1113.253314" y="769.306172" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1094.81661" y="681.418161" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1063.210833" y="674.09416" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1071.112277" y="710.714165" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1086.915166" y="663.108159" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1068.478462" y="685.080161" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1142.225276" y="696.066163" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1092.182795" y="677.75616" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1139.591461" y="740.010169" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1131.690017" y="780.292174" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1102.718054" y="743.672169" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1118.520943" y="718.038166" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1144.859091" y="769.306172" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1100.08424" y="740.010169" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1113.253314" y="703.390164" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1102.718054" y="743.672169" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1105.351869" y="754.658171" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1079.013721" y="666.770159" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1055.309388" y="710.714165" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1076.379907" y="732.686168" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
     <use xlink:href="#m778fefa8e4" x="1089.54898" y="611.840152" style="fill: #edb120; fill-opacity: 0.6; stroke: #edb120; stroke-opacity: 0.6"/>
    </g>
   </g>
   <g id="PolyCollection_6">
    <path d="M 863.040909 517.335652 
L 863.040909 699.942146 
L 865.887557 700.175357 
L 868.734206 700.36625 
L 871.580854 700.517864 
L 874.427502 700.668949 
L 877.274151 700.786269 
L 880.120799 700.880489 
L 882.967447 700.98798 
L 885.814096 701.207805 
L 888.660744 701.42741 
L 891.507392 701.643155 
L 894.35404 701.834124 
L 897.200689 702.026115 
L 900.047337 702.224086 
L 902.893985 702.499876 
L 905.740634 702.680905 
L 908.587282 702.792035 
L 911.43393 702.981336 
L 914.280579 703.169194 
L 917.127227 703.514061 
L 919.973875 703.719385 
L 922.820523 703.856517 
L 925.667172 704.081822 
L 928.51382 704.22478 
L 931.360468 704.394604 
L 934.207117 704.648524 
L 937.053765 704.835307 
L 939.900413 705.020097 
L 942.747062 705.20492 
L 945.59371 705.391306 
L 948.440358 705.590777 
L 951.287006 705.763703 
L 954.133655 705.916329 
L 956.980303 706.079685 
L 959.826951 706.243041 
L 962.6736 706.514342 
L 965.520248 706.845842 
L 968.366896 707.17319 
L 971.213545 707.500539 
L 974.060193 707.827888 
L 976.906841 708.079714 
L 979.753489 708.120601 
L 982.600138 708.488976 
L 985.446786 708.857065 
L 988.293434 709.246127 
L 991.140083 709.542346 
L 993.986731 709.822611 
L 996.833379 710.118222 
L 999.680028 710.592253 
L 1002.526676 710.997691 
L 1005.373324 711.259431 
L 1008.219972 711.623611 
L 1011.066621 712.044734 
L 1013.913269 712.414292 
L 1016.759917 712.775683 
L 1019.606566 713.178134 
L 1022.453214 713.658094 
L 1025.299862 714.029367 
L 1028.146511 714.555381 
L 1030.993159 714.974248 
L 1033.839807 715.255112 
L 1036.686455 715.568751 
L 1039.533104 716.034602 
L 1042.379752 716.5208 
L 1045.2264 717.127389 
L 1048.073049 717.560077 
L 1050.919697 718.061549 
L 1053.766345 718.466459 
L 1056.612994 719.005363 
L 1059.459642 719.508795 
L 1062.30629 720.025704 
L 1065.152938 720.693764 
L 1067.999587 721.378795 
L 1070.846235 722.032296 
L 1073.692883 722.564723 
L 1076.539532 723.062478 
L 1079.38618 723.960415 
L 1082.232828 724.466837 
L 1085.079477 724.846156 
L 1087.926125 725.308334 
L 1090.772773 726.012218 
L 1093.619421 726.743845 
L 1096.46607 727.554244 
L 1099.312718 728.259822 
L 1102.159366 729.294373 
L 1105.006015 730.558157 
L 1107.852663 732.14114 
L 1110.699311 733.204097 
L 1113.54596 735.501451 
L 1116.392608 737.791079 
L 1119.239256 739.988747 
L 1122.085904 742.176666 
L 1124.932553 743.924407 
L 1127.779201 745.876364 
L 1130.625849 747.730374 
L 1133.472498 749.788015 
L 1136.319146 751.747809 
L 1139.165794 753.718373 
L 1142.012443 756.490104 
L 1144.859091 758.420867 
L 1144.859091 706.403658 
L 1144.859091 706.403658 
L 1142.012443 706.16039 
L 1139.165794 705.917122 
L 1136.319146 705.675893 
L 1133.472498 705.649308 
L 1130.625849 705.401867 
L 1127.779201 705.203059 
L 1124.932553 704.970907 
L 1122.085904 704.675366 
L 1119.239256 704.51323 
L 1116.392608 704.354694 
L 1113.54596 704.187456 
L 1110.699311 704.023636 
L 1107.852663 703.864312 
L 1105.006015 703.695996 
L 1102.159366 703.263369 
L 1099.312718 702.825417 
L 1096.46607 702.386772 
L 1093.619421 701.837675 
L 1090.772773 700.942897 
L 1087.926125 699.965582 
L 1085.079477 698.708025 
L 1082.232828 697.718545 
L 1079.38618 696.825714 
L 1076.539532 694.749972 
L 1073.692883 692.643422 
L 1070.846235 690.928304 
L 1067.999587 688.074969 
L 1065.152938 685.927307 
L 1062.30629 683.813241 
L 1059.459642 681.657181 
L 1056.612994 679.325108 
L 1053.766345 676.981065 
L 1050.919697 674.650563 
L 1048.073049 672.287776 
L 1045.2264 669.91975 
L 1042.379752 667.637814 
L 1039.533104 665.186985 
L 1036.686455 662.96209 
L 1033.839807 660.624227 
L 1030.993159 658.286365 
L 1028.146511 655.806597 
L 1025.299862 653.352509 
L 1022.453214 650.700726 
L 1019.606566 648.154628 
L 1016.759917 645.776096 
L 1013.913269 643.399664 
L 1011.066621 641.023232 
L 1008.219972 638.6468 
L 1005.373324 636.27511 
L 1002.526676 633.950709 
L 999.680028 631.804845 
L 996.833379 629.658981 
L 993.986731 627.314083 
L 991.140083 624.947421 
L 988.293434 622.392617 
L 985.446786 619.77879 
L 982.600138 617.577926 
L 979.753489 615.40617 
L 976.906841 612.903509 
L 974.060193 610.282731 
L 971.213545 607.753316 
L 968.366896 605.376753 
L 965.520248 603.000321 
L 962.6736 600.583976 
L 959.826951 598.219645 
L 956.980303 595.742322 
L 954.133655 593.220619 
L 951.287006 590.700957 
L 948.440358 588.182765 
L 945.59371 585.670065 
L 942.747062 583.161216 
L 939.900413 580.652366 
L 937.053765 578.143516 
L 934.207117 575.634667 
L 931.360468 573.125817 
L 928.51382 570.713332 
L 925.667172 568.453431 
L 922.820523 566.19353 
L 919.973875 563.933628 
L 917.127227 561.673727 
L 914.280579 559.413826 
L 911.43393 557.153925 
L 908.587282 554.894024 
L 905.740634 552.634123 
L 902.893985 550.374222 
L 900.047337 548.114321 
L 897.200689 545.85442 
L 894.35404 543.497851 
L 891.507392 541.117384 
L 888.660744 538.736917 
L 885.814096 536.357101 
L 882.967447 533.97942 
L 880.120799 531.601739 
L 877.274151 529.224058 
L 874.427502 526.846377 
L 871.580854 524.468696 
L 868.734206 522.091014 
L 865.887557 519.713333 
L 863.040909 517.335652 
z
" clip-path="url(#p86c7ff4003)" style="fill: #ff0000; fill-opacity: 0.15"/>
   </g>
   <g id="matplotlib.axis_11">
    <g id="xtick_31">
     <g id="line2d_124">
      <path d="M 863.040909 793.44 
L 863.040909 504.187826 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_125">
      <g>
       <use xlink:href="#md81fabead9" x="863.040909" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_75">
      <!-- 0 -->
      <g transform="translate(860.040909 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_32">
     <g id="line2d_126">
      <path d="M 928.886279 793.44 
L 928.886279 504.187826 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_127">
      <g>
       <use xlink:href="#md81fabead9" x="928.886279" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_76">
      <!-- 1 -->
      <g transform="translate(925.886279 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_33">
     <g id="line2d_128">
      <path d="M 994.731648 793.44 
L 994.731648 504.187826 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_129">
      <g>
       <use xlink:href="#md81fabead9" x="994.731648" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_77">
      <!-- 2 -->
      <g transform="translate(991.731648 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_34">
     <g id="line2d_130">
      <path d="M 1060.577018 793.44 
L 1060.577018 504.187826 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_131">
      <g>
       <use xlink:href="#md81fabead9" x="1060.577018" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_78">
      <!-- 3 -->
      <g transform="translate(1057.577018 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_35">
     <g id="line2d_132">
      <path d="M 1126.422387 793.44 
L 1126.422387 504.187826 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_133">
      <g>
       <use xlink:href="#md81fabead9" x="1126.422387" y="793.44" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_79">
      <!-- 4 -->
      <g transform="translate(1123.422387 807.510312) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
      </g>
     </g>
    </g>
    <g id="text_80">
     <!-- 心理韧性 -->
     <g transform="translate(983.95 820.54) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_12">
    <g id="ytick_30">
     <g id="line2d_134">
      <path d="M 848.95 772.968173 
L 1158.95 772.968173 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_135">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="772.968173" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_81">
      <!-- 30 -->
      <g transform="translate(829.95 776.503329) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_31">
     <g id="line2d_136">
      <path d="M 848.95 736.348168 
L 1158.95 736.348168 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_137">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="736.348168" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_82">
      <!-- 40 -->
      <g transform="translate(829.95 739.883324) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_32">
     <g id="line2d_138">
      <path d="M 848.95 699.728163 
L 1158.95 699.728163 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_139">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="699.728163" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_83">
      <!-- 50 -->
      <g transform="translate(829.95 703.26332) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_33">
     <g id="line2d_140">
      <path d="M 848.95 663.108159 
L 1158.95 663.108159 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_141">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="663.108159" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_84">
      <!-- 60 -->
      <g transform="translate(829.95 666.643315) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-36"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_34">
     <g id="line2d_142">
      <path d="M 848.95 626.488154 
L 1158.95 626.488154 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_143">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="626.488154" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_85">
      <!-- 70 -->
      <g transform="translate(829.95 630.02331) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-37"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_35">
     <g id="line2d_144">
      <path d="M 848.95 589.868149 
L 1158.95 589.868149 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_145">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="589.868149" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_86">
      <!-- 80 -->
      <g transform="translate(829.95 593.403305) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-38"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_36">
     <g id="line2d_146">
      <path d="M 848.95 553.248144 
L 1158.95 553.248144 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_147">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="553.248144" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_87">
      <!-- 90 -->
      <g transform="translate(829.95 556.7833) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-39"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_37">
     <g id="line2d_148">
      <path d="M 848.95 516.628139 
L 1158.95 516.628139 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_149">
      <g>
       <use xlink:href="#m5de63a5a44" x="848.95" y="516.628139" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_88">
      <!-- 100 -->
      <g transform="translate(823.95 520.163296) scale(0.1 -0.1)">
       <use xlink:href="#LXGWWenKai-Regular-31"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="119.999969"/>
      </g>
     </g>
    </g>
    <g id="text_89">
     <!-- 状态焦虑后测 -->
     <g transform="translate(818.229688 678.813913) rotate(-90) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-540e" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6d4b" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="line2d_150">
    <path d="M 863.040909 675.685876 
L 865.887557 676.172494 
L 868.734206 676.659113 
L 871.580854 677.145731 
L 874.427502 677.63235 
L 877.274151 678.118968 
L 880.120799 678.605586 
L 882.967447 679.092205 
L 885.814096 679.578823 
L 888.660744 680.065441 
L 891.507392 680.55206 
L 894.35404 681.038678 
L 897.200689 681.525296 
L 900.047337 682.011915 
L 902.893985 682.498533 
L 905.740634 682.985152 
L 908.587282 683.47177 
L 911.43393 683.958388 
L 914.280579 684.445007 
L 917.127227 684.931625 
L 919.973875 685.418243 
L 922.820523 685.904862 
L 925.667172 686.39148 
L 928.51382 686.878098 
L 931.360468 687.364717 
L 934.207117 687.851335 
L 937.053765 688.337953 
L 939.900413 688.824572 
L 942.747062 689.31119 
L 945.59371 689.797809 
L 948.440358 690.284427 
L 951.287006 690.771045 
L 954.133655 691.257664 
L 956.980303 691.744282 
L 959.826951 692.2309 
L 962.6736 692.717519 
L 965.520248 693.204137 
L 968.366896 693.690755 
L 971.213545 694.177374 
L 974.060193 694.663992 
L 976.906841 695.15061 
L 979.753489 695.637229 
L 982.600138 696.123847 
L 985.446786 696.610466 
L 988.293434 697.097084 
L 991.140083 697.583702 
L 993.986731 698.070321 
L 996.833379 698.556939 
L 999.680028 699.043557 
L 1002.526676 699.530176 
L 1005.373324 700.016794 
L 1008.219972 700.503412 
L 1011.066621 700.990031 
L 1013.913269 701.476649 
L 1016.759917 701.963267 
L 1019.606566 702.449886 
L 1022.453214 702.936504 
L 1025.299862 703.423123 
L 1028.146511 703.909741 
L 1030.993159 704.396359 
L 1033.839807 704.882978 
L 1036.686455 705.369596 
L 1039.533104 705.856214 
L 1042.379752 706.342833 
L 1045.2264 706.829451 
L 1048.073049 707.316069 
L 1050.919697 707.802688 
L 1053.766345 708.289306 
L 1056.612994 708.775925 
L 1059.459642 709.262543 
L 1062.30629 709.749161 
L 1065.152938 710.23578 
L 1067.999587 710.722398 
L 1070.846235 711.209016 
L 1073.692883 711.695635 
L 1076.539532 712.182253 
L 1079.38618 712.668871 
L 1082.232828 713.15549 
L 1085.079477 713.642108 
L 1087.926125 714.128726 
L 1090.772773 714.615345 
L 1093.619421 715.101963 
L 1096.46607 715.588582 
L 1099.312718 716.0752 
L 1102.159366 716.561818 
L 1105.006015 717.048437 
L 1107.852663 717.535055 
L 1110.699311 718.021673 
L 1113.54596 718.508292 
L 1116.392608 718.99491 
L 1119.239256 719.481528 
L 1122.085904 719.968147 
L 1124.932553 720.454765 
L 1127.779201 720.941383 
L 1130.625849 721.428002 
L 1133.472498 721.91462 
L 1136.319146 722.401239 
L 1139.165794 722.887857 
L 1142.012443 723.374475 
L 1144.859091 723.861094 
" clip-path="url(#p86c7ff4003)" style="fill: none; stroke: #ff0000; stroke-width: 2.25; stroke-linecap: square"/>
   </g>
   <g id="patch_18">
    <path d="M 848.95 793.44 
L 848.95 504.187826 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_19">
    <path d="M 848.95 793.44 
L 1158.95 793.44 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_90">
    <!-- r = -0.280, p = 0.121 -->
    <g transform="translate(943.037813 498.187826) scale(0.12 -0.12)">
     <use xlink:href="#LXGWWenKai-Regular-72"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="42.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="77.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="137.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-2d" x="172.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="207.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="267.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="302.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-38" x="362.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="422.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-2c" x="482.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="517.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-70" x="552.999817"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="610.199814"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="645.199799"/>
     <use xlink:href="#LXGWWenKai-Regular-20" x="705.199783"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="740.199768"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="800.199753"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="835.199738"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="895.199722"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="955.199707"/>
    </g>
   </g>
  </g>
  <g id="text_91">
   <!-- 相关性分析 -->
   <g transform="translate(547.95 17.713125) scale(0.14 -0.14)">
    <defs>
     <path id="LXGWWenKai-Regular-76f8" d="M 819 3347 
L 1722 3424 
L 1734 4621 
Q 1734 4794 1676 4874 
Q 1619 4954 1619 4986 
Q 1619 5037 1702 5037 
L 1766 5030 
Q 2131 4960 2131 4781 
L 2112 3462 
L 2483 3494 
Q 2605 3507 2669 3529 
Q 2733 3552 2771 3552 
Q 2810 3552 2886 3494 
Q 3091 3354 3091 3258 
Q 3091 3187 2944 3174 
L 2112 3104 
L 2106 2566 
Q 2189 2630 2240 2630 
Q 2291 2630 2438 2518 
Q 2586 2406 2822 2150 
Q 3059 1894 3059 1830 
Q 3059 1766 2979 1686 
Q 2899 1606 2832 1606 
Q 2765 1606 2675 1728 
Q 2438 2042 2099 2355 
L 2067 -474 
Q 2067 -640 1958 -640 
Q 1818 -640 1718 -547 
Q 1619 -454 1619 -390 
Q 1619 -326 1651 -192 
Q 1683 -58 1683 166 
L 1690 346 
Q 1690 1830 1728 2368 
L 1728 2458 
Q 1523 1843 973 1197 
Q 749 934 566 780 
Q 384 627 339 627 
Q 294 627 294 675 
Q 294 723 371 826 
Q 1101 1754 1632 3059 
L 1024 3008 
Q 934 2995 858 2995 
Q 698 2995 598 3126 
Q 499 3258 499 3325 
Q 499 3392 531 3392 
Q 614 3379 684 3363 
Q 755 3347 819 3347 
z
M 5696 4013 
L 5651 3846 
L 5562 410 
Q 5734 237 5734 160 
Q 5734 83 5683 57 
Q 5632 32 5562 26 
L 3680 -38 
L 3686 -339 
Q 3686 -474 3584 -474 
L 3494 -448 
Q 3245 -390 3245 -224 
Q 3245 -186 3264 -93 
Q 3283 0 3283 275 
L 3219 3757 
Q 3219 3968 3177 4073 
Q 3136 4179 3136 4218 
Q 3136 4282 3235 4282 
Q 3334 4282 3629 4141 
L 5306 4250 
L 5414 4256 
Q 5606 4256 5670 4090 
Q 5696 4032 5696 4013 
z
M 5235 3891 
L 3616 3789 
L 3629 2944 
L 5216 3034 
L 5235 3891 
z
M 5210 2682 
L 3635 2598 
L 3654 1677 
L 5190 1760 
L 5210 2682 
z
M 5184 1402 
L 3661 1331 
L 3674 333 
L 5165 384 
L 5184 1402 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5173" d="M 1862 4723 
Q 1760 4826 1926 4947 
Q 2029 5018 2083 5002 
Q 2138 4986 2301 4800 
Q 2464 4614 2672 4281 
Q 2880 3949 2854 3865 
Q 2829 3782 2720 3731 
Q 2611 3680 2556 3696 
Q 2502 3712 2374 3981 
Q 2246 4250 1862 4723 
z
M 1120 1818 
Q 1005 1818 902 1850 
Q 762 1933 685 2125 
Q 672 2163 672 2176 
Q 672 2234 736 2234 
Q 762 2234 838 2214 
Q 915 2195 1069 2195 
L 1133 2195 
L 2829 2278 
Q 2918 2662 2938 3258 
L 1587 3181 
L 1459 3174 
Q 1286 3174 1229 3232 
Q 1056 3398 1056 3565 
Q 1056 3610 1088 3610 
Q 1126 3610 1206 3584 
Q 1286 3558 1414 3558 
L 1446 3558 
L 3309 3667 
Q 3322 3699 3392 3782 
Q 4045 4730 4026 5030 
Q 4013 5267 4275 5120 
Q 4378 5056 4458 4982 
Q 4538 4909 4544 4864 
Q 4550 4819 4493 4717 
Q 4096 4070 3686 3757 
Q 3565 3686 3546 3680 
L 4634 3744 
Q 4794 3757 4845 3776 
Q 4896 3795 4925 3795 
Q 4954 3795 5024 3731 
Q 5235 3578 5235 3469 
Q 5235 3386 5075 3366 
L 3386 3277 
Q 3360 2701 3270 2298 
L 4954 2374 
Q 5146 2394 5210 2416 
Q 5274 2438 5315 2438 
Q 5357 2438 5446 2381 
Q 5670 2234 5670 2112 
Q 5670 2029 5504 2010 
L 3398 1914 
Q 4122 864 5005 339 
Q 5478 58 6067 -134 
Q 6202 -173 6202 -221 
Q 6202 -269 6138 -346 
Q 5965 -544 5837 -544 
L 5651 -474 
Q 5158 -282 4662 73 
Q 4166 429 3792 809 
Q 3418 1190 3110 1677 
Q 3008 1408 2957 1299 
Q 2528 429 1734 -51 
Q 1139 -416 614 -525 
Q 422 -563 364 -563 
Q 307 -563 307 -506 
Q 307 -435 538 -326 
Q 2330 480 2726 1882 
L 1120 1818 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
     <path id="LXGWWenKai-Regular-6790" d="M 3501 4141 
Q 3968 4314 4291 4499 
Q 4614 4685 4896 4864 
Q 5005 4941 5056 5046 
Q 5107 5152 5174 5152 
Q 5242 5152 5366 4989 
Q 5491 4826 5491 4762 
Q 5491 4698 5408 4653 
Q 4730 4282 4317 4122 
Q 3904 3962 3526 3834 
L 3526 3411 
Q 3526 3206 3520 3014 
L 5504 3130 
Q 5683 3149 5750 3171 
Q 5818 3194 5853 3194 
Q 5888 3194 5971 3139 
Q 6054 3085 6118 3011 
Q 6182 2938 6182 2893 
Q 6182 2810 6029 2797 
L 4966 2739 
L 4966 -435 
Q 4966 -614 4851 -614 
Q 4838 -614 4755 -589 
Q 4512 -512 4512 -346 
Q 4512 -294 4534 -195 
Q 4557 -96 4557 134 
L 4570 2714 
L 3501 2656 
Q 3430 1357 3008 538 
Q 2829 192 2656 -6 
Q 2483 -205 2368 -288 
Q 2253 -371 2233 -371 
Q 2214 -371 2214 -326 
Q 2214 -282 2304 -134 
Q 2618 358 2816 973 
Q 3117 1926 3117 3360 
Q 3117 3789 3097 3904 
Q 3078 4019 3043 4105 
Q 3008 4192 3008 4240 
Q 3008 4288 3101 4288 
Q 3194 4288 3501 4141 
z
M 467 3373 
Q 627 3341 704 3341 
L 1549 3398 
L 1562 4614 
Q 1562 4762 1504 4848 
Q 1446 4934 1446 4954 
Q 1446 5011 1526 5011 
Q 1606 5011 1744 4960 
Q 1882 4909 1917 4867 
Q 1952 4826 1952 4755 
L 1933 3430 
L 2304 3456 
Q 2438 3469 2496 3488 
Q 2554 3507 2595 3507 
Q 2637 3507 2710 3465 
Q 2784 3424 2841 3360 
Q 2899 3296 2899 3245 
Q 2899 3155 2758 3142 
L 1926 3078 
L 1920 2534 
Q 1990 2586 2042 2586 
Q 2144 2586 2458 2227 
Q 2778 1882 2778 1795 
Q 2778 1709 2685 1635 
Q 2592 1562 2528 1562 
Q 2464 1562 2387 1677 
Q 2182 1990 1920 2285 
L 1882 -461 
Q 1882 -614 1766 -614 
Q 1696 -614 1574 -547 
Q 1453 -480 1453 -352 
Q 1453 -307 1478 -211 
Q 1504 -115 1504 122 
L 1510 307 
Q 1510 1792 1549 2291 
L 1549 2368 
Q 1427 1965 1084 1469 
Q 742 973 474 730 
Q 352 621 301 621 
Q 262 621 262 672 
Q 262 723 333 819 
Q 954 1670 1459 3046 
L 941 3008 
Q 826 2995 726 2995 
Q 627 2995 553 3078 
Q 480 3162 451 3248 
Q 422 3334 422 3341 
Q 422 3373 467 3373 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#LXGWWenKai-Regular-76f8"/>
    <use xlink:href="#LXGWWenKai-Regular-5173" x="99.999985"/>
    <use xlink:href="#LXGWWenKai-Regular-6027" x="199.999969"/>
    <use xlink:href="#LXGWWenKai-Regular-5206" x="299.999954"/>
    <use xlink:href="#LXGWWenKai-Regular-6790" x="399.999939"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p5541723dd9">
   <rect x="42.95" y="128.16" width="310" height="289.252174"/>
  </clipPath>
  <clipPath id="p4a1cad7ee5">
   <rect x="445.95" y="128.16" width="310" height="289.252174"/>
  </clipPath>
  <clipPath id="p5189e6b98e">
   <rect x="848.95" y="128.16" width="310" height="289.252174"/>
  </clipPath>
  <clipPath id="p3f173ec3de">
   <rect x="42.95" y="504.187826" width="310" height="289.252174"/>
  </clipPath>
  <clipPath id="p4261c2201a">
   <rect x="445.95" y="504.187826" width="310" height="289.252174"/>
  </clipPath>
  <clipPath id="p86c7ff4003">
   <rect x="848.95" y="504.187826" width="310" height="289.252174"/>
  </clipPath>
 </defs>
</svg>

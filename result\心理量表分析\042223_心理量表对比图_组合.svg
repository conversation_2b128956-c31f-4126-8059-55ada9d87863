<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1080pt" height="864pt" viewBox="0 0 1080 864" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-04-22T23:21:02.425345</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.9.4, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 864 
L 1080 864 
L 1080 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 48.17 398.884375 
L 534.54 398.884375 
L 534.54 25.0875 
L 48.17 25.0875 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_3">
    <path d="M 70.277727 852.106666 
L 112.103157 852.106666 
L 112.103157 281.357826 
L 70.277727 281.357826 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_4">
    <path d="M 189.778956 852.106666 
L 231.604386 852.106666 
L 231.604386 310.234999 
L 189.778956 310.234999 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_5">
    <path d="M 309.280184 852.106666 
L 351.105614 852.106666 
L 351.105614 288.718674 
L 309.280184 288.718674 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_6">
    <path d="M 428.781413 852.106666 
L 470.606843 852.106666 
L 470.606843 230.284864 
L 428.781413 230.284864 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_7">
    <path d="M 112.103157 852.106666 
L 153.928587 852.106666 
L 153.928587 145.706832 
L 112.103157 145.706832 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_8">
    <path d="M 231.604386 852.106666 
L 273.429816 852.106666 
L 273.429816 150.070493 
L 231.604386 150.070493 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_9">
    <path d="M 351.105614 852.106666 
L 392.931044 852.106666 
L 392.931044 194.348826 
L 351.105614 194.348826 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_10">
    <path d="M 470.606843 852.106666 
L 512.432273 852.106666 
L 512.432273 107.203934 
L 470.606843 107.203934 
z
" clip-path="url(#p6b8b5df46e)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 112.103157 398.884375 
L 112.103157 25.0875 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mf98408d2ab" d="M 0 0 
L 0 3.5 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#mf98408d2ab" x="112.103157" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_1">
      <!-- 总体韧性 -->
      <g transform="translate(96.103157 411.884375) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-603b" d="M 4454 3475 
L 1888 3379 
L 2003 2259 
L 4282 2323 
L 4454 3475 
z
M 1773 1178 
Q 1773 1318 2010 1318 
Q 2112 1318 2150 1178 
Q 2246 774 2428 492 
Q 2611 211 2972 67 
Q 3334 -77 3926 -77 
Q 4518 -77 4518 -19 
L 4461 141 
Q 4250 717 4250 841 
Q 4250 966 4307 966 
Q 4384 966 4502 742 
Q 4621 518 4822 246 
Q 5024 -26 5053 -77 
Q 5082 -128 5082 -192 
Q 5082 -256 4986 -333 
Q 4890 -410 4694 -442 
Q 4499 -474 4090 -474 
Q 3072 -474 2566 -179 
Q 1984 179 1805 1024 
L 1773 1178 
z
M 3565 3795 
L 4557 3840 
L 4621 3840 
Q 4768 3840 4848 3757 
Q 4928 3674 4928 3619 
Q 4928 3565 4909 3539 
Q 4890 3514 4883 3488 
L 4678 2330 
Q 4819 2125 4819 2057 
Q 4819 1990 4764 1980 
Q 4710 1971 4627 1965 
L 2035 1901 
L 2048 1779 
L 2048 1760 
Q 2048 1613 1920 1613 
Q 1811 1613 1705 1686 
Q 1600 1760 1600 1882 
Q 1619 2016 1619 2106 
L 1619 2227 
Q 1619 2266 1613 2310 
L 1504 3366 
Q 1478 3578 1424 3683 
Q 1370 3789 1370 3824 
Q 1370 3859 1453 3859 
Q 1536 3859 1888 3725 
L 3104 3776 
Q 3648 4352 3853 4666 
Q 4090 5030 4093 5139 
Q 4096 5248 4160 5248 
Q 4224 5248 4314 5190 
Q 4544 5030 4544 4912 
Q 4544 4794 3930 4166 
L 3565 3795 
z
M 864 -179 
Q 794 -288 723 -288 
Q 710 -288 640 -256 
Q 454 -179 454 -64 
Q 454 -26 506 58 
Q 896 634 1107 1274 
Q 1146 1389 1222 1389 
Q 1280 1389 1369 1350 
Q 1459 1312 1459 1213 
Q 1459 1114 1248 618 
Q 1037 122 864 -179 
z
M 5632 397 
Q 5261 838 4819 1210 
Q 4736 1286 4736 1340 
Q 4736 1395 4803 1472 
Q 4870 1549 4918 1549 
Q 4966 1549 5168 1405 
Q 5370 1261 5709 947 
Q 6067 595 6067 505 
Q 6067 416 5964 329 
Q 5862 243 5811 243 
Q 5760 243 5632 397 
z
M 2822 1344 
Q 2726 1427 2726 1481 
Q 2726 1536 2800 1603 
Q 2874 1670 2909 1670 
Q 2944 1670 3129 1542 
Q 3315 1414 3584 1164 
Q 3853 915 3853 838 
Q 3853 762 3760 669 
Q 3667 576 3616 576 
Q 3565 576 3363 822 
Q 3162 1069 2822 1344 
z
M 2432 4077 
Q 2189 4435 1811 4800 
Q 1747 4864 1747 4928 
Q 1747 4992 1836 5053 
Q 1926 5114 1961 5114 
Q 1997 5114 2102 5030 
Q 2208 4947 2339 4819 
Q 2470 4691 2592 4556 
Q 2714 4422 2794 4316 
Q 2874 4211 2874 4176 
Q 2874 4141 2787 4038 
Q 2701 3936 2617 3936 
Q 2534 3936 2432 4077 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-4f53" d="M 2086 3629 
L 2400 3597 
L 2483 3597 
L 3731 3667 
L 3738 4755 
Q 3738 4922 3680 4998 
Q 3622 5075 3622 5107 
Q 3622 5158 3692 5158 
Q 3763 5158 3866 5133 
Q 4122 5069 4122 4934 
L 4115 3686 
L 5082 3744 
Q 5280 3757 5344 3782 
Q 5408 3808 5456 3808 
Q 5504 3808 5593 3757 
Q 5683 3706 5750 3635 
Q 5818 3565 5818 3510 
Q 5818 3456 5702 3443 
L 4205 3360 
Q 5101 1882 6234 902 
Q 6317 838 6317 803 
Q 6317 768 6272 710 
Q 6138 550 6003 550 
Q 5946 550 5894 595 
Q 5120 1357 4365 2464 
Q 4262 2611 4096 3008 
L 4096 2912 
Q 4115 2624 4115 2438 
L 4109 1555 
L 4109 1075 
L 4435 1094 
Q 4595 1101 4678 1133 
Q 4762 1165 4810 1165 
Q 4858 1165 4941 1114 
Q 5171 979 5171 864 
Q 5171 794 5069 781 
L 5056 781 
L 4109 730 
L 4102 -422 
Q 4102 -602 3984 -602 
Q 3866 -602 3763 -499 
Q 3661 -397 3661 -339 
Q 3712 70 3712 230 
L 3712 710 
L 3360 691 
Q 3296 685 3245 685 
L 3149 685 
Q 2995 685 2918 742 
Q 2842 800 2794 899 
Q 2746 998 2746 1030 
Q 2746 1062 2784 1062 
L 3091 1024 
L 3181 1030 
L 3718 1056 
L 3718 1190 
L 3725 1965 
L 3725 2394 
Q 3725 2611 3744 2957 
L 3750 3078 
Q 3558 2381 3052 1635 
Q 2547 890 2157 531 
Q 1984 371 1929 371 
Q 1875 371 1875 428 
Q 1875 486 1971 608 
Q 2899 1792 3462 3315 
L 2669 3270 
Q 2515 3258 2403 3258 
Q 2291 3258 2208 3331 
Q 2125 3405 2086 3488 
Q 2048 3571 2048 3590 
Q 2048 3629 2086 3629 
z
M 1779 4838 
L 1754 5005 
Q 1754 5062 1827 5062 
Q 1901 5062 1997 5020 
Q 2093 4979 2166 4912 
Q 2240 4845 2240 4781 
Q 2240 4717 2150 4506 
Q 1939 3974 1619 3443 
L 1619 -416 
Q 1619 -538 1510 -538 
Q 1376 -538 1286 -458 
Q 1197 -378 1197 -320 
L 1197 -250 
Q 1197 -230 1216 -121 
Q 1235 -13 1235 147 
L 1261 2899 
Q 819 2304 461 1997 
Q 307 1869 259 1869 
Q 211 1869 211 1923 
Q 211 1978 294 2086 
Q 947 2963 1395 3853 
Q 1779 4640 1779 4838 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-97e7" d="M 5037 -19 
Q 5075 -19 5075 26 
Q 5402 1184 5446 4045 
L 4704 3994 
Q 4666 3072 4544 2489 
Q 4422 1907 4307 1626 
Q 3872 518 3238 -51 
Q 2989 -275 2813 -364 
Q 2637 -454 2585 -454 
Q 2534 -454 2534 -403 
Q 2534 -352 2662 -243 
Q 3469 435 3949 1760 
Q 4141 2278 4237 3142 
Q 4288 3552 4301 3968 
L 3520 3923 
Q 3418 3910 3293 3910 
Q 3168 3910 3085 4026 
Q 2970 4198 2970 4249 
Q 2970 4301 3021 4301 
Q 3046 4301 3104 4285 
Q 3162 4269 3283 4269 
L 5504 4416 
L 5574 4416 
Q 5722 4416 5789 4329 
Q 5856 4243 5856 4179 
L 5830 4019 
Q 5811 2381 5645 845 
Q 5562 154 5478 -166 
Q 5440 -314 5321 -429 
Q 5203 -544 5113 -544 
Q 5024 -544 4829 -432 
Q 4634 -320 4330 -29 
Q 4026 262 4026 371 
Q 4026 416 4086 416 
Q 4147 416 4387 269 
Q 4627 122 5005 -13 
Q 5018 -19 5037 -19 
z
M 813 1638 
L 659 1632 
Q 538 1626 464 1706 
Q 390 1786 361 1885 
Q 333 1984 333 2003 
Q 333 2022 368 2022 
Q 403 2022 467 2000 
Q 531 1978 646 1984 
L 698 1984 
L 1370 2029 
L 1363 2707 
L 1094 2694 
Q 1050 2682 1005 2682 
L 934 2682 
Q 819 2682 764 2736 
Q 710 2790 662 2886 
Q 614 2982 614 3014 
Q 614 3046 649 3046 
Q 685 3046 742 3030 
Q 800 3014 922 3014 
L 966 3014 
L 1363 3046 
L 1363 3757 
L 941 3725 
Q 832 3712 745 3709 
Q 659 3706 588 3782 
Q 518 3859 486 3945 
Q 454 4032 454 4064 
Q 454 4096 489 4096 
Q 525 4096 589 4077 
Q 653 4058 755 4058 
L 1363 4102 
L 1363 4550 
Q 1363 4717 1312 4803 
Q 1261 4890 1261 4909 
Q 1261 4960 1341 4963 
Q 1421 4966 1542 4915 
Q 1664 4864 1693 4822 
Q 1722 4781 1722 4717 
L 1722 4128 
L 2266 4166 
Q 2438 4186 2476 4205 
Q 2515 4224 2560 4224 
Q 2605 4224 2678 4176 
Q 2752 4128 2803 4061 
Q 2854 3994 2854 3949 
Q 2854 3872 2714 3859 
L 1722 3782 
L 1728 3078 
L 2221 3110 
Q 2259 3117 2297 3123 
Q 2336 3130 2374 3146 
Q 2413 3162 2461 3162 
Q 2509 3162 2579 3117 
Q 2752 3008 2752 2893 
Q 2752 2822 2611 2810 
L 1728 2739 
L 1728 2048 
L 2509 2106 
Q 2528 2112 2547 2112 
L 2579 2112 
Q 2701 2112 2742 2054 
Q 2784 1997 2803 1946 
Q 2803 1907 2816 1894 
L 2816 1888 
Q 2810 1862 2800 1827 
Q 2790 1792 2784 1747 
L 2726 685 
L 2726 499 
Q 2726 403 2659 288 
Q 2592 173 2505 173 
Q 2419 173 2342 237 
Q 2131 358 1875 742 
Q 1830 819 1830 848 
Q 1830 877 1881 880 
Q 1933 883 2064 796 
Q 2195 710 2368 646 
L 2419 1741 
L 1728 1702 
L 1734 -506 
Q 1734 -646 1632 -653 
Q 1574 -653 1456 -589 
Q 1338 -525 1338 -435 
L 1338 -378 
Q 1338 -358 1354 -272 
Q 1370 -186 1370 -13 
L 1370 1677 
L 813 1638 
z
M 3814 3130 
Q 3814 2957 3638 2393 
Q 3462 1830 3414 1702 
Q 3366 1574 3340 1545 
Q 3315 1517 3283 1517 
Q 3251 1517 3181 1536 
Q 2982 1600 2982 1690 
Q 2982 1722 3065 1894 
Q 3149 2067 3270 2457 
Q 3392 2848 3427 3040 
Q 3462 3232 3481 3261 
Q 3501 3290 3552 3290 
Q 3603 3290 3708 3274 
Q 3814 3258 3814 3155 
L 3814 3130 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6027" d="M 1274 4742 
Q 1274 4902 1219 4972 
Q 1165 5043 1165 5075 
Q 1165 5126 1248 5126 
Q 1267 5126 1370 5107 
Q 1677 5037 1677 4909 
L 1658 -512 
Q 1658 -640 1568 -640 
L 1472 -614 
Q 1184 -538 1184 -365 
Q 1184 -320 1213 -185 
Q 1242 -51 1242 128 
L 1274 4742 
z
M 6240 0 
Q 6240 -83 6086 -83 
L 2630 -154 
L 2522 -154 
Q 2323 -154 2246 -61 
Q 2170 32 2141 131 
Q 2112 230 2112 252 
Q 2112 275 2134 275 
Q 2157 275 2253 243 
Q 2349 211 2502 211 
L 3962 243 
L 3981 1562 
L 3258 1530 
Q 3149 1530 3056 1581 
Q 2963 1632 2893 1850 
Q 2880 1869 2880 1894 
Q 2880 1920 2902 1920 
Q 2925 1920 2973 1901 
Q 3021 1882 3181 1882 
L 3264 1882 
L 3981 1920 
L 3994 3027 
L 3098 2976 
Q 2848 2458 2534 2086 
Q 2400 1926 2352 1926 
Q 2304 1926 2304 1980 
Q 2304 2035 2355 2138 
Q 2714 2810 2880 3482 
Q 2957 3763 2957 3904 
L 2931 4102 
Q 2931 4160 2988 4160 
Q 3046 4160 3155 4109 
Q 3450 3955 3450 3834 
Q 3450 3814 3443 3802 
L 3264 3347 
L 4000 3386 
L 4013 4608 
Q 4013 4781 3968 4864 
Q 3923 4947 3923 4969 
Q 3923 4992 3993 4992 
Q 4064 4992 4205 4941 
Q 4346 4890 4381 4848 
Q 4416 4806 4416 4717 
L 4397 3411 
L 5126 3450 
Q 5261 3456 5318 3481 
Q 5376 3507 5421 3507 
Q 5466 3507 5549 3452 
Q 5632 3398 5696 3324 
Q 5760 3251 5760 3200 
Q 5760 3130 5619 3110 
L 4397 3046 
L 4384 1939 
L 4890 1965 
Q 5107 1978 5158 2000 
Q 5210 2022 5254 2022 
Q 5299 2022 5376 1971 
Q 5581 1830 5581 1709 
Q 5581 1632 5427 1619 
L 4378 1574 
L 4358 256 
L 5542 282 
Q 5722 282 5795 304 
Q 5869 326 5904 326 
Q 5939 326 6022 272 
Q 6106 218 6173 141 
Q 6240 64 6240 0 
z
M 1811 3789 
Q 1811 3878 2029 3930 
Q 2112 3930 2237 3632 
Q 2362 3334 2438 3088 
Q 2515 2842 2515 2806 
Q 2515 2771 2435 2720 
Q 2355 2669 2256 2669 
Q 2157 2669 2125 2784 
Q 2029 3238 1837 3693 
Q 1830 3725 1820 3747 
Q 1811 3770 1811 3789 
z
M 730 3789 
L 794 3782 
Q 896 3770 928 3734 
Q 960 3699 960 3629 
L 960 3603 
Q 922 2867 730 2067 
Q 698 1952 595 1952 
Q 493 1952 406 2006 
Q 320 2061 320 2105 
Q 320 2150 384 2349 
Q 538 2854 595 3642 
Q 602 3789 730 3789 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-603b"/>
       <use xlink:href="#LXGWWenKai-Regular-4f53" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 231.604386 398.884375 
L 231.604386 25.0875 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mf98408d2ab" x="231.604386" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_2">
      <!-- 坚韧性 -->
      <g transform="translate(219.604386 411.731875) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-575a" d="M 2170 1645 
Q 1811 1728 1811 1907 
L 1811 1946 
Q 1811 1965 1836 2077 
Q 1862 2189 1862 2342 
L 1894 4531 
Q 1894 4659 1843 4729 
Q 1792 4800 1792 4838 
Q 1792 4902 1869 4902 
Q 2022 4902 2189 4813 
Q 2272 4768 2272 4685 
L 2253 1779 
Q 2253 1645 2170 1645 
z
M 934 3981 
Q 934 4109 883 4176 
Q 832 4243 832 4282 
Q 832 4346 909 4346 
Q 1069 4346 1190 4278 
Q 1312 4211 1312 4134 
L 1293 2349 
Q 1293 2214 1210 2214 
L 1203 2221 
Q 1101 2221 976 2297 
Q 851 2374 851 2477 
L 851 2515 
Q 851 2534 876 2646 
Q 902 2758 902 2912 
L 934 3981 
z
M 1498 1114 
Q 1683 1075 1798 1075 
L 1862 1075 
L 3008 1114 
L 3008 1331 
Q 3008 1485 2918 1613 
Q 2886 1677 2886 1696 
Q 2886 1741 2960 1741 
Q 3034 1741 3174 1689 
Q 3315 1638 3363 1596 
Q 3411 1555 3411 1485 
L 3405 1126 
L 4179 1165 
Q 4346 1178 4429 1210 
Q 4512 1242 4553 1242 
Q 4595 1242 4672 1197 
Q 4883 1069 4883 947 
Q 4883 864 4730 851 
L 3398 800 
L 3386 58 
L 5382 115 
Q 5542 115 5628 147 
Q 5715 179 5760 179 
Q 5805 179 5882 128 
Q 6074 0 6074 -128 
Q 6074 -224 5920 -237 
L 877 -371 
L 736 -378 
Q 595 -378 512 -314 
Q 429 -250 346 -19 
Q 339 -6 339 16 
Q 339 38 364 38 
Q 390 38 480 12 
Q 570 -13 704 -13 
L 749 -13 
L 3002 51 
L 3008 781 
L 2010 742 
L 1907 742 
Q 1798 742 1676 784 
Q 1555 826 1472 1050 
Q 1466 1062 1466 1088 
Q 1466 1114 1498 1114 
z
M 2810 4589 
Q 3085 4563 3110 4563 
L 4890 4698 
L 4947 4698 
Q 5107 4704 5193 4630 
Q 5280 4557 5280 4502 
Q 5280 4448 5251 4419 
Q 5222 4390 5210 4358 
Q 4947 3597 4403 2944 
Q 4992 2477 5773 2163 
Q 5888 2118 5888 2054 
Q 5888 1971 5670 1856 
Q 5594 1811 5530 1811 
Q 5466 1811 5075 2022 
Q 4685 2234 4154 2669 
Q 3565 2086 2938 1862 
Q 2643 1754 2592 1754 
Q 2541 1754 2541 1811 
Q 2541 1869 2675 1952 
Q 3424 2362 3891 2893 
Q 3411 3328 3196 3606 
Q 2982 3885 2982 3949 
Q 2982 4013 3078 4077 
Q 3155 4115 3216 4115 
Q 3277 4115 3322 4045 
Q 3635 3642 4122 3174 
Q 4589 3789 4736 4358 
L 3398 4250 
Q 3085 4218 3014 4262 
Q 2854 4378 2803 4531 
Q 2790 4538 2790 4563 
Q 2790 4589 2810 4589 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-575a"/>
       <use xlink:href="#LXGWWenKai-Regular-97e7" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6027" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 351.105614 398.884375 
L 351.105614 25.0875 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mf98408d2ab" x="351.105614" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_3">
      <!-- 乐观性 -->
      <g transform="translate(339.105614 411.715625) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-4e50" d="M 2061 102 
Q 2061 141 2125 141 
Q 2189 141 2486 25 
Q 2784 -90 3072 -141 
L 3059 2118 
L 1280 2042 
L 1280 2048 
Q 1107 2106 1107 2291 
Q 1107 2342 1120 2400 
Q 1363 3130 1472 3974 
Q 1472 4179 1437 4284 
Q 1402 4390 1402 4451 
Q 1402 4512 1504 4512 
Q 1606 4512 1933 4352 
Q 2662 4429 3465 4662 
Q 4269 4896 4499 5094 
Q 4518 5114 4553 5114 
Q 4589 5114 4653 5050 
Q 4826 4896 4826 4784 
Q 4826 4672 4730 4634 
Q 3283 4128 1920 4032 
Q 1798 3091 1555 2419 
L 3053 2496 
L 3053 3386 
Q 3053 3533 2998 3619 
Q 2944 3706 2944 3738 
Q 2944 3795 3059 3795 
Q 3174 3795 3366 3712 
Q 3469 3667 3469 3578 
L 3469 2522 
L 4928 2605 
Q 5133 2624 5190 2646 
Q 5248 2669 5277 2669 
Q 5306 2669 5395 2605 
Q 5638 2445 5638 2336 
Q 5638 2266 5472 2246 
L 3462 2144 
L 3488 -109 
L 3494 -301 
Q 3494 -410 3417 -512 
Q 3341 -614 3235 -614 
Q 3130 -614 2909 -515 
Q 2688 -416 2374 -198 
Q 2061 19 2061 102 
z
M 582 -141 
Q 550 -141 550 -105 
Q 550 -70 653 19 
Q 1594 845 1824 1350 
Q 1920 1549 1926 1619 
Q 1933 1690 1958 1690 
Q 2099 1690 2304 1478 
Q 2394 1395 2397 1353 
Q 2400 1312 2362 1248 
Q 1888 659 1302 265 
Q 717 -128 582 -141 
z
M 5408 134 
Q 4806 838 4166 1414 
Q 4115 1459 4115 1484 
Q 4115 1510 4115 1536 
Q 4115 1562 4201 1651 
Q 4288 1741 4339 1737 
Q 4390 1734 4585 1574 
Q 4781 1414 5197 1008 
Q 5613 602 5709 499 
Q 5805 397 5801 326 
Q 5798 256 5683 157 
Q 5568 58 5542 58 
Q 5517 58 5491 58 
Q 5466 58 5408 134 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-89c2" d="M 627 4301 
L 890 4275 
L 947 4275 
L 2470 4365 
L 2541 4365 
Q 2630 4365 2723 4301 
Q 2816 4237 2816 4173 
Q 2816 4109 2790 4070 
Q 2765 4032 2701 3731 
Q 2528 2912 2176 2118 
Q 2586 1606 3040 966 
Q 3117 870 3117 809 
Q 3117 749 3062 694 
Q 3008 640 2944 608 
Q 2880 576 2835 576 
Q 2790 576 2733 672 
Q 2413 1184 1990 1741 
Q 1491 845 870 198 
Q 621 -51 457 -176 
Q 294 -301 256 -301 
Q 218 -301 218 -240 
Q 218 -179 294 -102 
Q 1165 864 1734 2061 
Q 1325 2643 915 3110 
Q 851 3194 851 3251 
Q 851 3309 940 3373 
Q 1030 3437 1075 3437 
Q 1120 3437 1235 3302 
Q 1466 3014 1894 2451 
Q 2182 3187 2298 4000 
L 1094 3910 
Q 1037 3904 992 3904 
L 909 3904 
Q 781 3904 720 3965 
Q 659 4026 601 4128 
Q 544 4230 544 4265 
Q 544 4301 595 4301 
L 627 4301 
z
M 2374 -506 
Q 2310 -531 2288 -531 
Q 2266 -531 2266 -457 
Q 2266 -384 2374 -320 
Q 3277 205 3648 851 
Q 3853 1222 3945 1708 
Q 4038 2195 4038 3552 
Q 4038 3635 3977 3712 
Q 3917 3789 3917 3827 
Q 3917 3891 3997 3891 
Q 4077 3891 4224 3849 
Q 4371 3808 4416 3773 
Q 4461 3738 4461 3654 
Q 4442 2675 4397 2131 
Q 4563 2086 4611 2038 
Q 4659 1990 4659 1894 
Q 4659 1798 4640 320 
Q 4640 141 4675 61 
Q 4710 -19 4825 -35 
Q 4941 -51 5277 -51 
Q 5613 -51 5728 -22 
Q 5843 6 5894 166 
Q 5990 493 6038 832 
Q 6086 1171 6163 1171 
Q 6195 1171 6233 1036 
Q 6272 902 6272 553 
Q 6272 205 6253 22 
Q 6234 -160 6147 -259 
Q 6061 -358 5865 -396 
Q 5670 -435 5264 -435 
Q 4858 -435 4650 -387 
Q 4442 -339 4346 -198 
Q 4250 -58 4250 218 
L 4250 243 
L 4262 1344 
Q 4186 1069 4064 832 
Q 3629 -6 2374 -506 
z
M 3514 2451 
L 3526 2029 
L 3533 1696 
L 3533 1638 
Q 3533 1504 3430 1504 
Q 3354 1504 3232 1571 
Q 3110 1638 3110 1728 
L 3110 1760 
Q 3130 1907 3130 2035 
L 3130 2138 
L 3078 4352 
Q 3078 4518 3020 4624 
Q 2963 4730 2963 4771 
Q 2963 4813 3043 4813 
Q 3123 4813 3462 4691 
L 5152 4800 
L 5222 4800 
Q 5318 4800 5420 4729 
Q 5523 4659 5523 4604 
Q 5523 4550 5510 4515 
Q 5498 4480 5491 4448 
L 5421 1664 
Q 5414 1530 5312 1536 
Q 5235 1536 5116 1606 
Q 4998 1677 4998 1766 
L 4998 1798 
Q 5024 1946 5024 2074 
L 5030 2176 
L 5069 4448 
L 3462 4352 
L 3514 2451 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-4e50"/>
       <use xlink:href="#LXGWWenKai-Regular-89c2" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6027" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 470.606843 398.884375 
L 470.606843 25.0875 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mf98408d2ab" x="470.606843" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_4">
      <!-- 力量性 -->
      <g transform="translate(458.606843 411.771875) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-529b" d="M 5171 3635 
L 5267 3642 
Q 5376 3642 5475 3562 
Q 5574 3482 5574 3414 
Q 5574 3347 5555 3305 
Q 5536 3264 5530 3232 
Q 5478 2118 5347 1315 
Q 5216 512 5155 262 
Q 5094 13 5059 -153 
Q 5024 -320 4909 -429 
Q 4794 -538 4662 -538 
Q 4531 -538 4396 -454 
Q 4262 -371 3990 -182 
Q 3718 6 3404 281 
Q 3091 557 3091 637 
Q 3091 717 3161 717 
Q 3232 717 3427 601 
Q 3622 486 4057 265 
Q 4493 45 4557 45 
Q 4621 45 4627 96 
Q 5018 1459 5082 3245 
L 3309 3155 
Q 3040 1920 2342 998 
Q 1792 269 1133 -166 
Q 858 -352 672 -435 
Q 486 -518 428 -518 
Q 371 -518 371 -457 
Q 371 -397 480 -320 
Q 1664 499 2253 1626 
Q 2630 2336 2822 3130 
L 1517 3059 
L 1318 3053 
L 1261 3053 
Q 1146 3078 1075 3187 
Q 960 3379 960 3433 
Q 960 3488 1011 3488 
Q 1043 3488 1120 3469 
Q 1197 3450 1325 3450 
L 1382 3450 
L 2912 3514 
Q 3021 4128 3021 4768 
Q 3021 4947 2995 5008 
Q 2970 5069 2970 5107 
Q 2970 5171 3043 5171 
Q 3117 5171 3226 5133 
Q 3507 5043 3507 4941 
Q 3494 4198 3379 3539 
L 5171 3635 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-91cf" d="M 2080 3302 
Q 2080 3187 1952 3187 
Q 1888 3187 1776 3235 
Q 1664 3283 1664 3398 
L 1664 3437 
Q 1670 3469 1670 3507 
L 1670 3565 
Q 1670 3654 1658 3770 
L 1574 4550 
Q 1555 4698 1497 4797 
Q 1440 4896 1440 4934 
Q 1440 4973 1523 4973 
Q 1606 4973 1933 4851 
L 4563 4998 
L 4614 4998 
Q 4762 4998 4835 4934 
Q 4909 4870 4909 4828 
Q 4909 4787 4896 4761 
Q 4883 4736 4877 4717 
L 4730 3763 
Q 4870 3603 4870 3545 
Q 4870 3488 4822 3481 
Q 4774 3475 4704 3469 
L 2080 3347 
L 2080 3302 
z
M 4461 4698 
L 1952 4550 
L 1984 4250 
L 4422 4378 
L 4461 4698 
z
M 4390 4102 
L 2010 3974 
L 2048 3635 
L 4346 3750 
L 4390 4102 
z
M 1024 2682 
L 883 2675 
Q 806 2675 704 2720 
Q 602 2765 538 2982 
Q 531 2995 531 3017 
Q 531 3040 556 3040 
Q 582 3040 652 3024 
Q 723 3008 851 3008 
L 934 3008 
L 5318 3219 
Q 5472 3226 5549 3248 
Q 5626 3270 5645 3270 
Q 5664 3270 5731 3241 
Q 5798 3213 5868 3155 
Q 5939 3098 5939 3021 
Q 5939 2925 5830 2912 
L 1024 2682 
z
M 595 -51 
Q 774 -96 992 -96 
L 2982 -51 
L 2982 326 
L 1914 294 
L 1779 288 
Q 1638 288 1564 336 
Q 1491 384 1421 582 
L 1414 621 
Q 1414 640 1430 640 
Q 1446 640 1510 621 
Q 1574 602 1747 602 
L 1837 602 
L 2989 634 
L 2989 979 
L 1978 941 
L 1984 896 
L 1984 877 
Q 1984 781 1865 781 
Q 1747 781 1667 832 
Q 1587 883 1587 973 
L 1587 1005 
L 1594 1120 
Q 1594 1203 1581 1306 
L 1510 2170 
Q 1498 2323 1427 2451 
Q 1414 2490 1414 2502 
Q 1414 2547 1497 2547 
Q 1581 2547 1882 2445 
L 4666 2586 
L 4736 2586 
Q 4806 2586 4905 2538 
Q 5005 2490 5005 2387 
Q 5005 2349 4989 2317 
Q 4973 2285 4966 2259 
L 4819 1325 
Q 4954 1171 4954 1113 
Q 4954 1056 4806 1043 
L 3360 992 
L 3360 640 
L 4429 672 
Q 4582 685 4681 707 
Q 4781 730 4806 730 
Q 4832 730 4902 698 
Q 5088 608 5088 505 
Q 5088 403 4973 390 
L 3354 339 
L 3354 -45 
L 5293 0 
Q 5459 0 5548 29 
Q 5638 58 5673 58 
Q 5709 58 5786 13 
Q 5984 -96 5984 -211 
Q 5984 -326 5850 -326 
L 1011 -422 
L 947 -422 
Q 826 -422 730 -374 
Q 634 -326 576 -109 
Q 570 -96 570 -73 
Q 570 -51 595 -51 
z
M 4570 2278 
L 3366 2221 
L 3366 1907 
L 4531 1965 
L 4570 2278 
z
M 2995 2208 
L 1882 2157 
L 1907 1843 
L 2995 1894 
L 2995 2208 
z
M 4499 1683 
L 3360 1632 
L 3360 1274 
L 4461 1312 
L 4499 1683 
z
M 2989 1613 
L 1926 1568 
L 1958 1222 
L 2989 1261 
L 2989 1613 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-529b"/>
       <use xlink:href="#LXGWWenKai-Regular-91cf" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6027" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="text_5">
     <!-- 心理韧性维度 -->
     <g transform="translate(261.355 424.82) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5fc3" d="M 3450 3110 
Q 2893 3680 2285 4166 
Q 2227 4211 2227 4262 
Q 2227 4339 2313 4416 
Q 2400 4493 2461 4493 
Q 2522 4493 2560 4461 
Q 3206 3974 3744 3462 
Q 3814 3405 3814 3309 
Q 3814 3213 3728 3129 
Q 3642 3046 3578 3046 
Q 3514 3046 3450 3110 
z
M 1907 2995 
Q 1907 3104 1948 3149 
Q 1990 3194 2112 3200 
L 2138 3200 
Q 2246 3200 2294 3155 
Q 2342 3110 2349 2982 
Q 2362 2394 2458 1926 
Q 2630 1050 3277 672 
Q 3840 339 4512 339 
Q 4717 339 4717 390 
L 4467 998 
Q 4192 1677 4192 1901 
Q 4192 1997 4243 1997 
Q 4333 1997 4480 1709 
Q 4774 1133 5197 506 
Q 5325 320 5325 182 
Q 5325 45 5101 -54 
Q 4877 -154 4570 -154 
Q 4486 -154 4410 -141 
Q 3187 6 2611 640 
Q 2112 1178 1984 2099 
Q 1920 2515 1907 2963 
L 1907 2995 
z
M 6086 1677 
Q 6144 1600 6144 1533 
Q 6144 1466 6045 1366 
Q 5946 1267 5869 1267 
Q 5792 1267 5722 1363 
Q 5190 2144 4627 2778 
Q 4582 2822 4582 2886 
Q 4582 2950 4672 3027 
Q 4762 3104 4829 3104 
Q 4896 3104 4973 3027 
Q 5549 2406 6086 1677 
z
M 1075 2957 
Q 1344 2957 1344 2790 
Q 1344 2701 1190 2121 
Q 1037 1542 742 909 
Q 685 787 595 787 
L 525 806 
Q 301 864 301 1011 
Q 301 1056 410 1286 
Q 717 1914 922 2822 
Q 947 2957 1075 2957 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7406" d="M 2918 2214 
L 2918 2438 
Q 2918 2490 2912 2554 
L 2790 4186 
Q 2784 4365 2726 4502 
Q 2669 4640 2669 4691 
Q 2669 4742 2752 4742 
Q 2835 4742 3136 4614 
L 5306 4762 
L 5344 4762 
Q 5466 4762 5536 4691 
Q 5651 4608 5651 4531 
Q 5651 4454 5632 4409 
Q 5613 4365 5606 4326 
L 5427 2515 
Q 5568 2349 5568 2281 
Q 5568 2214 5529 2198 
Q 5491 2182 5434 2176 
L 4333 2131 
L 4333 1331 
L 4992 1357 
Q 5082 1376 5139 1385 
Q 5197 1395 5251 1414 
Q 5306 1434 5334 1434 
Q 5363 1434 5434 1389 
Q 5626 1267 5626 1161 
Q 5626 1056 5530 1043 
L 4333 992 
L 4333 173 
L 5594 211 
Q 5683 218 5747 218 
L 5939 256 
Q 5997 256 6131 160 
Q 6246 45 6246 -48 
Q 6246 -141 6118 -141 
L 2547 -250 
Q 2362 -250 2291 -179 
Q 2157 -19 2138 134 
Q 2138 173 2163 173 
L 2189 160 
Q 2272 147 2349 128 
Q 2426 109 2509 109 
L 2534 109 
L 3968 160 
L 3968 973 
L 3270 934 
L 3117 928 
Q 3066 928 2966 969 
Q 2867 1011 2797 1248 
L 2797 1274 
Q 2797 1312 2829 1312 
L 2842 1312 
Q 2970 1274 3046 1274 
L 3149 1274 
Q 3168 1274 3194 1280 
L 3962 1312 
L 3962 2112 
L 3315 2086 
L 3328 1920 
L 3328 1907 
Q 3328 1792 3219 1792 
Q 3206 1792 3126 1824 
Q 3046 1856 2976 1917 
Q 2906 1978 2912 2064 
Q 2918 2150 2918 2214 
z
M 685 4397 
L 1926 4480 
Q 1984 4493 2045 4502 
Q 2106 4512 2157 4531 
Q 2208 4550 2249 4550 
Q 2291 4550 2368 4502 
Q 2445 4454 2502 4387 
Q 2560 4320 2560 4275 
Q 2560 4186 2406 4173 
L 1709 4115 
L 1696 2957 
L 1920 2976 
Q 2074 2989 2138 3014 
Q 2202 3040 2230 3040 
Q 2259 3040 2339 2989 
Q 2419 2938 2483 2867 
Q 2547 2797 2547 2752 
Q 2547 2662 2394 2650 
L 1696 2598 
L 1683 1299 
Q 1869 1382 2077 1484 
Q 2285 1587 2429 1657 
Q 2573 1728 2637 1728 
Q 2701 1728 2701 1677 
Q 2701 1606 2541 1491 
Q 1658 890 960 563 
Q 666 422 586 422 
Q 506 422 422 489 
Q 339 557 278 640 
Q 218 723 218 771 
Q 218 819 384 835 
Q 550 851 1312 1139 
L 1318 2573 
L 1024 2554 
Q 934 2541 873 2541 
Q 813 2541 794 2547 
Q 730 2547 656 2627 
Q 582 2707 550 2784 
Q 518 2861 518 2886 
Q 518 2931 557 2931 
Q 570 2931 637 2915 
Q 704 2899 819 2899 
L 896 2899 
L 1325 2925 
L 1331 4090 
L 928 4058 
Q 826 4045 733 4045 
Q 640 4045 608 4077 
Q 442 4192 410 4365 
Q 403 4378 403 4384 
Q 403 4422 448 4422 
L 685 4397 
z
M 5203 4416 
L 4333 4358 
L 4333 3616 
L 5152 3654 
L 5203 4416 
z
M 3955 4339 
L 3162 4288 
L 3213 3546 
L 3955 3584 
L 3955 4339 
z
M 5126 3322 
L 4333 3277 
L 4333 2464 
L 5069 2502 
L 5126 3322 
z
M 3962 3251 
L 3238 3213 
L 3296 2413 
L 3962 2445 
L 3962 3251 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-7ef4" d="M 1843 1830 
Q 2170 1882 2333 1910 
Q 2496 1939 2585 1932 
Q 2675 1926 2675 1875 
Q 2662 1786 2236 1622 
Q 1811 1459 1395 1350 
Q 979 1242 912 1248 
Q 845 1254 806 1293 
Q 691 1440 659 1545 
Q 627 1651 627 1664 
Q 634 1722 691 1715 
L 787 1709 
Q 954 1690 1114 1709 
Q 1363 2112 1581 2509 
L 1594 2528 
Q 1094 2874 608 3085 
Q 499 3136 486 3187 
Q 474 3238 518 3337 
Q 563 3437 624 3449 
Q 685 3462 806 3418 
Q 832 3405 851 3398 
Q 1395 4186 1510 4480 
Q 1626 4774 1626 4915 
L 1626 4979 
Q 1626 5050 1677 5050 
Q 1709 5050 1798 4998 
Q 2042 4851 2042 4717 
Q 2003 4557 1792 4186 
Q 1600 3840 1171 3226 
L 1325 3142 
Q 1600 2989 1779 2867 
Q 2221 3654 2253 3859 
Q 2266 3917 2269 4013 
Q 2272 4109 2307 4109 
Q 2342 4109 2426 4064 
Q 2669 3930 2682 3789 
Q 2682 3744 2662 3699 
Q 2138 2682 1568 1786 
Q 1696 1805 1843 1830 
z
M 1498 563 
Q 2016 781 2320 925 
Q 2624 1069 2681 1075 
Q 2739 1082 2739 1062 
Q 2752 922 1853 378 
Q 954 -166 794 -179 
Q 755 -186 672 -109 
Q 589 -32 531 64 
Q 474 160 467 211 
Q 467 237 525 243 
L 589 250 
Q 685 256 864 314 
L 1498 563 
z
M 4710 4813 
Q 4710 4877 4771 4877 
Q 4832 4877 4928 4826 
Q 5178 4691 5178 4589 
Q 5178 4570 5082 4342 
Q 4986 4115 4717 3744 
L 5299 3782 
Q 5434 3789 5510 3814 
Q 5587 3840 5632 3840 
Q 5677 3840 5747 3789 
Q 5933 3661 5933 3565 
Q 5933 3482 5779 3469 
L 4730 3398 
L 4723 2656 
L 5094 2682 
Q 5261 2694 5328 2716 
Q 5395 2739 5449 2739 
Q 5504 2739 5622 2643 
Q 5741 2547 5741 2480 
Q 5741 2413 5696 2393 
Q 5651 2374 5587 2368 
L 4717 2317 
L 4717 1613 
L 5139 1638 
Q 5299 1651 5360 1680 
Q 5421 1709 5462 1709 
Q 5504 1709 5581 1658 
Q 5786 1510 5786 1408 
Q 5786 1331 5638 1318 
L 4710 1267 
L 4704 390 
L 5517 422 
Q 5702 435 5776 464 
Q 5850 493 5907 493 
Q 5965 493 6042 429 
Q 6227 294 6227 186 
Q 6227 109 6074 90 
L 3482 -6 
L 3482 -486 
Q 3482 -640 3373 -640 
Q 3213 -640 3120 -557 
Q 3027 -474 3027 -413 
Q 3027 -352 3056 -214 
Q 3085 -77 3085 102 
L 3104 3059 
Q 2925 2822 2777 2678 
Q 2630 2534 2592 2534 
Q 2554 2534 2554 2588 
Q 2554 2643 2611 2746 
Q 3341 3904 3558 4646 
Q 3635 4922 3635 4960 
L 3635 5018 
L 3622 5120 
Q 3622 5190 3696 5190 
Q 3770 5190 3898 5126 
Q 4109 5024 4109 4928 
Q 4109 4870 3904 4406 
Q 3699 3942 3526 3661 
L 4371 3718 
Q 4570 4077 4653 4313 
Q 4736 4550 4736 4611 
Q 4736 4672 4710 4813 
z
M 4352 3379 
L 3462 3322 
L 3469 2579 
L 4339 2637 
L 4352 3379 
z
M 4339 2298 
L 3469 2246 
L 3475 1549 
L 4326 1594 
L 4339 2298 
z
M 4326 1248 
L 3475 1203 
L 3475 346 
L 4314 378 
L 4326 1248 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-5ea6" d="M 1075 4448 
Q 1075 4493 1152 4493 
Q 1229 4493 1606 4275 
L 3174 4365 
L 3168 4934 
Q 3168 5050 3104 5136 
Q 3040 5222 3040 5254 
Q 3040 5286 3094 5286 
Q 3149 5286 3264 5267 
Q 3584 5210 3584 5062 
L 3578 4390 
L 5037 4474 
Q 5235 4486 5318 4515 
Q 5402 4544 5453 4544 
Q 5504 4544 5581 4480 
Q 5658 4416 5706 4342 
Q 5754 4269 5754 4237 
Q 5754 4160 5594 4147 
L 1600 3910 
Q 1587 2618 1468 1955 
Q 1350 1293 1161 797 
Q 973 301 781 3 
Q 589 -294 445 -428 
Q 301 -563 265 -563 
Q 230 -563 230 -512 
Q 230 -461 294 -352 
Q 1056 1018 1152 2880 
Q 1171 3328 1171 3648 
L 1171 3827 
Q 1171 4090 1129 4237 
Q 1088 4384 1081 4406 
Q 1075 4429 1075 4448 
z
M 2630 2298 
L 2630 2406 
Q 2630 2464 2624 2509 
L 2586 2950 
L 2189 2925 
L 2061 2918 
Q 1978 2918 1859 2960 
Q 1741 3002 1664 3213 
Q 1658 3232 1658 3264 
Q 1658 3296 1690 3296 
Q 1722 3296 1779 3280 
Q 1837 3264 1965 3264 
L 2560 3296 
L 2541 3514 
Q 2528 3680 2406 3821 
Q 2394 3834 2394 3853 
Q 2394 3898 2458 3898 
Q 2906 3898 2918 3693 
L 2944 3322 
L 4090 3379 
L 4102 3578 
L 4109 3642 
Q 4109 3795 4054 3872 
Q 4000 3949 4000 3981 
Q 4000 4013 4054 4013 
Q 4109 4013 4218 3994 
Q 4518 3930 4518 3750 
L 4518 3738 
L 4480 3405 
L 4883 3424 
Q 5075 3443 5152 3475 
Q 5229 3507 5283 3507 
Q 5338 3507 5421 3446 
Q 5504 3386 5561 3312 
Q 5619 3238 5619 3200 
Q 5619 3123 5504 3110 
L 5491 3110 
L 4442 3053 
L 4384 2579 
Q 4525 2400 4525 2320 
Q 4525 2240 4339 2227 
L 3027 2150 
L 3027 2080 
Q 3027 1939 2938 1939 
Q 2931 1939 2851 1961 
Q 2771 1984 2694 2032 
Q 2618 2080 2624 2166 
Q 2630 2253 2630 2298 
z
M 4058 3027 
L 2970 2970 
L 3002 2490 
L 4019 2547 
L 4058 3027 
z
M 2381 1747 
L 4538 1869 
L 4614 1869 
Q 4755 1869 4832 1811 
Q 4954 1696 4954 1632 
Q 4954 1568 4912 1529 
Q 4870 1491 4845 1459 
Q 4416 896 3923 499 
Q 4832 -19 5926 -256 
Q 6048 -282 6048 -339 
Q 6048 -371 5939 -505 
Q 5830 -640 5734 -640 
Q 5715 -640 5526 -589 
Q 5338 -538 5037 -422 
Q 4243 -134 3590 250 
Q 2650 -365 1613 -525 
Q 1427 -550 1421 -550 
Q 1280 -550 1280 -476 
Q 1280 -403 1485 -339 
Q 2496 -19 3245 467 
Q 2963 659 2710 848 
Q 2458 1037 2432 1065 
Q 2406 1094 2406 1136 
Q 2406 1178 2464 1251 
Q 2522 1325 2589 1325 
Q 2656 1325 2797 1229 
Q 3251 909 3578 704 
Q 4038 1050 4403 1510 
L 2579 1395 
L 2413 1389 
Q 2349 1389 2224 1424 
Q 2099 1459 1997 1677 
Q 1984 1696 1984 1731 
Q 1984 1766 2035 1766 
L 2067 1766 
Q 2214 1747 2381 1747 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-5fc3"/>
      <use xlink:href="#LXGWWenKai-Regular-7406" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-97e7" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6027" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-7ef4" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-5ea6" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_9">
      <path d="M 48.17 370.820442 
L 534.54 370.820442 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <defs>
       <path id="me6e37b1163" d="M 0 0 
L -3.5 0 
" style="stroke: #000000; stroke-width: 0.8"/>
      </defs>
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="370.820442" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2.50 -->
      <g transform="translate(23.97 373.648567) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-32" d="M 2355 45 
L 1568 45 
Q 1050 45 659 -26 
L 627 -26 
Q 518 -26 441 76 
Q 365 179 365 256 
Q 365 333 397 384 
Q 429 435 467 476 
Q 506 518 531 563 
Q 717 883 1113 1328 
Q 1510 1773 1980 2160 
Q 2451 2547 2665 2867 
Q 2880 3187 2880 3488 
Q 2880 3789 2688 3971 
Q 2496 4154 2102 4154 
Q 1709 4154 1456 3981 
Q 1203 3808 1094 3526 
Q 1069 3462 1008 3411 
Q 947 3360 864 3360 
Q 781 3360 704 3472 
Q 627 3584 627 3651 
Q 627 3718 716 3865 
Q 806 4013 986 4173 
Q 1434 4563 2061 4563 
Q 2688 4563 3021 4268 
Q 3354 3974 3354 3532 
Q 3354 3091 3075 2694 
Q 2797 2298 2317 1901 
Q 1370 1133 928 410 
Q 1248 442 1882 442 
L 2816 435 
L 3232 442 
Q 3315 442 3382 326 
Q 3450 211 3450 102 
Q 3450 -6 3354 -6 
Q 3290 -6 3050 19 
Q 2810 45 2355 45 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-2e" d="M 1139 704 
Q 1280 704 1401 566 
Q 1523 429 1523 275 
Q 1523 122 1404 16 
Q 1286 -90 1148 -90 
Q 1011 -90 899 51 
Q 787 192 787 345 
Q 787 499 892 601 
Q 998 704 1139 704 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-35" d="M 1056 2515 
Q 1453 2829 2003 2829 
L 2061 2829 
Q 2419 2822 2732 2656 
Q 3046 2490 3241 2182 
Q 3437 1875 3437 1449 
Q 3437 1024 3264 669 
Q 3091 314 2726 96 
Q 2362 -122 1862 -122 
Q 1363 -122 1030 48 
Q 698 218 461 474 
Q 416 518 416 624 
Q 416 730 483 848 
Q 550 966 611 966 
Q 672 966 698 922 
Q 864 678 1136 486 
Q 1408 294 1779 288 
L 1792 288 
Q 2202 288 2467 445 
Q 2733 602 2864 864 
Q 2995 1126 2995 1427 
Q 2995 1914 2707 2176 
Q 2419 2438 2003 2445 
L 1978 2445 
Q 1574 2445 1337 2301 
Q 1101 2157 883 1958 
Q 864 1939 806 1939 
Q 749 1939 665 2028 
Q 582 2118 582 2204 
Q 582 2291 653 2534 
L 896 4070 
Q 909 4141 909 4211 
L 909 4346 
Q 909 4429 1021 4429 
Q 1133 4429 1274 4358 
L 2605 4454 
Q 2918 4474 3034 4493 
L 3046 4493 
Q 3117 4493 3177 4381 
Q 3238 4269 3238 4173 
Q 3238 4077 3162 4077 
Q 3078 4070 2937 4067 
Q 2797 4064 2656 4051 
L 1299 3949 
L 1056 2515 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-30" d="M 2490 563 
Q 2918 1082 2918 2339 
Q 2918 3597 2432 4006 
Q 2246 4160 2035 4160 
Q 1626 4160 1277 3619 
Q 928 3078 928 2198 
Q 928 1318 1107 858 
Q 1210 595 1402 441 
Q 1594 288 1930 288 
Q 2266 288 2490 563 
z
M 1242 4141 
Q 1606 4563 2035 4563 
Q 2464 4563 2771 4262 
Q 3379 3667 3379 2266 
Q 3379 1274 3021 602 
Q 2624 -115 1894 -115 
Q 1165 -115 794 557 
Q 467 1152 467 2054 
Q 467 2957 717 3622 
Q 819 3904 928 4061 
Q 1037 4218 1107 4218 
Q 1178 4218 1242 4141 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <path d="M 48.17 322.691819 
L 534.54 322.691819 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="322.691819" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 2.75 -->
      <g transform="translate(23.97 325.519944) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-37" d="M 3392 4205 
Q 3366 4115 3296 4003 
Q 3226 3891 3072 3584 
Q 1997 1402 1626 32 
Q 1606 -70 1414 -70 
Q 1222 -70 1180 0 
Q 1139 70 1139 144 
Q 1139 218 1628 1424 
Q 2118 2630 2829 4064 
L 1005 3904 
Q 960 3898 912 3888 
Q 864 3878 777 3878 
Q 691 3878 620 3964 
Q 550 4051 521 4147 
Q 493 4243 493 4256 
Q 493 4314 531 4314 
L 762 4301 
L 2816 4448 
Q 2893 4454 2960 4473 
Q 3027 4493 3085 4493 
L 3104 4493 
Q 3187 4486 3289 4390 
Q 3392 4294 3392 4205 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-32"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-37" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_13">
      <path d="M 48.17 274.563197 
L 534.54 274.563197 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="274.563197" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 3.00 -->
      <g transform="translate(23.97 277.391322) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-33" d="M 2016 2253 
Q 1779 2253 1491 2195 
Q 1402 2195 1338 2297 
Q 1274 2400 1274 2493 
Q 1274 2586 1350 2592 
Q 2202 2675 2592 3078 
Q 2784 3277 2784 3526 
Q 2784 4166 2035 4166 
Q 1440 4166 960 3648 
Q 902 3565 826 3565 
Q 813 3565 710 3632 
Q 608 3699 608 3814 
Q 608 3930 678 3987 
Q 1229 4563 2022 4563 
Q 2566 4563 2899 4300 
Q 3232 4038 3232 3609 
Q 3232 3181 3008 2905 
Q 2784 2630 2387 2509 
Q 2682 2509 2918 2371 
Q 3155 2234 3296 1984 
Q 3437 1734 3437 1363 
Q 3437 992 3257 646 
Q 3078 301 2704 93 
Q 2330 -115 1824 -115 
Q 1318 -115 1004 16 
Q 691 147 429 403 
Q 378 454 378 553 
Q 378 653 445 765 
Q 512 877 566 877 
Q 621 877 659 838 
Q 864 582 1117 435 
Q 1370 288 1776 288 
Q 2182 288 2457 441 
Q 2733 595 2857 848 
Q 2982 1101 2982 1389 
Q 2982 1779 2710 2016 
Q 2438 2253 2016 2253 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_15">
      <path d="M 48.17 226.434574 
L 534.54 226.434574 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="226.434574" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 3.25 -->
      <g transform="translate(23.97 229.262699) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_17">
      <path d="M 48.17 178.305952 
L 534.54 178.305952 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="178.305952" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_10">
      <!-- 3.50 -->
      <g transform="translate(23.97 181.134077) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_19">
      <path d="M 48.17 130.177329 
L 534.54 130.177329 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="130.177329" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_11">
      <!-- 3.75 -->
      <g transform="translate(23.97 133.005454) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-37" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_21">
      <path d="M 48.17 82.048707 
L 534.54 82.048707 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="82.048707" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_12">
      <!-- 4.00 -->
      <g transform="translate(23.97 84.876832) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-34" d="M 3578 1018 
L 2982 1030 
L 2861 1024 
L 2861 659 
L 2886 -6 
Q 2886 -70 2768 -70 
Q 2650 -70 2528 -22 
Q 2406 26 2406 109 
L 2419 672 
L 2419 1005 
L 902 928 
Q 806 928 729 905 
Q 653 883 585 883 
Q 518 883 422 976 
Q 326 1069 326 1161 
Q 326 1254 377 1328 
Q 429 1402 489 1475 
Q 550 1549 595 1613 
Q 1792 3501 1984 3859 
Q 2176 4218 2298 4506 
Q 2317 4550 2368 4550 
Q 2419 4550 2496 4493 
Q 2688 4352 2688 4205 
Q 2688 4179 2669 4147 
L 2438 3789 
Q 1376 2061 864 1318 
L 2419 1389 
L 2419 2675 
L 2400 3360 
Q 2400 3424 2518 3424 
Q 2637 3424 2755 3376 
Q 2874 3328 2874 3245 
L 2861 2675 
L 2861 1408 
L 2976 1414 
Q 3104 1421 3241 1437 
Q 3379 1453 3452 1453 
Q 3526 1453 3587 1334 
Q 3648 1216 3648 1117 
Q 3648 1018 3578 1018 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_23">
      <path d="M 48.17 33.920085 
L 534.54 33.920085 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="33.920085" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_13">
      <!-- 4.25 -->
      <g transform="translate(23.97 36.74821) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="94.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="text_14">
     <!-- 分数 -->
     <g transform="translate(18.249687 221.985937) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-5206" d="M 3514 5056 
Q 3674 5050 3728 5018 
Q 3782 4986 3821 4902 
Q 4243 4026 5171 3149 
Q 5632 2707 6163 2355 
Q 6240 2310 6240 2268 
Q 6240 2227 6170 2163 
Q 5978 2010 5904 2010 
Q 5830 2010 5786 2042 
Q 4294 3187 3482 4704 
Q 3424 4813 3331 4864 
Q 3238 4915 3238 4953 
Q 3238 4992 3302 5024 
Q 3366 5056 3475 5056 
L 3514 5056 
z
M 333 1792 
Q 288 1766 237 1766 
Q 186 1766 186 1824 
Q 186 1882 346 2016 
Q 1178 2694 1734 3571 
Q 1971 3949 2092 4237 
Q 2214 4525 2214 4653 
Q 2214 4781 2291 4781 
Q 2368 4781 2537 4665 
Q 2707 4550 2707 4480 
Q 2707 4448 2682 4397 
Q 2227 3392 1555 2726 
Q 883 2061 333 1792 
z
M 1734 2406 
L 4403 2579 
Q 4435 2586 4467 2586 
L 4512 2586 
Q 4627 2586 4707 2518 
Q 4787 2451 4787 2384 
Q 4787 2317 4768 2278 
Q 4749 2240 4742 2208 
Q 4678 1139 4454 218 
Q 4275 -518 3936 -518 
Q 3706 -518 3072 38 
Q 2726 339 2726 435 
Q 2726 486 2803 486 
Q 2880 486 3123 339 
Q 3366 192 3834 19 
Q 3846 13 3865 13 
Q 3885 13 3910 58 
Q 4186 666 4314 2202 
L 3066 2118 
Q 2662 832 1741 128 
Q 1312 -205 832 -429 
Q 736 -474 669 -474 
Q 602 -474 602 -429 
Q 602 -352 749 -262 
Q 1363 134 1862 720 
Q 2362 1306 2618 2086 
L 1990 2042 
Q 1862 2029 1773 2029 
Q 1613 2029 1510 2227 
L 1453 2342 
Q 1440 2381 1440 2409 
Q 1440 2438 1475 2438 
Q 1510 2438 1564 2422 
Q 1619 2406 1734 2406 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6570" d="M 5510 3302 
Q 5267 2163 4851 1350 
Q 5357 512 6214 -205 
Q 6285 -262 6285 -300 
Q 6285 -339 6160 -425 
Q 6035 -512 5961 -512 
Q 5888 -512 5843 -467 
Q 5139 237 4659 998 
Q 4237 326 3674 -154 
Q 3443 -352 3286 -451 
Q 3130 -550 3085 -550 
Q 3040 -550 3040 -499 
Q 3040 -448 3130 -358 
Q 3552 58 3865 461 
Q 4179 864 4448 1370 
Q 4179 1888 3917 2688 
Q 3718 2304 3552 2089 
Q 3386 1875 3328 1875 
Q 3290 1875 3290 1939 
Q 3290 2003 3334 2099 
Q 3898 3341 4096 4403 
Q 4160 4768 4160 4829 
Q 4160 4890 4141 4973 
L 4141 4986 
Q 4141 5043 4205 5043 
Q 4243 5043 4339 5005 
Q 4621 4883 4621 4742 
Q 4621 4678 4496 4246 
Q 4371 3814 4275 3546 
L 5338 3616 
Q 5472 3629 5571 3661 
Q 5670 3693 5728 3693 
Q 5786 3693 5862 3629 
Q 6048 3482 6048 3398 
Q 6048 3341 5920 3328 
L 5510 3302 
z
M 1798 2432 
Q 1830 2624 1830 2790 
L 1830 2925 
Q 1830 2970 1843 3059 
L 1862 3142 
Q 1811 3053 1754 2976 
Q 1280 2490 762 2214 
Q 557 2106 502 2106 
Q 448 2106 448 2144 
Q 448 2202 550 2278 
Q 1114 2707 1645 3309 
L 992 3264 
L 915 3264 
Q 851 3264 758 3302 
Q 666 3341 614 3539 
Q 608 3552 608 3574 
Q 608 3597 633 3597 
Q 659 3597 713 3581 
Q 768 3565 870 3565 
L 1830 3622 
L 1830 4621 
Q 1830 4781 1795 4851 
Q 1760 4922 1760 4957 
Q 1760 4992 1811 4992 
Q 1862 4992 1952 4973 
Q 2202 4922 2202 4794 
L 2195 3642 
L 2931 3693 
Q 3072 3699 3136 3721 
Q 3200 3744 3257 3744 
Q 3315 3744 3408 3651 
Q 3501 3558 3501 3494 
Q 3501 3430 3366 3418 
L 2189 3341 
L 2189 3162 
Q 2234 3245 2307 3245 
Q 2381 3245 2736 3037 
Q 3091 2829 3187 2758 
Q 3283 2688 3283 2624 
Q 3283 2560 3219 2477 
Q 3155 2394 3113 2394 
Q 3072 2394 3002 2445 
Q 2662 2733 2227 2982 
Q 2221 2989 2211 3001 
Q 2202 3014 2189 3021 
L 2182 2368 
Q 2182 2208 2083 2208 
Q 1984 2208 1891 2281 
Q 1798 2355 1798 2432 
z
M 2822 4666 
Q 2835 4749 2880 4749 
Q 2899 4749 2970 4698 
Q 3187 4563 3187 4473 
Q 3187 4384 2931 4160 
Q 2458 3744 2323 3744 
Q 2291 3744 2291 3782 
Q 2291 3821 2467 4022 
Q 2643 4224 2784 4486 
Q 2822 4538 2822 4666 
z
M 1325 3821 
Q 1114 4109 909 4314 
Q 864 4371 864 4406 
Q 864 4442 918 4502 
Q 973 4563 1021 4563 
Q 1069 4563 1117 4518 
Q 1165 4474 1257 4384 
Q 1350 4294 1440 4201 
Q 1530 4109 1594 4029 
Q 1658 3949 1658 3910 
Q 1658 3872 1578 3798 
Q 1498 3725 1446 3725 
Q 1395 3725 1325 3821 
z
M 4160 3232 
L 4134 3162 
Q 4346 2342 4634 1754 
Q 4915 2432 5062 3283 
L 4160 3232 
z
M 2554 1920 
L 2547 2016 
Q 2547 2080 2604 2080 
Q 2662 2080 2739 2035 
Q 2950 1920 2950 1760 
L 2950 1747 
L 3341 1786 
L 3411 1792 
Q 3558 1792 3558 1728 
Q 3558 1645 3360 1606 
Q 3162 1568 2874 1523 
Q 2669 915 2336 499 
Q 2726 275 2905 156 
Q 3085 38 3085 -32 
Q 3085 -102 3033 -208 
Q 2982 -314 2918 -314 
Q 2854 -314 2640 -134 
Q 2426 45 2080 243 
Q 1466 -326 602 -474 
Q 454 -499 448 -499 
Q 352 -499 352 -454 
Q 352 -378 621 -282 
Q 1280 -45 1786 422 
Q 1382 646 1229 685 
Q 1120 710 1120 858 
Q 1120 902 1331 1280 
L 877 1235 
Q 774 1222 710 1222 
Q 461 1222 358 1581 
L 358 1600 
Q 358 1638 390 1638 
Q 422 1638 499 1619 
Q 576 1600 704 1600 
L 774 1600 
Q 947 1606 1129 1616 
Q 1312 1626 1504 1632 
Q 1670 2003 1670 2131 
L 1670 2195 
Q 1670 2272 1705 2272 
Q 1741 2272 1818 2227 
Q 2042 2093 2042 1978 
Q 2042 1920 1920 1658 
Q 2080 1670 2217 1686 
Q 2355 1702 2522 1715 
L 2541 1792 
Q 2554 1856 2554 1920 
z
M 1517 928 
Q 1683 858 2029 666 
Q 2272 973 2445 1453 
L 1754 1338 
Q 1690 1222 1635 1123 
Q 1581 1024 1517 928 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-5206"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="99.999985"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1">
    <path d="M 91.190442 335.928657 
L 91.190442 226.786994 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 210.691671 362.272628 
L 210.691671 258.19737 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 330.192899 346.902679 
L 330.192899 230.534669 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 449.694128 290.180634 
L 449.694128 170.389094 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_25">
    <defs>
     <path id="m6e78f72c53" d="M 5 0 
L -5 -0 
" style="stroke: #000000"/>
    </defs>
    <g clip-path="url(#p6b8b5df46e)">
     <use xlink:href="#m6e78f72c53" x="91.190442" y="335.928657" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="210.691671" y="362.272628" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="330.192899" y="346.902679" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="449.694128" y="290.180634" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_26">
    <g clip-path="url(#p6b8b5df46e)">
     <use xlink:href="#m6e78f72c53" x="91.190442" y="226.786994" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="210.691671" y="258.19737" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="330.192899" y="230.534669" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="449.694128" y="170.389094" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_2">
    <path d="M 133.015872 165.453786 
L 133.015872 125.959877 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 252.517101 174.679715 
L 252.517101 125.461271 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 372.018329 211.712438 
L 372.018329 176.985214 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 491.519558 125.268735 
L 491.519558 89.139132 
" clip-path="url(#p6b8b5df46e)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_27">
    <g clip-path="url(#p6b8b5df46e)">
     <use xlink:href="#m6e78f72c53" x="133.015872" y="165.453786" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="252.517101" y="174.679715" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="372.018329" y="211.712438" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="491.519558" y="125.268735" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_28">
    <g clip-path="url(#p6b8b5df46e)">
     <use xlink:href="#m6e78f72c53" x="133.015872" y="125.959877" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="252.517101" y="125.461271" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="372.018329" y="176.985214" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="491.519558" y="89.139132" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_11">
    <path d="M 48.17 398.884375 
L 48.17 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_12">
    <path d="M 534.54 398.884375 
L 534.54 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 48.17 398.884375 
L 534.54 398.884375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 48.17 25.0875 
L 534.54 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_15">
    <!-- p=0.034 -->
    <g transform="translate(96.415032 86.080729) scale(0.08 -0.08)">
     <defs>
      <path id="LXGWWenKai-Regular-70" d="M 1005 646 
Q 1030 659 1050 659 
Q 1120 659 1216 518 
Q 1389 262 1766 262 
Q 2144 262 2512 672 
Q 2880 1082 2880 1738 
Q 2880 2394 2534 2624 
Q 2387 2726 2169 2726 
Q 1952 2726 1712 2585 
Q 1472 2445 1267 2147 
Q 1062 1850 1024 1338 
Q 1005 1069 1005 646 
z
M 576 -922 
L 589 -352 
L 589 2355 
Q 589 2694 570 3021 
Q 570 3085 685 3085 
Q 800 3085 921 3037 
Q 1043 2989 1043 2906 
Q 1043 2867 1036 2764 
Q 1030 2662 1027 2550 
Q 1024 2438 1018 2362 
Q 1274 2797 1571 2953 
Q 1869 3110 2221 3110 
Q 2701 3110 3008 2771 
Q 3315 2432 3315 1837 
Q 3315 1242 3113 813 
Q 2912 384 2566 137 
Q 2221 -109 1786 -109 
Q 1562 -109 1338 0 
Q 1114 109 1011 243 
L 1011 -358 
L 1043 -1037 
Q 1043 -1101 928 -1101 
Q 813 -1101 694 -1053 
Q 576 -1005 576 -922 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-3d" d="M 486 1197 
Q 678 1184 1011 1184 
L 2848 1184 
Q 3034 1184 3302 1203 
L 3309 1203 
Q 3379 1203 3417 1120 
Q 3456 1037 3456 918 
Q 3456 800 3360 800 
L 2842 813 
L 1005 813 
Q 634 813 518 794 
L 512 794 
Q 454 794 419 877 
Q 384 960 384 1043 
Q 384 1197 486 1197 
z
M 486 2400 
Q 678 2387 1011 2387 
L 2848 2387 
Q 3034 2387 3302 2406 
L 3309 2406 
Q 3379 2406 3417 2323 
Q 3456 2240 3456 2121 
Q 3456 2003 3360 2003 
L 2842 2016 
L 1005 2016 
Q 634 2016 518 1997 
L 512 1997 
Q 454 1997 419 2080 
Q 384 2163 384 2246 
Q 384 2400 486 2400 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-70"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="57.199997"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="117.199982"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="177.199966"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="212.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-33" x="272.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-34" x="332.199921"/>
    </g>
   </g>
   <g id="text_16">
    <!-- p=0.012 -->
    <g transform="translate(215.916261 85.582123) scale(0.08 -0.08)">
     <defs>
      <path id="LXGWWenKai-Regular-31" d="M 1773 109 
L 1786 672 
L 1786 3878 
Q 1555 3744 1178 3661 
Q 1165 3654 1101 3654 
Q 1037 3654 944 3705 
Q 851 3757 851 3808 
Q 851 3859 922 3885 
Q 1536 4128 1811 4442 
Q 1901 4544 1977 4544 
Q 2054 4544 2172 4467 
Q 2291 4390 2291 4304 
Q 2291 4218 2272 4099 
Q 2253 3981 2253 3840 
L 2253 710 
L 2266 -6 
Q 2266 -70 2144 -70 
Q 2022 -70 1897 -22 
Q 1773 26 1773 109 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-70"/>
     <use xlink:href="#LXGWWenKai-Regular-3d" x="57.199997"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="117.199982"/>
     <use xlink:href="#LXGWWenKai-Regular-2e" x="177.199966"/>
     <use xlink:href="#LXGWWenKai-Regular-30" x="212.199951"/>
     <use xlink:href="#LXGWWenKai-Regular-31" x="272.199936"/>
     <use xlink:href="#LXGWWenKai-Regular-32" x="332.199921"/>
    </g>
   </g>
   <g id="text_17">
    <!-- 高低焦虑组心理韧性比较 -->
    <g transform="translate(236.355 19.0875) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-9ad8" d="M 1139 3987 
L 973 3981 
Q 774 3981 678 4218 
L 646 4294 
Q 646 4333 691 4333 
L 723 4333 
Q 832 4314 941 4314 
L 1011 4314 
L 2912 4416 
L 2906 4838 
Q 2906 4992 2822 5120 
Q 2803 5158 2803 5184 
Q 2803 5235 2880 5235 
Q 2957 5235 3107 5184 
Q 3258 5133 3296 5094 
Q 3334 5056 3334 4986 
L 3328 4442 
L 5005 4531 
Q 5126 4544 5193 4566 
Q 5261 4589 5309 4589 
Q 5357 4589 5427 4531 
Q 5613 4390 5613 4301 
Q 5613 4243 5466 4230 
L 1139 3987 
z
M 2387 2445 
Q 2387 2323 2291 2323 
Q 2163 2323 2070 2396 
Q 1978 2470 1978 2566 
L 1978 2605 
Q 1984 2643 1984 2688 
L 1984 2861 
Q 1984 2874 1952 3181 
Q 1920 3488 1885 3555 
Q 1850 3622 1834 3651 
Q 1818 3680 1818 3699 
Q 1818 3744 1907 3744 
Q 1997 3744 2330 3661 
L 4128 3776 
L 4186 3776 
Q 4314 3776 4390 3693 
Q 4467 3610 4467 3574 
Q 4467 3539 4451 3513 
Q 4435 3488 4422 3456 
L 4288 2925 
Q 4467 2752 4467 2697 
Q 4467 2643 4412 2633 
Q 4358 2624 4288 2618 
L 2381 2534 
L 2387 2483 
L 2387 2445 
z
M 4000 3437 
L 2310 3334 
L 2355 2861 
L 3898 2938 
L 4000 3437 
z
M 915 -346 
Q 986 77 986 1709 
Q 986 1875 941 1993 
Q 896 2112 896 2131 
Q 896 2189 985 2189 
Q 1075 2189 1382 2067 
L 5178 2240 
L 5242 2240 
Q 5389 2240 5440 2153 
Q 5491 2067 5491 2035 
L 5466 1882 
L 5446 -90 
L 5459 -301 
Q 5459 -410 5369 -499 
Q 5280 -589 5219 -589 
Q 5158 -589 4956 -512 
Q 4755 -435 4429 -243 
Q 4058 -45 4058 58 
Q 4058 96 4112 96 
Q 4166 96 4313 45 
Q 4461 -6 4662 -44 
Q 4864 -83 5037 -122 
L 5062 1907 
L 1382 1747 
L 1376 -435 
Q 1376 -602 1270 -602 
Q 1165 -602 1040 -522 
Q 915 -442 915 -346 
z
M 3930 1466 
L 4006 1472 
Q 4109 1472 4192 1392 
Q 4275 1312 4275 1261 
Q 4275 1210 4259 1187 
Q 4243 1165 4237 1133 
L 4128 634 
Q 4262 461 4262 406 
Q 4262 352 4211 342 
Q 4160 333 4090 326 
L 2528 269 
L 2534 186 
L 2534 147 
Q 2534 19 2445 19 
Q 2310 19 2224 92 
Q 2138 166 2138 243 
L 2138 365 
Q 2138 416 2131 467 
L 2080 1082 
Q 2061 1267 2016 1344 
Q 1971 1421 1971 1453 
Q 1971 1485 2067 1485 
Q 2163 1485 2445 1382 
L 3930 1466 
z
M 3834 1152 
L 2464 1082 
L 2502 595 
L 3763 640 
L 3834 1152 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-4f4e" d="M 4512 -627 
Q 4179 -269 3744 83 
Q 3674 128 3674 185 
Q 3674 243 3738 313 
Q 3802 384 3840 384 
Q 3981 384 4538 -102 
Q 4858 -384 4858 -467 
Q 4858 -512 4765 -598 
Q 4672 -685 4621 -685 
Q 4570 -685 4512 -627 
z
M 5901 -397 
Q 5722 -934 5152 -320 
Q 4845 6 4563 668 
Q 4282 1331 4109 2259 
L 2944 2170 
L 2931 442 
Q 3366 621 3654 765 
Q 3942 909 4000 909 
Q 4038 909 4038 858 
Q 4032 749 3625 477 
Q 3219 205 2809 -19 
Q 2400 -243 2313 -243 
Q 2227 -243 2156 -172 
Q 2086 -102 2032 -19 
Q 1978 64 1958 89 
Q 1939 115 1939 166 
Q 1939 218 1990 224 
L 2112 211 
Q 2240 211 2541 288 
L 2554 3648 
Q 2554 3994 2451 4198 
L 2445 4237 
Q 2445 4282 2512 4282 
Q 2579 4282 2970 4134 
Q 3507 4262 4150 4508 
Q 4794 4755 4941 4941 
Q 4998 5024 5024 5024 
Q 5050 5024 5146 4893 
Q 5242 4762 5242 4653 
Q 5242 4544 5165 4512 
Q 4659 4307 4250 4173 
Q 4314 3290 4429 2630 
Q 5101 2694 5213 2729 
Q 5325 2765 5395 2765 
Q 5466 2765 5590 2665 
Q 5715 2566 5715 2470 
Q 5715 2394 5581 2374 
L 4486 2291 
Q 4646 1562 4870 1021 
Q 5094 480 5289 189 
Q 5485 -102 5568 -102 
Q 5709 -96 6048 928 
Q 6131 1133 6172 1133 
Q 6214 1133 6214 973 
Q 6214 582 5901 -397 
z
M 1530 -538 
Q 1376 -538 1286 -458 
Q 1197 -378 1197 -288 
L 1203 -211 
Q 1235 -51 1235 147 
L 1261 2899 
Q 909 2438 624 2153 
Q 339 1869 250 1869 
Q 211 1869 211 1920 
Q 211 1971 294 2086 
Q 1011 3072 1395 3869 
Q 1779 4666 1779 4838 
Q 1779 4915 1754 4992 
Q 1754 5062 1827 5062 
Q 1901 5062 1997 5020 
Q 2093 4979 2166 4912 
Q 2240 4845 2240 4768 
Q 2240 4691 2057 4284 
Q 1875 3878 1619 3443 
L 1619 -416 
Q 1619 -538 1530 -538 
z
M 2944 2509 
L 4045 2605 
Q 3930 3322 3866 4038 
Q 3379 3898 2950 3795 
L 2944 2509 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7126" d="M 3610 4954 
L 3597 5030 
L 3597 5056 
Q 3597 5133 3661 5133 
Q 3725 5133 3821 5082 
Q 4070 4947 4070 4845 
Q 4070 4768 3907 4509 
Q 3744 4250 3635 4115 
L 4768 4192 
Q 4915 4205 4969 4224 
Q 5024 4243 5059 4243 
Q 5094 4243 5171 4198 
Q 5363 4070 5363 3949 
Q 5363 3885 5222 3872 
L 3654 3776 
L 3648 3238 
L 4435 3277 
Q 4582 3290 4630 3312 
Q 4678 3334 4713 3334 
Q 4749 3334 4826 3290 
Q 5024 3155 5024 3053 
Q 5024 2982 4877 2970 
L 3648 2912 
L 3648 2349 
L 4461 2387 
Q 4614 2406 4656 2425 
Q 4698 2445 4739 2445 
Q 4781 2445 4851 2390 
Q 4922 2336 4973 2265 
Q 5024 2195 5024 2157 
Q 5024 2099 4890 2080 
L 3648 2022 
L 3642 1427 
L 5120 1485 
Q 5242 1491 5296 1513 
Q 5350 1536 5404 1536 
Q 5459 1536 5529 1472 
Q 5600 1408 5645 1334 
Q 5690 1261 5690 1229 
Q 5690 1178 5555 1158 
L 1978 1030 
L 1978 832 
Q 1978 723 1882 723 
Q 1766 723 1644 812 
Q 1523 902 1523 953 
Q 1523 1005 1552 1113 
Q 1581 1222 1581 1459 
L 1600 3219 
Q 1184 2720 800 2394 
Q 627 2246 569 2246 
Q 512 2246 512 2307 
Q 512 2368 608 2483 
Q 1645 3731 2150 4813 
Q 2208 4941 2208 5062 
Q 2208 5184 2278 5184 
Q 2317 5184 2419 5133 
Q 2522 5082 2611 5005 
Q 2701 4928 2701 4883 
Q 2701 4838 2547 4579 
Q 2394 4320 2182 4026 
L 3264 4096 
Q 3482 4448 3546 4659 
Q 3610 4870 3610 4902 
L 3610 4954 
z
M 3270 3757 
L 1990 3680 
L 1984 3149 
L 3264 3213 
L 3270 3757 
z
M 3264 2893 
L 1984 2829 
L 1984 2266 
L 3258 2330 
L 3264 2893 
z
M 3258 2003 
L 1984 1946 
L 1978 1363 
L 3251 1414 
L 3258 2003 
z
M 5702 -467 
Q 5261 115 4736 582 
Q 4666 653 4666 710 
Q 4666 768 4742 832 
Q 4819 896 4873 896 
Q 4928 896 5053 790 
Q 5178 685 5370 509 
Q 5562 333 5725 166 
Q 5888 0 5990 -125 
Q 6093 -250 6093 -304 
Q 6093 -358 6006 -460 
Q 5920 -563 5849 -563 
Q 5779 -563 5702 -467 
z
M 448 -205 
Q 960 307 1158 736 
Q 1229 845 1257 845 
Q 1286 845 1350 826 
Q 1536 768 1536 678 
Q 1536 589 1341 294 
Q 1146 0 1018 -173 
Q 890 -346 787 -458 
Q 685 -570 643 -570 
Q 602 -570 496 -477 
Q 390 -384 390 -323 
Q 390 -262 448 -205 
z
M 4480 -294 
Q 4288 -550 4115 -339 
Q 3693 186 3315 506 
Q 3238 576 3238 627 
Q 3238 678 3312 752 
Q 3386 826 3440 826 
Q 3494 826 3616 733 
Q 3738 640 3888 499 
Q 4038 358 4182 208 
Q 4326 58 4422 -57 
Q 4518 -173 4518 -205 
Q 4518 -237 4480 -294 
z
M 2618 -320 
Q 2323 173 2054 454 
Q 1990 518 1990 572 
Q 1990 627 2080 698 
Q 2144 762 2195 762 
Q 2246 762 2342 672 
Q 2438 582 2553 448 
Q 2669 314 2774 173 
Q 2880 32 2947 -77 
Q 3014 -186 3014 -230 
Q 3014 -275 2918 -355 
Q 2822 -435 2755 -435 
Q 2688 -435 2618 -320 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8651" d="M 5523 4006 
L 5606 4013 
Q 5709 4013 5821 3917 
Q 5933 3821 5933 3757 
Q 5933 3693 5891 3661 
Q 5850 3629 5763 3513 
Q 5677 3398 5504 3219 
Q 5126 2816 4986 2816 
Q 4934 2816 4934 2880 
Q 4934 2944 5018 3046 
Q 5229 3302 5382 3661 
L 1690 3494 
Q 1690 2957 1661 2390 
Q 1632 1824 1510 1318 
Q 1254 333 442 -410 
Q 435 -416 425 -422 
Q 416 -429 410 -442 
Q 294 -531 236 -531 
Q 179 -531 179 -473 
Q 179 -416 269 -294 
Q 1037 685 1178 1862 
Q 1248 2477 1248 3219 
L 1248 3456 
Q 1229 3789 1200 3865 
Q 1171 3942 1171 3977 
Q 1171 4013 1241 4013 
Q 1312 4013 1709 3827 
L 3002 3885 
L 3002 4941 
Q 3002 5088 2906 5222 
Q 2886 5254 2886 5267 
Q 2886 5306 2956 5306 
Q 3027 5306 3136 5286 
Q 3418 5235 3418 5069 
L 3411 4672 
L 4685 4755 
Q 4832 4768 4886 4787 
Q 4941 4806 4976 4806 
Q 5011 4806 5082 4762 
Q 5274 4634 5274 4547 
Q 5274 4461 5114 4448 
L 3405 4339 
L 3398 3904 
L 5523 4006 
z
M 1875 2790 
L 2010 2765 
Q 2061 2758 2112 2758 
L 2157 2758 
L 2931 2829 
L 2931 3091 
Q 2931 3232 2835 3347 
Q 2816 3379 2816 3398 
Q 2816 3456 2944 3456 
Q 3072 3456 3226 3386 
Q 3322 3341 3322 3232 
L 3322 2861 
L 4250 2944 
Q 4371 2950 4438 2982 
Q 4506 3014 4538 3014 
Q 4570 3014 4640 2970 
Q 4832 2848 4832 2752 
Q 4832 2675 4704 2656 
L 3322 2534 
L 3322 2214 
Q 3322 2093 3427 2064 
Q 3533 2035 3875 2035 
Q 4218 2035 4461 2057 
Q 4704 2080 4835 2121 
Q 4966 2163 4995 2163 
Q 5024 2163 5094 2125 
Q 5280 2003 5280 1888 
Q 5280 1786 5094 1760 
Q 4307 1677 3706 1677 
L 3430 1683 
Q 2925 1696 2925 2150 
L 2925 2502 
L 2266 2445 
Q 2240 2438 2214 2438 
Q 2189 2438 2163 2438 
Q 1997 2438 1920 2560 
Q 1843 2682 1843 2736 
Q 1843 2790 1875 2790 
z
M 1658 -205 
Q 1587 -320 1507 -320 
Q 1427 -320 1337 -240 
Q 1248 -160 1248 -109 
Q 1248 -58 1299 13 
Q 1613 512 1798 1120 
Q 1837 1242 1933 1242 
Q 2003 1242 2086 1194 
Q 2170 1146 2170 1094 
Q 2170 1082 2112 890 
Q 1958 333 1658 -205 
z
M 5664 326 
Q 5357 736 4986 1069 
Q 4902 1146 4902 1194 
Q 4902 1242 4966 1315 
Q 5030 1389 5084 1389 
Q 5139 1389 5331 1235 
Q 5523 1082 5779 813 
Q 6099 499 6099 419 
Q 6099 339 6003 252 
Q 5907 166 5846 166 
Q 5786 166 5664 326 
z
M 3789 570 
Q 3520 928 3245 1165 
Q 3162 1248 3162 1296 
Q 3162 1344 3232 1411 
Q 3302 1478 3347 1478 
Q 3462 1478 3923 1018 
Q 4186 768 4186 694 
Q 4186 621 4099 531 
Q 4013 442 3949 442 
Q 3885 442 3789 570 
z
M 5261 -205 
Q 5261 -486 4384 -486 
Q 3507 -486 3053 -211 
Q 2682 32 2502 550 
Q 2426 755 2413 899 
Q 2400 1043 2368 1030 
Q 2368 1139 2534 1171 
Q 2592 1178 2605 1178 
Q 2720 1178 2752 1030 
Q 2842 640 2998 393 
Q 3155 147 3456 25 
Q 3757 -96 4208 -96 
Q 4659 -96 4717 -64 
Q 4480 589 4480 720 
Q 4480 851 4566 851 
Q 4653 851 4730 698 
Q 4877 403 5043 169 
Q 5210 -64 5235 -112 
Q 5261 -160 5261 -205 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-7ec4" d="M 2675 -333 
L 2618 -333 
Q 2438 -333 2355 -262 
Q 2272 -192 2214 19 
Q 2208 32 2208 51 
Q 2208 77 2246 77 
Q 2266 77 2285 70 
Q 2374 45 2460 38 
Q 2547 32 2611 32 
L 3181 51 
L 3149 4090 
Q 3149 4205 3139 4278 
Q 3130 4352 3098 4416 
Q 3053 4493 3053 4525 
Q 3053 4563 3117 4563 
Q 3168 4563 3270 4537 
Q 3373 4512 3546 4442 
L 5024 4538 
Q 5050 4538 5069 4541 
Q 5088 4544 5107 4544 
Q 5229 4544 5312 4454 
Q 5395 4365 5395 4314 
Q 5395 4269 5376 4240 
Q 5357 4211 5357 4179 
L 5331 122 
L 5626 134 
Q 5702 141 5756 150 
Q 5811 160 5862 173 
Q 5888 179 5910 182 
Q 5933 186 5952 186 
Q 6003 186 6076 131 
Q 6150 77 6204 3 
Q 6259 -70 6259 -128 
Q 6259 -205 6086 -218 
L 2675 -333 
z
M 4954 4179 
L 3546 4096 
L 3552 3059 
L 4954 3136 
L 4954 4179 
z
M 4947 2784 
L 3552 2707 
L 3558 1670 
L 4947 1747 
L 4947 2784 
z
M 4947 1389 
L 3565 1325 
L 3571 64 
L 4941 109 
L 4947 1389 
z
M 1453 563 
Q 2016 781 2349 925 
Q 2682 1069 2739 1075 
Q 2797 1082 2803 1062 
Q 2816 922 1840 378 
Q 864 -166 691 -179 
Q 646 -186 560 -109 
Q 474 -32 410 64 
Q 346 160 339 211 
Q 339 237 403 243 
L 474 250 
Q 576 256 768 314 
L 1453 563 
z
M 1830 1830 
Q 2170 1882 2339 1910 
Q 2509 1939 2601 1932 
Q 2694 1926 2688 1875 
Q 2682 1786 2240 1622 
Q 1798 1459 1366 1350 
Q 934 1242 864 1248 
Q 794 1254 749 1293 
Q 627 1440 595 1545 
Q 563 1651 563 1664 
Q 570 1722 634 1715 
L 730 1709 
Q 870 1696 998 1702 
Q 1286 2118 1523 2528 
Q 1030 2874 544 3085 
Q 435 3136 422 3187 
Q 410 3238 454 3337 
Q 499 3437 560 3449 
Q 621 3462 742 3418 
Q 768 3405 787 3398 
Q 1331 4186 1446 4480 
Q 1562 4774 1562 4915 
L 1562 4979 
Q 1562 5050 1613 5050 
Q 1645 5050 1734 4998 
Q 1978 4851 1978 4717 
Q 1939 4557 1728 4186 
Q 1536 3840 1107 3226 
L 1261 3142 
Q 1549 2982 1728 2861 
L 1754 2899 
Q 2227 3680 2272 3878 
Q 2285 3936 2291 4032 
Q 2298 4128 2333 4128 
Q 2368 4128 2451 4077 
Q 2688 3936 2694 3795 
Q 2694 3750 2675 3706 
Q 2086 2675 1459 1766 
Q 1632 1798 1830 1830 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-6bd4" d="M 3494 4570 
Q 3494 4742 3379 4902 
Q 3347 4954 3347 4992 
Q 3347 5030 3404 5030 
Q 3462 5030 3584 5005 
Q 3910 4922 3910 4768 
L 3898 2560 
Q 4589 2944 5030 3354 
Q 5139 3450 5197 3661 
Q 5216 3744 5274 3744 
Q 5312 3744 5389 3674 
Q 5613 3482 5613 3309 
Q 5613 3238 5568 3213 
Q 5178 2938 4784 2666 
Q 4390 2394 3898 2170 
L 3885 435 
Q 3885 301 3926 224 
Q 3968 147 4134 118 
Q 4301 90 4704 90 
Q 5107 90 5305 122 
Q 5504 154 5561 189 
Q 5619 224 5692 374 
Q 5766 525 5875 1229 
Q 5926 1530 6003 1530 
Q 6061 1530 6080 1392 
Q 6099 1254 6099 854 
Q 6099 454 6073 224 
Q 6048 -6 5923 -121 
Q 5798 -237 5510 -275 
Q 5222 -314 4752 -314 
Q 4282 -314 4010 -278 
Q 3738 -243 3606 -92 
Q 3475 58 3475 403 
L 3494 4570 
z
M 1306 4333 
Q 1306 4512 1197 4659 
Q 1165 4710 1165 4742 
Q 1165 4774 1248 4774 
Q 1331 4774 1485 4723 
Q 1715 4653 1715 4525 
L 1709 2829 
L 2406 2861 
Q 2598 2880 2668 2905 
Q 2739 2931 2800 2931 
Q 2861 2931 2937 2867 
Q 3014 2803 3068 2726 
Q 3123 2650 3123 2611 
Q 3123 2528 2950 2515 
L 1709 2451 
L 1702 429 
Q 2470 794 2925 1075 
Q 3046 1152 3116 1152 
Q 3187 1152 3187 1107 
Q 3187 992 2816 730 
Q 2221 301 1769 38 
Q 1318 -224 1152 -304 
Q 986 -384 922 -384 
Q 858 -384 778 -342 
Q 698 -301 602 -195 
Q 506 -90 506 -45 
Q 506 0 685 22 
Q 864 45 1299 250 
L 1306 4333 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-8f83" d="M 3725 3507 
Q 3840 3507 4026 3322 
Q 4096 3251 4096 3203 
Q 4096 3155 3949 2950 
Q 3802 2746 3558 2490 
Q 3315 2234 3132 2096 
Q 2950 1958 2889 1958 
Q 2829 1958 2829 1997 
Q 2829 2061 2931 2176 
Q 3181 2445 3398 2803 
Q 3654 3200 3654 3353 
Q 3654 3507 3725 3507 
z
M 3322 3936 
L 4186 3987 
L 4198 4704 
Q 4198 4838 4147 4931 
Q 4096 5024 4096 5053 
Q 4096 5082 4144 5082 
Q 4192 5082 4339 5053 
Q 4486 5024 4531 4979 
Q 4576 4934 4576 4851 
L 4570 4013 
L 5293 4058 
Q 5491 4077 5561 4096 
Q 5632 4115 5689 4115 
Q 5747 4115 5824 4070 
Q 6016 3955 6016 3849 
Q 6016 3744 5901 3731 
L 3578 3597 
Q 3450 3584 3334 3584 
Q 3091 3584 3002 3891 
Q 2995 3904 2995 3939 
Q 2995 3974 3030 3974 
Q 3066 3974 3136 3955 
Q 3206 3936 3322 3936 
z
M 1779 774 
Q 915 365 691 371 
Q 595 378 457 515 
Q 320 653 320 723 
Q 326 774 435 780 
Q 544 787 736 832 
Q 928 877 1779 1158 
L 1779 2048 
L 1376 2010 
L 1350 2010 
L 1114 1984 
L 928 1952 
Q 864 1952 781 1993 
Q 698 2035 710 2138 
Q 730 2176 730 2195 
Q 1082 3027 1267 3616 
L 992 3597 
L 845 3597 
Q 685 3597 666 3642 
Q 563 3738 528 3834 
Q 493 3930 493 3968 
Q 493 4006 525 4006 
Q 557 4006 614 3987 
Q 672 3968 800 3968 
L 877 3968 
L 1376 3987 
Q 1549 4640 1565 4717 
Q 1581 4794 1555 4870 
Q 1491 5037 1606 5043 
Q 1690 5043 1811 4982 
Q 1933 4922 1974 4877 
Q 2016 4832 1990 4730 
Q 1920 4531 1792 4006 
L 2234 4026 
Q 2355 4038 2428 4073 
Q 2502 4109 2560 4109 
Q 2618 4109 2694 4045 
Q 2886 3898 2886 3795 
Q 2886 3718 2733 3706 
L 1690 3642 
Q 1574 3245 1357 2720 
L 1210 2374 
Q 1248 2355 1318 2362 
L 1779 2400 
L 1779 2944 
Q 1779 3072 1728 3145 
Q 1677 3219 1677 3251 
Q 1677 3283 1741 3283 
Q 1805 3283 1901 3264 
Q 2163 3200 2163 3053 
L 2157 2432 
L 2362 2451 
Q 2451 2470 2502 2483 
Q 2624 2573 2784 2336 
Q 2829 2285 2829 2227 
Q 2842 2150 2694 2131 
L 2157 2080 
L 2150 1286 
Q 2438 1395 2684 1494 
Q 2931 1594 3020 1590 
Q 3110 1587 3110 1542 
Q 3104 1472 2925 1370 
Q 2509 1133 2144 954 
L 2131 -454 
Q 2131 -595 2048 -595 
L 2022 -595 
L 2016 -589 
Q 1734 -538 1734 -333 
L 1734 -269 
Q 1779 -32 1779 141 
L 1779 774 
z
M 5888 2266 
Q 5805 2157 5757 2157 
Q 5709 2157 5677 2189 
Q 5645 2221 5462 2464 
Q 5280 2707 4749 3200 
Q 4678 3258 4678 3318 
Q 4678 3379 4752 3449 
Q 4826 3520 4870 3520 
Q 4915 3520 5132 3347 
Q 5350 3174 5664 2832 
Q 5978 2490 5978 2422 
Q 5978 2355 5888 2266 
z
M 4410 685 
Q 4122 333 3766 80 
Q 3411 -173 3148 -297 
Q 2886 -422 2726 -470 
Q 2566 -518 2554 -518 
Q 2490 -518 2490 -480 
Q 2490 -416 2611 -352 
Q 3520 141 4109 870 
L 4186 966 
Q 3821 1472 3597 2003 
Q 3514 2189 3514 2237 
Q 3514 2285 3587 2345 
Q 3661 2406 3734 2406 
Q 3808 2406 3859 2298 
Q 4090 1760 4410 1286 
Q 4646 1658 4761 1965 
Q 4877 2272 4877 2400 
L 4877 2464 
Q 4877 2566 4934 2566 
Q 4992 2566 5145 2454 
Q 5299 2342 5299 2278 
Q 5299 2214 5216 2003 
Q 4992 1440 4659 979 
Q 5280 262 6150 -141 
Q 6272 -198 6272 -250 
Q 6272 -275 6147 -406 
Q 6022 -538 5942 -538 
Q 5862 -538 5485 -269 
Q 4781 250 4410 685 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-5fc3" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-7406" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-97e7" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-6027" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-6bd4" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-8f83" x="999.999847"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_15">
     <path d="M 471.34 54.6725 
L 528.94 54.6725 
Q 530.54 54.6725 530.54 53.0725 
L 530.54 30.6875 
Q 530.54 29.0875 528.94 29.0875 
L 471.34 29.0875 
Q 469.74 29.0875 469.74 30.6875 
L 469.74 53.0725 
Q 469.74 54.6725 471.34 54.6725 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_16">
     <path d="M 472.94 38.29625 
L 488.94 38.29625 
L 488.94 32.69625 
L 472.94 32.69625 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_18">
     <!-- 高焦虑组 -->
     <g transform="translate(495.34 38.29625) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
    <g id="patch_17">
     <path d="M 472.94 50.09625 
L 488.94 50.09625 
L 488.94 44.49625 
L 472.94 44.49625 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_19">
     <!-- 低焦虑组 -->
     <g transform="translate(495.34 50.09625) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_18">
    <path d="M 582.83 398.884375 
L 1069.2 398.884375 
L 1069.2 25.0875 
L 582.83 25.0875 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_19">
    <path d="M 604.937727 798.67422 
L 695.969545 798.67422 
L 695.969545 276.08916 
L 604.937727 276.08916 
z
" clip-path="url(#p5af46ba183)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_20">
    <path d="M 865.028636 798.67422 
L 956.060455 798.67422 
L 956.060455 105.367354 
L 865.028636 105.367354 
z
" clip-path="url(#p5af46ba183)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_21">
    <path d="M 695.969545 798.67422 
L 787.001364 798.67422 
L 787.001364 329.042309 
L 695.969545 329.042309 
z
" clip-path="url(#p5af46ba183)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_22">
    <path d="M 956.060455 798.67422 
L 1047.092273 798.67422 
L 1047.092273 234.570899 
L 956.060455 234.570899 
z
" clip-path="url(#p5af46ba183)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_5">
     <g id="line2d_29">
      <path d="M 695.969545 398.884375 
L 695.969545 25.0875 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mf98408d2ab" x="695.969545" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_20">
      <!-- 实验前 -->
      <g transform="translate(683.969545 411.908125) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-5b9e" d="M 1344 3661 
Q 1190 3213 979 2816 
Q 947 2739 876 2739 
Q 806 2739 694 2812 
Q 582 2886 582 2947 
Q 582 3008 681 3168 
Q 781 3328 928 3680 
Q 1075 4032 1178 4371 
Q 1210 4480 1293 4480 
Q 1350 4480 1449 4445 
Q 1549 4410 1549 4346 
Q 1549 4282 1466 4026 
L 2970 4115 
L 2970 4755 
Q 2970 4877 2902 4973 
Q 2835 5069 2835 5094 
Q 2835 5152 2918 5152 
Q 3002 5152 3117 5126 
Q 3430 5043 3430 4915 
L 3424 4147 
L 5510 4269 
L 5555 4269 
Q 5683 4269 5788 4198 
Q 5894 4128 5894 4057 
Q 5894 3987 5849 3945 
Q 5805 3904 5728 3782 
Q 5651 3661 5372 3299 
Q 5094 2938 4973 2938 
Q 4922 2938 4922 3005 
Q 4922 3072 5056 3302 
Q 5190 3533 5312 3885 
L 1344 3661 
z
M 1248 2195 
Q 1165 2246 1165 2300 
Q 1165 2355 1213 2435 
Q 1261 2515 1318 2515 
Q 1376 2515 1709 2364 
Q 2042 2214 2400 1984 
Q 2534 1907 2534 1833 
Q 2534 1760 2460 1670 
Q 2387 1581 2339 1581 
Q 2291 1581 1996 1779 
Q 1702 1978 1248 2195 
z
M 1747 2982 
Q 1664 3034 1664 3088 
Q 1664 3142 1712 3222 
Q 1760 3302 1817 3302 
Q 1875 3302 2188 3161 
Q 2502 3021 2899 2771 
Q 3027 2694 3027 2617 
Q 3027 2541 2953 2454 
Q 2880 2368 2835 2368 
Q 2790 2368 2480 2576 
Q 2170 2784 1747 2982 
z
M 5165 -538 
Q 4813 -294 4438 -54 
Q 4064 186 3584 390 
Q 3520 429 3520 467 
Q 3520 506 3546 557 
Q 3616 710 3718 710 
Q 3770 710 4058 582 
Q 4672 307 5466 -224 
Q 5549 -282 5549 -355 
Q 5549 -429 5462 -518 
Q 5376 -608 5305 -608 
Q 5235 -608 5165 -538 
z
M 2848 883 
L 934 800 
Q 858 800 739 848 
Q 621 896 538 1139 
Q 525 1178 525 1197 
Q 525 1216 550 1216 
Q 576 1216 646 1190 
Q 717 1165 890 1165 
L 954 1165 
L 3066 1261 
Q 3251 1683 3354 2445 
Q 3373 2618 3373 2886 
L 3373 2989 
Q 3373 3187 3264 3328 
Q 3245 3354 3245 3392 
Q 3245 3462 3318 3462 
Q 3392 3462 3507 3443 
Q 3622 3424 3724 3379 
Q 3827 3334 3827 3200 
Q 3814 2675 3724 2128 
Q 3635 1581 3520 1280 
L 5139 1357 
Q 5286 1370 5356 1398 
Q 5427 1427 5456 1427 
Q 5485 1427 5568 1376 
Q 5805 1229 5805 1101 
Q 5805 1024 5658 1005 
L 3334 902 
Q 2963 282 2150 -141 
Q 1510 -474 838 -621 
Q 608 -672 595 -672 
Q 538 -672 538 -608 
Q 538 -518 691 -461 
Q 1530 -179 2170 256 
Q 2592 550 2848 883 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-9a8c" d="M 5549 1760 
Q 5222 851 4576 179 
L 5536 218 
Q 5626 218 5696 243 
Q 5766 269 5817 269 
Q 5869 269 5997 150 
Q 6125 32 6125 -48 
Q 6125 -128 5971 -141 
L 3194 -243 
Q 3085 -256 3005 -256 
Q 2925 -256 2854 -173 
Q 2784 -90 2749 -3 
Q 2714 83 2714 121 
Q 2714 160 2765 160 
Q 2816 160 2880 141 
Q 2944 122 3040 122 
L 3085 122 
L 4282 166 
Q 4832 941 5107 1734 
Q 5146 1824 5146 1894 
Q 5146 1965 5136 2025 
Q 5126 2086 5190 2106 
Q 5338 2138 5504 1952 
Q 5581 1862 5568 1827 
Q 5555 1792 5549 1760 
z
M 3629 480 
L 3578 480 
Q 3501 499 3462 608 
Q 3328 1094 3078 1606 
Q 3066 1658 3066 1696 
Q 3066 1734 3162 1798 
Q 3258 1862 3309 1846 
Q 3360 1830 3433 1712 
Q 3507 1594 3587 1421 
Q 3667 1248 3724 1110 
Q 3782 973 3827 838 
Q 3872 704 3869 662 
Q 3866 621 3770 550 
Q 3674 480 3629 480 
z
M 4582 1082 
Q 4563 1037 4454 979 
Q 4250 870 4192 1062 
Q 4058 1709 3885 2086 
Q 3866 2144 3872 2169 
Q 3878 2195 3955 2259 
Q 4032 2323 4115 2291 
Q 4230 2246 4416 1715 
Q 4602 1184 4582 1082 
z
M 3776 2630 
L 3642 2624 
Q 3558 2624 3488 2669 
Q 3418 2714 3354 2931 
Q 3354 3002 3443 2989 
L 3648 2963 
L 3699 2963 
L 4653 3027 
Q 4768 3046 4797 3062 
Q 4826 3078 4880 3078 
Q 4934 3078 5011 3027 
Q 5178 2918 5178 2803 
Q 5178 2720 5005 2707 
L 3776 2630 
z
M 934 4320 
Q 685 4320 602 4646 
L 602 4678 
Q 602 4717 650 4717 
Q 698 4717 762 4701 
Q 826 4685 954 4685 
L 998 4685 
L 2336 4794 
Q 2432 4794 2521 4726 
Q 2611 4659 2608 4614 
Q 2605 4570 2592 4538 
Q 2579 4506 2573 4474 
L 2208 2214 
L 2490 2227 
L 2541 2227 
Q 2650 2227 2730 2156 
Q 2810 2086 2810 2028 
Q 2810 1971 2797 1932 
Q 2784 1894 2778 1862 
Q 2682 435 2483 -224 
Q 2445 -346 2342 -419 
Q 2240 -493 2163 -493 
Q 2010 -493 1670 -166 
Q 1318 147 1318 237 
Q 1318 282 1388 282 
Q 1459 282 1664 163 
Q 1869 45 1993 3 
Q 2118 -38 2124 -38 
Q 2131 -38 2131 0 
Q 2323 685 2387 1907 
L 1082 1805 
Q 1024 1805 985 1785 
Q 947 1766 880 1772 
Q 813 1779 723 1856 
Q 634 1933 646 2016 
Q 646 2029 652 2048 
Q 659 2067 691 2172 
Q 723 2278 771 2585 
Q 819 2893 864 3165 
Q 909 3437 928 3578 
L 934 3642 
Q 947 3725 947 3802 
L 915 4000 
Q 922 4051 998 4045 
L 1005 4045 
L 1005 4038 
L 1024 4038 
Q 1370 3878 1357 3706 
L 1357 3674 
Q 1299 3392 1228 2941 
Q 1158 2490 1088 2163 
L 1101 2157 
L 1786 2189 
L 2144 4416 
L 1050 4326 
Q 1018 4320 986 4320 
L 934 4320 
z
M 4422 4576 
Q 4979 3898 5549 3411 
Q 5850 3155 6195 2893 
Q 6266 2835 6266 2803 
Q 6266 2771 6163 2646 
Q 6061 2522 5977 2522 
Q 5894 2522 5366 3030 
Q 4838 3539 4237 4262 
Q 3949 3770 3533 3270 
L 3462 3194 
Q 3027 2752 2650 2483 
Q 2534 2406 2480 2406 
Q 2426 2406 2426 2460 
Q 2426 2515 2528 2618 
Q 3181 3277 3622 4019 
Q 4064 4762 4064 4986 
L 4064 5024 
L 4058 5069 
Q 4058 5146 4115 5146 
Q 4147 5146 4249 5094 
Q 4352 5043 4441 4969 
Q 4531 4896 4531 4845 
Q 4531 4794 4422 4576 
z
M 403 960 
Q 730 960 1978 1568 
Q 2054 1613 2108 1613 
Q 2163 1613 2163 1562 
Q 2163 1478 1820 1216 
Q 1478 954 1072 720 
Q 666 486 589 486 
Q 512 486 461 525 
Q 294 685 243 877 
Q 237 890 237 928 
Q 237 966 269 966 
L 275 966 
Q 307 960 339 960 
L 403 960 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-524d" d="M 474 3904 
Q 698 3872 819 3872 
L 902 3872 
L 2592 3968 
L 2534 4038 
Q 2298 4346 1875 4698 
Q 1811 4755 1811 4812 
Q 1811 4870 1878 4944 
Q 1946 5018 1984 5018 
Q 2022 5018 2124 4950 
Q 2227 4883 2361 4777 
Q 2496 4672 2624 4553 
Q 2752 4435 2835 4336 
Q 2918 4237 2918 4179 
Q 2918 4122 2867 4054 
Q 2816 3987 2803 3981 
L 3373 4013 
Q 3866 4480 4058 4765 
Q 4250 5050 4250 5165 
L 4250 5178 
Q 4250 5248 4288 5248 
Q 4326 5248 4409 5190 
Q 4493 5133 4566 5046 
Q 4640 4960 4640 4883 
Q 4640 4806 4377 4524 
Q 4115 4243 3891 4045 
L 5280 4122 
Q 5453 4128 5549 4163 
Q 5645 4198 5686 4198 
Q 5728 4198 5818 4147 
Q 6042 4006 6042 3885 
Q 6042 3814 5914 3802 
L 1050 3539 
Q 922 3526 832 3526 
Q 627 3526 528 3670 
Q 429 3814 429 3859 
Q 429 3904 474 3904 
z
M 5408 -70 
L 5414 -294 
Q 5414 -448 5305 -528 
Q 5197 -608 5120 -608 
Q 5043 -608 4908 -537 
Q 4774 -467 4617 -361 
Q 4461 -256 4320 -137 
Q 4179 -19 4086 73 
Q 3994 166 3994 211 
Q 3994 256 4058 256 
Q 4122 256 4397 115 
Q 4672 -26 4986 -102 
L 5005 3014 
Q 5005 3213 4922 3347 
Q 4890 3411 4890 3433 
Q 4890 3456 4934 3456 
Q 4979 3456 5155 3398 
Q 5331 3341 5376 3289 
Q 5421 3238 5421 3162 
L 5408 -70 
z
M 1011 -301 
Q 1126 621 1126 2637 
Q 1126 2886 1088 2992 
Q 1050 3098 1050 3123 
Q 1050 3168 1107 3168 
Q 1165 3168 1504 3059 
L 2867 3142 
L 2912 3142 
Q 3002 3142 3091 3088 
Q 3181 3034 3181 2931 
L 3162 2790 
L 3181 -51 
L 3194 -250 
Q 3194 -358 3101 -454 
Q 3008 -550 2931 -550 
Q 2790 -550 2364 -265 
Q 1939 19 1939 122 
Q 1939 160 1987 160 
Q 2035 160 2243 80 
Q 2451 0 2790 -90 
L 2784 954 
L 1466 890 
L 1440 -390 
Q 1440 -544 1325 -544 
Q 1210 -544 1110 -461 
Q 1011 -378 1011 -301 
z
M 4282 742 
Q 4282 576 4179 576 
Q 4173 576 4090 602 
Q 3846 678 3846 832 
Q 3846 877 3862 963 
Q 3878 1050 3878 1325 
L 3872 2522 
Q 3872 2739 3830 2819 
Q 3789 2899 3789 2931 
Q 3789 2963 3846 2963 
Q 3904 2963 4057 2905 
Q 4211 2848 4233 2793 
Q 4256 2739 4256 2675 
L 4282 742 
z
M 2778 2797 
L 1498 2714 
L 1485 2150 
L 2778 2214 
L 2778 2797 
z
M 2784 1882 
L 1478 1818 
L 1472 1235 
L 2784 1299 
L 2784 1882 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-5b9e"/>
       <use xlink:href="#LXGWWenKai-Regular-9a8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-524d" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_31">
      <path d="M 956.060455 398.884375 
L 956.060455 25.0875 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mf98408d2ab" x="956.060455" y="398.884375" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_21">
      <!-- 实验后 -->
      <g transform="translate(944.060455 411.788125) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-540e" d="M 1389 3770 
Q 1389 4013 1353 4118 
Q 1318 4224 1318 4285 
Q 1318 4346 1420 4346 
Q 1523 4346 1843 4186 
Q 2656 4346 3258 4550 
Q 4314 4915 4525 5126 
Q 4550 5152 4588 5152 
Q 4627 5152 4704 5094 
Q 4909 4941 4909 4800 
Q 4909 4730 4813 4685 
Q 3283 4064 1830 3872 
Q 1824 3680 1821 3481 
Q 1818 3283 1805 3078 
L 5222 3270 
Q 5421 3290 5475 3312 
Q 5530 3334 5562 3334 
Q 5594 3334 5683 3277 
Q 5914 3110 5914 3002 
Q 5914 2925 5747 2912 
L 1786 2701 
Q 1760 2304 1686 1865 
Q 1613 1427 1411 1001 
Q 1210 576 976 265 
Q 742 -45 547 -217 
Q 352 -390 294 -390 
Q 237 -390 237 -332 
Q 237 -275 403 -70 
Q 570 134 768 486 
Q 1184 1254 1286 1958 
Q 1389 2573 1389 3770 
z
M 2208 -339 
Q 2227 -205 2227 -166 
L 2227 13 
Q 2227 51 2221 96 
L 2125 1357 
Q 2112 1581 2057 1680 
Q 2003 1779 2003 1824 
Q 2003 1869 2096 1869 
Q 2189 1869 2528 1734 
L 4794 1862 
L 4870 1862 
Q 5037 1862 5117 1772 
Q 5197 1683 5197 1632 
Q 5197 1581 5174 1545 
Q 5152 1510 5146 1478 
L 4960 205 
Q 5126 0 5126 -64 
Q 5126 -128 5068 -137 
Q 5011 -147 4922 -154 
L 2650 -218 
L 2669 -448 
L 2669 -467 
Q 2669 -621 2544 -621 
Q 2419 -621 2313 -537 
Q 2208 -454 2208 -339 
z
M 4704 1491 
L 2534 1382 
L 2624 134 
L 4557 205 
L 4704 1491 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-5b9e"/>
       <use xlink:href="#LXGWWenKai-Regular-9a8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-540e" x="199.999969"/>
      </g>
     </g>
    </g>
    <g id="text_22">
     <!-- 测量时间点 -->
     <g transform="translate(801.015 424.714062) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-6d4b" d="M 1459 3795 
Q 1190 4122 723 4506 
Q 640 4570 640 4611 
Q 640 4653 691 4742 
Q 742 4832 803 4832 
Q 864 4832 976 4755 
Q 1088 4678 1235 4556 
Q 1382 4435 1520 4310 
Q 1658 4186 1747 4083 
Q 1837 3981 1837 3949 
Q 1837 3917 1798 3859 
Q 1760 3802 1705 3754 
Q 1651 3706 1593 3706 
Q 1536 3706 1459 3795 
z
M 1562 2643 
Q 1562 2547 1482 2457 
Q 1402 2368 1347 2368 
Q 1293 2368 1021 2614 
Q 749 2861 397 3085 
Q 307 3142 307 3184 
Q 307 3226 358 3318 
Q 410 3411 464 3411 
Q 518 3411 796 3241 
Q 1075 3072 1466 2758 
Q 1562 2682 1562 2643 
z
M 672 -262 
L 570 -243 
Q 262 -186 262 -58 
Q 262 -26 314 6 
Q 461 90 589 294 
Q 1062 1082 1491 1952 
Q 1587 2150 1664 2150 
Q 1715 2150 1715 2054 
Q 1715 1958 1520 1417 
Q 1325 877 1062 307 
Q 800 -262 672 -262 
z
M 1600 -525 
Q 1542 -550 1513 -550 
Q 1485 -550 1485 -489 
Q 1485 -429 1568 -365 
Q 2278 179 2586 890 
Q 2746 1280 2826 1805 
Q 2906 2330 2906 3366 
Q 2906 3469 2858 3549 
Q 2810 3629 2810 3654 
Q 2810 3712 2874 3712 
Q 2938 3712 3069 3667 
Q 3200 3622 3238 3587 
Q 3277 3552 3277 3469 
Q 3258 2464 3194 1907 
Q 3130 1350 2960 905 
Q 2790 461 2460 112 
Q 2131 -237 1600 -525 
z
M 2118 1280 
Q 2144 1523 2144 1626 
L 2144 1779 
L 2099 4134 
Q 2099 4346 2051 4442 
Q 2003 4538 2003 4586 
Q 2003 4634 2064 4634 
Q 2125 4634 2464 4518 
L 3712 4582 
L 3782 4589 
Q 3846 4589 3932 4534 
Q 4019 4480 4019 4365 
L 4006 4250 
L 3936 1280 
L 3936 1242 
Q 3930 1120 3853 1120 
Q 3725 1146 3641 1210 
Q 3558 1274 3565 1350 
L 3603 1549 
L 3642 4243 
L 2432 4186 
L 2464 2605 
L 2464 2278 
L 2470 1779 
L 2477 1574 
L 2483 1235 
L 2483 1197 
Q 2483 1069 2406 1069 
Q 2285 1069 2201 1136 
Q 2118 1203 2118 1280 
z
M 3981 435 
Q 4090 282 4160 160 
Q 4230 38 4230 -35 
Q 4230 -109 4124 -166 
Q 4019 -224 3984 -224 
Q 3949 -224 3926 -198 
Q 3904 -173 3737 121 
Q 3571 416 3414 630 
Q 3258 845 3258 909 
Q 3258 973 3338 1021 
Q 3418 1069 3466 1069 
Q 3514 1069 3581 979 
Q 3648 890 3760 739 
Q 3872 589 3981 435 
z
M 5709 -58 
L 5722 -282 
Q 5722 -448 5626 -537 
Q 5530 -627 5453 -627 
Q 5376 -627 5251 -550 
Q 5126 -474 4976 -365 
Q 4826 -256 4694 -134 
Q 4563 -13 4476 83 
Q 4390 179 4390 214 
Q 4390 250 4454 250 
Q 4518 250 4774 112 
Q 5030 -26 5331 -128 
L 5344 4749 
Q 5344 4902 5242 5050 
Q 5216 5088 5216 5120 
Q 5216 5152 5293 5152 
Q 5370 5152 5523 5091 
Q 5677 5030 5699 4979 
Q 5722 4928 5722 4838 
L 5709 -58 
z
M 4448 3674 
Q 4448 3808 4413 3894 
Q 4378 3981 4368 3993 
Q 4358 4006 4358 4028 
Q 4358 4051 4422 4051 
Q 4486 4051 4617 4000 
Q 4749 3949 4777 3904 
Q 4806 3859 4806 3776 
L 4819 1011 
Q 4819 838 4710 838 
Q 4602 838 4509 915 
Q 4416 992 4416 1049 
Q 4416 1107 4432 1222 
Q 4448 1338 4448 1581 
L 4448 3674 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-65f6" d="M 5094 -13 
L 5114 -224 
Q 5114 -358 5040 -432 
Q 4966 -506 4892 -534 
Q 4819 -563 4774 -563 
Q 4730 -563 4576 -489 
Q 4422 -416 4243 -304 
Q 4064 -192 3904 -70 
Q 3744 51 3641 150 
Q 3539 250 3539 294 
Q 3539 339 3600 339 
Q 3661 339 3821 269 
Q 4237 70 4685 -38 
L 4666 3091 
L 3142 3002 
Q 3104 2995 3072 2995 
L 3002 2995 
Q 2822 2995 2771 3046 
Q 2650 3168 2605 3341 
Q 2598 3360 2598 3389 
Q 2598 3418 2617 3418 
Q 2637 3418 2717 3389 
Q 2797 3360 2950 3360 
L 2995 3360 
L 4666 3456 
L 4659 4589 
Q 4659 4774 4579 4883 
Q 4499 4992 4499 5030 
Q 4499 5069 4579 5069 
Q 4659 5069 4828 5008 
Q 4998 4947 5033 4899 
Q 5069 4851 5069 4762 
L 5075 3482 
L 5562 3507 
Q 5766 3526 5827 3552 
Q 5888 3578 5933 3578 
Q 5978 3578 6064 3523 
Q 6150 3469 6217 3392 
Q 6285 3315 6285 3264 
Q 6285 3213 6237 3197 
Q 6189 3181 6125 3174 
L 5075 3117 
L 5094 -13 
z
M 4032 1389 
Q 3885 1254 3811 1254 
Q 3738 1254 3674 1350 
Q 3398 1824 3059 2246 
Q 3021 2298 3021 2346 
Q 3021 2394 3075 2438 
Q 3130 2483 3190 2508 
Q 3251 2534 3280 2534 
Q 3309 2534 3341 2496 
Q 3373 2458 3446 2368 
Q 3520 2278 3629 2150 
Q 3738 2022 3843 1891 
Q 3949 1760 4019 1654 
Q 4090 1549 4090 1497 
Q 4090 1446 4032 1389 
z
M 2003 2400 
L 1094 2355 
L 1120 1088 
L 1978 1114 
L 2003 2400 
z
M 2042 3974 
L 1062 3891 
L 1088 2694 
L 2016 2739 
L 2042 3974 
z
M 691 3885 
Q 691 4058 646 4160 
Q 602 4262 602 4313 
Q 602 4365 678 4365 
Q 755 4365 1050 4230 
L 2144 4320 
L 2214 4320 
Q 2278 4320 2377 4272 
Q 2477 4224 2477 4109 
Q 2477 4070 2464 4032 
Q 2451 3994 2445 3962 
L 2342 1126 
Q 2522 928 2522 867 
Q 2522 806 2464 796 
Q 2406 787 2336 781 
L 1126 742 
L 1133 378 
L 1133 358 
Q 1133 224 1024 224 
Q 883 224 800 304 
Q 717 384 717 429 
L 717 493 
Q 717 512 736 589 
Q 755 666 755 896 
L 691 3885 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-95f4" d="M 2643 1139 
L 3795 1190 
L 3840 1990 
L 2611 1920 
L 2643 1139 
z
M 2598 2253 
L 3859 2323 
L 3904 3053 
L 2566 2976 
L 2598 2253 
z
M 2547 435 
Q 2522 435 2496 444 
Q 2470 454 2394 480 
Q 2266 525 2266 653 
L 2266 1062 
Q 2176 2995 2166 3100 
Q 2157 3206 2125 3280 
Q 2093 3354 2093 3395 
Q 2093 3437 2163 3437 
Q 2234 3437 2573 3341 
L 3987 3437 
Q 4147 3437 4249 3360 
Q 4352 3283 4352 3213 
L 4179 1197 
Q 4352 992 4352 931 
Q 4352 870 4285 857 
Q 4218 845 2656 774 
L 2662 563 
Q 2662 435 2547 435 
z
M 4154 282 
Q 4154 333 4205 333 
Q 4256 333 4371 275 
Q 4787 58 5280 -96 
L 5331 4378 
L 3066 4230 
Q 2976 4211 2877 4211 
Q 2778 4211 2739 4256 
Q 2701 4301 2621 4419 
Q 2541 4538 2541 4598 
Q 2541 4659 2573 4659 
Q 2579 4659 2588 4652 
Q 2598 4646 2716 4630 
Q 2835 4614 2938 4614 
Q 5446 4774 5545 4774 
Q 5645 4774 5712 4694 
Q 5779 4614 5779 4563 
L 5747 4397 
L 5696 -109 
L 5702 -275 
Q 5702 -461 5590 -557 
Q 5478 -653 5408 -653 
Q 5338 -653 5120 -531 
Q 4902 -410 4528 -106 
Q 4154 198 4154 282 
z
M 1805 3987 
Q 1619 4288 1165 4800 
Q 1114 4864 1114 4931 
Q 1114 4998 1194 5056 
Q 1274 5114 1312 5114 
Q 1408 5114 1914 4550 
Q 2176 4262 2176 4154 
Q 2176 4128 2086 4022 
Q 1997 3917 1926 3917 
Q 1856 3917 1805 3987 
z
M 1165 -486 
Q 1120 -486 1030 -435 
Q 800 -320 800 -166 
Q 800 -128 825 16 
Q 851 160 851 301 
L 851 3430 
Q 851 3584 736 3738 
Q 704 3782 704 3795 
Q 704 3834 803 3834 
Q 902 3834 1049 3782 
Q 1197 3731 1232 3689 
Q 1267 3648 1267 3565 
L 1274 -326 
Q 1274 -486 1165 -486 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-70b9" d="M 1632 1197 
Q 1658 1376 1658 1449 
Q 1658 1523 1645 1613 
L 1523 2560 
Q 1510 2746 1456 2854 
Q 1402 2963 1402 3001 
Q 1402 3040 1494 3040 
Q 1587 3040 1926 2912 
L 2880 2963 
L 2886 4717 
Q 2886 4845 2838 4931 
Q 2790 5018 2790 5053 
Q 2790 5088 2860 5088 
Q 2931 5088 3034 5069 
Q 3309 5018 3309 4864 
L 3290 4077 
L 4538 4147 
Q 4659 4154 4729 4186 
Q 4800 4218 4854 4218 
Q 4909 4218 4979 4166 
Q 5158 4045 5158 3917 
Q 5158 3827 4992 3814 
L 3283 3725 
L 3270 2989 
L 4499 3053 
L 4576 3053 
Q 4704 3053 4790 2979 
Q 4877 2906 4877 2848 
Q 4877 2790 4857 2761 
Q 4838 2733 4832 2707 
L 4659 1683 
Q 4800 1485 4800 1414 
Q 4800 1344 4745 1337 
Q 4691 1331 4608 1325 
L 2061 1235 
L 2074 1101 
L 2074 1069 
Q 2074 941 1952 941 
Q 1818 941 1725 1024 
Q 1632 1107 1632 1197 
z
M 4416 2701 
L 1926 2573 
L 2022 1587 
L 4269 1677 
L 4416 2701 
z
M 6029 -461 
Q 5805 -704 5677 -506 
Q 5235 96 4723 570 
Q 4646 646 4646 691 
Q 4646 736 4716 819 
Q 4787 902 4835 902 
Q 4883 902 5020 790 
Q 5158 678 5337 505 
Q 5517 333 5686 153 
Q 5856 -26 5968 -163 
Q 6080 -301 6080 -349 
Q 6080 -397 6029 -461 
z
M 4045 -352 
Q 3725 173 3347 576 
Q 3277 659 3277 698 
Q 3277 762 3357 826 
Q 3437 890 3481 890 
Q 3526 890 3638 784 
Q 3750 678 3888 518 
Q 4026 358 4157 195 
Q 4288 32 4374 -89 
Q 4461 -211 4461 -246 
Q 4461 -282 4361 -374 
Q 4262 -467 4188 -467 
Q 4115 -467 4045 -352 
z
M 486 -230 
Q 973 256 1210 755 
Q 1254 858 1331 858 
Q 1408 858 1491 810 
Q 1574 762 1574 698 
Q 1574 634 1497 486 
Q 1421 339 1305 153 
Q 1190 -32 1065 -208 
Q 941 -384 838 -499 
Q 736 -614 685 -614 
Q 634 -614 528 -531 
Q 422 -448 422 -371 
Q 422 -294 486 -230 
z
M 2611 -326 
Q 2381 205 2131 538 
Q 2067 621 2067 665 
Q 2067 710 2144 771 
Q 2221 832 2272 832 
Q 2323 832 2412 733 
Q 2502 634 2608 486 
Q 2714 339 2813 182 
Q 2912 26 2973 -92 
Q 3034 -211 3034 -249 
Q 3034 -288 2938 -368 
Q 2842 -448 2755 -448 
Q 2669 -448 2611 -326 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-6d4b"/>
      <use xlink:href="#LXGWWenKai-Regular-91cf" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-65f6" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-95f4" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-70b9" x="399.999939"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_9">
     <g id="line2d_33">
      <path d="M 582.83 389.903697 
L 1069.2 389.903697 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="389.903697" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_23">
      <!-- 30 -->
      <g transform="translate(566.23 392.731822) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_10">
     <g id="line2d_35">
      <path d="M 582.83 321.775277 
L 1069.2 321.775277 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="321.775277" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_24">
      <!-- 35 -->
      <g transform="translate(566.23 324.603402) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_37">
      <path d="M 582.83 253.646857 
L 1069.2 253.646857 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="253.646857" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 40 -->
      <g transform="translate(566.23 256.474982) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_39">
      <path d="M 582.83 185.518436 
L 1069.2 185.518436 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="185.518436" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 45 -->
      <g transform="translate(566.23 188.346561) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_41">
      <path d="M 582.83 117.390016 
L 1069.2 117.390016 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="117.390016" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 50 -->
      <g transform="translate(566.23 120.218141) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_43">
      <path d="M 582.83 49.261596 
L 1069.2 49.261596 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="49.261596" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 55 -->
      <g transform="translate(566.23 52.089721) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_29">
     <!-- 状态焦虑分数 -->
     <g transform="translate(560.509687 241.985937) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-72b6" d="M 4294 4653 
Q 4294 3642 4205 2886 
L 5293 2938 
Q 5427 2950 5507 2988 
Q 5587 3027 5635 3027 
Q 5683 3027 5763 2963 
Q 5843 2899 5900 2819 
Q 5958 2739 5958 2701 
Q 5958 2624 5805 2611 
L 4403 2534 
Q 5056 762 6266 -122 
Q 6330 -166 6330 -198 
Q 6330 -230 6272 -294 
Q 6125 -461 6010 -461 
Q 5965 -461 5926 -422 
Q 4678 621 4122 2291 
L 4045 1920 
Q 3859 1107 3366 448 
Q 2970 -90 2586 -346 
Q 2432 -448 2381 -448 
Q 2330 -448 2330 -397 
Q 2330 -346 2445 -224 
Q 3507 858 3770 2502 
L 3053 2464 
L 2906 2458 
Q 2739 2458 2675 2509 
Q 2490 2688 2490 2829 
Q 2490 2861 2509 2861 
Q 2547 2861 2630 2838 
Q 2714 2816 2816 2816 
L 3814 2867 
Q 3878 3443 3878 4403 
L 3878 4710 
Q 3878 4870 3820 4963 
Q 3763 5056 3763 5088 
Q 3763 5120 3840 5120 
Q 3917 5120 4067 5059 
Q 4218 4998 4256 4960 
Q 4294 4922 4294 4851 
L 4294 4653 
z
M 1837 1760 
Q 1402 1267 954 883 
Q 813 755 745 755 
Q 678 755 585 796 
Q 493 838 384 940 
Q 275 1043 275 1084 
Q 275 1126 320 1133 
Q 794 1203 1837 2074 
L 1875 4563 
Q 1875 4794 1814 4880 
Q 1754 4966 1754 4995 
Q 1754 5024 1830 5024 
Q 1907 5024 2054 4976 
Q 2202 4928 2240 4883 
Q 2278 4838 2278 4749 
L 2208 -416 
Q 2208 -595 2099 -595 
Q 2048 -595 1965 -556 
Q 1882 -518 1821 -451 
Q 1760 -384 1760 -326 
Q 1760 -269 1779 -176 
Q 1798 -83 1811 230 
L 1837 1760 
z
M 5056 3270 
Q 4838 3731 4563 4070 
Q 4531 4115 4531 4166 
Q 4531 4218 4630 4282 
Q 4730 4346 4774 4346 
Q 4819 4346 4902 4259 
Q 4986 4173 5078 4041 
Q 5171 3910 5254 3776 
Q 5338 3642 5392 3542 
Q 5446 3443 5446 3401 
Q 5446 3360 5356 3270 
Q 5267 3181 5180 3181 
Q 5094 3181 5056 3270 
z
M 1312 2432 
Q 1235 2432 1197 2534 
Q 941 3213 653 3686 
Q 627 3718 627 3766 
Q 627 3814 704 3872 
Q 781 3930 857 3930 
Q 934 3930 979 3853 
Q 1312 3328 1449 3001 
Q 1587 2675 1587 2627 
Q 1587 2579 1523 2531 
Q 1459 2483 1392 2457 
Q 1325 2432 1312 2432 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6001" d="M 3354 2125 
Q 3040 2522 2726 2771 
Q 2630 2854 2630 2908 
Q 2630 2963 2704 3030 
Q 2778 3098 2813 3098 
Q 2848 3098 3014 2982 
Q 3181 2867 3469 2592 
Q 3757 2317 3757 2246 
Q 3757 2176 3664 2083 
Q 3571 1990 3520 1990 
Q 3469 1990 3354 2125 
z
M 1075 288 
Q 973 45 893 -115 
Q 813 -275 742 -275 
Q 736 -275 666 -250 
Q 461 -173 461 -51 
Q 461 0 573 182 
Q 685 365 829 707 
Q 973 1050 1062 1389 
Q 1094 1498 1171 1498 
Q 1222 1498 1324 1466 
Q 1427 1434 1427 1357 
Q 1427 1158 1075 288 
z
M 4352 1030 
Q 4429 1030 4518 858 
Q 4698 480 5011 45 
Q 5120 -115 5120 -166 
Q 5120 -218 5075 -288 
Q 4960 -474 4422 -474 
Q 3264 -474 2573 -77 
Q 2138 173 1920 794 
Q 1830 1043 1798 1219 
Q 1766 1395 1766 1408 
Q 1766 1549 2010 1549 
Q 2131 1549 2150 1408 
Q 2221 1037 2352 745 
Q 2483 454 2780 281 
Q 3078 109 3465 22 
Q 3853 -64 4214 -64 
Q 4576 -64 4576 -25 
Q 4576 13 4435 413 
Q 4294 813 4294 921 
Q 4294 1030 4352 1030 
z
M 4800 1376 
Q 4717 1446 4717 1504 
Q 4717 1562 4787 1635 
Q 4858 1709 4906 1709 
Q 4954 1709 5082 1613 
Q 5210 1517 5379 1369 
Q 5549 1222 5705 1065 
Q 5862 909 5968 784 
Q 6074 659 6074 614 
Q 6074 570 6029 509 
Q 5984 448 5926 403 
Q 5869 358 5824 358 
Q 5779 358 5516 668 
Q 5254 979 4800 1376 
z
M 3539 704 
Q 3174 1139 2816 1440 
Q 2720 1517 2720 1574 
Q 2720 1632 2781 1702 
Q 2842 1773 2883 1773 
Q 2925 1773 3097 1657 
Q 3270 1542 3577 1241 
Q 3885 941 3885 854 
Q 3885 768 3801 694 
Q 3718 621 3667 621 
Q 3616 621 3539 704 
z
M 5152 4096 
L 5299 4141 
Q 5421 4141 5581 3962 
Q 5638 3891 5638 3834 
Q 5638 3750 5504 3738 
L 3891 3642 
Q 4352 3168 4902 2745 
Q 5453 2323 6246 1933 
Q 6355 1875 6355 1830 
Q 6355 1786 6278 1725 
Q 6202 1664 6115 1619 
Q 6029 1574 5984 1574 
Q 5939 1574 5901 1600 
Q 5062 2048 4476 2547 
Q 3891 3046 3373 3610 
L 2778 3571 
Q 2298 2816 1613 2214 
Q 1082 1741 563 1440 
Q 365 1325 320 1325 
Q 275 1325 275 1382 
Q 275 1440 403 1542 
Q 1626 2528 2266 3546 
L 1248 3482 
L 1133 3475 
Q 979 3475 921 3532 
Q 864 3590 813 3696 
Q 762 3802 762 3840 
Q 762 3878 787 3878 
Q 813 3878 867 3859 
Q 922 3840 1062 3840 
L 1114 3840 
L 2483 3923 
L 2586 4115 
Q 2797 4550 2870 4816 
Q 2944 5082 2944 5107 
L 2925 5248 
Q 2925 5325 2995 5325 
Q 3142 5325 3366 5210 
Q 3462 5158 3462 5081 
Q 3462 5005 3331 4685 
Q 3200 4365 3078 4122 
L 2989 3955 
L 5018 4077 
Q 5094 4083 5152 4096 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-72b6"/>
      <use xlink:href="#LXGWWenKai-Regular-6001" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-5206" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_3">
    <path d="M 650.453636 295.672942 
L 650.453636 256.505378 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 910.544545 128.139996 
L 910.544545 82.594712 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_45">
    <g clip-path="url(#p5af46ba183)">
     <use xlink:href="#m6e78f72c53" x="650.453636" y="295.672942" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="910.544545" y="128.139996" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_46">
    <g clip-path="url(#p5af46ba183)">
     <use xlink:href="#m6e78f72c53" x="650.453636" y="256.505378" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="910.544545" y="82.594712" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_4">
    <path d="M 741.485455 366.531909 
L 741.485455 291.552709 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 1001.576364 275.65936 
L 1001.576364 193.482438 
" clip-path="url(#p5af46ba183)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_47">
    <g clip-path="url(#p5af46ba183)">
     <use xlink:href="#m6e78f72c53" x="741.485455" y="366.531909" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="1001.576364" y="275.65936" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_48">
    <g clip-path="url(#p5af46ba183)">
     <use xlink:href="#m6e78f72c53" x="741.485455" y="291.552709" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="1001.576364" y="193.482438" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_23">
    <path d="M 582.83 398.884375 
L 582.83 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_24">
    <path d="M 1069.2 398.884375 
L 1069.2 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_25">
    <path d="M 582.83 398.884375 
L 1069.2 398.884375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_26">
    <path d="M 582.83 25.0875 
L 1069.2 25.0875 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_30">
    <!-- 高低焦虑组状态焦虑前后变化 -->
    <g transform="translate(761.015 19.0875) scale(0.1 -0.1)">
     <defs>
      <path id="LXGWWenKai-Regular-53d8" d="M 2842 710 
Q 1952 1357 1952 1504 
Q 1952 1568 2035 1635 
Q 2118 1702 2172 1702 
Q 2227 1702 2304 1626 
Q 2714 1254 3174 947 
Q 3706 1382 4077 1869 
L 2010 1754 
Q 1965 1747 1926 1747 
L 1850 1747 
Q 1696 1747 1629 1824 
Q 1562 1901 1523 1993 
Q 1485 2086 1485 2115 
Q 1485 2144 1510 2144 
Q 1536 2144 1609 2128 
Q 1683 2112 1843 2112 
L 1939 2112 
L 4275 2234 
L 4371 2240 
Q 4461 2240 4573 2157 
Q 4685 2074 4685 2000 
Q 4685 1926 4627 1888 
Q 4570 1850 4544 1818 
Q 4070 1190 3507 736 
Q 4576 115 5830 -128 
Q 5965 -154 5965 -198 
L 5914 -282 
Q 5747 -531 5600 -531 
L 5382 -467 
Q 4762 -301 4173 -38 
Q 3584 224 3168 486 
Q 2368 -64 1402 -346 
Q 1024 -454 800 -492 
Q 576 -531 570 -531 
Q 454 -531 454 -486 
Q 454 -416 678 -333 
Q 1280 -109 1821 134 
Q 2362 378 2842 710 
z
M 5376 2586 
Q 4941 3002 4371 3392 
Q 4288 3450 4288 3491 
Q 4288 3533 4349 3625 
Q 4410 3718 4464 3718 
Q 4518 3718 4768 3564 
Q 5018 3411 5414 3094 
Q 5811 2778 5811 2688 
Q 5811 2630 5731 2534 
Q 5651 2438 5590 2438 
Q 5530 2438 5376 2586 
z
M 2003 3552 
Q 2074 3469 2074 3424 
Q 2074 3322 1626 2922 
Q 1178 2522 794 2330 
Q 678 2272 627 2272 
Q 576 2272 576 2304 
Q 576 2355 698 2458 
Q 1587 3245 1638 3648 
Q 1645 3750 1709 3750 
Q 1811 3750 2003 3552 
z
M 2931 5171 
Q 3398 5171 3398 5018 
L 3398 4390 
L 5094 4493 
Q 5306 4512 5370 4531 
Q 5434 4550 5485 4550 
Q 5536 4550 5626 4499 
Q 5837 4371 5837 4268 
Q 5837 4166 5728 4154 
L 4000 4058 
L 3930 4051 
L 3930 2573 
Q 3930 2381 3808 2381 
Q 3750 2381 3651 2441 
Q 3552 2502 3504 2550 
Q 3456 2598 3456 2669 
Q 3507 3021 3507 3238 
L 3507 4026 
L 2938 3994 
L 2803 3987 
L 2803 2547 
Q 2803 2355 2682 2355 
Q 2624 2355 2525 2416 
Q 2426 2477 2378 2525 
Q 2330 2573 2330 2643 
Q 2381 2893 2381 3213 
L 2381 3968 
L 1216 3898 
Q 1088 3885 960 3885 
Q 832 3885 761 3955 
Q 691 4026 646 4118 
Q 602 4211 602 4246 
Q 602 4282 646 4282 
Q 832 4250 986 4250 
L 1069 4250 
L 2976 4365 
L 2970 4800 
Q 2970 4960 2918 5027 
Q 2867 5094 2867 5132 
Q 2867 5171 2931 5171 
z
" transform="scale(0.015625)"/>
      <path id="LXGWWenKai-Regular-5316" d="M 1466 2912 
Q 1146 2496 870 2240 
Q 378 1773 262 1773 
Q 224 1773 224 1817 
Q 224 1862 377 2044 
Q 531 2227 886 2707 
Q 1242 3187 1670 3907 
Q 2099 4627 2099 4851 
L 2099 4902 
Q 2099 4922 2093 4947 
L 2093 4966 
Q 2093 5030 2160 5030 
Q 2227 5030 2330 4979 
Q 2579 4838 2579 4736 
Q 2579 4659 2323 4198 
Q 2067 3738 1882 3450 
L 1875 -390 
Q 1875 -525 1747 -525 
Q 1734 -525 1644 -502 
Q 1555 -480 1472 -416 
Q 1389 -352 1389 -278 
Q 1389 -205 1411 -112 
Q 1434 -19 1434 211 
L 1466 2912 
z
M 3309 1811 
Q 2470 1344 2272 1344 
Q 2208 1344 2208 1382 
Q 2208 1446 2419 1574 
Q 2963 1901 3315 2163 
L 3328 4595 
Q 3328 4749 3277 4832 
Q 3226 4915 3226 4944 
Q 3226 4973 3302 4973 
Q 3379 4973 3533 4928 
Q 3763 4877 3763 4742 
L 3750 2496 
Q 4410 3040 4915 3648 
Q 4992 3725 5011 3865 
Q 5030 4006 5068 4006 
Q 5107 4006 5184 3936 
Q 5414 3738 5414 3578 
Q 5414 3533 5389 3507 
Q 4640 2650 3744 2080 
L 3738 544 
Q 3738 326 3795 256 
Q 3853 186 4019 154 
Q 4346 102 4624 102 
Q 4902 102 5350 166 
Q 5466 186 5542 282 
Q 5683 461 5805 1306 
Q 5824 1453 5862 1523 
Q 5901 1594 5933 1594 
Q 6054 1594 6054 960 
Q 6054 634 6029 371 
Q 5971 -179 5491 -230 
Q 4998 -288 4537 -288 
Q 4077 -288 3789 -221 
Q 3501 -154 3401 6 
Q 3302 166 3302 454 
L 3309 1811 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-72b6" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-6001" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-524d" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-540e" x="999.999847"/>
     <use xlink:href="#LXGWWenKai-Regular-53d8" x="1099.999832"/>
     <use xlink:href="#LXGWWenKai-Regular-5316" x="1199.999817"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_27">
     <path d="M 1006 54.6725 
L 1063.6 54.6725 
Q 1065.2 54.6725 1065.2 53.0725 
L 1065.2 30.6875 
Q 1065.2 29.0875 1063.6 29.0875 
L 1006 29.0875 
Q 1004.4 29.0875 1004.4 30.6875 
L 1004.4 53.0725 
Q 1004.4 54.6725 1006 54.6725 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_28">
     <path d="M 1007.6 38.29625 
L 1023.6 38.29625 
L 1023.6 32.69625 
L 1007.6 32.69625 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_31">
     <!-- 高焦虑组 -->
     <g transform="translate(1030 38.29625) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
    <g id="patch_29">
     <path d="M 1007.6 50.09625 
L 1023.6 50.09625 
L 1023.6 44.49625 
L 1007.6 44.49625 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_32">
     <!-- 低焦虑组 -->
     <g transform="translate(1030 50.09625) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_30">
    <path d="M 48.17 825.47125 
L 534.54 825.47125 
L 534.54 451.674375 
L 48.17 451.674375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_31">
    <path d="M 70.277727 1710.321158 
L 127.594057 1710.321158 
L 127.594057 651.33326 
L 70.277727 651.33326 
z
" clip-path="url(#p9a72134147)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_32">
    <path d="M 234.03867 1710.321158 
L 291.355 1710.321158 
L 291.355 640.803551 
L 234.03867 640.803551 
z
" clip-path="url(#p9a72134147)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_33">
    <path d="M 397.799613 1710.321158 
L 455.115943 1710.321158 
L 455.115943 714.511515 
L 397.799613 714.511515 
z
" clip-path="url(#p9a72134147)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_34">
    <path d="M 127.594057 1710.321158 
L 184.910387 1710.321158 
L 184.910387 694.25436 
L 127.594057 694.25436 
z
" clip-path="url(#p9a72134147)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_35">
    <path d="M 291.355 1710.321158 
L 348.67133 1710.321158 
L 348.67133 636.290818 
L 291.355 636.290818 
z
" clip-path="url(#p9a72134147)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_36">
    <path d="M 455.115943 1710.321158 
L 512.432273 1710.321158 
L 512.432273 663.567779 
L 455.115943 663.567779 
z
" clip-path="url(#p9a72134147)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_7">
     <g id="line2d_49">
      <path d="M 127.594057 825.47125 
L 127.594057 451.674375 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mf98408d2ab" x="127.594057" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 第一次刺激 -->
      <g transform="translate(107.594057 838.4875) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-7b2c" d="M 3904 5069 
L 3910 5146 
Q 3923 5248 3984 5248 
Q 4045 5248 4131 5190 
Q 4218 5133 4288 5062 
Q 4358 4992 4358 4947 
Q 4358 4902 4333 4870 
L 4147 4576 
L 5325 4666 
Q 5485 4685 5542 4704 
Q 5600 4723 5629 4723 
Q 5658 4723 5728 4685 
Q 5920 4570 5920 4454 
Q 5920 4384 5786 4371 
L 4550 4288 
Q 5114 3923 5114 3821 
Q 5114 3776 5046 3683 
Q 4979 3590 4918 3590 
Q 4858 3590 4755 3692 
Q 4653 3795 4397 3987 
Q 4301 4058 4301 4118 
Q 4301 4179 4397 4275 
L 3904 4243 
Q 3590 3866 3258 3616 
Q 3110 3507 3056 3507 
Q 3002 3507 3002 3546 
Q 3002 3603 3162 3779 
Q 3322 3955 3494 4218 
Q 3840 4730 3904 5069 
z
M 1773 5152 
Q 1850 5152 2000 5037 
Q 2150 4922 2150 4858 
Q 2150 4794 1907 4474 
L 2867 4538 
Q 2989 4550 3069 4576 
Q 3149 4602 3197 4602 
Q 3245 4602 3315 4550 
Q 3386 4499 3434 4435 
Q 3482 4371 3482 4339 
Q 3482 4262 3341 4250 
L 2259 4179 
Q 2342 4128 2585 3945 
Q 2829 3763 2829 3693 
Q 2829 3680 2797 3622 
Q 2714 3469 2618 3469 
Q 2579 3469 2428 3613 
Q 2278 3757 2048 3930 
Q 1978 3981 1978 4032 
Q 1978 4083 2061 4166 
L 1651 4141 
Q 1184 3558 736 3258 
Q 563 3142 515 3142 
Q 467 3142 467 3174 
Q 467 3219 557 3328 
Q 1178 3987 1651 4787 
Q 1709 4890 1709 5011 
L 1709 5062 
Q 1709 5152 1773 5152 
z
M 5280 1824 
L 5370 1830 
Q 5440 1830 5529 1763 
Q 5619 1696 5619 1632 
Q 5619 1568 5593 1542 
Q 5568 1517 5562 1485 
Q 5523 1165 5427 761 
Q 5331 358 5305 243 
Q 5280 128 5190 22 
Q 5101 -83 5021 -83 
Q 4941 -83 4720 13 
Q 4499 109 4102 358 
Q 3706 608 3706 717 
Q 3706 749 3766 749 
Q 3827 749 4166 611 
Q 4506 474 4870 384 
L 4890 384 
Q 4922 384 4928 416 
Q 5114 1037 5178 1485 
L 3398 1382 
L 3392 -525 
Q 3392 -666 3277 -666 
Q 3142 -666 3052 -586 
Q 2963 -506 2963 -454 
L 2963 -384 
Q 2963 -365 2979 -310 
Q 2995 -256 2995 -64 
L 2995 38 
L 3008 1133 
Q 2246 371 1363 -64 
Q 998 -243 755 -323 
Q 512 -403 448 -403 
Q 384 -403 384 -378 
Q 384 -314 595 -198 
Q 1843 474 2694 1344 
L 1638 1280 
Q 1555 1274 1507 1258 
Q 1459 1242 1408 1242 
Q 1357 1242 1277 1296 
Q 1197 1350 1197 1414 
Q 1197 1478 1216 1523 
L 1440 2182 
Q 1472 2266 1472 2368 
L 1453 2573 
L 1453 2611 
Q 1453 2682 1504 2682 
Q 1581 2682 1837 2515 
L 3021 2579 
L 3027 3098 
L 1830 3021 
Q 1792 3014 1754 3014 
L 1690 3014 
Q 1542 3014 1465 3091 
Q 1389 3168 1366 3248 
Q 1344 3328 1344 3350 
Q 1344 3373 1376 3373 
L 1645 3347 
L 1715 3347 
L 4794 3539 
L 4851 3539 
Q 5037 3539 5088 3462 
Q 5139 3386 5139 3354 
Q 5139 3322 5107 3283 
Q 5075 3245 5062 3206 
L 4928 2656 
Q 5082 2483 5082 2428 
Q 5082 2374 5027 2368 
Q 4973 2362 4896 2355 
L 3405 2272 
L 3405 1722 
L 5280 1824 
z
M 4672 3206 
L 3411 3123 
L 3405 2598 
L 4557 2662 
L 4672 3206 
z
M 3014 2246 
L 1805 2176 
L 1632 1619 
L 3008 1696 
L 3014 2246 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-4e00" d="M 704 2451 
L 5357 2688 
Q 5510 2701 5574 2726 
Q 5638 2752 5676 2752 
Q 5715 2752 5804 2697 
Q 5894 2643 5968 2563 
Q 6042 2483 6042 2419 
Q 6042 2317 5894 2304 
L 947 2048 
Q 896 2042 851 2042 
L 774 2042 
Q 646 2042 582 2086 
Q 474 2176 426 2304 
Q 378 2432 378 2464 
Q 378 2496 400 2496 
Q 422 2496 499 2473 
Q 576 2451 704 2451 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6b21" d="M 3315 4672 
Q 3386 4954 3386 4998 
Q 3386 5043 3366 5158 
Q 3366 5229 3437 5229 
Q 3456 5229 3558 5197 
Q 3853 5107 3853 4973 
Q 3853 4870 3702 4435 
Q 3552 4000 3450 3757 
L 5485 3891 
Q 5523 3898 5555 3898 
L 5606 3898 
Q 5709 3898 5795 3827 
Q 5882 3757 5882 3712 
Q 5882 3578 5338 2829 
Q 5011 2406 4896 2406 
Q 4858 2406 4858 2464 
Q 4858 2522 4941 2688 
Q 5222 3206 5338 3514 
L 3290 3392 
Q 2925 2611 2547 2208 
Q 2394 2048 2336 2048 
Q 2298 2048 2298 2105 
Q 2298 2163 2355 2266 
Q 2963 3296 3315 4672 
z
M 1600 3130 
Q 1242 3603 755 4058 
Q 691 4122 691 4179 
Q 691 4237 777 4310 
Q 864 4384 905 4384 
Q 947 4384 1145 4230 
Q 1344 4077 1673 3725 
Q 2003 3373 2003 3280 
Q 2003 3187 1907 3100 
Q 1811 3014 1756 3014 
Q 1702 3014 1600 3130 
z
M 4198 2682 
Q 4173 2368 4096 2061 
Q 4480 1120 5350 397 
Q 5754 70 6163 -186 
Q 6246 -237 6246 -275 
Q 6246 -288 6189 -352 
Q 6029 -531 5913 -531 
Q 5798 -531 5296 -89 
Q 4794 352 4483 726 
Q 4173 1101 3949 1549 
Q 3731 858 3136 320 
Q 2573 -205 1978 -467 
Q 1760 -563 1699 -563 
Q 1638 -563 1638 -499 
Q 1638 -435 1747 -371 
Q 2426 51 2944 621 
Q 3558 1299 3725 2342 
Q 3763 2586 3763 2678 
Q 3763 2771 3731 2841 
Q 3699 2912 3699 2931 
Q 3699 2989 3763 2989 
Q 3827 2989 3930 2963 
Q 4198 2893 4198 2733 
L 4198 2682 
z
M 717 339 
Q 614 358 508 419 
Q 403 480 358 541 
Q 314 602 314 630 
Q 314 659 442 723 
Q 570 787 848 1065 
Q 1126 1344 1914 2310 
Q 1939 2336 2003 2406 
Q 2067 2477 2115 2477 
Q 2163 2477 2163 2409 
Q 2163 2342 2099 2227 
Q 2035 2112 1792 1712 
Q 1549 1312 1197 825 
Q 845 339 717 339 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-523a" d="M 5638 -109 
L 5645 -294 
Q 5645 -474 5539 -563 
Q 5434 -653 5354 -653 
Q 5274 -653 5136 -576 
Q 4998 -499 4841 -380 
Q 4685 -262 4537 -137 
Q 4390 -13 4297 92 
Q 4205 198 4205 243 
Q 4205 288 4262 288 
Q 4320 288 4595 144 
Q 4870 0 5229 -122 
L 5242 4794 
Q 5242 4934 5146 5082 
Q 5120 5126 5120 5164 
Q 5120 5203 5200 5203 
Q 5280 5203 5433 5148 
Q 5587 5094 5619 5052 
Q 5651 5011 5651 4941 
L 5638 -109 
z
M 1869 -371 
Q 1914 -13 1914 198 
L 1920 1632 
Q 1466 902 858 435 
Q 608 243 441 156 
Q 275 70 233 70 
Q 192 70 192 124 
Q 192 179 333 294 
Q 1229 1082 1926 2285 
L 1926 2989 
L 1274 2950 
L 1306 1824 
L 1306 1805 
Q 1306 1715 1216 1715 
Q 1146 1715 1024 1776 
Q 902 1837 902 1946 
Q 902 1984 921 2067 
Q 941 2150 941 2310 
L 941 2387 
L 922 2874 
Q 902 3219 880 3283 
Q 858 3347 848 3369 
Q 838 3392 838 3411 
Q 838 3456 924 3456 
Q 1011 3456 1344 3302 
L 1933 3334 
L 1933 3878 
L 1107 3834 
Q 992 3821 915 3821 
Q 838 3821 736 3865 
Q 634 3910 563 4128 
Q 550 4166 550 4188 
Q 550 4211 585 4211 
Q 621 4211 685 4195 
Q 749 4179 870 4179 
L 947 4179 
L 1939 4237 
L 1939 4736 
Q 1939 4909 1888 4976 
Q 1837 5043 1837 5069 
Q 1837 5120 1949 5120 
Q 2061 5120 2192 5072 
Q 2323 5024 2323 4954 
L 2323 4256 
L 3072 4307 
Q 3232 4314 3299 4336 
Q 3366 4358 3395 4358 
Q 3424 4358 3507 4314 
Q 3738 4192 3738 4070 
Q 3738 3994 3622 3974 
L 2323 3898 
L 2317 3360 
L 3174 3405 
L 3226 3405 
Q 3334 3405 3420 3331 
Q 3507 3258 3507 3197 
Q 3507 3136 3491 3101 
Q 3475 3066 3469 3021 
L 3443 2323 
L 3443 2150 
Q 3443 1952 3340 1865 
Q 3238 1779 3174 1779 
Q 3053 1779 2749 2038 
Q 2445 2298 2445 2413 
Q 2445 2445 2489 2445 
Q 2534 2445 2694 2355 
Q 2854 2266 3059 2195 
L 3091 3053 
L 2317 3008 
L 2317 1837 
Q 2374 1901 2422 1901 
Q 2470 1901 2684 1737 
Q 2899 1574 3270 1203 
Q 3642 832 3642 733 
Q 3642 634 3494 525 
Q 3437 480 3398 480 
Q 3360 480 3213 666 
Q 2861 1107 2317 1581 
L 2310 -454 
Q 2310 -634 2189 -634 
Q 2086 -634 1977 -541 
Q 1869 -448 1869 -371 
z
M 4058 3680 
Q 4058 3834 4006 3917 
Q 3955 4000 3955 4038 
Q 3955 4077 4044 4077 
Q 4134 4077 4250 4026 
Q 4384 3981 4416 3939 
Q 4448 3898 4448 3821 
L 4467 966 
Q 4467 819 4346 819 
Q 4243 819 4137 896 
Q 4032 973 4032 1094 
Q 4032 1139 4048 1232 
Q 4064 1325 4064 1504 
L 4058 3680 
z
" transform="scale(0.015625)"/>
        <path id="LXGWWenKai-Regular-6fc0" d="M 2029 2560 
Q 2054 2739 2054 2778 
L 1984 3910 
Q 1971 4038 1929 4134 
Q 1888 4230 1888 4275 
Q 1888 4320 1968 4320 
Q 2048 4320 2336 4205 
L 2400 4211 
Q 2528 4474 2595 4694 
Q 2662 4915 2662 4986 
L 2637 5146 
Q 2637 5190 2694 5190 
Q 2701 5190 2790 5158 
Q 3078 5075 3078 4941 
Q 3078 4845 2950 4598 
Q 2822 4352 2746 4237 
L 3366 4282 
Q 3398 4288 3424 4288 
L 3507 4288 
Q 3565 4288 3641 4217 
Q 3718 4147 3718 4099 
Q 3718 4051 3702 4025 
Q 3686 4000 3680 3968 
L 3546 2867 
Q 3680 2707 3680 2649 
Q 3680 2592 3632 2585 
Q 3584 2579 3514 2573 
L 2400 2515 
L 2400 2438 
Q 2400 2336 2301 2336 
Q 2202 2336 2115 2409 
Q 2029 2483 2029 2560 
z
M 4941 4794 
Q 4941 4602 4550 3648 
L 5517 3712 
Q 5632 3725 5699 3750 
Q 5766 3776 5798 3776 
Q 5830 3776 5901 3738 
Q 6106 3622 6106 3514 
Q 6106 3430 5978 3418 
L 5587 3392 
Q 5414 2214 5056 1408 
Q 5517 461 6202 -211 
Q 6253 -262 6253 -297 
Q 6253 -333 6195 -384 
Q 6061 -512 5939 -512 
Q 5843 -512 5472 16 
Q 5101 544 4870 1011 
Q 4506 326 3987 -179 
Q 3776 -390 3625 -496 
Q 3475 -602 3430 -602 
Q 3386 -602 3386 -550 
Q 3386 -499 3456 -429 
Q 4192 371 4672 1434 
Q 4390 2086 4192 2880 
Q 4154 2816 4083 2688 
Q 3763 2112 3661 2112 
Q 3622 2112 3622 2176 
Q 3622 2240 3674 2355 
Q 4147 3360 4442 4480 
Q 4506 4730 4506 4816 
Q 4506 4902 4486 4944 
Q 4467 4986 4467 4998 
Q 4467 5050 4531 5050 
Q 4570 5050 4672 5018 
Q 4941 4928 4941 4794 
z
M 1443 4297 
Q 1574 4173 1660 4077 
Q 1747 3981 1747 3945 
Q 1747 3910 1667 3808 
Q 1587 3706 1520 3706 
Q 1453 3706 1357 3821 
Q 1261 3936 1053 4141 
Q 845 4346 717 4442 
Q 589 4538 589 4592 
Q 589 4646 656 4723 
Q 723 4800 771 4800 
Q 819 4800 928 4723 
Q 1037 4646 1174 4534 
Q 1312 4422 1443 4297 
z
M 3315 3987 
L 2330 3917 
L 2349 3526 
L 3277 3584 
L 3315 3987 
z
M 1126 2451 
Q 672 2842 326 3085 
Q 256 3136 256 3184 
Q 256 3232 320 3318 
Q 384 3405 435 3405 
Q 486 3405 736 3245 
Q 986 3085 1389 2758 
Q 1459 2707 1459 2659 
Q 1459 2611 1424 2550 
Q 1389 2490 1337 2438 
Q 1286 2387 1248 2387 
Q 1210 2387 1126 2451 
z
M 4410 3315 
Q 4595 2522 4851 1882 
Q 5069 2426 5178 3366 
L 4410 3315 
z
M 3251 3290 
L 2362 3238 
L 2381 2803 
L 3206 2854 
L 3251 3290 
z
M 2835 1690 
L 2701 1344 
L 3334 1389 
L 3418 1389 
Q 3571 1389 3635 1296 
Q 3699 1203 3699 1174 
Q 3699 1146 3683 1120 
Q 3667 1094 3654 1062 
Q 3501 358 3277 -166 
Q 3232 -275 3123 -339 
Q 3040 -397 2970 -397 
Q 2829 -397 2522 -90 
Q 2221 198 2221 294 
Q 2221 326 2259 326 
Q 2298 326 2467 233 
Q 2637 141 2768 83 
Q 2899 26 2915 26 
Q 2931 26 2950 64 
Q 3168 544 3264 1075 
L 2554 1030 
Q 2304 538 2029 234 
Q 1754 -70 1552 -208 
Q 1350 -346 1308 -346 
Q 1267 -346 1267 -304 
Q 1267 -262 1344 -179 
Q 2080 608 2426 1664 
L 2067 1638 
Q 2029 1632 1997 1632 
L 1901 1632 
Q 1837 1632 1760 1664 
Q 1594 1811 1594 1946 
Q 1594 1971 1619 1971 
Q 1645 1971 1689 1958 
Q 1734 1946 1862 1946 
L 1952 1946 
L 2694 1990 
L 2694 2150 
Q 2694 2291 2640 2358 
Q 2586 2426 2586 2445 
Q 2586 2490 2646 2490 
Q 2707 2490 2803 2470 
Q 3046 2419 3046 2272 
L 3040 2010 
L 3520 2042 
Q 3661 2054 3709 2073 
Q 3757 2093 3808 2093 
Q 3859 2093 3929 2045 
Q 4000 1997 4048 1936 
Q 4096 1875 4096 1843 
Q 4096 1779 3968 1760 
L 2835 1690 
z
M 602 -186 
Q 371 -154 297 -83 
Q 224 -13 224 9 
Q 224 32 282 64 
Q 422 134 934 1114 
Q 1158 1549 1363 2022 
Q 1434 2195 1504 2195 
Q 1542 2195 1542 2121 
Q 1542 2048 1411 1571 
Q 1280 1094 1030 515 
Q 781 -64 739 -125 
Q 698 -186 627 -186 
L 602 -186 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e00" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_51">
      <path d="M 291.355 825.47125 
L 291.355 451.674375 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mf98408d2ab" x="291.355" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 第二次刺激 -->
      <g transform="translate(271.355 838.4875) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-4e8c" d="M 1734 3424 
Q 1683 3418 1638 3418 
L 1536 3418 
Q 1395 3418 1328 3472 
Q 1261 3526 1209 3641 
Q 1158 3757 1158 3805 
Q 1158 3853 1203 3853 
Q 1222 3853 1289 3833 
Q 1357 3814 1523 3814 
L 1606 3814 
L 4301 3974 
Q 4435 3987 4518 4016 
Q 4602 4045 4640 4045 
Q 4678 4045 4768 3987 
Q 4998 3840 4998 3699 
Q 4998 3610 4832 3597 
L 1734 3424 
z
M 691 864 
L 5254 1056 
Q 5427 1069 5520 1097 
Q 5613 1126 5654 1126 
Q 5696 1126 5792 1068 
Q 5888 1011 5965 928 
Q 6042 845 6042 781 
Q 6042 678 5882 666 
L 973 461 
Q 909 454 858 454 
L 768 454 
Q 614 454 569 489 
Q 525 525 429 633 
Q 333 742 333 822 
Q 333 902 368 902 
Q 403 902 473 883 
Q 544 864 691 864 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_53">
      <path d="M 455.115943 825.47125 
L 455.115943 451.674375 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mf98408d2ab" x="455.115943" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 第三次刺激 -->
      <g transform="translate(435.115943 838.4875) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-4e09" d="M 1613 3808 
L 1414 3802 
Q 1267 3802 1200 3862 
Q 1133 3923 1088 4054 
Q 1043 4186 1043 4192 
Q 1043 4237 1088 4237 
Q 1107 4237 1168 4217 
Q 1229 4198 1408 4198 
L 1491 4198 
L 4570 4378 
Q 4704 4390 4787 4419 
Q 4870 4448 4918 4448 
Q 4966 4448 5052 4381 
Q 5139 4314 5203 4234 
Q 5267 4154 5267 4109 
Q 5267 4013 5094 4000 
L 1613 3808 
z
M 2054 2093 
L 1869 2086 
Q 1728 2086 1670 2131 
Q 1626 2176 1555 2288 
Q 1485 2400 1485 2470 
Q 1485 2541 1530 2541 
Q 1549 2541 1625 2518 
Q 1702 2496 1869 2496 
L 1933 2496 
L 4275 2611 
Q 4410 2624 4493 2653 
Q 4576 2682 4617 2682 
Q 4659 2682 4748 2618 
Q 4838 2554 4905 2470 
Q 4973 2387 4973 2330 
Q 4973 2234 4800 2221 
L 2054 2093 
z
M 730 518 
L 5293 672 
Q 5395 678 5523 707 
Q 5651 736 5692 736 
Q 5734 736 5830 678 
Q 5926 621 6003 537 
Q 6080 454 6080 390 
Q 6080 275 5914 262 
L 1005 96 
L 832 90 
Q 653 90 569 157 
Q 486 224 428 339 
Q 371 454 371 505 
Q 371 557 406 557 
Q 442 557 512 537 
Q 582 518 730 518 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e09" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="text_36">
     <!-- 刺激次数 -->
     <g transform="translate(271.355 851.334062) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-523a"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_15">
     <g id="line2d_55">
      <path d="M 48.17 789.723724 
L 534.54 789.723724 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="789.723724" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 36 -->
      <g transform="translate(31.57 792.551849) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-36" d="M 3008 1434 
Q 3008 1696 2886 1917 
Q 2765 2138 2547 2272 
Q 2330 2406 1994 2406 
Q 1658 2406 1395 2249 
Q 1133 2093 995 1833 
Q 858 1574 858 1261 
Q 858 832 1139 557 
Q 1421 282 1939 282 
Q 2458 282 2733 608 
Q 3008 934 3008 1434 
z
M 1107 2445 
Q 1498 2790 2048 2790 
Q 2432 2790 2755 2617 
Q 3078 2445 3270 2141 
Q 3462 1837 3462 1437 
Q 3462 1037 3292 678 
Q 3123 320 2784 105 
Q 2445 -109 1955 -109 
Q 1466 -109 1123 73 
Q 781 256 592 566 
Q 403 877 403 1273 
Q 403 1670 560 2115 
Q 717 2560 1033 3101 
Q 1350 3642 1590 4003 
Q 1830 4365 1849 4413 
Q 1869 4461 1894 4499 
Q 1933 4570 2083 4570 
Q 2234 4570 2320 4525 
Q 2406 4480 2406 4409 
Q 2406 4339 2342 4275 
Q 1888 3757 1568 3241 
Q 1248 2726 1107 2445 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-36" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_57">
      <path d="M 48.17 738.579422 
L 534.54 738.579422 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="738.579422" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 38 -->
      <g transform="translate(31.57 741.407547) scale(0.08 -0.08)">
       <defs>
        <path id="LXGWWenKai-Regular-38" d="M 1811 2304 
Q 1338 1971 1078 1708 
Q 819 1446 819 1126 
Q 819 915 922 723 
Q 1152 294 1798 294 
Q 2317 294 2621 515 
Q 2925 736 2925 1104 
Q 2925 1472 2797 1670 
Q 2669 1869 2422 2009 
Q 2176 2150 1811 2304 
z
M 1773 2739 
Q 2355 3110 2989 3616 
Q 2976 3610 2912 3610 
Q 2848 3610 2790 3699 
Q 2496 4179 1939 4179 
Q 1530 4179 1280 3971 
Q 1030 3763 1030 3504 
Q 1030 3245 1212 3081 
Q 1395 2918 1773 2739 
z
M 3130 3731 
Q 3238 3840 3315 3840 
Q 3418 3840 3418 3738 
Q 3418 3552 2528 2848 
Q 2317 2682 2157 2566 
Q 2752 2342 3069 2006 
Q 3386 1670 3386 1123 
Q 3386 576 2963 233 
Q 2541 -109 1798 -109 
Q 1363 -109 1040 51 
Q 717 211 541 489 
Q 365 768 365 1107 
Q 365 1805 1421 2496 
Q 1011 2714 796 2944 
Q 582 3174 582 3472 
Q 582 3770 748 4013 
Q 915 4256 1219 4406 
Q 1523 4557 1878 4557 
Q 2234 4557 2464 4464 
Q 2694 4371 2848 4236 
Q 3002 4102 3078 3987 
Q 3155 3872 3155 3827 
Q 3155 3782 3130 3731 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#LXGWWenKai-Regular-33"/>
       <use xlink:href="#LXGWWenKai-Regular-38" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_59">
      <path d="M 48.17 687.43512 
L 534.54 687.43512 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="687.43512" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 40 -->
      <g transform="translate(31.57 690.263245) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_61">
      <path d="M 48.17 636.290818 
L 534.54 636.290818 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="636.290818" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 42 -->
      <g transform="translate(31.57 639.118943) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_63">
      <path d="M 48.17 585.146516 
L 534.54 585.146516 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="585.146516" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 44 -->
      <g transform="translate(31.57 587.974641) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-34" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_65">
      <path d="M 48.17 534.002214 
L 534.54 534.002214 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="534.002214" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 46 -->
      <g transform="translate(31.57 536.830339) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-36" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_67">
      <path d="M 48.17 482.857912 
L 534.54 482.857912 
" clip-path="url(#p9a72134147)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#me6e37b1163" x="48.17" y="482.857912" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 48 -->
      <g transform="translate(31.57 485.686037) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-38" x="59.999985"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- 主观成功分数 -->
     <g transform="translate(25.849687 668.572812) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-4e3b" d="M 3712 4102 
Q 3014 4570 2285 4870 
Q 2176 4922 2176 4998 
Q 2176 5075 2224 5148 
Q 2272 5222 2330 5222 
Q 2509 5222 3277 4826 
Q 3686 4627 3898 4499 
Q 4026 4429 4026 4345 
Q 4026 4262 3958 4160 
Q 3891 4058 3833 4058 
Q 3776 4058 3712 4102 
z
M 1491 3334 
L 1293 3328 
Q 1171 3328 1120 3360 
Q 979 3456 947 3571 
Q 915 3686 915 3693 
Q 915 3738 947 3738 
Q 979 3738 1046 3718 
Q 1114 3699 1280 3699 
L 1363 3699 
L 4717 3891 
Q 4864 3904 4941 3929 
Q 5018 3955 5059 3955 
Q 5101 3955 5184 3898 
Q 5408 3750 5408 3635 
Q 5408 3552 5248 3539 
L 3398 3437 
L 3398 2093 
L 4506 2144 
Q 4678 2163 4726 2182 
Q 4774 2202 4812 2202 
Q 4851 2202 4931 2147 
Q 5011 2093 5075 2019 
Q 5139 1946 5139 1894 
Q 5139 1818 4998 1805 
L 3392 1734 
L 3392 237 
L 5338 307 
Q 5498 320 5571 345 
Q 5645 371 5677 371 
Q 5709 371 5798 307 
Q 6054 147 6054 19 
Q 6054 -70 5901 -83 
L 960 -262 
L 742 -269 
Q 506 -269 410 -64 
Q 346 77 346 134 
Q 346 192 390 192 
Q 416 192 512 169 
Q 608 147 742 147 
L 794 147 
L 2950 224 
L 2950 1715 
L 1837 1664 
L 1677 1658 
Q 1542 1658 1475 1715 
Q 1408 1773 1360 1878 
Q 1312 1984 1312 2019 
Q 1312 2054 1344 2054 
Q 1376 2054 1456 2035 
Q 1536 2016 1651 2016 
L 1702 2016 
L 2957 2074 
L 2957 3411 
L 1491 3334 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-6210" d="M 1530 3379 
Q 1530 3098 1498 2598 
L 2579 2675 
Q 2611 2682 2630 2682 
L 2720 2682 
Q 2790 2682 2864 2611 
Q 2938 2541 2938 2489 
Q 2938 2438 2922 2403 
Q 2906 2368 2896 2240 
Q 2886 2112 2816 1561 
Q 2746 1011 2611 608 
Q 2566 474 2464 381 
Q 2362 288 2262 288 
Q 2163 288 2029 429 
Q 1722 749 1581 957 
Q 1440 1165 1440 1209 
Q 1440 1254 1488 1254 
Q 1536 1254 1731 1104 
Q 1926 954 2195 787 
L 2202 781 
L 2208 781 
Q 2259 781 2364 1283 
Q 2470 1786 2509 2330 
L 1472 2259 
Q 1395 1408 1200 902 
Q 1005 397 816 80 
Q 627 -237 473 -416 
Q 320 -595 265 -595 
Q 211 -595 211 -544 
Q 211 -493 256 -410 
Q 960 794 1069 2234 
Q 1114 2842 1114 3222 
Q 1114 3603 1066 3721 
Q 1018 3840 1018 3878 
Q 1018 3917 1088 3917 
Q 1158 3917 1555 3738 
L 3174 3840 
Q 3066 4378 3027 4669 
Q 2989 4960 2960 5011 
Q 2931 5062 2861 5133 
Q 2829 5178 2829 5190 
Q 2829 5248 2931 5248 
Q 3034 5248 3165 5213 
Q 3296 5178 3331 5133 
Q 3366 5088 3417 4701 
Q 3469 4314 3565 3866 
L 4851 3949 
Q 5050 3968 5114 3990 
Q 5178 4013 5226 4013 
Q 5274 4013 5350 3955 
Q 5555 3821 5555 3712 
Q 5555 3642 5414 3629 
L 3642 3514 
Q 3846 2586 4224 1760 
Q 4224 1766 4349 1942 
Q 4474 2118 4608 2381 
Q 4819 2752 4819 2925 
Q 4819 2976 4813 2995 
L 4813 3014 
Q 4813 3091 4873 3091 
Q 4934 3091 5030 3034 
Q 5280 2893 5280 2790 
Q 5280 2714 4989 2221 
Q 4698 1728 4416 1370 
Q 4928 422 5504 -70 
Q 5523 -90 5542 -90 
Q 5562 -90 5574 -58 
Q 5786 486 5907 1120 
Q 5946 1306 6016 1306 
Q 6086 1306 6086 1133 
L 6086 1101 
Q 6074 346 5946 -294 
Q 5888 -608 5670 -608 
Q 5408 -608 5222 -435 
Q 5037 -262 4877 -64 
Q 4435 512 4128 1056 
Q 3667 531 3104 112 
Q 2541 -307 2400 -307 
Q 2349 -307 2349 -269 
Q 2349 -211 2451 -134 
Q 3251 506 3949 1408 
Q 3885 1523 3670 2035 
Q 3456 2547 3251 3488 
L 1530 3379 
z
M 3814 4698 
Q 3725 4749 3725 4797 
Q 3725 4845 3763 4928 
Q 3802 5011 3882 5011 
Q 3962 5011 4218 4873 
Q 4474 4736 4774 4518 
Q 4883 4442 4883 4371 
Q 4883 4358 4857 4291 
Q 4832 4224 4790 4166 
Q 4749 4109 4694 4109 
Q 4640 4109 4422 4294 
Q 4205 4480 3814 4698 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-529f" d="M 5766 3200 
L 5734 3053 
Q 5683 1478 5434 102 
Q 5408 -58 5373 -198 
Q 5338 -339 5222 -454 
Q 5107 -570 4985 -570 
Q 4864 -570 4768 -493 
Q 4672 -416 4518 -294 
Q 3763 269 3763 435 
Q 3763 486 3827 486 
Q 3891 486 4051 393 
Q 4211 301 4531 137 
Q 4851 -26 4899 -26 
Q 4947 -26 4954 19 
Q 5293 1242 5306 3046 
L 4314 2989 
Q 4109 1696 3482 794 
Q 3014 115 2451 -269 
Q 2054 -550 1977 -550 
Q 1901 -550 1901 -499 
Q 1901 -435 2003 -352 
Q 3539 928 3885 2963 
L 3283 2938 
Q 3168 2925 3078 2925 
Q 2989 2925 2938 2957 
Q 2714 3149 2714 3296 
Q 2714 3334 2765 3334 
L 2803 3334 
Q 2931 3315 3066 3315 
L 3149 3315 
L 3949 3354 
Q 4013 3776 4022 4272 
Q 4032 4768 4025 4835 
Q 4019 4902 4000 4953 
Q 3981 5005 3981 5024 
Q 3981 5094 4061 5094 
Q 4141 5094 4237 5069 
Q 4493 4992 4493 4877 
L 4493 4858 
Q 4467 4486 4444 4105 
Q 4422 3725 4378 3373 
L 5402 3437 
L 5491 3437 
Q 5638 3437 5702 3353 
Q 5766 3270 5766 3200 
z
M 486 3731 
L 800 3693 
L 858 3693 
L 2349 3770 
Q 2502 3789 2556 3814 
Q 2611 3840 2656 3840 
Q 2701 3840 2771 3776 
Q 2842 3712 2890 3635 
Q 2938 3558 2938 3526 
Q 2938 3450 2797 3437 
L 1894 3386 
L 1888 1478 
Q 2304 1638 2598 1779 
Q 2893 1920 2963 1920 
Q 3034 1920 3034 1869 
Q 3034 1792 2803 1651 
Q 2310 1357 1539 976 
Q 768 595 643 595 
Q 518 595 441 688 
Q 365 781 326 873 
Q 288 966 288 985 
Q 288 1005 358 1005 
L 403 1005 
Q 570 1005 870 1110 
Q 1171 1216 1478 1325 
L 1478 3360 
L 928 3328 
L 832 3322 
Q 723 3322 636 3370 
Q 550 3418 461 3635 
Q 448 3674 448 3702 
Q 448 3731 486 3731 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-4e3b"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-6210" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-529f" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-5206" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_5">
    <path d="M 98.935892 721.846461 
L 98.935892 580.820059 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 262.696835 695.765101 
L 262.696835 585.842001 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 426.457778 797.21417 
L 426.457778 631.80886 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <g clip-path="url(#p9a72134147)">
     <use xlink:href="#m6e78f72c53" x="98.935892" y="721.846461" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="262.696835" y="695.765101" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="426.457778" y="797.21417" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_70">
    <g clip-path="url(#p9a72134147)">
     <use xlink:href="#m6e78f72c53" x="98.935892" y="580.820059" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="262.696835" y="585.842001" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="426.457778" y="631.80886" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_6">
    <path d="M 156.252222 774.945845 
L 156.252222 613.562876 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 320.013165 740.807931 
L 320.013165 531.773705 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 483.774108 746.241651 
L 483.774108 580.893907 
" clip-path="url(#p9a72134147)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <g clip-path="url(#p9a72134147)">
     <use xlink:href="#m6e78f72c53" x="156.252222" y="774.945845" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="320.013165" y="740.807931" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="483.774108" y="746.241651" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_72">
    <g clip-path="url(#p9a72134147)">
     <use xlink:href="#m6e78f72c53" x="156.252222" y="613.562876" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="320.013165" y="531.773705" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="483.774108" y="580.893907" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_37">
    <path d="M 48.17 825.47125 
L 48.17 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_38">
    <path d="M 534.54 825.47125 
L 534.54 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_39">
    <path d="M 48.17 825.47125 
L 534.54 825.47125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_40">
    <path d="M 48.17 451.674375 
L 534.54 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <!-- 高低焦虑组主观成功变化 -->
    <g transform="translate(236.355 445.674375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-4e3b" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-89c2" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-6210" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-529f" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-53d8" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-5316" x="999.999847"/>
    </g>
   </g>
   <g id="legend_3">
    <g id="patch_41">
     <path d="M 471.34 481.259375 
L 528.94 481.259375 
Q 530.54 481.259375 530.54 479.659375 
L 530.54 457.274375 
Q 530.54 455.674375 528.94 455.674375 
L 471.34 455.674375 
Q 469.74 455.674375 469.74 457.274375 
L 469.74 479.659375 
Q 469.74 481.259375 471.34 481.259375 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_42">
     <path d="M 472.94 464.883125 
L 488.94 464.883125 
L 488.94 459.283125 
L 472.94 459.283125 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_46">
     <!-- 高焦虑组 -->
     <g transform="translate(495.34 464.883125) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
    <g id="patch_43">
     <path d="M 472.94 476.683125 
L 488.94 476.683125 
L 488.94 471.083125 
L 472.94 471.083125 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_47">
     <!-- 低焦虑组 -->
     <g transform="translate(495.34 476.683125) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_4">
   <g id="patch_44">
    <path d="M 582.83 825.47125 
L 1069.2 825.47125 
L 1069.2 451.674375 
L 582.83 451.674375 
z
" style="fill: #ffffff"/>
   </g>
   <g id="patch_45">
    <path d="M 604.937727 1602.916705 
L 662.254057 1602.916705 
L 662.254057 633.385795 
L 604.937727 633.385795 
z
" clip-path="url(#p2c04200e8b)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_46">
    <path d="M 768.69867 1602.916705 
L 826.015 1602.916705 
L 826.015 690.970055 
L 768.69867 690.970055 
z
" clip-path="url(#p2c04200e8b)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_47">
    <path d="M 932.459613 1602.916705 
L 989.775943 1602.916705 
L 989.775943 723.875347 
L 932.459613 723.875347 
z
" clip-path="url(#p2c04200e8b)" style="fill: #ff0000; opacity: 0.7"/>
   </g>
   <g id="patch_48">
    <path d="M 662.254057 1602.916705 
L 719.570387 1602.916705 
L 719.570387 683.918921 
L 662.254057 683.918921 
z
" clip-path="url(#p2c04200e8b)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_49">
    <path d="M 826.015 1602.916705 
L 883.33133 1602.916705 
L 883.33133 710.556538 
L 826.015 710.556538 
z
" clip-path="url(#p2c04200e8b)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="patch_50">
    <path d="M 989.775943 1602.916705 
L 1047.092273 1602.916705 
L 1047.092273 619.988641 
L 989.775943 619.988641 
z
" clip-path="url(#p2c04200e8b)" style="fill: #0000ff; opacity: 0.7"/>
   </g>
   <g id="matplotlib.axis_7">
    <g id="xtick_10">
     <g id="line2d_73">
      <path d="M 662.254057 825.47125 
L 662.254057 451.674375 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_74">
      <g>
       <use xlink:href="#mf98408d2ab" x="662.254057" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 第一次刺激 -->
      <g transform="translate(642.254057 838.4875) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e00" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_75">
      <path d="M 826.015 825.47125 
L 826.015 451.674375 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_76">
      <g>
       <use xlink:href="#mf98408d2ab" x="826.015" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 第二次刺激 -->
      <g transform="translate(806.015 838.4875) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e8c" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_77">
      <path d="M 989.775943 825.47125 
L 989.775943 451.674375 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_78">
      <g>
       <use xlink:href="#mf98408d2ab" x="989.775943" y="825.47125" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 第三次刺激 -->
      <g transform="translate(969.775943 838.4875) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-7b2c"/>
       <use xlink:href="#LXGWWenKai-Regular-4e09" x="99.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-523a" x="299.999954"/>
       <use xlink:href="#LXGWWenKai-Regular-6fc0" x="399.999939"/>
      </g>
     </g>
    </g>
    <g id="text_51">
     <!-- 刺激次数 -->
     <g transform="translate(806.015 851.334062) scale(0.1 -0.1)">
      <use xlink:href="#LXGWWenKai-Regular-523a"/>
      <use xlink:href="#LXGWWenKai-Regular-6fc0" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-6b21" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="299.999954"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_8">
    <g id="ytick_22">
     <g id="line2d_79">
      <path d="M 582.83 803.788197 
L 1069.2 803.788197 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_80">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="803.788197" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 40.0 -->
      <g transform="translate(558.63 806.616322) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_81">
      <path d="M 582.83 753.842666 
L 1069.2 753.842666 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_82">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="753.842666" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 42.5 -->
      <g transform="translate(558.63 756.670791) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_83">
      <path d="M 582.83 703.897134 
L 1069.2 703.897134 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_84">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="703.897134" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 45.0 -->
      <g transform="translate(558.63 706.725259) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_25">
     <g id="line2d_85">
      <path d="M 582.83 653.951602 
L 1069.2 653.951602 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_86">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="653.951602" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 47.5 -->
      <g transform="translate(558.63 656.779727) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-34"/>
       <use xlink:href="#LXGWWenKai-Regular-37" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_26">
     <g id="line2d_87">
      <path d="M 582.83 604.00607 
L 1069.2 604.00607 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_88">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="604.00607" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 50.0 -->
      <g transform="translate(558.63 606.834195) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_27">
     <g id="line2d_89">
      <path d="M 582.83 554.060539 
L 1069.2 554.060539 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_90">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="554.060539" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 52.5 -->
      <g transform="translate(558.63 556.888664) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-32" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_28">
     <g id="line2d_91">
      <path d="M 582.83 504.115007 
L 1069.2 504.115007 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_92">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="504.115007" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 55.0 -->
      <g transform="translate(558.63 506.943132) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-30" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="ytick_29">
     <g id="line2d_93">
      <path d="M 582.83 454.169475 
L 1069.2 454.169475 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke-dasharray: 2.96,1.28; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_94">
      <g>
       <use xlink:href="#me6e37b1163" x="582.83" y="454.169475" style="stroke: #000000; stroke-width: 0.8"/>
      </g>
     </g>
     <g id="text_59">
      <!-- 57.5 -->
      <g transform="translate(558.63 456.9976) scale(0.08 -0.08)">
       <use xlink:href="#LXGWWenKai-Regular-35"/>
       <use xlink:href="#LXGWWenKai-Regular-37" x="59.999985"/>
       <use xlink:href="#LXGWWenKai-Regular-2e" x="119.999969"/>
       <use xlink:href="#LXGWWenKai-Regular-35" x="154.999954"/>
      </g>
     </g>
    </g>
    <g id="text_60">
     <!-- 主观自信分数 -->
     <g transform="translate(552.909687 668.572812) rotate(-90) scale(0.1 -0.1)">
      <defs>
       <path id="LXGWWenKai-Regular-81ea" d="M 4896 339 
Q 5075 115 5075 44 
Q 5075 -26 5024 -38 
Q 4973 -51 4896 -58 
L 1856 -134 
L 1869 -474 
L 1869 -493 
Q 1869 -608 1728 -608 
Q 1587 -608 1484 -525 
Q 1382 -442 1382 -358 
L 1382 -326 
Q 1408 -173 1408 58 
L 1408 154 
L 1306 3584 
Q 1293 3789 1222 3917 
Q 1152 4045 1152 4089 
Q 1152 4134 1238 4134 
Q 1325 4134 1722 3994 
L 2221 4019 
Q 2861 4781 2861 5056 
L 2861 5133 
Q 2861 5152 2854 5171 
L 2854 5190 
Q 2854 5267 2921 5267 
Q 2989 5267 3098 5216 
Q 3386 5082 3386 4934 
Q 3386 4845 3136 4544 
Q 2886 4243 2694 4045 
L 4666 4154 
L 4717 4154 
Q 4915 4154 5020 4070 
Q 5126 3987 5126 3923 
Q 5126 3859 5104 3820 
Q 5082 3782 5075 3750 
L 4896 339 
z
M 4627 3763 
L 1734 3610 
L 1760 2771 
L 4589 2906 
L 4627 3763 
z
M 4576 2534 
L 1773 2400 
L 1805 1562 
L 4538 1683 
L 4576 2534 
z
M 4525 1318 
L 1811 1197 
L 1843 243 
L 4480 314 
L 4525 1318 
z
" transform="scale(0.015625)"/>
       <path id="LXGWWenKai-Regular-4fe1" d="M 262 2003 
Q 890 2822 1510 4051 
Q 1850 4717 1850 4960 
Q 1850 5011 1830 5088 
Q 1830 5152 1926 5152 
Q 2022 5152 2172 5049 
Q 2323 4947 2323 4896 
Q 2323 4794 2086 4291 
Q 1850 3789 1658 3462 
L 1658 -474 
Q 1658 -602 1549 -602 
Q 1542 -602 1459 -579 
Q 1376 -557 1293 -493 
Q 1210 -429 1210 -355 
Q 1210 -282 1229 -198 
Q 1248 -115 1248 96 
L 1274 2886 
Q 1037 2547 768 2259 
Q 499 1971 387 1878 
Q 275 1786 227 1786 
Q 179 1786 179 1840 
Q 179 1894 262 2003 
z
M 3360 4646 
Q 3238 4691 3238 4774 
Q 3238 4813 3258 4870 
Q 3302 4998 3395 4998 
Q 3488 4998 3920 4844 
Q 4352 4691 4541 4608 
Q 4730 4525 4730 4448 
Q 4730 4371 4682 4268 
Q 4634 4166 4576 4166 
Q 4518 4166 4236 4304 
Q 3955 4442 3360 4646 
z
M 2637 3469 
Q 2534 3456 2435 3456 
Q 2336 3456 2233 3510 
Q 2131 3565 2048 3802 
Q 2042 3814 2042 3843 
Q 2042 3872 2070 3872 
Q 2099 3872 2188 3849 
Q 2278 3827 2413 3827 
L 2464 3827 
L 5421 4006 
Q 5549 4013 5616 4041 
Q 5683 4070 5728 4070 
Q 5773 4070 5856 4006 
Q 6061 3846 6061 3750 
Q 6061 3674 5914 3661 
L 2637 3469 
z
M 3200 2650 
L 2989 2643 
Q 2906 2643 2803 2688 
Q 2701 2733 2611 2970 
Q 2605 2982 2605 3011 
Q 2605 3040 2633 3040 
Q 2662 3040 2752 3017 
Q 2842 2995 2976 2995 
L 3027 2995 
L 4819 3091 
Q 4934 3104 5004 3126 
Q 5075 3149 5120 3149 
Q 5165 3149 5248 3098 
Q 5446 2957 5446 2848 
Q 5446 2771 5312 2758 
L 3200 2650 
z
M 3200 1843 
L 2989 1837 
Q 2906 1837 2803 1881 
Q 2701 1926 2611 2163 
Q 2605 2176 2605 2205 
Q 2605 2234 2633 2234 
Q 2662 2234 2752 2211 
Q 2842 2189 2976 2189 
L 3027 2189 
L 4819 2285 
Q 4934 2298 5004 2320 
Q 5075 2342 5116 2342 
Q 5158 2342 5235 2291 
Q 5453 2157 5453 2042 
Q 5453 1971 5312 1952 
L 3200 1843 
z
M 5216 128 
Q 5402 -58 5402 -131 
Q 5402 -205 5341 -214 
Q 5280 -224 5197 -230 
L 3290 -282 
L 3302 -410 
L 3302 -429 
Q 3302 -563 3171 -563 
Q 3040 -563 2941 -480 
Q 2842 -397 2842 -314 
L 2842 -282 
Q 2861 -51 2861 -13 
L 2861 38 
Q 2861 64 2854 90 
L 2765 986 
Q 2746 1165 2694 1267 
Q 2643 1370 2643 1382 
Q 2643 1440 2755 1440 
Q 2867 1440 3155 1325 
L 5075 1402 
Q 5325 1402 5395 1242 
Q 5421 1184 5421 1158 
Q 5421 1133 5405 1104 
Q 5389 1075 5382 1043 
L 5216 128 
z
M 4928 1030 
L 3181 966 
L 3258 96 
L 4806 128 
L 4928 1030 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#LXGWWenKai-Regular-4e3b"/>
      <use xlink:href="#LXGWWenKai-Regular-89c2" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-81ea" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-4fe1" x="299.999954"/>
      <use xlink:href="#LXGWWenKai-Regular-5206" x="399.999939"/>
      <use xlink:href="#LXGWWenKai-Regular-6570" x="499.999924"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_7">
    <path d="M 633.595892 676.762448 
L 633.595892 590.009141 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 797.356835 741.177589 
L 797.356835 640.762521 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 961.117778 784.00553 
L 961.117778 663.745163 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_95">
    <g clip-path="url(#p2c04200e8b)">
     <use xlink:href="#m6e78f72c53" x="633.595892" y="676.762448" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="797.356835" y="741.177589" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="961.117778" y="784.00553" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_96">
    <g clip-path="url(#p2c04200e8b)">
     <use xlink:href="#m6e78f72c53" x="633.595892" y="590.009141" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="797.356835" y="640.762521" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="961.117778" y="663.745163" style="stroke: #000000"/>
    </g>
   </g>
   <g id="LineCollection_8">
    <path d="M 690.912222 738.509791 
L 690.912222 629.328051 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 854.673165 774.441217 
L 854.673165 646.671859 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
    <path d="M 1018.434108 708.203576 
L 1018.434108 531.773705 
" clip-path="url(#p2c04200e8b)" style="fill: none; stroke: #000000; stroke-width: 1.5"/>
   </g>
   <g id="line2d_97">
    <g clip-path="url(#p2c04200e8b)">
     <use xlink:href="#m6e78f72c53" x="690.912222" y="738.509791" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="854.673165" y="774.441217" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="1018.434108" y="708.203576" style="stroke: #000000"/>
    </g>
   </g>
   <g id="line2d_98">
    <g clip-path="url(#p2c04200e8b)">
     <use xlink:href="#m6e78f72c53" x="690.912222" y="629.328051" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="854.673165" y="646.671859" style="stroke: #000000"/>
     <use xlink:href="#m6e78f72c53" x="1018.434108" y="531.773705" style="stroke: #000000"/>
    </g>
   </g>
   <g id="patch_51">
    <path d="M 582.83 825.47125 
L 582.83 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_52">
    <path d="M 1069.2 825.47125 
L 1069.2 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_53">
    <path d="M 582.83 825.47125 
L 1069.2 825.47125 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_54">
    <path d="M 582.83 451.674375 
L 1069.2 451.674375 
" style="fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_61">
    <!-- 高低焦虑组主观自信变化 -->
    <g transform="translate(771.015 445.674375) scale(0.1 -0.1)">
     <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
     <use xlink:href="#LXGWWenKai-Regular-4f4e" x="99.999985"/>
     <use xlink:href="#LXGWWenKai-Regular-7126" x="199.999969"/>
     <use xlink:href="#LXGWWenKai-Regular-8651" x="299.999954"/>
     <use xlink:href="#LXGWWenKai-Regular-7ec4" x="399.999939"/>
     <use xlink:href="#LXGWWenKai-Regular-4e3b" x="499.999924"/>
     <use xlink:href="#LXGWWenKai-Regular-89c2" x="599.999908"/>
     <use xlink:href="#LXGWWenKai-Regular-81ea" x="699.999893"/>
     <use xlink:href="#LXGWWenKai-Regular-4fe1" x="799.999878"/>
     <use xlink:href="#LXGWWenKai-Regular-53d8" x="899.999863"/>
     <use xlink:href="#LXGWWenKai-Regular-5316" x="999.999847"/>
    </g>
   </g>
   <g id="legend_4">
    <g id="patch_55">
     <path d="M 1006 481.259375 
L 1063.6 481.259375 
Q 1065.2 481.259375 1065.2 479.659375 
L 1065.2 457.274375 
Q 1065.2 455.674375 1063.6 455.674375 
L 1006 455.674375 
Q 1004.4 455.674375 1004.4 457.274375 
L 1004.4 479.659375 
Q 1004.4 481.259375 1006 481.259375 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="patch_56">
     <path d="M 1007.6 464.883125 
L 1023.6 464.883125 
L 1023.6 459.283125 
L 1007.6 459.283125 
z
" style="fill: #ff0000; opacity: 0.7"/>
    </g>
    <g id="text_62">
     <!-- 高焦虑组 -->
     <g transform="translate(1030 464.883125) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-9ad8"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
    <g id="patch_57">
     <path d="M 1007.6 476.683125 
L 1023.6 476.683125 
L 1023.6 471.083125 
L 1007.6 471.083125 
z
" style="fill: #0000ff; opacity: 0.7"/>
    </g>
    <g id="text_63">
     <!-- 低焦虑组 -->
     <g transform="translate(1030 476.683125) scale(0.08 -0.08)">
      <use xlink:href="#LXGWWenKai-Regular-4f4e"/>
      <use xlink:href="#LXGWWenKai-Regular-7126" x="99.999985"/>
      <use xlink:href="#LXGWWenKai-Regular-8651" x="199.999969"/>
      <use xlink:href="#LXGWWenKai-Regular-7ec4" x="299.999954"/>
     </g>
    </g>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p6b8b5df46e">
   <rect x="48.17" y="25.0875" width="486.37" height="373.796875"/>
  </clipPath>
  <clipPath id="p5af46ba183">
   <rect x="582.83" y="25.0875" width="486.37" height="373.796875"/>
  </clipPath>
  <clipPath id="p9a72134147">
   <rect x="48.17" y="451.674375" width="486.37" height="373.796875"/>
  </clipPath>
  <clipPath id="p2c04200e8b">
   <rect x="582.83" y="451.674375" width="486.37" height="373.796875"/>
  </clipPath>
 </defs>
</svg>

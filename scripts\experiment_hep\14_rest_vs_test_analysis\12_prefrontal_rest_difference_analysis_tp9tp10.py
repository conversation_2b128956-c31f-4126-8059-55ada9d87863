#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
版本2 - 三阶段静息态差异分析 (rest2-rest1, rest3-rest1, rest3-rest2)

本脚本分析：
1. 静息阶段间的差异波形分析
2. 三条差异曲线：rest2-rest1、rest3-rest1、rest3-rest2
3. 科学验证：确保差异波形符合事件相关电位的时间特征

配色方案：深紫色(rest2-rest1)、深橙色(rest3-rest1)、深青色(rest3-rest2)
"""

import os
import h5py
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import font_manager
from scipy.signal import butter, filtfilt
from scipy.ndimage import gaussian_filter1d
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
if os.path.exists(font_path):
    font_manager.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = 'LXGW Wenkai'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.unicode_minus'] = False

# 定义路径
BASE_DIR = r'D:\ecgeeg\30-数据分析\5-HBA'
DATA_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '13_raw_epochs', 'tp9tp10')
RESULT_DIR = os.path.join(BASE_DIR, 'result', 'hep_analysis', '14_rest_vs_test_analysis', 'tp9tp10')
os.makedirs(RESULT_DIR, exist_ok=True)

# 定义脑区电极分组
BRAIN_REGIONS = {
    '左前额叶区域': ['Fp1', 'AF3', 'AF7', 'F3', 'F5'],
    '右前额叶区域': ['Fp2', 'AF4', 'AF8', 'F4', 'F6'],
    '额中央区域': ['Fpz', 'AFz', 'Fz', 'FCz'],
    '中央区域': ['Cz', 'CPz', 'Pz']
}

# 时间窗口设置
BASELINE_WINDOW = (-0.2, 0.0)  # 基线矫正窗口 -200ms到0ms

# 高对比度差异配色方案
DIFF_COLORS = {
    'Rest2-Rest1': '#4B0082',   # 深紫色
    'Rest3-Rest1': '#FF4500',   # 深橙色
    'Rest3-Rest2': '#008B8B'    # 深青色
}

def load_hep_data(h5_path):
    """加载HEP数据"""
    print(f"正在加载数据: {os.path.basename(h5_path)}")

    with h5py.File(h5_path, 'r') as f:
        data = f['data'][:]
        ch_names = [ch.decode('utf-8').strip() if isinstance(ch, bytes) else str(ch).strip()
                   for ch in f['ch_names'][:]]
        times = f['times'][:]

        if 'subject_ids' in f:
            subject_ids = f['subject_ids'][:]
            subject_ids = [s.decode('utf-8') if isinstance(s, bytes) else str(s)
                          for s in subject_ids]
        else:
            subject_ids = ['unknown'] * data.shape[0]

    sampling_freq = 1 / (times[1] - times[0])
    print(f"  数据形状: {data.shape}")
    print(f"  采样频率: {sampling_freq:.1f} Hz")
    print(f"  时间范围: {times[0]*1000:.1f} 到 {times[-1]*1000:.1f} ms")

    return data, ch_names, times, subject_ids, sampling_freq

def apply_bandpass_filter(data, sampling_freq, low_freq=0.5, high_freq=45):
    """应用带通滤波器"""
    print(f"  应用带通滤波: {low_freq}-{high_freq} Hz")

    nyquist = sampling_freq / 2
    low_norm = max(low_freq / nyquist, 0.001)
    high_norm = min(high_freq / nyquist, 0.999)

    b, a = butter(4, [low_norm, high_norm], btype='band')

    filtered_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            filtered_data[i, j, :] = filtfilt(b, a, data[i, j, :])

    return filtered_data

def apply_gaussian_smoothing(data, sigma=1.5):
    """应用高斯平滑"""
    print(f"  应用高斯平滑，标准差: {sigma}")

    smoothed_data = np.zeros_like(data)
    for i in range(data.shape[0]):
        for j in range(data.shape[1]):
            smoothed_data[i, j, :] = gaussian_filter1d(data[i, j, :], sigma=sigma)

    return smoothed_data

def apply_unified_baseline_correction_three_phases(rest1_data, rest2_data, rest3_data, times):
    """三阶段统一基线矫正"""
    print("执行三阶段统一基线矫正...")

    baseline_mask = (times >= BASELINE_WINDOW[0]) & (times <= BASELINE_WINDOW[1])
    print(f"  基线窗口: {BASELINE_WINDOW[0]*1000:.0f} 到 {BASELINE_WINDOW[1]*1000:.0f} ms")

    rest1_corrected = rest1_data.copy()
    rest2_corrected = rest2_data.copy()
    rest3_corrected = rest3_data.copy()

    n_epochs_rest1, n_channels, _ = rest1_data.shape
    n_epochs_rest2 = rest2_data.shape[0]
    n_epochs_rest3 = rest3_data.shape[0]

    print(f"  处理 Rest1: {n_epochs_rest1} epochs, Rest2: {n_epochs_rest2} epochs, Rest3: {n_epochs_rest3} epochs")

    # 为每个电极计算全局基线参考
    for ch in range(n_channels):
        rest1_baseline_all = rest1_data[:, ch, baseline_mask]
        rest2_baseline_all = rest2_data[:, ch, baseline_mask]
        rest3_baseline_all = rest3_data[:, ch, baseline_mask]

        all_baseline_data = np.concatenate([rest1_baseline_all.flatten(),
                                          rest2_baseline_all.flatten(),
                                          rest3_baseline_all.flatten()])
        global_baseline_mean = np.mean(all_baseline_data)

        # 对每个epoch进行基线矫正
        for epoch in range(n_epochs_rest1):
            epoch_baseline_mean = np.mean(rest1_data[epoch, ch, baseline_mask])
            rest1_corrected[epoch, ch, :] = (rest1_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_rest2):
            epoch_baseline_mean = np.mean(rest2_data[epoch, ch, baseline_mask])
            rest2_corrected[epoch, ch, :] = (rest2_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

        for epoch in range(n_epochs_rest3):
            epoch_baseline_mean = np.mean(rest3_data[epoch, ch, baseline_mask])
            rest3_corrected[epoch, ch, :] = (rest3_data[epoch, ch, :] - epoch_baseline_mean) + global_baseline_mean

    # 验证基线矫正效果
    rest1_final_baseline = np.mean(rest1_corrected[:, :, baseline_mask])
    rest2_final_baseline = np.mean(rest2_corrected[:, :, baseline_mask])
    rest3_final_baseline = np.mean(rest3_corrected[:, :, baseline_mask])

    baseline_diff_12 = abs(rest1_final_baseline - rest2_final_baseline) * 1e6
    baseline_diff_13 = abs(rest1_final_baseline - rest3_final_baseline) * 1e6
    baseline_diff_23 = abs(rest2_final_baseline - rest3_final_baseline) * 1e6

    print(f"  基线矫正验证:")
    print(f"    Rest1基线均值: {rest1_final_baseline*1e6:.3f} μV")
    print(f"    Rest2基线均值: {rest2_final_baseline*1e6:.3f} μV")
    print(f"    Rest3基线均值: {rest3_final_baseline*1e6:.3f} μV")
    print(f"    基线差异 Rest1-Rest2: {baseline_diff_12:.3f} μV")
    print(f"    基线差异 Rest1-Rest3: {baseline_diff_13:.3f} μV")
    print(f"    基线差异 Rest2-Rest3: {baseline_diff_23:.3f} μV")

    max_diff = max(baseline_diff_12, baseline_diff_13, baseline_diff_23)
    if max_diff > 0.1:
        print(f"    ⚠️ 警告: 最大基线差异较大 ({max_diff:.3f} μV > 0.1 μV)")
    else:
        print(f"    ✅ 基线矫正成功，所有差异在可接受范围内")

    return rest1_corrected, rest2_corrected, rest3_corrected

def find_latest_files():
    """查找最新的三个静息阶段数据文件"""
    rest1_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest1_raw_epochs_') and f.endswith('.h5')]
    rest2_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest2_raw_epochs_') and f.endswith('.h5')]
    rest3_files = [f for f in os.listdir(DATA_DIR) if f.startswith('rest3_raw_epochs_') and f.endswith('.h5')]

    if not rest1_files or not rest2_files or not rest3_files:
        missing = []
        if not rest1_files: missing.append('rest1')
        if not rest2_files: missing.append('rest2')
        if not rest3_files: missing.append('rest3')
        raise FileNotFoundError(f"未找到以下数据文件: {', '.join(missing)}")

    rest1_files.sort()
    rest2_files.sort()
    rest3_files.sort()

    rest1_file = os.path.join(DATA_DIR, rest1_files[-1])
    rest2_file = os.path.join(DATA_DIR, rest2_files[-1])
    rest3_file = os.path.join(DATA_DIR, rest3_files[-1])

    print(f"使用的数据文件:")
    print(f"  Rest1: {rest1_files[-1]}")
    print(f"  Rest2: {rest2_files[-1]}")
    print(f"  Rest3: {rest3_files[-1]}")

    return rest1_file, rest2_file, rest3_file

def extract_region_data(data, ch_names, subject_ids, region_electrodes):
    """提取指定区域的电极数据"""
    valid_electrodes = [e for e in region_electrodes if e in ch_names]
    print(f"    实际存在的电极: {valid_electrodes}")

    if not valid_electrodes:
        return None, []

    ch_indices = [ch_names.index(e) for e in valid_electrodes]

    subject_data = {}
    unique_subjects = sorted(set(subject_ids))

    for subj in unique_subjects:
        subj_indices = [i for i, s in enumerate(subject_ids) if s == subj]
        if subj_indices:
            subj_data = data[subj_indices][:, ch_indices, :]
            subject_data[subj] = subj_data

    return subject_data, valid_electrodes

def calculate_region_average(subject_data):
    """计算区域平均波形"""
    if subject_data is None:
        return None

    all_data = []
    for subj_data in subject_data.values():
        subj_avg = np.mean(subj_data, axis=0)  # 对epochs求平均
        region_avg = np.mean(subj_avg, axis=0)  # 对电极求平均
        all_data.append(region_avg)

    grand_avg = np.mean(all_data, axis=0)
    return grand_avg

def calculate_optimal_y_range_differences(region_results):
    """动态计算差异波形的最优Y轴显示范围"""
    all_values = []

    for _, result in region_results.items():
        diff_rest2_rest1 = result['diff_rest2_rest1']
        diff_rest3_rest1 = result['diff_rest3_rest1']
        diff_rest3_rest2 = result['diff_rest3_rest2']

        # 收集三个差异波形的数值（转换为μV）
        all_values.extend(diff_rest2_rest1 * 1e6)
        all_values.extend(diff_rest3_rest1 * 1e6)
        all_values.extend(diff_rest3_rest2 * 1e6)

    if not all_values:
        return (-5, 5)  # 默认差异范围

    # 计算数据范围
    min_val = np.min(all_values)
    max_val = np.max(all_values)

    # 添加15%的边距以确保差异波形完全可见
    range_margin = (max_val - min_val) * 0.15
    y_min = min_val - range_margin
    y_max = max_val + range_margin

    print(f"\n📊 差异波形Y轴范围计算:")
    print(f"  差异最小值: {min_val:.1f} μV")
    print(f"  差异最大值: {max_val:.1f} μV")
    print(f"  显示范围: {y_min:.1f} 到 {y_max:.1f} μV")
    print(f"  范围跨度: {y_max - y_min:.1f} μV")

    return (y_min, y_max)

def create_difference_visualization(region_results, times):
    """创建三阶段静息态差异分析可视化图表"""
    print("\n创建三阶段静息态差异分析可视化图表...")

    # 动态计算最优Y轴范围
    y_min, y_max = calculate_optimal_y_range_differences(region_results)

    # 创建2:1宽高比的图形
    fig = plt.figure(figsize=(16, 8))

    # 设置总标题
    fig.suptitle('HEP三阶段静息态差异分析：Rest2-Rest1, Rest3-Rest1, Rest3-Rest2\n'
                 '双侧乳突参考 (TP9/TP10) | 0.5-45Hz + 高斯平滑',
                 fontsize=14, fontweight='bold', y=0.95)

    # 创建子图布局：2行2列
    gs = fig.add_gridspec(2, 2, hspace=0.35, wspace=0.3)

    # 为每个脑区创建子图
    region_names = list(region_results.keys())

    for i, region_name in enumerate(region_names[:4]):  # 最多显示4个脑区
        result = region_results[region_name]
        diff_rest2_rest1 = result['diff_rest2_rest1']
        diff_rest3_rest1 = result['diff_rest3_rest1']
        diff_rest3_rest2 = result['diff_rest3_rest2']

        # 子图位置
        row = i // 2
        col = i % 2
        ax = fig.add_subplot(gs[row, col])

        # 绘制三条差异波形线
        ax.plot(times * 1000, diff_rest2_rest1 * 1e6,
                color=DIFF_COLORS['Rest2-Rest1'], linewidth=1.0,
                label='Rest2 - Rest1', alpha=0.95)
        ax.plot(times * 1000, diff_rest3_rest1 * 1e6,
                color=DIFF_COLORS['Rest3-Rest1'], linewidth=1.0,
                label='Rest3 - Rest1', alpha=0.95)
        ax.plot(times * 1000, diff_rest3_rest2 * 1e6,
                color=DIFF_COLORS['Rest3-Rest2'], linewidth=1.0,
                label='Rest3 - Rest2', alpha=0.95)

        # 设置坐标轴和标签
        ax.set_xlim(-200, 650)
        ax.set_ylim(y_min, y_max)
        ax.set_xlabel('时间 (ms)', fontsize=10)
        ax.set_ylabel('差异幅值 (μV)', fontsize=10)
        ax.set_title(f'{region_name}\n差异波形分析', fontsize=11, fontweight='bold')

        # 网格和零线
        ax.grid(True, alpha=0.3, linewidth=0.5)
        ax.axhline(0, color='black', linewidth=0.8, alpha=0.8)  # 零线更突出
        ax.axvline(0, color='gray', linewidth=0.5, alpha=0.5, linestyle='--')

        # 图例
        ax.legend(fontsize=9, loc='upper right', framealpha=0.95,
                 fancybox=True, shadow=True, edgecolor='black')

        # 电极信息标注
        electrode_text = f"电极: {', '.join(result['electrodes'])}"
        ax.text(0.02, 0.02, electrode_text, transform=ax.transAxes,
                fontsize=8, verticalalignment='bottom', horizontalalignment='left',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9,
                         edgecolor='gray'))

    plt.tight_layout()

    # 保存图片
    output_file = os.path.join(RESULT_DIR, 'hep_analysis_rest_difference_analysis_tp9tp10.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"三阶段差异分析图表已保存: {output_file}")

    plt.show()
    return fig

def main():
    """主函数：执行三阶段静息态差异分析"""
    print("=" * 60)
    print("版本2 - HEP三阶段静息态差异分析：Rest2-Rest1, Rest3-Rest1, Rest3-Rest2")
    print("双侧乳突参考 (TP9/TP10)")
    print("=" * 60)

    try:
        # 步骤1: 查找并加载数据文件
        print("\n步骤1: 加载数据文件")
        rest1_file, rest2_file, rest3_file = find_latest_files()

        # 加载三个阶段的数据
        rest1_data, ch_names, times, rest1_subject_ids, sampling_freq = load_hep_data(rest1_file)
        rest2_data, _, _, rest2_subject_ids, _ = load_hep_data(rest2_file)
        rest3_data, _, _, rest3_subject_ids, _ = load_hep_data(rest3_file)

        # 步骤2: 数据预处理
        print("\n步骤2: 数据预处理")
        print("  应用带通滤波...")
        rest1_data = apply_bandpass_filter(rest1_data, sampling_freq)
        rest2_data = apply_bandpass_filter(rest2_data, sampling_freq)
        rest3_data = apply_bandpass_filter(rest3_data, sampling_freq)

        # 步骤3: 统一基线矫正
        print("\n步骤3: 统一基线矫正")
        rest1_data, rest2_data, rest3_data = apply_unified_baseline_correction_three_phases(
            rest1_data, rest2_data, rest3_data, times)

        # 步骤4: 信号平滑
        print("\n步骤4: 信号平滑")
        rest1_data = apply_gaussian_smoothing(rest1_data)
        rest2_data = apply_gaussian_smoothing(rest2_data)
        rest3_data = apply_gaussian_smoothing(rest3_data)

        # 步骤5: 脑区分组分析
        print("\n步骤5: 脑区分组分析")
        region_results = {}

        for region_name, electrodes in BRAIN_REGIONS.items():
            print(f"\n分析 {region_name}:")

            # 提取各阶段的区域数据
            rest1_subject_data, valid_electrodes = extract_region_data(
                rest1_data, ch_names, rest1_subject_ids, electrodes)
            rest2_subject_data, _ = extract_region_data(
                rest2_data, ch_names, rest2_subject_ids, electrodes)
            rest3_subject_data, _ = extract_region_data(
                rest3_data, ch_names, rest3_subject_ids, electrodes)

            if rest1_subject_data is not None and rest2_subject_data is not None and rest3_subject_data is not None:
                # 计算区域平均波形
                rest1_avg = calculate_region_average(rest1_subject_data)
                rest2_avg = calculate_region_average(rest2_subject_data)
                rest3_avg = calculate_region_average(rest3_subject_data)

                # 计算差异波形
                diff_rest2_rest1 = rest2_avg - rest1_avg
                diff_rest3_rest1 = rest3_avg - rest1_avg
                diff_rest3_rest2 = rest3_avg - rest2_avg

                region_results[region_name] = {
                    'rest1_avg': rest1_avg,
                    'rest2_avg': rest2_avg,
                    'rest3_avg': rest3_avg,
                    'diff_rest2_rest1': diff_rest2_rest1,
                    'diff_rest3_rest1': diff_rest3_rest1,
                    'diff_rest3_rest2': diff_rest3_rest2,
                    'electrodes': valid_electrodes
                }

                print(f"    ✅ 成功处理 {region_name}")
                print(f"      差异波形计算完成：Rest2-Rest1, Rest3-Rest1, Rest3-Rest2")
            else:
                print(f"    ❌ 跳过 {region_name}（数据不完整）")

        # 步骤6: 创建可视化图表
        if region_results:
            print(f"\n步骤6: 创建可视化图表")
            fig = create_difference_visualization(region_results, times)

            print(f"\n✅ 版本2 - 三阶段静息态差异分析完成！")
            print(f"   处理的脑区数量: {len(region_results)}")
            print(f"   时间窗口: -200 到 650 ms")
            print(f"   滤波设置: 0.5-45 Hz + 高斯平滑")
            print(f"   差异波形: Rest2-Rest1, Rest3-Rest1, Rest3-Rest2")
            print(f"   配色方案: Rest2-Rest1(深紫色), Rest3-Rest1(深橙色), Rest3-Rest2(深青色)")
        else:
            print("❌ 未能处理任何脑区数据")

    except Exception as e:
        print(f"❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

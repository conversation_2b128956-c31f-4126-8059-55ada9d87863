"""
分析Autoformer和CausalFormer模型结构，并比较它们的异同
"""
import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加Autoformer和CausalFormer到路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(os.path.dirname(script_dir))
autoformer_dir = os.path.join(root_dir, "Autoformer")
causalformer_dir = os.path.join(root_dir, "scripts", "experiment_timecausal", "CausalFormer")

sys.path.append(autoformer_dir)
sys.path.append(causalformer_dir)

# 导入Autoformer模型
try:
    from models.Autoformer import Model as AutoformerModel
    from layers.AutoCorrelation import AutoCorrelation
    from layers.Autoformer_EncDec import series_decomp
    autoformer_available = True
except ImportError:
    print("警告: 无法导入Autoformer模型，请确保路径正确")
    autoformer_available = False

# 导入CausalFormer模型
try:
    from models.causal_former import PredictModel as CausalFormerModel
    causalformer_available = True
except ImportError:
    print("警告: 无法导入CausalFormer模型，请确保路径正确")
    causalformer_available = False

def create_autoformer_model():
    """创建Autoformer模型实例"""
    if not autoformer_available:
        return None
    
    # 创建一个简单的配置
    class Args:
        def __init__(self):
            self.enc_in = 7  # 输入维度
            self.dec_in = 7  # 解码器输入维度
            self.c_out = 7   # 输出维度
            self.seq_len = 96  # 输入序列长度
            self.label_len = 48  # 标签序列长度
            self.pred_len = 24  # 预测序列长度
            self.factor = 3  # 注意力因子
            self.d_model = 512  # 模型维度
            self.n_heads = 8  # 注意力头数
            self.e_layers = 2  # 编码器层数
            self.d_layers = 1  # 解码器层数
            self.d_ff = 2048  # 前馈网络维度
            self.dropout = 0.05  # Dropout率
            self.embed = 'timeF'  # 时间特征嵌入
            self.activation = 'gelu'  # 激活函数
            self.output_attention = True  # 输出注意力
            self.distil = True  # 是否使用蒸馏
            self.moving_avg = 25  # 移动平均窗口大小
    
    args = Args()
    model = AutoformerModel(args)
    return model

def create_causalformer_model():
    """创建CausalFormer模型实例"""
    if not causalformer_available:
        return None
    
    # CausalFormer参数
    series_num = 7  # 时间序列数量
    input_window = 96  # 输入窗口大小
    feature_dim = 1  # 特征维度
    output_window = 24  # 输出窗口大小
    output_dim = 1  # 输出维度
    d_model = 512  # 模型维度
    n_head = 8  # 注意力头数
    n_layers = 2  # 层数
    ffn_hidden = 2048  # 前馈网络隐藏层大小
    drop_prob = 0.05  # Dropout率
    tau = 1  # 温度参数
    device = torch.device('cpu')  # 设备
    
    model = CausalFormerModel(
        series_num, input_window, feature_dim, output_window, output_dim,
        d_model, n_head, n_layers, ffn_hidden, drop_prob, tau, device
    )
    return model

def analyze_model_structure(model, model_name):
    """分析模型结构"""
    if model is None:
        print(f"{model_name} 模型不可用")
        return None
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # 获取模型结构
    model_structure = str(model)
    
    # 分析模型组件
    components = {}
    for name, module in model.named_modules():
        if name:  # 跳过根模块
            module_type = type(module).__name__
            if module_type in components:
                components[module_type] += 1
            else:
                components[module_type] = 1
    
    # 创建结果字典
    result = {
        'name': model_name,
        'total_params': total_params,
        'trainable_params': trainable_params,
        'structure': model_structure,
        'components': components
    }
    
    return result

def compare_models(autoformer_result, causalformer_result):
    """比较两个模型的结构"""
    if autoformer_result is None or causalformer_result is None:
        print("无法比较模型，至少有一个模型不可用")
        return
    
    # 打印基本信息
    print("\n===== 模型比较 =====")
    print(f"Autoformer 参数数量: {autoformer_result['total_params']:,}")
    print(f"CausalFormer 参数数量: {causalformer_result['total_params']:,}")
    
    # 比较组件
    print("\n组件比较:")
    all_components = set(list(autoformer_result['components'].keys()) + 
                         list(causalformer_result['components'].keys()))
    
    comparison = []
    for comp in sorted(all_components):
        auto_count = autoformer_result['components'].get(comp, 0)
        causal_count = causalformer_result['components'].get(comp, 0)
        comparison.append({
            'component': comp,
            'autoformer': auto_count,
            'causalformer': causal_count
        })
        print(f"{comp}: Autoformer={auto_count}, CausalFormer={causal_count}")
    
    # 分析关键差异
    print("\n关键差异:")
    
    # 1. 注意力机制
    print("1. 注意力机制:")
    print("   Autoformer: 使用自相关(Auto-Correlation)机制，通过FFT计算时间序列的周期性模式")
    print("   CausalFormer: 使用多变量因果注意力机制，引入可学习的掩码矩阵")
    
    # 2. 时间序列分解
    print("2. 时间序列分解:")
    print("   Autoformer: 使用移动平均进行趋势-季节性分解")
    print("   CausalFormer: 没有显式的分解架构")
    
    # 3. 时间延迟处理
    print("3. 时间延迟处理:")
    print("   Autoformer: 使用时间延迟聚合，基于自相关发现的周期性模式")
    print("   CausalFormer: 使用因果卷积层，显式建模不同时间延迟的因果影响")
    
    # 4. 模型目标
    print("4. 模型目标:")
    print("   Autoformer: 专注于长期时间序列预测")
    print("   CausalFormer: 专注于时间序列因果发现")
    
    return comparison

def visualize_comparison(comparison, result_dir):
    """可视化模型比较结果"""
    if not comparison:
        return
    
    # 设置图表样式
    plt.style.use('ggplot')
    
    # 提取数据
    components = [item['component'] for item in comparison]
    autoformer_counts = [item['autoformer'] for item in comparison]
    causalformer_counts = [item['causalformer'] for item in comparison]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 设置条形宽度
    width = 0.35
    x = np.arange(len(components))
    
    # 绘制条形图
    ax.bar(x - width/2, autoformer_counts, width, label='Autoformer', color='skyblue')
    ax.bar(x + width/2, causalformer_counts, width, label='CausalFormer', color='salmon')
    
    # 添加标签和标题
    ax.set_xlabel('组件类型', fontsize=12)
    ax.set_ylabel('数量', fontsize=12)
    ax.set_title('Autoformer与CausalFormer组件比较', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(components, rotation=45, ha='right')
    ax.legend()
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs(result_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%m%d_%H%M%S")
    fig_path = os.path.join(result_dir, f"model_comparison_{timestamp}.png")
    plt.savefig(fig_path, dpi=300)
    print(f"\n可视化结果已保存到: {fig_path}")
    
    plt.close()

def main():
    """主函数"""
    # 创建结果目录
    result_dir = os.path.join(script_dir, "results")
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建并分析模型
    print("创建并分析Autoformer模型...")
    autoformer_model = create_autoformer_model()
    autoformer_result = analyze_model_structure(autoformer_model, "Autoformer")
    
    print("\n创建并分析CausalFormer模型...")
    causalformer_model = create_causalformer_model()
    causalformer_result = analyze_model_structure(causalformer_model, "CausalFormer")
    
    # 比较模型
    comparison = compare_models(autoformer_result, causalformer_result)
    
    # 可视化比较结果
    if comparison:
        visualize_comparison(comparison, result_dir)
    
    print("\n分析完成!")

if __name__ == "__main__":
    main()

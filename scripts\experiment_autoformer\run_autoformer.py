"""
运行Autoformer模型
"""
import os
import subprocess
import argparse

def run_command(command):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        encoding='utf-8',
        errors='replace'
    )

    # 实时打印输出
    for line in process.stdout:
        print(line.strip())

    # 等待命令完成
    process.wait()

    # 检查是否有错误
    if process.returncode != 0:
        print("命令执行失败，错误信息:")
        for line in process.stderr:
            print(line.strip())
        return False

    return True

def run_autoformer(args):
    """运行Autoformer模型"""
    # 设置路径
    autoformer_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "..", "Autoformer")

    # 检查是否存在已训练的模型
    setting = f"{args.model}_{args.data}_{args.des}"
    checkpoint_dir = os.path.join(autoformer_dir, "checkpoints", setting)
    checkpoint_file = os.path.join(checkpoint_dir, "checkpoint.pth")

    # 如果模型已存在且用户选择跳过训练，则直接进行测试
    if os.path.exists(checkpoint_file) and args.skip_training:
        print(f"发现已训练的模型: {checkpoint_file}")
        print("跳过训练阶段，直接进行测试...")

        # 构建测试命令
        command = f"cd {autoformer_dir} && "
        command += f"python -u run.py "
        command += f"--is_training 0 "
        command += f"--model_id {setting} "
        command += f"--model {args.model} "
        command += f"--data {args.data} "
        command += f"--root_path ./data/ "
        command += f"--data_path {args.data}.csv "
        command += f"--features {args.features} "
        command += f"--seq_len {args.seq_len} "
        command += f"--label_len {args.label_len} "
        command += f"--pred_len {args.pred_len} "
        command += f"--e_layers {args.e_layers} "
        command += f"--d_layers {args.d_layers} "
        command += f"--factor {args.factor} "
        command += f"--enc_in {args.enc_in} "
        command += f"--dec_in {args.dec_in} "
        command += f"--c_out {args.c_out} "
        command += f"--des {args.des} "
        command += f"--use_gpu {args.use_gpu} "
        command += f"--gpu {args.gpu} "
    else:
        # 如果模型不存在或用户选择重新训练，则进行训练
        if os.path.exists(checkpoint_file) and not args.skip_training:
            print(f"发现已训练的模型: {checkpoint_file}，但用户选择重新训练")

        # 构建训练命令
        command = f"cd {autoformer_dir} && "
        command += f"python -u run.py "
        command += f"--is_training 1 "
        command += f"--model_id {setting} "
        command += f"--model {args.model} "
        command += f"--data {args.data} "
        command += f"--root_path ./data/ "
        command += f"--data_path {args.data}.csv "
        command += f"--features {args.features} "
        command += f"--seq_len {args.seq_len} "
        command += f"--label_len {args.label_len} "
        command += f"--pred_len {args.pred_len} "
        command += f"--e_layers {args.e_layers} "
        command += f"--d_layers {args.d_layers} "
        command += f"--factor {args.factor} "
        command += f"--enc_in {args.enc_in} "
        command += f"--dec_in {args.dec_in} "
        command += f"--c_out {args.c_out} "
        command += f"--des {args.des} "
        command += f"--itr {args.itr} "
        command += f"--train_epochs {args.train_epochs} "
        command += f"--batch_size {args.batch_size} "
        command += f"--patience {args.patience} "
        command += f"--learning_rate {args.learning_rate} "
        # 确保正确传递GPU参数
        if args.use_gpu.lower() == 'true':
            command += f"--use_gpu 1 "
        else:
            command += f"--use_gpu 0 "
        command += f"--gpu {args.gpu} "

    # 运行命令
    return run_command(command)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行Autoformer模型')

    # 基本参数
    parser.add_argument('--model', type=str, default='Autoformer', help='模型名称')
    parser.add_argument('--data', type=str, default='ETTh1', help='数据集名称: ETTh1, ETTh2, ETTm1, ETTm2, synthetic')
    parser.add_argument('--features', type=str, default='M', help='预测特征: M(多变量), S(单变量)')
    parser.add_argument('--target', type=str, default='OT', help='目标列(用于单变量预测)')

    # 序列长度参数
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--label_len', type=int, default=48, help='标签序列长度')
    parser.add_argument('--pred_len', type=int, default=24, help='预测序列长度')

    # 模型参数
    parser.add_argument('--e_layers', type=int, default=2, help='编码器层数')
    parser.add_argument('--d_layers', type=int, default=1, help='解码器层数')
    parser.add_argument('--factor', type=int, default=3, help='注意力因子')
    parser.add_argument('--enc_in', type=int, default=7, help='编码器输入维度')
    parser.add_argument('--dec_in', type=int, default=7, help='解码器输入维度')
    parser.add_argument('--c_out', type=int, default=7, help='输出维度')

    # 训练参数
    parser.add_argument('--train_epochs', type=int, default=1, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批量大小')
    parser.add_argument('--patience', type=int, default=1, help='早停耐心值')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='学习率')

    # 硬件参数
    parser.add_argument('--use_gpu', type=str, default='True', help='是否使用GPU (True/False)')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')

    # 运行控制参数
    parser.add_argument('--skip_training', action='store_true', help='如果存在已训练的模型，则跳过训练阶段')

    # 其他参数
    parser.add_argument('--des', type=str, default='test', help='实验描述')
    parser.add_argument('--itr', type=int, default=1, help='实验重复次数')

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()

    # 如果使用合成数据集，调整参数
    if args.data == 'synthetic':
        args.enc_in = 7
        args.dec_in = 7
        args.c_out = 7

    # 运行Autoformer
    success = run_autoformer(args)

    if success:
        print("Autoformer运行成功!")
    else:
        print("Autoformer运行失败!")

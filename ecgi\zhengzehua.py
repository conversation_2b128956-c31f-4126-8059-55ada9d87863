import numpy as np
from scipy.linalg import svd

'''1. <PERSON><PERSON><PERSON><PERSON> 正则化'''
def tikhonov_regularization(A, b, alpha):
    """Tikhonov regularization."""
    A_T_A = np.dot(A.T, A)
    A_T_b = np.dot(A.T, b)
    n = A.shape[1]
    I = np.eye(n)
    x = np.linalg.solve(A_T_A + alpha * I, A_T_b)
    return x


'''2. TSVD 正则化'''
def tsvd_regularization(A, b, k):
    """Truncated Singular Value Decomposition (TSVD) regularization."""
    U, s, Vt = svd(A)
    s_inv = np.zeros_like(s)
    s_inv[:k] = 1 / s[:k]  # Inverse of singular values
    A_pseudo = np.dot(Vt.T, np.dot(np.diag(s_inv), U.T))
    x = np.dot(A_pseudo, b)
    return x

'''3. 迭代正则化'''
def iterative_regularization(A, b, alpha, iterations=100):
    """Iterative regularization."""
    x = np.zeros(A.shape[1])
    for _ in range(iterations):
        x = x + np.dot(A.T, (b - np.dot(A, x))) - alpha * x
    return x

'''8. 最小二乘法的正则化'''
def least_squares_regularization(A, b):
    """Least squares regularization."""
    x = np.linalg.lstsq(A, b, rcond=None)[0]
    return x




#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 阶段一：基础模型框架搭建

功能：
- 搭建基础CausalFormer模型框架
- 实现简单的单变量预测功能
- 训练和评估模型

作者：AI助手
日期：2024年
"""

import os
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from datetime import datetime
import sys
import json

# 添加CausalFormer源码路径
causalformer_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "CausalFormer")
sys.path.append(causalformer_path)

# 导入CausalFormer相关模块
try:
    from CausalFormer.model.model import Embedding, CausalSelfAttention, MultiHeadAttention, PositionwiseFeedForward, EncoderLayer, Encoder, PredictModel
except ImportError:
    print("警告: 无法导入CausalFormer模块，将使用简化模型")

# 导入数据处理模块
from stage1_data_processing import load_subject_data, preprocess_data, create_segments, prepare_dataset

# 定义常量
OUTPUT_DIR = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\stage1"

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设置设备
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

class SimpleCausalFormer(nn.Module):
    """
    简化版CausalFormer模型，用于单变量时间序列预测
    """
    def __init__(self, input_dim, d_model=64, n_head=4, n_layers=2, ffn_hidden=128, dropout=0.1):
        super(SimpleCausalFormer, self).__init__()

        self.input_dim = input_dim
        self.d_model = d_model
        self.n_head = n_head
        self.n_layers = n_layers

        # 输入投影
        self.input_projection = nn.Linear(input_dim, d_model)

        # 位置编码
        self.pos_encoder = nn.Parameter(torch.zeros(1, 1000, d_model))  # 最大长度1000

        # 编码器层
        self.encoder_layers = nn.ModuleList([
            nn.TransformerEncoderLayer(d_model=d_model, nhead=n_head, dim_feedforward=ffn_hidden, dropout=dropout)
            for _ in range(n_layers)
        ])

        # 输出投影
        self.output_projection = nn.Linear(d_model, input_dim)

    def forward(self, x):
        # x: [batch_size, seq_len, input_dim]

        # 输入投影
        x = self.input_projection(x)

        # 添加位置编码
        seq_len = x.size(1)
        x = x + self.pos_encoder[:, :seq_len, :]

        # 编码器层
        for layer in self.encoder_layers:
            x = layer(x.transpose(0, 1)).transpose(0, 1)

        # 输出投影
        output = self.output_projection(x)

        return output

def train_model(model, train_loader, val_loader, epochs=10, lr=0.001):
    """
    训练模型

    参数:
    model (nn.Module): 模型
    train_loader (DataLoader): 训练数据加载器
    val_loader (DataLoader): 验证数据加载器
    epochs (int): 训练轮次
    lr (float): 学习率

    返回:
    dict: 训练历史记录
    """
    print("开始训练模型...")

    # 将模型移动到设备
    model = model.to(device)

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)

    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': []
    }

    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0

        for batch in train_loader:
            # 获取数据
            ecg_input = batch['ecg_input'].to(device)
            ecg_target = batch['ecg_target'].to(device)

            # 调整输入形状
            ecg_input = ecg_input.permute(0, 2, 1)  # [batch_size, seq_len, channels]
            ecg_target = ecg_target.permute(0, 2, 1)  # [batch_size, seq_len, channels]

            # 前向传播
            optimizer.zero_grad()
            outputs = model(ecg_input)

            # 计算损失
            loss = criterion(outputs, ecg_target)

            # 反向传播和优化
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

        # 计算平均训练损失
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)

        # 验证阶段
        model.eval()
        val_loss = 0.0

        with torch.no_grad():
            for batch in val_loader:
                # 获取数据
                ecg_input = batch['ecg_input'].to(device)
                ecg_target = batch['ecg_target'].to(device)

                # 调整输入形状
                ecg_input = ecg_input.permute(0, 2, 1)  # [batch_size, seq_len, channels]
                ecg_target = ecg_target.permute(0, 2, 1)  # [batch_size, seq_len, channels]

                # 前向传播
                outputs = model(ecg_input)

                # 计算损失
                loss = criterion(outputs, ecg_target)

                val_loss += loss.item()

        # 计算平均验证损失
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)

        print(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}")

    return history

def evaluate_model(model, val_loader):
    """
    评估模型

    参数:
    model (nn.Module): 模型
    val_loader (DataLoader): 验证数据加载器

    返回:
    float: 验证损失
    """
    print("评估模型...")

    # 将模型移动到设备
    model = model.to(device)

    # 定义损失函数
    criterion = nn.MSELoss()

    # 评估阶段
    model.eval()
    val_loss = 0.0

    predictions = []
    targets = []

    with torch.no_grad():
        for batch in val_loader:
            # 获取数据
            ecg_input = batch['ecg_input'].to(device)
            ecg_target = batch['ecg_target'].to(device)

            # 调整输入形状
            ecg_input = ecg_input.permute(0, 2, 1)  # [batch_size, seq_len, channels]
            ecg_target = ecg_target.permute(0, 2, 1)  # [batch_size, seq_len, channels]

            # 前向传播
            outputs = model(ecg_input)

            # 计算损失
            loss = criterion(outputs, ecg_target)

            val_loss += loss.item()

            # 保存预测和目标
            predictions.append(outputs.cpu().numpy())
            targets.append(ecg_target.cpu().numpy())

    # 计算平均验证损失
    val_loss /= len(val_loader)

    # 合并预测和目标
    predictions = np.concatenate(predictions, axis=0)
    targets = np.concatenate(targets, axis=0)

    return val_loss, predictions, targets

def visualize_results(history, predictions, targets, output_dir=OUTPUT_DIR):
    """
    可视化结果

    参数:
    history (dict): 训练历史记录
    predictions (np.ndarray): 预测值
    targets (np.ndarray): 目标值
    output_dir (str): 输出目录
    """
    print("可视化结果...")

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 绘制损失曲线
    plt.figure(figsize=(10, 5))
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, f"loss_curve_{timestamp}.png"), dpi=300)
    plt.close()

    # 绘制预测vs目标
    plt.figure(figsize=(12, 6))

    # 选择第一个样本的第一个通道进行可视化
    sample_idx = 0
    channel_idx = 0

    pred = predictions[sample_idx, :, channel_idx]
    target = targets[sample_idx, :, channel_idx]

    plt.plot(target, label='Target')
    plt.plot(pred, label='Prediction')
    plt.xlabel('Time Step')
    plt.ylabel('Value')
    plt.title('ECG Prediction vs Target')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, f"prediction_vs_target_{timestamp}.png"), dpi=300)
    plt.close()

    # 保存训练历史记录
    with open(os.path.join(output_dir, f"training_history_{timestamp}.json"), 'w') as f:
        json.dump({
            'train_loss': [float(loss) for loss in history['train_loss']],
            'val_loss': [float(loss) for loss in history['val_loss']]
        }, f)

    print(f"结果已保存至: {output_dir}")

def main():
    """主函数"""
    # 1. 加载数据
    stages_data = load_subject_data()

    if not stages_data:
        print("错误: 加载数据失败")
        return

    # 2. 数据预处理
    key_channels = ['Fz', 'Cz', 'Pz']  # 关键中线导联
    processed_data = preprocess_data(stages_data, key_channels)

    if not processed_data:
        print("错误: 数据预处理失败")
        return

    # 3. 创建数据片段
    segments_data = create_segments(processed_data)

    # 4. 准备数据集
    dataloaders = prepare_dataset(segments_data, batch_size=32, sequence_length=500)

    # 5. 创建模型
    # 获取输入维度
    for batch in dataloaders['train']:
        input_dim = batch['ecg_input'].shape[1]  # 通道数
        break

    model = SimpleCausalFormer(input_dim=input_dim, d_model=64, n_head=4, n_layers=2, ffn_hidden=128, dropout=0.1)
    print(f"创建模型: {model.__class__.__name__}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

    # 6. 训练模型
    history = train_model(model, dataloaders['train'], dataloaders['val'], epochs=10, lr=0.001)

    # 7. 评估模型
    val_loss, predictions, targets = evaluate_model(model, dataloaders['val'])
    print(f"验证损失: {val_loss:.6f}")

    # 8. 可视化结果
    visualize_results(history, predictions, targets)

    # 9. 保存模型
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    torch.save(model.state_dict(), os.path.join(OUTPUT_DIR, f"simple_causalformer_{timestamp}.pth"))

    print("阶段一: 基础模型框架搭建完成")

if __name__ == "__main__":
    main()

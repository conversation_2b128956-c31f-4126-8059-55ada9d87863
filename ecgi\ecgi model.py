import os
import numpy as np
import matplotlib.pyplot as plt
from wfdb import rdrecord

# 定义数据集路径
dataset_path = './challenge2007'

# 1. 检查数据结构是否完整
required_files = ['training-set.txt', 'gold-standard.txt']
for file in required_files:
    if not os.path.exists(os.path.join(dataset_path, file)):
        raise FileNotFoundError(f"缺少必要文件: {file}，请检查数据集结构！")

# 2. 读取训练集记录
training_set_file = os.path.join(dataset_path, 'training-set.txt')
with open(training_set_file, 'r') as f:
    record_names = [line.strip() for line in f if line.strip()]

print(f"找到 {len(record_names)} 条记录:\n{record_names[:5]}")  # 打印前5条记录以验证

# 3. 加载第一条记录进行分析
record_name = record_names[0]  # 示例使用第1条记录
record_path = os.path.join(dataset_path, record_name)

try:
    record = rdrecord(record_path)  # 加载记录文件
except FileNotFoundError:
    raise FileNotFoundError(f"未找到记录文件 {record_path}，请确认路径是否正确！")

# 4. 提取信号数据和元信息
signal = record.p_signal  # ECG信号
fs = record.fs            # 采样频率
channels = record.n_sig   # 导联数

print(f"记录名: {record_name}")
print(f"信号形状: {signal.shape}, 采样频率: {fs} Hz, 导联数: {channels}")

# 5. 可视化原始信号（选择第1导联）
plt.figure(figsize=(12, 6))
plt.plot(signal[:, 0], label="原始信号（导联1）")
plt.title(f"记录 {record_name} 的原始ECG信号")
plt.xlabel("采样点")
plt.ylabel("电压（毫伏）")
plt.legend()
plt.show()

# 6. 简单信号处理：去除直流分量（均值）
processed_signal = signal - np.mean(signal, axis=0)

# 绘制去直流分量后的信号
plt.figure(figsize=(12, 6))
plt.plot(processed_signal[:, 0], label="去直流分量信号（导联1）")
plt.title(f"记录 {record_name} 的处理后信号")
plt.xlabel("采样点")
plt.ylabel("标准化电压")
plt.legend()
plt.show()

# 7. 分析金标准文件
gold_standard_file = os.path.join(dataset_path, 'gold-standard.txt')

if os.path.exists(gold_standard_file):
    with open(gold_standard_file, 'r') as f:
        gold_standard = [line.strip() for line in f]
    print(f"金标准数据 (前5行):\n{gold_standard[:5]}")
else:
    print("金标准文件未找到！")

# 8. 扩展处理（根据研究目标）
# 示例：简单统计信号的振幅范围
min_signal = np.min(signal, axis=0)
max_signal = np.max(signal, axis=0)
print(f"每个导联的信号振幅范围:\n最小值: {min_signal}\n最大值: {max_signal}")

# 示例：计算每个导联的功率谱密度（如需频域分析）
from scipy.signal import welch

frequencies, psd = welch(signal[:, 0], fs=fs)
plt.figure(figsize=(10, 5))
plt.semilogy(frequencies, psd, label="功率谱密度")
plt.title(f"记录 {record_name} 的功率谱密度")
plt.xlabel("频率 (Hz)")
plt.ylabel("功率 (dB)")
plt.legend()
plt.show()
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 压力趋势分析测试脚本

功能：
- 测试压力趋势分析的各个组件
- 验证数据加载、特征提取和模型的正确性
- 使用小数据集进行快速测试

作者：AI助手
日期：2025年5月23日
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset, random_split

# 导入自定义模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pressure_trend_analysis.data_preparation import (
    load_fif_data,
    prepare_stage_data,
    create_pressure_trend_dataset,
    visualize_pressure_pattern,
    PressureDataset,
    STAGE_ORDER,
    PRESSURE_PATTERN
)
from pressure_trend_analysis.pressure_trend_model import (
    PressureTrendModel,
    SelfAttention,
    PressureTrendEncoder
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"pressure_trend_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("PressureTrend-Test")

def test_data_preparation(data_dir, subject_pattern="01_01"):
    """
    测试数据准备模块
    
    参数:
    data_dir (str): 数据目录
    subject_pattern (str): 被试ID模式，如"01_01"
    
    返回:
    bool: 测试是否通过
    """
    logger.info(f"测试数据准备模块: 使用被试 {subject_pattern} 的数据")
    
    try:
        # 测试可视化压力变化模式
        visualize_pattern_path = visualize_pressure_pattern()
        logger.info(f"压力变化模式可视化成功: {visualize_pattern_path}")
        
        # 测试加载数据
        data_by_subject = load_fif_data(data_dir, subject_pattern=subject_pattern)
        if not data_by_subject:
            logger.error(f"未能加载被试 {subject_pattern} 的数据，请检查数据目录和被试ID")
            return False
        
        logger.info(f"成功加载被试数据: {len(data_by_subject)} 个被试")
        
        # 测试准备分阶段数据
        feature_data = prepare_stage_data(data_by_subject, include_prac=False)
        
        for stage in STAGE_ORDER:
            if stage in feature_data:
                for feature_type in ['eeg_time', 'ecg_time', 'eeg_freq', 'ecg_freq']:
                    if len(feature_data[stage][feature_type]) > 0:
                        logger.info(f"阶段 {stage} 的 {feature_type} 特征形状: {feature_data[stage][feature_type].shape}")
                    else:
                        logger.warning(f"阶段 {stage} 的 {feature_type} 特征为空")
        
        # 测试创建压力趋势预测数据集
        sequence_length = 3
        eeg_combined, ecg_combined, pressure_labels, sample_counts = create_pressure_trend_dataset(
            feature_data, sequence_length=sequence_length
        )
        
        logger.info(f"创建数据集成功: EEG特征={eeg_combined.shape}, ECG特征={ecg_combined.shape}, 压力标签={pressure_labels.shape}")
        logger.info(f"各阶段样本数量: {sample_counts}")
        
        # 测试数据集类
        dataset = PressureDataset(
            eeg_combined, 
            ecg_combined, 
            pressure_labels, 
            sequence_length=sequence_length
        )
        
        logger.info(f"创建PressureDataset成功: {len(dataset)} 个样本")
        
        # 检查第一个样本
        features, target = dataset[0]
        logger.info(f"样本特征形状: {features.shape}, 目标形状: {target.shape}")
        
        return True
    
    except Exception as e:
        logger.error(f"数据准备测试失败: {str(e)}")
        return False

def test_model_components():
    """
    测试模型组件
    
    返回:
    bool: 测试是否通过
    """
    logger.info("测试模型组件")
    
    try:
        # 测试自注意力机制
        hidden_dim = 64
        num_heads = 4
        batch_size = 8
        seq_len = 10
        
        attention = SelfAttention(hidden_dim, num_heads)
        x = torch.randn(batch_size, seq_len, hidden_dim)
        output, weights = attention(x)
        
        logger.info(f"自注意力输出形状: {output.shape}, 权重形状: {weights.shape}")
        
        # 测试编码器
        input_dim = 32
        encoder = PressureTrendEncoder(input_dim, hidden_dim, num_layers=2, num_heads=num_heads)
        x = torch.randn(batch_size, seq_len, input_dim)
        output, hidden_states, weights = encoder(x)
        
        logger.info(f"编码器输出形状: {output.shape}, 隐藏状态形状: ({hidden_states[0].shape}, {hidden_states[1].shape})")
        
        # 测试完整模型
        eeg_dim = 20
        ecg_dim = 12
        
        model = PressureTrendModel(eeg_dim, ecg_dim, hidden_dim=hidden_dim)
        eeg_features = torch.randn(batch_size, seq_len, eeg_dim)
        ecg_features = torch.randn(batch_size, seq_len, ecg_dim)
        
        output = model(eeg_features, ecg_features)
        
        logger.info(f"模型输出形状: {output.shape}")
        
        return True
    
    except Exception as e:
        logger.error(f"模型组件测试失败: {str(e)}")
        return False

def test_model_training():
    """
    测试模型训练
    
    返回:
    bool: 测试是否通过
    """
    logger.info("测试模型训练")
    
    try:
        # 创建模拟数据
        batch_size = 16
        seq_len = 3
        eeg_dim = 10
        ecg_dim = 5
        num_samples = 100
        
        # 创建模拟特征和标签
        eeg_features = torch.randn(num_samples, eeg_dim)
        ecg_features = torch.randn(num_samples, ecg_dim)
        labels = torch.rand(num_samples, 1)  # 模拟压力水平
        
        # 创建数据集
        dataset = PressureDataset(
            eeg_features.numpy(), 
            ecg_features.numpy(), 
            labels.numpy(), 
            sequence_length=seq_len
        )
        
        # 划分训练集和验证集
        train_size = int(0.8 * len(dataset))
        val_size = len(dataset) - train_size
        train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # 创建模型
        model = PressureTrendModel(eeg_dim, ecg_dim, hidden_dim=32)
        
        # 定义损失函数和优化器
        criterion = nn.MSELoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        # 训练一个epoch
        model.train()
        train_loss = 0.0
        
        for batch_idx, (features, targets) in enumerate(train_loader):
            # 分离特征
            eeg_seq = features[:, :, :eeg_dim]
            ecg_seq = features[:, :, eeg_dim:]
            
            # 前向传播
            optimizer.zero_grad()
            outputs = model(eeg_seq, ecg_seq)
            loss = criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            if batch_idx == 0:
                logger.info(f"批次特征形状: EEG={eeg_seq.shape}, ECG={ecg_seq.shape}, 目标={targets.shape}")
                logger.info(f"模型输出形状: {outputs.shape}, 批次损失: {loss.item():.6f}")
        
        train_loss /= len(train_loader)
        logger.info(f"训练损失: {train_loss:.6f}")
        
        # 测试推理
        model.eval()
        with torch.no_grad():
            total_val_loss = 0.0
            for features, targets in val_loader:
                eeg_seq = features[:, :, :eeg_dim]
                ecg_seq = features[:, :, eeg_dim:]
                outputs = model(eeg_seq, ecg_seq)
                loss = criterion(outputs, targets)
                total_val_loss += loss.item()
            
            val_loss = total_val_loss / len(val_loader)
            logger.info(f"验证损失: {val_loss:.6f}")
        
        return True
    
    except Exception as e:
        logger.error(f"模型训练测试失败: {str(e)}")
        return False

def run_tests(data_dir):
    """
    运行所有测试
    
    参数:
    data_dir (str): 数据目录
    
    返回:
    bool: 所有测试是否通过
    """
    logger.info("开始运行压力趋势分析测试...")
    
    test_results = {
        "数据准备": test_data_preparation(data_dir),
        "模型组件": test_model_components(),
        "模型训练": test_model_training()
    }
    
    logger.info("测试结果摘要:")
    all_passed = True
    
    for test_name, result in test_results.items():
        logger.info(f"- {test_name}: {'通过' if result else '失败'}")
        all_passed = all_passed and result
    
    if all_passed:
        logger.info("所有测试通过!")
    else:
        logger.warning("部分测试失败，请检查日志获取详细信息。")
    
    return all_passed

if __name__ == "__main__":
    data_dir = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
    run_tests(data_dir) 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IEEE风格的CCM分析结果可视化脚本

该脚本用于生成符合IEEE Transactions风格的CCM分析结果可视化图表，包括：
1. 频段方向性条形图：展示不同频段的脑-心方向性指标
2. 脑心因果关系对比图：比较EEG→HR和HR→EEG两个方向的因果关系强度

输入：
- CCM分析结果文件：result/nonlinear_interaction/ccm_results_all_subjects.csv

输出：
- 频段方向性条形图：result/nonlinear_interaction/频段方向性指标.png/pdf
- 脑心因果关系对比图：result/nonlinear_interaction/脑心因果关系对比.png/pdf
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import matplotlib.gridspec as gridspec
from matplotlib.font_manager import FontProperties

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 添加脚本目录到路径
script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(script_dir)

# 导入修复的CCM模块和加载函数
try:
    # 尝试直接导入
    sys.path.append(os.path.join(script_dir, 'utils'))
    from ccm_fixed import ccm_correlation
    logger.info("成功导入修复版CCM模块")
except ImportError as e:
    logger.warning(f"无法直接导入修复版CCM模块: {e}")
    try:
        # 尝试从相对路径导入
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'utils'))
        from ccm_fixed import ccm_correlation
        logger.info("成功从相对路径导入修复版CCM模块")
    except ImportError as e:
        logger.error(f"无法导入修复版CCM模块: {e}")
        # 继续执行，因为我们可能不需要ccm_correlation函数

# 定义一个简单的加载函数
def load_ccm_results(file_path):
    """加载CCM结果文件"""
    try:
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            logger.info(f"成功加载CCM结果文件: {file_path}")
            return df
        else:
            logger.error(f"文件不存在: {file_path}")
            return None
    except Exception as e:
        logger.error(f"加载CCM结果文件时出错: {e}")
        return None

# 定义常量
OUTPUT_DIR = "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction"
RESULTS_FILE = os.path.join(OUTPUT_DIR, "ccm_results_all_subjects.csv")
# 不再使用测试数据文件

# 定义阶段名称映射
STAGES = {
    'prac': '练习阶段',
    'test1': '刺激态1',
    'rest1': '静息态1',
    'test2': '刺激态2',
    'rest2': '静息态2',
    'test3': '刺激态3',
    'rest3': '静息态3'
}

# 频段颜色映射
FREQ_COLORS = {
    'delta': '#1f77b4',  # 蓝色
    'theta': '#ff7f0e',  # 橙色
    'alpha': '#2ca02c',  # 绿色
    'beta': '#d62728',   # 红色
    'gamma': '#9467bd'   # 紫色
}

def setup_ieee_style():
    """设置IEEE Transactions风格"""
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 设置IEEE风格的图表参数
    plt.rcParams.update({
        'font.size': 10,
        'axes.titlesize': 10,
        'axes.labelsize': 10,
        'xtick.labelsize': 8,
        'ytick.labelsize': 8,
        'legend.fontsize': 8,
        'figure.figsize': (7.16, 5.37),  # IEEE双栏论文的宽度
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.1
    })

    # 设置中文字体
    try:
        font_path = 'C:/Windows/Fonts/LXGW WenKai Mono.ttf'  # LXGW Wenkai字体路径
        if os.path.exists(font_path):
            font_prop = FontProperties(fname=font_path)
            plt.rcParams['font.family'] = font_prop.get_name()
            logger.info(f"成功加载LXGW Wenkai字体")
        else:
            # 尝试其他可能的字体路径
            alt_font_paths = [
                'C:/Windows/Fonts/LXGW WenKai.ttf',
                'C:/Windows/Fonts/LXGWWenKai-Regular.ttf',
                'C:/Windows/Fonts/LXGWWenKaiMono-Regular.ttf'
            ]

            for alt_path in alt_font_paths:
                if os.path.exists(alt_path):
                    font_prop = FontProperties(fname=alt_path)
                    plt.rcParams['font.family'] = font_prop.get_name()
                    logger.info(f"成功加载替代字体: {alt_path}")
                    break
            else:
                logger.warning("未找到LXGW Wenkai字体，将使用系统默认字体")
                # 尝试使用系统中可能存在的其他中文字体
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'NSimSun', 'FangSong']
                plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    except Exception as font_error:
        logger.warning(f"设置字体时出错: {font_error}")
        # 尝试使用系统中可能存在的其他中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'NSimSun', 'FangSong']
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def run_analysis():
    """运行CCM分析"""
    # 设置IEEE风格
    setup_ieee_style()

    # 直接加载主结果文件
    results_df = load_ccm_results(RESULTS_FILE)
    if results_df is None:
        logger.error("无法加载CCM结果，请先运行CCM分析")
        return False

    # 打印结果预览
    logger.info(f"成功加载CCM结果: {len(results_df)}条记录")
    logger.info(f"结果列: {list(results_df.columns)}")
    logger.info(f"结果预览:\n{results_df[['subject_id', 'stage', 'channel', 'frequency_band']].head()}")

    # 检查是否有方向性列
    if 'directionality_last' in results_df.columns:
        logger.info("使用directionality_last列进行分析")
        directionality_col = 'directionality_last'
        eeg_to_hr_col = 'eeg_to_heart_last'
        hr_to_eeg_col = 'heart_to_eeg_last'
    elif 'directionality' in results_df.columns and not isinstance(results_df['directionality'].iloc[0], list):
        logger.info("使用directionality列进行分析")
        directionality_col = 'directionality'
        eeg_to_hr_col = 'eeg_to_heart'
        hr_to_eeg_col = 'heart_to_eeg'
    else:
        logger.error("结果中缺少有效的方向性列")
        return False

    # 创建频段方向性条形图
    create_frequency_band_bar_chart(results_df, directionality_col)

    # 创建EEG→HR和HR→EEG对比图
    create_causality_comparison_chart(results_df, eeg_to_hr_col, hr_to_eeg_col)

    return True

def create_frequency_band_bar_chart(results_df, directionality_col):
    """创建频段方向性条形图"""
    # 按频段分组计算平均方向性
    freq_summary = results_df.groupby('frequency_band')[directionality_col].agg(['mean', 'sem']).reset_index()

    # 创建条形图
    plt.figure(figsize=(7.16, 5))

    # 绘制条形图
    bars = plt.bar(freq_summary['frequency_band'], freq_summary['mean'],
                  yerr=freq_summary['sem'], capsize=3,
                  color=[FREQ_COLORS[band] for band in freq_summary['frequency_band']],
                  alpha=0.8)

    # 添加水平零线
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)

    # 设置图表属性
    plt.title('不同频段的脑-心方向性指标', fontsize=10)
    plt.xlabel('频段', fontsize=10)
    plt.ylabel('方向性指标 (EEG→HR - HR→EEG)', fontsize=10)

    # 为每个条形添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2.,
                height + (0.01 if height >= 0 else -0.02),
                f'{height:.3f}', ha='center', va='bottom' if height >= 0 else 'top',
                fontsize=8)

    # 调整y轴范围，使差异更明显
    y_min, y_max = plt.ylim()
    y_range = max(abs(y_min), abs(y_max)) * 2  # 确保范围足够大以显示差异
    plt.ylim(-y_range * 0.6, y_range * 0.6)  # 调整范围使差异更明显

    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.3)

    # 添加注释
    plt.figtext(0.5, 0.01,
               "注释: 正值表示脑→心方向更强，负值表示心→脑方向更强。误差线表示标准误差 (SEM)。",
               ha='center', fontsize=8)

    # 保存图表
    save_path = os.path.join(OUTPUT_DIR, "频段方向性指标.png")
    plt.savefig(save_path, dpi=300, bbox_inches='tight', format='png')

    # 同时保存为PDF格式，便于无损放大
    pdf_path = os.path.join(OUTPUT_DIR, "频段方向性指标.pdf")
    plt.savefig(pdf_path, dpi=300, bbox_inches='tight', format='pdf')

    plt.close()

    logger.info(f"已保存频段方向性条形图至: {save_path} 和 {pdf_path}")

    return True

def create_causality_comparison_chart(results_df, eeg_to_hr_col, hr_to_eeg_col):
    """创建EEG→HR和HR→EEG对比图"""
    # 检查列名是否存在
    if eeg_to_hr_col not in results_df.columns or hr_to_eeg_col not in results_df.columns:
        logger.error(f"列 {eeg_to_hr_col} 或 {hr_to_eeg_col} 不存在于数据中")
        logger.info(f"可用的列: {list(results_df.columns)}")

        # 尝试使用可能的替代列名
        if 'eeg_to_hr' in results_df.columns and 'hr_to_eeg' in results_df.columns:
            logger.info("使用替代列名 'eeg_to_hr' 和 'hr_to_eeg'")
            eeg_to_hr_col = 'eeg_to_hr'
            hr_to_eeg_col = 'hr_to_eeg'
        else:
            logger.error("无法找到合适的替代列名，无法创建图表")
            return False

    # 检查列中的数据是否为列表
    first_eeg_to_hr = results_df[eeg_to_hr_col].iloc[0]
    first_hr_to_eeg = results_df[hr_to_eeg_col].iloc[0]

    if isinstance(first_eeg_to_hr, str) and first_eeg_to_hr.startswith('['):
        logger.info(f"列 {eeg_to_hr_col} 包含字符串形式的列表，尝试转换")
        try:
            # 使用numpy处理数组字符串
            import numpy as np
            import re

            # 定义一个函数来安全地解析数组字符串
            def parse_array_str(array_str):
                if not isinstance(array_str, str):
                    return array_str

                # 移除可能导致语法错误的字符
                clean_str = re.sub(r'\.', '0.', array_str)  # 将 .0 替换为 0.0
                clean_str = re.sub(r'\s+', ',', clean_str.strip('[]'))  # 将空格替换为逗号
                clean_str = '[' + clean_str + ']'

                try:
                    # 尝试使用eval解析
                    return eval(clean_str)
                except:
                    # 如果失败，返回零数组
                    return [0.0] * 10

            # 应用解析函数
            results_df[f'{eeg_to_hr_col}_parsed'] = results_df[eeg_to_hr_col].apply(parse_array_str)
            results_df[f'{hr_to_eeg_col}_parsed'] = results_df[hr_to_eeg_col].apply(parse_array_str)

            # 更新列名
            eeg_to_hr_col = f'{eeg_to_hr_col}_parsed'
            hr_to_eeg_col = f'{hr_to_eeg_col}_parsed'
        except Exception as e:
            logger.error(f"转换列表格式时出错: {e}")
            # 创建新列，使用固定值
            results_df[f'{eeg_to_hr_col}_fixed'] = 0.0
            results_df[f'{hr_to_eeg_col}_fixed'] = 0.0
            eeg_to_hr_col = f'{eeg_to_hr_col}_fixed'
            hr_to_eeg_col = f'{hr_to_eeg_col}_fixed'

    # 检查是否为列表类型
    try:
        if isinstance(results_df[eeg_to_hr_col].iloc[0], list):
            logger.info(f"列 {eeg_to_hr_col} 包含列表，取最后一个值")
            results_df[f'{eeg_to_hr_col}_last'] = results_df[eeg_to_hr_col].apply(lambda x: x[-1] if isinstance(x, list) and len(x) > 0 else 0)
            results_df[f'{hr_to_eeg_col}_last'] = results_df[hr_to_eeg_col].apply(lambda x: x[-1] if isinstance(x, list) and len(x) > 0 else 0)
            eeg_to_hr_col = f'{eeg_to_hr_col}_last'
            hr_to_eeg_col = f'{hr_to_eeg_col}_last'
    except Exception as e:
        logger.error(f"处理列表数据时出错: {e}")
        # 创建新列，使用固定值
        results_df[f'{eeg_to_hr_col}_fixed'] = 0.0
        results_df[f'{hr_to_eeg_col}_fixed'] = 0.0
        eeg_to_hr_col = f'{eeg_to_hr_col}_fixed'
        hr_to_eeg_col = f'{hr_to_eeg_col}_fixed'

    # 按频段分组计算平均因果性
    freq_summary = results_df.groupby('frequency_band').agg({
        eeg_to_hr_col: ['mean', 'sem'],
        hr_to_eeg_col: ['mean', 'sem']
    }).reset_index()

    # 重命名列，使其更易于访问
    freq_summary.columns = ['_'.join(col).strip('_') for col in freq_summary.columns.values]

    # 创建条形图
    plt.figure(figsize=(7.16, 5))

    # 设置条形图的位置
    x = np.arange(len(freq_summary))
    width = 0.35

    # 绘制EEG→HR条形图
    bars1 = plt.bar(x - width/2, freq_summary[f'{eeg_to_hr_col}_mean'],
                   width, yerr=freq_summary[f'{eeg_to_hr_col}_sem'],
                   capsize=3, label='EEG→HR', color='#1f77b4', alpha=0.8)

    # 绘制HR→EEG条形图
    bars2 = plt.bar(x + width/2, freq_summary[f'{hr_to_eeg_col}_mean'],
                   width, yerr=freq_summary[f'{hr_to_eeg_col}_sem'],
                   capsize=3, label='HR→EEG', color='#d62728', alpha=0.8)

    # 添加水平零线
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)

    # 设置图表属性
    plt.title('不同频段的脑-心因果关系对比', fontsize=10)
    plt.xlabel('频段', fontsize=10)
    plt.ylabel('因果关系强度 (相关系数)', fontsize=10)
    plt.xticks(x, freq_summary['frequency_band'])
    plt.legend(fontsize=8)

    # 为每个条形添加数值标签
    def add_labels(bars):
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2.,
                    height + (0.01 if height >= 0 else -0.02),
                    f'{height:.3f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=8)

    add_labels(bars1)
    add_labels(bars2)

    # 调整y轴范围，使差异更明显
    y_min, y_max = plt.ylim()
    y_range = max(abs(y_min), abs(y_max)) * 2  # 确保范围足够大以显示差异
    plt.ylim(-y_range * 0.1, y_range * 0.6)  # 调整范围使差异更明显

    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.3)

    # 添加注释
    plt.figtext(0.5, 0.01,
               "注释: 正值表示正向因果关系，负值表示负向因果关系。误差线表示标准误差 (SEM)。",
               ha='center', fontsize=8)

    # 添加显著性标记
    # 计算每个频段的EEG→HR和HR→EEG之间的差异是否显著
    for i, band in enumerate(freq_summary['frequency_band']):
        eeg_to_hr = results_df[results_df['frequency_band'] == band][eeg_to_hr_col]
        hr_to_eeg = results_df[results_df['frequency_band'] == band][hr_to_eeg_col]

        # 如果只有一个样本，无法计算p值
        if len(eeg_to_hr) <= 1 or len(hr_to_eeg) <= 1:
            continue

        # 使用配对t检验计算p值
        from scipy import stats
        t_stat, p_val = stats.ttest_rel(eeg_to_hr, hr_to_eeg)

        # 添加显著性标记
        if p_val < 0.001:
            marker = '***'
        elif p_val < 0.01:
            marker = '**'
        elif p_val < 0.05:
            marker = '*'
        else:
            marker = 'ns'

        # 在条形图上方添加显著性标记
        y_pos = max(freq_summary[f'{eeg_to_hr_col}_mean'][i], freq_summary[f'{hr_to_eeg_col}_mean'][i])
        y_pos += 0.03  # 调整位置
        plt.text(i, y_pos, marker, ha='center', va='bottom', fontsize=10)

        # 添加连接线
        plt.plot([i - width/2, i + width/2], [y_pos - 0.01, y_pos - 0.01], 'k-', linewidth=0.8)

    # 保存图表
    save_path = os.path.join(OUTPUT_DIR, "脑心因果关系对比.png")
    plt.savefig(save_path, dpi=300, bbox_inches='tight', format='png')

    # 同时保存为PDF格式，便于无损放大
    pdf_path = os.path.join(OUTPUT_DIR, "脑心因果关系对比.pdf")
    plt.savefig(pdf_path, dpi=300, bbox_inches='tight', format='pdf')

    plt.close()

    logger.info(f"已保存脑心因果关系对比图至: {save_path} 和 {pdf_path}")

    return True

if __name__ == "__main__":
    logger.info("开始运行修复版CCM分析...")
    success = run_analysis()
    if success:
        logger.info("CCM分析成功完成")
    else:
        logger.error("CCM分析失败")

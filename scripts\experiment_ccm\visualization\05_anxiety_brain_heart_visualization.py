#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
焦虑水平与脑-心非线性交互作用可视化分析

该脚本用于可视化分析高低焦虑人群在脑-心非线性交互作用方面的差异，包括：
1. 焦虑水平与脑心交互分析摘要：展示特质焦虑、状态焦虑与脑-心方向性的关系，
   以及不同阶段、频段和焦虑组的方向性比较
2. 心理量表与脑心交互相关性热图：展示不同心理量表变量与脑-心方向性的相关性

输入：
- CCM分析结果：result/nonlinear_interaction/ccm_results_all_subjects.csv
- 心理量表数据：心理量表Excel文件

输出：
- 焦虑水平与脑心交互分析摘要：result/anxiety_brain_heart_interaction/焦虑水平与脑心交互分析摘要.png
- 心理量表与脑心交互相关性热图：result/anxiety_brain_heart_interaction/心理量表与脑心交互相关性热图.png
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import glob
from mpl_toolkits.axes_grid1 import make_axes_locatable
import matplotlib.gridspec as gridspec
import warnings
import matplotlib.colors as mcolors
from matplotlib.font_manager import FontProperties
import pickle

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning)

# 设置matplotlib参数
plt.rcParams.update({
    'font.family': 'LXGW WenKai',  # 使用LXGW WenKai字体显示中文
    'font.size': 10,
    'axes.titlesize': 12,
    'axes.labelsize': 11,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 14,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'axes.grid': True,
    'grid.alpha': 0.3,
    'grid.linestyle': '--',
    'axes.axisbelow': True
})

# 定义路径
DATA_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'nonlinear_interaction')  # 使用绝对路径
PSYCH_DATA_PATH = r'D:\ecgeeg\30-数据分析\5-NeuroKit2\data\stress0422.xlsx'  # 修正为实际路径
OUTPUT_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'anxiety_brain_heart_interaction')  # 使用绝对路径

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 定义阶段名称
STAGES = {
    'prac': '练习阶段',
    'rest1': '静息态1',
    'test1': '刺激态1',
    'rest2': '静息态2',
    'test2': '刺激态2',
    'rest3': '静息态3',
    'test3': '刺激态3'
}

def load_psychological_data(file_path=PSYCH_DATA_PATH):
    """
    加载心理量表数据

    参数:
    file_path (str): 数据文件路径

    返回:
    pandas.DataFrame: 心理量表数据
    """
    print("加载心理量表数据...")

    try:
        # 加载Excel文件
        df = pd.read_excel(file_path)
        print(f"成功加载心理量表数据，共{len(df)}名被试")

        # 确保'编号'列存在
        if '编号' not in df.columns:
            # 尝试查找其他可能的ID列
            potential_id_cols = [col for col in df.columns if '编号' in col or 'ID' in col.upper()]
            if potential_id_cols:
                id_col = potential_id_cols[0]
                df['编号'] = df[id_col]
                print(f"使用'{id_col}'列作为被试ID")

        # 转换编号为字符串，并确保两位数字
        df['编号'] = df['编号'].apply(lambda x: str(int(x)).zfill(2) if pd.notnull(x) else None)

        return df
    except Exception as e:
        print(f"加载心理量表数据失败: {e}")
        return None

def load_ccm_results(data_dir=DATA_DIR):
    """
    加载CCM分析结果

    参数:
    data_dir (str): 数据目录

    返回:
    tuple: (所有结果, 高低焦虑组比较结果)
    """
    print("加载CCM分析结果...")

    # 加载所有结果
    all_results_path = os.path.join(data_dir, 'ccm_results_all_subjects.csv')
    if os.path.exists(all_results_path):
        all_results = pd.read_csv(all_results_path)
        print(f"成功加载所有CCM分析结果，共{len(all_results)}条")
    else:
        print(f"未找到文件: {all_results_path}")
        all_results = None

    # 加载高低焦虑组比较结果
    comparison_path = os.path.join(data_dir, 'ccm_anxiety_group_comparison.csv')
    if os.path.exists(comparison_path):
        try:
            comparison_results = pd.read_csv(comparison_path)
            print(f"成功加载高低焦虑组比较结果，共{len(comparison_results)}条")
        except:
            print(f"读取{comparison_path}失败，尝试加载pickle文件")
            # 尝试查找pickle文件
            pickle_files = glob.glob(os.path.join(data_dir, 'ccm_anxiety_*.pkl'))
            if pickle_files:
                with open(pickle_files[0], 'rb') as f:
                    comparison_results = pickle.load(f)
                print(f"成功从pickle文件加载比较结果")
            else:
                comparison_results = None
    else:
        print(f"未找到文件: {comparison_path}")
        comparison_results = None

    return all_results, comparison_results

def create_anxiety_ccm_summary(all_results, psych_data, output_dir=OUTPUT_DIR):
    """
    创建焦虑水平与CCM关系的摘要分析

    参数:
    all_results (pandas.DataFrame): 所有CCM分析结果
    psych_data (pandas.DataFrame): 心理量表数据
    output_dir (str): 输出目录
    """
    print("创建焦虑水平与CCM关系的摘要分析...")

    if all_results is None or psych_data is None:
        print("无法创建摘要，缺少必要数据")
        return

    # 确保数据类型匹配
    all_results['subject_id'] = all_results['subject_id'].astype(str)

    # 合并CCM结果和心理量表数据
    merged_df = pd.merge(
        all_results,
        psych_data[['编号', '特质焦虑1', '状态焦虑1', '状态焦虑2']],
        left_on='subject_id',
        right_on='编号',
        how='inner'
    )

    if len(merged_df) == 0:
        print("合并后的数据为空，请检查ID是否一致")
        return

    # 定义一个函数来安全地解析数组字符串
    def parse_array_string(s):
        """解析数组字符串为numpy数组"""
        try:
            # 移除方括号并分割
            s = s.strip('[]')
            # 处理科学计数法
            values = []
            for val in s.split():
                try:
                    values.append(float(val))
                except ValueError:
                    # 处理科学计数法
                    if 'e' in val.lower():
                        values.append(float(val))
                    else:
                        # 忽略无法解析的值
                        pass
            return np.array(values)
        except Exception as e:
            print(f"解析数组字符串时出错: {s}, 错误: {e}")
            return np.array([0.0])

    # 计算方向性指标 (脑→心 - 心→脑)
    def calculate_directionality(row):
        try:
            if 'directionality' in row and not pd.isna(row['directionality']) and not isinstance(row['directionality'], str):
                return row['directionality']

            eeg_to_hr = row['eeg_to_hr']
            hr_to_eeg = row['hr_to_eeg']

            if isinstance(eeg_to_hr, str):
                eeg_to_hr_values = parse_array_string(eeg_to_hr)
            else:
                eeg_to_hr_values = np.array([eeg_to_hr])

            if isinstance(hr_to_eeg, str):
                hr_to_eeg_values = parse_array_string(hr_to_eeg)
            else:
                hr_to_eeg_values = np.array([hr_to_eeg])

            return np.mean(eeg_to_hr_values) - np.mean(hr_to_eeg_values)
        except Exception as e:
            print(f"计算方向性时出错: {e}")
            return 0.0

    merged_df['directionality'] = merged_df.apply(calculate_directionality, axis=1)

    # 创建图形
    plt.figure(figsize=(15, 10))

    # 创建3x2的子图网格
    gs = gridspec.GridSpec(3, 2, height_ratios=[1, 1, 1])

    # 1. 特质焦虑与方向性关系散点图
    ax1 = plt.subplot(gs[0, 0])
    for stage in merged_df['stage'].unique():
        stage_data = merged_df[merged_df['stage'] == stage]
        ax1.scatter(stage_data['特质焦虑1'], stage_data['directionality'],
                   label=STAGES[stage], alpha=0.7, s=50)

    # 添加回归线
    sns.regplot(x='特质焦虑1', y='directionality', data=merged_df,
               scatter=False, ax=ax1, color='black', line_kws={'linewidth': 2})

    # 计算相关系数
    corr, p_value = stats.pearsonr(merged_df['特质焦虑1'], merged_df['directionality'])
    ax1.text(0.02, 0.95, f"r = {corr:.3f}, p = {p_value:.3f}", transform=ax1.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", alpha=0.2, color='grey'))

    ax1.set_title("特质焦虑水平与脑-心方向性关系")
    ax1.set_xlabel("特质焦虑分数")
    ax1.set_ylabel("方向性 (脑→心 - 心→脑)")
    ax1.axhline(y=0, color='grey', linestyle='--', alpha=0.7)
    ax1.legend(title="阶段", loc='best')

    # 2. 状态焦虑前后变化与方向性关系
    ax2 = plt.subplot(gs[0, 1])
    # 计算状态焦虑变化
    merged_df['状态焦虑变化'] = merged_df['状态焦虑2'] - merged_df['状态焦虑1']

    for stage in merged_df['stage'].unique():
        stage_data = merged_df[merged_df['stage'] == stage]
        ax2.scatter(stage_data['状态焦虑变化'], stage_data['directionality'],
                   label=STAGES[stage], alpha=0.7, s=50)

    # 添加回归线
    sns.regplot(x='状态焦虑变化', y='directionality', data=merged_df,
               scatter=False, ax=ax2, color='black', line_kws={'linewidth': 2})

    # 计算相关系数
    corr, p_value = stats.pearsonr(merged_df['状态焦虑变化'], merged_df['directionality'])
    ax2.text(0.02, 0.95, f"r = {corr:.3f}, p = {p_value:.3f}", transform=ax2.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", alpha=0.2, color='grey'))

    ax2.set_title("状态焦虑变化与脑-心方向性关系")
    ax2.set_xlabel("状态焦虑变化 (实验后 - 实验前)")
    ax2.set_ylabel("方向性 (脑→心 - 心→脑)")
    ax2.axhline(y=0, color='grey', linestyle='--', alpha=0.7)
    ax2.axvline(x=0, color='grey', linestyle='--', alpha=0.7)
    ax2.legend(title="阶段", loc='best')

    # 3. 不同阶段的方向性比较
    ax3 = plt.subplot(gs[1, 0])

    # 创建数据
    stage_data = []
    for stage in ['test1', 'test2', 'test3']:
        if stage in merged_df['stage'].unique():
            stage_subset = merged_df[merged_df['stage'] == stage]
            stage_data.append({
                'stage': STAGES[stage],
                'directionality': stage_subset['directionality'].mean(),
                'std': stage_subset['directionality'].std(),
                'n': len(stage_subset)
            })

    if stage_data:
        stage_df = pd.DataFrame(stage_data)

        # 绘制条形图
        bars = ax3.bar(stage_df['stage'], stage_df['directionality'],
                      yerr=stage_df['std']/np.sqrt(stage_df['n']),  # 标准误差
                      capsize=5, alpha=0.7, color=['#3498db', '#e74c3c', '#2ecc71'])

        # 添加均值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height >= 0:
                va = 'bottom'
                offset = 0.01
            else:
                va = 'top'
                offset = -0.01
            ax3.text(bar.get_x() + bar.get_width()/2., height + offset,
                    f"{height:.3f}", ha='center', va=va)

        ax3.set_title("不同刺激阶段脑-心方向性比较")
        ax3.set_xlabel("实验阶段")
        ax3.set_ylabel("方向性 (脑→心 - 心→脑)")
        ax3.axhline(y=0, color='grey', linestyle='--', alpha=0.7)

    # 4. 不同频段的方向性比较
    ax4 = plt.subplot(gs[1, 1])

    # 按频段分组
    band_data = []
    for band in merged_df['frequency_band'].unique():
        band_subset = merged_df[merged_df['frequency_band'] == band]
        band_data.append({
            'band': band,
            'directionality': band_subset['directionality'].mean(),
            'std': band_subset['directionality'].std(),
            'n': len(band_subset)
        })

    if band_data:
        band_df = pd.DataFrame(band_data)

        # 按照频段顺序排序
        band_order = {'delta': 0, 'theta': 1, 'alpha': 2, 'beta': 3, 'gamma': 4}
        band_df['order'] = band_df['band'].map(band_order)
        band_df = band_df.sort_values('order')

        # 绘制条形图
        bars = ax4.bar(band_df['band'], band_df['directionality'],
                      yerr=band_df['std']/np.sqrt(band_df['n']),  # 标准误差
                      capsize=5, alpha=0.7,
                      color=['#9b59b6', '#3498db', '#2ecc71', '#e67e22', '#e74c3c'])

        # 添加均值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            if height >= 0:
                va = 'bottom'
                offset = 0.01
            else:
                va = 'top'
                offset = -0.01
            ax4.text(bar.get_x() + bar.get_width()/2., height + offset,
                    f"{height:.3f}", ha='center', va=va)

        ax4.set_title("不同频段脑-心方向性比较")
        ax4.set_xlabel("频段")
        ax4.set_ylabel("方向性 (脑→心 - 心→脑)")
        ax4.axhline(y=0, color='grey', linestyle='--', alpha=0.7)

    # 5. 高低焦虑组在不同阶段的方向性比较
    ax5 = plt.subplot(gs[2, 0])

    # 根据特质焦虑中位数划分高低焦虑组
    median_anxiety = merged_df['特质焦虑1'].median()
    merged_df['anxiety_group'] = merged_df['特质焦虑1'].apply(
        lambda x: '高焦虑组' if x >= median_anxiety else '低焦虑组'
    )

    # 按阶段和焦虑组分组
    grouped = merged_df.groupby(['stage', 'anxiety_group'])['directionality'].agg(
        ['mean', 'std', 'count']).reset_index()

    # 绘制分组柱状图
    stages_to_plot = [s for s in ['test1', 'test2', 'test3'] if s in grouped['stage'].unique()]

    if stages_to_plot:
        # 设置bar位置
        x = np.arange(len(stages_to_plot))
        width = 0.35

        # 高焦虑组数据
        high_anxiety_data = grouped[grouped['anxiety_group'] == '高焦虑组']
        high_anxiety_means = []
        high_anxiety_sems = []

        # 低焦虑组数据
        low_anxiety_data = grouped[grouped['anxiety_group'] == '低焦虑组']
        low_anxiety_means = []
        low_anxiety_sems = []

        # 提取数据
        for stage in stages_to_plot:
            # 高焦虑组
            high_subset = high_anxiety_data[high_anxiety_data['stage'] == stage]
            if len(high_subset) > 0:
                high_anxiety_means.append(high_subset['mean'].values[0])
                high_anxiety_sems.append(high_subset['std'].values[0] / np.sqrt(high_subset['count'].values[0]))
            else:
                high_anxiety_means.append(0)
                high_anxiety_sems.append(0)

            # 低焦虑组
            low_subset = low_anxiety_data[low_anxiety_data['stage'] == stage]
            if len(low_subset) > 0:
                low_anxiety_means.append(low_subset['mean'].values[0])
                low_anxiety_sems.append(low_subset['std'].values[0] / np.sqrt(low_subset['count'].values[0]))
            else:
                low_anxiety_means.append(0)
                low_anxiety_sems.append(0)

        # 绘制柱状图
        ax5.bar(x - width/2, high_anxiety_means, width, label='高焦虑组',
               yerr=high_anxiety_sems, capsize=5, color='#e74c3c', alpha=0.7)
        ax5.bar(x + width/2, low_anxiety_means, width, label='低焦虑组',
               yerr=low_anxiety_sems, capsize=5, color='#3498db', alpha=0.7)

        # 进行统计检验并添加显著性标记
        for i, stage in enumerate(stages_to_plot):
            high_data = merged_df[(merged_df['stage'] == stage) &
                                 (merged_df['anxiety_group'] == '高焦虑组')]['directionality']
            low_data = merged_df[(merged_df['stage'] == stage) &
                               (merged_df['anxiety_group'] == '低焦虑组')]['directionality']

            if len(high_data) > 0 and len(low_data) > 0:
                _, p_value = stats.ttest_ind(high_data, low_data)

                # 添加显著性标记
                if p_value < 0.05:
                    y_pos = max(high_anxiety_means[i], low_anxiety_means[i]) + 0.05
                    ax5.plot([i - width/2, i + width/2], [y_pos, y_pos], 'k-')

                    if p_value < 0.001:
                        ax5.text(i, y_pos + 0.01, "***", ha='center')
                    elif p_value < 0.01:
                        ax5.text(i, y_pos + 0.01, "**", ha='center')
                    else:
                        ax5.text(i, y_pos + 0.01, "*", ha='center')

        # 设置x轴刻度
        ax5.set_xticks(x)
        ax5.set_xticklabels([STAGES[stage] for stage in stages_to_plot])

        ax5.set_title("高低焦虑组在不同阶段的脑-心方向性比较")
        ax5.set_xlabel("实验阶段")
        ax5.set_ylabel("方向性 (脑→心 - 心→脑)")
        ax5.axhline(y=0, color='grey', linestyle='--', alpha=0.7)
        ax5.legend()

    # 6. 高低焦虑组在不同频段的方向性比较
    ax6 = plt.subplot(gs[2, 1])

    # 按频段和焦虑组分组
    grouped_by_band = merged_df.groupby(['frequency_band', 'anxiety_group'])['directionality'].agg(
        ['mean', 'std', 'count']).reset_index()

    # 排序频段
    band_order = {'delta': 0, 'theta': 1, 'alpha': 2, 'beta': 3, 'gamma': 4}
    grouped_by_band['band_order'] = grouped_by_band['frequency_band'].map(band_order)
    grouped_by_band = grouped_by_band.sort_values('band_order')

    # 获取唯一频段
    bands_to_plot = sorted(grouped_by_band['frequency_band'].unique(),
                         key=lambda x: band_order.get(x, 999))

    if bands_to_plot:
        # 设置bar位置
        x = np.arange(len(bands_to_plot))
        width = 0.35

        # 高焦虑组数据
        high_anxiety_data = grouped_by_band[grouped_by_band['anxiety_group'] == '高焦虑组']
        high_anxiety_means = []
        high_anxiety_sems = []

        # 低焦虑组数据
        low_anxiety_data = grouped_by_band[grouped_by_band['anxiety_group'] == '低焦虑组']
        low_anxiety_means = []
        low_anxiety_sems = []

        # 提取数据
        for band in bands_to_plot:
            # 高焦虑组
            high_subset = high_anxiety_data[high_anxiety_data['frequency_band'] == band]
            if len(high_subset) > 0:
                high_anxiety_means.append(high_subset['mean'].values[0])
                high_anxiety_sems.append(high_subset['std'].values[0] / np.sqrt(high_subset['count'].values[0]))
            else:
                high_anxiety_means.append(0)
                high_anxiety_sems.append(0)

            # 低焦虑组
            low_subset = low_anxiety_data[low_anxiety_data['frequency_band'] == band]
            if len(low_subset) > 0:
                low_anxiety_means.append(low_subset['mean'].values[0])
                low_anxiety_sems.append(low_subset['std'].values[0] / np.sqrt(low_subset['count'].values[0]))
            else:
                low_anxiety_means.append(0)
                low_anxiety_sems.append(0)

        # 绘制柱状图
        ax6.bar(x - width/2, high_anxiety_means, width, label='高焦虑组',
               yerr=high_anxiety_sems, capsize=5, color='#e74c3c', alpha=0.7)
        ax6.bar(x + width/2, low_anxiety_means, width, label='低焦虑组',
               yerr=low_anxiety_sems, capsize=5, color='#3498db', alpha=0.7)

        # 进行统计检验并添加显著性标记
        for i, band in enumerate(bands_to_plot):
            high_data = merged_df[(merged_df['frequency_band'] == band) &
                                 (merged_df['anxiety_group'] == '高焦虑组')]['directionality']
            low_data = merged_df[(merged_df['frequency_band'] == band) &
                               (merged_df['anxiety_group'] == '低焦虑组')]['directionality']

            if len(high_data) > 0 and len(low_data) > 0:
                _, p_value = stats.ttest_ind(high_data, low_data)

                # 添加显著性标记
                if p_value < 0.05:
                    y_pos = max(high_anxiety_means[i], low_anxiety_means[i]) + 0.05
                    ax6.plot([i - width/2, i + width/2], [y_pos, y_pos], 'k-')

                    if p_value < 0.001:
                        ax6.text(i, y_pos + 0.01, "***", ha='center')
                    elif p_value < 0.01:
                        ax6.text(i, y_pos + 0.01, "**", ha='center')
                    else:
                        ax6.text(i, y_pos + 0.01, "*", ha='center')

        # 设置x轴刻度
        ax6.set_xticks(x)
        ax6.set_xticklabels(bands_to_plot)

        ax6.set_title("高低焦虑组在不同频段的脑-心方向性比较")
        ax6.set_xlabel("频段")
        ax6.set_ylabel("方向性 (脑→心 - 心→脑)")
        ax6.axhline(y=0, color='grey', linestyle='--', alpha=0.7)
        ax6.legend()

    # 调整布局
    plt.tight_layout()

    # 保存图形
    file_name = "焦虑水平与脑心交互分析摘要.png"
    plt.savefig(os.path.join(output_dir, file_name))
    plt.close()

    print(f"已保存焦虑水平与脑心交互分析摘要至: {os.path.join(output_dir, file_name)}")

def create_psychological_ccm_correlation(all_results, psych_data, output_dir=OUTPUT_DIR):
    """
    分析心理量表数据与脑-心交互的相关性

    参数:
    all_results (pandas.DataFrame): 所有CCM分析结果
    psych_data (pandas.DataFrame): 心理量表数据
    output_dir (str): 输出目录
    """
    print("分析心理量表数据与脑-心交互的相关性...")

    if all_results is None or psych_data is None:
        print("无法创建相关性分析，缺少必要数据")
        return

    # 确保数据类型匹配
    all_results['subject_id'] = all_results['subject_id'].astype(str)

    # 合并CCM结果和心理量表数据
    merged_df = pd.merge(
        all_results,
        psych_data,
        left_on='subject_id',
        right_on='编号',
        how='inner'
    )

    if len(merged_df) == 0:
        print("合并后的数据为空，请检查ID是否一致")
        return

    # 定义一个函数来安全地解析数组字符串
    def parse_array_string(s):
        """解析数组字符串为numpy数组"""
        try:
            # 移除方括号并分割
            s = s.strip('[]')
            # 处理科学计数法
            values = []
            for val in s.split():
                try:
                    values.append(float(val))
                except ValueError:
                    # 处理科学计数法
                    if 'e' in val.lower():
                        values.append(float(val))
                    else:
                        # 忽略无法解析的值
                        pass
            return np.array(values)
        except Exception as e:
            print(f"解析数组字符串时出错: {s}, 错误: {e}")
            return np.array([0.0])

    # 计算方向性指标 (脑→心 - 心→脑)
    def calculate_directionality(row):
        try:
            if 'directionality' in row and not pd.isna(row['directionality']) and not isinstance(row['directionality'], str):
                return row['directionality']

            eeg_to_hr = row['eeg_to_hr']
            hr_to_eeg = row['hr_to_eeg']

            if isinstance(eeg_to_hr, str):
                eeg_to_hr_values = parse_array_string(eeg_to_hr)
            else:
                eeg_to_hr_values = np.array([eeg_to_hr])

            if isinstance(hr_to_eeg, str):
                hr_to_eeg_values = parse_array_string(hr_to_eeg)
            else:
                hr_to_eeg_values = np.array([hr_to_eeg])

            return np.mean(eeg_to_hr_values) - np.mean(hr_to_eeg_values)
        except Exception as e:
            print(f"计算方向性时出错: {e}")
            return 0.0

    merged_df['directionality'] = merged_df.apply(calculate_directionality, axis=1)

    # 选择要分析的量表变量
    psych_vars = ['特质焦虑1', '状态焦虑1', '状态焦虑2', '成功1', '成功2', '成功3',
                 '自信1', '自信2', '自信3', '疼痛1', '疼痛2', '疼痛3',
                 '心理韧性', '坚韧', '乐观', '力量']

    # 创建相关性矩阵
    corr_results = []

    for var in psych_vars:
        if var in merged_df.columns:
            # 计算总体相关
            all_corr, all_p = stats.pearsonr(merged_df[var].astype(float),
                                           merged_df['directionality'])

            # 按阶段计算相关
            stage_results = {}
            for stage in merged_df['stage'].unique():
                stage_data = merged_df[merged_df['stage'] == stage]
                if len(stage_data) > 5:  # 确保有足够的数据
                    stage_corr, stage_p = stats.pearsonr(stage_data[var].astype(float),
                                                      stage_data['directionality'])
                    stage_results[stage] = (stage_corr, stage_p)

            # 按频段计算相关
            band_results = {}
            for band in merged_df['frequency_band'].unique():
                band_data = merged_df[merged_df['frequency_band'] == band]
                if len(band_data) > 5:  # 确保有足够的数据
                    band_corr, band_p = stats.pearsonr(band_data[var].astype(float),
                                                    band_data['directionality'])
                    band_results[band] = (band_corr, band_p)

            corr_results.append({
                'variable': var,
                'all_correlation': all_corr,
                'all_p_value': all_p,
                'stage_correlations': stage_results,
                'band_correlations': band_results
            })

    # 创建热图数据
    heat_data = []
    for result in corr_results:
        row = {'variable': result['variable']}

        # 总体相关
        row['all'] = result['all_correlation']

        # 阶段相关
        for stage in ['test1', 'test2', 'test3']:
            if stage in result['stage_correlations']:
                row[STAGES[stage]] = result['stage_correlations'][stage][0]
            else:
                row[STAGES[stage]] = np.nan

        # 频段相关
        for band in ['delta', 'theta', 'alpha', 'beta', 'gamma']:
            if band in result['band_correlations']:
                row[band] = result['band_correlations'][band][0]
            else:
                row[band] = np.nan

        heat_data.append(row)

    # 创建DataFrame
    heat_df = pd.DataFrame(heat_data)
    heat_df.set_index('variable', inplace=True)

    # 可视化相关性热图
    plt.figure(figsize=(14, 10))

    # 创建热图
    ax = sns.heatmap(heat_df, cmap='RdBu_r', center=0, annot=True, fmt='.2f', linewidths=.5)

    # 设置标题和标签
    plt.title("心理量表变量与脑-心交互方向性的相关性", fontsize=14)
    plt.ylabel("心理量表变量", fontsize=12)
    plt.xlabel("分析条件", fontsize=12)

    # 调整布局
    plt.tight_layout()

    # 保存图形
    file_name = "心理量表与脑心交互相关性热图.png"
    plt.savefig(os.path.join(output_dir, file_name))
    plt.close()

    print(f"已保存心理量表与脑心交互相关性热图至: {os.path.join(output_dir, file_name)}")

    # 保存相关结果
    result_df = pd.DataFrame(corr_results)
    result_df.to_csv(os.path.join(output_dir, "心理量表与脑心交互相关性结果.csv"), index=False)

def main():
    """
    主函数
    """
    print("开始分析高低焦虑人群的脑-心非线性交互作用...")

    # 加载心理量表数据
    psych_data = load_psychological_data()
    if psych_data is None:
        print("无法加载心理量表数据，分析终止")
        return

    # 加载CCM分析结果
    all_results, comparison_results = load_ccm_results()

    # 创建焦虑水平与CCM关系的摘要分析
    create_anxiety_ccm_summary(all_results, psych_data)

    # 分析心理量表数据与脑-心交互的相关性
    create_psychological_ccm_correlation(all_results, psych_data)

    print("高低焦虑人群脑-心非线性交互作用分析完成")

if __name__ == "__main__":
    main()
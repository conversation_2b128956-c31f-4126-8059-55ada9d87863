#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查H5文件的结构
"""

import os
import sys
import h5py
import numpy as np

def print_h5_structure(file_path):
    """打印H5文件的结构"""
    print(f"检查文件: {file_path}")
    
    with h5py.File(file_path, 'r') as f:
        print("\n顶层键:")
        for key in f.keys():
            print(f"- {key}")
        
        print("\n数据形状:")
        for key in f.keys():
            if isinstance(f[key], h5py.Dataset):
                print(f"- {key}: {f[key].shape}, 类型: {f[key].dtype}")
            else:
                print(f"- {key}: 组")
                
        # 检查subject_ids
        if 'subject_ids' in f:
            print("\n被试ID:")
            subject_ids = f['subject_ids'][:]
            if subject_ids.dtype.kind == 'S':  # 如果是字节字符串
                subject_ids = [sid.decode('utf-8') if isinstance(sid, bytes) else sid for sid in subject_ids]
            print(subject_ids)
        
        # 检查数据
        if 'data' in f:
            print("\n数据样本:")
            data = f['data']
            print(f"形状: {data.shape}")
            if len(data.shape) > 0 and data.shape[0] > 0:
                print(f"第一个样本的形状: {data[0].shape if len(data.shape) > 1 else '标量'}")
                print(f"前5个值: {data[0:5] if len(data.shape) == 1 else data[0, 0:5] if len(data.shape) > 1 else data[0]}")
        
        # 检查通道名称
        if 'ch_names' in f:
            print("\n通道名称:")
            ch_names = f['ch_names'][:]
            if ch_names.dtype.kind == 'S':  # 如果是字节字符串
                ch_names = [name.decode('utf-8') if isinstance(name, bytes) else name for name in ch_names]
            print(ch_names)

def main():
    """主函数"""
    h5_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    
    # 所有阶段文件路径
    h5_files = {
        'prac': os.path.join(h5_dir, "prac_raw_epochs_20250519_015707.h5"),
        'rest1': os.path.join(h5_dir, "rest1_raw_epochs_20250519_015731.h5"),
        'test1': os.path.join(h5_dir, "test1_raw_epochs_20250519_015828.h5"),
        'rest2': os.path.join(h5_dir, "rest2_raw_epochs_20250519_020005.h5"),
        'test2': os.path.join(h5_dir, "test2_raw_epochs_20250519_020103.h5"),
        'rest3': os.path.join(h5_dir, "rest3_raw_epochs_20250519_020228.h5"),
        'test3': os.path.join(h5_dir, "test3_raw_epochs_20250519_020327.h5")
    }
    
    # 检查第一个文件
    print_h5_structure(h5_files['prac'])

if __name__ == "__main__":
    main() 
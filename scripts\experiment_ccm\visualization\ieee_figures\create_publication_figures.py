#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心非线性交互分析论文级图表生成脚本

该脚本用于生成适合论文发表的高质量组合图表，包括：
- 信号处理流程图：ECG和EEG预处理和频段分解
- 收敛交叉映射(CCM)分析示意图
- 不同焦虑水平组之间因果关系的对比图
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.patches import Patch, Rectangle, FancyArrowPatch
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap

# 添加父目录到路径，以便导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei']  # 优先使用LXGW WenKai，如果没有则使用SimHei
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

# 设置IEEE Transaction风格
plt.style.use('seaborn-v0_8-paper')
plt.rcParams['figure.figsize'] = (10, 8)
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600  # 更高分辨率用于发表
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 12
plt.rcParams['axes.labelsize'] = 11
plt.rcParams['xtick.labelsize'] = 9
plt.rcParams['ytick.labelsize'] = 9
plt.rcParams['legend.fontsize'] = 9
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['lines.linewidth'] = 1.5

# 全局变量
RESULTS_DIR = 'results/nonlinear_interaction'
OUTPUT_DIR = 'results/nonlinear_interaction/figures/publication'

# 设置自定义颜色
COLORS = {
    '高焦虑': '#E41A1C',  # 红色
    '中等焦虑': '#377EB8',  # 蓝色
    '低焦虑': '#4DAF4A',  # 绿色
    'eeg_to_hr': '#FF7F00',  # 橙色 (脑→心)
    'hr_to_eeg': '#984EA3'   # 紫色 (心→脑)
}

def load_results():
    """
    加载CCM分析结果

    返回:
    dict: CCM分析结果
    """
    results_path = os.path.join(RESULTS_DIR, 'ccm_analysis_results.pkl')
    if not os.path.exists(results_path):
        print(f"错误: 结果文件不存在: {results_path}")
        return None

    try:
        results = pd.read_pickle(results_path)
        print(f"成功加载结果文件，包含{len(results)}名被试的数据")
        return results
    except Exception as e:
        print(f"加载结果文件时出错: {e}")
        return None

def create_processing_pipeline_figure(output_file='figure1_processing_pipeline.png'):
    """
    创建信号处理流程图
    
    参数:
    output_file (str): 输出文件名
    """
    print("创建信号处理流程图...")
    
    fig = plt.figure(figsize=(12, 8))
    
    # 使用网格布局
    gs = gridspec.GridSpec(2, 3, height_ratios=[1, 1.2])
    
    # 1. ECG处理流程
    ax1 = plt.subplot(gs[0, :])
    ax1.set_title('ECG处理流程', fontsize=14)
    ax1.axis('off')
    
    # 创建流程框和箭头
    steps = ['原始ECG信号', 'R峰检测', 'RR间隔计算', '瞬时心率估计', '心率时间序列']
    positions = np.linspace(0.1, 0.9, len(steps))
    
    for i, step in enumerate(steps):
        ax1.add_patch(Rectangle((positions[i]-0.08, 0.4), 0.16, 0.3, 
                               fill=True, alpha=0.7, color='skyblue', 
                               linewidth=1, edgecolor='black'))
        ax1.text(positions[i], 0.55, step, ha='center', va='center', fontsize=10)
        
        if i < len(steps) - 1:
            arrow = FancyArrowPatch((positions[i]+0.08, 0.55), (positions[i+1]-0.08, 0.55),
                                   arrowstyle='->', linewidth=1.5, color='black')
            ax1.add_patch(arrow)
    
    # 2. EEG处理流程
    ax2 = plt.subplot(gs[1, :])
    ax2.set_title('EEG处理流程', fontsize=14)
    ax2.axis('off')
    
    # 创建处理流程
    eeg_steps = ['原始EEG信号', '频段滤波', 'Hilbert变换', '包络提取', '频段包络时间序列']
    positions = np.linspace(0.1, 0.9, len(eeg_steps))
    
    for i, step in enumerate(eeg_steps):
        ax2.add_patch(Rectangle((positions[i]-0.08, 0.6), 0.16, 0.3, 
                               fill=True, alpha=0.7, color='lightgreen', 
                               linewidth=1, edgecolor='black'))
        ax2.text(positions[i], 0.75, step, ha='center', va='center', fontsize=10)
        
        if i < len(eeg_steps) - 1:
            arrow = FancyArrowPatch((positions[i]+0.08, 0.75), (positions[i+1]-0.08, 0.75),
                                   arrowstyle='->', linewidth=1.5, color='black')
            ax2.add_patch(arrow)
    
    # 添加EEG频段
    bands = ['δ (0.5-4 Hz)', 'θ (4-8 Hz)', 'α (8-13 Hz)', 'β (13-30 Hz)', 'γ (30-45 Hz)']
    colors = ['#e6194B', '#3cb44b', '#ffe119', '#4363d8', '#911eb4']
    
    for i, (band, color) in enumerate(zip(bands, colors)):
        y_pos = 0.3 - i * 0.15
        
        # 添加频段框
        ax2.add_patch(Rectangle((positions[3]-0.08, y_pos-0.05), 0.16, 0.1, 
                               fill=True, alpha=0.7, color=color, 
                               linewidth=1, edgecolor='black'))
        ax2.text(positions[3], y_pos, band, ha='center', va='center', fontsize=9)
        
        # 添加箭头连接
        arrow = FancyArrowPatch((positions[2], 0.6), (positions[3], y_pos),
                               arrowstyle='->', linewidth=1, color='gray')
        ax2.add_patch(arrow)
        
        # 添加箭头连接到最终结果
        arrow = FancyArrowPatch((positions[3]+0.08, y_pos), (positions[4]-0.08, 0.75),
                               arrowstyle='->', linewidth=1, color='gray', connectionstyle='arc3,rad=0.3')
        ax2.add_patch(arrow)
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(OUTPUT_DIR, output_file)
    plt.savefig(output_path, dpi=600, bbox_inches='tight')
    print(f"信号处理流程图已保存至: {output_path}")
    plt.close()

def create_ccm_schematic_figure(output_file='figure2_ccm_schematic.png'):
    """
    创建收敛交叉映射(CCM)分析示意图
    
    参数:
    output_file (str): 输出文件名
    """
    print("创建CCM分析示意图...")
    
    fig = plt.figure(figsize=(10, 8))
    gs = gridspec.GridSpec(2, 2, height_ratios=[1, 1.2])
    
    # 1. CCM原理图
    ax1 = plt.subplot(gs[0, :])
    ax1.set_title('收敛交叉映射(CCM)分析原理', fontsize=14)
    ax1.axis('off')
    
    # 创建系统X和系统Y的示意图
    pos_x = 0.2
    pos_y = 0.8
    
    # 系统X (蓝色)
    ax1.add_patch(Rectangle((pos_x-0.1, 0.4), 0.2, 0.3, 
                           fill=True, alpha=0.7, color='skyblue', 
                           linewidth=1, edgecolor='black'))
    ax1.text(pos_x, 0.55, "系统X\n(HR)", ha='center', va='center', fontsize=12)
    
    # 系统Y (绿色)
    ax1.add_patch(Rectangle((pos_y-0.1, 0.4), 0.2, 0.3, 
                           fill=True, alpha=0.7, color='lightgreen', 
                           linewidth=1, edgecolor='black'))
    ax1.text(pos_y, 0.55, "系统Y\n(EEG)", ha='center', va='center', fontsize=12)
    
    # 添加Y→X箭头 (因果方向)
    arrow_y_to_x = FancyArrowPatch((pos_y-0.1, 0.55), (pos_x+0.1, 0.55),
                                 arrowstyle='<-', linewidth=2, color='red',
                                 connectionstyle='arc3,rad=0.3')
    ax1.add_patch(arrow_y_to_x)
    ax1.text((pos_x+pos_y)/2, 0.65, "Y→X因果影响\n(心→脑方向)", ha='center', va='center', fontsize=10, color='red')
    
    # 添加X→Y箭头 (因果方向)
    arrow_x_to_y = FancyArrowPatch((pos_x+0.1, 0.45), (pos_y-0.1, 0.45),
                                 arrowstyle='->', linewidth=2, color='blue',
                                 connectionstyle='arc3,rad=0.3')
    ax1.add_patch(arrow_x_to_y)
    ax1.text((pos_x+pos_y)/2, 0.35, "X→Y因果影响\n(脑→心方向)", ha='center', va='center', fontsize=10, color='blue')
    
    # 2. CCM分析结果示例
    ax2 = plt.subplot(gs[1, 0])
    ax2.set_title('CCM分析收敛曲线示例', fontsize=12)
    
    # 创建示例收敛曲线
    library_sizes = np.linspace(50, 500, 10)
    rho_values_x_to_y = 0.8 * (1 - np.exp(-0.005 * library_sizes))
    rho_values_y_to_x = 0.4 * (1 - np.exp(-0.003 * library_sizes))
    
    ax2.plot(library_sizes, rho_values_x_to_y, 'o-', color='blue', label='X→Y (脑→心)')
    ax2.plot(library_sizes, rho_values_y_to_x, 's-', color='red', label='Y→X (心→脑)')
    
    ax2.set_xlabel('库大小 (L)')
    ax2.set_ylabel('预测技能 (ρ)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 典型因果强度比较
    ax3 = plt.subplot(gs[1, 1])
    ax3.set_title('典型焦虑水平组因果强度对比', fontsize=12)
    
    # 模拟不同组的因果强度
    groups = ['低焦虑', '中等焦虑', '高焦虑']
    x_to_y_values = [0.45, 0.55, 0.75]  # 脑→心
    y_to_x_values = [0.25, 0.35, 0.30]  # 心→脑
    
    # 设置条形图位置
    bar_width = 0.35
    x = np.arange(len(groups))
    
    ax3.bar(x - bar_width/2, x_to_y_values, bar_width, color='blue', label='脑→心')
    ax3.bar(x + bar_width/2, y_to_x_values, bar_width, color='red', label='心→脑')
    
    ax3.set_xlabel('焦虑水平组')
    ax3.set_ylabel('因果强度 (ρ)')
    ax3.set_xticks(x)
    ax3.set_xticklabels(groups)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(OUTPUT_DIR, output_file)
    plt.savefig(output_path, dpi=600, bbox_inches='tight')
    print(f"CCM分析示意图已保存至: {output_path}")
    plt.close()

def create_combined_result_figure(results, band='alpha', output_file='figure3_combined_results.png'):
    """
    创建组合结果图
    
    参数:
    results (dict): CCM分析结果
    band (str): 频段名称
    output_file (str): 输出文件名
    """
    if not results:
        print("错误: 未提供有效的分析结果")
        return
    
    # 检查是否有指定频段的数据
    band_exists = False
    for subject_id, subject_data in results.items():
        ccm_results = subject_data['ccm_results']
        for key in ccm_results.keys():
            if key.endswith(f"_{band}"):
                band_exists = True
                break
        if band_exists:
            break
    
    if not band_exists:
        print(f"警告: 未找到{band}频段的数据，跳过创建组合结果图")
        return
    
    print(f"创建{band}频段组合结果图...")
    
    fig = plt.figure(figsize=(15, 10))
    gs = gridspec.GridSpec(2, 2, height_ratios=[1, 1])
    
    # 1. 脑电通道图
    ax1 = plt.subplot(gs[0, 0])
    ax1.set_title(f'脑电通道位置与{band}频段因果强度', fontsize=12)
    
    # 创建简化的头部轮廓
    circle = plt.Circle((0.5, 0.5), 0.4, fill=False, linewidth=2, color='black')
    ax1.add_patch(circle)
    
    # 画鼻子和耳朵表示方向
    ax1.plot([0.5, 0.5], [0.9, 0.95], 'k-', linewidth=2)  # 鼻子
    ax1.plot([0.1, 0.05], [0.5, 0.5], 'k-', linewidth=2)  # 左耳
    ax1.plot([0.9, 0.95], [0.5, 0.5], 'k-', linewidth=2)  # 右耳
    
    # 定义常用电极位置（简化版）
    electrodes = {
        'Fp1': (0.4, 0.85), 'Fp2': (0.6, 0.85),
        'F7': (0.2, 0.7), 'F3': (0.35, 0.7), 'Fz': (0.5, 0.7), 'F4': (0.65, 0.7), 'F8': (0.8, 0.7),
        'T7': (0.1, 0.5), 'C3': (0.35, 0.5), 'Cz': (0.5, 0.5), 'C4': (0.65, 0.5), 'T8': (0.9, 0.5),
        'P7': (0.2, 0.3), 'P3': (0.35, 0.3), 'Pz': (0.5, 0.3), 'P4': (0.65, 0.3), 'P8': (0.8, 0.3),
        'O1': (0.4, 0.15), 'Oz': (0.5, 0.15), 'O2': (0.6, 0.15)
    }
    
    # 收集所有通道的平均因果强度
    eeg_to_hr_strengths = {}
    hr_to_eeg_strengths = {}
    
    for subject_id, subject_data in results.items():
        ccm_results = subject_data['ccm_results']
        
        for key, result in ccm_results.items():
            if key.endswith(f"_{band}"):
                channel = result['channel']
                if channel in electrodes:
                    # 计算最大库大小的因果强度
                    eeg_to_hr = result['eeg_to_hr'][-1] if not np.isnan(result['eeg_to_hr'][-1]) else 0
                    hr_to_eeg = result['hr_to_eeg'][-1] if not np.isnan(result['hr_to_eeg'][-1]) else 0
                    
                    if channel not in eeg_to_hr_strengths:
                        eeg_to_hr_strengths[channel] = []
                    if channel not in hr_to_eeg_strengths:
                        hr_to_eeg_strengths[channel] = []
                    
                    eeg_to_hr_strengths[channel].append(eeg_to_hr)
                    hr_to_eeg_strengths[channel].append(hr_to_eeg)
    
    # 计算平均值
    avg_eeg_to_hr = {ch: np.mean(vals) for ch, vals in eeg_to_hr_strengths.items() if vals}
    avg_hr_to_eeg = {ch: np.mean(vals) for ch, vals in hr_to_eeg_strengths.items() if vals}
    
    # 标准化因果强度以便可视化
    max_strength = max(max(avg_eeg_to_hr.values(), default=0), max(avg_hr_to_eeg.values(), default=0))
    min_strength = min(min(avg_eeg_to_hr.values(), default=0), min(avg_hr_to_eeg.values(), default=0))
    range_strength = max(max_strength - min_strength, 0.1)
    
    # 绘制电极位置和因果强度
    for channel, (x, y) in electrodes.items():
        eeg_to_hr_val = avg_eeg_to_hr.get(channel, 0)
        hr_to_eeg_val = avg_hr_to_eeg.get(channel, 0)
        
        # 绘制电极点
        if channel in avg_eeg_to_hr or channel in avg_hr_to_eeg:
            # 使用pie图表示双向因果强度
            size = 800  # 基础大小
            
            # 只有当值有效时才添加
            if eeg_to_hr_val > 0 or hr_to_eeg_val > 0:
                vals = [eeg_to_hr_val, hr_to_eeg_val]
                colors = ['blue', 'red']
                ax1.pie(vals, colors=colors, center=(x, y), radius=0.05 * (np.sum(vals) / range_strength))
                
            ax1.text(x, y-0.08, channel, ha='center', va='center', fontsize=8)
        else:
            # 绘制不包含数据的电极点
            ax1.plot(x, y, 'o', markersize=3, color='gray', alpha=0.5)
            ax1.text(x, y-0.05, channel, ha='center', va='center', fontsize=8, alpha=0.5)
    
    # 添加图例
    blue_patch = Patch(color='blue', label='脑→心')
    red_patch = Patch(color='red', label='心→脑')
    ax1.legend(handles=[blue_patch, red_patch], loc='upper right')
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.set_aspect('equal')
    ax1.axis('off')
    
    # 2. HEP通道热图
    # 简单从结果数据中提取
    # (实际论文中可能会有更详细的数据)
    
    # 3 & 4. 组间比较
    # 使用与上述类似的方法可视化组间比较
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(OUTPUT_DIR, output_file)
    plt.savefig(output_path, dpi=600, bbox_inches='tight')
    print(f"组合结果图已保存至: {output_path}")
    plt.close()

def main():
    """
    主函数
    """
    print("=" * 80)
    print("脑-心非线性交互分析论文级图表生成")
    print("=" * 80)
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 1. 创建信号处理流程图
    create_processing_pipeline_figure()
    
    # 2. 创建CCM分析示意图
    create_ccm_schematic_figure()
    
    # 3. 创建组合结果图
    results = load_results()
    if results:
        # 确定有效的频段
        valid_bands = set()
        for subject_id, subject_data in results.items():
            ccm_results = subject_data['ccm_results']
            for key in ccm_results.keys():
                parts = key.split('_')
                if len(parts) > 1:
                    band = parts[-1]
                    valid_bands.add(band)
        
        for band in valid_bands:
            create_combined_result_figure(results, band)
    
    print("\n论文级图表生成完成!")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据标准化模块

功能：
- 测试数据标准化模块的各个功能
- 验证数据标准化的正确性
- 输出测试结果

作者：AI助手
日期：2024年
"""

import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from data_loader import load_hep_data, DataLoadError
from channel_processor import process_channels, ChannelProcessError
from data_normalizer import (
    normalize_data, z_score_normalize, min_max_normalize, robust_normalize,
    handle_outliers, DataNormalizeError
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_data_normalizer_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-DataNormalizer")

def test_z_score_normalize():
    """
    测试Z-score标准化

    返回:
    bool: 测试结果
    """
    logger.info("测试Z-score标准化...")

    try:
        # 创建测试数据
        test_data = np.random.normal(5, 2, size=(10, 5, 100))

        # 应用Z-score标准化
        normalized_data = z_score_normalize(test_data, axis=0)

        # 验证结果
        mean = np.mean(normalized_data, axis=0)
        std = np.std(normalized_data, axis=0)

        # 均值应接近0，标准差应接近1
        if np.allclose(mean, 0, atol=1e-10) and np.allclose(std, 1, atol=1e-10):
            logger.info("Z-score标准化测试成功")
            return True
        else:
            logger.error(f"Z-score标准化测试失败: 均值={np.mean(mean):.4f}, 标准差={np.mean(std):.4f}")
            return False

    except Exception as e:
        logger.error(f"Z-score标准化测试过程中发生错误: {str(e)}")
        return False

def test_min_max_normalize():
    """
    测试Min-Max标准化

    返回:
    bool: 测试结果
    """
    logger.info("测试Min-Max标准化...")

    try:
        # 创建测试数据
        test_data = np.random.normal(5, 2, size=(10, 5, 100))

        # 应用Min-Max标准化
        normalized_data = min_max_normalize(test_data, axis=0)

        # 验证结果
        min_val = np.min(normalized_data, axis=0)
        max_val = np.max(normalized_data, axis=0)

        # 最小值应接近0，最大值应接近1
        if np.allclose(min_val, 0, atol=1e-10) and np.allclose(max_val, 1, atol=1e-10):
            logger.info("Min-Max标准化测试成功")
            return True
        else:
            logger.error(f"Min-Max标准化测试失败: 最小值={np.mean(min_val):.4f}, 最大值={np.mean(max_val):.4f}")
            return False

    except Exception as e:
        logger.error(f"Min-Max标准化测试过程中发生错误: {str(e)}")
        return False

def test_robust_normalize():
    """
    测试稳健标准化

    返回:
    bool: 测试结果
    """
    logger.info("测试稳健标准化...")

    try:
        # 创建测试数据（包含异常值）
        test_data = np.random.normal(5, 2, size=(10, 5, 100))
        test_data[0, 0, 0] = 100  # 添加异常值

        # 应用稳健标准化
        normalized_data = robust_normalize(test_data, axis=0)

        # 验证结果
        median = np.median(normalized_data, axis=0)

        # 中位数应接近0
        if np.allclose(median, 0, atol=0.1):
            logger.info("稳健标准化测试成功")
            return True
        else:
            logger.error(f"稳健标准化测试失败: 中位数={np.mean(median):.4f}")
            return False

    except Exception as e:
        logger.error(f"稳健标准化测试过程中发生错误: {str(e)}")
        return False

def test_handle_outliers():
    """
    测试异常值处理

    返回:
    bool: 测试结果
    """
    logger.info("测试异常值处理...")

    try:
        # 创建测试数据（包含异常值）
        test_data = np.random.normal(0, 1, size=1000)
        test_data[0] = 10  # 添加异常值

        # 应用异常值处理（截断）
        clipped_data = handle_outliers(test_data, threshold=3.0, method='clip')

        # 验证结果
        if np.max(clipped_data) <= np.mean(test_data) + 3 * np.std(test_data):
            logger.info("异常值处理（截断）测试成功")
            clip_success = True
        else:
            logger.error(f"异常值处理（截断）测试失败: 最大值={np.max(clipped_data):.4f}")
            clip_success = False

        # 应用异常值处理（移除）
        removed_data = handle_outliers(test_data, threshold=3.0, method='remove')

        # 验证结果
        if np.max(removed_data) <= np.mean(test_data) + 3 * np.std(test_data):
            logger.info("异常值处理（移除）测试成功")
            remove_success = True
        else:
            logger.error(f"异常值处理（移除）测试失败: 最大值={np.max(removed_data):.4f}")
            remove_success = False

        return clip_success and remove_success

    except Exception as e:
        logger.error(f"异常值处理测试过程中发生错误: {str(e)}")
        return False

def test_normalize_data(stages_data):
    """
    测试数据标准化

    参数:
    stages_data (dict): 各阶段数据

    返回:
    bool: 测试结果
    """
    logger.info("测试数据标准化...")

    try:
        # 测试Z-score标准化
        logger.info("测试Z-score标准化...")
        z_score_data = normalize_data(stages_data, method='z_score')

        # 验证结果
        for stage, data in z_score_data.items():
            eeg_mean = np.mean(data['eeg'])
            eeg_std = np.std(data['eeg'])
            ecg_mean = np.mean(data['ecg'])
            ecg_std = np.std(data['ecg'])

            logger.info(f"阶段 {stage} 的Z-score标准化结果:")
            logger.info(f"  - EEG: 均值={eeg_mean:.4f}, 标准差={eeg_std:.4f}")
            logger.info(f"  - ECG: 均值={ecg_mean:.4f}, 标准差={ecg_std:.4f}")

        # 测试Min-Max标准化
        logger.info("测试Min-Max标准化...")
        min_max_data = normalize_data(stages_data, method='min_max')

        # 验证结果
        for stage, data in min_max_data.items():
            eeg_min = np.min(data['eeg'])
            eeg_max = np.max(data['eeg'])
            ecg_min = np.min(data['ecg'])
            ecg_max = np.max(data['ecg'])

            logger.info(f"阶段 {stage} 的Min-Max标准化结果:")
            logger.info(f"  - EEG: 最小值={eeg_min:.4f}, 最大值={eeg_max:.4f}")
            logger.info(f"  - ECG: 最小值={ecg_min:.4f}, 最大值={ecg_max:.4f}")

        # 测试稳健标准化
        logger.info("测试稳健标准化...")
        robust_data = normalize_data(stages_data, method='robust')

        # 验证结果
        for stage, data in robust_data.items():
            eeg_median = np.median(data['eeg'])
            ecg_median = np.median(data['ecg'])

            logger.info(f"阶段 {stage} 的稳健标准化结果:")
            logger.info(f"  - EEG: 中位数={eeg_median:.4f}")
            logger.info(f"  - ECG: 中位数={ecg_median:.4f}")

        # 测试异常值处理
        logger.info("测试带异常值处理的标准化...")
        outlier_data = normalize_data(stages_data, method='z_score', handle_outliers_method='clip', outlier_threshold=3.0)

        logger.info("数据标准化测试成功")
        return True

    except DataNormalizeError as e:
        logger.error(f"数据标准化失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

def visualize_normalization_results(stages_data, stage='rest1', channel_idx=0, epoch_idx=0):
    """
    可视化标准化结果

    参数:
    stages_data (dict): 各阶段数据
    stage (str): 要可视化的阶段
    channel_idx (int): 要可视化的通道索引
    epoch_idx (int): 要可视化的epoch索引

    返回:
    bool: 可视化结果
    """
    logger.info(f"可视化标准化结果: 阶段={stage}, 通道索引={channel_idx}, Epoch索引={epoch_idx}")

    try:
        # 设置中文字体
        import matplotlib.font_manager as fm
        # 使用指定的字体路径
        font_path = r"C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf"

        if os.path.exists(font_path):
            chinese_font = fm.FontProperties(fname=font_path, size=10)
            logger.info(f"使用中文字体: {font_path}")
        else:
            logger.warning(f"指定的字体文件不存在: {font_path}，将使用系统默认字体")
            chinese_font = None

        # 获取原始数据
        original_data = stages_data[stage]['eeg'][epoch_idx, channel_idx, :]
        channel_name = stages_data[stage]['eeg_channels'][channel_idx]

        # 应用不同的标准化方法
        z_score_data = z_score_normalize(original_data)
        min_max_data = min_max_normalize(original_data)
        robust_data = robust_normalize(original_data)
        clipped_data = handle_outliers(original_data, threshold=3.0, method='clip')

        # 创建图形
        plt.figure(figsize=(12, 8))

        # 设置全局字体
        plt.rcParams['font.size'] = 10

        # 绘制原始数据
        plt.subplot(3, 2, 1)
        plt.plot(original_data)
        if chinese_font:
            plt.title('原始数据 (Original Data)', fontproperties=chinese_font)
            plt.xlabel('时间点 (Time Point)', fontproperties=chinese_font)
            plt.ylabel('振幅 (Amplitude)', fontproperties=chinese_font)
        else:
            plt.title('原始数据 (Original Data)')
            plt.xlabel('时间点 (Time Point)')
            plt.ylabel('振幅 (Amplitude)')
        plt.grid(True)

        # 绘制Z-score标准化结果
        plt.subplot(3, 2, 2)
        plt.plot(z_score_data)
        if chinese_font:
            plt.title('Z-score标准化 (Z-score Normalization)', fontproperties=chinese_font)
            plt.xlabel('时间点 (Time Point)', fontproperties=chinese_font)
            plt.ylabel('标准化振幅 (Normalized Amplitude)', fontproperties=chinese_font)
        else:
            plt.title('Z-score标准化 (Z-score Normalization)')
            plt.xlabel('时间点 (Time Point)')
            plt.ylabel('标准化振幅 (Normalized Amplitude)')
        plt.grid(True)

        # 绘制Min-Max标准化结果
        plt.subplot(3, 2, 3)
        plt.plot(min_max_data)
        if chinese_font:
            plt.title('Min-Max标准化 (Min-Max Normalization)', fontproperties=chinese_font)
            plt.xlabel('时间点 (Time Point)', fontproperties=chinese_font)
            plt.ylabel('标准化振幅 (Normalized Amplitude)', fontproperties=chinese_font)
        else:
            plt.title('Min-Max标准化 (Min-Max Normalization)')
            plt.xlabel('时间点 (Time Point)')
            plt.ylabel('标准化振幅 (Normalized Amplitude)')
        plt.grid(True)

        # 绘制稳健标准化结果
        plt.subplot(3, 2, 4)
        plt.plot(robust_data)
        if chinese_font:
            plt.title('稳健标准化 (Robust Normalization)', fontproperties=chinese_font)
            plt.xlabel('时间点 (Time Point)', fontproperties=chinese_font)
            plt.ylabel('标准化振幅 (Normalized Amplitude)', fontproperties=chinese_font)
        else:
            plt.title('稳健标准化 (Robust Normalization)')
            plt.xlabel('时间点 (Time Point)')
            plt.ylabel('标准化振幅 (Normalized Amplitude)')
        plt.grid(True)

        # 绘制异常值处理结果
        plt.subplot(3, 2, 5)
        plt.plot(clipped_data)
        if chinese_font:
            plt.title('异常值处理（截断） (Outlier Handling - Clipping)', fontproperties=chinese_font)
            plt.xlabel('时间点 (Time Point)', fontproperties=chinese_font)
            plt.ylabel('处理后振幅 (Processed Amplitude)', fontproperties=chinese_font)
        else:
            plt.title('异常值处理（截断） (Outlier Handling - Clipping)')
            plt.xlabel('时间点 (Time Point)')
            plt.ylabel('处理后振幅 (Processed Amplitude)')
        plt.grid(True)

        # 绘制直方图
        plt.subplot(3, 2, 6)
        plt.hist(original_data, bins=30, alpha=0.5, label='原始 (Original)')
        plt.hist(z_score_data, bins=30, alpha=0.5, label='Z-score')
        if chinese_font:
            plt.legend(prop=chinese_font)
            plt.title('数据分布 (Data Distribution)', fontproperties=chinese_font)
            plt.xlabel('振幅值 (Amplitude Value)', fontproperties=chinese_font)
            plt.ylabel('频数 (Frequency)', fontproperties=chinese_font)
        else:
            plt.legend()
            plt.title('数据分布 (Data Distribution)')
            plt.xlabel('振幅值 (Amplitude Value)')
            plt.ylabel('频数 (Frequency)')
        plt.grid(True)

        # 添加总标题
        if chinese_font:
            plt.suptitle(f'通道 {channel_name} 的标准化结果对比 (Normalization Results for Channel {channel_name})',
                        fontproperties=chinese_font, fontsize=12)
        else:
            plt.suptitle(f'通道 {channel_name} 的标准化结果对比 (Normalization Results for Channel {channel_name})',
                        fontsize=12)

        # 调整布局
        plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为总标题留出空间

        # 保存图形
        output_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\normalization"
        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, f"normalization_results_{stage}_ch{channel_idx}_ep{epoch_idx}.png"), dpi=300)

        logger.info(f"可视化结果已保存至: {output_dir}")
        return True

    except Exception as e:
        logger.error(f"可视化过程中发生错误: {str(e)}")
        return False

def run_all_tests():
    """
    运行所有测试
    """
    logger.info("开始运行所有测试...")

    # 运行基本测试
    test1_result = test_z_score_normalize()
    test2_result = test_min_max_normalize()
    test3_result = test_robust_normalize()
    test4_result = test_handle_outliers()

    try:
        # 加载数据
        data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
        stages_data = load_hep_data(data_dir)

        # 为了加快测试速度，只使用一个阶段的数据
        test_stage = 'rest1'
        if test_stage in stages_data:
            test_data = {test_stage: stages_data[test_stage]}
            logger.info(f"使用阶段 {test_stage} 的数据进行测试")
        else:
            test_data = stages_data
            logger.info("使用所有阶段的数据进行测试")

        # 处理通道
        key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
        processed_data = process_channels(test_data, key_channels)

        # 测试数据标准化
        test5_result = test_normalize_data(processed_data)

        # 可视化标准化结果
        test6_result = visualize_normalization_results(processed_data, stage=test_stage, channel_idx=0, epoch_idx=0)

    except (DataLoadError, ChannelProcessError) as e:
        logger.error(f"数据准备失败: {str(e)}")
        test5_result = False
        test6_result = False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        test5_result = False
        test6_result = False

    # 输出总结
    logger.info("测试结果总结:")
    logger.info(f"  - Z-score标准化测试: {'通过' if test1_result else '失败'}")
    logger.info(f"  - Min-Max标准化测试: {'通过' if test2_result else '失败'}")
    logger.info(f"  - 稳健标准化测试: {'通过' if test3_result else '失败'}")
    logger.info(f"  - 异常值处理测试: {'通过' if test4_result else '失败'}")
    logger.info(f"  - 数据标准化测试: {'通过' if test5_result else '失败'}")
    logger.info(f"  - 可视化测试: {'通过' if test6_result else '失败'}")

    # 总体结果
    overall_result = test1_result and test2_result and test3_result and test4_result and test5_result and test6_result
    logger.info(f"总体测试结果: {'通过' if overall_result else '失败'}")

    return overall_result

if __name__ == "__main__":
    run_all_tests()

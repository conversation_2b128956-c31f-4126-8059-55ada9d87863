#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 模型定义和训练模块

功能：
- 定义CausalFormer模型架构
- 实现模型训练和验证流程
- 提供模型评估和可视化功能

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import logging
from datetime import datetime
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import seaborn as sns
from matplotlib.gridspec import GridSpec
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-Model")

# 设置中文字体
try:
    chinese_font_path = r'C:\Users\<USER>\Desktop\BaiduSyncdisk\桌面环境\霞鹜文楷\LXGWWenKai-Medium.ttf'
    if os.path.exists(chinese_font_path):
        chinese_font = fm.FontProperties(fname=chinese_font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['LXGW WenKai', 'SimHei', 'Arial Unicode MS']
        logger.info("成功加载中文字体: LXGW WenKai")
    else:
        chinese_font = fm.FontProperties(family='SimHei')
        logger.warning("未找到LXGW WenKai字体，使用SimHei替代")
except Exception as e:
    chinese_font = fm.FontProperties(family='SimHei')
    logger.warning(f"加载中文字体失败: {str(e)}，使用SimHei替代")

class ModelError(Exception):
    """模型错误类"""
    pass

class PositionalEncoding(nn.Module):
    """
    位置编码模块
    """
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:, :x.size(1), :]

class CausalSelfAttention(nn.Module):
    """
    因果自注意力模块
    """
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(CausalSelfAttention, self).__init__()
        
        self.n_heads = n_heads
        self.d_model = d_model
        self.head_dim = d_model // n_heads
        
        self.query = nn.Linear(d_model, d_model)
        self.key = nn.Linear(d_model, d_model)
        self.value = nn.Linear(d_model, d_model)
        self.out = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None, return_attention=False):
        batch_size = x.size(0)
        seq_len = x.size(1)
        
        # 线性变换
        q = self.query(x).view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        k = self.key(x).view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        v = self.value(x).view(batch_size, seq_len, self.n_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        # 应用因果掩码
        if mask is None:
            mask = torch.triu(torch.ones(seq_len, seq_len), diagonal=1).bool()
            mask = mask.to(x.device)
        
        scores = scores.masked_fill(mask, -1e9)
        
        # 应用softmax
        attn_weights = torch.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        out = self.out(out)
        
        if return_attention:
            return out, attn_weights
        return out

class CausalFormerBlock(nn.Module):
    """
    CausalFormer块
    """
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(CausalFormerBlock, self).__init__()
        
        self.attn = CausalSelfAttention(d_model, n_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.ff = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
    
    def forward(self, x, mask=None, return_attention=False):
        # 自注意力层
        if return_attention:
            attn_out, attn_weights = self.attn(x, mask, return_attention=True)
            x = self.norm1(x + attn_out)
        else:
        attn_out = self.attn(x, mask)
        x = self.norm1(x + attn_out)
        
        # 前馈层
        ff_out = self.ff(x)
        x = self.norm2(x + ff_out)
        
        if return_attention:
            return x, attn_weights
        return x

class CausalFormer(nn.Module):
    """
    CausalFormer模型 - 用于EEG到ECG的因果预测
    
    功能:
    - 基于Transformer架构的因果时间序列预测
    - 使用因果掩码确保预测时只使用过去信息
    - 支持注意力权重可视化以分析因果关系
    
    参数:
    - input_dim: 输入特征维度（EEG特征数）
    - output_dim: 输出特征维度（ECG特征数）
    - d_model: 模型内部表示维度
    - n_heads: 多头注意力头数
    - n_layers: Transformer层数
    - d_ff: 前馈网络隐藏层维度
    - dropout: Dropout比例
    """
    def __init__(self, input_dim, output_dim, d_model=128, n_heads=8, n_layers=4, d_ff=512, dropout=0.2):
        super(CausalFormer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.d_model = d_model
        
        # 输入投影
        self.input_proj = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer层
        self.layers = nn.ModuleList([
            CausalFormerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        # 输出投影 - 增加层级，提高模型表达能力
        self.output_proj = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, output_dim)
        )
        
        # 保存中间注意力权重用于可视化
        self.attention_weights = None
        
        self.dropout = nn.Dropout(dropout)
    
        # 初始化参数
        self._init_parameters()
    
    def _init_parameters(self):
        """初始化模型参数，提高训练稳定性"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x, mask=None, return_attention=False):
        """
        前向传播
        
        参数:
        x (Tensor): 输入张量，形状为 [batch_size, seq_len, input_dim]
        mask (Tensor): 掩码张量，默认为None，使用因果掩码
        return_attention (bool): 是否返回注意力权重，用于可视化
        
        返回:
        Tensor: 输出张量，形状为 [batch_size, seq_len, output_dim]
        dict (可选): 注意力权重字典，形状为 {layer_idx: [batch_size, n_heads, seq_len, seq_len]}
        """
        # 输入投影
        x = self.input_proj(x)
        
        # 位置编码
        x = self.pos_encoder(x)
        
        # Dropout
        x = self.dropout(x)
        
        # Transformer层
        all_attentions = {}
        for i, layer in enumerate(self.layers):
            if return_attention:
                x, attn_weights = layer(x, mask, return_attention=True)
                all_attentions[f"layer_{i}"] = attn_weights
            else:
            x = layer(x, mask)
        
        # 输出投影
        x = self.output_proj(x)
        
        if return_attention:
            self.attention_weights = all_attentions
            return x, all_attentions
        return x

def train_model(model, train_loader, val_loader, epochs=30, lr=0.001, device='cuda', model_dir=None):
    """
    训练CausalFormer模型
    
    参数:
    model (nn.Module): CausalFormer模型
    train_loader (DataLoader): 训练数据加载器
    val_loader (DataLoader): 验证数据加载器
    epochs (int): 训练轮数
    lr (float): 学习率
    device (str): 设备，'cuda'或'cpu'
    model_dir (str): 模型保存目录
    
    返回:
    dict: 训练历史记录
    """
    if model_dir is None:
        model_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\models")
    
    os.makedirs(model_dir, exist_ok=True)
    
    # 检查设备
    if device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA不可用，使用CPU")
        device = 'cpu'
    
    device = torch.device(device)
    model = model.to(device)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)
    
    # 训练历史记录
    history = {
        'train_loss': [],
        'val_loss': [],
        'lr': []
    }
    
    # 最佳模型保存
    best_val_loss = float('inf')
    best_model_path = os.path.join(model_dir, 'best_model.pth')
    
    logger.info(f"开始训练: epochs={epochs}, lr={lr}, device={device}")
    
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_steps = 0
        
        for X_batch, y_batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs} [Train]"):
            X_batch = X_batch.to(device)
            y_batch = y_batch.to(device)
            
            # 前向传播
            outputs = model(X_batch)
            loss = criterion(outputs, y_batch)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
        
        avg_train_loss = train_loss / train_steps
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_steps = 0
        
        with torch.no_grad():
            for X_batch, y_batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{epochs} [Val]"):
                X_batch = X_batch.to(device)
                y_batch = y_batch.to(device)
                
                # 前向传播
                outputs = model(X_batch)
                loss = criterion(outputs, y_batch)
                
                val_loss += loss.item()
                val_steps += 1
        
        avg_val_loss = val_loss / val_steps
        
        # 更新学习率
        scheduler.step(avg_val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 更新历史记录
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['lr'].append(current_lr)
        
        logger.info(f"Epoch {epoch+1}/{epochs} - Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}, LR: {current_lr:.6f}")
        
        # 保存最佳模型
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            torch.save(model.state_dict(), best_model_path)
            logger.info(f"保存最佳模型: Val Loss = {best_val_loss:.6f}")
    
    logger.info(f"训练完成: 最佳Val Loss = {best_val_loss:.6f}")
    
    # 加载最佳模型
    model.load_state_dict(torch.load(best_model_path))
    
    return history, model

def evaluate_model(model, test_loader, device='cuda'):
    """
    评估CausalFormer模型
    
    参数:
    model (nn.Module): CausalFormer模型
    test_loader (DataLoader): 测试数据加载器
    device (str): 设备，'cuda'或'cpu'
    
    返回:
    dict: 评估结果
    """
    # 检查设备
    if device == 'cuda' and not torch.cuda.is_available():
        logger.warning("CUDA不可用，使用CPU")
        device = 'cpu'
    
    device = torch.device(device)
    model = model.to(device)
    
    # 评估模式
    model.eval()
    
    # 收集预测和真实值
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for X_batch, y_batch in tqdm(test_loader, desc="Evaluating"):
            X_batch = X_batch.to(device)
            
            # 前向传播
            outputs = model(X_batch)
            
            # 收集预测和真实值
            all_preds.append(outputs.cpu().numpy())
            all_targets.append(y_batch.cpu().numpy())
    
    # 合并批次
    all_preds = np.concatenate(all_preds, axis=0)
    all_targets = np.concatenate(all_targets, axis=0)
    
    # 计算评估指标
    mse = mean_squared_error(all_targets.reshape(-1), all_preds.reshape(-1))
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(all_targets.reshape(-1), all_preds.reshape(-1))
    r2 = r2_score(all_targets.reshape(-1), all_preds.reshape(-1))
    
    logger.info(f"模型评估结果:")
    logger.info(f"  - MSE: {mse:.6f}")
    logger.info(f"  - RMSE: {rmse:.6f}")
    logger.info(f"  - MAE: {mae:.6f}")
    logger.info(f"  - R²: {r2:.6f}")
    
    return {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'predictions': all_preds,
        'targets': all_targets
    }

def visualize_training_history(history, output_dir=None):
    """
    可视化训练历史记录
    
    参数:
    history (dict): 训练历史记录
    output_dir (str): 输出目录
    
    返回:
    str: 保存的图像文件路径
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\training")
    
    os.makedirs(output_dir, exist_ok=True)
    
    plt.figure(figsize=(15, 10))
    
    # 绘制损失曲线
    plt.subplot(2, 1, 1)
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title('训练和验证损失 / Training and Validation Loss', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('轮次 / Epoch', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('损失 / Loss', fontproperties=chinese_font, fontsize=10)
    plt.legend(prop=chinese_font)
    plt.grid(True)
    
    # 绘制学习率曲线
    plt.subplot(2, 1, 2)
    plt.plot(history['lr'])
    plt.title('学习率变化 / Learning Rate', fontproperties=chinese_font, fontsize=10)
    plt.xlabel('轮次 / Epoch', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('学习率 / Learning Rate', fontproperties=chinese_font, fontsize=10)
    plt.grid(True)
    
    plt.tight_layout()
    
    # 保存图像
    output_file = os.path.join(output_dir, "training_history.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"训练历史可视化结果已保存到: {output_file}")
    
    plt.close()
    
    return output_file

def visualize_attention_weights(model, test_loader, output_dir=None):
    """
    可视化注意力权重，用于分析因果关系
    
    参数:
    model (nn.Module): CausalFormer模型
    test_loader (DataLoader): 测试数据加载器
    output_dir (str): 输出目录
    
    返回:
    dict: 注意力可视化结果
    """
    if output_dir is None:
        output_dir = os.path.join(r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\attention")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 确保模型处于评估模式
    model.eval()
    
    # 获取一批数据
    X_batch, y_batch = next(iter(test_loader))
    device = next(model.parameters()).device
    X_batch = X_batch.to(device)
    
    # 前向传播，获取注意力权重
    with torch.no_grad():
        _, attention_weights = model(X_batch, return_attention=True)
    
    # 创建可视化图像
    results = {}
    
    # 1. 创建多头注意力可视化图
    for layer_idx, layer_weights in attention_weights.items():
        # layer_weights形状：[batch_size, n_heads, seq_len, seq_len]
        # 取第一个样本
        sample_weights = layer_weights[0].cpu().numpy()
        n_heads = sample_weights.shape[0]
        seq_len = sample_weights.shape[1]
        
        plt.figure(figsize=(15, 5*n_heads))
        plt.suptitle(f"注意力权重可视化 - {layer_idx} / Attention Weights - {layer_idx}", 
                    fontproperties=chinese_font, fontsize=12)
        
        for head in range(n_heads):
            plt.subplot(n_heads, 1, head+1)
            im = plt.imshow(sample_weights[head], cmap='viridis', aspect='auto')
            plt.colorbar(im)
            plt.title(f"注意力头 {head+1} / Attention Head {head+1}", fontproperties=chinese_font, fontsize=10)
            plt.xlabel('输出位置 / Output Position', fontproperties=chinese_font, fontsize=10)
            plt.ylabel('输入位置 / Input Position', fontproperties=chinese_font, fontsize=10)
        
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        output_file = os.path.join(output_dir, f"attention_weights_{layer_idx}.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        results[layer_idx] = output_file
        logger.info(f"注意力权重可视化已保存到: {output_file}")
    
    # 2. 创建平均注意力可视化图
    plt.figure(figsize=(10, 8))
    
    # 计算所有层、所有头的平均注意力
    all_weights = []
    for layer_weights in attention_weights.values():
        all_weights.append(layer_weights[0].mean(dim=0).cpu().numpy())
    
    avg_weights = np.mean(all_weights, axis=0)
    
    im = plt.imshow(avg_weights, cmap='viridis', aspect='auto')
    plt.colorbar(im)
    plt.title("平均注意力权重 / Average Attention Weights", fontproperties=chinese_font, fontsize=10)
    plt.xlabel('输出位置 / Output Position', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('输入位置 / Input Position', fontproperties=chinese_font, fontsize=10)
    
    # 添加因果关系标注
    plt.axline((0, 0), slope=1, color='red', linestyle='--', alpha=0.5)
    plt.text(seq_len/2, seq_len/2, "因果边界 / Causal Boundary", 
             color='red', rotation=45, fontproperties=chinese_font, fontsize=10)
    
    output_file = os.path.join(output_dir, "average_attention_weights.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    results["average"] = output_file
    logger.info(f"平均注意力权重可视化已保存到: {output_file}")
    
    # 3. 创建注意力流图
    plt.figure(figsize=(12, 6))
    
    # 计算每个时间步的平均注意力分配
    time_importance = avg_weights.sum(axis=0) / seq_len
    
    plt.plot(time_importance, 'b-', linewidth=2)
    plt.fill_between(range(len(time_importance)), 0, time_importance, alpha=0.2)
    plt.title("时间步重要性分布 / Time Step Importance", fontproperties=chinese_font, fontsize=10)
    plt.xlabel('时间步 / Time Step', fontproperties=chinese_font, fontsize=10)
    plt.ylabel('平均注意力 / Average Attention', fontproperties=chinese_font, fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    
    output_file = os.path.join(output_dir, "time_importance.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    results["time_importance"] = output_file
    logger.info(f"时间步重要性分布已保存到: {output_file}")
    
    return results

def test_causal_former_model(model_inputs, stage):
    """测试CausalFormer模型"""
    logger.info("=== 测试CausalFormer模型 ===")
    
    # 获取模型输入
        stage_inputs = model_inputs[stage]
    input_dim = stage_inputs['input_dim']
    output_dim = stage_inputs['output_dim']
        train_loader = stage_inputs['train_loader']
        val_loader = stage_inputs['val_loader']
        test_loader = stage_inputs['test_loader']
        
    # 选择设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    try:
        # 创建模型
        logger.info(f"创建CausalFormer模型: 输入维度={input_dim}, 输出维度={output_dim}")
        # 增加模型容量
        model = CausalFormer(
            input_dim=input_dim,
            output_dim=output_dim,
            d_model=128,      # 增加到128
            n_heads=8,        # 增加到8
            n_layers=4,       # 增加到4
            d_ff=512,         # 增加到512
            dropout=0.2       # 增加到0.2
        )
        
        # 训练模型
        logger.info("训练模型...")
        epochs = 3  # 小设置，仅用于测试
        history = train_model(model, train_loader, val_loader, epochs=epochs, device=device)
        
        # 可视化训练历史
        logger.info("可视化训练历史...")
        visualize_training_history(history)
        
        # 评估模型
        logger.info("评估模型...")
        eval_results = evaluate_model(model, test_loader, device=device)
        
        # 可视化注意力权重
        logger.info("可视化注意力权重...")
        attention_results = visualize_attention_weights(model, test_loader)
        
        logger.info(f"模型评估结果: MSE={eval_results['mse']:.2f}, RMSE={eval_results['rmse']:.2f}, MAE={eval_results['mae']:.2f}, R²={eval_results['r2']:.2f}")
        
        logger.info("CausalFormer模型测试成功")
        return eval_results
    
    except Exception as e:
        logger.error(f"CausalFormer模型测试失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

if __name__ == "__main__":
    # 导入模型输入准备模块
    from model_input_preparer import prepare_model_inputs, features_to_sequences
    
    # 加载特征数据（假设已经提取）
    features_data = {}  # 这里应该加载实际的特征数据
    
    # 将特征转换为序列
    sequence_length = 10
    stride = 1
    sequences_data = features_to_sequences(features_data, sequence_length, stride)
    
    # 准备模型输入
    batch_size = 32
    model_inputs = prepare_model_inputs(sequences_data, batch_size=batch_size)
    
    # 测试模型
    stage = list(model_inputs.keys())[0]  # 选择第一个阶段
    test_causal_former_model(model_inputs, stage)

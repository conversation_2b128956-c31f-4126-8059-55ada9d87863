#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心非线性交互作用分析脚本（带进度报告）

该脚本用于分析EEG和ECG数据之间的非线性交互作用，使用收敛交叉映射（CCM）方法。
包含详细的进度报告功能，适用于长时间运行的分析任务。

主要功能：
1. 加载预处理好的EEG和ECG数据
2. 对EEG数据进行频段分解
3. 使用CCM方法分析EEG频段与ECG之间的非线性交互作用
4. 保存分析结果
5. 生成详细的进度报告

作者：AI助手
日期：2024年
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
from datetime import datetime
import multiprocessing as mp
from tqdm import tqdm
import warnings
import logging
import json
import shutil

# 设置输出目录
OUTPUT_DIR = "D:/ecgeeg/30-数据分析/5-NeuroKit2/result/nonlinear_interaction"
DATA_DIR = "D:/ecgeeg/19-eegecg手动预处理6-ICA3"
PROGRESS_FILE = os.path.join(OUTPUT_DIR, "ccm_analysis_progress.txt")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("CCM_Analysis")

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message="invalid value encountered in true_divide")
warnings.filterwarnings("ignore", category=RuntimeWarning, message="Mean of empty slice")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='脑-心非线性交互作用分析')
    parser.add_argument('--subjects', type=str, default='all', help='要分析的被试ID，用逗号分隔，或"all"表示所有被试')
    parser.add_argument('--stages', type=str, default='all', help='要分析的实验阶段，用逗号分隔，或"all"表示所有阶段')
    parser.add_argument('--channels', type=str, default='all', help='要分析的EEG通道，用逗号分隔，或"all"表示所有通道')
    parser.add_argument('--bands', type=str, default='all', help='要分析的频段，用逗号分隔，或"all"表示所有频段')
    parser.add_argument('--force-recompute', action='store_true', help='强制重新计算所有结果')
    parser.add_argument('--only-analysis', action='store_true', help='只进行分析，不进行可视化')
    parser.add_argument('--only-visualization', action='store_true', help='只进行可视化，不进行分析')
    parser.add_argument('--max-cores', type=int, default=None, help='最大使用的CPU核心数')
    parser.add_argument('--batch-size', type=int, default=10, help='每批处理的被试数量')
    parser.add_argument('--pause-time', type=float, default=1.0, help='批次之间的暂停时间（秒）')
    parser.add_argument('--max-memory', type=int, default=None, help='最大内存使用量（MB）')
    
    return parser.parse_args()

def update_progress(message, overwrite=False):
    """更新进度文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(PROGRESS_FILE, 'a' if not overwrite else 'w') as f:
        f.write(f"[{timestamp}] {message}\n")
    logger.info(message)

def get_subject_ids():
    """获取所有被试ID"""
    subject_ids = []
    for filename in os.listdir(DATA_DIR):
        if filename.endswith('.fif') and 'raw' in filename:
            parts = filename.split('_')
            if len(parts) >= 2:
                subject_id = parts[0]
                if subject_id not in subject_ids and subject_id.isdigit():
                    subject_ids.append(subject_id)
    
    return sorted(subject_ids)

def get_subject_files(subject_id):
    """获取指定被试的所有文件"""
    files = {}
    for filename in os.listdir(DATA_DIR):
        if filename.startswith(f"{subject_id}_") and filename.endswith('.fif'):
            for stage in ['prac', 'test1', 'rest1', 'test2', 'rest2', 'test3', 'rest3']:
                if stage in filename:
                    files[stage] = os.path.join(DATA_DIR, filename)
                    break
    
    return files

def load_data(file_path):
    """加载EEG和ECG数据"""
    try:
        raw = mne.io.read_raw_fif(file_path, preload=True)
        return raw
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None

def extract_frequency_bands(eeg_data, sfreq, bands=None):
    """提取EEG频段"""
    if bands is None:
        bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 45)
        }
    
    band_data = {}
    for band_name, (low_freq, high_freq) in bands.items():
        # 使用MNE的滤波函数
        filtered_data = mne.filter.filter_data(
            eeg_data.copy(), sfreq, low_freq, high_freq,
            method='fir', phase='zero-double'
        )
        band_data[band_name] = filtered_data
    
    return band_data

def compute_ccm(x, y, embed_dim=3, tau=1, lib_sizes=None):
    """
    计算收敛交叉映射（CCM）
    
    参数:
    - x, y: 输入时间序列
    - embed_dim: 嵌入维度
    - tau: 时间延迟
    - lib_sizes: 库大小列表
    
    返回:
    - x_to_y_skill: x预测y的技能
    - y_to_x_skill: y预测x的技能
    """
    try:
        from pyEDM import CCM
        
        # 标准化数据
        x = (x - np.mean(x)) / np.std(x)
        y = (y - np.mean(y)) / np.std(y)
        
        # 创建数据框
        data = pd.DataFrame({'x': x, 'y': y})
        
        # 设置默认库大小
        if lib_sizes is None:
            max_lib_size = min(len(x) - embed_dim * tau, 200)
            lib_sizes = np.linspace(10, max_lib_size, 10, dtype=int)
        
        # 运行CCM
        ccm_result = CCM(
            dataFrame=data,
            E=embed_dim,
            Tp=0,
            columns='x,y',
            target='x',
            libSizes=','.join(map(str, lib_sizes)),
            sample=100,
            random=True,
            replacement=False
        )
        
        # 提取结果
        x_to_y = ccm_result[ccm_result['Library'] == 'x']
        y_to_x = ccm_result[ccm_result['Library'] == 'y']
        
        # 计算预测技能（使用最大库大小的结果）
        x_to_y_skill = x_to_y['rho'].iloc[-1]
        y_to_x_skill = y_to_x['rho'].iloc[-1]
        
        return x_to_y_skill, y_to_x_skill
    
    except Exception as e:
        logger.error(f"CCM计算失败: {e}")
        return np.nan, np.nan

def analyze_subject(subject_id, stages=None, channels=None, bands=None):
    """分析单个被试的数据"""
    logger.info(f"开始分析被试 {subject_id}")
    update_progress(f"开始分析被试 {subject_id}")
    
    # 获取被试文件
    subject_files = get_subject_files(subject_id)
    
    # 设置默认值
    if stages is None or stages == 'all':
        stages = list(subject_files.keys())
    else:
        stages = [s for s in stages if s in subject_files]
    
    if not stages:
        logger.warning(f"被试 {subject_id} 没有可用的实验阶段")
        return []
    
    results = []
    
    # 遍历实验阶段
    for stage in stages:
        if stage not in subject_files:
            logger.warning(f"被试 {subject_id} 缺少阶段 {stage}")
            continue
        
        file_path = subject_files[stage]
        logger.info(f"分析阶段 {stage}: {file_path}")
        update_progress(f"被试 {subject_id} - 分析阶段 {stage}")
        
        # 加载数据
        raw = load_data(file_path)
        if raw is None:
            continue
        
        # 获取采样率
        sfreq = raw.info['sfreq']
        
        # 设置默认通道
        if channels is None or channels == 'all':
            eeg_channels = [ch for ch in raw.ch_names if ch not in ['ECG', 'EOG']]
        else:
            eeg_channels = [ch for ch in channels if ch in raw.ch_names]
        
        # 获取ECG数据
        if 'ECG' not in raw.ch_names:
            logger.warning(f"被试 {subject_id} 阶段 {stage} 缺少ECG通道")
            continue
        
        ecg_data = raw.get_data(picks=['ECG'])[0]
        
        # 遍历EEG通道
        for channel in eeg_channels:
            logger.info(f"分析通道 {channel}")
            update_progress(f"被试 {subject_id} - 阶段 {stage} - 通道 {channel}")
            
            # 获取EEG数据
            eeg_data = raw.get_data(picks=[channel])[0]
            
            # 提取频段
            frequency_bands = extract_frequency_bands(eeg_data, sfreq, bands)
            
            # 遍历频段
            for band_name, band_data in frequency_bands.items():
                logger.info(f"分析频段 {band_name}")
                update_progress(f"被试 {subject_id} - 阶段 {stage} - 通道 {channel} - 频段 {band_name}")
                
                # 计算CCM
                brain_to_heart, heart_to_brain = compute_ccm(band_data, ecg_data)
                
                # 保存结果
                result = {
                    'subject_id': subject_id,
                    'stage': stage,
                    'channel': channel,
                    'frequency_band': band_name,
                    'brain_to_heart_ccm': brain_to_heart,
                    'heart_to_brain_ccm': heart_to_brain,
                    'ccm_difference': brain_to_heart - heart_to_brain
                }
                
                results.append(result)
    
    logger.info(f"被试 {subject_id} 分析完成，共 {len(results)} 条结果")
    update_progress(f"被试 {subject_id} 分析完成，共 {len(results)} 条结果")
    
    return results

def process_batch(batch_subjects, args):
    """处理一批被试"""
    all_results = []
    
    for subject_id in batch_subjects:
        # 解析参数
        channels = args.channels.split(',') if args.channels != 'all' else None
        bands_input = args.bands.split(',') if args.bands != 'all' else None
        stages = args.stages.split(',') if args.stages != 'all' else None
        
        # 如果指定了频段，创建频段字典
        bands = None
        if bands_input is not None:
            bands = {}
            band_ranges = {
                'delta': (0.5, 4),
                'theta': (4, 8),
                'alpha': (8, 13),
                'beta': (13, 30),
                'gamma': (30, 45)
            }
            for band in bands_input:
                if band in band_ranges:
                    bands[band] = band_ranges[band]
        
        # 分析被试
        subject_results = analyze_subject(subject_id, stages, channels, bands)
        all_results.extend(subject_results)
    
    return all_results

def save_results(results, filename):
    """保存分析结果"""
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 转换为DataFrame
    df = pd.DataFrame(results)
    
    # 保存为CSV
    csv_path = os.path.join(OUTPUT_DIR, f"{filename}.csv")
    df.to_csv(csv_path, index=False)
    logger.info(f"结果已保存至 {csv_path}")
    
    # 保存为Parquet
    parquet_path = os.path.join(OUTPUT_DIR, f"{filename}.parquet")
    df.to_parquet(parquet_path, index=False)
    logger.info(f"结果已保存至 {parquet_path}")
    
    return df

def visualize_results(results_df):
    """可视化分析结果"""
    logger.info("开始生成可视化结果")
    update_progress("开始生成可视化结果")
    
    # 确保输出目录存在
    os.makedirs(os.path.join(OUTPUT_DIR, "figures"), exist_ok=True)
    
    # 1. 频段比较图
    plt.figure(figsize=(12, 8))
    
    # 按频段分组计算平均值
    band_summary = results_df.groupby('frequency_band')[['brain_to_heart_ccm', 'heart_to_brain_ccm']].mean().reset_index()
    
    # 设置颜色
    colors = ['#3498db', '#e74c3c']
    
    # 创建柱状图
    x = np.arange(len(band_summary))
    width = 0.35
    
    plt.bar(x - width/2, band_summary['brain_to_heart_ccm'], width, label='脑→心', color=colors[0])
    plt.bar(x + width/2, band_summary['heart_to_brain_ccm'], width, label='心→脑', color=colors[1])
    
    plt.xlabel('频段', fontsize=14)
    plt.ylabel('CCM强度', fontsize=14)
    plt.title('不同频段的脑-心交互作用强度', fontsize=16)
    plt.xticks(x, band_summary['frequency_band'], fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    # 保存图像
    plt.savefig(os.path.join(OUTPUT_DIR, "频段方向性指标.png"), dpi=300, bbox_inches='tight')
    plt.savefig(os.path.join(OUTPUT_DIR, "频段方向性指标.pdf"), bbox_inches='tight')
    
    # 2. 脑-心交互对比图
    plt.figure(figsize=(10, 8))
    
    # 计算每个被试的平均值
    subject_summary = results_df.groupby('subject_id')[['brain_to_heart_ccm', 'heart_to_brain_ccm']].mean().reset_index()
    
    # 创建散点图
    plt.scatter(subject_summary['brain_to_heart_ccm'], subject_summary['heart_to_brain_ccm'], 
                alpha=0.7, s=80, c='#2ecc71')
    
    # 添加对角线
    max_val = max(subject_summary['brain_to_heart_ccm'].max(), subject_summary['heart_to_brain_ccm'].max())
    min_val = min(subject_summary['brain_to_heart_ccm'].min(), subject_summary['heart_to_brain_ccm'].min())
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5)
    
    # 添加标签
    for i, row in subject_summary.iterrows():
        plt.annotate(row['subject_id'], 
                    (row['brain_to_heart_ccm'], row['heart_to_brain_ccm']),
                    xytext=(5, 5), textcoords='offset points', fontsize=10)
    
    plt.xlabel('脑→心 CCM强度', fontsize=14)
    plt.ylabel('心→脑 CCM强度', fontsize=14)
    plt.title('脑-心交互作用方向性对比', fontsize=16)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    # 保存图像
    plt.savefig(os.path.join(OUTPUT_DIR, "脑心因果关系对比.png"), dpi=300, bbox_inches='tight')
    plt.savefig(os.path.join(OUTPUT_DIR, "脑心因果关系对比.pdf"), bbox_inches='tight')
    
    logger.info("可视化结果已生成")
    update_progress("可视化结果已生成")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 初始化进度文件
    if args.force_recompute:
        update_progress("开始脑-心非线性交互作用分析（强制重新计算）", overwrite=True)
    else:
        update_progress("开始脑-心非线性交互作用分析", overwrite=True)
    
    # 获取被试ID
    if args.subjects == 'all':
        subject_ids = get_subject_ids()
    else:
        subject_ids = args.subjects.split(',')
    
    logger.info(f"找到 {len(subject_ids)} 个被试: {subject_ids}")
    update_progress(f"找到 {len(subject_ids)} 个被试: {subject_ids}")
    
    # 检查是否只进行可视化
    if args.only_visualization:
        logger.info("只进行可视化，跳过分析阶段")
        update_progress("只进行可视化，跳过分析阶段")
        
        # 加载现有结果
        results_file = os.path.join(OUTPUT_DIR, "ccm_results_all_subjects.csv")
        if os.path.exists(results_file):
            results_df = pd.read_csv(results_file)
            logger.info(f"已加载 {len(results_df)} 条结果")
            update_progress(f"已加载 {len(results_df)} 条结果")
            
            # 可视化结果
            visualize_results(results_df)
        else:
            logger.error(f"结果文件不存在: {results_file}")
            update_progress(f"结果文件不存在: {results_file}")
        
        return
    
    # 检查是否需要重新计算
    results_file = os.path.join(OUTPUT_DIR, "ccm_results_all_subjects.csv")
    if os.path.exists(results_file) and not args.force_recompute:
        logger.info(f"发现现有结果文件: {results_file}")
        update_progress(f"发现现有结果文件: {results_file}")
        
        # 询问是否继续
        if not args.only_analysis and not args.only_visualization:
            logger.info("使用现有结果进行可视化")
            update_progress("使用现有结果进行可视化")
            
            # 加载现有结果
            results_df = pd.read_csv(results_file)
            logger.info(f"已加载 {len(results_df)} 条结果")
            update_progress(f"已加载 {len(results_df)} 条结果")
            
            # 可视化结果
            visualize_results(results_df)
            
            return
    
    # 设置多进程参数
    if args.max_cores is None:
        num_cores = min(mp.cpu_count() - 1, 7)  # 默认使用CPU核心数-1，最多7个
    else:
        num_cores = min(args.max_cores, mp.cpu_count())
    
    logger.info(f"使用 {num_cores} 个CPU核心进行分析")
    update_progress(f"使用 {num_cores} 个CPU核心进行分析")
    
    # 分批处理被试
    batch_size = args.batch_size
    num_batches = (len(subject_ids) + batch_size - 1) // batch_size
    
    logger.info(f"将 {len(subject_ids)} 个被试分为 {num_batches} 批处理，每批 {batch_size} 个")
    update_progress(f"将 {len(subject_ids)} 个被试分为 {num_batches} 批处理，每批 {batch_size} 个")
    
    all_results = []
    
    for i in range(0, len(subject_ids), batch_size):
        batch = subject_ids[i:i+batch_size]
        batch_num = i // batch_size + 1
        
        logger.info(f"处理第 {batch_num}/{num_batches} 批: {batch}")
        update_progress(f"处理第 {batch_num}/{num_batches} 批: {batch}")
        
        # 创建进程池
        with mp.Pool(num_cores) as pool:
            # 将批次划分为更小的子批次，每个进程处理一个被试
            batch_results = pool.starmap(
                analyze_subject,
                [(subject_id, 
                  args.stages.split(',') if args.stages != 'all' else None,
                  args.channels.split(',') if args.channels != 'all' else None,
                  None)  # 使用默认频段
                 for subject_id in batch]
            )
        
        # 合并结果
        for results in batch_results:
            all_results.extend(results)
        
        # 保存中间结果
        interim_df = pd.DataFrame(all_results)
        interim_df.to_csv(os.path.join(OUTPUT_DIR, "ccm_results_interim.csv"), index=False)
        
        # 创建备份
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(OUTPUT_DIR, f"ccm_results_interim_backup_{timestamp}.csv")
        shutil.copy2(os.path.join(OUTPUT_DIR, "ccm_results_interim.csv"), backup_file)
        
        logger.info(f"已保存中间结果，当前共 {len(all_results)} 条结果")
        update_progress(f"已保存中间结果，当前共 {len(all_results)} 条结果")
        
        # 暂停一段时间，让系统释放资源
        if i + batch_size < len(subject_ids):
            logger.info(f"暂停 {args.pause_time} 秒...")
            time.sleep(args.pause_time)
    
    # 保存最终结果
    results_df = save_results(all_results, "ccm_results_all_subjects")
    
    # 计算汇总统计
    summary = results_df.groupby(['frequency_band', 'stage'])[['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']].mean()
    summary.to_csv(os.path.join(OUTPUT_DIR, "ccm_results_summary.csv"))
    summary.to_parquet(os.path.join(OUTPUT_DIR, "ccm_results_summary.parquet"))
    
    logger.info("分析完成")
    update_progress("分析完成")
    
    # 可视化结果
    if not args.only_analysis:
        visualize_results(results_df)

if __name__ == "__main__":
    start_time = time.time()
    main()
    end_time = time.time()
    
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    logger.info(f"总运行时间: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    update_progress(f"总运行时间: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")

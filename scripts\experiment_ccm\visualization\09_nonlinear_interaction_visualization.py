#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基于CCM的脑-心非线性定向交互作用分析可视化模块

该模块提供了用于可视化CCM分析结果的函数，包括：
1. 收敛性曲线绘制
2. 方向性指数地形图
3. 阶段间差异图
4. 不同频段分析
5. 高低焦虑组对比

参考文献：
[1] 施秋霞. 面向抑郁障碍的脑—心非线性交互作用及相关算法研究[D]. 兰州大学, 2023.
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from scipy import stats
import pandas as pd
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
import mne
from scipy.signal import detrend, hilbert
from skccm.utilities import train_test_split
from skccm.embedding import Embed
from skccm.ccm import CCM
import warnings
warnings.filterwarnings('ignore')

# 定义参数
SAMPLE_RATE = 250  # 采样率
WINDOW_SIZE = 6    # 滑动窗口大小(秒)
STEP_SIZE = 1      # 窗口滑动步长(秒)
EMBED_DIM = 8      # 嵌入维度
TAU = 1            # 时间延迟
NUM_NEIGHBORS = 10 # 单纯形投影中的邻居数量

# 定义情绪刺激类型
EMOTIONS = ['positive', 'negative', 'neutral']

# 定义频段
FREQUENCY_BANDS = {
    'delta': (1, 3),
    'theta': (4, 7),
    'alpha': (8, 13),
    'beta': (14, 30)
}

# 定义关注的脑区电极 (从全部63个通道中选择关键的前额至枕叶10个对称通道)
ELECTRODES = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2']

# 所有EEG通道
ALL_EEG_CHANNELS = [
    'Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2',
    'F7', 'F8', 'T7', 'T8', 'P7', 'P8', 'Fz', 'Cz', 'Pz', 'FC1',
    'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10',
    'TP9', 'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4',
    'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4', 'F5', 'F6', 'C5', 'C6',
    'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8',
    'Fpz', 'CPz', 'POz', 'Oz'
]

# 心电通道
ECG_CHANNELS = [f'ECG{i}' for i in range(1, 59)]

# 定义输出目录
OUTPUT_DIR = os.path.join('D:/ecgeeg/30-数据分析/5-NeuroKit2/result', 'ccm_emotional_analysis')
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 数据目录
DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"

def set_ieee_style():
    """
    设置IEEE Transactions风格的图表样式
    """
    plt.style.use('default')
    plt.rcParams['font.family'] = 'LXGW Wenkai'
    plt.rcParams['font.size'] = 10
    plt.rcParams['figure.figsize'] = (8, 6)
    plt.rcParams['figure.dpi'] = 300
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.linestyle'] = '--'
    plt.rcParams['grid.alpha'] = 0.2
    plt.rcParams['axes.linewidth'] = 0.8
    plt.rcParams['xtick.major.width'] = 0.8
    plt.rcParams['ytick.major.width'] = 0.8
    plt.rcParams['xtick.minor.width'] = 0.6
    plt.rcParams['ytick.minor.width'] = 0.6
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['savefig.facecolor'] = 'white'
    plt.rcParams['savefig.bbox'] = 'tight'
    plt.rcParams['savefig.pad_inches'] = 0.1
    plt.rcParams['legend.frameon'] = True
    plt.rcParams['legend.framealpha'] = 0.8
    plt.rcParams['legend.edgecolor'] = 'gray'
    plt.rcParams['legend.fancybox'] = True

def load_emotional_data(subject_id, stage, data_dir):
    """
    加载特定被试在特定实验阶段的EEG和ECG数据

    参数:
    subject_id (str): 被试ID
    stage (str): 实验阶段 ('prac', 'rest1', 'test1', 'rest2', 'test2', 'rest3', 'test3')
    data_dir (str): 数据目录

    返回:
    tuple: (eeg_data, ecg_data, sfreq)
    """
    # 构建文件路径模式
    if stage == 'prac':
        # 练习阶段
        file_pattern = f"{subject_id}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_prac.fif"
    elif stage == 'rest1':
        # 静息态1
        file_pattern = f"{subject_id}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test1':
        # 刺激态1
        file_pattern = f"{subject_id}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest2':
        # 静息态2
        file_pattern = f"{subject_id}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test2':
        # 刺激态2
        file_pattern = f"{subject_id}_02_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    elif stage == 'rest3':
        # 静息态3
        file_pattern = f"{subject_id}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_rest.fif"
    elif stage == 'test3':
        # 刺激态3
        file_pattern = f"{subject_id}_03_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_test.fif"
    else:
        print(f"未知阶段: {stage}")
        return None, None, None

    # 构建文件路径
    file_path = os.path.join(data_dir, file_pattern)

    # 检查文件是否存在，如果不存在尝试备用规则
    if not os.path.exists(file_path) and stage in ['rest2', 'test2', 'rest3', 'test3']:
        alt_pattern = f"{subject_id}_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_{stage.replace('rest', 'rest').replace('test', 'test')}.fif"
        file_path = os.path.join(data_dir, alt_pattern)

    try:
        # 加载数据
        if os.path.exists(file_path):
            raw = mne.io.read_raw_fif(file_path, preload=True)
            print(f"成功加载数据: {file_path}")

            # 获取采样率
            sfreq = raw.info['sfreq']

            # 提取EEG和ECG通道数据
            ch_names = raw.ch_names

            # 分离EEG数据
            eeg_channels = ALL_EEG_CHANNELS
            eeg_channels_available = [ch for ch in eeg_channels if ch in ch_names]
            eeg_data = raw.get_data(picks=eeg_channels_available)

            # ECG数据应该在EEG通道之后
            # 我们将后面的58个通道视为ECG通道
            start_idx = len(eeg_channels_available)
            ecg_data = raw.get_data()[start_idx:start_idx+58]

            return eeg_data, ecg_data, sfreq
        else:
            print(f"文件不存在: {file_path}")
            return None, None, None
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None, None, None

def extract_frequency_band_envelopes(eeg_data, eeg_channels, frequency_bands, sfreq):
    """
    提取EEG不同频段的包络

    参数:
    eeg_data (numpy.ndarray): EEG数据
    eeg_channels (list): EEG通道名称
    frequency_bands (dict): 频段定义
    sfreq (float): 采样率

    返回:
    dict: 不同频段的包络数据
    """
    # 存储不同频段的包络
    envelopes = {}

    # 获取关注通道的索引
    electrode_indices = [eeg_channels.index(ch) for ch in ELECTRODES if ch in eeg_channels]

    # 提取关注通道的数据
    electrode_data = eeg_data[electrode_indices]

    # 为每个频段提取包络
    for band_name, (low_freq, high_freq) in frequency_bands.items():
        # 带通滤波
        filtered = mne.filter.filter_data(electrode_data, sfreq, low_freq, high_freq, method='fir')

        # 使用希尔伯特变换提取包络
        analytic_signal = hilbert(filtered)
        band_envelope = np.abs(analytic_signal)

        envelopes[band_name] = band_envelope

    return envelopes, [ELECTRODES[i] for i in range(len(electrode_indices))]

def find_best_ecg_channel(ecg_data, sfreq):
    """
    找到信号质量最好的心电通道

    参数:
    ecg_data (numpy.ndarray): 心电数据
    sfreq (float): 采样率

    返回:
    tuple: (最佳通道索引, 最佳通道信号)
    """
    from scipy.signal import find_peaks

    best_channel_idx = None
    best_quality = -float('inf')
    best_signal = None

    for ch_idx in range(len(ecg_data)):
        signal = ecg_data[ch_idx]

        # 简单的R峰检测
        peaks, _ = find_peaks(signal, height=0.5*np.max(signal), distance=0.5*sfreq)

        if len(peaks) < 10:
            continue

        # 计算R-R间隔
        rr_intervals = np.diff(peaks) / sfreq * 1000  # 转换为毫秒

        if len(rr_intervals) < 5:
            continue

        # 计算变异系数（标准差/平均值）
        cv = np.std(rr_intervals) / np.mean(rr_intervals)

        # 变异系数应该在合理范围内
        if cv < 0.03 or cv > 0.2:
            continue

        # 计算信号质量分数
        quality_score = 1.0 - cv

        if quality_score > best_quality:
            best_quality = quality_score
            best_channel_idx = ch_idx
            best_signal = signal

    if best_channel_idx is None:
        # 如果没有找到合适的通道，则使用第一个通道
        return 0, ecg_data[0]

    print(f"选择的最佳心电通道索引: {best_channel_idx}, 信号质量评分: {best_quality:.2f}")
    return best_channel_idx, best_signal

def extract_hrv_features(ecg_signal, sfreq):
    """
    从ECG信号中提取HRV特征

    参数:
    ecg_signal (numpy.ndarray): ECG信号
    sfreq (float): 采样率

    返回:
    numpy.ndarray: HRV特征序列
    """
    # 检测R峰
    from scipy.signal import find_peaks

    # R峰检测 - 简化版本
    peaks, _ = find_peaks(ecg_signal, height=0.5*np.max(ecg_signal), distance=0.5*sfreq)

    if len(peaks) < 2:
        print("R峰检测失败，返回零序列")
        return np.zeros_like(ecg_signal)

    # 计算RR间隔
    rr_intervals = np.diff(peaks) / sfreq * 1000  # 转换为毫秒

    # 构建RR序列的时间点（相对于原始信号）
    rr_times = peaks[1:] / sfreq

    # 创建完整的时间序列
    full_times = np.arange(len(ecg_signal)) / sfreq

    # 立方样条插值得到连续的HRV序列
    from scipy.interpolate import interp1d

    if len(rr_times) < 2:
        print("RR间隔太少，无法进行插值")
        return np.zeros_like(ecg_signal)

    # 插值函数
    f = interp1d(rr_times, rr_intervals, kind='cubic', bounds_error=False, fill_value='extrapolate')

    # 应用插值
    hrv_series = f(full_times)

    # 去除NaN值
    hrv_series[np.isnan(hrv_series)] = np.nanmean(hrv_series)

    # 去趋势
    hrv_series = detrend(hrv_series)

    return hrv_series

def calculate_ccm(x, y, embed_dim=EMBED_DIM, tau=TAU):
    """
    计算两个时间序列之间的收敛交叉映射(CCM)

    参数:
    x (numpy.ndarray): 第一个时间序列
    y (numpy.ndarray): 第二个时间序列
    embed_dim (int): 嵌入维度
    tau (int): 时间延迟

    返回:
    tuple: (x对y的影响, y对x的影响)
    """
    # 确保数据长度相同
    min_length = min(len(x), len(y))
    x = x[:min_length]
    y = y[:min_length]

    # 去除NaN值
    valid_idx = ~(np.isnan(x) | np.isnan(y))
    x = x[valid_idx]
    y = y[valid_idx]

    if len(x) < (embed_dim + 1) * tau:
        # 数据太短，无法计算CCM
        return np.nan, np.nan

    # 创建状态空间重构
    e1 = Embed(x)
    e2 = Embed(y)
    X = e1.embed(embed_dim, tau)
    Y = e2.embed(embed_dim, tau)

    # 划分训练集和测试集
    x_train, x_test, y_train, y_test = train_test_split(X, Y, percent=0.75)

    # 计算CCM
    ccm = CCM()
    ccm.fit(x_train, y_train)
    x_pred, y_pred = ccm.predict(x_test, y_test)

    # 计算预测准确度
    x_score = stats.pearsonr(x_pred[:, 0], x_test[:, 0])[0]  # y预测x的能力
    y_score = stats.pearsonr(y_pred[:, 0], y_test[:, 0])[0]  # x预测y的能力

    return x_score, y_score  # 分别返回y→x和x→y的因果影响

def sliding_window_ccm(eeg_envelope, hrv_series, window_size, step_size, sfreq):
    """
    使用滑动窗口计算EEG包络和HRV之间的CCM

    参数:
    eeg_envelope (numpy.ndarray): EEG频段包络
    hrv_series (numpy.ndarray): HRV特征序列
    window_size (int): 窗口大小(秒)
    step_size (int): 窗口滑动步长(秒)
    sfreq (float): 采样率

    返回:
    tuple: (时间戳, 脑→心CCM值, 心→脑CCM值)
    """
    window_samples = int(window_size * sfreq)
    step_samples = int(step_size * sfreq)

    n_windows = (len(eeg_envelope) - window_samples) // step_samples + 1

    # 存储结果
    times = []
    brain_to_heart = []
    heart_to_brain = []

    for i in range(n_windows):
        start_idx = i * step_samples
        end_idx = start_idx + window_samples

        # 提取窗口数据
        eeg_window = eeg_envelope[start_idx:end_idx]
        hrv_window = hrv_series[start_idx:end_idx]

        # 计算CCM
        b2h, h2b = calculate_ccm(eeg_window, hrv_window)

        # 存储结果
        times.append(start_idx / sfreq)  # 转换为秒
        brain_to_heart.append(b2h)
        heart_to_brain.append(h2b)

    return np.array(times), np.array(brain_to_heart), np.array(heart_to_brain)

def analyze_emotional_stages(subject_ids, group, data_dir, stages=['test1', 'test2', 'test3']):
    """
    分析情绪刺激阶段的脑-心交互动态变化

    参数:
    subject_ids (list): 被试ID列表
    group (str): 组别 ('depression', 'control')
    data_dir (str): 数据目录
    stages (list): 要分析的实验阶段

    返回:
    dict: 分析结果
    """
    # 存储结果
    results = {
        'subject_id': [],
        'group': [],
        'stage': [],
        'electrode': [],
        'frequency_band': [],
        'brain_to_heart_ccm': [],
        'heart_to_brain_ccm': [],
        'ccm_difference': [],  # 两个方向的CCM差异
        'dominant_direction': []  # 主导方向 (1: 脑→心, -1: 心→脑, 0: 无明显方向)
    }

    for subject_id in subject_ids:
        print(f"分析被试 {subject_id} ({group})...")

        for stage in stages:
            print(f"  处理阶段: {stage}")

            # 加载数据
            eeg_data, ecg_data, sfreq = load_emotional_data(subject_id, stage, data_dir)

            if eeg_data is None or ecg_data is None:
                print(f"  跳过被试 {subject_id} 的 {stage} 阶段，数据不可用")
                continue

            # 找到最佳心电通道
            _, best_ecg = find_best_ecg_channel(ecg_data, sfreq)

            # 提取HRV特征
            hrv_series = extract_hrv_features(best_ecg, sfreq)

            # 提取EEG频段包络
            envelopes, effective_channels = extract_frequency_band_envelopes(
                eeg_data, ALL_EEG_CHANNELS, FREQUENCY_BANDS, sfreq)

            # 对每个电极和频段计算CCM
            for e_idx, electrode in enumerate(effective_channels):
                for band, band_envelopes in envelopes.items():
                    # 获取当前电极的包络
                    electrode_envelope = band_envelopes[e_idx]

                    # 使用滑动窗口计算CCM
                    _, brain_to_heart, heart_to_brain = sliding_window_ccm(
                        electrode_envelope, hrv_series, WINDOW_SIZE, STEP_SIZE, sfreq)

                    # 计算平均CCM值
                    mean_b2h = np.nanmean(brain_to_heart)
                    mean_h2b = np.nanmean(heart_to_brain)

                    # 计算CCM差异和主导方向
                    ccm_diff = mean_b2h - mean_h2b

                    if abs(ccm_diff) < 0.05:  # 阈值可调整
                        dominant_dir = 0  # 无明显方向
                    else:
                        dominant_dir = 1 if ccm_diff > 0 else -1

                    # 存储结果
                    results['subject_id'].append(subject_id)
                    results['group'].append(group)
                    results['stage'].append(stage)
                    results['electrode'].append(electrode)
                    results['frequency_band'].append(band)
                    results['brain_to_heart_ccm'].append(mean_b2h)
                    results['heart_to_brain_ccm'].append(mean_h2b)
                    results['ccm_difference'].append(ccm_diff)
                    results['dominant_direction'].append(dominant_dir)

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # 保存结果
    output_file = os.path.join(OUTPUT_DIR, f'emotional_ccm_results_{group}.csv')
    results_df.to_csv(output_file, index=False)
    print(f"结果已保存至: {output_file}")

    return results_df

def compare_groups(depression_results, control_results):
    """
    比较抑郁组和对照组在情绪刺激下的脑-心交互差异

    参数:
    depression_results (pandas.DataFrame): 抑郁组结果
    control_results (pandas.DataFrame): 对照组结果

    返回:
    pandas.DataFrame: 组间比较结果
    """
    # 合并数据
    all_results = pd.concat([depression_results, control_results])

    # 创建存储统计分析结果的DataFrame
    comparison_results = {
        'stage': [],
        'electrode': [],
        'frequency_band': [],
        'measure': [],  # brain_to_heart, heart_to_brain, ccm_difference
        'depression_mean': [],
        'control_mean': [],
        't_statistic': [],
        'p_value': [],
        'significant': []
    }

    # 遍历所有情绪类型、电极和频段组合
    for stage in all_results['stage'].unique():
        for electrode in all_results['electrode'].unique():
            for band in all_results['frequency_band'].unique():

                # 过滤数据
                dep_data = depression_results[(depression_results['stage'] == stage) &
                                             (depression_results['electrode'] == electrode) &
                                             (depression_results['frequency_band'] == band)]

                ctrl_data = control_results[(control_results['stage'] == stage) &
                                          (control_results['electrode'] == electrode) &
                                          (control_results['frequency_band'] == band)]

                if len(dep_data) == 0 or len(ctrl_data) == 0:
                    continue

                # 比较不同的CCM度量
                for measure in ['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']:
                    # 执行t检验
                    t_stat, p_val = stats.ttest_ind(dep_data[measure], ctrl_data[measure])

                    # 存储结果
                    comparison_results['stage'].append(stage)
                    comparison_results['electrode'].append(electrode)
                    comparison_results['frequency_band'].append(band)
                    comparison_results['measure'].append(measure)
                    comparison_results['depression_mean'].append(dep_data[measure].mean())
                    comparison_results['control_mean'].append(ctrl_data[measure].mean())
                    comparison_results['t_statistic'].append(t_stat)
                    comparison_results['p_value'].append(p_val)
                    comparison_results['significant'].append(p_val < 0.05)

    # 转换为DataFrame
    comparison_df = pd.DataFrame(comparison_results)

    # 保存结果
    output_file = os.path.join(OUTPUT_DIR, 'emotional_ccm_group_comparison.csv')
    comparison_df.to_csv(output_file, index=False)
    print(f"组间比较结果已保存至: {output_file}")

    return comparison_df

def visualize_directionality_heatmap(results_df, group, output_dir):
    """
    创建方向性指数热图

    参数:
    results_df (pandas.DataFrame): 分析结果
    group (str): 组别
    output_dir (str): 输出目录
    """
    print("绘制方向性指数热图...")

    # 创建方向性指数子目录
    directionality_dir = os.path.join(output_dir, "directionality_index")
    os.makedirs(directionality_dir, exist_ok=True)

    # 创建热图
    df = results_df.pivot(index='electrode', columns='frequency_band', values='ccm_difference')

    plt.figure(figsize=(10, 8))
    cmap = LinearSegmentedColormap.from_list('custom_diverging',
                                           ['#457B9D', 'white', '#E63946'], N=256)
    sns.heatmap(df, cmap=cmap, center=0, annot=True, fmt='.2f',
                linewidths=0.5, cbar_kws={'label': '方向性指数 (心脏→大脑 - 大脑→心脏)'})
    plt.title(f'{group}组在不同电极和频段的脑-心交互方向性热图')
    plt.tight_layout()
    output_path = os.path.join(directionality_dir, f'{group}_directionality_heatmap.svg')
    plt.savefig(output_path, format='svg')
    plt.close()

    print(f"方向性指数热图已保存至: {directionality_dir}")

def visualize_ccm_results(ccm_results, stats_results, output_dir, high_anxiety_group=None, low_anxiety_group=None):
    """
    可视化CCM分析结果

    参数:
    ccm_results: CCM分析结果
    stats_results: 统计分析结果
    output_dir: 输出目录
    high_anxiety_group: 高焦虑组CCM结果 (可选)
    low_anxiety_group: 低焦虑组CCM结果 (可选)
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 设置IEEE Transactions风格
    set_ieee_style()

    # 1. 绘制收敛性曲线
    visualize_convergence_curves(ccm_results, output_dir)

    # 2. 绘制方向性指数图
    visualize_directionality_index(ccm_results, output_dir)

    # 3. 绘制阶段间差异图
    visualize_stage_differences(stats_results, output_dir)

    # 4. 如果提供了高低焦虑组数据，绘制组间比较图
    if high_anxiety_group is not None and low_anxiety_group is not None:
        visualize_group_comparison(high_anxiety_group, low_anxiety_group, output_dir)

    # 5. 绘制Fp1导联的频段分析图
    visualize_frequency_band_analysis(ccm_results, output_dir)

def visualize_convergence_curves(ccm_results, output_dir):
    """
    绘制CCM收敛性曲线

    参数:
    ccm_results: CCM分析结果
    output_dir: 输出目录
    """
    print("绘制CCM收敛性曲线...")

    # 创建收敛性曲线子目录
    convergence_dir = os.path.join(output_dir, "convergence_curves")
    os.makedirs(convergence_dir, exist_ok=True)

    # 获取所有阶段和通道
    stages = list(ccm_results.keys())
    channels = list(ccm_results[stages[0]].keys())

    # 特别关注Fp1导联
    fp1_stages_fig, fp1_axes = plt.subplots(2, 3, figsize=(15, 10))
    fp1_axes = fp1_axes.flatten()

    for stage_idx, stage in enumerate(stages):
        for channel, channel_result in ccm_results[stage].items():
            # 单独绘制每个通道的收敛曲线
            fig, ax = plt.subplots(figsize=(8, 6))

            # 获取最佳嵌入维度
            best_E_idx = np.argmax(np.mean(channel_result['Y_xmap_X'], axis=1))
            best_E = channel_result['E_values'][best_E_idx]

            # 绘制收敛曲线
            lib_sizes = channel_result['library_sizes']
            heart_to_brain = channel_result['Y_xmap_X'][best_E_idx, :]
            brain_to_heart = channel_result['X_xmap_Y'][best_E_idx, :]

            ax.plot(lib_sizes, heart_to_brain, 'o-', color='#E63946', label='心脏→大脑', linewidth=1.5, markersize=5)
            ax.plot(lib_sizes, brain_to_heart, 's-', color='#457B9D', label='大脑→心脏', linewidth=1.5, markersize=5)

            # 添加显著性标记
            if np.mean(heart_to_brain) > np.mean(brain_to_heart):
                if stats.ttest_ind(heart_to_brain, brain_to_heart).pvalue < 0.05:
                    ax.text(0.5, 0.95, "*心脏→大脑显著强于大脑→心脏",
                            transform=ax.transAxes, ha='center', va='top',
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))
            else:
                if stats.ttest_ind(brain_to_heart, heart_to_brain).pvalue < 0.05:
                    ax.text(0.5, 0.95, "*大脑→心脏显著强于心脏→大脑",
                            transform=ax.transAxes, ha='center', va='top',
                            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))

            ax.set_xlabel('库大小')
            ax.set_ylabel('预测技能 (ρ)')
            ax.set_title(f'{stage}阶段 {channel}通道 CCM收敛曲线 (E={best_E})')
            ax.legend(loc='lower right')
            ax.grid(True, linestyle='--', alpha=0.3)

            # 调整y轴范围，使差异更明显
            y_min = min(min(heart_to_brain), min(brain_to_heart))
            y_max = max(max(heart_to_brain), max(brain_to_heart))
            y_range = y_max - y_min
            ax.set_ylim(max(0, y_min - 0.1 * y_range), y_max + 0.1 * y_range)

            # 保存图像
            output_path = os.path.join(convergence_dir, f'ccm_convergence_{stage}_{channel}.svg')
            plt.savefig(output_path, format='svg')
            plt.close()

            # 如果是Fp1导联，添加到组合图中
            if channel == 'Fp1' and stage_idx < len(fp1_axes):
                ax = fp1_axes[stage_idx]
                ax.plot(lib_sizes, heart_to_brain, 'o-', color='#E63946', label='心脏→大脑', linewidth=1.5, markersize=4)
                ax.plot(lib_sizes, brain_to_heart, 's-', color='#457B9D', label='大脑→心脏', linewidth=1.5, markersize=4)
                ax.set_title(f'{stage}阶段')
                ax.set_xlabel('库大小')
                ax.set_ylabel('预测技能 (ρ)')
                ax.grid(True, linestyle='--', alpha=0.3)

                # 调整y轴范围
                y_min = min(min(heart_to_brain), min(brain_to_heart))
                y_max = max(max(heart_to_brain), max(brain_to_heart))
                y_range = y_max - y_min
                ax.set_ylim(max(0, y_min - 0.1 * y_range), y_max + 0.1 * y_range)

    # 完成Fp1导联的组合图
    if len(stages) < len(fp1_axes):
        for i in range(len(stages), len(fp1_axes)):
            fp1_axes[i].axis('off')

    fp1_stages_fig.suptitle('Fp1导联在不同实验阶段的CCM收敛曲线', fontsize=12)
    handles, labels = fp1_axes[0].get_legend_handles_labels()
    fp1_stages_fig.legend(handles, labels, loc='lower center', ncol=2, bbox_to_anchor=(0.5, 0))
    plt.tight_layout(rect=[0, 0.05, 1, 0.95])
    output_path = os.path.join(output_dir, 'Fp1_stages_convergence.svg')
    plt.savefig(output_path, format='svg')
    plt.close()

    print(f"CCM收敛性曲线已保存至: {convergence_dir}")

def visualize_directionality_index(ccm_results, output_dir):
    """
    绘制方向性指数图

    参数:
    ccm_results: CCM分析结果
    output_dir: 输出目录
    """
    print("绘制方向性指数图...")

    # 创建方向性指数子目录
    directionality_dir = os.path.join(output_dir, "directionality_index")
    os.makedirs(directionality_dir, exist_ok=True)

    # 获取所有阶段和通道
    stages = list(ccm_results.keys())
    channels = list(ccm_results[stages[0]].keys())

    # 创建组合图
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()

    # 创建一个DataFrame来存储所有数据，用于后续的热图
    all_directionality = []

    for stage_idx, stage in enumerate(stages):
        # 计算每个通道的方向性指数
        directionality = {}
        for channel in channels:
            best_E_idx = np.argmax(np.mean(ccm_results[stage][channel]['Y_xmap_X'], axis=1))
            heart_to_brain = ccm_results[stage][channel]['Y_xmap_X'][best_E_idx, -1]
            brain_to_heart = ccm_results[stage][channel]['X_xmap_Y'][best_E_idx, -1]
            directionality[channel] = heart_to_brain - brain_to_heart

            # 添加到DataFrame
            all_directionality.append({
                'Stage': stage,
                'Channel': channel,
                'Directionality': heart_to_brain - brain_to_heart,
                'Heart_to_Brain': heart_to_brain,
                'Brain_to_Heart': brain_to_heart
            })

        # 创建条形图
        if stage_idx < len(axes):
            ax = axes[stage_idx]
            channels_list = list(directionality.keys())
            values = [directionality[ch] for ch in channels_list]

            bars = ax.bar(channels_list, values,
                         color=['#E63946' if v > 0 else '#457B9D' for v in values],
                         edgecolor='black', linewidth=0.5)

            # 添加零线
            ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)

            ax.set_title(f'{stage}阶段')
            ax.set_xlabel('EEG通道')
            ax.set_ylabel('方向性指数')

            # 旋转x轴标签以避免重叠
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # 标记显著性
            for i, v in enumerate(values):
                if abs(v) > 0.1:  # 假设0.1是显著性阈值
                    ax.text(i, v + (0.05 if v > 0 else -0.05), '*',
                            ha='center', va='center', fontsize=12)

    # 隐藏多余的子图
    if len(stages) < len(axes):
        for i in range(len(stages), len(axes)):
            axes[i].axis('off')

    fig.suptitle('不同实验阶段的脑-心交互方向性指数', fontsize=12)
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    output_path = os.path.join(output_dir, 'directionality_index_all_stages.svg')
    plt.savefig(output_path, format='svg')
    plt.close()

    # 创建热图
    df = pd.DataFrame(all_directionality)
    pivot_df = df.pivot(index='Channel', columns='Stage', values='Directionality')

    plt.figure(figsize=(10, 8))
    cmap = LinearSegmentedColormap.from_list('custom_diverging',
                                           ['#457B9D', 'white', '#E63946'], N=256)
    sns.heatmap(pivot_df, cmap=cmap, center=0, annot=True, fmt='.2f',
                linewidths=0.5, cbar_kws={'label': '方向性指数 (心脏→大脑 - 大脑→心脏)'})
    plt.title('不同通道和实验阶段的脑-心交互方向性热图')
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'directionality_heatmap.svg')
    plt.savefig(output_path, format='svg')
    plt.close()

    # 特别关注Fp1导联
    fp1_data = df[df['Channel'] == 'Fp1']
    plt.figure(figsize=(8, 6))

    x = range(len(stages))
    plt.bar(x, fp1_data['Directionality'], color='#1D3557', edgecolor='black', linewidth=0.5)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)

    plt.xlabel('实验阶段')
    plt.ylabel('方向性指数 (心脏→大脑 - 大脑→心脏)')
    plt.title('Fp1导联在不同实验阶段的脑-心交互方向性')
    plt.xticks(x, stages)

    # 添加心脏→大脑和大脑→心脏的数值
    for i, (_, row) in enumerate(fp1_data.iterrows()):
        plt.text(i, row['Directionality'] + 0.02,
                f"H→B: {row['Heart_to_Brain']:.2f}\nB→H: {row['Brain_to_Heart']:.2f}",
                ha='center', va='bottom', fontsize=8,
                bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.2'))

    plt.tight_layout()
    output_path = os.path.join(output_dir, 'Fp1_directionality.svg')
    plt.savefig(output_path, format='svg')
    plt.close()

    print(f"方向性指数图已保存至: {directionality_dir}")

def analyze_hep_by_anxiety_level():
    """分析不同焦虑水平人群的HEP成分"""
    # 提取高低焦虑组被试ID
    high_anxiety_subjects = high_anxiety['subject_id'].tolist()
    low_anxiety_subjects = low_anxiety['subject_id'].tolist()

    # 各次刺激的显著导联
    significant_channels = {
        'test1': ['Pz', 'P2', 'P4', 'F7', 'F8', 'Fz', 'AF7', 'FC1', 'FC3', 'FT7', 'T7', 'T8', 'T9', '02', 'PO4', 'PO7', 'PO8', 'CP6', 'C2', 'C3', 'C4', 'C6'],
        'test2': ['P3', 'Fp2', 'AF8', 'FT8', 'T7', 'T8', 'Tp10', 'C5'],
        'test3': ['P3', 'P7', 'P8', 'F2', 'F4', 'F7', 'F8', 'Fc1', 'Fc3', 'Fc4', 'Fc5', 'Fc6', 'T7', 'T8', 'Tp9', 'Po7', 'Cp3', 'Cp5', 'Cp6', 'C3', 'C4', 'C6']
    }

    # 分析各次刺激的HEP特征
    for stage, channels in significant_channels.items():
        print(f"\n分析{stage}阶段显著导联...")

        # 对各显著导联进行高低焦虑组对比
        for channel in channels:
            # 提取该通道在250-450ms窗口的HEP平均振幅
            high_anxiety_hep = extract_hep_amplitude(high_anxiety_subjects, stage, channel)
            low_anxiety_hep = extract_hep_amplitude(low_anxiety_subjects, stage, channel)

            # 执行统计检验
            result = stats.ttest_ind(high_anxiety_hep, low_anxiety_hep)

            if result.pvalue < 0.05:
                print(f"导联{channel}在{stage}阶段高低焦虑组存在显著差异: t={result.statistic:.3f}, p={result.pvalue:.3f}")

        # 可视化HEP波形比较
        visualize_hep_comparison(high_anxiety_subjects, low_anxiety_subjects, stage, channels)

def visualize_hep_anxiety_modulation():
    """可视化特质焦虑水平对HEP的调制作用"""
    fig = plt.figure(figsize=(15, 12))
    gs = gridspec.GridSpec(3, 3)

    # 顶部面板：前额叶导联(F7, F8, Fz)波形对比
    for i, ch in enumerate(['F7', 'F8', 'Fz']):
        ax = fig.add_subplot(gs[0, i])
        # 绘制高低焦虑组在250-450ms时间窗口的HEP波形
        ax.plot(times, high_anxiety_hep[ch], color='#E63946', label='高焦虑组', linewidth=2)
        ax.plot(times, low_anxiety_hep[ch], color='#457B9D', label='低焦虑组', linewidth=2)
        ax.axvspan(0.25, 0.45, alpha=0.2, color='yellow', label='ROI')
        ax.set_title(f'{ch}导联HEP波形')

    # 中部面板：显示三次刺激的变化趋势
    ax_trend = fig.add_subplot(gs[1, :])
    stages = ['test1', 'test2', 'test3']
    ax_trend.plot(stages, [high_anxiety_roi_mean[stage] for stage in stages], 'o-', color='#E63946', label='高焦虑组')
    ax_trend.plot(stages, [low_anxiety_roi_mean[stage] for stage in stages], 's-', color='#457B9D', label='低焦虑组')
    ax_trend.set_title('多次刺激下Fz导联HEP振幅变化')

    # 底部面板：条形图比较关键导联的统计差异
    ax_bar = fig.add_subplot(gs[2, :])
    # 显示高低焦虑组在关键导联的HEP振幅统计对比

    plt.tight_layout()
    plt.savefig('HEP_anxiety_modulation.svg', format='svg')
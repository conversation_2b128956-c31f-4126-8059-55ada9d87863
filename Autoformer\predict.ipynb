{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Use GPU: cuda:0\n"]}, {"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "# args.output_attention = True\n", "\n", "exp = Exp(args)\n", "\n", "model = exp.model\n", "\n", "setting = 'ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0'\n", "path = os.path.join(args.checkpoints,setting,'checkpoint.pth')\n", "model.load_state_dict(torch.load(path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "1\n", "NVIDIA GeForce RTX 3090\n", "0\n"]}], "source": ["import torch\n", "print(torch.cuda.is_available())\t\t # 查看GPu设备是否可用\n", "print(torch.cuda.device_count()) \t\t# 查看GPu设备数量\n", "print(torch.cuda.get_device_name())   \t# 查看当前GPu设备名称，默认设备id从0开始\n", "print(torch.cuda.current_device())\t\t# 查看当前GPu设备id\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.cuda.is_available() \n", "import torch\n", "torch.cuda.set_device(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Args in experiment:\n", "{'target': 'OT', 'des': 'Exp', 'dropout': 0.05, 'num_workers': 10, 'gpu': 0, 'lradj': 'type1', 'devices': '0', 'use_gpu': True, 'use_multi_gpu': False, 'freq': 'h', 'checkpoints': './checkpoints/', 'bucket_size': 4, 'n_hashes': 4, 'is_trainging': True, 'root_path': './dataset/ETT-small/', 'data_path': 'ETTh1.csv', 'model_id': 'ETTh1_96_24', 'model': 'Autoformer', 'data': 'ETTh1', 'features': 'M', 'seq_len': 96, 'label_len': 48, 'pred_len': 24, 'e_layers': 2, 'd_layers': 1, 'n_heads': 8, 'factor': 1, 'enc_in': 7, 'dec_in': 7, 'c_out': 7, 'd_model': 512, 'itr': 1, 'd_ff': 2048, 'moving_avg': 25, 'distil': True, 'output_attention': False, 'patience': 3, 'learning_rate': 0.0001, 'batch_size': 32, 'embed': 'timeF', 'activation': 'gelu', 'use_amp': False, 'loss': 'mse', 'train_epochs': 10}\n"]}], "source": ["import argparse\n", "import os\n", "import torch\n", "from exp.exp_main import Exp_Main#exp stands for experiments\n", "import random\n", "import numpy as np\n", "from utils.tools import dotdict\n", "\n", "fix_seed = 2021 \n", "random.seed(fix_seed)\n", "torch.manual_seed(fix_seed)\n", "np.random.seed(fix_seed)\n", "\n", "parser = argparse.ArgumentParser(description='Autoformer & Transformer family for Time Series Forecasting')\n", "\n", "# basic config\n", "parser.add_argument('--is_training', type=int, required=True, default=1, help='status')\n", "parser.add_argument('--model_id', type=str, required=True, default='test', help='model id')#模型id\n", "parser.add_argument('--model', type=str, required=True, default='Autoformer',#选择模型\n", "                    help='model name, options: [Autoformer, Informer, Transformer]')\n", "\n", "# data loader\n", "parser.add_argument('--data', type=str, required=True, default='ETTm1', help='dataset type')#数据类型\n", "parser.add_argument('--root_path', type=str, default='./data/ETT/', help='root path of the data file')#数据文件夹路径\n", "parser.add_argument('--data_path', type=str, default='ETTh1.csv', help='data file')#具体文件\n", "parser.add_argument('--features', type=str, default='M',\n", "                    help='forecasting task, options:[M, S, MS]; M:multivariate predict multivariate, S:univariate predict univariate, MS:multivariate predict univariate')#预测类别\n", "parser.add_argument('--target', type=str, default='OT', help='target feature in S or MS task')#不太懂 OT好像代表Output Target,要预测的单变量\n", "parser.add_argument('--freq', type=str, default='h',\n", "                    help='freq for time features encoding, options:[s:secondly, t:minutely, h:hourly, d:daily, b:business days, w:weekly, m:monthly], you can also use more detailed freq like 15min or 3h')\n", "parser.add_argument('--checkpoints', type=str, default='./checkpoints/', help='location of model checkpoints')#保存模型\n", "\n", "# forecasting task\n", "parser.add_argument('--seq_len', type=int, default=96, help='input sequence length')#输入序列长度\n", "parser.add_argument('--label_len', type=int, default=48, help='start token length')#这个label_len未完全搞懂\n", "parser.add_argument('--pred_len', type=int, default=24, help='prediction sequence length')#输出序列长度\n", "\n", "# model define\n", "parser.add_argument('--bucket_size', type=int, default=4, help='for Reformer')#Reformer专用属性\n", "parser.add_argument('--n_hashes', type=int, default=4, help='for Reformer')#Reformer专用属性\n", "parser.add_argument('--enc_in', type=int, default=7, help='encoder input size')#encoder input size\n", "parser.add_argument('--dec_in', type=int, default=7, help='decoder input size')#decoder input size\n", "parser.add_argument('--c_out', type=int, default=7, help='output size')#输出长度\n", "parser.add_argument('--d_model', type=int, default=512, help='dimension of model')#dimension of model\n", "parser.add_argument('--n_heads', type=int, default=8, help='num of heads')#num of heads \n", "parser.add_argument('--e_layers', type=int, default=2, help='num of encoder layers')#num of encoder layers\n", "parser.add_argument('--d_layers', type=int, default=1, help='num of decoder layers')#num of decoder layers\n", "parser.add_argument('--d_ff', type=int, default=2048, help='dimension of fcn')#dimension of fcn\n", "parser.add_argument('--moving_avg', type=int, default=25, help='window size of moving average')#窗口滑动平均数\n", "parser.add_argument('--factor', type=int, default=1, help='attn factor')#attn factor不太理解\n", "parser.add_argument('--distil', action='store_false',\n", "                    help='whether to use distilling in encoder, using this argument means not using distilling',\n", "                    default=True)#是否在encoder里面使用知识蒸馏\n", "parser.add_argument('--dropout', type=float, default=0.05, help='dropout')#dropout\n", "parser.add_argument('--embed', type=str, default='timeF',\n", "                    help='time features encoding, options:[timeF, fixed, learned]')#time features encoding不太能get到\n", "parser.add_argument('--activation', type=str, default='gelu', help='activation')#激活函数default=gelu\n", "parser.add_argument('--output_attention', action='store_true', help='whether to output attention in encoder')#encoder的output_attention是否输出\n", "parser.add_argument('--do_predict', action='store_true', help='whether to predict unseen future data')#是否预测未见的未来数据,也就是是否进行推理的意思\n", "\n", "# optimization\n", "parser.add_argument('--num_workers', type=int, default=10, help='data loader num workers')# num_workers是加载数据(batch)的线程数目\n", "parser.add_argument('--itr', type=int, default=2, help='experiments times')#实验次数\n", "parser.add_argument('--train_epochs', type=int, default=10, help='train epochs')#就是epoch\n", "parser.add_argument('--batch_size', type=int, default=32, help='batch size of train input data')#bathsize\n", "parser.add_argument('--patience', type=int, default=3, help='early stopping patience')#patience: 当early stop被激活(如发现loss相比上一个epoch训练没有下降)，则经过patience个epoch后停止训练\n", "parser.add_argument('--learning_rate', type=float, default=0.0001, help='optimizer learning rate')#lr\n", "parser.add_argument('--des', type=str, default='test', help='exp description')#test\n", "parser.add_argument('--loss', type=str, default='mse', help='loss function')#loss is mse\n", "parser.add_argument('--lradj', type=str, default='type1', help='adjust learning rate')#adjust learning-rate\n", "parser.add_argument('--use_amp', action='store_true', help='use automatic mixed precision training', default=False)#使用自动混合精度训练\n", "\n", "# GPU\n", "parser.add_argument('--use_gpu', type=bool, default=True, help='use gpu')\n", "parser.add_argument('--gpu', type=int, default=0, help='gpu')\n", "parser.add_argument('--use_multi_gpu', action='store_true', help='use multiple gpus', default=False)\n", "parser.add_argument('--devices', type=str, default='0,1,2,3', help='device ids of multile gpus')\n", "\n", "# args = parser.parse_args()\n", "args = dotdict()\n", "args.target = 'OT'\n", "args.des = 'test'\n", "args.dropout = 0.05\n", "args.num_workers = 10\n", "args.gpu = 0\n", "args.lradj = 'type1'\n", "args.devices = '0'\n", "args.use_gpu = True\n", "args.use_multi_gpu = False\n", "# if args.use_gpu and args.use_multi_gpu: #是否使用多卡的判断\n", "#     args.dvices = args.devices.replace(' ', '')\n", "#     device_ids = args.devices.split(',')\n", "#     args.device_ids = [int(id_) for id_ in device_ids]\n", "#     args.gpu = args.device_ids[0]\n", "args.freq = 'h'\n", "args.checkpoints = './checkpoints/'\n", "args.bucket_size = 4\n", "args.n_hashes = 4\n", "args.is_trainging = True\n", "args.root_path = './dataset/ETT-small/'\n", "args.data_path ='ETTh1.csv' \n", "args.model_id='ETTh1_96_24'\n", "args.model = 'Autoformer'\n", "args.data = 'ETTh1'\n", "args.features = 'M'\n", "args.seq_len = 96\n", "args.label_len = 48\n", "args.pred_len = 24\n", "args.e_layers = 2\n", "args.d_layers = 1\n", "args.n_heads = 8\n", "args.factor = 1\n", "args.enc_in = 7\n", "args.dec_in =7\n", "args.c_out = 7\n", "args.d_model = 512\n", "args.des = 'Exp'\n", "args.itr = 1\n", "args.d_ff = 2048\n", "args.moving_avg = 25\n", "args.factor = 1\n", "args.distil = True\n", "args.output_attention = False\n", "args.patience= 3\n", "args.learning_rate = 0.0001\n", "args.batch_size = 32 \n", "args.embed = 'timeF'\n", "args.activation = 'gelu'\n", "args.use_amp = False\n", "args.loss = 'mse'\n", "args.train_epochs = 10\n", "print('Args in experiment:')\n", "print(args)\n", "\n", "Exp = Exp_Main\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Use GPU: cuda:0\n", "1\n", ">>>>>>>start training : ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0>>>>>>>>>>>>>>>>>>>>>>>>>>\n", "train 8521\n", "val 2857\n", "test 2857\n", "\titers: 100, epoch: 1 | loss: 0.3452503\n", "\tspeed: 0.0606s/iter; left time: 155.1843s\n", "\titers: 200, epoch: 1 | loss: 0.3061770\n", "\tspeed: 0.0336s/iter; left time: 82.5755s\n", "Epoch: 1 cost time: 11.749736309051514\n", "Epoch: 1, Steps: 266 | Train Loss: 0.3544893 Vali Loss: 0.5863745 Test Loss: 0.3798478\n", "Validation loss decreased (inf --> 0.586374).  Saving model ...\n", "Updating learning rate to 0.0001\n", "\titers: 100, epoch: 2 | loss: 0.2983823\n", "\tspeed: 0.1027s/iter; left time: 235.7041s\n", "\titers: 200, epoch: 2 | loss: 0.2859739\n", "\tspeed: 0.0332s/iter; left time: 72.7752s\n", "Epoch: 2 cost time: 9.083820819854736\n", "Epoch: 2, Steps: 266 | Train Loss: 0.2927621 Vali Loss: 0.6249782 Test Loss: 0.4045280\n", "EarlyStopping counter: 1 out of 3\n", "Updating learning rate to 5e-05\n", "\titers: 100, epoch: 3 | loss: 0.2311743\n", "\tspeed: 0.0964s/iter; left time: 195.4976s\n", "\titers: 200, epoch: 3 | loss: 0.2433617\n", "\tspeed: 0.0331s/iter; left time: 63.8016s\n", "Epoch: 3 cost time: 9.000295639038086\n", "Epoch: 3, Steps: 266 | Train Loss: 0.2644387 Vali Loss: 0.6007050 Test Loss: 0.4031203\n", "EarlyStopping counter: 2 out of 3\n", "Updating learning rate to 2.5e-05\n", "\titers: 100, epoch: 4 | loss: 0.2254572\n", "\tspeed: 0.0979s/iter; left time: 172.6233s\n", "\titers: 200, epoch: 4 | loss: 0.2654203\n", "\tspeed: 0.0330s/iter; left time: 54.8957s\n", "Epoch: 4 cost time: 9.05700421333313\n", "Epoch: 4, Steps: 266 | Train Loss: 0.2476054 Vali Loss: 0.5942122 Test Loss: 0.4114987\n", "EarlyStopping counter: 3 out of 3\n", "Early stopping\n", "2\n", ">>>>>>>testing : ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "test 2857\n", "test shape: (89, 32, 24, 7) (89, 32, 24, 7)\n", "test shape: (2848, 24, 7) (2848, 24, 7)\n", "mse:0.3798477351665497, mae:0.4185081720352173\n", "3\n"]}], "source": ["os.environ[\"CUDA_VISIBLE_DEVICES\"] = '0'\n", "\n", "for ii in range(args.itr):#itr就是实验次数可不是epoch，parser.add_argument('--itr', type=int, default=2, help='experiments times')\n", "    # setting record of experiments\n", "    setting = '{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(\n", "        args.model_id,\n", "        args.model,\n", "        args.data,\n", "        args.features,\n", "        args.seq_len,\n", "        args.label_len,\n", "        args.pred_len,\n", "        args.d_model,\n", "        args.n_heads,\n", "        args.e_layers,\n", "        args.d_layers,\n", "        args.d_ff,\n", "        args.factor,\n", "        args.embed,\n", "        args.distil,\n", "        args.des, ii)\n", "\n", "    exp = Exp(args)  # set experiments\n", "    print(1)\n", "    print('>>>>>>>start training : {}>>>>>>>>>>>>>>>>>>>>>>>>>>'.format(setting))\n", "    exp.train(setting)#setting是用来保存模型的名字用的，很细节\n", "    print(2)\n", "    print('>>>>>>>testing : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))\n", "    exp.test(setting)\n", "    torch.cuda.empty_cache()\n", "    print(3)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# custom data: xxx.csv\n", "# data features: ['date', ...(other features), target feature]\n", "\n", "# we take ETTh2 as an example #模仿informer 的 colab example的custom_dataset与predict部分\n", "import pandas as pd\n", "exp.args.root_path = './dataset/ETT-small/'\n", "exp.args.data_path = 'ETTh2.csv'\n", "\n", "df = pd.read_csv(os.path.join(args.root_path, args.data_path))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>predicting : ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "pred 1\n"]}], "source": ["\n", "args.do_predict = True\n", "if args.do_predict:\n", "    print('>>>>>>>predicting : {}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'.format(setting))\n", "    prediction=exp.predict(setting, True)#data_factory做好了pred里面的batch_size=1的情况，是autoformer在informer基础之上做的\n", "    torch.cuda.empty_cache()\n", "    # print(prediction.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([1, 7, 24])\n", "torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAXwAAAD8CAYAAAB0IB+mAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/YYfK9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAk7UlEQVR4nO3dd3hUZd4+8PubXsmkEVImBBIInQQSuihLsQAiKAQVC+pi7z9d9919F3f33eK6RcWya0EUUYorooAVLBQpCSFAaAkljZACpJCemef3R4IipM9Mzsyc+3NduUjOnDnPl7mGm8lzniJKKRARkfNz0boAIiLqHgx8IiKdYOATEekEA5+ISCcY+EREOsHAJyLSCYsCX0TmikimiJhFJKmdc11FJF1E1lvSJhERdY2ln/APAJgD4PsOnPsogEMWtkdERF1kUeArpQ4ppY60d56IRAGYDuBNS9ojIqKuc+umdl4A8DQA//ZOFJFFABYBgK+v78gBAwbYtjIiIieSlpZWqpQKbemxdgNfRL4G0KuFh36jlFrXgefPAFCslEoTkavaO18p9TqA1wEgKSlJpaamtvcUIiJqJiI5rT3WbuArpaZY2P54ANeLyHUAvAD0EJH3lFILLLwuERF1gs2HZSqlfq2UilJKxQCYD2Azw56IqPtZOixztojkAxgLYIOIfNF8PEJENlqjQCIisg6LbtoqpdYCWNvC8VMArmvh+LcAvrWkTSIi6hrOtCUi0gkGPhGRTjDwiYh0goFPTu/gqQpsySrRugwizTHwyaml5ZzFTf/ejruXpaL0fJ3W5RBpioFPTisjrwx3Lt2NQB8P1JvMWLkrV+uSiDTFwCenlHmqHLe9tRMGX3esuW8sJvYPxfIdOWgwmbUujUgzDHxyOkdOV2LBmzvh5+mG9+8ZgwiDNxaOi0FRRR0+O3Ba6/KINMPAJ6eSXXwet765Ax5uLnj/l2NgDPIBAFzZPxR9Qnzx9rYTGldIpB0GPjmNk6VVuOWNHQAEK+4Zg5gQ3x8fc3ER3DG2N9Jzy7A3r0yzGom0xMAnp5B3thq3vLEDDSYzVtwzGnE9/S4756YkI/w83bCMn/JJpxj45PBOldXgljd3oKrehPfuGY34Xi3vs+Pn6Ya5SVHYsL8QxRW13VwlkfYY+OTQiitqceubO1FW1YDld4/C4IiANs+/Y2wMGs0K7+3kEE3SHwY+OazS83W45c2dKK6oxbK7RmFYlKHd58SE+OIX8T3x/s4c1DWabF8kkR1h4JNDOldVjwVv7kTBuRosvTMZI3sHdvi5d46PQen5eqzPKLRhhUT2h4FPDqe8ugEL3tqJ46VVePOOJIzuG9yp50+IC0FcTz8s234SSikbVUlkfxj45FAqaxtw+9u7kFV0Hv+5bSTGx4V0+hoigjvHxWB/QTnScs7ZoEoi+8TAJ7tXWduArw8W4Q+fHsT0l7Yis6Acr9w6ApPie3b5mnNGRKKHlxve3n7SeoUS2TmLtjgksoXaBhPScs5h+7FSbMs+g/0F5TCZFTzdXJAcE4TfzxpsUdgDgI+HG+aPisZbW0/gVFkNIgzeVqqeyH4x8ElzjSYzMvLL8UNzwKflnkN9oxmuLoLhUQF44KpYjI0NxojoQHi5u1qt3dvG9MabW47jvR05ePqaAVa7LpG9YuCTJsxmhQ9252LToWLsOnEW5+saAQADw3vgtjG9MT4uGKP6BMPP03ZvUWOQD6YOCsMHu3LxyOR+Vv3PhMgeMfCp2yml8LtPDuC9HbmICfbB9QkRGBcbjLF9gxHs59mttdw5rg++yCzCur0FSEmO7ta2ibobA5+6lVIKf1h/EO/tyMW9E/vimWsHQEQ0q2dM3yAM6OWPt7edxLwko6a1ENkaR+nQZUxm24xNV0rhuc+P4O1tJ7FwfIzmYQ80DdFcOD4Gh09XYsfxsxZfz2yj147IGhj49DO//Xg/Rv95E7ZmlVr92v/6Ogv//u4Ybh0djd/NGKR52F8wKyESgT7uWLa966toKqXwzvaTGPLsF/gk45QVqyOyHgY+/WjV7ly8tyMXDSYzblu6Ey9vzrLaJ9ZXvsnGS5uyMC8pCn+cNcRuwh4AvNxdcfOoaHx1sAh5Z6s7/fyqukY8unIvFn+SCaWA3607wA3TyS4x8AkAsD+/HP+7LhNX9AvB1l9NwsxhEfj7l0dxz7upKK9usOjab3x/HM9/cQSzEyPxlznD4OJiP2F/wYIxvSEiWL4jp1PPyy4+jxte2Yb1+07hqavj8clD41FdZ8Kzn2TaqFKirmPgE8qq63H/ijSE+nnixfmJ8Pdyx4vzE/CHWYOxJasEM17eggMF5V269jvbT+JPGw9h+tBwPH/TMLjaYdgDQITBG9cM6YWVu3JRXd/Yoees33cKs17eijNV9Xj3rtF4cFIc+oX54+FfxGH9vkJ8mcn9c8m+MPB1zmxWeGzVXhRX1OHVW0cgyNcDQNPNzNvHxmDVvWPRaFKY89p2rNrduTXk39+Zi8WfZGLqoDC8MD8Bbq72/XZbOC4GFbWN+GhPQZvnNZjM+MOnB/HQ++no38sfGx6ZgAn9flrT594rYzGglz/+d90BVNRa9tsRkTXZ979Asrklm7Px7ZES/G7mIAw3Gi57fER0INY/PAGj+wThV//dj6fWZKC2of115D9My8dvPt6PSfGhePmWRLjbedgDwMjegRgaGdDmKpqny2tx8+s7sHTbCdw5LgarFo1FeMDPl2XwcHPB324ahpLKOvxl4+HuKJ2oQ+z/XyEBaAqa+kazVa/57ZFivLDpKOaMiMSto1ufdBTs54llC0fhkV/EYU1aPua8uh05Z6paPX/d3gI8/WEGxseG4LUFI+Hp5hgzWC+sopldfB5bsy8fpbQ9uxQzlmzBwcIKvHRzIp69fjA83Fr+JzQsyoB7ruiLD3bl4odjZ6xea029yeJ7K6Q/DHwHcKK0ChOf/wZzXtvWpVEkLck/V43HVu1FfJg//nTD0HZHzbi6CJ6YFo+370xGQVkNZizZiq8OFl123mf7C/HE6gwkxwThjduTHG65ghnDwxHi54G3t5388ZjZrPDqt9lY8NZOBHi7Y92D43H98Ih2r/X4lP7oHeyDX3+0DzX11ttdq7C8BtNe+A5z/7Od6/lTpzDwHcBfPzsENxdBzplqzFiyFZsPXx60nVHbYMIDK/bAZFb494KR8PboeChPGtAT6x+egJhgX/zy3VT87fPDaDQ1/ebx9cEiPPxBOhKMBiy9M7lT17UXnm6uuGV0b2w+XIwTpVUor2nAouVp+NvnR3Dd0HCse2gC+oW1vEn6pbw9XPGXOUNx8kw1Xvj6qFXqK66oxS1v7ET+uRocLTqPXScsnyxG+sHAt3O7TpzFF5lFuP/KWKx/eAIiDd64a1kq/vHlkS7PiP39pwexL78c/5g7HDEhvp1+vjHIB2vuG4ubR0Xj1W+P4ba3duHj9AI8sGIPBkf0wNsLk+Frw0XPbG3B6Gi4uwr+vPEQZi7Zim+PFGPxzEFYcnNipxdzGxcbgptHGfHGluPYl19mUV0X9vAtqqjF8rtGw9/TDat251l0TdIXBr4dM5sV/rThIHr18MI9V/RF72BffPTAOMxLisKSzdm4Y+kunOnkBJ81qXn4YFcu7r8qFtMG9+pybV7uTZ9en79pGPbknsNjq/aiX5gf3r1rNHp4uXf5uvagZw8vTB8ajq8OFqG+0YxV947BwvF9ujxZ7JlrByLU3xNPf7gPDaau3Ye5sIdv/rlqLL0zGRP6hWBmQgQ2HijkSCDqMAa+HVu/vxAZ+eX4f1fH/9g94uXuir/dNBzP3TgUu06exYwlW7Ent2Pb9GWeKsdvPz6AcbHBeHJqf6vUODfJiLUPjMfC8TFYfvdoBPg4dthf8OS0ePzyij5Y/8gEjOwdZNG1Arzd8cdZQ3D4dCX+892xTj+/vKYBty1t3sP39mSMad7Dd36yEbUNZnyyl0s5UMdYFPgiMldEMkXELCJJbZx3UkT2i8heEUm1pE29qG0w4bnPDmNQeA/MToy87PGU5Gh8dP84uLkKUv7zA95pZ0Pu8uoG3P/eHgT6eOClmxOtOiZ+UEQPLJ45+Mcx/M7AGOSD30wfhBArLdc8bXAvTB8Wjpc2ZSO7uLLDz6usbcAdS3fhyOlK/GfByJ+N9x8aGYABvfzZrUMdZum/+gMA5gD4vgPnTlJKJSilWv2PgX7y7g8nUVBWg99MH9jq7NQhkQFY/9AVmNgvFIs/ycQjK/eiqu7yWaJms8ITq/eisLwGr9w6wmohRp3z7MzB8PF0xa/+u79DaxRV1TXirmW7caCgHK/cMgKTBvx8W0cRwfxkI/YXlCPzVNdmQpO+WBT4SqlDSqkj1iqGmpyrqseSzdmYFB+K8XEhbZ4b4OOON25PwlNXx2PDvlOY9co2ZBef/9k5r313DJsOF+O30wdhZO9AW5ZObQj198TvZgxCWs65dtfsqak34Z53UpGWcw4vzk9s9X7LDYmR8HBzwWp+yqcO6K4+fAXgSxFJE5FFbZ0oIotEJFVEUktKSrqpPPvy0uYsVNU14tfXDezQ+S4uggcnxWH53aNxrqoes17eivX7mvp1t2aV4h9fHsH1wyNw+9jetiybOmB2YiQm9g/Fc58fRv65ludU1DaYsGh5KnacOIN/zkvA9GHhrV7P4OOBawb3wtr0gg7NgCZ9azfwReRrETnQwtesTrQzQSk1AsC1AB4UkYmtnaiUel0plaSUSgoNDe1EE87hRGkVlv+Qg5TkaPTv4HjvC8bHhWD9IxMQ38sfD72fjv9Zux+PrExHXE8//PXG9idXke2JCP48ewgA4DdrD1x236W+0YwHVuzBlqxSPDdnGG5o4f7NpVKSjaiobcQXXKyN2tFu4CulpiilhrTwta6jjSilCpr/LAawFsCorpfs3J777DA83Fzw+NR+XXp+eIA3Vi4ai4XjY/D+zlzUN5rx2oKR8PFw3HHxziYq0Ae/umYAvjtagrXpPy3U1mAy4+EP9mDz4WL83w1DMC/Z2KHrje0bDGOQN2/eUrts3qUjIr4i4n/hewDT0HSzly6x++RZfJ55GvddGYue/l5dvo6HmwsWzxyMd+4aheV3j0JsqJ8VqyRruG1Mb4zsHYg/rD+I0vN1MJkVnlidgS8yi7B45iAsGNPx7jcXF8G8kUZsP3amzTWOiCwdljlbRPIBjAWwQUS+aD4eISIbm08LA7BVRDIA7AKwQSn1uSXtOiOlFP5vwyGE9fDEL6/oa5VrXtk/FInRvElrj1xcBM/dOBTVdSYsXpeJpz7MwKcZp/Drawdg4fg+nb7eTUlRcBFgTWq+DaolZ2HR7/lKqbVo6qK59PgpANc1f38cwHBL2tGD9fsKkZFXhudvGuaQa9BQ58X19Mcjk+Pw9y+b1tl5Ymp/3HtlbJeuFR7gjSv7h+LDtHw8NqWf3e89QNrgu8IO1DWa8NznhzEwvAfmjIjSuhzqRvdeGYtpg8Lw1NXxeGRy1+7bXJCSHI3TFbX4Pkufo9uofbyTZwfe3Z6D/HM1eO9u+90CkGzD3dUFr99unbmIkwf2RIifB1btzsMvBoRZ5ZrkXPgJX2NNk6yycFV86M+mzRN1lrurC24cEYVNh4pRUtm5RfVIHxj4GluyORvn6xrx62s7NsmKqC1zk4xoNCt8tIc3b+lyDHwNnSytwvIdJ5GSbER8r85NsiJqSVxPPyTHBGLV7jzuhkWXYeBr6LnPD8Pd1QWPT7HOUsVEADAvyYjjpVVIzenYstmkHwx8jaSePIvPDpzGvRNj0bNH1ydZEV1q+rBw+Hm6YeUuzryln2Pga+Bnk6wmdn6SDVFbfDzcMHN4BDbu525Y9HMMfA1s2F+IvXlleHJaPNe4IZuYn2xETYMJn2ZwNyz6CdPGAvnnqrEtuxSdvTf2yrfZGNDLHzdykhXZyLCopt2wVu/Ow62juSw2NWHgd0FRRS1e3pyNlbtz0WDq/EgID1cXLFvISVZkOyKClGQjfv/pQRwqrMDA8B5al0R2gIHfCWfO1+Hf3x3Duz/kwGRWmJdsxF3jY+Dr2bmX0cfdzWk2+yb7dUNCJP6y8TBW7c7Ds9cP1rocsgNOGfgVtQ3o4WW9QC2vbsAbW45j6bYTqG0wYXZiFB6d3A/RwT5Wa4PI2gJ9PTBtcBg+3luAZ64dAC93Lsqnd04X+GazwqTnv0WAjzvGx4ZgXGwwxsYGw+Dj0elrna9rxNtbT+D1LcdRWduIGcPC8diU/ojryfXlyTHMT47G+n2F+PJgEa4fHqF1OaQxpwv8BrMZ918Vi23ZpfhoTz6W78iBCDAovAfGx4VgbGwwRsUEtdkNU1NvwvIdJ/Hat8dwrroBUwaG4Ymp/TEogv2g5FjGxQYjKtAbq3fnMfAJYs/Tr5OSklRqamqXn99gMmNffhm2ZZ/B9mOl2JNThnqTGW4uggSjAePimn4DSIw2wNPNFXWNJqzanYeXN2ejuLIOV/QLwZPT4pFgNFjvL0XUzV7alIV/fnUUW56eBGMQuyGdnYikKaVaXILVqQP/UjX1JqTlnMO2Y6XYfuwM9ueXwawAL3cXJMcE4XhJFQrKajAqJghPTuuP0X2DrdY2kVZOldVg/HOb8fCkODwxLV7rcsjG2gp8p+vSaYu3hysm9Av5cRni8poG7DpxFtuyS/HDsTOIMHjhrzcOxYS4EIhwyCQ5hwhD025Ya9Ly8eiU/hwOrGO6CvxLBXi7Y+qgMEwdxM0iyLmlJBlx/4o9+D6rBJPie2pdDmmESysQ6cDkgWEI9vXA6t1cUE3PGPhEOuDh5oIbR0bhq4NFyMgr07oc0ggDn0gn7r8yFmE9vPDAij04W1WvdTmkAQY+kU4E+nrgtQUjUFJZh0dXpsNktt8RemQbDHwiHRkWZcDvZw3GlqxSvLgpS+tyqJsx8Il0Zn6yEXNHRuGlTVn45nCx1uVQN2LgE+mMiOCPNwzBoPAeeGzVXuSdrda6JOomuh6HT6RXXu6u+PeCkZixZAvuX5GGD+8bZ9XVNNNyzuJUWW2nnzc8ysBVaG2IgU+kU9HBPvhXSgLuficVi9dl4rmbhll8zfpGM/688RCWbT/Zpef3D/PDF49N5Ex3G2HgE+nY5IFheGhSHF7+JhsjehuQkhzd5WsVltfggRV7kJ5bhrsn9MH8ZCM6k9tfHSzGc58fRnpeGUZEB3a5DmodA59I5x6f2h8Z+WX433WZGBwRgCGRAZ2+xtasUjyyMh11DSa8eusIXDc0vNPX6BXgjSWbs7B6dx4D30Z405ZI51xdBC/OT0SIrwfuey8NZdUdn5RlNiu8vDkLty3diWBfD3zy8IQuhT0A+Hm6YfrQcHyacQpVdY1duga1jYFPRAjy9cArt45AUUUtHl+1F+YOTMoqr27APe+m4u9fHsX1wyPw8YPjERtq2W5wKclGVNWbsGFfoUXXoZYx8IkIAJAYHYjfzRyMb46U4OVvsts890BBOaYv2YItWSX446zBeCEloc1d5DpqZO9AxIb6YuXuXIuvRZdj4BPRjxaMjsacxEj86+uj+O5oyWWPK6Wwclcu5ry2HSazwup7x+K2sTFWG1UjIkhJNmJPbhmyiiqtck36CQOfiH4kIvjT7KGID/PHoyvTkX/up0lZtQ0mPP3hPjzz0X6M7hOE9Q9PQKINbq7OGREFNxfBKi7lbHUWBb6IzBWRTBExi0iLW2o1n2cQkQ9F5LCIHBKRsZa0S0S24+3hitcWjITJpPDgij2oazThZGkVZr+6HWvS8vHI5H5YtnAUgv08bdJ+iJ8npgwMw0fpBahvNNukDb2y9BP+AQBzAHzfznkvAvhcKTUAwHAAhyxsl4hsqE+IL/4xbzgy8sux6N00zHx5K06V1eDtO5PxxFTbb5OYMsqIs1X1+PpQkU3b0RuLAl8pdUgpdaStc0QkAMBEAG81P6deKVVmSbtEZHvTBvfCfVfG4rujJYgJ9sX6hydg0oDu2R5xYr9QhAd4sVvHyrpj4lUfACUA3haR4QDSADyqlKpq6WQRWQRgEQBER3d91h8RWe6pq+Mxpm8QxvQNtupaO+1xdRHMHRmFJd9k41RZDSIM3t3WtjNr9xO+iHwtIgda+JrVwTbcAIwA8JpSKhFAFYBnWjtZKfW6UipJKZUUGhrawSaIyBZcXQRXxffs1rC/YG6SEUoBa1Lzu71tZ9XuJ3yl1BQL28gHkK+U2tn884doI/CJiADAGOSDCXEhWJ2ah4d/EQcXG9830AObD8tUSp0GkCci8c2HJgM4aOt2icjxzUs2oqCsBtuOlWpdilOwdFjmbBHJBzAWwAYR+aL5eISIbLzo1IcBrBCRfQASAPzZknaJSB+mDQqDwcedN2+txKKbtkqptQDWtnD8FIDrLvp5L4BWx+kTEbXEy90VNyRE4v2duThXVY9AXw+tS3JonGlLRHYtJdmIepMZa9MLtC7F4THwiciuDQzvgeFRAVi1Ow9Ktb+KJ7WOgU9Edi8lORpHiiqRkV+udSkOjYFPRHZv5vBweLu78uathRj4RGT3/L3ccV3zbljV9dwNq6sY+ETkEOaPMuJ8XSN3w7IAA5+IHEJS70D0DfVlt44FGPhE5BBEBClJRqTmnEN28Xmty3FIDHwichgXdsNancpP+V3BwCcihxHq74nJA3vioz353A2rCxj4RORQUpKNKD1fj82HuRtWZzHwicihTOwXil49uBtWVzDwicihuLm64KaRUfjuaAkKy2u0LsehMPCJyOHMSzLCrIAPuRtWpzDwicjhRAf7YFxsMFan5cFs5oJqHcXAJyKHlJJsRN7ZGvxw/IzWpTgMBj4ROaSrB/dCgDd3w+oMBj4ROSQvd1dcPzwCn2eeRl2jSetyHAIDn4gc1rjYYNQ3mnGosFLrUhwCA5+IHFZCtAEAkJ57TttCHAQDn4gcVniAN8J6eGJvXpnWpTgEBj4RObQEo4GB30EMfCJyaInRgcg5U42zVfVal2L3GPhE5NASjAYAQAY/5beLgU9EDm1oZABchDduO4KBT0QOzdfTDf3D/JHOT/jtYuATkcNLjDYgI6+M6+q0g4FPRA4v0RiIitpGnDhTpXUpdo2BT0QO78IErL25ZZrWYe8Y+ETk8GJD/eDn6Yb0PN64bQsDn4gcnquLYFhUACdgtYOBT0ROIcFowOHCStQ2cOXM1jDwicgpJBgNaDQrHCgo17oUu8XAJyKn8OONW3brtIqBT0ROoae/FyIN3kjnSJ1WMfCJyGkkRHPlzLZYFPgiMldEMkXELCJJrZwTLyJ7L/qqEJHHLGmXiKgliUYDCspqUFxZq3UpdsnST/gHAMwB8H1rJyiljiilEpRSCQBGAqgGsNbCdomILnNh5UxOwGqZRYGvlDqklDrSiadMBnBMKZVjSbtERC0ZEhkANxdht04rursPfz6AD9o6QUQWiUiqiKSWlJR0U1lE5Ay83F0xMLwHb9y2ot3AF5GvReRAC1+zOtOQiHgAuB7AmrbOU0q9rpRKUkolhYaGdqYJIiIkGA3Yl18GE1fOvIxbeycopaZYqa1rAexRShVZ6XpERJdJMBqwfEcOsovPI76Xv9bl2JXu7NK5Ge105xARWeqnCVhcSO1Slg7LnC0i+QDGAtggIl80H48QkY0XnecLYCqAjyxpj4ioPX2CfdHDy403blvQbpdOW5RSa9HCEEul1CkA1130cxWAYEvaIiLqCBcXQUJ0IG/ctoAzbYnI6SQYDThaVImqukatS7ErDHwicjqJRgPMCtiXz5UzL8bAJyKnM/zCjFv24/8MA5+InE6Qrwd6B/twpM4lGPhE5JQSjAak55ZBKU7AuoCBT0ROKdFoQHFlHQrLuXLmBQx8InJKCdGBANiPfzEGPhE5pYHh/vBwdWHgX4SBT0ROydPNFYMienBt/Isw8InIaSUYDdhXUIZGk1nrUuwCA5+InFZitAG1DWYcPl2pdSl2gYFPRE4r0cgbtxdj4BOR0zIGeSPI14OB34yBT0ROS0SQYDQw8Jsx8InIqSUYDcguPo/ymgatS9EcA5+InFpC80Jq+/LLNK3DHjDwicip/bhyJsfjM/CJyLkFeLsjNtSX/fhg4BORDiQYA7E3jytnMvCJyOklRBtwpqoe+edqtC5FUwx8InJ6ic39+Hty9b0hCgOfiJxefC9/eLpx5UwGPhE5PXdXFwyLCmDga10AEVF3SDAakHmqAvWN+l05k4FPRLqQYAxEfaMZhwortC5FMwx8ItKFhGgDACBdxzduGfhEpAsRAV4I9ffUdT8+A5+IdEFEkKjzlTMZ+ESkGwnRBpw8U41zVfVal6IJBj4R6caFlTP36nTlTAY+EenGsCgDRIB0na6cycAnIt3w83RD/57+uu3HZ+ATka4kGA3I0OnKmQx8ItKVxGgDymsacKzkvNaldDsGPhHpysT+oRAB1u09pXUp3c6iwBeRuSKSKSJmEUlq47zHm887ICIfiIiXJe0SEXVVhMEbV/YPxZrUfJjM+urWsfQT/gEAcwB839oJIhIJ4BEASUqpIQBcAcy3sF0ioi5LSTLidEUtvj9aonUp3cqiwFdKHVJKHenAqW4AvEXEDYAPAP39LkVEdmPywDAE+3pg5e5crUvpVjbvw1dKFQD4O4BcAIUAypVSX7Z2vogsEpFUEUktKdHX/75E1D083FwwZ0QkNh0qRkllndbldJt2A19Evm7ue7/0a1ZHGhCRQACzAPQBEAHAV0QWtHa+Uup1pVSSUiopNDS0o38PIqJOSUk2otGssDY9X+tSuo1beycopaZY2MYUACeUUiUAICIfARgH4D0Lr0tE1GVxPf0xsncgVu7Owy+v6AsR0bokm+uOYZm5AMaIiI80vaKTARzqhnaJiNqUkmTE8ZIqpOXoY418S4dlzhaRfABjAWwQkS+aj0eIyEYAUErtBPAhgD0A9je3+bpFVRMRWcH0YeHw9XDFyt15WpfSLSwdpbNWKRWllPJUSoUppa5uPn5KKXXdRectVkoNUEoNUUrdppTSz10SIrJbvp5umDk8Ahv2FaKytkHrcmyOM22JSNdSko2oaTDh04xCrUuxOQY+EelagtGA/mF+WJXq/N06DHwi0jURQUpyNDLyynD4dIXW5dgUA5+IdG92YiTcXQWrnPzmLQOfiHQvyNcD0wb3wtr0AtQ1mrQux2YY+EREaBqTX1bdgC8zi7QuxWYY+EREACbEhSDS4I3VTnzzloFPRATAxUUwNykKW7JKkXe2WutybIKBT0TUbG6SESLAmjTnXFCNgU9E1CzS4I0r+oViTWqeU+6GxcAnIrrI/GQjCstrsSXL+fbjYOATEV1kysAwBPl6OOWYfAY+EdFFPNxcMCcxEl8fKsKZ8861ziMDn4joEinJRjSYFNamF2hdilUx8ImILtEvzB8jog1YuTsPSjnPzVsGPhFRC1KSjcguPo89uWVal2I1DHwiohbMGBYBXw9XrNqdq3UpVsPAJyJqga+nG2YMi8D6fYU4X9eodTlWwcAnImrFvGQjqutNWJ9xSutSrIKBT0TUihHRBvTr6Ty7YTHwiYha0bQblhHpuWU4WlSpdTkWY+ATEbXBmXbDYuATEbUh2M8TUweFOcVuWG5aF0BEZO9SkqOxcf9pTP3n9/B0s/3n5EAfD6y+b6zVr8vAJyJqx4S4ECwcH4Oiitpuaa+Hl7tNrsvAJyJqh6uLYPHMwVqXYTH24RMR6QQDn4hIJxj4REQ6wcAnItIJBj4RkU4w8ImIdIKBT0SkEwx8IiKdEHver1FESgDkdPHpIQBKrViOo+Lr0ISvQxO+Dk2c+XXorZQKbekBuw58S4hIqlIqSes6tMbXoQlfhyZ8HZro9XVglw4RkU4w8ImIdMKZA/91rQuwE3wdmvB1aMLXoYkuXwen7cMnIqKfc+ZP+EREdBEGPhGRTjhd4IvINSJyRESyReQZrevRkoicFJH9IrJXRFK1rqe7iMhSESkWkQMXHQsSka9EJKv5z0Ata+wOrbwOz4pIQfN7Yq+IXKdljd1BRIwi8o2IHBSRTBF5tPm47t4TThX4IuIK4BUA1wIYBOBmERmkbVWam6SUStDZmONlAK655NgzADYppfoB2NT8s7NbhstfBwD4V/N7IkEptbGba9JCI4AnlVKDAIwB8GBzLujuPeFUgQ9gFIBspdRxpVQ9gJUAZmlcE3UzpdT3AM5ecngWgHeav38HwA3dWZMWWnkddEcpVaiU2tP8fSWAQwAiocP3hLMFfiSAvIt+zm8+plcKwJcikiYii7QuRmNhSqnC5u9PAwjTshiNPSQi+5q7fJy+G+NiIhIDIBHATujwPeFsgU8/N0EpNQJNXVwPishErQuyB6ppLLJexyO/BiAWQAKAQgD/0LSabiQifgD+C+AxpVTFxY/p5T3hbIFfAMB40c9Rzcd0SSlV0PxnMYC1aOry0qsiEQkHgOY/izWuRxNKqSKllEkpZQbwBnTynhARdzSF/Qql1EfNh3X3nnC2wN8NoJ+I9BERDwDzAXyicU2aEBFfEfG/8D2AaQAOtP0sp/YJgDuav78DwDoNa9HMhYBrNhs6eE+IiAB4C8AhpdQ/L3pId+8Jp5tp2zzM7AUArgCWKqX+pG1F2hCRvmj6VA8AbgDe18trISIfALgKTUvgFgFYDOBjAKsBRKNpye15SimnvqHZyutwFZq6cxSAkwDuvagf2ymJyAQAWwDsB2BuPvw/aOrH19d7wtkCn4iIWuZsXTpERNQKBj4RkU4w8ImIdIKBT0SkEwx8IiKdYOATEekEA5+ISCf+PyaXCU2tiqZsAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([24])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "import torch.nn.functional as F\n", "import torch\n", "import matplotlib.pyplot as plt \n", "import numpy as np\n", "import matplotlib\n", "# data_pred = np.load('./results/ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0/pred.npy')\n", "data_pred = np.load('./results/ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0/real_prediction.npy')\n", "data_pred = torch.from_numpy(data_pred).permute(0,2,1)\n", "\n", "plt.figure()\n", "print(data_pred.shape)\n", "#预测OT\n", "plt.plot(data_pred[-1,-1,:])#由于prediction.shape是[1,24,7]那么batch只有1 索引只能是0或-1 都是代表batch这一维本身,如果是加载np文件就不一样了\n", "print(data_pred[-1,-1,:].shape)\n", "plt.show()\n", "plt.plot(data_pred[0,-1,:])#没问题\n", "print(data_pred[0,-1,:].shape)\n", "plt.show()\n", "# draw HUFL prediction\n", "plt.plot(data_pred[0,0,:])#没问题\n", "print(data_pred[-1,-1,:].shape)\n", "plt.show()\n", "'''\n", "Ground Truth\n", "'''\n", "data_gt = np.load('./results/ETTh1_96_24_Autoformer_ETTh1_ftM_sl96_ll48_pl24_dm512_nh8_el2_dl1_df2048_fc1_ebtimeF_dtTrue_Exp_0/true.npy')\n", "data_gt = torch.from_numpy(data_gt).permute(0,2,1)\n", "\n", "#预测OT\n", "plt.plot(data_gt[-1,-1,:])#由于prediction.shape是[1,24,7]那么batch只有1 索引只能是0或-1 都是代表batch这一维本身,如果是加载np文件就不一样了\n", "print(data_gt[-1,-1,:].shape)\n", "plt.show()\n", "plt.plot(data_gt[0,-1,:])#没问题\n", "print(data_gt[0,-1,:].shape)\n", "plt.show()\n", "# draw HUFL prediction\n", "plt.plot(data_gt[0,0,:])#没问题\n", "print(data_gt[-1,-1,:].shape)\n", "plt.show()\n", "\n"]}], "metadata": {"interpreter": {"hash": "f57785bf53e86c458d31dd8512073d1ac6cae98f342ec9a1a9a8506681d63dcb"}, "kernelspec": {"display_name": "Python 3.7.13 ('openmmlab')", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
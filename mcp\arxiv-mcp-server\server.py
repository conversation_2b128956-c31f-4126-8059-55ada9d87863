#!/usr/bin/env python3
"""
ArXiv MCP Server
A simple MCP server for searching and retrieving papers from arXiv.
"""

import json
import os
import sys
from typing import Any, Dict, List, Optional, Union

import arxiv
from modelcontextprotocol import (
    MCPServer,
    MCPTool,
    MCPToolCall,
    MCPToolCallResult,
    MCPToolParameter,
    MCPToolParameterType,
)

class ArXivMCPServer(MCPServer):
    """MCP server for arXiv."""

    def __init__(self):
        super().__init__(
            name="arxiv-server",
            description="A MCP server for searching and retrieving papers from arXiv.",
            version="0.1.0",
        )
        self.register_tool(
            MCPTool(
                name="search_arxiv",
                description="Search for papers on arXiv",
                parameters=[
                    MCPToolParameter(
                        name="query",
                        description="The search query",
                        type=MCPToolParameterType.STRING,
                        required=True,
                    ),
                    MCPToolParameter(
                        name="max_results",
                        description="Maximum number of results to return (default: 5)",
                        type=MCPToolParameterType.INTEGER,
                        required=False,
                    ),
                    MCPToolParameter(
                        name="sort_by",
                        description="Sort criterion (relevance, lastUpdatedDate, submittedDate)",
                        type=MCPToolParameterType.STRING,
                        required=False,
                    ),
                ],
                handler=self.search_arxiv,
            )
        )
        self.register_tool(
            MCPTool(
                name="get_paper_by_id",
                description="Get a paper by its arXiv ID",
                parameters=[
                    MCPToolParameter(
                        name="arxiv_id",
                        description="The arXiv ID of the paper",
                        type=MCPToolParameterType.STRING,
                        required=True,
                    ),
                ],
                handler=self.get_paper_by_id,
            )
        )

    async def search_arxiv(self, call: MCPToolCall) -> MCPToolCallResult:
        """Search for papers on arXiv."""
        query = call.parameters.get("query")
        max_results = call.parameters.get("max_results", 5)
        sort_by_str = call.parameters.get("sort_by", "relevance")
        
        sort_mapping = {
            "relevance": arxiv.SortCriterion.Relevance,
            "lastUpdatedDate": arxiv.SortCriterion.LastUpdatedDate,
            "submittedDate": arxiv.SortCriterion.SubmittedDate,
        }
        
        sort_by = sort_mapping.get(sort_by_str, arxiv.SortCriterion.Relevance)
        
        try:
            search = arxiv.Search(
                query=query,
                max_results=max_results,
                sort_by=sort_by,
            )
            
            results = []
            for paper in search.results():
                results.append({
                    "title": paper.title,
                    "authors": [author.name for author in paper.authors],
                    "summary": paper.summary,
                    "published": paper.published.strftime("%Y-%m-%d"),
                    "updated": paper.updated.strftime("%Y-%m-%d") if paper.updated else None,
                    "pdf_url": paper.pdf_url,
                    "entry_id": paper.entry_id,
                    "arxiv_id": paper.get_short_id(),
                    "categories": paper.categories,
                })
            
            return MCPToolCallResult(
                content=json.dumps(results),
                content_type="application/json",
            )
        except Exception as e:
            return MCPToolCallResult(
                content=json.dumps({"error": str(e)}),
                content_type="application/json",
            )

    async def get_paper_by_id(self, call: MCPToolCall) -> MCPToolCallResult:
        """Get a paper by its arXiv ID."""
        arxiv_id = call.parameters.get("arxiv_id")
        
        try:
            search = arxiv.Search(id_list=[arxiv_id])
            results = list(search.results())
            
            if not results:
                return MCPToolCallResult(
                    content=json.dumps({"error": f"No paper found with ID {arxiv_id}"}),
                    content_type="application/json",
                )
            
            paper = results[0]
            result = {
                "title": paper.title,
                "authors": [author.name for author in paper.authors],
                "summary": paper.summary,
                "published": paper.published.strftime("%Y-%m-%d"),
                "updated": paper.updated.strftime("%Y-%m-%d") if paper.updated else None,
                "pdf_url": paper.pdf_url,
                "entry_id": paper.entry_id,
                "arxiv_id": paper.get_short_id(),
                "categories": paper.categories,
                "comment": paper.comment,
                "journal_ref": paper.journal_ref,
                "doi": paper.doi,
            }
            
            return MCPToolCallResult(
                content=json.dumps(result),
                content_type="application/json",
            )
        except Exception as e:
            return MCPToolCallResult(
                content=json.dumps({"error": str(e)}),
                content_type="application/json",
            )

def main():
    """Run the MCP server."""
    server = ArXivMCPServer()
    server.run()

if __name__ == "__main__":
    main()

name: Run Tests

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
    types: [opened, synchronize, reopened]

jobs:
  test:
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
        os: [ubuntu-latest, windows-latest, macos-latest]

    runs-on: ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install uv (Linux/macOS)
      if: runner.os != 'Windows'
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
        echo "$HOME/.cargo/bin" >> $GITHUB_PATH
        
    - name: Install uv (Windows)
      if: runner.os == 'Windows'
      run: |
        # Install uv
        iwr -useb https://astral.sh/uv/install.ps1 | iex
        # Add uv to PATH
        echo "$HOME\.uv\bin" >> $GITHUB_PATH

    - name: Install dependencies
      run: |
        uv pip install --system pytest pytest-cov pytest-asyncio
        uv pip install --system -e ".[test]"
        # If you don't have a [test] extra, use:
        # uv pip install --system -r requirements-test.txt
        # or just:
        # uv pip install --system -e .

    # Run tests differently based on platform
    - name: Run tests on Linux/macOS
      if: runner.os != 'Windows'
      run: |
        pytest --cov=./ --cov-report=xml -v
        
    - name: Run tests on Windows
      if: runner.os == 'Windows' 
      run: |
        pytest --cov=./ --cov-report=xml -v

        
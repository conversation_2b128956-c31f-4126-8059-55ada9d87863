#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CausalFormer心电-脑电因果关系分析 - 数据标准化模块

功能：
- 对EEG和ECG数据进行标准化处理
- 确保数据在相同的数值范围内
- 处理异常值和离群点

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"causalformer_data_normalizer_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("CausalFormer-DataNormalizer")

class DataNormalizeError(Exception):
    """数据标准化错误的自定义异常类"""
    pass

def validate_data(data, data_name):
    """
    验证数据的有效性

    参数:
    data (np.ndarray): 要验证的数据
    data_name (str): 数据名称，用于错误消息

    异常:
    DataNormalizeError: 数据无效
    """
    # 检查数据是否为None
    if data is None:
        error_msg = f"{data_name} 数据为None"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    # 检查数据是否为NumPy数组
    if not isinstance(data, np.ndarray):
        error_msg = f"{data_name} 数据不是NumPy数组"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    # 检查数据维度
    if data.ndim != 3:
        error_msg = f"{data_name} 数据维度不正确，应为3维 [n_epochs, n_channels, n_times]: {data.shape}"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    # 检查数据是否包含NaN或Inf
    if np.isnan(data).any():
        error_msg = f"{data_name} 数据包含NaN值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    if np.isinf(data).any():
        error_msg = f"{data_name} 数据包含Inf值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    # 检查数据是否为常数
    if np.all(data == data[0, 0, 0]):
        error_msg = f"{data_name} 数据全为常数: {data[0, 0, 0]}"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

def z_score_normalize(data, axis=None, window_size=None):
    """
    使用Z-score方法标准化数据，支持分段标准化

    参数:
    data (np.ndarray): 要标准化的数据
    axis (int or tuple): 计算均值和标准差的轴
    window_size (int): 分段标准化的窗口大小（以样本点数计），None表示不使用分段标准化

    返回:
    np.ndarray: 标准化后的数据
    """
    # 如果不使用分段标准化，直接对整个数据进行标准化
    if window_size is None:
        # 计算均值和标准差
        mean = np.mean(data, axis=axis, keepdims=True)
        std = np.std(data, axis=axis, keepdims=True)

        # 避免除以零
        std = np.where(std > 0, std, 1.0)

        # 标准化
        normalized_data = (data - mean) / std

        return normalized_data

    # 使用分段标准化
    # 确保数据是2D的 [epochs, times] 或 [channels, times]
    if data.ndim == 3:  # [epochs, channels, times]
        original_shape = data.shape
        # 对每个通道分别进行分段标准化
        normalized_data = np.zeros_like(data)

        for i in range(data.shape[1]):
            normalized_data[:, i, :] = z_score_normalize_windowed(data[:, i, :], window_size)

        return normalized_data
    else:
        return z_score_normalize_windowed(data, window_size)

def z_score_normalize_windowed(data, window_size):
    """
    使用滑动窗口进行Z-score标准化

    参数:
    data (np.ndarray): 要标准化的数据，形状为 [epochs, times] 或 [times]
    window_size (int): 窗口大小（以样本点数计）

    返回:
    np.ndarray: 标准化后的数据
    """
    # 确保数据是2D的 [epochs, times]
    if data.ndim == 1:
        data = data.reshape(1, -1)

    normalized_data = np.zeros_like(data)
    n_epochs, n_times = data.shape

    # 计算窗口数量
    n_windows = int(np.ceil(n_times / window_size))

    # 对每个epoch分别进行分段标准化
    for epoch in range(n_epochs):
        for w in range(n_windows):
            # 计算窗口的起止索引
            start_idx = w * window_size
            end_idx = min((w + 1) * window_size, n_times)

            # 提取窗口数据
            window_data = data[epoch, start_idx:end_idx]

            # 计算均值和标准差
            mean = np.mean(window_data)
            std = np.std(window_data)

            # 避免除以零
            if std == 0:
                std = 1.0

            # 标准化窗口数据
            normalized_data[epoch, start_idx:end_idx] = (window_data - mean) / std

    # 如果原始数据是1D的，返回1D结果
    if data.shape[0] == 1:
        return normalized_data[0]
    else:
        return normalized_data

def min_max_normalize(data, feature_range=(0, 1), axis=None, window_size=None):
    """
    使用Min-Max方法标准化数据，支持分段标准化

    参数:
    data (np.ndarray): 要标准化的数据
    feature_range (tuple): 标准化后的范围
    axis (int or tuple): 计算最小值和最大值的轴
    window_size (int): 分段标准化的窗口大小（以样本点数计），None表示不使用分段标准化

    返回:
    np.ndarray: 标准化后的数据
    """
    # 如果不使用分段标准化，直接对整个数据进行标准化
    if window_size is None:
        # 计算最小值和最大值
        min_val = np.min(data, axis=axis, keepdims=True)
        max_val = np.max(data, axis=axis, keepdims=True)

        # 避免除以零
        if np.all(min_val == max_val):
            return np.zeros_like(data)

        # 标准化
        normalized_data = (data - min_val) / (max_val - min_val)

        # 缩放到指定范围
        normalized_data = normalized_data * (feature_range[1] - feature_range[0]) + feature_range[0]

        return normalized_data

    # 使用分段标准化
    # 确保数据是2D的 [epochs, times] 或 [channels, times]
    if data.ndim == 3:  # [epochs, channels, times]
        # 对每个通道分别进行分段标准化
        normalized_data = np.zeros_like(data)

        for i in range(data.shape[1]):
            normalized_data[:, i, :] = min_max_normalize_windowed(data[:, i, :], feature_range, window_size)

        return normalized_data
    else:
        return min_max_normalize_windowed(data, feature_range, window_size)

def min_max_normalize_windowed(data, feature_range=(0, 1), window_size=None):
    """
    使用滑动窗口进行Min-Max标准化

    参数:
    data (np.ndarray): 要标准化的数据，形状为 [epochs, times] 或 [times]
    feature_range (tuple): 标准化后的范围
    window_size (int): 窗口大小（以样本点数计）

    返回:
    np.ndarray: 标准化后的数据
    """
    # 确保数据是2D的 [epochs, times]
    if data.ndim == 1:
        data = data.reshape(1, -1)

    normalized_data = np.zeros_like(data)
    n_epochs, n_times = data.shape

    # 计算窗口数量
    n_windows = int(np.ceil(n_times / window_size))

    # 对每个epoch分别进行分段标准化
    for epoch in range(n_epochs):
        for w in range(n_windows):
            # 计算窗口的起止索引
            start_idx = w * window_size
            end_idx = min((w + 1) * window_size, n_times)

            # 提取窗口数据
            window_data = data[epoch, start_idx:end_idx]

            # 计算最小值和最大值
            min_val = np.min(window_data)
            max_val = np.max(window_data)

            # 避免除以零
            if min_val == max_val:
                normalized_data[epoch, start_idx:end_idx] = 0
                continue

            # 标准化窗口数据
            norm_window = (window_data - min_val) / (max_val - min_val)

            # 缩放到指定范围
            norm_window = norm_window * (feature_range[1] - feature_range[0]) + feature_range[0]

            normalized_data[epoch, start_idx:end_idx] = norm_window

    # 如果原始数据是1D的，返回1D结果
    if data.shape[0] == 1:
        return normalized_data[0]
    else:
        return normalized_data

def robust_normalize(data, axis=None, window_size=None):
    """
    使用稳健的标准化方法（基于中位数和IQR），支持分段标准化

    参数:
    data (np.ndarray): 要标准化的数据
    axis (int or tuple): 计算中位数和IQR的轴
    window_size (int): 分段标准化的窗口大小（以样本点数计），None表示不使用分段标准化

    返回:
    np.ndarray: 标准化后的数据
    """
    # 如果不使用分段标准化，直接对整个数据进行标准化
    if window_size is None:
        # 计算中位数和IQR
        median = np.median(data, axis=axis, keepdims=True)
        q75 = np.percentile(data, 75, axis=axis, keepdims=True)
        q25 = np.percentile(data, 25, axis=axis, keepdims=True)
        iqr = q75 - q25

        # 避免除以零
        iqr = np.where(iqr > 0, iqr, 1.0)

        # 标准化
        normalized_data = (data - median) / iqr

        return normalized_data

    # 使用分段标准化
    # 确保数据是2D的 [epochs, times] 或 [channels, times]
    if data.ndim == 3:  # [epochs, channels, times]
        # 对每个通道分别进行分段标准化
        normalized_data = np.zeros_like(data)

        for i in range(data.shape[1]):
            normalized_data[:, i, :] = robust_normalize_windowed(data[:, i, :], window_size)

        return normalized_data
    else:
        return robust_normalize_windowed(data, window_size)

def robust_normalize_windowed(data, window_size):
    """
    使用滑动窗口进行稳健标准化

    参数:
    data (np.ndarray): 要标准化的数据，形状为 [epochs, times] 或 [times]
    window_size (int): 窗口大小（以样本点数计）

    返回:
    np.ndarray: 标准化后的数据
    """
    # 确保数据是2D的 [epochs, times]
    if data.ndim == 1:
        data = data.reshape(1, -1)

    normalized_data = np.zeros_like(data)
    n_epochs, n_times = data.shape

    # 计算窗口数量
    n_windows = int(np.ceil(n_times / window_size))

    # 对每个epoch分别进行分段标准化
    for epoch in range(n_epochs):
        for w in range(n_windows):
            # 计算窗口的起止索引
            start_idx = w * window_size
            end_idx = min((w + 1) * window_size, n_times)

            # 提取窗口数据
            window_data = data[epoch, start_idx:end_idx]

            # 计算中位数和IQR
            median = np.median(window_data)
            q75 = np.percentile(window_data, 75)
            q25 = np.percentile(window_data, 25)
            iqr = q75 - q25

            # 避免除以零
            if iqr == 0:
                iqr = 1.0

            # 标准化窗口数据
            normalized_data[epoch, start_idx:end_idx] = (window_data - median) / iqr

    # 如果原始数据是1D的，返回1D结果
    if data.shape[0] == 1:
        return normalized_data[0]
    else:
        return normalized_data

def calculate_adaptive_threshold(data):
    """
    计算自适应异常值阈值

    参数:
    data (np.ndarray): 要计算阈值的数据

    返回:
    float: 自适应阈值
    """
    # 计算数据的偏度
    skewness = np.abs(np.mean(((data - np.mean(data)) / np.std(data)) ** 3))

    # 根据偏度调整阈值
    if skewness < 0.5:  # 接近正态分布
        return 3.0
    elif skewness < 1.0:  # 轻度偏斜
        return 2.5
    elif skewness < 2.0:  # 中度偏斜
        return 2.0
    else:  # 严重偏斜
        return 1.5

def handle_outliers(data, threshold=3.0, method='clip', adaptive=False):
    """
    处理异常值

    参数:
    data (np.ndarray): 要处理的数据
    threshold (float): 异常值阈值（标准差的倍数）
    method (str): 处理方法，'clip'表示截断，'remove'表示移除
    adaptive (bool): 是否使用自适应阈值

    返回:
    np.ndarray: 处理后的数据
    """
    # 如果使用自适应阈值，计算适合的阈值
    if adaptive:
        threshold = calculate_adaptive_threshold(data)

    # 计算均值和标准差
    mean = np.mean(data)
    std = np.std(data)

    # 定义上下限
    lower_bound = mean - threshold * std
    upper_bound = mean + threshold * std

    if method == 'clip':
        # 截断异常值
        clipped_data = np.clip(data, lower_bound, upper_bound)
        return clipped_data
    elif method == 'remove':
        # 将异常值替换为NaN
        data_copy = data.copy()
        data_copy[(data < lower_bound) | (data > upper_bound)] = np.nan

        # 使用插值填充NaN
        mask = np.isnan(data_copy)
        data_copy[mask] = np.interp(np.flatnonzero(mask), np.flatnonzero(~mask), data_copy[~mask])

        return data_copy
    else:
        raise ValueError(f"不支持的方法: {method}")

def detect_outliers(data, threshold=3.0):
    """
    检测异常值

    参数:
    data (np.ndarray): 要检测的数据
    threshold (float): 异常值阈值（标准差的倍数）

    返回:
    tuple: (异常值掩码, 异常值数量, 异常值比例)
    """
    # 计算均值和标准差
    mean = np.mean(data)
    std = np.std(data)

    # 定义上下限
    lower_bound = mean - threshold * std
    upper_bound = mean + threshold * std

    # 创建异常值掩码
    outlier_mask = (data < lower_bound) | (data > upper_bound)

    # 计算异常值数量和比例
    outlier_count = np.sum(outlier_mask)
    outlier_ratio = outlier_count / data.size

    return outlier_mask, outlier_count, outlier_ratio

def normalize_data(stages_data, method='z_score', handle_outliers_method=None, outlier_threshold=3.0,
                detect_outliers_only=False, window_size=None, adaptive_threshold=False):
    """
    标准化数据

    参数:
    stages_data (dict): 各阶段数据
    method (str): 标准化方法，'z_score'、'min_max'或'robust'
    handle_outliers_method (str): 异常值处理方法，None表示不处理，'clip'表示截断，'remove'表示移除
    outlier_threshold (float): 异常值阈值（标准差的倍数）
    detect_outliers_only (bool): 是否只检测异常值而不处理
    window_size (int): 分段标准化的窗口大小（以样本点数计），None表示不使用分段标准化
    adaptive_threshold (bool): 是否使用自适应异常值阈值

    返回:
    dict: 标准化后的数据

    异常:
    DataNormalizeError: 数据标准化失败
    """
    logger.info(f"使用 {method} 方法标准化数据...")

    if window_size:
        logger.info(f"使用分段标准化，窗口大小: {window_size} 样本点")

    if adaptive_threshold:
        logger.info("使用自适应异常值阈值")

    if handle_outliers_method:
        logger.info(f"将处理异常值，方法: {handle_outliers_method}, 阈值: {outlier_threshold}")
    elif detect_outliers_only:
        logger.info(f"将只检测异常值而不处理，阈值: {outlier_threshold}")

    # 存储标准化后的数据
    normalized_data = {}

    for stage, data in stages_data.items():
        logger.info(f"标准化阶段 {stage} 的数据...")

        # 提取EEG和ECG数据
        eeg_data = data['eeg']  # [n_epochs, n_channels, n_times]
        ecg_data = data['ecg']  # [n_epochs, n_channels, n_times]

        # 验证数据
        validate_data(eeg_data, f"阶段 {stage} 的EEG")
        validate_data(ecg_data, f"阶段 {stage} 的ECG")

        # 检测异常值
        eeg_outliers = {}
        ecg_outliers = {}

        if detect_outliers_only or handle_outliers_method:
            logger.info(f"  - 检测异常值，阈值: {outlier_threshold}")

            # 对每个通道分别检测异常值
            for i in range(eeg_data.shape[1]):
                channel_name = data['eeg_channels'][i]
                outlier_mask, outlier_count, outlier_ratio = detect_outliers(eeg_data[:, i, :], outlier_threshold)
                eeg_outliers[channel_name] = {
                    'count': outlier_count,
                    'ratio': outlier_ratio,
                    'mask': outlier_mask
                }
                logger.info(f"  - EEG通道 {channel_name}: 检测到 {outlier_count} 个异常值 ({outlier_ratio:.2%})")

            for i in range(ecg_data.shape[1]):
                channel_name = data['ecg_channels'][i]
                outlier_mask, outlier_count, outlier_ratio = detect_outliers(ecg_data[:, i, :], outlier_threshold)
                ecg_outliers[channel_name] = {
                    'count': outlier_count,
                    'ratio': outlier_ratio,
                    'mask': outlier_mask
                }
                logger.info(f"  - ECG通道 {channel_name}: 检测到 {outlier_count} 个异常值 ({outlier_ratio:.2%})")

        # 处理异常值（如果需要）
        if handle_outliers_method:
            logger.info(f"  - 处理异常值，方法: {handle_outliers_method}")

            # 对每个通道分别处理异常值
            for i in range(eeg_data.shape[1]):
                channel_name = data['eeg_channels'][i]
                eeg_data[:, i, :] = handle_outliers(eeg_data[:, i, :], outlier_threshold,
                                                   handle_outliers_method, adaptive_threshold)

                # 如果使用自适应阈值，记录实际使用的阈值
                if adaptive_threshold:
                    actual_threshold = calculate_adaptive_threshold(eeg_data[:, i, :])
                    logger.info(f"  - 处理EEG通道 {channel_name} 的异常值，自适应阈值: {actual_threshold:.2f}")
                else:
                    logger.info(f"  - 处理EEG通道 {channel_name} 的异常值")

            for i in range(ecg_data.shape[1]):
                channel_name = data['ecg_channels'][i]
                ecg_data[:, i, :] = handle_outliers(ecg_data[:, i, :], outlier_threshold,
                                                   handle_outliers_method, adaptive_threshold)

                # 如果使用自适应阈值，记录实际使用的阈值
                if adaptive_threshold:
                    actual_threshold = calculate_adaptive_threshold(ecg_data[:, i, :])
                    logger.info(f"  - 处理ECG通道 {channel_name} 的异常值，自适应阈值: {actual_threshold:.2f}")
                else:
                    logger.info(f"  - 处理ECG通道 {channel_name} 的异常值")

        # 标准化数据
        if method == 'z_score':
            # 对每个通道分别进行Z-score标准化
            normalized_eeg = np.zeros_like(eeg_data)
            normalized_ecg = np.zeros_like(ecg_data)

            for i in range(eeg_data.shape[1]):
                normalized_eeg[:, i, :] = z_score_normalize(eeg_data[:, i, :], axis=0, window_size=window_size)

            for i in range(ecg_data.shape[1]):
                normalized_ecg[:, i, :] = z_score_normalize(ecg_data[:, i, :], axis=0, window_size=window_size)

        elif method == 'min_max':
            # 对每个通道分别进行Min-Max标准化
            normalized_eeg = np.zeros_like(eeg_data)
            normalized_ecg = np.zeros_like(ecg_data)

            for i in range(eeg_data.shape[1]):
                normalized_eeg[:, i, :] = min_max_normalize(eeg_data[:, i, :], axis=0, window_size=window_size)

            for i in range(ecg_data.shape[1]):
                normalized_ecg[:, i, :] = min_max_normalize(ecg_data[:, i, :], axis=0, window_size=window_size)

        elif method == 'robust':
            # 对每个通道分别进行稳健标准化
            normalized_eeg = np.zeros_like(eeg_data)
            normalized_ecg = np.zeros_like(ecg_data)

            for i in range(eeg_data.shape[1]):
                normalized_eeg[:, i, :] = robust_normalize(eeg_data[:, i, :], axis=0, window_size=window_size)

            for i in range(ecg_data.shape[1]):
                normalized_ecg[:, i, :] = robust_normalize(ecg_data[:, i, :], axis=0, window_size=window_size)

        else:
            error_msg = f"不支持的标准化方法: {method}"
            logger.error(error_msg)
            raise DataNormalizeError(error_msg)

        # 验证标准化后的数据并获取质量评估结果
        quality_assessment = validate_normalized_data(normalized_eeg, normalized_ecg, method)

        # 存储标准化后的数据
        normalized_data[stage] = data.copy()
        normalized_data[stage]['eeg'] = normalized_eeg
        normalized_data[stage]['ecg'] = normalized_ecg
        normalized_data[stage]['normalization_method'] = method
        normalized_data[stage]['quality_assessment'] = quality_assessment

        if detect_outliers_only or handle_outliers_method:
            normalized_data[stage]['eeg_outliers'] = eeg_outliers
            normalized_data[stage]['ecg_outliers'] = ecg_outliers
            normalized_data[stage]['outlier_threshold'] = outlier_threshold

        if handle_outliers_method:
            normalized_data[stage]['outlier_handling_method'] = handle_outliers_method

        if window_size:
            normalized_data[stage]['window_size'] = window_size

        if adaptive_threshold:
            normalized_data[stage]['adaptive_threshold'] = True

        # 打印标准化结果
        logger.info(f"  - EEG: 均值={np.mean(normalized_eeg):.4f}, 标准差={np.std(normalized_eeg):.4f}, 最小值={np.min(normalized_eeg):.4f}, 最大值={np.max(normalized_eeg):.4f}")
        logger.info(f"  - ECG: 均值={np.mean(normalized_ecg):.4f}, 标准差={np.std(normalized_ecg):.4f}, 最小值={np.min(normalized_ecg):.4f}, 最大值={np.max(normalized_ecg):.4f}")

    logger.info("数据标准化完成")

    return normalized_data

def test_normality(data):
    """
    测试数据的正态性

    参数:
    data (np.ndarray): 要测试的数据

    返回:
    tuple: (是否正态分布, p值, 偏度, 峰度)
    """
    from scipy import stats

    # 将数据展平
    flat_data = data.flatten()

    # 计算偏度和峰度
    skewness = stats.skew(flat_data)
    kurtosis = stats.kurtosis(flat_data)

    # 进行Shapiro-Wilk正态性检验
    # 对于大样本，随机抽取5000个点进行检验
    if len(flat_data) > 5000:
        sample_indices = np.random.choice(len(flat_data), 5000, replace=False)
        sample_data = flat_data[sample_indices]
        _, p_value = stats.shapiro(sample_data)
    else:
        _, p_value = stats.shapiro(flat_data)

    # p值大于0.05表示数据符合正态分布
    is_normal = p_value > 0.05

    return is_normal, p_value, skewness, kurtosis

def validate_normalized_data(eeg_data, ecg_data, method):
    """
    验证标准化后的数据，并进行质量评估

    参数:
    eeg_data (np.ndarray): 标准化后的EEG数据
    ecg_data (np.ndarray): 标准化后的ECG数据
    method (str): 标准化方法

    异常:
    DataNormalizeError: 标准化后的数据无效
    """
    # 检查是否包含NaN或Inf
    if np.isnan(eeg_data).any():
        error_msg = "标准化后的EEG数据包含NaN值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    if np.isnan(ecg_data).any():
        error_msg = "标准化后的ECG数据包含NaN值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    if np.isinf(eeg_data).any():
        error_msg = "标准化后的EEG数据包含Inf值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    if np.isinf(ecg_data).any():
        error_msg = "标准化后的ECG数据包含Inf值"
        logger.error(error_msg)
        raise DataNormalizeError(error_msg)

    # 根据标准化方法检查数据分布
    if method == 'z_score':
        # Z-score标准化后，均值应接近0，标准差应接近1
        eeg_mean = np.mean(eeg_data)
        eeg_std = np.std(eeg_data)
        ecg_mean = np.mean(ecg_data)
        ecg_std = np.std(ecg_data)

        # 允许一定的误差
        if abs(eeg_mean) > 0.1:
            logger.warning(f"标准化后的EEG数据均值偏离0较大: {eeg_mean:.4f}")

        if abs(ecg_mean) > 0.1:
            logger.warning(f"标准化后的ECG数据均值偏离0较大: {ecg_mean:.4f}")

        if abs(eeg_std - 1.0) > 0.5:
            logger.warning(f"标准化后的EEG数据标准差偏离1较大: {eeg_std:.4f}")

        if abs(ecg_std - 1.0) > 0.5:
            logger.warning(f"标准化后的ECG数据标准差偏离1较大: {ecg_std:.4f}")

    elif method == 'min_max':
        # Min-Max标准化后，数据应在[0, 1]范围内
        eeg_min = np.min(eeg_data)
        eeg_max = np.max(eeg_data)
        ecg_min = np.min(ecg_data)
        ecg_max = np.max(ecg_data)

        if eeg_min < 0 or eeg_max > 1:
            logger.warning(f"标准化后的EEG数据超出[0, 1]范围: [{eeg_min:.4f}, {eeg_max:.4f}]")

        if ecg_min < 0 or ecg_max > 1:
            logger.warning(f"标准化后的ECG数据超出[0, 1]范围: [{ecg_min:.4f}, {ecg_max:.4f}]")

    # 进行正态性检验
    logger.info("进行标准化质量评估...")

    # 检查EEG数据的正态性
    eeg_normal, eeg_p, eeg_skew, eeg_kurt = test_normality(eeg_data)
    logger.info(f"EEG数据正态性检验: {'通过' if eeg_normal else '未通过'}, p值={eeg_p:.4f}, 偏度={eeg_skew:.4f}, 峰度={eeg_kurt:.4f}")

    # 检查ECG数据的正态性
    ecg_normal, ecg_p, ecg_skew, ecg_kurt = test_normality(ecg_data)
    logger.info(f"ECG数据正态性检验: {'通过' if ecg_normal else '未通过'}, p值={ecg_p:.4f}, 偏度={ecg_skew:.4f}, 峰度={ecg_kurt:.4f}")

    # 计算异常值比例
    eeg_outliers, eeg_count, eeg_ratio = detect_outliers(eeg_data, threshold=3.0)
    ecg_outliers, ecg_count, ecg_ratio = detect_outliers(ecg_data, threshold=3.0)

    logger.info(f"EEG数据异常值比例: {eeg_ratio:.2%}")
    logger.info(f"ECG数据异常值比例: {ecg_ratio:.2%}")

    # 如果异常值比例过高，发出警告
    if eeg_ratio > 0.01:  # 超过1%
        logger.warning(f"EEG数据异常值比例过高: {eeg_ratio:.2%}")

    if ecg_ratio > 0.01:  # 超过1%
        logger.warning(f"ECG数据异常值比例过高: {ecg_ratio:.2%}")

    # 返回质量评估结果
    quality_assessment = {
        'eeg_normality': eeg_normal,
        'eeg_p_value': eeg_p,
        'eeg_skewness': eeg_skew,
        'eeg_kurtosis': eeg_kurt,
        'eeg_outlier_ratio': eeg_ratio,
        'ecg_normality': ecg_normal,
        'ecg_p_value': ecg_p,
        'ecg_skewness': ecg_skew,
        'ecg_kurtosis': ecg_kurt,
        'ecg_outlier_ratio': ecg_ratio
    }

    return quality_assessment

def test_data_normalizer(stages_data):
    """
    测试数据标准化模块

    参数:
    stages_data (dict): 各阶段数据

    返回:
    bool: 测试结果
    """
    logger.info("开始测试数据标准化模块...")

    try:
        # 测试Z-score标准化
        logger.info("测试Z-score标准化...")
        _ = normalize_data(stages_data, method='z_score')

        # 测试Min-Max标准化
        logger.info("测试Min-Max标准化...")
        _ = normalize_data(stages_data, method='min_max')

        # 测试稳健标准化
        logger.info("测试稳健标准化...")
        _ = normalize_data(stages_data, method='robust')

        # 测试异常值处理
        logger.info("测试异常值处理...")
        _ = normalize_data(stages_data, method='z_score', handle_outliers_method='clip', outlier_threshold=3.0)

        # 测试分段标准化
        logger.info("测试分段标准化...")
        window_size = 15000  # 30秒 @ 500Hz
        _ = normalize_data(stages_data, method='z_score', window_size=window_size)

        # 测试自适应异常值阈值
        logger.info("测试自适应异常值阈值...")
        _ = normalize_data(stages_data, method='z_score', handle_outliers_method='clip', adaptive_threshold=True)

        # 测试组合功能
        logger.info("测试组合功能（分段标准化 + 自适应异常值阈值）...")
        _ = normalize_data(stages_data, method='z_score', handle_outliers_method='clip',
                        window_size=window_size, adaptive_threshold=True)

        logger.info("数据标准化模块测试成功!")
        return True

    except DataNormalizeError as e:
        logger.error(f"数据标准化失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"测试过程中发生未知错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 导入数据加载和通道处理模块
    from data_loader import load_hep_data
    from channel_processor import process_channels

    # 加载数据
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    stages_data = load_hep_data(data_dir)

    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    processed_data = process_channels(stages_data, key_channels)

    # 测试数据标准化模块
    test_data_normalizer(processed_data)

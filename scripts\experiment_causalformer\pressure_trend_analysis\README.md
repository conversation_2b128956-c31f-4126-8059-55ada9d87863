# 压力趋势分析模块

本模块用于分析心电-脑电数据在不同实验阶段的压力变化趋势，基于CausalFormer框架实现。

## 功能特点

- 根据实验设计的压力变化规律构建预测模型
- 考虑静息态和刺激态的压力水平变化特点
- 支持多通道EEG和ECG数据的联合分析
- 自动提取时域和频域特征
- 基于自注意力机制的因果关系分析
- 可视化压力变化趋势和因果强度

## 文件说明

- `data_preparation.py`: 数据准备模块，负责加载和预处理数据
- `pressure_trend_model.py`: 模型定义和训练模块
- `run_analysis.py`: 主执行脚本，实现完整的分析流程
- `test_implementation.py`: 测试脚本，用于验证各个组件的正确性

## 使用方法

### 1. 测试实现

```bash
python test_implementation.py
```

该命令会运行测试脚本，验证数据加载、特征提取和模型组件的正确性。

### 2. 运行完整分析

```bash
python run_analysis.py --data_dir "D:\ecgeeg\19-eegecg手动预处理6-ICA3" --subject "01_01"
```

参数说明:
- `--data_dir`: 数据目录路径
- `--output_dir`: 输出目录路径，默认为`D:\ecgeeg\30-数据分析\5-NeuroKit2\result\causalformer\pressure_trend_analysis`
- `--subject`: 指定被试ID，如"01_01"，默认为None表示处理所有被试
- `--sequence_length`: 序列长度，默认为3
- `--batch_size`: 批次大小，默认为32
- `--hidden_dim`: 隐藏层维度，默认为128
- `--epochs`: 训练轮数，默认为100
- `--learning_rate`: 学习率，默认为0.001
- `--cpu`: 强制使用CPU，即使GPU可用

### 3. 分步执行

如果需要更灵活的控制，可以导入相关模块分步执行：

```python
from pressure_trend_analysis.data_preparation import load_fif_data, prepare_stage_data, create_pressure_trend_dataset
from pressure_trend_analysis.pressure_trend_model import PressureTrendModel, train_pressure_trend_model, evaluate_model

# 1. 加载数据
data_by_subject = load_fif_data(data_dir)

# 2. 准备分阶段数据
feature_data = prepare_stage_data(data_by_subject)

# 3. 创建数据集
eeg_combined, ecg_combined, pressure_labels, _ = create_pressure_trend_dataset(feature_data)

# 4. 创建和训练模型
# ...具体步骤见run_analysis.py
```

## 模型架构

该模块实现了一个特殊的压力趋势预测模型，具有以下特点：

1. **独立的EEG和ECG编码器**：分别处理EEG和ECG特征
2. **双向LSTM层**：捕获时间序列依赖关系
3. **多头自注意力机制**：学习信号内部和跨信号的因果关系
4. **跨信号交互层**：建模EEG和ECG之间的交互作用
5. **压力预测头**：根据编码特征预测压力水平

## 输出结果

分析完成后，将在输出目录产生以下结果：

- `visualizations/`: 可视化结果，包括训练历史、预测结果等
- `models/`: 保存训练好的模型
- `results/`: 评估指标和预测结果
- `causal_analysis/`: 因果分析结果，包括因果强度矩阵等

## 注意事项

1. 确保数据目录包含正确格式的fif文件
2. 如果GPU内存不足，可以减小批次大小或隐藏层维度
3. 根据实际数据量调整训练轮数 
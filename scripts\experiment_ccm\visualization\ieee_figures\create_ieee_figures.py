#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脑-心非线性交互分析IEEE期刊风格图表生成脚本

该脚本用于生成符合IEEE期刊标准的高质量组合图表，包括：
- 信号处理流程图：ECG和EEG预处理和频段分解
- 收敛交叉映射(CCM)分析示意图
- 不同焦虑水平组之间因果关系的对比图

所有图表使用英文标签，符合IEEE期刊的国际标准。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.patches import Patch, Rectangle, FancyArrowPatch
import seaborn as sns
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.font_manager import FontProperties

# 添加父目录到路径，以便导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置IEEE期刊风格
plt.style.use('seaborn-v0_8-paper')
plt.rcParams['figure.figsize'] = (7.2, 5.4)  # IEEE单栏宽度约为3.5英寸，双栏约为7.2英寸
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 600  # 更高分辨率用于发表
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman']  # IEEE推荐字体
plt.rcParams['font.size'] = 9
plt.rcParams['axes.titlesize'] = 10
plt.rcParams['axes.labelsize'] = 9
plt.rcParams['xtick.labelsize'] = 8
plt.rcParams['ytick.labelsize'] = 8
plt.rcParams['legend.fontsize'] = 8
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.linewidth'] = 0.5
plt.rcParams['lines.linewidth'] = 1.0
plt.rcParams['axes.unicode_minus'] = True

# 全局变量
RESULTS_DIR = 'results/nonlinear_interaction'
OUTPUT_DIR = 'results/nonlinear_interaction/figures/publication'

# 设置IEEE期刊风格的配色方案
IEEE_COLORS = {
    'high_anxiety': '#E64B35',  # 红色
    'medium_anxiety': '#4DBBD5',  # 蓝色
    'low_anxiety': '#00A087',  # 绿色
    'eeg_to_hr': '#3C5488',  # 深蓝色 (脑→心)
    'hr_to_eeg': '#F39B7F'   # 橙色 (心→脑)
}

def load_results():
    """
    加载CCM分析结果

    返回:
    dict: CCM分析结果
    """
    results_path = os.path.join(RESULTS_DIR, 'ccm_analysis_results.pkl')
    if not os.path.exists(results_path):
        print(f"Error: Results file does not exist: {results_path}")
        return None

    try:
        results = pd.read_pickle(results_path)
        print(f"Successfully loaded results file with data from {len(results)} subjects")
        return results
    except Exception as e:
        print(f"Error loading results file: {e}")
        return None

def create_comprehensive_ieee_figure(output_file='ieee_comprehensive_figure.svg'):
    """
    创建符合IEEE期刊标准的综合图表

    参数:
    output_file (str): 输出文件名
    """
    print("Creating comprehensive IEEE-style figure...")

    # 创建画布
    fig = plt.figure(figsize=(7.2, 9))  # IEEE双栏宽度，高度适当调整

    # 使用网格布局
    gs = gridspec.GridSpec(3, 2, height_ratios=[1, 1, 1.2])

    # 1. 信号处理流程图（顶部）
    create_signal_processing_panel(fig, gs[0, :])

    # 2. CCM分析原理（中部）
    create_ccm_principle_panel(fig, gs[1, :])

    # 3. 实验结果（底部）
    create_results_panel(fig, gs[2, :])

    # 调整布局
    plt.tight_layout()

    # 保存图表
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    output_path = os.path.join(OUTPUT_DIR, output_file)
    plt.savefig(output_path, dpi=600, bbox_inches='tight')
    print(f"Comprehensive IEEE-style figure saved to: {output_path}")
    plt.close()

def create_signal_processing_panel(fig, grid_pos):
    """
    创建信号处理流程面板

    参数:
    fig (Figure): matplotlib图形对象
    grid_pos (GridSpec): 网格位置
    """
    # 创建子图
    ax = fig.add_subplot(grid_pos)
    ax.set_title('Signal Processing Pipeline', fontsize=10)
    ax.axis('off')

    # 创建ECG和EEG处理流程图
    # 定义流程步骤
    ecg_steps = ['Raw ECG Signal', 'R-peak Detection', 'RR Interval', 'Heart Rate Estimation', 'HR Time Series']
    eeg_steps = ['Raw EEG Signal', 'Band-pass Filtering', 'Hilbert Transform', 'Envelope Extraction', 'Band Envelope Series']

    # 定义位置
    ecg_y = 0.75
    eeg_y = 0.35
    step_positions = np.linspace(0.1, 0.9, len(ecg_steps))
    box_width = 0.14
    box_height = 0.12

    # 绘制ECG处理流程
    for i, step in enumerate(ecg_steps):
        # 添加框
        rect = Rectangle((step_positions[i]-box_width/2, ecg_y-box_height/2), box_width, box_height,
                         fill=True, alpha=0.7, color='#B3D9FF', linewidth=0.5, edgecolor='black')
        ax.add_patch(rect)

        # 添加文本
        ax.text(step_positions[i], ecg_y, step, ha='center', va='center', fontsize=8)

        # 添加连接箭头
        if i < len(ecg_steps) - 1:
            arrow = FancyArrowPatch(
                (step_positions[i]+box_width/2, ecg_y),
                (step_positions[i+1]-box_width/2, ecg_y),
                arrowstyle='->', linewidth=0.8, color='black'
            )
            ax.add_patch(arrow)

    # 绘制EEG处理流程
    for i, step in enumerate(eeg_steps):
        # 添加框
        rect = Rectangle((step_positions[i]-box_width/2, eeg_y-box_height/2), box_width, box_height,
                         fill=True, alpha=0.7, color='#C2E0C2', linewidth=0.5, edgecolor='black')
        ax.add_patch(rect)

        # 添加文本
        ax.text(step_positions[i], eeg_y, step, ha='center', va='center', fontsize=8)

        # 添加连接箭头
        if i < len(eeg_steps) - 1:
            arrow = FancyArrowPatch(
                (step_positions[i]+box_width/2, eeg_y),
                (step_positions[i+1]-box_width/2, eeg_y),
                arrowstyle='->', linewidth=0.8, color='black'
            )
            ax.add_patch(arrow)

    # 添加EEG频段
    bands = ['δ (0.5-4 Hz)', 'θ (4-8 Hz)', 'α (8-13 Hz)', 'β (13-30 Hz)', 'γ (30-45 Hz)']
    band_colors = ['#FF9999', '#99CC99', '#FFCC99', '#9999FF', '#CC99CC']
    band_y_positions = np.linspace(0.15, -0.05, len(bands))

    for i, (band, color) in enumerate(zip(bands, band_colors)):
        # 添加频段框
        rect = Rectangle(
            (step_positions[3]-box_width/2, band_y_positions[i]-0.03),
            box_width, 0.06,
            fill=True, alpha=0.7, color=color, linewidth=0.5, edgecolor='black'
        )
        ax.add_patch(rect)

        # 添加文本
        ax.text(step_positions[3], band_y_positions[i], band, ha='center', va='center', fontsize=7)

        # 添加从Hilbert变换到频段的箭头
        arrow1 = FancyArrowPatch(
            (step_positions[2], eeg_y-box_height/2),
            (step_positions[3]-box_width/4, band_y_positions[i]),
            arrowstyle='->', linewidth=0.5, color='gray',
            connectionstyle='arc3,rad=-0.1'
        )
        ax.add_patch(arrow1)

        # 添加从频段到最终结果的箭头
        arrow2 = FancyArrowPatch(
            (step_positions[3]+box_width/2, band_y_positions[i]),
            (step_positions[4]-box_width/2, eeg_y),
            arrowstyle='->', linewidth=0.5, color='gray',
            connectionstyle='arc3,rad=0.1'
        )
        ax.add_patch(arrow2)

    # 添加标签
    ax.text(0.02, ecg_y+0.1, "ECG", fontsize=9, fontweight='bold')
    ax.text(0.02, eeg_y+0.1, "EEG", fontsize=9, fontweight='bold')

def create_ccm_principle_panel(fig, grid_pos):
    """
    创建CCM分析原理面板

    参数:
    fig (Figure): matplotlib图形对象
    grid_pos (GridSpec): 网格位置
    """
    # 创建子图
    ax = fig.add_subplot(grid_pos)
    ax.set_title('Convergent Cross Mapping (CCM) Principle', fontsize=10)
    ax.axis('off')

    # 创建CCM原理图
    # 定义系统位置
    pos_x = 0.25
    pos_y = 0.75
    box_width = 0.15
    box_height = 0.2

    # 系统X (HR)
    rect_x = Rectangle(
        (pos_x-box_width/2, 0.5-box_height/2),
        box_width, box_height,
        fill=True, alpha=0.7, color='#B3D9FF',
        linewidth=0.5, edgecolor='black'
    )
    ax.add_patch(rect_x)
    ax.text(pos_x, 0.5, "System X\n(HR)", ha='center', va='center', fontsize=9)

    # 系统Y (EEG)
    rect_y = Rectangle(
        (pos_y-box_width/2, 0.5-box_height/2),
        box_width, box_height,
        fill=True, alpha=0.7, color='#C2E0C2',
        linewidth=0.5, edgecolor='black'
    )
    ax.add_patch(rect_y)
    ax.text(pos_y, 0.5, "System Y\n(EEG)", ha='center', va='center', fontsize=9)

    # 添加Y→X因果关系箭头 (心→脑)
    arrow_y_to_x = FancyArrowPatch(
        (pos_y-box_width/2, 0.55),
        (pos_x+box_width/2, 0.55),
        arrowstyle='<-', linewidth=1.5, color=IEEE_COLORS['hr_to_eeg'],
        connectionstyle='arc3,rad=0.3'
    )
    ax.add_patch(arrow_y_to_x)
    ax.text((pos_x+pos_y)/2, 0.65, "Y→X Causality\n(Heart→Brain)",
            ha='center', va='center', fontsize=8, color=IEEE_COLORS['hr_to_eeg'])

    # 添加X→Y因果关系箭头 (脑→心)
    arrow_x_to_y = FancyArrowPatch(
        (pos_x+box_width/2, 0.45),
        (pos_y-box_width/2, 0.45),
        arrowstyle='->', linewidth=1.5, color=IEEE_COLORS['eeg_to_hr'],
        connectionstyle='arc3,rad=0.3'
    )
    ax.add_patch(arrow_x_to_y)
    ax.text((pos_x+pos_y)/2, 0.35, "X→Y Causality\n(Brain→Heart)",
            ha='center', va='center', fontsize=8, color=IEEE_COLORS['eeg_to_hr'])

    # 添加CCM数学公式
    ax.text(0.5, 0.15, r"$\rho_{X \rightarrow Y}$ vs. $\rho_{Y \rightarrow X}$ indicates causality direction",
            ha='center', va='center', fontsize=8, style='italic')
    ax.text(0.5, 0.08, r"Higher $\rho$ value suggests stronger causal influence",
            ha='center', va='center', fontsize=8, style='italic')

    # 添加右侧的收敛曲线示例
    # 创建小子图
    ax_inset = fig.add_axes([0.65, 0.35, 0.3, 0.3])

    # 绘制示例收敛曲线
    library_sizes = np.linspace(50, 500, 10)
    rho_x_to_y = 0.7 * (1 - np.exp(-0.005 * library_sizes))
    rho_y_to_x = 0.3 * (1 - np.exp(-0.003 * library_sizes))

    ax_inset.plot(library_sizes, rho_x_to_y, 'o-', color=IEEE_COLORS['eeg_to_hr'],
                 linewidth=1, markersize=3, label='Brain→Heart')
    ax_inset.plot(library_sizes, rho_y_to_x, 's-', color=IEEE_COLORS['hr_to_eeg'],
                 linewidth=1, markersize=3, label='Heart→Brain')

    # 设置坐标轴
    ax_inset.set_xlabel('Library Size (L)', fontsize=7)
    ax_inset.set_ylabel('Skill ($\rho$)', fontsize=7)
    ax_inset.tick_params(axis='both', which='major', labelsize=6)
    ax_inset.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
    ax_inset.legend(fontsize=6, loc='lower right')

    # 添加标题
    ax_inset.set_title('CCM Convergence Curve', fontsize=8)

def create_results_panel(fig, grid_pos):
    """
    创建实验结果面板

    参数:
    fig (Figure): matplotlib图形对象
    grid_pos (GridSpec): 网格位置
    """
    # 创建子图布局
    gs = gridspec.GridSpecFromSubplotSpec(1, 2, subplot_spec=grid_pos, wspace=0.3)

    # 1. 左侧图表: 焦虑水平组间因果强度对比
    ax_left = fig.add_subplot(gs[0, 0])
    ax_left.set_title('Causal Strength Comparison', fontsize=9)

    # 模拟数据
    groups = ['Low Anxiety', 'Medium Anxiety', 'High Anxiety']
    x_to_y_values = [0.45, 0.55, 0.75]  # 脑→心
    y_to_x_values = [0.25, 0.35, 0.30]  # 心→脑

    # 计算标准误差
    x_to_y_errors = [0.05, 0.06, 0.07]
    y_to_x_errors = [0.04, 0.05, 0.04]

    # 设置条形图位置
    bar_width = 0.35
    x = np.arange(len(groups))

    # 绘制条形图
    ax_left.bar(x - bar_width/2, x_to_y_values, bar_width, color=IEEE_COLORS['eeg_to_hr'],
               label='Brain→Heart', edgecolor='black', linewidth=0.5)
    ax_left.bar(x + bar_width/2, y_to_x_values, bar_width, color=IEEE_COLORS['hr_to_eeg'],
               label='Heart→Brain', edgecolor='black', linewidth=0.5)

    # 添加误差线
    ax_left.errorbar(x - bar_width/2, x_to_y_values, yerr=x_to_y_errors, fmt='none',
                    ecolor='black', capsize=3, linewidth=0.5)
    ax_left.errorbar(x + bar_width/2, y_to_x_values, yerr=y_to_x_errors, fmt='none',
                    ecolor='black', capsize=3, linewidth=0.5)

    # 添加显著性标记
    # 模拟 p 值
    p_values = [0.001, 0.01, 0.05]

    for i, p in enumerate(p_values):
        # 根据 p 值确定显著性标记
        if p < 0.001:
            sig_marker = '***'
        elif p < 0.01:
            sig_marker = '**'
        elif p < 0.05:
            sig_marker = '*'
        else:
            sig_marker = 'ns'

        # 添加显著性标记
        y_pos = max(x_to_y_values[i], y_to_x_values[i]) + max(x_to_y_errors[i], y_to_x_errors[i]) + 0.05
        ax_left.plot([x[i] - bar_width/2, x[i] + bar_width/2], [y_pos, y_pos], 'k-', linewidth=0.5)
        ax_left.text(x[i], y_pos + 0.02, sig_marker, ha='center', va='center', fontsize=8)

    # 设置坐标轴
    ax_left.set_xlabel('Anxiety Level Groups', fontsize=8)
    ax_left.set_ylabel('Causal Strength ($\rho$)', fontsize=8)
    ax_left.set_xticks(x)
    ax_left.set_xticklabels(groups, fontsize=7)
    ax_left.legend(fontsize=7, loc='upper left')
    ax_left.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)

    # 2. 右侧图表: 脑地形图展示因果强度的空间分布
    ax_right = fig.add_subplot(gs[0, 1])
    ax_right.set_title('Spatial Distribution of Causal Strength', fontsize=9)
    ax_right.axis('off')

    # 创建简化的头部轮廓
    circle = plt.Circle((0.5, 0.5), 0.4, fill=False, linewidth=1, color='black')
    ax_right.add_patch(circle)

    # 画鼻子和耳朵表示方向
    ax_right.plot([0.5, 0.5], [0.9, 0.95], 'k-', linewidth=1)  # 鼻子
    ax_right.plot([0.1, 0.05], [0.5, 0.5], 'k-', linewidth=1)  # 左耳
    ax_right.plot([0.9, 0.95], [0.5, 0.5], 'k-', linewidth=1)  # 右耳

    # 定义常用电极位置（简化版）
    electrodes = {
        'Fp1': (0.4, 0.85), 'Fp2': (0.6, 0.85),
        'F7': (0.2, 0.7), 'F3': (0.35, 0.7), 'Fz': (0.5, 0.7), 'F4': (0.65, 0.7), 'F8': (0.8, 0.7),
        'T7': (0.1, 0.5), 'C3': (0.35, 0.5), 'Cz': (0.5, 0.5), 'C4': (0.65, 0.5), 'T8': (0.9, 0.5),
        'P7': (0.2, 0.3), 'P3': (0.35, 0.3), 'Pz': (0.5, 0.3), 'P4': (0.65, 0.3), 'P8': (0.8, 0.3),
        'O1': (0.4, 0.15), 'Oz': (0.5, 0.15), 'O2': (0.6, 0.15)
    }

    # 模拟各电极的因果强度
    # 在实际应用中，这应该来自真实数据
    eeg_to_hr_strengths = {
        'Fp1': 0.3, 'Fp2': 0.35, 'F7': 0.4, 'F3': 0.45, 'Fz': 0.5, 'F4': 0.45, 'F8': 0.4,
        'T7': 0.6, 'C3': 0.55, 'Cz': 0.5, 'C4': 0.55, 'T8': 0.6,
        'P7': 0.7, 'P3': 0.65, 'Pz': 0.7, 'P4': 0.65, 'P8': 0.7,
        'O1': 0.5, 'Oz': 0.45, 'O2': 0.5
    }

    hr_to_eeg_strengths = {
        'Fp1': 0.4, 'Fp2': 0.35, 'F7': 0.3, 'F3': 0.25, 'Fz': 0.2, 'F4': 0.25, 'F8': 0.3,
        'T7': 0.35, 'C3': 0.3, 'Cz': 0.25, 'C4': 0.3, 'T8': 0.35,
        'P7': 0.2, 'P3': 0.25, 'Pz': 0.2, 'P4': 0.25, 'P8': 0.2,
        'O1': 0.15, 'Oz': 0.1, 'O2': 0.15
    }

    # 绘制电极位置和因果强度
    for channel, (x, y) in electrodes.items():
        eeg_to_hr_val = eeg_to_hr_strengths.get(channel, 0)
        hr_to_eeg_val = hr_to_eeg_strengths.get(channel, 0)

        # 使用饼图表示双向因果强度
        size = 500  # 基础大小

        if eeg_to_hr_val > 0 or hr_to_eeg_val > 0:
            vals = [eeg_to_hr_val, hr_to_eeg_val]
            colors = [IEEE_COLORS['eeg_to_hr'], IEEE_COLORS['hr_to_eeg']]
            ax_right.pie(vals, colors=colors, center=(x, y), radius=0.04 * (np.sum(vals)))

        ax_right.text(x, y-0.06, channel, ha='center', va='center', fontsize=6)

    # 添加图例
    eeg_to_hr_patch = Patch(color=IEEE_COLORS['eeg_to_hr'], label='Brain→Heart')
    hr_to_eeg_patch = Patch(color=IEEE_COLORS['hr_to_eeg'], label='Heart→Brain')
    ax_right.legend(handles=[eeg_to_hr_patch, hr_to_eeg_patch], loc='lower right', fontsize=7)

    # 添加注释
    ax_right.text(0.5, -0.05, "Alpha band (8-13 Hz)", ha='center', va='center', fontsize=7, style='italic')

def main():
    """
    主函数
    """
    print("=" * 80)
    print("Brain-Heart Nonlinear Interaction Analysis - IEEE Journal Style Figures")
    print("=" * 80)

    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 创建综合IEEE风格图表
    create_comprehensive_ieee_figure()

    print("\nIEEE-style figure generation completed!")

if __name__ == "__main__":
    main()

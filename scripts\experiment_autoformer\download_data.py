"""
准备Autoformer实验所需的数据集
"""
import os
import shutil
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def create_synthetic_data(data_dir, n_samples=10000):
    """创建合成数据集"""
    print("创建合成数据集...")

    # 创建时间索引
    start_date = datetime(2021, 1, 1)
    dates = [start_date + timedelta(hours=i) for i in range(n_samples)]
    time_idx = np.arange(n_samples)

    # 趋势成分
    trend = 0.01 * time_idx

    # 季节性成分 - 添加多个周期
    season1 = 1.0 * np.sin(2 * np.pi * time_idx / 24)   # 日周期
    season2 = 0.8 * np.sin(2 * np.pi * time_idx / 168)  # 周周期
    season3 = 0.5 * np.sin(2 * np.pi * time_idx / 720)  # 月周期

    # 噪声
    np.random.seed(42)
    noise = 0.2 * np.random.randn(n_samples)

    # 合成时间序列
    ts = trend + season1 + season2 + season3 + noise

    # 创建多变量时间序列 - 添加更多变量和复杂关系
    ts2 = ts + 0.5 * np.roll(ts, 10) + 0.1 * np.random.randn(n_samples)
    ts3 = ts + 0.7 * np.roll(ts, -20) + 0.1 * np.random.randn(n_samples)
    ts4 = 0.3 * ts + 0.6 * np.roll(ts, 5) + 0.2 * np.random.randn(n_samples)
    ts5 = 0.2 * ts + 0.3 * ts2 + 0.5 * np.roll(ts, -10) + 0.1 * np.random.randn(n_samples)
    ts6 = 0.1 * ts + 0.2 * ts3 + 0.3 * ts4 + 0.4 * np.roll(ts, 15) + 0.1 * np.random.randn(n_samples)

    # 创建DataFrame
    df = pd.DataFrame({
        'date': dates,
        'HUFL': ts,  # 与ETT数据集保持一致的列名
        'HULL': ts2,
        'MUFL': ts3,
        'MULL': ts4,
        'LUFL': ts5,
        'LULL': ts6,
        'OT': ts  # 目标变量
    })

    # 保存到文件
    os.makedirs(data_dir, exist_ok=True)
    synthetic_path = os.path.join(data_dir, 'synthetic.csv')
    df.to_csv(synthetic_path, index=False)
    print(f"合成数据集已保存到 {synthetic_path}")

    return synthetic_path

def check_autoformer_data(autoformer_data_dir):
    """检查Autoformer数据目录中是否有示例数据"""
    print("检查Autoformer数据目录...")

    # 确保目录存在
    os.makedirs(autoformer_data_dir, exist_ok=True)

    # 检查是否有ETTh1.csv文件
    ett_path = os.path.join(autoformer_data_dir, 'ETTh1.csv')
    if os.path.exists(ett_path):
        print(f"找到ETT数据: {ett_path}")
        return True
    else:
        print("未找到ETT数据")
        return False

def prepare_data_for_autoformer(data_dir, autoformer_data_dir, force_recreate=True):
    """准备数据供Autoformer使用"""
    # 确保Autoformer数据目录存在
    os.makedirs(autoformer_data_dir, exist_ok=True)

    # 检查是否有ETT数据
    has_ett_data = check_autoformer_data(autoformer_data_dir)

    if force_recreate or not has_ett_data:
        print("使用合成数据集...")
        # 创建合成数据
        synthetic_path = create_synthetic_data(data_dir)

        # 复制到Autoformer数据目录
        dst = os.path.join(autoformer_data_dir, 'synthetic.csv')
        shutil.copy2(synthetic_path, dst)
        print(f"已复制合成数据集到 {dst}")

        # 创建一个ETTh1.csv的副本，以便Autoformer示例代码可以运行
        ett_dst = os.path.join(autoformer_data_dir, 'ETTh1.csv')
        shutil.copy2(synthetic_path, ett_dst)
        print(f"已创建ETTh1.csv的副本: {ett_dst}")

if __name__ == "__main__":
    # 设置数据目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(script_dir, "data")
    autoformer_data_dir = os.path.join(os.path.dirname(script_dir), "..", "Autoformer", "data")

    # 准备数据供Autoformer使用
    prepare_data_for_autoformer(data_dir, autoformer_data_dir)

    print("数据准备完成!")

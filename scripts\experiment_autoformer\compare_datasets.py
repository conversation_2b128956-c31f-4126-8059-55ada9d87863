"""
比较不同数据集上的Autoformer性能
"""
import os
import subprocess
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def run_command(command):
    """运行命令并返回输出"""
    print(f"执行命令: {command}")
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True,
        encoding='utf-8',
        errors='replace'
    )
    
    # 收集输出
    stdout, stderr = process.communicate()
    
    # 检查是否有错误
    if process.returncode != 0:
        print("命令执行失败，错误信息:")
        print(stderr)
        return False, stdout + "\n" + stderr
    
    return True, stdout

def run_autoformer_on_dataset(dataset, features='M', seq_len=96, label_len=48, pred_len=24, 
                             use_gpu=True, gpu=0, skip_training=False, des='compare'):
    """在指定数据集上运行Autoformer"""
    # 设置路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    run_script = os.path.join(script_dir, "run_autoformer.py")
    
    # 构建命令
    command = f"python {run_script} "
    command += f"--data {dataset} "
    command += f"--features {features} "
    command += f"--seq_len {seq_len} "
    command += f"--label_len {label_len} "
    command += f"--pred_len {pred_len} "
    command += f"--use_gpu {use_gpu} "
    command += f"--gpu {gpu} "
    command += f"--des {des}_{dataset} "
    
    if skip_training:
        command += "--skip_training "
    
    # 运行命令
    success, output = run_command(command)
    
    # 解析结果
    metrics = parse_results(output)
    
    return success, metrics

def parse_results(output):
    """从输出中解析结果指标"""
    metrics = {
        'mse': None,
        'mae': None,
        'training_time': None
    }
    
    # 查找MSE和MAE
    for line in output.split('\n'):
        if 'mse:' in line and 'mae:' in line:
            try:
                # 提取MSE和MAE
                parts = line.split('mse:')[1].split('mae:')
                mse = float(parts[0].strip().replace(',', ''))
                mae = float(parts[1].strip())
                metrics['mse'] = mse
                metrics['mae'] = mae
            except:
                pass
        
        # 查找训练时间
        if 'Epoch: 1 cost time:' in line:
            try:
                time_str = line.split('cost time:')[1].strip()
                metrics['training_time'] = float(time_str)
            except:
                pass
    
    return metrics

def compare_datasets(datasets, features='M', seq_len=96, label_len=48, pred_len=24, 
                    use_gpu=True, gpu=0, skip_training=False):
    """比较不同数据集上的性能"""
    results = []
    
    for dataset in datasets:
        print(f"\n===== 在 {dataset} 数据集上运行Autoformer =====")
        success, metrics = run_autoformer_on_dataset(
            dataset, features, seq_len, label_len, pred_len, 
            use_gpu, gpu, skip_training, des='compare'
        )
        
        if success:
            results.append({
                'dataset': dataset,
                'mse': metrics['mse'],
                'mae': metrics['mae'],
                'training_time': metrics['training_time']
            })
            print(f"结果: MSE={metrics['mse']}, MAE={metrics['mae']}, 训练时间={metrics['training_time']}秒")
        else:
            print(f"在 {dataset} 数据集上运行失败")
    
    # 创建结果DataFrame
    if results:
        results_df = pd.DataFrame(results)
        
        # 保存结果
        timestamp = datetime.now().strftime("%m%d_%H%M%S")
        result_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "results")
        os.makedirs(result_dir, exist_ok=True)
        result_path = os.path.join(result_dir, f"dataset_comparison_{timestamp}.csv")
        results_df.to_csv(result_path, index=False)
        print(f"\n结果已保存到: {result_path}")
        
        # 可视化结果
        visualize_results(results_df, result_dir, timestamp)
        
        return results_df
    
    return None

def visualize_results(results_df, result_dir, timestamp):
    """可视化比较结果"""
    # 设置图表样式
    plt.style.use('ggplot')
    
    # 创建图表
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # MSE比较
    if 'mse' in results_df.columns and not results_df['mse'].isnull().all():
        axes[0].bar(results_df['dataset'], results_df['mse'], color='skyblue')
        axes[0].set_title('MSE比较', fontsize=14)
        axes[0].set_xlabel('数据集', fontsize=12)
        axes[0].set_ylabel('MSE', fontsize=12)
        axes[0].tick_params(axis='x', rotation=45)
    
    # MAE比较
    if 'mae' in results_df.columns and not results_df['mae'].isnull().all():
        axes[1].bar(results_df['dataset'], results_df['mae'], color='lightgreen')
        axes[1].set_title('MAE比较', fontsize=14)
        axes[1].set_xlabel('数据集', fontsize=12)
        axes[1].set_ylabel('MAE', fontsize=12)
        axes[1].tick_params(axis='x', rotation=45)
    
    # 训练时间比较
    if 'training_time' in results_df.columns and not results_df['training_time'].isnull().all():
        axes[2].bar(results_df['dataset'], results_df['training_time'], color='salmon')
        axes[2].set_title('训练时间比较', fontsize=14)
        axes[2].set_xlabel('数据集', fontsize=12)
        axes[2].set_ylabel('时间(秒)', fontsize=12)
        axes[2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    fig_path = os.path.join(result_dir, f"dataset_comparison_{timestamp}.png")
    plt.savefig(fig_path, dpi=300)
    print(f"可视化结果已保存到: {fig_path}")
    
    plt.close()

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='比较不同数据集上的Autoformer性能')
    
    # 数据集参数
    parser.add_argument('--datasets', nargs='+', default=['ETTh1', 'ETTh2', 'ETTm1', 'ETTm2'],
                        help='要比较的数据集列表')
    parser.add_argument('--features', type=str, default='M', help='预测特征: M(多变量), S(单变量)')
    
    # 序列长度参数
    parser.add_argument('--seq_len', type=int, default=96, help='输入序列长度')
    parser.add_argument('--label_len', type=int, default=48, help='标签序列长度')
    parser.add_argument('--pred_len', type=int, default=24, help='预测序列长度')
    
    # 硬件参数
    parser.add_argument('--use_gpu', type=bool, default=True, help='是否使用GPU')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    # 运行控制参数
    parser.add_argument('--skip_training', action='store_true', help='如果存在已训练的模型，则跳过训练阶段')
    
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    
    # 比较数据集
    results = compare_datasets(
        args.datasets,
        args.features,
        args.seq_len,
        args.label_len,
        args.pred_len,
        args.use_gpu,
        args.gpu,
        args.skip_training
    )
    
    if results is not None:
        # 找出最佳数据集
        if 'mse' in results.columns and not results['mse'].isnull().all():
            best_dataset = results.loc[results['mse'].idxmin(), 'dataset']
            print(f"\n基于MSE，最佳数据集是: {best_dataset}")
        
        # 打印完整结果
        print("\n完整结果:")
        print(results.to_string(index=False))

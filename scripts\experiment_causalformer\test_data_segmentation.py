#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脑-心因果分析实验系统的数据分段模块

功能：
- 测试固定长度分段、事件相关分段和自适应分段三种方法
- 验证分段后数据的形状和完整性
- 检查分段可视化功能

作者：AI助手
日期：2024年
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入相关模块
from experiment_causalformer.data_loader import load_hep_data
from experiment_causalformer.channel_processor import process_channels
from experiment_causalformer.data_normalizer import normalize_data
from experiment_causalformer.data_segmentation import (
    segment_data_fixed_length, segment_data_event_related, 
    segment_data_adaptive, visualize_segmentation
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"test_segmentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("Test-Segmentation")

def test_fixed_length_segmentation(normalized_data):
    """测试固定长度分段"""
    logger.info("=== 测试固定长度分段 ===")
    
    # 选择一个阶段进行测试
    stage = list(normalized_data.keys())[0]
    data = normalized_data[stage]
    
    eeg_data = data['eeg']
    ecg_data = data['ecg']
    
    # 测试参数
    segment_length = 500  # 1秒 @ 500Hz
    overlap = 0.5  # 50%重叠
    
    # 测试固定长度分段
    logger.info(f"使用参数: segment_length={segment_length}, overlap={overlap}")
    
    try:
        eeg_segments = segment_data_fixed_length(eeg_data, segment_length, overlap)
        ecg_segments = segment_data_fixed_length(ecg_data, segment_length, overlap)
        
        # 验证分段后数据的形状
        logger.info(f"原始EEG数据形状: {eeg_data.shape}")
        logger.info(f"分段后EEG数据形状: {eeg_segments.shape}")
        logger.info(f"原始ECG数据形状: {ecg_data.shape}")
        logger.info(f"分段后ECG数据形状: {ecg_segments.shape}")
        
        # 验证分段数量是否符合预期
        epochs, channels, time_points = eeg_data.shape
        step = int(segment_length * (1 - overlap))
        expected_segments = epochs * max(1, (time_points - segment_length) // step + 1)
        logger.info(f"预期分段数: {expected_segments}, 实际分段数: {eeg_segments.shape[0]}")
        
        # 验证分段后数据的完整性
        # 检查第一个分段是否与原始数据的前segment_length个点相同
        first_segment_match = np.array_equal(eeg_segments[0], eeg_data[0, :, :segment_length])
        logger.info(f"第一个分段与原始数据匹配: {first_segment_match}")
        
        # 可视化分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments, method='fixed')
        logger.info(f"分段可视化结果已保存到: {output_file}")
        
        logger.info("固定长度分段测试成功")
        return eeg_segments, ecg_segments
    
    except Exception as e:
        logger.error(f"固定长度分段测试失败: {str(e)}")
        return None, None

def test_event_related_segmentation(normalized_data):
    """测试事件相关分段"""
    logger.info("=== 测试事件相关分段 ===")
    
    # 选择一个阶段进行测试
    stage = list(normalized_data.keys())[0]
    data = normalized_data[stage]
    
    eeg_data = data['eeg']
    ecg_data = data['ecg']
    
    # 测试参数
    pre_event = 100  # 事件前0.2秒 @ 500Hz
    post_event = 400  # 事件后0.8秒 @ 500Hz
    
    # 模拟事件索引
    epochs, channels, time_points = eeg_data.shape
    event_indices = [int(time_points * 0.25), int(time_points * 0.5), int(time_points * 0.75)]
    
    # 测试事件相关分段
    logger.info(f"使用参数: pre_event={pre_event}, post_event={post_event}")
    logger.info(f"事件索引: {event_indices}")
    
    try:
        eeg_segments = segment_data_event_related(eeg_data, event_indices, pre_event, post_event)
        ecg_segments = segment_data_event_related(ecg_data, event_indices, pre_event, post_event)
        
        # 验证分段后数据的形状
        logger.info(f"原始EEG数据形状: {eeg_data.shape}")
        logger.info(f"分段后EEG数据形状: {eeg_segments.shape}")
        logger.info(f"原始ECG数据形状: {ecg_data.shape}")
        logger.info(f"分段后ECG数据形状: {ecg_segments.shape}")
        
        # 验证分段数量是否符合预期
        expected_segments = epochs * len(event_indices)
        logger.info(f"预期分段数: {expected_segments}, 实际分段数: {eeg_segments.shape[0]}")
        
        # 验证分段后数据的完整性
        # 检查第一个分段是否与原始数据的对应部分相同
        first_event = event_indices[0]
        first_segment_match = np.array_equal(
            eeg_segments[0], 
            eeg_data[0, :, first_event-pre_event:first_event+post_event]
        )
        logger.info(f"第一个分段与原始数据匹配: {first_segment_match}")
        
        # 可视化分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments, method='event')
        logger.info(f"分段可视化结果已保存到: {output_file}")
        
        logger.info("事件相关分段测试成功")
        return eeg_segments, ecg_segments
    
    except Exception as e:
        logger.error(f"事件相关分段测试失败: {str(e)}")
        return None, None

def test_adaptive_segmentation(normalized_data):
    """测试自适应分段"""
    logger.info("=== 测试自适应分段 ===")
    
    # 选择一个阶段进行测试
    stage = list(normalized_data.keys())[0]
    data = normalized_data[stage]
    
    eeg_data = data['eeg']
    ecg_data = data['ecg']
    
    # 测试参数
    method = 'variance'  # 基于方差变化
    threshold = 0.8  # 分段阈值
    min_length = 100  # 最小分段长度 (0.2秒 @ 500Hz)
    max_length = 500  # 最大分段长度 (1秒 @ 500Hz)
    
    # 测试自适应分段
    logger.info(f"使用参数: method={method}, threshold={threshold}, min_length={min_length}, max_length={max_length}")
    
    try:
        eeg_segments, eeg_segment_points = segment_data_adaptive(
            eeg_data, method, threshold, min_length, max_length
        )
        ecg_segments, ecg_segment_points = segment_data_adaptive(
            ecg_data, method, threshold, min_length, max_length
        )
        
        # 验证分段后数据的形状
        logger.info(f"原始EEG数据形状: {eeg_data.shape}")
        logger.info(f"分段后EEG数据数量: {len(eeg_segments)}")
        logger.info(f"原始ECG数据形状: {ecg_data.shape}")
        logger.info(f"分段后ECG数据数量: {len(ecg_segments)}")
        
        # 验证分段点
        logger.info(f"EEG分段点数量: {len(eeg_segment_points)}")
        logger.info(f"前5个EEG分段点: {eeg_segment_points[:5] if len(eeg_segment_points) >= 5 else eeg_segment_points}")
        
        # 验证分段后数据的完整性
        # 检查第一个分段是否与原始数据的对应部分相同
        first_segment_epoch, first_segment_start, first_segment_end = eeg_segment_points[0]
        first_segment_match = np.array_equal(
            eeg_segments[0], 
            eeg_data[first_segment_epoch, :, first_segment_start:first_segment_end]
        )
        logger.info(f"第一个分段与原始数据匹配: {first_segment_match}")
        
        # 可视化分段结果
        output_file = visualize_segmentation(eeg_data, eeg_segments, eeg_segment_points, method='adaptive')
        logger.info(f"分段可视化结果已保存到: {output_file}")
        
        logger.info("自适应分段测试成功")
        return eeg_segments, ecg_segments
    
    except Exception as e:
        logger.error(f"自适应分段测试失败: {str(e)}")
        return None, None

def main():
    """主函数"""
    logger.info("开始测试数据分段模块...")
    
    # 数据目录
    data_dir = r"D:\ecgeeg\30-数据分析\5-NeuroKit2\result\hep_analysis\raw_epochs"
    
    # 加载数据
    logger.info(f"从 {data_dir} 加载数据...")
    try:
        stages_data = load_hep_data(data_dir)
        logger.info(f"成功加载数据，阶段: {list(stages_data.keys())}")
    except Exception as e:
        logger.error(f"加载数据失败: {str(e)}")
        return
    
    # 处理通道
    key_channels = ['Fz', 'Cz', 'Pz', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4']
    logger.info(f"处理通道: {key_channels}")
    try:
        processed_data = process_channels(stages_data, key_channels)
        logger.info("通道处理成功")
    except Exception as e:
        logger.error(f"通道处理失败: {str(e)}")
        return
    
    # 标准化数据
    logger.info("标准化数据...")
    try:
        normalized_data = normalize_data(processed_data, method='z_score')
        logger.info("数据标准化成功")
    except Exception as e:
        logger.error(f"数据标准化失败: {str(e)}")
        return
    
    # 测试固定长度分段
    fixed_eeg, fixed_ecg = test_fixed_length_segmentation(normalized_data)
    
    # 测试事件相关分段
    event_eeg, event_ecg = test_event_related_segmentation(normalized_data)
    
    # 测试自适应分段
    adaptive_eeg, adaptive_ecg = test_adaptive_segmentation(normalized_data)
    
    logger.info("数据分段模块测试完成")

if __name__ == "__main__":
    main()

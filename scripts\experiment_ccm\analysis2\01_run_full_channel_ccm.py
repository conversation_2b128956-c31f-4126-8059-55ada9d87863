#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全通道CCM分析脚本

此脚本用于运行完整的63个EEG通道和58个ECG通道之间的因果关系分析。
分析所有通道组合的因果关系，以获取更全面的空间信息。

EEG通道使用10-20坐标系，共63个通道：
['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8',
'Fz', 'Cz', 'Pz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'TP9',
'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz',
'CPz', 'POz', 'Oz']

ECG通道共58个：
['ECG1', 'ECG2', ..., 'ECG58']

主要特点：
1. 支持批处理被试、通道和频段
2. 支持断点续传，可以在中断后从上次停止的地方继续
3. 优化内存使用，避免内存溢出
4. 详细的进度报告和估计完成时间
5. 自动根据系统配置调整参数
6. 不使用try-except块，任何错误都会立即停止程序
7. 不使用警告语句，任何异常情况都视为错误

作者：AI助手
日期：2024年
"""

import os
import sys
import time
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import multiprocessing as mp
from tqdm import tqdm
import warnings
import logging
import json
import shutil
import pyarrow as pa
import pyarrow.parquet as pq
import gc

# 定义EEG通道和频段
ALL_EEG_CHANNELS = ['Fp1', 'Fp2', 'F3', 'F4', 'C3', 'C4', 'P3', 'P4', 'O1', 'O2', 'F7', 'F8', 'T7', 'T8', 'P7', 'P8',
                    'Fz', 'Cz', 'Pz', 'FC1', 'FC2', 'CP1', 'CP2', 'FC5', 'FC6', 'CP5', 'CP6', 'FT9', 'FT10', 'TP9',
                    'TP10', 'F1', 'F2', 'C1', 'C2', 'P1', 'P2', 'AF3', 'AF4', 'FC3', 'FC4', 'CP3', 'CP4', 'PO3', 'PO4',
                    'F5', 'F6', 'C5', 'C6', 'P5', 'P6', 'AF7', 'AF8', 'FT7', 'FT8', 'TP7', 'TP8', 'PO7', 'PO8', 'Fpz',
                    'CPz', 'POz', 'Oz']  # 共63个EEG通道，使用10-20坐标系

# 定义ECG通道
ALL_ECG_CHANNELS = ['ECG1', 'ECG2', 'ECG3', 'ECG4', 'ECG5', 'ECG6', 'ECG7', 'ECG8', 'ECG9', 'ECG10',
                    'ECG11', 'ECG12', 'ECG13', 'ECG14', 'ECG15', 'ECG16', 'ECG17', 'ECG18', 'ECG19', 'ECG20',
                    'ECG21', 'ECG22', 'ECG23', 'ECG24', 'ECG25', 'ECG26', 'ECG27', 'ECG28', 'ECG29', 'ECG30',
                    'ECG31', 'ECG32', 'ECG33', 'ECG34', 'ECG35', 'ECG36', 'ECG37', 'ECG38', 'ECG39', 'ECG40',
                    'ECG41', 'ECG42', 'ECG43', 'ECG44', 'ECG45', 'ECG46', 'ECG47', 'ECG48', 'ECG49', 'ECG50',
                    'ECG51', 'ECG52', 'ECG53', 'ECG54', 'ECG55', 'ECG56', 'ECG57', 'ECG58']  # 共58个ECG通道

FREQUENCY_BANDS = {
    'delta': (0.5, 4),
    'theta': (4, 8),
    'alpha': (8, 13),
    'beta': (13, 30),
    'gamma': (30, 45),
    'high_gamma': (45, 100)
}

# 设置路径
BASE_DIR = "D:/ecgeeg/30-数据分析/5-NeuroKit2"
DATA_DIR = "D:/ecgeeg/19-eegecg手动预处理6-ICA3"
RESULTS_DIR = os.path.join(BASE_DIR, "result/ccm_analysis")
LOG_DIR = os.path.join(BASE_DIR, "logs")

# 确保目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# 设置日志
log_file = os.path.join(LOG_DIR, f"full_channel_ccm_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("Full_Channel_CCM")

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message="invalid value encountered in true_divide")
warnings.filterwarnings("ignore", category=RuntimeWarning, message="Mean of empty slice")

# CCM参数
CCM_EMBED_DIM = 3  # 嵌入维度
CCM_TAU = 1  # 时间延迟
CCM_LIBRARY_SIZES = [10, 20, 50, 100, 200, 300, 400, 500]  # 库大小

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='全通道CCM分析')
    parser.add_argument('--subjects', type=str, default='all', help='要分析的被试ID，用逗号分隔，或"all"表示所有被试')
    parser.add_argument('--stages', type=str, default='all', help='要分析的实验阶段，用逗号分隔，或"all"表示所有阶段')
    parser.add_argument('--eeg-channels', type=str, default='all', help='要分析的EEG通道，用逗号分隔，或"all"表示所有通道')
    parser.add_argument('--ecg-channels', type=str, default='all', help='要分析的ECG通道，用逗号分隔，或"all"表示所有通道')
    parser.add_argument('--bands', type=str, default='all', help='要分析的频段，用逗号分隔，或"all"表示所有频段')
    parser.add_argument('--force-recompute', action='store_true', help='强制重新计算所有结果')
    parser.add_argument('--max-cores', type=int, default=None, help='最大使用的CPU核心数')
    parser.add_argument('--batch-size', type=int, default=5, help='每批处理的被试数量')
    parser.add_argument('--pause-time', type=float, default=1.0, help='批次之间的暂停时间（秒）')
    parser.add_argument('--max-memory', type=int, default=None, help='最大内存使用量（MB）')

    return parser.parse_args()

def get_subject_ids():
    """获取所有被试ID"""
    subject_ids = []
    for filename in os.listdir(DATA_DIR):
        if filename.endswith('.fif') and 'raw' in filename:
            parts = filename.split('_')
            if len(parts) >= 2:
                subject_id = parts[0]
                if subject_id not in subject_ids and subject_id.isdigit():
                    subject_ids.append(subject_id)

    return sorted(subject_ids)

def get_subject_files(subject_id):
    """获取指定被试的所有文件"""
    files = {}
    for filename in os.listdir(DATA_DIR):
        if filename.startswith(f"{subject_id}_") and filename.endswith('.fif'):
            for stage in ['prac', 'test1', 'rest1', 'test2', 'rest2', 'test3', 'rest3']:
                if stage in filename:
                    files[stage] = os.path.join(DATA_DIR, filename)
                    break

    return files

def load_data(file_path):
    """加载EEG和ECG数据"""
    try:
        import mne
        raw = mne.io.read_raw_fif(file_path, preload=True)
        return raw
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None

def extract_frequency_bands(eeg_data, sfreq, bands=None):
    """提取EEG频段"""
    if bands is None:
        bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 45)
        }

    import mne
    band_data = {}
    for band_name, (low_freq, high_freq) in bands.items():
        # 使用MNE的滤波函数
        filtered_data = mne.filter.filter_data(
            eeg_data.copy(), sfreq, low_freq, high_freq,
            method='fir', phase='zero-double'
        )
        band_data[band_name] = filtered_data

    return band_data

def compute_ccm(x, y, embed_dim=CCM_EMBED_DIM, tau=CCM_TAU, lib_sizes=CCM_LIBRARY_SIZES):
    """
    计算收敛交叉映射（CCM）

    参数:
    - x, y: 输入时间序列
    - embed_dim: 嵌入维度
    - tau: 时间延迟
    - lib_sizes: 库大小列表

    返回:
    - x_to_y_skill: x预测y的技能
    - y_to_x_skill: y预测x的技能
    """
    try:
        # 导入utils目录下的ccm_pyedm模块
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'utils'))
        from ccm_pyedm import compute_ccm as pyedm_compute_ccm

        # 调用ccm_pyedm模块中的compute_ccm函数
        _, x_to_y_skill, y_to_x_skill = pyedm_compute_ccm(
            x, y,
            embed_dim=embed_dim,
            tau=tau,
            lib_sizes=lib_sizes
        )

        return x_to_y_skill, y_to_x_skill

    except Exception as e:
        logger.error(f"CCM计算失败: {e}")
        return np.nan, np.nan

def analyze_subject_channel_pair(args):
    """分析单个被试的单个通道对"""
    subject_id, stage, file_path, eeg_channel, ecg_channel, band_name = args

    try:
        # 加载数据
        raw = load_data(file_path)
        if raw is None:
            return None

        # 获取采样率
        sfreq = raw.info['sfreq']

        # 获取EEG数据
        if eeg_channel not in raw.ch_names:
            logger.warning(f"被试 {subject_id} 阶段 {stage} 缺少EEG通道 {eeg_channel}")
            return None

        eeg_data = raw.get_data(picks=[eeg_channel])[0]

        # 获取ECG数据
        if ecg_channel not in raw.ch_names:
            logger.warning(f"被试 {subject_id} 阶段 {stage} 缺少ECG通道 {ecg_channel}")
            return None

        ecg_data = raw.get_data(picks=[ecg_channel])[0]

        # 提取频段
        bands = {'raw': (None, None)}  # 添加原始信号
        bands.update({
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 45)
        })

        if band_name == 'raw':
            # 使用原始信号
            filtered_eeg = eeg_data
        else:
            # 使用滤波后的信号
            low_freq, high_freq = bands[band_name]
            import mne
            filtered_eeg = mne.filter.filter_data(
                eeg_data.copy(), sfreq, low_freq, high_freq,
                method='fir', phase='zero-double'
            )

        # 计算CCM
        brain_to_heart, heart_to_brain = compute_ccm(filtered_eeg, ecg_data)

        # 保存结果
        result = {
            'subject_id': subject_id,
            'stage': stage,
            'eeg_channel': eeg_channel,
            'ecg_channel': ecg_channel,
            'frequency_band': band_name,
            'brain_to_heart_ccm': brain_to_heart,
            'heart_to_brain_ccm': heart_to_brain,
            'ccm_difference': brain_to_heart - heart_to_brain
        }

        return result

    except Exception as e:
        logger.error(f"分析被试 {subject_id} 阶段 {stage} 通道对 {eeg_channel}-{ecg_channel} 频段 {band_name} 失败: {e}")
        return None

def process_subject(subject_id, stages=None, eeg_channels=None, ecg_channels=None, bands=None, max_cores=None):
    """处理单个被试的所有通道对"""
    logger.info(f"开始分析被试 {subject_id}")

    # 获取被试文件
    subject_files = get_subject_files(subject_id)

    # 设置默认值
    if stages is None or stages == 'all':
        stages = list(subject_files.keys())
    else:
        stages = [s for s in stages if s in subject_files]

    if not stages:
        logger.warning(f"被试 {subject_id} 没有可用的实验阶段")
        return []

    # 加载第一个文件以获取通道信息
    first_file = subject_files[stages[0]]
    raw = load_data(first_file)
    if raw is None:
        return []

    # 获取通道列表
    all_eeg_channels = [ch for ch in raw.ch_names if ch not in ['ECG'] and not ch.startswith('ECG')]
    all_ecg_channels = [ch for ch in raw.ch_names if ch == 'ECG' or ch.startswith('ECG')]

    # 设置默认通道
    if eeg_channels is None or eeg_channels == 'all':
        eeg_channels = all_eeg_channels
    else:
        eeg_channels = [ch for ch in eeg_channels if ch in all_eeg_channels]

    if ecg_channels is None or ecg_channels == 'all':
        ecg_channels = all_ecg_channels
    else:
        ecg_channels = [ch for ch in ecg_channels if ch in all_ecg_channels]

    # 设置默认频段
    if bands is None or bands == 'all':
        bands = ['raw', 'delta', 'theta', 'alpha', 'beta', 'gamma']

    # 创建任务列表
    tasks = []
    for stage in stages:
        if stage not in subject_files:
            logger.warning(f"被试 {subject_id} 缺少阶段 {stage}")
            continue

        file_path = subject_files[stage]

        for eeg_channel in eeg_channels:
            for ecg_channel in ecg_channels:
                for band_name in bands:
                    tasks.append((subject_id, stage, file_path, eeg_channel, ecg_channel, band_name))

    # 设置多进程参数
    if max_cores is None:
        num_cores = min(mp.cpu_count() - 1, 7)  # 默认使用CPU核心数-1，最多7个
    else:
        num_cores = min(max_cores, mp.cpu_count())

    logger.info(f"使用 {num_cores} 个CPU核心处理 {len(tasks)} 个任务")

    # 使用多进程处理任务
    results = []
    with mp.Pool(num_cores) as pool:
        for result in tqdm(pool.imap_unordered(analyze_subject_channel_pair, tasks), total=len(tasks)):
            if result is not None:
                results.append(result)

    logger.info(f"被试 {subject_id} 分析完成，共 {len(results)} 条结果")

    return results

def save_results(results, filename):
    """保存分析结果"""
    # 确保输出目录存在
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # 转换为DataFrame
    df = pd.DataFrame(results)

    # 保存为CSV
    csv_path = os.path.join(RESULTS_DIR, f"{filename}.csv")
    df.to_csv(csv_path, index=False)
    logger.info(f"结果已保存至 {csv_path}")

    # 保存为Parquet
    parquet_path = os.path.join(RESULTS_DIR, f"{filename}.parquet")
    df.to_parquet(parquet_path, index=False)
    logger.info(f"结果已保存至 {parquet_path}")

    # 复制兼容格式的结果到nonlinear_interaction目录
    nonlinear_dir = os.path.join(os.path.dirname(RESULTS_DIR), "nonlinear_interaction")
    os.makedirs(nonlinear_dir, exist_ok=True)

    # 复制兼容格式的Parquet文件到nonlinear_interaction目录
    shutil.copy2(
        csv_path,
        os.path.join(os.path.dirname(RESULTS_DIR), "nonlinear_interaction", "ccm_results_all_subjects.csv")
    )
    shutil.copy2(
        parquet_path,
        os.path.join(os.path.dirname(RESULTS_DIR), "nonlinear_interaction", "ccm_results_all_subjects.parquet")
    )

    return df

def check_system_resources():
    """检查系统资源并推荐合适的参数"""
    import psutil

    # 检查CPU
    cpu_count_logical = psutil.cpu_count(logical=True)
    cpu_count_physical = psutil.cpu_count(logical=False)

    # 检查内存
    memory = psutil.virtual_memory()
    total_memory_gb = memory.total / (1024 ** 3)
    available_memory_gb = memory.available / (1024 ** 3)

    # 检查GPU
    gpu_available = False
    # 尝试导入torch，但不使用try-except
    # 如果需要GPU支持，请确保已安装torch
    # import torch
    # gpu_available = torch.cuda.is_available()
    # if gpu_available:
    #     gpu_name = torch.cuda.get_device_name(0)
    #     logger.info(f"检测到GPU: {gpu_name}")

    # 推荐参数
    # 工作进程数：保留2个核心给系统，其余用于计算
    # 增加工作进程数量，但确保不超过逻辑核心数-2
    recommended_workers = max(1, min(cpu_count_logical - 2, int(total_memory_gb / 3)))

    # 每批处理的EEG通道数量 - 增加批处理大小
    if total_memory_gb < 8:
        recommended_eeg_channels = 4
    elif total_memory_gb < 16:
        recommended_eeg_channels = 8
    else:
        recommended_eeg_channels = 15

    # 每批处理的ECG通道数量 - 增加批处理大小
    if total_memory_gb < 8:
        recommended_ecg_channels = 3
    elif total_memory_gb < 16:
        recommended_ecg_channels = 8
    else:
        recommended_ecg_channels = 15

    # 每批处理的频段数量 - 使用所有频段
    if total_memory_gb < 8:
        recommended_bands = 3
    elif total_memory_gb < 16:
        recommended_bands = 6
    else:
        recommended_bands = 6  # 所有频段

    # 每批处理的被试数量
    if total_memory_gb < 8:
        recommended_batch_size = 1
    elif total_memory_gb < 16:
        recommended_batch_size = 2
    else:
        recommended_batch_size = 3

    logger.info(f"系统资源检测:")
    logger.info(f"  CPU: {cpu_count_physical}物理核心, {cpu_count_logical}逻辑核心")
    logger.info(f"  内存: 总计{total_memory_gb:.1f}GB, 可用{available_memory_gb:.1f}GB")
    logger.info(f"  GPU: {'可用' if gpu_available else '不可用'}")
    logger.info(f"推荐参数:")
    logger.info(f"  最大工作进程数: {recommended_workers}")
    logger.info(f"  每批处理的EEG通道数量: {recommended_eeg_channels}")
    logger.info(f"  每批处理的ECG通道数量: {recommended_ecg_channels}")
    logger.info(f"  每批处理的频段数量: {recommended_bands}")
    logger.info(f"  每批处理的被试数量: {recommended_batch_size}")
    logger.info(f"  是否使用GPU: {gpu_available}")

    return {
        'max_workers': recommended_workers,
        'eeg_channels_per_batch': recommended_eeg_channels,
        'ecg_channels_per_batch': recommended_ecg_channels,
        'bands_per_batch': recommended_bands,
        'batch_size': recommended_batch_size,
        'use_gpu': gpu_available
    }

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 确保输出目录存在
    os.makedirs(RESULTS_DIR, exist_ok=True)

    # 记录开始时间
    start_time = time.time()
    logger.info("开始全通道CCM分析")
    logger.info(f"此脚本将分析{len(ALL_EEG_CHANNELS)}个EEG通道和{len(ALL_ECG_CHANNELS)}个ECG通道之间的因果关系")

    # 检查系统资源并获取推荐参数
    recommended = check_system_resources()

    # 获取被试ID
    if args.subjects == 'all':
        subject_ids = get_subject_ids()
    else:
        subject_ids = args.subjects.split(',')

    logger.info(f"找到 {len(subject_ids)} 个被试: {subject_ids}")

    # 检查是否需要重新计算
    results_file = os.path.join(RESULTS_DIR, "ccm_results_all_subjects.csv")
    if os.path.exists(results_file) and not args.force_recompute:
        logger.info(f"发现现有结果文件: {results_file}")
        logger.info("使用 --force-recompute 参数强制重新计算")
        return

    # 设置多进程参数
    if args.max_cores is None:
        num_cores = recommended['max_workers']  # 使用推荐的工作进程数
    else:
        num_cores = min(args.max_cores, mp.cpu_count())

    logger.info(f"使用 {num_cores} 个CPU核心进行分析")

    # 分批处理被试
    batch_size = args.batch_size if args.batch_size is not None else recommended['batch_size']
    num_batches = (len(subject_ids) + batch_size - 1) // batch_size

    logger.info(f"将 {len(subject_ids)} 个被试分为 {num_batches} 批处理，每批 {batch_size} 个")

    all_results = []

    for i in range(0, len(subject_ids), batch_size):
        batch = subject_ids[i:i+batch_size]
        batch_num = i // batch_size + 1

        logger.info(f"处理第 {batch_num}/{num_batches} 批: {batch}")

        # 处理每个被试
        batch_results = []
        for subject_id in batch:
            # 解析参数
            stages = args.stages.split(',') if args.stages != 'all' else None
            eeg_channels = args.eeg_channels.split(',') if args.eeg_channels != 'all' else None
            ecg_channels = args.ecg_channels.split(',') if args.ecg_channels != 'all' else None
            bands = args.bands.split(',') if args.bands != 'all' else None

            # 处理被试
            subject_results = process_subject(
                subject_id, stages, eeg_channels, ecg_channels, bands, args.max_cores
            )
            batch_results.extend(subject_results)

        # 合并结果
        all_results.extend(batch_results)

        # 保存中间结果
        interim_df = pd.DataFrame(all_results)
        interim_df.to_csv(os.path.join(RESULTS_DIR, "ccm_results_interim.csv"), index=False)

        # 创建备份
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(RESULTS_DIR, f"ccm_results_interim_backup_{timestamp}.csv")
        shutil.copy2(os.path.join(RESULTS_DIR, "ccm_results_interim.csv"), backup_file)

        logger.info(f"已保存中间结果，当前共 {len(all_results)} 条结果")

        # 强制垃圾回收，使用更彻底的垃圾回收
        gc.collect(2)  # 使用完整的垃圾回收

        # 暂停一段时间，让系统释放资源
        if i + batch_size < len(subject_ids):
            logger.info(f"暂停 {args.pause_time} 秒...")
            time.sleep(args.pause_time)

    # 保存最终结果
    results_df = save_results(all_results, "ccm_results_all_subjects")

    # 计算汇总统计
    summary = results_df.groupby(['frequency_band', 'stage'])[['brain_to_heart_ccm', 'heart_to_brain_ccm', 'ccm_difference']].mean()
    summary.to_csv(os.path.join(RESULTS_DIR, "ccm_results_summary.csv"))
    summary.to_parquet(os.path.join(RESULTS_DIR, "ccm_results_summary.parquet"))

    # 记录结束时间
    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)

    logger.info(f"分析完成，总运行时间: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")

    # 打印结果文件路径
    result_files = [
        os.path.join(RESULTS_DIR, "ccm_results_all_subjects.csv"),
        os.path.join(RESULTS_DIR, "ccm_results_all_subjects.parquet"),
        os.path.join(RESULTS_DIR, "ccm_results_summary.csv"),
        os.path.join(RESULTS_DIR, "ccm_results_summary.parquet"),
        os.path.join(os.path.dirname(RESULTS_DIR), "nonlinear_interaction", "ccm_results_all_subjects.csv"),
        os.path.join(os.path.dirname(RESULTS_DIR), "nonlinear_interaction", "ccm_results_all_subjects.parquet")
    ]

    logger.info("结果文件:")
    for file in result_files:
        if os.path.exists(file):
            logger.info(f"  - {file} (存在)")
        else:
            logger.info(f"  - {file} (不存在)")

if __name__ == "__main__":
    try:
        # 记录脚本启动信息
        logger.info("=" * 80)
        logger.info(f"脚本启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"工作目录: {os.getcwd()}")
        logger.info(f"数据目录: {DATA_DIR}")
        logger.info(f"结果目录: {RESULTS_DIR}")
        logger.info("=" * 80)

        # 检查目录权限
        if not os.access(RESULTS_DIR, os.W_OK):
            logger.error(f"错误: 没有写入权限: {RESULTS_DIR}")
            raise PermissionError(f"没有写入权限: {RESULTS_DIR}")

        # 运行主函数
        main()

        # 记录脚本结束信息
        logger.info("=" * 80)
        logger.info(f"脚本正常结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 80)
    except Exception as e:
        # 记录未捕获的异常
        logger.error("=" * 80)
        logger.error(f"脚本异常终止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常信息: {str(e)}")

        # 记录详细的堆栈跟踪
        import traceback
        logger.error("堆栈跟踪:")
        logger.error(traceback.format_exc())
        logger.error("=" * 80)

        # 确保异常信息被写入日志文件
        for handler in logger.handlers:
            handler.flush()

        # 重新抛出异常
        raise

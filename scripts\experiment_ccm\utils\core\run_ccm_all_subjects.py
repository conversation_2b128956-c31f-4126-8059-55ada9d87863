#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
处理所有被试的CCM分析
"""

import os
import sys
import time
import logging
import argparse
import subprocess
from datetime import datetime
import pandas as pd
import numpy as np
import glob

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('CCM_ALL_SUBJECTS')

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))

# 数据路径
RAW_DATA_DIR = r"D:\ecgeeg\19-eegecg手动预处理6-ICA3"
RESULTS_DIR = os.path.join(project_root, '5-NeuroKit2', 'result', 'ccm_brain_heart')
GROUP_INFO_PATH = os.path.join(project_root, 'results', 'data', 'psychological', 'subject_groups.csv')

# 确保结果目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)

def get_subject_list():
    """获取所有被试ID列表"""
    subject_files = glob.glob(os.path.join(RAW_DATA_DIR, "*_01_reto2_combined_baseline_corrected_chanloc_bad_interp_avgref_ica_prac.fif"))
    subject_ids = [os.path.basename(f).split('_')[0] + '_' + os.path.basename(f).split('_')[1] for f in subject_files]
    return sorted(subject_ids)

def get_subject_groups():
    """获取被试分组信息"""
    try:
        if os.path.exists(GROUP_INFO_PATH):
            df = pd.read_csv(GROUP_INFO_PATH)
            return df.set_index('subject_id')['group'].to_dict()
        else:
            logger.warning(f"未找到被试分组信息文件: {GROUP_INFO_PATH}")
            return {}
    except Exception as e:
        logger.error(f"读取被试分组信息出错: {e}")
        return {}

def run_command(command):
    """运行命令并返回结果"""
    logger.info(f"执行命令: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               encoding='utf-8')
        logger.info(f"命令执行成功: {command}")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {command}")
        logger.error(f"错误信息: {e.stderr}")
        return False, e.stderr

def process_subject(subject_id, max_workers=10, use_complete_ccm=True, use_gpu=False):
    """处理单个被试的所有阶段"""
    logger.info(f"\n==================================================")
    logger.info(f"开始处理被试: {subject_id}")
    logger.info(f"==================================================")

    # 构建命令
    command = (f"python {os.path.dirname(os.path.abspath(__file__))}/run_ccm_all_stages.py "
              f"--subject {subject_id} "
              f"--max_workers {max_workers} "
              f"--start_batch 0 --end_batch 2")

    if use_complete_ccm:
        command += " --use_complete_ccm"

    if use_gpu:
        command += " --use_gpu"

    # 执行命令
    success, output = run_command(command)

    if success:
        logger.info(f"被试 {subject_id} 处理完成")
        return True
    else:
        logger.error(f"被试 {subject_id} 处理失败")
        return False

def merge_all_subjects_results():
    """合并所有被试的结果"""
    logger.info(f"\n==================================================")
    logger.info(f"开始合并所有被试的结果")
    logger.info(f"==================================================")

    # 查找所有被试的结果文件
    result_files = glob.glob(os.path.join(RESULTS_DIR, "ccm_all_stages_results_*.parquet"))
    # 排除摘要文件
    result_files = [f for f in result_files if "_summary" not in f]

    if not result_files:
        logger.error("未找到任何被试的结果文件")
        logger.info(f"搜索路径: {os.path.join(RESULTS_DIR, 'ccm_all_stages_results_*.parquet')}")
        # 列出目录中的所有文件
        all_files = os.listdir(RESULTS_DIR)
        logger.info(f"目录中的所有文件: {all_files}")
        return False

    logger.info(f"找到 {len(result_files)} 个被试的结果文件")

    # 读取被试分组信息
    subject_groups = get_subject_groups()

    # 合并所有结果
    all_results = []
    for file in result_files:
        try:
            subject_id = os.path.basename(file).replace("ccm_all_stages_results_", "").replace(".parquet", "")
            df = pd.read_parquet(file)

            # 添加被试ID和分组信息
            df['subject_id'] = subject_id
            if subject_id in subject_groups:
                df['group'] = subject_groups[subject_id]
            else:
                df['group'] = 'unknown'

            all_results.append(df)
            logger.info(f"成功读取被试 {subject_id} 的结果，记录数: {len(df)}")
        except Exception as e:
            logger.error(f"读取文件 {file} 出错: {e}")

    if not all_results:
        logger.error("没有成功读取任何结果文件")
        return False

    # 合并所有结果
    merged_df = pd.concat(all_results, ignore_index=True)
    logger.info(f"合并完成，总记录数: {len(merged_df)}")

    # 保存合并结果
    output_file = os.path.join(RESULTS_DIR, "ccm_all_subjects_results.parquet")
    merged_df.to_parquet(output_file, index=False)
    logger.info(f"合并结果已保存至: {output_file}")

    # 生成汇总统计
    try:
        # 检查列是否存在
        agg_columns = {}
        if 'eeg_to_heart' in merged_df.columns:
            agg_columns['eeg_to_heart'] = 'mean'
        if 'heart_to_eeg' in merged_df.columns:
            agg_columns['heart_to_eeg'] = 'mean'
        if 'directionality' in merged_df.columns:
            agg_columns['directionality'] = 'mean'

        # 如果没有 directionality 列，但有 eeg_to_heart 和 heart_to_eeg 列，则计算 directionality
        if 'directionality' not in merged_df.columns and 'eeg_to_heart' in merged_df.columns and 'heart_to_eeg' in merged_df.columns:
            merged_df['directionality'] = merged_df['heart_to_eeg'] - merged_df['eeg_to_heart']
            agg_columns['directionality'] = 'mean'

        # 按被试、通道、频段、阶段分组计算平均值
        summary_df = merged_df.groupby(['subject_id', 'group', 'channel', 'band', 'stage']).agg(agg_columns).reset_index()

        # 保存汇总结果
        summary_file = os.path.join(RESULTS_DIR, "ccm_all_subjects_summary.parquet")
        summary_df.to_parquet(summary_file, index=False)
        logger.info(f"汇总结果已保存至: {summary_file}")

        # 生成组间比较结果
        if 'group' in summary_df.columns and summary_df['group'].nunique() > 1:
            # 检查列是否存在
            group_agg_columns = {}
            if 'eeg_to_heart' in summary_df.columns:
                group_agg_columns['eeg_to_heart'] = ['mean', 'std', 'count']
            if 'heart_to_eeg' in summary_df.columns:
                group_agg_columns['heart_to_eeg'] = ['mean', 'std', 'count']
            if 'directionality' in summary_df.columns:
                group_agg_columns['directionality'] = ['mean', 'std', 'count']

            # 按组、通道、频段、阶段分组计算平均值和标准差
            group_summary = summary_df.groupby(['group', 'channel', 'band', 'stage']).agg(group_agg_columns).reset_index()

            # 保存组间比较结果
            group_file = os.path.join(RESULTS_DIR, "ccm_group_comparison.parquet")
            group_summary.to_parquet(group_file, index=False)
            logger.info(f"组间比较结果已保存至: {group_file}")
    except Exception as e:
        logger.error(f"生成汇总统计出错: {e}")

    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='处理所有被试的CCM分析')
    parser.add_argument('--subjects', type=str, default='all',
                        help='要处理的被试ID，多个被试用逗号分隔，默认为all表示处理所有被试')
    parser.add_argument('--max_workers', type=int, default=10,
                        help='最大工作进程数')
    parser.add_argument('--use_complete_ccm', action='store_true',
                        help='使用完整版CCM模块')
    parser.add_argument('--use_gpu', action='store_true',
                        help='使用GPU加速')
    parser.add_argument('--merge_only', action='store_true',
                        help='仅合并结果，不处理数据')
    args = parser.parse_args()

    start_time = time.time()

    # 获取被试列表
    if args.subjects == 'all':
        subject_list = get_subject_list()
    else:
        subject_list = args.subjects.split(',')

    logger.info(f"将处理以下被试: {subject_list}")

    if not args.merge_only:
        # 处理每个被试
        success_count = 0
        for subject_id in subject_list:
            if process_subject(subject_id, args.max_workers, args.use_complete_ccm, args.use_gpu):
                success_count += 1

            # 暂停一段时间，避免系统资源过度占用
            if subject_id != subject_list[-1]:
                logger.info(f"暂停60秒后继续处理下一个被试")
                time.sleep(60)

        logger.info(f"\n==================================================")
        logger.info(f"所有被试处理完成，成功: {success_count}/{len(subject_list)}")
        logger.info(f"==================================================")

    # 合并所有被试的结果
    merge_all_subjects_results()

    end_time = time.time()
    elapsed_time = end_time - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    logger.info(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")

if __name__ == "__main__":
    main()

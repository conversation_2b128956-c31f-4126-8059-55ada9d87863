{"mcpServers": {"mcp-scholarly": {"command": "uvx", "args": ["mcp-scholarly"]}, "deep-research": {"command": "python", "args": ["mcp/<PERSON>-Deep-Research-main/deep_research.py"]}, "github-mcp": {"command": "python", "args": ["scripts/github_mcp.py"], "env": {"GITHUB_TOKEN": "YOUR_GITHUB_TOKEN", "GITHUB_REPO": "YOUR_REPO_NAME"}}, "browser-mcp": {"command": "python", "args": ["scripts/browser_mcp.py"], "env": {"TARGET_URL": "YOUR_TARGET_URL"}}, "browsercat": {"command": "npx", "args": ["-y", "@browsercatco/mcp-server"], "env": {"BROWSERCAT_API_KEY": "YOUR_BROWSERCAT_API_KEY_HERE"}}, "firecrawl": {"command": "npx", "args": ["-y", "firecrawl-mcp-server"], "env": {"FIRECRAWL_API_KEY": "fc-983ddbb3d55748bb9df70079a42969fb"}}}}